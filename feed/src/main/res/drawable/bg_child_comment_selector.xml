<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:drawable="@drawable/bg_child_comment" android:state_enabled="false" android:state_focused="true" android:state_pressed="true"/>
    <item android:drawable="@drawable/bg_child_comment" android:state_enabled="false" android:state_focused="true"/>
    <item android:drawable="@drawable/bg_child_comment_press" android:state_focused="true" android:state_pressed="true"/>
    <item android:drawable="@drawable/bg_child_comment_press" android:state_focused="false" android:state_pressed="true"/>
    <item android:drawable="@drawable/bg_child_comment" android:state_focused="true"/>
    <item android:drawable="@drawable/bg_child_comment"/>
</selector>