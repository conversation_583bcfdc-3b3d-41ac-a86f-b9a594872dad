<?xml version="1.0" encoding="utf-8"?>
<resources>

    <dimen name="micro_video_layout_padding">17dp</dimen>
    <dimen name="micro_video_layout_item_padding">3.5dp</dimen>

    <dimen name="front_page_item_margin_left">2dp</dimen>
    <dimen name="front_page_item_margin_right">2dp</dimen>
    <dimen name="front_page_item_margin_top">5dp</dimen>

    <integer name="recommend_micro_video_fragment_column_num">2</integer>
    <dimen name="listitem_feed_image_hight">80dip</dimen>

    <dimen name="item_feed_padding">15dp</dimen>
    <dimen name="item_feed_margin">15dp</dimen>
    <dimen name="item_feed_content_margin">8dp</dimen>
    <dimen name="item_feed_content_margin_top">5.5dp</dimen>
    <dimen name="profile_item_padding">15dp</dimen>


    <dimen name="feed_listitem_content_margin">10dp</dimen>
    <!--Feed 列表左侧空白,与用户头像大小，边距相关联-->
    <dimen name="feed_listitem_image_size">85dp</dimen>

    <!--直播图片，城市卡片的宽度 宽高比为-->
    <dimen name="feed_large_image_width">275dp</dimen>


    <!-- 6.0重构的重点颜色-->
    <color name="feed_text_content">@color/color_text_6b6b6b</color>

    <dimen name="feed_mgs_game_round">12dp</dimen>

    <dimen name="feed_content_text_size">14sp</dimen>

    <!--新的Feed-->
    <dimen name="headimage_size_48">48dp</dimen>
    <dimen name="lba_headimage_size">36dp</dimen>

    <!-- 列表页通用的主要文字大小 -->
    <dimen name="text_feed_list_desc">12sp</dimen>

    <dimen name="feed_btn_text_padding_top">5dip</dimen>

    <dimen name="square_bg_round">7dp</dimen>
    <dimen name="square_view_round">57dp</dimen>
    <dimen name="square_tab_width">90dp</dimen>
    <dimen name="square_tab_height">45dp</dimen>

    <!--Feed分割线的高度-->
    <dimen name="feed_section_bar_size">5dp</dimen>
</resources>