<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="first_page_default_bottom_text">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">#fff</item>
        <item name="android:singleLine">true</item>
        <item name="android:maxLines">1</item>
    </style>

    <style name="first_page_default_bottom_text.gravity_left">
        <item name="android:gravity">left|center</item>
    </style>

    <style name="first_page_default_bottom_text.gravity_left.nearbypeople">
        <item name="android:layout_width">130dp</item>
        <item name="android:text">@string/feed_nearby_people_default_desc</item>
        <item name="android:textColor">#ccffffff</item>
    </style>

    <style name="StyleFeedDividerFill">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">4dp</item>
        <item name="android:background">@drawable/bg_feed_line</item>
    </style>

    <style name="StyleFeedDivider" parent="StyleFeedDividerFill">
        <item name="android:layout_marginStart">@dimen/item_feed_padding</item>
        <item name="android:layout_marginEnd">@dimen/item_feed_padding</item>
    </style>

    <style name="LineFeedHorizontal">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1px</item>
        <item name="android:background">#ebebeb</item>
        <item name="android:layout_marginStart">@dimen/item_feed_padding</item>
        <item name="android:layout_marginEnd">@dimen/item_feed_padding</item>
    </style>

    <style name="front_page_item_like_style">
        <item name="android:layout_width">18dp</item>
        <item name="android:layout_height">18dp</item>
        <item name="rd_style">@style/like_view_ring_style</item>
        <item name="sd_style">@style/front_page_item_like_star_style</item>
        <item name="lab_style">@style/front_page_item_like_anim_style</item>
    </style>

    <style name="FrontPageItem">
        <item name="android:layout_marginLeft">@dimen/front_page_item_margin_left</item>
        <item name="android:layout_marginRight">@dimen/front_page_item_margin_right</item>
        <item name="android:layout_marginTop">@dimen/front_page_item_margin_top</item>
    </style>

    <!--风险提示textview -->
    <style name="Risk_Tips">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/bg_11dp_corner_1a000000</item>
        <item name="android:drawableLeft">@drawable/icon_risk_tips</item>
        <item name="android:drawablePadding">5dp</item>

        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingTop">4dp</item>
        <item name="android:paddingRight">8dp</item>

        <item name="android:paddingBottom">4dp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">12sp</item>

        <item name="android:layout_marginLeft">0dp</item>
        <item name="android:layout_marginBottom">0dp</item>
        <item name="android:text">@string/safe_notice</item>
        <!--<item name="android:visibility">visible</item>-->
        <item name="android:visibility">gone</item>
    </style>

    <style name="MicroVideoLayoutFullLineMargin">
        <item name="android:layout_marginLeft">-4dp</item>
        <item name="android:layout_marginRight">-4dp</item>
    </style>


    <style name="MicroVideoItem">
        <item name="android:layout_marginLeft">3.5dp</item>
        <item name="android:layout_marginRight">3.5dp</item>
        <item name="android:layout_marginBottom">7dp</item>
    </style>

    <style name="front_page_item_like_star_style">
        <item name="sd_star_color">#ffffff</item>
        <item name="sd_min_radiu">2dp</item>
        <item name="sd_star_max_length">5dp</item>
        <item name="sd_star_width">1.5dp</item>
        <item name="sd_star_count">8</item>
        <item name="sd_draw_star">true</item>
        <item name="sd_need_clip">false</item>
    </style>

    <style name="front_page_item_like_anim_style" parent="@style/base_like_anim_view_style">
        <item name="lab_min_inside_radiu">1dp</item>
        <item name="lab_max_inside_radiu">18dp</item>
        <item name="lab_normal_drawable">@drawable/icon_image_unlike</item>
        <item name="lab_liked_drawable">@drawable/ic_image_feed_like</item>
        <item name="lab_drawable_padding">0dp</item>
        <item name="lab_ring_start_color">#00FFFFFF</item>
        <item name="lab_ring_end_color">#4CFFFFFF</item>
        <item name="lab_ring_drawable_padding">3dp</item>
    </style>

    <style name="image_browser_feed_like_style">
        <item name="android:layout_width">24dp</item>
        <item name="android:layout_height">24dp</item>
        <item name="rd_style">@style/like_view_ring_style</item>
        <item name="sd_style">@style/front_page_item_like_star_style</item>
        <item name="lab_style">@style/front_page_item_like_anim_style</item>
    </style>

    <style name="LineFeedHorizontalFill">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1px</item>
        <item name="android:background">#ebebeb</item>
    </style>
    <style name="lottie_style_in_video">
        <item name="mlav_fps">30</item>
    </style>

    <style name="feed_comment_like_anim_button_style">
        <item name="android:layout_width">36dp</item>
        <item name="android:layout_height">36dp</item>
        <item name="rd_style">@style/like_view_ring_style</item>
        <item name="sd_style">@style/like_view_star_style</item>
        <item name="lab_style">@style/feed_comment_like_anim_view_style</item>
    </style>

    <style name="world_staggered_like_anim_button_style">
        <item name="android:layout_width">38dp</item>
        <item name="android:layout_height">38dp</item>
        <item name="rd_style">@style/like_view_ring_style</item>
        <item name="sd_style">@style/like_view_star_style</item>
        <item name="lab_style">@style/feed_comment_like_anim_view_style</item>
    </style>

    <style name="world_test_staggered_like_anim_button_style">
        <item name="android:layout_width">38dp</item>
        <item name="android:layout_height">38dp</item>
        <item name="rd_style">@style/like_view_ring_style</item>
        <item name="sd_style">@style/like_view_star_style</item>
        <item name="lab_style">@style/world_test_like_anim_view_style</item>
    </style>

    <style name="feed_comment_like_anim_view_style" parent="@style/base_like_anim_view_style">
        <item name="lab_min_inside_radiu">1dp</item>
        <item name="lab_max_inside_radiu">15dp</item>
        <item name="lab_normal_drawable">@drawable/ic_comment_unlike</item>
        <item name="lab_liked_drawable">@drawable/ic_comment_like</item>
        <item name="lab_ring_start_color">#FFFA02</item>
        <item name="lab_ring_end_color">@color/color_brand</item>
        <item name="lab_ring_drawable_padding">3dp</item>
    </style>

    <style name="world_test_like_anim_view_style" parent="@style/base_like_anim_view_style">
        <item name="lab_min_inside_radiu">1dp</item>
        <item name="lab_max_inside_radiu">15dp</item>
        <item name="lab_normal_drawable">@drawable/ic_test_comment_unlike</item>
        <item name="lab_liked_drawable">@drawable/ic_comment_like</item>
        <item name="lab_ring_start_color">#FFFA02</item>
        <item name="lab_ring_end_color">@color/color_brand</item>
        <item name="lab_ring_drawable_padding">3dp</item>
    </style>

    <style name="FeedCommentTitle">
        <item name="android:textColor">#aaaaaa</item>
        <item name="android:textSize">12sp</item>
    </style>

    <style name="FeedCommentDesc">
        <item name="android:textColor">@color/color_323333_to_80f</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="menu_nearby_selected_filter">
        <item name="android:background">@drawable/bg_nearby_dialog_filer</item>
        <item name="android:layout_weight">1.0</item>
        <item name="android:layout_width">0.0dip</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:minHeight">33dip</item>
        <item name="android:gravity">center</item>
        <item name="android:button">@null</item>
        <item name="android:textSize">@dimen/font_28_px</item>
        <item name="android:textColor">@color/btn_nearby_dialog</item>
    </style>

    <style name="menu_nearby_selected_filter.feedOrder">
        <item name="android:background">@drawable/bg_feed_order_filer</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:lineSpacingExtra">4sp</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingTop">5.5dp</item>
        <item name="android:paddingRight">10dp</item>
        <item name="android:paddingBottom">5.5dp</item>
        <item name="android:textSize">15sp</item>
    </style>

    <style name="Popup_Animation_Nearby_Filter" mce_bogus="1" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/nearby_filter_push_up_in</item>
        <item name="android:windowExitAnimation">@anim/nearby_filter_push_up_out</item>
        <item name="android:forceDarkAllowed" tools:targetApi="q">false</item>
    </style>

    <style name="Gene_Selected_Dialog_Animation_DownUp" mce_bogus="1" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/anim_music_panel_in</item>
        <item name="android:windowExitAnimation">@anim/anim_music_panel_out</item>
    </style>

    <!--点赞动画距离校准stubview2-->
    <style name="Like_StubView_Style_Ad">
        <item name="android:layout_width">148dp</item>
        <item name="android:layout_height">123dp</item>
        <item name="android:layout_marginLeft">2dp</item>
        <item name="android:layout_marginBottom">3.5dp</item>
    </style>

    <style name="FeedDataProtectDialog" parent="android:style/Theme.Dialog">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <!--引导窗dialog的样式-->
    <style name="style_guide_dialog" parent="style_dialog_base">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:backgroundDimAmount">0.6</item>
    </style>

    <style name="moment_video_like_anim_button_style">
        <item name="android:layout_width">120dp</item>
        <item name="android:layout_height">120dp</item>
        <item name="android:padding">10dp</item>
        <item name="rd_style">@style/like_view_ring_style</item>
        <item name="sd_style">@style/moment_video_like_view_star_style</item>
        <item name="lab_style">@style/moment_video_like_anim_view_style</item>
    </style>

    <style name="moment_video_like_view_star_style">
        <item name="sd_star_color">#4CFFFFFF</item>
        <item name="sd_min_radiu">22.5dp</item>
        <item name="sd_star_max_length">20dp</item>
        <item name="sd_star_width">3dp</item>
        <item name="sd_star_count">12</item>
        <item name="sd_draw_star">true</item>
        <item name="sd_need_clip">false</item>
    </style>

    <style name="moment_video_like_anim_view_style">
        <item name="lab_ring_init_radiu">22.5dp</item>
        <item name="lab_ring_max_radiu">40dp</item>
        <item name="lab_do_ring_anim_same_time">true</item>
        <item name="lab_min_inside_radiu">21dp</item>
        <item name="lab_max_inside_radiu">38.5dp</item>
        <item name="lab_liked_drawable">@drawable/ic_moment_video_center_like</item>
        <item name="lab_drawable_padding">28dp</item>
        <item name="lab_ring_start_color">#00FFFFFF</item>
        <item name="lab_ring_end_color">#4CFFFFFF</item>
        <item name="lab_ring_drawable_padding">10dp</item>
    </style>

    <!-- Feed列表中的描述文本内容 距离，点赞，评论数-->
    <style name="Feed_List_Text_Item">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:singleLine">true</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/color_969696_to_40fff</item>
        <item name="android:gravity">center</item>
    </style>

    <!--动态 点赞和评论弹窗动画-->
    <style name="Popup_Animation_Feed_More" mce_bogus="1" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/feed_more_right_in</item>
        <item name="android:windowExitAnimation">@anim/feed_more_right_out</item>
    </style>

    <style name="styleBtnFeedChat">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">30dp</item>
        <item name="android:layout_alignParentRight">true</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:layout_gravity">right</item>
        <item name="android:background">@drawable/bg_18dp_round_corner_f9f9f9</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingLeft">15dp</item>
        <item name="android:textAlignment">center</item>
        <item name="android:paddingRight">15dp</item>
        <item name="android:textColor">@color/color_6e6e6e</item>
        <item name="android:textSize">14sp</item>
    </style>

    <!--点赞动画距离校准stubview-->
    <style name="Like_StubView_Style">
        <item name="android:layout_width">115dp</item>
        <item name="android:layout_height">115dp</item>
        <item name="android:layout_marginLeft">2dp</item>
        <item name="android:layout_marginBottom">5dp</item>
    </style>

    <style name="FeedTextViewStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">@dimen/item_feed_padding</item>
        <item name="android:layout_marginRight">@dimen/item_feed_padding</item>
        <item name="android:layout_marginTop">@dimen/item_feed_content_margin_top</item>
    </style>

    <style name="FeedFaillImg">
        <item name="android:layout_width">18dp</item>
        <item name="android:layout_height">18dp</item>
        <item name="android:layout_marginLeft">7dp</item>
        <item name="android:src">@drawable/ic_feed_invalid</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:scaleType">centerCrop</item>
    </style>

    <style name="FeedFaillText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:text">该动态已删除或失效</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:textColor">#aaaaaa</item>
        <item name="android:textSize">16sp</item>
        <item name="android:layout_marginLeft">4dp</item>
    </style>

    <style name="FeedContentTextStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">@dimen/item_feed_padding</item>
        <item name="android:layout_marginRight">@dimen/item_feed_padding</item>
        <item name="android:layout_marginBottom">@dimen/item_feed_content_margin</item>
    </style>

    <!-- Feed 列表的正文内容-->
    <style name="Feed_Text_Content">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/feed_content_text_size</item>
        <item name="android:textColor">@color/feed_text_content</item>
    </style>

    <!--Feed 列表的头像-->
    <style name="Feed_List_HeadImage">
        <item name="android:layout_width">@dimen/headimage_size_48</item>
        <item name="android:layout_height">@dimen/headimage_size_48</item>
        <item name="android:scaleType">centerCrop</item>
    </style>

    <!--Feed 列表的头像（小）-->
    <style name="Feed_List_HeadImage_Small">
        <item name="android:layout_width">@dimen/lba_headimage_size</item>
        <item name="android:layout_height">@dimen/lba_headimage_size</item>
        <item name="android:scaleType">centerCrop</item>
    </style>

    <!-- Feed 列表小头像轮廓-->
    <style name="Feed_List_HeadImage_Small_Frame">
        <item name="android:layout_width">@dimen/headimage_size_48</item>
        <item name="android:layout_height">@dimen/headimage_size_48</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="Feed_Text_List_Desc_New">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/text_feed_list_desc</item>
        <item name="android:textColor">@color/color_969696_to_40fff</item>
    </style>

    <style name="Text_List_Desc_Level2">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/text_feed_list_desc</item>
        <item name="android:textColor">@color/feed_text_sub_color</item>
    </style>

    <style name="style_single_line_cell_trans">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:paddingTop">15dp</item>
        <item name="android:paddingBottom">15dp</item>
        <item name="android:paddingLeft">15dp</item>
        <item name="android:paddingRight">15dp</item>
        <item name="android:background">@drawable/bglistitem_selector_trans_dark</item>
        <item name="drawTopLine">false</item>
        <item name="drawBottomLine">false</item>
        <item name="dlw_lineColor">@color/C03</item>
        <item name="supportDark">true</item>
    </style>

    <!-- 列表上文本 第二级黑 / 用户签名 发现页二级标题 -->
    <style name="Text_List_Content">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/text_list_desc</item>
        <item name="android:textColor">@color/text_content</item>
    </style>

    <!-- 9.0 发布动态，创建房间 -->
    <style name="Popup_Animation_Publish_Feed_Select_lua" mce_bogus="1" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/anim_feed_select_publish_enter_lua</item>
        <item name="android:windowExitAnimation">@anim/anim_feed_select_publish_exit_lua</item>
    </style>

    <!--标题样式-->
    <style name="category_tab_text">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="FindPageNameStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/text_26px</item>
        <item name="android:textColor">#5a5a5a</item>
        <item name="android:layout_marginTop">12dp</item>
    </style>

    <style name="FindPageInfoStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">11sp</item>
        <item name="android:textColor">#ababab</item>
        <item name="android:layout_marginTop">4dp</item>
    </style>

    <style name="feed_NewProfile_Item_Text_Title_9">
        <item name="android:singleLine">true</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textStyle">bold</item>
        <item name="android:paddingBottom">10dp</item>
        <item name="android:textColor">@color/C_12</item>
        <item name="android:textSize">@dimen/font_24_px</item>
    </style>

    <style name="marketing_account_style">
        <item name="android:layout_marginTop">15dp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:clipChildren">false</item>
        <item name="android:orientation">vertical</item>
    </style>

    <style name="common_feed_forward_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="orientation">vertical</item>
    </style>

    <style name="sayhi_edit_dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowSoftInputMode">adjustResize|stateAlwaysVisible</item>
    </style>

    <style name="ZoomViewDialogStyle">
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
</resources>