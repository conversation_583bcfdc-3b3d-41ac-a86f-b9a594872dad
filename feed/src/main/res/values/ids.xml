<?xml version="1.0" encoding="utf-8"?>
<resources>
    <item name="tag_userlist_item" type="id" />
    <item name="tag_item_emotionspan" type="id" />

    <item name="signeditor_iv_icon" type="id"/>
    <item name="tag_item_id" type="id" />

    <item name="tag_webview_id" type="id" />

    <item name="feed_list_rv" type="id" />
    <item name="tag_item_position" type="id" />

    <!--动态发布——MoreInput-->
    <item name="layout_add_emotion" type="id" />
    <item name="layout_add_music" type="id" />
    <item name="layout_add_punch" type="id" />
    <item name="layout_add_movie" type="id" />
    <item name="layout_add_book" type="id" />
    <item name="layout_add_live_photo" type="id" />

    <item name="recyclerview_in_feed_item" type="id" />

    <item name="feed_profile_like_user_tag" type="id" />
    <item name="feed_video_view_tag" type="id" />
    
    <item name="feed_mgs_manager_inner_tag" type="id"/>

    <item name="feed_center_customized_stub" type="id"/>
    <item name="feed_top_customized_stub" type="id"/>
    <item name="feed_bottom_customized_stub" type="id"/>

    <item name="feed_appliers_id" type="id"/>
    <item name="feed_appliers_id_for_applier" type="id"/>

    <item name="id_common_feed_extra_layout" type="id"/>
    <item name="id_common_feed_forward_layout" type="id"/>
    <item name="id_common_feed_resource_view_vs" type="id"/>

    <item name="zoomable" type="id" />
    <item name="unzoomable" type="id" />

    <item name="tag_feed_more_guide" type="id" />

</resources>