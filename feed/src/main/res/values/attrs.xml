<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="ResourceView">
        <attr name="style1IconSize" format="dimension|reference" />
        <attr name="iconMargin" format="dimension|reference" />
        <attr name="style2IconWidth" format="dimension|reference" />
        <attr name="style2IconHeight" format="dimension|reference" />
        <attr name="iconMarginRight" format="dimension|reference" />
        <attr name="titleBottomMargin" format="dimension|reference" />
        <attr name="descMargin" format="dimension|reference" />
        <attr name="titleTextSize" format="dimension|reference" />
        <attr name="descTextSize" format="dimension|reference" />
        <attr name="titleTxtColor" format="color|reference" />
        <attr name="descColor" format="color|reference" />
        <attr name="buttonMargin" format="dimension|reference" />
        <attr name="buttonPaddingL" format="dimension|reference" />
        <attr name="buttonPaddingT" format="dimension|reference" />
        <attr name="buttonPaddingR" format="dimension|reference" />
        <attr name="buttonPaddingB" format="dimension|reference" />
        <attr name="buttonTextSize" format="dimension|reference" />
    </declare-styleable>

    <declare-styleable name="MaxWidthLinearLayout">
        <attr name="layout_maxWidth" format="dimension|reference" />
    </declare-styleable>

    <declare-styleable name="CommonFeedLayout">
        <attr name="feed_center_customized_layout" format="reference" />
        <attr name="feed_top_customized_layout" format="reference" />
        <attr name="feed_bottom_customized_layout" format="reference" />
    </declare-styleable>

    <declare-styleable name="CommonForwardLayout">
        <attr name="error_layout_marginBottom" format="dimension|reference" />
        <attr name="forward_layout_empty_bg" format="boolean" />
        <attr name="forward_layout_child_layout" format="reference" />
    </declare-styleable>

    <declare-styleable name="CommonExtraLayout">
        <attr name="horizontal_layout_margin_left" format="dimension|reference"/>
        <attr name="horizontal_layout_margin_right" format="dimension|reference"/>
        <attr name="horizontal_layout_margin_top" format="dimension|reference"/>
    </declare-styleable>

    <declare-styleable name="CommentViewStyle">
        <attr name="emote_res" format="reference" />
    </declare-styleable>

    <declare-styleable name="RoundFrameLayout">
        <attr name="radius" format="dimension|reference"/>
    </declare-styleable>

</resources>