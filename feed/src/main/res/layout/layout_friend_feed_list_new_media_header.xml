<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/bglistitem_selector_trans_dark"
    android:orientation="vertical"
    android:padding="15dp">

    <ImageView
        android:id="@+id/img_media"
        android:layout_width="41dp"
        android:layout_height="41dp"
        android:src="@drawable/ic_new_media_header"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="8dp"
        android:layout_toRightOf="@id/img_media"
        android:orientation="vertical">

        <TextView
            android:id="@+id/title"
            style="@style/Text_Style_F20"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:text="@string/follow_has_new_feed_title"
            android:textColor="@color/color_1e1e1e"
            android:textSize="@dimen/text_32px" />

        <TextView
            style="@style/Text_Style_F20"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:maxLines="1"
            android:text="分享你的新鲜事..."
            android:textColor="@color/color_aaaaaa"
            android:textSize="@dimen/text_26px" />
    </LinearLayout>

    <View
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:background="@drawable/bg_4dp_round_corner_e8e8e8" />

    <LinearLayout
        android:id="@+id/new_media_list_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="5dp"
        android:clickable="true"
        android:orientation="horizontal"/>
</RelativeLayout>
