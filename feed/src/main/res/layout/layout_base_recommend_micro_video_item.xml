<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="3.5dp"
    android:layout_marginEnd="3.5dp"
    android:layout_marginBottom="25dp"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical">

    <com.immomo.momo.android.view.FixAspectRatioRelativeLayout
        android:id="@+id/rl_texture_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp10"
        app:aspectRatio="0.75"
        app:maxCalHeight="240dp"
        app:minCalHeight="120dp"
        app:radius="8dp"
        tools:layout_width="160dp">

        <com.immomo.momo.frontpage.widget.FrontPageFeedTextureLayout
            android:id="@+id/recommend_video_texture_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <ViewStub
            android:id="@+id/viewstub_micro_video_cover_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="8dp"
            android:layout_marginRight="8dp"
            android:layout="@layout/layout_micro_video_cover_tag"
            tools:visibility="visible" />
    </com.immomo.momo.android.view.FixAspectRatioRelativeLayout>

    <LinearLayout
        android:id="@+id/ll_tag"
        android:layout_width="wrap_content"
        android:layout_height="23dp"
        android:layout_marginBottom="5dp"
        android:background="@drawable/bg_recommend_video_tag"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingStart="8dp"
        android:paddingTop="4dp"
        android:paddingEnd="8dp"
        android:paddingBottom="4dp">

        <ImageView
            android:id="@+id/iv_tag_icon"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_marginEnd="3dp"
            tools:src="@drawable/icon_add_friend" />

        <TextView
            android:id="@+id/tv_tag_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="11sp"
            tools:text="红人推荐" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="3dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="#323333"
        android:textSize="14sp"
        tools:text="夏日炎炎夏日炎炎夏日炎炎夏日炎炎夏日炎炎，有你真甜！甜到你了吗？" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:orientation="horizontal">

        <com.immomo.momo.feedlist.widget.avatarview.CircleAvatarAnimView
            android:id="@+id/iv_avatar"
            android:layout_width="17dp"
            android:layout_height="17dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:anim_ring_width="1"/>

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/color_aaaaaa"
            android:textSize="11sp"
            tools:text="萌萌哒" />

        <ImageView
            android:id="@+id/iv_like_icon"
            android:layout_width="15dp"
            android:layout_height="15dp"
            tools:src="@drawable/icon_recommend_video_like_count" />

        <TextView
            android:id="@+id/tv_like"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:layout_marginStart="3.5dp"
            android:textColor="@color/color_aaaaaa"
            android:textSize="11sp"
            tools:text="25" />



    </LinearLayout>
</LinearLayout>