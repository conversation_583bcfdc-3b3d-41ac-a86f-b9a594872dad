<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <View
        android:id="@+id/view_video_div"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="2dp"
        android:background="@color/color_ebebeb"
        android:visibility="gone"
        tools:visibility="visible" />

    <com.immomo.momo.android.view.FixAspectRatioRelativeLayout
        android:id="@+id/layout_feed_feedvideo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginTop="@dimen/item_feed_content_margin"
        android:background="@color/c_f3f3f3"
        app:radius="4dp"
        tools:minHeight="50dp"
        tools:minWidth="50dp">

        <com.immomo.momo.feed.player.FeedTextureLayout
            android:id="@+id/layout_feed_feedvideoview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true" />

        <ViewStub
            android:id="@+id/livephoto_cover"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout="@layout/vs_live_photo_cover" />

    </com.immomo.momo.android.view.FixAspectRatioRelativeLayout>

</LinearLayout>