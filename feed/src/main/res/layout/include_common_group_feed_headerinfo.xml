<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/feed_group_info_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="horizontal">

    <com.immomo.momo.feedlist.widget.avatarview.CircleAvatarAnimView
        android:id="@+id/iv_group_head"
        style="@style/Avatar_A15"
        android:layout_centerVertical="true"
        android:layout_marginLeft="@dimen/item_feed_padding"
        android:background="@null"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:anim_ring_width="1" />

    <RelativeLayout
        android:id="@+id/layout_group_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="@dimen/feed_padding_12dp"
        android:layout_toRightOf="@id/iv_group_head"
        android:gravity="center_vertical">

        <com.immomo.momo.android.view.EmoteTextView
            android:id="@+id/tv_group_name"
            style="@style/Text_Style_F34"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxWidth="260dp"
            android:singleLine="true"
            tools:text="陌陌是一家互联网s" />


        <com.immomo.momo.util.view.BadgeView
            android:id="@+id/view_badge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_group_name"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:layout_marginLeft="-2dp"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/sign_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_group_name"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone">

            <ImageView
                android:id="@+id/sign_icon"
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:layout_marginRight="2dp"
                android:scaleType="fitXY"
                tools:background="@color/blue" />

            <TextView
                android:id="@+id/sign_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:singleLine="true"
                android:textSize="13sp"
                tools:text="你加入的群" />

        </LinearLayout>

        <Button
            android:id="@+id/header_button"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/item_feed_padding"
            android:background="@drawable/bg_feed_group_party_button"
            android:clickable="false"
            android:maxLines="1"
            android:minWidth="65dp"
            android:paddingLeft="9dp"
            android:paddingRight="9dp"
            android:textSize="14sp"
            android:visibility="gone"
            tools:text="进群"
            tools:visibility="visible" />

    </RelativeLayout>

</RelativeLayout>