<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_selected_emote"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="bottom"
    android:layout_weight="1"
    android:visibility="gone"
    tools:visibility="gone">

    <com.immomo.momo.android.view.MGifImageView
        android:id="@+id/iv_selected_emote"
        android:layout_width="85dip"
        android:layout_height="85dip"
        android:layout_centerInParent="true"
        android:paddingTop="3dip"
        android:paddingRight="3dip"
        android:clickable="true"/>

    <ImageView
        android:id="@+id/iv_delete_emote"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/iv_selected_emote"
        android:layout_alignRight="@+id/iv_selected_emote"
        android:src="@drawable/ic_feed_image_bean_delete_new" />

</RelativeLayout>