<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/recommend_feed_cell_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingTop="9dp"
    android:paddingBottom="9dp">

    <com.immomo.momo.android.view.CircleImageView
        android:id="@+id/listitem_cell_img_icon"
        android:layout_width="55dp"
        android:layout_height="55dp"
        tools:background="@drawable/app_icon" />


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="18dp"
        android:layout_toLeftOf="@+id/bt_join"
        android:layout_toRightOf="@+id/listitem_cell_img_icon"
        android:orientation="vertical">

        <com.immomo.momo.android.view.EmoteTextView
            android:id="@+id/listitem_cell_txt_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="#323333"
            android:textSize="16sp"
            tools:text="就这样子吧" />

        <com.immomo.momo.android.view.EmoteTextView
            android:id="@+id/listitem_cell_txt_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="#929292"
            android:textSize="12sp"
            tools:text="陌陌出" />

    </LinearLayout>

    <Button
        android:id="@+id/bt_join"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:background="@drawable/md_button_blue_feed"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:text="加入"
        android:textColor="@color/white"
        android:textSize="14sp" />

</RelativeLayout>