<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="30dp"
    android:layout_marginRight="30dp"
    android:background="@drawable/bg_15dp_corner_white_dark"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="40dp"
        android:layout_marginRight="20dp"
        android:gravity="center"
        android:text="关注作者防止丢失"
        android:textColor="#323333"
        android:textSize="18sp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="5dp"
        android:layout_marginRight="20dp"
        android:gravity="center"
        android:text="下次观看即可快速找到"
        android:textColor="#AAAAAA"
        android:textSize="14sp" />


    <TextView
        android:id="@+id/video_follow_follow"
        android:layout_width="wrap_content"
        android:minWidth="200dp"
        android:layout_height="50dp"
        android:layout_marginTop="40dp"
        android:layout_marginLeft="@dimen/dp58"
        android:layout_marginRight="@dimen/dp58"
        android:background="@drawable/bg_30dp_round_corner_4e7fff"
        android:gravity="center"
        android:text="关注并退出"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/video_follow_close"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp10"
        android:layout_marginBottom="@dimen/dp10"
        android:padding="@dimen/dp5"
        android:gravity="center"
        android:text="仍然退出"
        android:textColor="#aaaaaa"
        android:textSize="14sp" />


</LinearLayout>