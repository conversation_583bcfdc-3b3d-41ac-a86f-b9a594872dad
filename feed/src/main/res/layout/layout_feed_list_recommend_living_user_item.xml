<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="169dp"
    android:layout_width="169dp"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_living_bg"
        android:layout_height="match_parent"
        android:layout_width="match_parent"
        tools:src="@color/red" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="10dp"
        android:layout_marginLeft="10dp">

        <com.immomo.momo.android.view.CircleImageView
            android:id="@+id/iv_user_avatar"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_centerVertical="true"
            tools:src="@drawable/ic_common_def_header_round" />

        <com.immomo.momo.android.view.EmoteTextView
            android:id="@+id/tv_user_name"
            android:singleLine="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="@color/white"
            android:layout_toRightOf="@+id/iv_user_avatar"
            android:layout_marginLeft="8dp"
            tools:text="婚姻" />

        <com.immomo.momo.android.view.HandyTextView
            android:id="@+id/tv_user_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:layout_below="@+id/tv_user_name"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="3dp"
            android:layout_toRightOf="@+id/iv_user_avatar"
            android:layout_marginLeft="8dp"
            android:textSize="12sp"
            android:textColor="@color/white"
            tools:text="551万人已加入" />

    </RelativeLayout>


</RelativeLayout>