<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_selected_video_thubnail"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_weight="1"
    android:gravity="center_vertical|bottom"
    android:orientation="vertical"
    android:visibility="gone"
    tools:visibility="gone">

    <FrameLayout
        android:id="@+id/layout_selected_video_thubnail_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.immomo.momo.android.view.FixAspectRatioRelativeLayout
            android:id="@+id/layout_feed_feedvideo"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:layout_marginRight="10dp"
            app:radius="4dp">

            <com.immomo.momo.feed.player.ExoTextureLayout
                android:id="@+id/video_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                tools:background="@color/red" />

            <ImageView
                android:id="@+id/video_cover"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </com.immomo.momo.android.view.FixAspectRatioRelativeLayout>


        <ImageView
            android:id="@+id/video_tbubnail_remove_video"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:src="@drawable/ic_feed_image_bean_delete_new" />

    </FrameLayout>


</RelativeLayout>