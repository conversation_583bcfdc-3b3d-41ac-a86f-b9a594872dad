<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:orientation="vertical">

    <com.immomo.momo.android.view.image.SquareImageGridLayout
        android:id="@+id/feed_image_gridlayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="10dp"
        app:imageColumns="3"
        app:imageHorizontalSpace="2.5dp"
        app:imageRadius="4dp"
        app:imageRatio="1"
        app:imageVerticalSpace="2.5dp"
        app:maxImageCount="9"
        app:singleImageScaleSize="1.7"
        tools:visibility="gone" />
    <!--用于显示表情-->
    <ViewStub
        android:id="@+id/iv_feed_image_vs"
        android:layout_width="@dimen/feed_listitem_image_size"
        android:layout_height="@dimen/feed_listitem_image_size"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="7dp"
        android:layout_marginRight="10dp"
        android:layout="@layout/listitem_common_feed_iv_image_vs" />

    <ViewStub
        android:id="@+id/gv_feed_img_vs"
        android:layout_width="@dimen/feed_listitem_image_size"
        android:layout_height="@dimen/feed_listitem_image_size"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="7dp"
        android:layout_marginRight="10dp"
        android:layout="@layout/listitem_common_feed_gv_img_vs" />

    <!--图片比例做调整 大概宽高比是 12:7 -->
    <ViewStub
        android:id="@+id/feed_list_city_card_vs"
        android:layout_width="300dp"
        android:layout_height="180dp"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="7dp"
        android:layout_marginRight="10dp"
        android:layout="@layout/listitem_common_feed_city_card_vs" />

    <RelativeLayout
        android:id="@+id/feed_resource_shadow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="7dp"
        android:layout_marginRight="10dp"
        android:background="@color/transparent"
        android:visibility="visible"
        android:orientation="vertical"
        >

        <ViewStub
            android:id="@id/id_common_feed_resource_view_vs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout="@layout/listitem_common_feed_resource_view_vs" />

        <ViewStub
            android:id="@+id/tv_photo_together_tip_vs"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_alignParentRight="true"
            android:layout_marginTop="-40dp"
            android:layout_marginRight="21dp"
            android:layout="@layout/listitem_common_feed_tip_text_view_vs"
            />

        <ViewStub
            android:id="@+id/triangle_view_vs"
            android:layout_width="24dp"
            android:layout_height="9.5dp"
            android:layout_alignParentRight="true"
            android:layout_marginRight="39dp"
            android:layout="@layout/listitem_common_feed_bottom_triangle_view_vs"/>

    </RelativeLayout>

</LinearLayout>