<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ll_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white_ffffff"
    android:orientation="vertical"
    android:gravity="center_horizontal">

    <com.immomo.momo.feed.ui.view.FeedTextView
        android:id="@+id/feed_textview"
        style="@style/FeedTextViewStyle"
        tools:layout_height="56dp" />

    <!-- 转发动态 -->
    <com.immomo.momo.feedlist.itemmodel.linear.common.view.CommonForwardLayout
        android:id="@id/id_common_feed_forward_layout"
        style="@style/common_feed_forward_style"
        app:forward_layout_empty_bg="true"
        app:forward_layout_child_layout="@layout/layout_common_mgs_game_forward_child"/>

    <ViewStub
        android:id="@id/id_common_feed_resource_view_vs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/feed_padding_20dp"
        android:layout_marginTop="@dimen/item_feed_content_margin"
        android:layout_marginRight="@dimen/feed_padding_20dp"
        android:layout="@layout/listitem_common_feed_resource_view_vs"
        android:visibility="gone"
        tools:layout_height="75dp"
        tools:visibility="visible" />

    <include layout="@layout/layout_feed_model_bottom" />

</LinearLayout>