<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.immomo.momo.feed.ui.view.FeedTextView
        android:id="@+id/feed_textview"
        style="@style/FeedTextViewStyle"
        tools:layout_height="56dp" />

    <!-- 转发动态 -->
    <com.immomo.momo.feedlist.itemmodel.linear.common.view.CommonForwardLayout
        android:id="@id/id_common_feed_forward_layout"
        style="@style/common_feed_forward_style"
        app:forward_layout_child_layout="@layout/layout_common_micro_video_forward_child"/>

    <ViewStub
        android:id="@id/id_common_feed_resource_view_vs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/item_feed_padding"
        android:layout_marginTop="@dimen/item_feed_content_margin"
        android:layout_marginRight="@dimen/item_feed_padding"
        android:layout="@layout/listitem_common_feed_resource_view_vs"
        android:visibility="gone"
        tools:layout_height="75dp"
        tools:visibility="visible" />

    <include layout="@layout/layout_feed_model_bottom" />

</LinearLayout>