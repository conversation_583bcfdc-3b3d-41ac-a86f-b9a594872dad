<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_all"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/item_feed_margin"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:descendantFocusability="blocksDescendants"
    android:orientation="vertical">

    <!--用户头像及名称-->
    <include layout="@layout/include_common_feed_userinfo_with_button" />

    <com.immomo.momo.feed.ui.view.FeedTextView
        android:id="@+id/tv_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/item_feed_padding"
        android:layout_marginEnd="@dimen/item_feed_padding"
        android:layout_marginTop="@dimen/item_feed_content_margin"
        android:textColor="@color/feed_mgs_gray"
        android:textSize="16sp" />

    <GridLayout
        android:id="@+id/rl_im_root"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/item_feed_padding"
        android:layout_marginTop="@dimen/item_feed_content_margin"
        android:columnCount="2"
        android:rowCount="2">

        <com.immomo.momo.android.view.MGifImageView
            android:id="@+id/im_gif1"
            android:layout_width="105dp"
            android:layout_height="105dp"
            android:layout_marginRight="2.5dp"
            android:layout_marginBottom="2.5dp"
            android:background="@color/avatar_default_color"
            android:scaleType="centerCrop"
            app:radius="4dp" />

        <com.immomo.momo.android.view.MGifImageView
            android:id="@+id/im_gif2"
            android:layout_width="105dp"
            android:layout_height="105dp"
            android:background="@color/avatar_default_color"
            android:scaleType="centerCrop"
            app:radius="4dp" />

        <com.immomo.momo.android.view.MGifImageView
            android:id="@+id/im_gif3"
            android:layout_width="105dp"
            android:layout_height="105dp"
            android:background="@color/avatar_default_color"
            android:scaleType="centerCrop"
            app:radius="4dp" />

        <com.immomo.momo.android.view.MGifImageView
            android:id="@+id/im_gif4"
            android:layout_width="105dp"
            android:layout_height="105dp"
            android:background="@color/avatar_default_color"
            android:scaleType="centerCrop"
            app:radius="4dp" />
    </GridLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="@dimen/item_feed_padding"
        android:paddingStart="@dimen/item_feed_padding"
        android:visibility="visible">

        <TextView
            android:id="@+id/txt_from"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:textColor="@color/color_aaaaaa"
            android:textSize="12sp"
            tools:text="来自群聊" />

        <ImageView
            android:id="@+id/btn_feed_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="5dp"
            android:src="@drawable/ic_feed_more_black"
            android:layout_gravity="end"/>
    </FrameLayout>

    <View
        android:id="@+id/bottom_line"
        style="@style/StyleFeedDivider"
        android:layout_marginTop="15dp" />
</LinearLayout>