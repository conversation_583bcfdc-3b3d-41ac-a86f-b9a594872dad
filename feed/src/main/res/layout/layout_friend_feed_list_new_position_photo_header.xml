<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/bglistitem_selector_trans_dark"
    android:orientation="vertical"
    android:padding="20dp">

    <View
        android:id="@+id/shadow"
        android:layout_width="37dp"
        android:layout_height="37dp"
        android:layout_centerVertical="true"
        android:layout_marginLeft="6dp"
        android:background="@drawable/bg_4dp_round_corner_e8e8e8" />

    <com.immomo.momo.android.view.RoundCornerImageView
        android:id="@+id/media_image"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:scaleType="fitXY"
        app:radius="4dp" />


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="12dp"
        android:layout_toLeftOf="@+id/no_use_right"
        android:layout_toRightOf="@+id/media_image"
        android:orientation="vertical">

        <TextView
            android:id="@+id/title"
            style="@style/Text_Style_F20"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/follow_has_new_feed_title"
            android:textColor="@color/color_1e1e1e"
            android:textSize="@dimen/text_30px" />

        <TextView
            android:id="@+id/description"
            style="@style/Text_Style_F20"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/follow_has_new_feed_content"
            android:textColor="@color/color_aaaaaa"
            android:textSize="@dimen/text_26px" />
    </LinearLayout>

    <ImageView
        android:id="@+id/no_use_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:src="@drawable/ic_common_arrow_right" />

</RelativeLayout>
