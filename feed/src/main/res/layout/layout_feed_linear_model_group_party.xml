<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="@dimen/item_feed_margin">

    <include layout="@layout/include_common_group_feed_headerinfo" />

    <RelativeLayout
        android:layout_width="300dp"
        android:layout_height="180dp"
        android:layout_marginLeft="@dimen/item_feed_padding"
        android:layout_marginTop="@dimen/item_feed_content_margin"
        tools:background="@color/blue">

        <com.immomo.momo.android.view.RoundCornerImageView
            android:id="@+id/bg_group_party"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            app:radius="8dp"
            tools:background="@color/blue" />

        <TextView
            android:id="@+id/party_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="18dp"
            android:layout_marginBottom="13dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/white"
            android:textSize="16sp"
            tools:text="聚会名聚会名聚会名聚会名聚会名聚会名聚会名聚会名聚会名聚会名聚会名" />

        <LinearLayout
            android:id="@+id/party_time_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/party_name"
            android:layout_marginLeft="20dp"
            android:layout_marginBottom="5dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/time_icon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:scaleType="fitXY"
                tools:background="@color/black" />

            <TextView
                android:id="@+id/time_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="12sp"
                tools:text="聚会时间聚会时间聚会时间聚会时间聚会时间聚会时间聚会时间" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/party_site_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/party_time_container"
            android:layout_marginLeft="20dp"
            android:layout_marginBottom="5dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/site_icon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:scaleType="fitXY"
                tools:background="@color/black" />

            <TextView
                android:id="@+id/site_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="12sp"
                tools:text="聚会地点聚会地点聚会地点聚会地点聚会地点聚会地点聚会地点" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/party_charge_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/party_site_container"
            android:layout_marginLeft="20dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/charge_icon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:scaleType="fitXY"
                tools:background="@color/black" />

            <TextView
                android:id="@+id/charge_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="12sp"
                tools:text="付费情况付费情况付费情况付费情况付费情况付费情况" />

        </LinearLayout>

        <Button
            android:id="@+id/btn_group_party"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="20dp"
            android:background="@drawable/bg_feed_group_party_bottom_btn"
            android:gravity="center"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:textColor="#323333"
            android:textSize="14sp"
            tools:text="马上报名" />

    </RelativeLayout>

    <View
        android:id="@+id/bottom_line"
        style="@style/StyleFeedDivider"
        android:layout_marginTop="15dp" />
</LinearLayout>