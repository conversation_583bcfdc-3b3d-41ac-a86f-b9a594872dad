<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="55dp"
    android:orientation="vertical"
    tools:layout_width="172dp">

    <com.immomo.momo.android.view.image.SmartImageView
        android:id="@+id/section_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"/>

    <View
        android:id="@+id/section_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_round_micro_video_topic"
        tools:background="@color/red"/>

    <LinearLayout
        android:id="@+id/section_cover_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:paddingLeft="13dp"
        android:paddingRight="13dp">

        <ImageView
            android:id="@+id/section_icon"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:adjustViewBounds="true"
            android:scaleType="centerCrop"
            tools:background="@color/green"/>

        <TextView
            android:id="@id/section_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_weight="1"
            android:singleLine="true"
            android:textColor="#ffffff"
            android:textSize="15sp"
            tools:text="五一一去哪儿一去哪儿一去哪儿去哪儿玩"/>
    </LinearLayout>
</RelativeLayout>