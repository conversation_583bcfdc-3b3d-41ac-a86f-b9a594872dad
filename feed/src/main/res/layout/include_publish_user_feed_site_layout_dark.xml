<?xml version="1.0" encoding="utf-8"?>
<com.immomo.framework.view.lineview.DrawLineLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_site"
    style="@style/Cell_Ce1"
    android:paddingRight="0dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bglistitem_selector_white_dark"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    app:lineRightMargin="@dimen/item_feed_padding"
    app:lineLeftMargin="@dimen/item_feed_padding"
    app:supportDark="true">

    <com.immomo.momo.android.view.HandyTextView
        android:id="@+id/publish_user_feed_site_desc"
        style="@style/Text_List_Title"
        android:singleLine="true"
        android:text="选择地点"
        android:textColor="@color/FC2" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_weight="1"
        android:gravity="right|center_vertical">

        <com.immomo.momo.android.view.HandyTextView
            android:id="@+id/tv_feed_site_selected"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            tools:text="望京SOHO"
            android:textColor="@color/FC9"
            android:textSize="@dimen/text_list_desc"
            android:drawablePadding="2dp"
            android:visibility="gone"
            tools:visibility="visible"/>

    </LinearLayout>

    <ImageView
        android:id="@+id/clear_feed_site_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="10dp"
        android:paddingTop="4dp"
        android:paddingBottom="4dp"
        android:paddingRight="15dp"
        android:src="@drawable/ic_common_close"
        android:visibility="gone"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/iv_site_arrow"
        android:paddingRight="15dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:src="@drawable/ic_common_arrow_right" />
</com.immomo.framework.view.lineview.DrawLineLinearLayout>
