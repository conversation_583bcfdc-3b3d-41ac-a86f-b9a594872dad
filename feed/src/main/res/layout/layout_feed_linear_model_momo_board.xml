<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:descendantFocusability="blocksDescendants"
    android:orientation="vertical"
    android:paddingStart="@dimen/item_feed_padding"
    android:paddingTop="@dimen/item_feed_margin"
    android:paddingEnd="@dimen/item_feed_padding"
    android:paddingBottom="@dimen/item_feed_margin">

    <LinearLayout
        android:id="@+id/ll_header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.immomo.momo.android.view.CircleImageView
            android:id="@+id/iv_user_head"
            style="@style/Avatar_A15"
            android:layout_gravity="center_vertical"
            android:background="@null"
            tools:src="@drawable/app_icon" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@id/iv_user_head"
                android:orientation="horizontal">
                <!-- 用户名称 -->
                <com.immomo.momo.android.view.EmoteTextView
                    android:id="@+id/tv_user_name"
                    style="@style/Text_Style_F34"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:paddingRight="4dp"
                    android:singleLine="true"
                    tools:text="MOMO附近店铺附近店铺附近店铺附近店铺附近店铺" />

            </LinearLayout>

            <com.immomo.momo.util.view.BadgeView
                android:id="@+id/view_badge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/first_line"
                android:layout_marginTop="4dp"
                android:orientation="horizontal"
                android:visibility="gone" />

            <com.immomo.momo.android.view.adaptive.AdaptiveLayout
                android:id="@+id/feed_list_lable_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="4dp"
                tools:layout_height="15dp"
                tools:layout_width="100dp" />
        </LinearLayout>

    </LinearLayout>
    <!-- 正文文本 -->
    <com.immomo.momo.feed.ui.view.FeedTextView
        android:id="@+id/feed_textview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/ll_header_layout"
        android:layout_marginTop="@dimen/item_feed_content_margin"
        tools:background="#f0f0f0"
        tools:layout_height="56dp" />
</RelativeLayout>
