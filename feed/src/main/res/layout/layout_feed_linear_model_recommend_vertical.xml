<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:descendantFocusability="blocksDescendants"
    android:orientation="vertical">


    <RelativeLayout
        android:id="@+id/listitem_recommend_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/item_feed_padding"
        android:paddingTop="@dimen/item_feed_margin"
        android:paddingRight="@dimen/item_feed_padding"
        android:paddingBottom="8dp">

        <com.immomo.momo.android.view.HandyTextView
            android:id="@+id/listitem_recommend_tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:textColor="#323333"
            android:textSize="12sp"
            android:textStyle="bold"
            tools:text="群组推荐" />


        <TextView
            android:id="@+id/group_feed_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:text="更多"
            android:textColor="#aaaaaa"
            android:textSize="12sp" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/listitem_recommend_item_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/item_feed_padding"
        android:layout_marginRight="@dimen/item_feed_padding"
        android:orientation="vertical">

        <include
            android:id="@+id/recommend_vertical_cell_1"
            layout="@layout/layout_recommend_group_vertical_cell" />

        <include
            android:id="@+id/recommend_vertical_cell_2"
            layout="@layout/layout_recommend_group_vertical_cell" />
    </LinearLayout>


    <View
        style="@style/StyleFeedDivider"
        android:layout_marginTop="15dp" />

</LinearLayout>