<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/content"
    android:orientation="vertical">


    <LinearLayout
        android:clickable="true"
        android:focusable="true"
        android:background="@drawable/bg_nearby_filter_dialog_window"
        android:layout_width="match_parent"
        android:layout_height="391dp"
        android:layout_gravity="bottom"
        android:orientation="vertical">

        <FrameLayout
            android:padding="15dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:src="@drawable/ic_publish_popupwindow_close"
                android:id="@+id/close"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center_vertical" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="同步到其他平台"
                android:textColor="#323333"
                android:textSize="15sp" />
        </FrameLayout>

        <com.immomo.momo.publish.weight.PublishSynchronizeLayout
            android:clickable="true"
            android:focusable="true"
            android:id="@+id/synchronize_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>

    <Button
        android:layout_gravity="bottom"
        android:background="@drawable/bg_publish_synchronize_popup_window_save"
        android:layout_marginLeft="20dp"
        android:layout_marginBottom="20dp"
        android:layout_marginRight="20dp"
        android:text="完成"
        android:id="@+id/save"
        android:textSize="14sp"
        android:textColor="@color/white"
        android:layout_width="match_parent"
        android:textStyle="bold"
        android:layout_height="55dp" />


</FrameLayout>