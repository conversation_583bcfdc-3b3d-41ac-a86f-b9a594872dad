<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/feed_user_info_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="horizontal">

    <!-- 头像 -->
    <RelativeLayout
        android:id="@+id/avatar_container"
        android:layout_width="wrap_content"
        android:layout_height="42dp"
        android:layout_marginLeft="@dimen/item_feed_padding"
        android:clipChildren="false"
        android:clipToPadding="false">

        <com.immomo.momo.feedlist.widget.avatarview.CircleAvatarAnimView
            android:id="@+id/iv_user_head"
            style="@style/Avatar_A15"
            android:layout_centerVertical="true"
            android:background="@null"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:anim_ring_width="1" />

        <View
            android:id="@+id/iv_user_online_status"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_below="@+id/iv_user_head"
            android:layout_marginLeft="-12dp"
            android:layout_marginTop="-14dp"
            android:layout_toRightOf="@id/iv_user_head"
            android:background="@drawable/feed_bg_circle_1dd36e"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/text_feed_online_tag"
            android:layout_width="24dp"
            android:layout_height="12dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:background="@drawable/bg_nearby_live_cornered"
            android:gravity="center"
            android:textColor="#FFF"
            android:textSize="8sp"
            android:visibility="gone" />


        <com.immomo.momo.feedlist.view.CircleImageViewBg
            android:id="@+id/iv_user_daily_mood"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:visibility="gone"
            android:layout_below="@+id/iv_user_head"
            android:layout_toEndOf="@+id/iv_user_head"
            android:layout_marginStart="-20.5dp"
            android:layout_marginTop="-20.5dp"
            android:scaleType="centerCrop"
            app:border_color="@color/color_1DD36E"
            android:src="@drawable/ic_common_def_header"
            app:border_width="1.5dp"
            tools:visibility="visible" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/feed_layout_btn_group_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:gravity="center_vertical"
        android:orientation="horizontal">

    </LinearLayout>


    <RelativeLayout
        android:id="@+id/layout_user_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="@dimen/feed_padding_12dp"
        android:layout_toLeftOf="@id/feed_layout_btn_group_layout"
        android:layout_toRightOf="@id/avatar_container"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/first_line"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">
            <!-- 用户名称 -->
            <com.immomo.momo.android.view.EmoteTextView
                android:id="@+id/tv_user_name"
                style="@style/Text_Style_F34"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:maxWidth="260dp"
                android:singleLine="true"
                tools:text="陌陌是一家互联网s" />

            <!--真人认证-->
            <ViewStub
                android:id="@+id/view_stub_real_man"
                style="@style/RealMan_Auth_Icon.Small"
                android:layout_toRightOf="@id/tv_user_name"
                android:layout="@layout/viewstub_real_man_auth" />
            <!-- 背景图片，高度是头像高度＋空白-->

            <com.immomo.framework.view.widget.ShimmerFrameLayout
                android:id="@+id/feed_vip_bg_image_shi"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:visibility="gone"
                app:auto_start="true"
                app:duration="1600"
                app:relative_width="1"
                app:repeat_count="0"
                app:repeat_delay="1000"
                app:shape="linear_custom">

                <ImageView
                    android:id="@+id/feed_bg_image"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/feed_svip"
                    tools:visibility="visible" />

            </com.immomo.framework.view.widget.ShimmerFrameLayout>


        </LinearLayout>

        <com.immomo.momo.util.view.BadgeView
            android:id="@+id/view_badge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/first_line"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/ll_badge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/first_line"
            android:layout_marginTop="4dp"
            android:orientation="horizontal">

            <com.immomo.momo.android.view.badgeview.FeedBadgeView
                android:id="@+id/feed_list_badgeview2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical" />

            <com.immomo.momo.android.view.HandyTextView
                android:id="@+id/tv_feed_hideinfo"
                style="@style/Text_Style_F22"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:drawableLeft="@drawable/ic_feed_hideinfo"
                android:drawablePadding="2dp"
                android:singleLine="true"
                android:visibility="gone"
                tools:text="只给自己看" />

            <!--推荐理由-->
            <com.immomo.momo.android.view.HandyTextView
                android:id="@+id/tv_feed_recommend"
                style="@style/Text_Style_F10"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:maxEms="30"
                android:singleLine="true"
                android:visibility="gone"
                tools:text="你很爱发视频"
                tools:visibility="gone" />

            <com.immomo.momo.android.view.HandyTextView
                android:id="@+id/feed_tv_top"
                style="@style/normal_Label_style"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="2dp"
                android:background="@drawable/bg_tag_feed_top"
                android:text="置顶"
                android:visibility="gone"
                tools:visibility="visible" />

        </LinearLayout>

        <!--尾部加入button-->
        <TextView
            android:id="@+id/btn_group_chat"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:background="@drawable/feed_bg_corner_19dp_f9f9f8"
            android:gravity="center"
            android:paddingLeft="@dimen/feed_padding_15dp"
            android:paddingTop="5dp"
            android:paddingRight="@dimen/feed_padding_15dp"
            android:layout_marginEnd="40dp"
            android:paddingBottom="5dp"
            android:textColor="@color/color_6e6e6e"
            android:textSize="14sp"
            tools:text="加入群聊" />
    </RelativeLayout>
</RelativeLayout>