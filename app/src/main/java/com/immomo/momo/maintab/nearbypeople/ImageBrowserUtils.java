package com.immomo.momo.maintab.nearbypeople;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;

import com.cosmos.mdlog.MDLog;
import com.immomo.android.module.feedlist.domain.model.style.common.TextPicFeedModel;
import com.immomo.android.module.feedlist.domain.model.style.inner.CrossPromotionUrl;
import com.immomo.android.router.momo.business.ImageBrowserHelper;
import com.immomo.framework.imageloader.ImageType;
import com.immomo.framework.utils.UIUtils;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.feed.service.FeedTransmitService;
import com.immomo.momo.imagefactory.imageborwser.ImageBrowserConfig;
import com.immomo.momo.imagefactory.imageborwser.impls.FeedImageBrowserActivity;
import com.immomo.momo.imagefactory.imageborwser.impls.ImageBrowserActivity;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;


/**
 * 图片预览工具类
 * <AUTHOR>
 * on 2019/8/8.
 */
public class ImageBrowserUtils {
    public static void openImageBrowser(int index, Context context, String[] thumbs, String[] origins, View view) {
        if (context == null){
            return;
        }
        Intent it = new Intent(context, ImageBrowserActivity.class);
        ImageBrowserConfig config = new ImageBrowserConfig.Builder()
                .imageLargeUrls(origins)
                .imageThumbUrls(thumbs)
                .imageMode(ImageBrowserConfig.Mode.IMAGE_URL)
                .imageType(ImageBrowserConfig.Type.TYPE_AVATAR)
                .startIndex(index)
                .imageBounds(getImageBounds(view, true))
                .build();
        it.putExtra(ImageBrowserActivity.KEY_CONFIG, config);
        Activity act = null;

        context.startActivity(it);
        if (context instanceof Activity) {
            act = ((Activity) context);
        } else if (view != null) {
            act = com.immomo.momo.util.MomoKit.INSTANCE.getActivityWithView(view);
        }
        if (act != null) {
            act.overridePendingTransition(R.anim.zoom_enter, R.anim.normal);
        }
    }

    public static void openFeedImageBrowser(View v, List<TextPicFeedModel> list, int index, boolean useViewBounds, boolean isFromImpressionPhoto) {
        if (list == null || list.isEmpty() || v == null) {
            return;
        }
        TextPicFeedModel album = index < list.size() ? list.get(index) : list.get(0);
        ArrayList<String> feedIdList = new ArrayList<>();
        List<String> thumbUrls = new ArrayList<>();
        List<String> originUrls = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            TextPicFeedModel model = list.get(i);
            feedIdList.add(model.getFeedId());
            CrossPromotionUrl crossInfo = model.getCrossPromotionUrl().orNull();
            if (crossInfo != null) {
                thumbUrls.add(crossInfo.getFeedImg());
                originUrls.add(crossInfo.getOriginalFeedImg());
            }

            if (FeedTransmitService.INSTANCE.get(model.getFeedId()) == null) {
                FeedTransmitService.INSTANCE.put(model.getFeedId(), model);
            }
        }

        gotoImageBrowserActivity(index, com.immomo.momo.util.MomoKit.INSTANCE.getActivityWithView(v), album, getImageBounds(v, useViewBounds),
                thumbUrls, originUrls, feedIdList, list.get(list.size() - 1).getCrossPromotionGuid(), isFromImpressionPhoto);
    }

    public static Rect[] getImageBounds(View v, boolean useViewBounds) {
        return ImageBrowserHelper.Companion.getImageBounds(v, useViewBounds);
    }

    private static void gotoImageBrowserActivity(int index, Activity activity, TextPicFeedModel album, Rect[] imageBounds,
                                                 @NonNull List<String> thumbUrls, @NonNull List<String> originUrls,
                                                 ArrayList<String> feedIdList, String cross_promotion_guid, boolean isFromImpressionPhoto) {


        Intent intent = new Intent(MomoKit.getContext(), FeedImageBrowserActivity.class);
        JSONObject extra = new JSONObject();
        try {
            extra.put(FeedImageBrowserActivity.KEY_HEADER_AUTOHIDE, true);
            extra.put(FeedImageBrowserActivity.KEY_FEED_ID, album.getFeedId());
            extra.put(FeedImageBrowserActivity.KEY_REMOTE_ID, album.getUserId());
            extra.put(FeedImageBrowserActivity.KEY_IS_FROM_IMPRESSION_PHOTO, isFromImpressionPhoto);
            extra.put(FeedImageBrowserActivity.KEY_LAST_IMAGE_GUID, cross_promotion_guid);
            intent.putStringArrayListExtra(FeedImageBrowserActivity.KEY_FEED_ID_LIST, feedIdList);
        } catch (JSONException e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }

        ImageBrowserConfig config = new ImageBrowserConfig.Builder()
                .imageType(ImageBrowserConfig.Type.TYPE_FEED)
                .startIndex(index)
                .imageBounds(imageBounds)
                .thumbImageType(ImageType.IMAGE_TYPE_FEEDIMG_400X400)
                .imageThumbUrls(thumbUrls.toArray(new String[0]))
                .imageLargeUrls(originUrls.toArray(new String[0]))
                .extraData(extra.toString())
                .imageMode(ImageBrowserConfig.Mode.IMAGE_URL)
                .build();
        intent.putExtra(FeedImageBrowserActivity.KEY_CONFIG, config);
        if (activity != null) {
            activity.startActivity(intent);
            if (activity.getParent() != null) {
                activity.getParent().overridePendingTransition(0, 0);
            } else {
                activity.overridePendingTransition(R.anim.feed_image_enter, 0);
            }
        }
    }

    public static void onlyRecommendMode(Context context, String momoid, String avatar) {
        if (context == null){
            return;
        }
        android.graphics.Rect[] imageBounds = new android.graphics.Rect[1];
        imageBounds[0] = new android.graphics.Rect(UIUtils.getScreenWidth() / 2, UIUtils.getScreenHeight() / 2, UIUtils.getScreenWidth() / 2, UIUtils.getScreenHeight() / 2);
        Intent intent = new Intent(context, FeedImageBrowserActivity.class);
        JSONObject extra = new JSONObject();
        try {
            extra.put(FeedImageBrowserActivity.KEY_HEADER_AUTOHIDE, true);
            extra.put(FeedImageBrowserActivity.KEY_FROM_GID, "");
            extra.put(FeedImageBrowserActivity.KEY_REMOTE_ID, momoid);
            extra.put(FeedImageBrowserActivity.KEY_IS_FROM_RECOMMEND, true);
            extra.put(FeedImageBrowserActivity.KEY_IS_SHOW_RECOMMEND_IMAGE, true);
            extra.put(FeedImageBrowserActivity.KEY_USER_AVATAR, avatar);
        } catch (JSONException e) {
            MDLog.printErrStackTrace(com.immomo.LogTag.COMMON, e);
        }

        ImageBrowserConfig config = new ImageBrowserConfig.Builder()
                .imageType(ImageBrowserConfig.Type.TYPE_FEED)
                .startIndex(0)
                .imageBounds(imageBounds)
                .thumbImageType(ImageType.IMAGE_TYPE_FEEDIMG_400X400)
                .imageThumbUrls(new String[]{})
                .imageLargeUrls(new String[]{})
                .extraData(extra.toString())
                .imageMode(ImageBrowserConfig.Mode.IMAGE_URL)
                .build();
        intent.putExtra(FeedImageBrowserActivity.KEY_CONFIG, config);
        intent.putExtra(FeedImageBrowserActivity.KEY_ONLY_RECOMMEND_ELEMENT, true);
        context.startActivity(intent);
        if (context instanceof Activity) {
            Activity activity = (Activity) context;
            if (activity.getParent() != null) {
                activity.getParent().overridePendingTransition(0, 0);
            } else {
                activity.overridePendingTransition(R.anim.feed_image_enter, 0);
            }
        }
    }

    /**
     * 图片大小单位转换
     */
    public static String getImageSizeStr(long imageSize) {
        String str;
        if (imageSize > 1024 * 1024) {
            str = ((float) Math.round(10f * imageSize / (1024 * 1024))) / 10 + "M";
        } else if (imageSize > 1024) {
            str = ((float) Math.round(10f * imageSize / (1024))) / 10 + "K";
        } else {
            str = imageSize + "B";
        }
        return str;
    }
}
