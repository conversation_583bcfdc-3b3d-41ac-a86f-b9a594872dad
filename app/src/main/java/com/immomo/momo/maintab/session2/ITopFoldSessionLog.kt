package com.immomo.momo.maintab.session2

import com.immomo.lcapt.evlog.anno.ClickPoint
import com.immomo.lcapt.evlog.anno.ExposurePoint
import com.immomo.lcapt.evlog.anno.Param

interface ITopFoldSessionLog {
    @ExposurePoint(requireId = "14938", page = "msg.msg_notice", action = "list.card")
    fun foldedNotificationItemShow(
        @Param("which_item") whichItem: String,
        @Param("is_reddot") isRedDot: Int,
        @Param("news_number") newsNumber: Int,
        @Param("pos") position: Int,
        @Param("update_time") updateTimeStr: String,
        @Param("msg_text") msgText: String,
        @Param("remoteid") remoteID: String,
        @Param("biz_name") bizName: String
    )
    @ClickPoint(requireId = "14939", page = "msg.msg_notice", action = "list.card")
    fun foldedNotificationItemClick(
        @Param("which_item") whichItem: String,
        @Param("is_reddot") isRedDot: Int,
        @Param("news_number") newsNumber: Int,
        @Param("pos") position: Int,
        @Param("update_time") updateTimeStr: String,
        @Param("msg_text") msgText: String,
        @Param("remoteid") remoteID: String,
        @Param("biz_name") bizName: String
    )
    @ExposurePoint(requireId = "14940", page = "msg.msg_notice", action = "banner.banner")
    fun foldedNotificationBannerShow(
        @Param("image_url") url: String,
        @Param("banner_id") id: String,
        @Param("pos") position: Int
    )
    @ClickPoint(requireId = "14941", page = "msg.msg_notice", action = "banner.banner")
    fun foldedNotificationBannerClick(
        @Param("image_url") url: String,
        @Param("banner_id") id: String,
        @Param("pos") position: Int
    )

}