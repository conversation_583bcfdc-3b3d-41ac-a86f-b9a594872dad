package com.immomo.momo.maintab.session2.presentation.fragment

import android.content.DialogInterface
import android.graphics.Rect
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.cosmos.mdlog.MDLog
import com.hello.group.modules.hlog.momo.AppLifecycleManager
import com.immomo.android.mm.cement2.AsyncCementModel
import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.android.mm.kobalt.presentation.view.cementBuilder
import com.immomo.android.mm.kobalt.presentation.viewmodel.pageViewModel
import com.immomo.android.mm.kobalt.presentation.viewmodel.withState
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.android.module.specific.presentation.fragment.KobaltBaseTabOptionFragment
import com.immomo.android.module.specific.presentation.itemmodel.EmptyViewItemModel
import com.immomo.android.module.specific.presentation.itemmodel.LoadMoreItemModel
import com.immomo.android.module.specific.presentation.itemmodel.loadMoreState
import com.immomo.android.module.specific.presentation.view.KobaltRecyclerView
import com.immomo.android.router.momo.business.kliao.KliaoMatchRouterImpl
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.storage.preference.SPKeys
import com.immomo.framework.utils.UIUtils
import com.immomo.framework.view.recyclerview.layoutmanager.LinearLayoutManagerWithSmoothScroller
import com.immomo.lcapt.evlog.EVLog
import com.immomo.mmstatistics.event.ClickEvent
import com.immomo.mmstatistics.event.ExposureEvent
import com.immomo.mmutil.task.MomoMainThreadExecutor
import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.mmutil.task.ThreadUtils
import com.immomo.momo.LogTag
import com.immomo.momo.R
import com.immomo.momo.android.view.dialog.MAlertDialog
import com.immomo.momo.android.view.tips.TipManager
import com.immomo.momo.android.view.tips.tip.ITip
import com.immomo.momo.common.AppKit
import com.immomo.momo.globalevent.GlobalEventManager
import com.immomo.momo.maintab.config.AvatarFramePageConfigV2
import com.immomo.momo.maintab.model.ActiveUser
import com.immomo.momo.maintab.session2.IFoldSessionLog
import com.immomo.momo.maintab.session2.SessionUpdateBundle
import com.immomo.momo.maintab.session2.apt.SessionAppConfigV2Getter
import com.immomo.momo.maintab.session2.data.database.SessionContent
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.data.mapper.toModel
import com.immomo.momo.maintab.session2.defs.FoldSessionDefinition
import com.immomo.momo.maintab.session2.defs.GiftSayHiSessionDefinition
import com.immomo.momo.maintab.session2.defs.SayHiContent
import com.immomo.momo.maintab.session2.defs.SayHiSessionDefinition
import com.immomo.momo.maintab.session2.defs.UniverseSessionDefinition
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.maintab.session2.domain.model.type.ActiveUserSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.ChatSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.GotoSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.HePaiSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.MsgFoldSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.NewBoySessionModel
import com.immomo.momo.maintab.session2.domain.model.type.SpamSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.UnReplySessionModel
import com.immomo.momo.maintab.session2.domain.model.type.UserChatSessionModel
import com.immomo.momo.maintab.session2.domain.topoperator.SessionTopOperatorManager
import com.immomo.momo.maintab.session2.event.SessionInnerReceiver
import com.immomo.momo.maintab.session2.presentation.activity.FoldMsgSessionListActivity
import com.immomo.momo.maintab.session2.presentation.itemmodel.ActiveUserExposure
import com.immomo.momo.maintab.session2.presentation.itemmodel.ActiveUserSessionItemModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.FoldSessionItemModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.FullSearchItemModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.HePaiSessionItemModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.HepaiSessionOnLongClickListener
import com.immomo.momo.maintab.session2.presentation.itemmodel.NewBoySessionItemModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.SessionItemModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.SessionNoticeItemModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.SessionTopOperatorItemModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.SpamSessionItemModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.SpamSessionOnLongClickListener
import com.immomo.momo.maintab.session2.presentation.itemmodel.UnReplySessionItemModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.events.DefaultSessionOnAvatarClickListener
import com.immomo.momo.maintab.session2.presentation.itemmodel.events.DefaultSessionOnClickListener
import com.immomo.momo.maintab.session2.presentation.itemmodel.events.DefaultSessionOnLongClickListener
import com.immomo.momo.maintab.session2.presentation.itemmodel.events.OnShowSessionLongClickDialog
import com.immomo.momo.maintab.session2.presentation.itemmodel.events.SessionDraggableViewTouchListener
import com.immomo.momo.maintab.session2.presentation.viewmodel.SessionListInnerViewModel
import com.immomo.momo.maintab.session2.presentation.viewmodel.SessionPaginationState
import com.immomo.momo.maintab.session2.utils.SessionFoldHelper
import com.immomo.momo.maintab.sessionlist.ISessionListPresenter
import com.immomo.momo.maintab.sessionlist.adapter.SessionListAdapter
import com.immomo.momo.maintab.sessionlist.task.RefreshOnlineStatusTask
import com.immomo.momo.maintab.sessionlist.task.RefreshOnlineStatusTask.Companion.KEY_MMID_USER_LOCATION_DATA_CHANGED
import com.immomo.momo.maintab.sessionlist.util.INoticeAnimManager
import com.immomo.momo.maintab.sessionlist.util.NoticeAnimManager
import com.immomo.momo.maintab.sessionlist.util.SessionHelper.ignoreUnreadCount
import com.immomo.momo.maintab.sessionlist.util.SessionUserHelper
import com.immomo.momo.maintab.task.FireSendTask
import com.immomo.momo.message.NewSayUIConfigV1
import com.immomo.momo.message.helper.MsgOfficialHelper
import com.immomo.momo.message.sayhi.NewSayHiStackCache
import com.immomo.momo.message.sayhi.SayHiStackCache.setHasNewHi
import com.immomo.momo.message.sayhi.utils.NewSayHiSessionFlowUtil
import com.immomo.momo.message.task.UnreadUploadRunnable
import com.immomo.momo.mk.util.BusinessNotifySwitchUtils
import com.immomo.momo.mulog.pair.MUPairItem
import com.immomo.momo.protocol.imjson.event.IMEventReporter
import com.immomo.momo.protocol.imjson.event.IMOfflineEvent
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.daobase.dbfix.DBFixHandler.onMainResume
import com.immomo.momo.statistics.EVAction
import com.immomo.momo.statistics.EVPage
import com.immomo.momo.statistics.logrecord.viewhelper.SessionListExposureLogHelper
import com.immomo.momo.universe.im.UniUnreadManager
import com.immomo.momo.util.StringUtils
import kotlinx.coroutines.InternalCoroutinesApi
import kotlinx.coroutines.delay
import org.json.JSONException
import org.json.JSONObject

class SessionListInnerFragment(
    private val mPresenter: ISessionListPresenter,
    private val dragListener: SessionDraggableViewTouchListener? = null,
    private val onVmInit: (SessionListInnerViewModel) -> Unit = {}
) : KobaltBaseTabOptionFragment(), AppLifecycleManager.AppLifecycleListener,
    GlobalEventManager.Subscriber {

    companion object {
        val TASK_HI_SESSION_FLOW = "task_hi_session_flow".hashCode()
        val KEY_IS_SHOWN_AVATAR_FRAME_TIP = "key_is_shown_avatar_frame_tip" // 是否展示了头像框提示
    }

    private val sessionListVm by pageViewModel(SessionListInnerViewModel::class)
    private var sessionInitTime = -1L

    private var logHelper: SessionListExposureLogHelper? = null
    private lateinit var sessionRv: KobaltRecyclerView
    private lateinit var sessionLm: LinearLayoutManagerWithSmoothScroller

    private val mNoticeAnimManager: INoticeAnimManager = NoticeAnimManager()
    private var sessionInnerReceiver: SessionInnerReceiver? = null
    val helper: MsgOfficialHelper = MsgOfficialHelper()
    private var isInitialedSessionTopOperatorItemModel = false // 是否初始化了顶部运营位
    private var isShowAvatarFrameTip: Boolean = false // 是否已经展示过头像框提示

    // 顶部运营位
    private val sessionTopOperatorItemModel by lazy {
        isInitialedSessionTopOperatorItemModel = true
        SessionTopOperatorItemModel(sessionListVm, lifecycleScope)
    }

    override fun getLayout(): Int {
        return R.layout.fragment_session_list_inner
    }

    override fun initViews(contentView: View) {
        sessionInitTime = System.currentTimeMillis()

        sessionRv = contentView.findViewById(R.id.recyclerview)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            sessionRv.isForceDarkAllowed = false
        }
        sessionLm = object : LinearLayoutManagerWithSmoothScroller(context, VERTICAL, false) {
            //FIX: 重写此方法，是为了解决在 OPPO FindX 手机上，切换到Session列表时，自动滚动到 ActiveViewHolder 的问题
            override fun requestChildRectangleOnScreen(
                parent: RecyclerView,
                child: View,
                rect: Rect,
                immediate: Boolean,
                focusedChildVisible: Boolean
            ): Boolean {
                return false
            }

            override fun requestChildRectangleOnScreen(
                parent: RecyclerView,
                child: View,
                rect: Rect,
                immediate: Boolean
            ): Boolean {
                return false
            }
        }
        sessionRv.visibleThreshold = 2
        sessionRv.layoutManager = sessionLm
        //去掉动画效果，频繁刷新时，动画可能会导致显示异常
        sessionRv.itemAnimator = null
        sessionRv.adapter = builder.adapter

        mNoticeAnimManager.bindRecyclerView(sessionRv, builder.adapter)

        if (SessionAppConfigV2Getter.get().enable() > 0 &&
            SessionAppConfigV2Getter.get().reportSessionInit() > 0
        ) {
            builder.subscribe {
                if (sessionInitTime < 0 || it.models.isEmpty()) return@subscribe

                val currentTime = System.currentTimeMillis()
                val delta = currentTime - sessionInitTime

                IMEventReporter.eventOfflineTime(
                    IMOfflineEvent.SESSION_MANAGER,
                    MUPairItem.startTime(sessionInitTime),
                    MUPairItem.endTime(currentTime),
                    MUPairItem.uploadTime(delta),
                    MUPairItem.id("sessionInit")
                )
                sessionInitTime = -1
            }
        }
        AppLifecycleManager.registerListener(this)
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        sessionListVm.init()
        MsgOfficialHelper.init()
        onVmInit(sessionListVm)
        sessionListVm.requestRefresh()
        sessionListVm.selectSubscribe(SessionPaginationState::totalUnread) {
            mPresenter.refreshUnreadMessageCount()
        }
        sessionListVm.selectSubscribe(
            SessionPaginationState::showTip,
            deliveryMode = uniqueOnly()
        ) {
            if (it) {
                mPresenter?.showOrHideBottomTips(true)
                KV.saveUserValue(
                    SPKeys.User.Chat.KEY_LAST_SHOW_MESSAGE_TIP,
                    System.currentTimeMillis()
                )
                MomoMainThreadExecutor.postDelayed(TAG, {
                    mPresenter?.showOrHideBottomTips(false)
                }, 3000L)
            } else {
                mPresenter?.showOrHideBottomTips(false)
            }
        }
        if (AppKit.getAccountManager().isOnline) {
            mPresenter.loadInteractionData()
        }

        val sessionBlankInterval = SessionAppConfigV2Getter.get().sessionBlankInterval() * 1000L
        if (SessionAppConfigV2Getter.get().enable() > 0 &&
            sessionBlankInterval > 0
        ) {
            lifecycleScope.launchWhenResumed {
                delay(sessionBlankInterval)

                try {
                    val models = builder.adapter.getModels().toList()
                    if (models.firstOrNull { it is SessionItemModel<*> } == null) {
                        SessionManager.get().dumpState(
                            "ui=${
                                models.map { model ->
                                    when (model) {
                                        is SessionItemModel<*> -> model.session.sessionKeyStr
                                        else -> model::class.java.name
                                    }
                                }
                            }\nvm=${
                                withState(sessionListVm) {
                                    it.models.map { model -> model.sessionKeyStr }
                                }
                            }"
                        )
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    override fun invalidate() {
    }

    override fun onLoad() {
        initData()
        initVMs()
        initEvents()
        initReceiver()
    }

    private fun initReceiver() {
        sessionInnerReceiver = SessionInnerReceiver(context)
    }

    private fun initData() {
    }

    private fun registerGotoSessionCallback(model: GotoSessionModel) {
        if (model.source != 1) return
        registerBusinessNotifyCallback(model, model.businessId)
    }

    private fun registerBusinessNotifyCallback(model: SessionModel, businessId: String) {
        val refreshSession = { sessionKey: SessionKey ->
            SessionManager.get().updateSession(sessionKey) {
                val content = it.content.castOrNull<SessionContent>()
                content?.forceRefresh() != null
            }
        }

        BusinessNotifySwitchUtils.getIntance()
            .addCallbackListener(sessionListVm.businessNotifySwitchTag,
                businessId,
                BusinessNotifySwitchUtils.CallbackListener { callbackStr ->
                    if (StringUtils.equalsNonNull(businessId, callbackStr)) {
                        refreshSession(model.sessionKey)
                    } else {
                        if (!TextUtils.isEmpty(callbackStr)) {
                            try {
                                val jsonObject = JSONObject(callbackStr).optJSONObject("data")
                                    ?: return@CallbackListener
                                val iterator = jsonObject.keys()
                                while (iterator.hasNext()) {
                                    val sId = iterator.next()
                                    if (businessId == sId) {
                                        refreshSession(model.sessionKey)
                                        return@CallbackListener
                                    }
                                }
                            } catch (e: JSONException) {
                                MDLog.printErrStackTrace(LogTag.Message.SessionList, e)
                            }
                        }
                    }
                })
    }

    @OptIn(InternalCoroutinesApi::class)
    private fun initVMs() {
        sessionListVm.selectSubscribe(
            SessionPaginationState::noticeInfo,
            SessionPaginationState::models,
            SessionPaginationState::reloadTotalUnread
        ) { _, _, _ ->
            sessionListVm.updateUnreadCount()
            builder.triggerBuild()
        }

        sessionListVm.selectSubscribe(SessionPaginationState::models) {
            it.forEach { model ->
                try {
                    when {
                        model is GotoSessionModel -> {
                            registerGotoSessionCallback(model)
                        }

                        model is UserChatSessionModel
                                && model.sessionBusinessType == Session.BUSINESS_KLIAO_MATCH -> {
                            // session更新，回调给红娘业务方
                            KliaoMatchRouterImpl.sessionCallback?.onSessionUpdate(
                                KliaoMatchRouterImpl.cloneWithSession(model)
                            )
                        }

                        model is HePaiSessionModel -> {
                            registerBusinessNotifyCallback(model, HePaiSessionItemModel.businessId)
                        }
                    }
                } catch (e: Exception) {
                }
            }
        }
    }

    private fun validateActiveUser(byResume: Boolean = false) {
        withState(sessionListVm) {
            if (!it.hasActiveUserItem) return@withState

            val range = sessionLm.completelyVisibleItemRange
            (range[0]..range[1]).firstOrNull { pos ->
                builder.adapter.getModel(pos) is ActiveUserSessionItemModel
            }?.let { pos ->
                val holder = sessionRv.findViewHolderForAdapterPosition(pos)
                        as? ActiveUserSessionItemModel.ViewHolder ?: return@let
                builder.adapter.getModel(pos).castOrNull<ActiveUserSessionItemModel>()
                    ?.validateExposeActiveUser(holder)
            }
            if (byResume) {
                startCheckAvatarTip()
            }
        }
    }

    private fun cancelAvatarFrameTipCheck() {
        MomoMainThreadExecutor.cancelAllRunnables(KEY_IS_SHOWN_AVATAR_FRAME_TIP)
    }

    private fun startCheckAvatarTip() {
        if (AvatarFramePageConfigV2.tipShowConfig && !isShowAvatarFrameTip) { // 尝试展示tip
            isShowAvatarFrameTip = KV.getUserBool(KEY_IS_SHOWN_AVATAR_FRAME_TIP, false)
            if (!isShowAvatarFrameTip) {
                cancelAvatarFrameTipCheck()
                MomoMainThreadExecutor.postDelayed(KEY_IS_SHOWN_AVATAR_FRAME_TIP, {
                    kotlin.runCatching {
                        showAvatarTip()
                    }
                }, 800)
            }
        }
    }

    private fun showAvatarTip() {
        val range = sessionLm.completelyVisibleItemRange
        (range[0]..range[1]).forEach { pos ->
            builder.adapter.getModel(pos)?.also {
                if (it is SessionItemModel<*> && it.session is UserChatSessionModel) {
                    it.session.avatarFrame?.getAvatarFrame()?.takeIf { it.isNotBlank() }?.also {
                        val holder = sessionRv.findViewHolderForAdapterPosition(pos)
                        if (holder is SessionItemModel.SessionViewHolder) {
                            val sideType = if (pos < 4) ITip.Triangle.TOP else ITip.Triangle.BOTTOM
                            showSessionAvatarFrameTip(holder.imgAvatarFrame, sideType)
                            return
                        }
                    }
                }
            }
        }
    }

    private fun showSessionAvatarFrameTip(ivTip: ImageView?, sideType: Int) {
        ivTip ?: return
        val context = activity ?: return
        val pixels = UIUtils.getPixels(10f)
        val tipManager = TipManager.bindActivity(context)
        tipManager.checkViewCanShowTip(ivTip) {
            tipManager.setTriangles(null, null, null, null)
                .setTouchToHideAll(true)
                .setBackground(UIUtils.getDrawable(R.drawable.bg_corner_10dp_4e7fff))
                .setTextColor(UIUtils.getColor(R.color.white))
                .setTextPadding(pixels, pixels, pixels, pixels)
                .showTipView(ivTip, AvatarFramePageConfigV2.tipTextConfig, 0, 0, sideType)
                ?.autoHide(AvatarFramePageConfigV2.tipDurationConfig)
            isShowAvatarFrameTip = true
            KV.saveUserValue(KEY_IS_SHOWN_AVATAR_FRAME_TIP, true)
        }
    }

    //将当前Session的User和Group加到待刷新列表
    fun getVisibleChatSessionModel(): List<SessionModel> {
        if (!isCreated) return emptyList()
        val models = arrayListOf<SessionModel>()
        return sessionLm.visibleItemRange.let { it[0]..it[1] }
            .mapNotNull {
                builder.adapter.getModel(it)
                    .castOrNull<AsyncCementModel<*, *>>()?.state?.castOrNull<SessionModel>()
            }.filter {
                it is ChatSessionModel || it is NewBoySessionModel || it is HePaiSessionModel
            } + models
    }

    private fun initEvents() {
        logHelper = SessionListExposureLogHelper().also {
            it.setupWithRv(sessionRv, builder.adapter)
        }
        sessionRv.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            var lastScrollTime = 0.0
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == RecyclerView.SCROLL_STATE_IDLE) { //滚动停止，去加载
                    validateActiveUser()
                }
            }

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (lastScrollTime - System.currentTimeMillis() > 120) {
                    validateActiveUser()
                }
                lastScrollTime = System.currentTimeMillis().toDouble()
            }
        })
        GlobalEventManager.getInstance().register(this, GlobalEventManager.EVN_NATIVE)
    }

    public override fun onFragmentResume() {
        super.onFragmentResume()
        onMainResume(this)

        getAdapter()?.resetExposureData()
        logHelper?.exposureItems(sessionRv)

        withState(sessionListVm) {
            if (it.models.isEmpty()) {
                sessionListVm.requestRefresh()
            }
        }
        mNoticeAnimManager.onResume()

        //活跃用户曝光
        validateActiveUser(true)
        onFragmentLifecycle(true)
    }

    fun checkUnread() {
        sessionListVm?.checkUnread()
    }

    public override fun onFragmentPause() {
        super.onFragmentPause()
        mNoticeAnimManager.onPause()
        onFragmentLifecycle(false)
        cancelAvatarFrameTipCheck()
    }

    override fun onDestroy() {
        super.onDestroy()
        GlobalEventManager.getInstance().unregister(GlobalEventManager.EVN_NATIVE)
        cancelAvatarFrameTipCheck()
        if (isInitialedSessionTopOperatorItemModel) { // 已经创建了顶部运营位
            sessionTopOperatorItemModel.release()
        }
        sessionInnerReceiver?.unregister()
        sessionRv.adapter = null
        MomoMainThreadExecutor.cancelAllRunnables(TASK_HI_SESSION_FLOW)
        MomoTaskExecutor.cancleAllTasksByTag(hashCode())
        AppLifecycleManager.unregisterListener(this)
    }

    fun getAdapter(): SessionListAdapter? = builder.adapter.castOrNull()

    fun addOnScrollListener(listener: RecyclerView.OnScrollListener) {
        lifecycleScope.launchWhenResumed {
            sessionRv.addOnScrollListener(listener)
        }
    }

    fun scrollToPosition(position: Int) {
        if (!isCreated) return
        sessionRv.scrollToPosition(position)
    }

    fun smoothScrollToPosition(position: Int) {
        if (!isCreated) return
        sessionRv.smoothScrollToPosition(position)
    }

    private fun findNextUnreadIndex(startVisible: Int): Int {
        return builder.adapter.getModels().asSequence()
            .drop(startVisible)
            .indexOfFirst {
                val count =
                    it.castOrNull<AsyncCementModel<*, *>>()?.state.castOrNull<SessionModel>()?.baseInfo?.unreadMessageCount
                count != null && count > 0
            }.let {
                if (it < 0) -1 else it + startVisible
            }
    }

    /**
     * 记录fragment声明周期变化回调
     */
    private fun onFragmentLifecycle(isResume: Boolean) {
        withState(sessionListVm) {
            kotlin.runCatching {
                if (!SessionTopOperatorManager.isABTestOpenSwitch) return@withState
                val range = sessionLm.completelyVisibleItemRange
                (range[0]..range[1]).firstOrNull { pos ->
                    builder.adapter.getModel(pos) is SessionTopOperatorItemModel
                }?.let { pos ->
                    builder.adapter.getModel(pos).castOrNull<SessionTopOperatorItemModel>()?.also {
                        it.onFragmentVisible(isResume)
                    }
                } ?: checkItemModels(isResume)
            }
        }
    }

    private fun checkItemModels(isResume: Boolean) {
        if (builder.adapter.getModels().isNotEmpty()) {
            (builder.adapter.getModel(0) as? SessionTopOperatorItemModel?)?.also {
                it.onFragmentVisible(isResume)
            }
        }
    }

    /**
     * 自动切换到下一个未读item
     */
    fun switchToNextUnread() {
        if (!isCreated) return

        val firstVisiblePosInAdapter: Int = sessionLm.findFirstVisibleItemPosition()
        val position = findNextUnreadIndex(firstVisiblePosInAdapter + 1)
        MDLog.d(LogTag.COMMON, "switchToNextUnread position:%d", position)
        if (position >= 0 && position < builder.adapter.itemCount) {
            sessionRv.smoothScrollToPosition(position)
            return
        }
        //无法滚到底部，则回到顶部
        sessionRv.smoothScrollToPosition(0)
    }

    private fun showHideActiveUserDialog() {
        val dialog =
            MAlertDialog.makeConfirm(activity, "有新推荐时，模块可能会再次出现", "取消", "确认隐藏",
                { dialog, which -> closeDialog() }
            ) { dialog, which -> mPresenter.hideActiveUser() }
        dialog.setTitle("隐藏最近在线")
        dialog.setSupportDark(true)
        showDialog(dialog)
    }


    private fun removeSelectActiveItem(user: ActiveUser) {
        if (user.type == ActiveUser.TYPE_NOT_DISMISS) return

        val remoteIdKey = "re_momo_id"
        val tDialog = MAlertDialog(
            context
        )
        tDialog.setTitle("移除此人")
        tDialog.setMessage("移除后，不再显示此人")
        tDialog.setButton(MAlertDialog.INDEX_LEFT, R.string.dialog_btn_cancel) { dialog, which ->
            ClickEvent.create()
                .page(EVPage.Msg.Chatlist)
                .action(EVAction.Window.Unremove)
                .putExtra(remoteIdKey, user.momoid)
                .submit()
        }
        tDialog.setButton(
            MAlertDialog.INDEX_RIGHT, "确认移除"
        ) { dialog: DialogInterface?, which: Int ->
            mPresenter.removeActiveUser(user)
            ClickEvent.create()
                .page(EVPage.Msg.Chatlist)
                .action(EVAction.Window.Remove)
                .putExtra(remoteIdKey, user.momoid)
                .submit()
        }
        tDialog.setSupportDark(true)
        showDialog(tDialog)
        ExposureEvent.create(ExposureEvent.Type.Normal)
            .page(EVPage.Msg.Chatlist)
            .action(EVAction.Window.Remove)
            .putExtra(remoteIdKey, user.momoid)
            .submit()
    }

    fun ignoreSessionUnread(posInAdapter: Int) {
        val session = builder.adapter.getModel(posInAdapter)
            ?.castOrNull<AsyncCementModel<*, *>>()?.state?.castOrNull<SessionModel>()
        val isSayHiNewSession =
            session != null && NewSayUIConfigV1.isUserNewUI() && SayHiSessionDefinition.KEY_SAYHI.value == session.sessionKey.value
        if (session != null && (session.baseInfo.unreadMessageCount > 0 || isSayHiNewSession)) {
            ignoreUnreadCount(session)

            SessionManager.get().syncSession(SessionUpdateBundle.ClearUnread(session.sessionKey))

            mPresenter.refreshUnreadMessageCount()
            ThreadUtils.execute(ThreadUtils.TYPE_RIGHT_NOW, UnreadUploadRunnable())
            //处理收招呼cell是否置顶
            SessionManager.get().updateSession(session.sessionKey) { entity: SessionEntity ->
                val content = entity.content as? SayHiContent ?: return@updateSession false
                setHasNewHi(content.hiUserTotalCount > 0)
                if (isSayHiNewSession) {
                    SessionManager.get()
                        .syncSession(SessionUpdateBundle.ReloadInfo(session.sessionKey))
                }
                false
            }
            NewSayHiStackCache.clearCache()
        }
    }

    fun clearAllUnread() {
        SessionManager.get().findAllSessions(filter = {
            it.markedAsDeleted.not() && it.foldType.safe() == 0 && (it.unreadCount > 0 || it.silenceCount > 0)
        }) { it }
            .forEach {
                val sessionKey = SessionKey.fromString(it.sessionKey)

                SessionManager.get().syncSession(SessionUpdateBundle.ClearUnread(sessionKey))
                if (sessionKey == SayHiSessionDefinition.KEY_SAYHI) {
                    SessionManager.get().updateSession(sessionKey, false) { entity: SessionEntity ->
                        val content =
                            entity.content as? SayHiContent ?: return@updateSession false
                        setHasNewHi(content.hiUserTotalCount > 0)
                        false
                    }
                } else if (sessionKey == GiftSayHiSessionDefinition.KEY_SAYHI) {
                    SessionManager.get().updateSession(sessionKey) { entity: SessionEntity ->
                        false
                    }
                } else if (sessionKey == FoldSessionDefinition.KEY_NEW_BOY || sessionKey == FoldSessionDefinition.KEY_HEPAI) {
                    ignoreUnreadCount(
                        SessionManager.get().getSession(it.sessionKey, false).first?.toModel()
                    )
                } else if (sessionKey == UniverseSessionDefinition.KEY_UNIVERSE) { // 清理小宇宙未读数
                    UniUnreadManager.clearAllUnread()
                }
            }
        NewSayHiStackCache.clearCache()
    }

    fun getSessionModel(sessionKey: String?): SessionModel? {
        if (!isCreated) return null

        return withState(sessionListVm) {
            it.models.firstOrNull { model -> model.baseInfo.sessionKey == sessionKey }
        }
    }


    private val activeUserExposure = ActiveUserExposure()

    fun getActiveExposeList() = activeUserExposure.getActiveExposeList(null)

    //<editor-fold desc="UI Builder">
    private val builder by lazy {
        cementBuilder(sessionListVm, adapterFactory = { SessionListAdapter(sessionRv, it) }) {
            if (SessionTopOperatorManager.isABTestOpenSwitch) {
                sessionTopOperatorItemModel.addToBuilder()
            } else {
                FullSearchItemModel().addToBuilder()
            }
            //configV1控制
            SessionNoticeItemModel(it.noticeInfo, dragListener) {
                mPresenter.gotoInteractionAc(it.noticeInfo.gotoTabType)
            }.addToBuilder()
            var empty = true

            it.models.forEach { model ->
                empty = false
                when (model) {
                    is MsgFoldSessionModel -> FoldSessionItemModel(model, {
                        EVLog.create(IFoldSessionLog::class.java)
                            .logMsgFoldHelpClick(it.adapterPosition - 1)
                        context?.also { showDialog(SessionFoldHelper.helpDialog(it)) }
                    }) {
                        SessionManager.get().syncSession(
                            SessionUpdateBundle.ClearUnread(model.sessionKey)
                        )
                        context?.also { FoldMsgSessionListActivity.start(it) }
                    }

                    is UnReplySessionModel -> UnReplySessionItemModel(model)
                    is NewBoySessionModel -> NewBoySessionItemModel(model, dragListener)
                    is HePaiSessionModel -> HePaiSessionItemModel(model,
                        dragListener,
                        object : HepaiSessionOnLongClickListener {
                            override fun onLongClicked(
                                view: View,
                                viewHolder: HePaiSessionItemModel.ViewHolder,
                                session: SessionModel,
                                adapterPosition: Int
                            ) {
                                mPresenter.showHepaiMsgSwitchDialog(
                                    model,
                                    HePaiSessionItemModel.businessId
                                )
                            }

                        })

                    is SpamSessionModel -> SpamSessionItemModel(model,
                        dragListener,
                        object : SpamSessionOnLongClickListener {
                            override fun onLongClicked(
                                view: View,
                                viewHolder: SpamSessionItemModel.ViewHolder,
                                session: SessionModel,
                                adapterPosition: Int
                            ) {
                                mPresenter.showSpamMsgSwitchDialog(
                                    model,
                                    HePaiSessionItemModel.businessId
                                )
                            }

                        })

                    is ActiveUserSessionModel -> ActiveUserSessionItemModel(
                        model,
                        false,
                        activeUserExposure,
                        sessionRv,
                        onActiveHideClick = {
                            showHideActiveUserDialog()
                        }, onActiveUserClick = { activeUser, isLongClick ->
                            if (isLongClick) {
                                if (activeUser.type != ActiveUser.TYPE_NOT_DISMISS) {
                                    removeSelectActiveItem(activeUser)
                                }
                            } else {
                                mPresenter.doActiveUserClicked(activeUser)
                            }
                        })

                    else -> {
                        SessionItemModel(
                            model,
                            object : DefaultSessionOnClickListener() {
                                override fun onClicked(
                                    view: View,
                                    viewHolder: SessionItemModel.SessionViewHolder,
                                    session: SessionModel,
                                    adapterPosition: Int
                                ) {
                                    super.onClicked(view, viewHolder, session, adapterPosition)
                                    getAdapter()?.removeAnimateItem(adapterPosition)
                                }
                            },
                            DefaultSessionOnLongClickListener(object :
                                OnShowSessionLongClickDialog {
                                override fun showDialog(
                                    viewHolder: SessionItemModel.SessionViewHolder,
                                    actionList: Array<String>,
                                    dontNeedSubtitle: Boolean,
                                    positionInAdapter: Int,
                                    typeGotoSessionId: String?
                                ) {
                                    mPresenter.showAlertDialog(
                                        viewHolder,
                                        positionInAdapter,
                                        actionList,
                                        dontNeedSubtitle,
                                        model,
                                        typeGotoSessionId
                                    )
                                }
                            }),
                            DefaultSessionOnAvatarClickListener(),
                            dragListener,
                            onFireClick = { chatId, sign ->
                                MomoTaskExecutor.executeTask(
                                    ThreadUtils.TYPE_RIGHT_NOW,
                                    <EMAIL>(),
                                    FireSendTask(chatId, sign, "cellBtn")
                                )
                                SessionUserHelper.clearSessionFireIcon(chatId)
                            }
                        )
                    }
                }.addToBuilder()
            }
            when {
                empty -> {
                    EmptyViewItemModel("暂无消息").also { empty ->
                        empty.setHeight(UIUtils.getPixels(530F))
                    }.addToBuilder()
                }

                it.hasMore -> {
                    LoadMoreItemModel(it.loadingRequest.loadMoreState) {
                        sessionListVm.loadMoreSessions(false)
                    }.addToBuilder()
                }
            }
        }
    }

    override fun onAppBackground() {

    }

    override fun onAppForeground() {
        if (NewSayUIConfigV1.isUserNewUI()) {
            MomoMainThreadExecutor.cancelAllRunnables(TASK_HI_SESSION_FLOW)
            MomoMainThreadExecutor.postDelayed(TASK_HI_SESSION_FLOW, {
                if (isResumed) {
                    NewSayHiSessionFlowUtil.checkSayhiForceFlow()
                }
            }, 100)
        }
    }

    override fun onGlobalEventReceived(event: GlobalEventManager.Event?) {
        val msgData = event?.msg ?: return
        when (event.name) {
            RefreshOnlineStatusTask.ON_USER_LOCATION_DATA_CHANGED -> {
                (msgData[KEY_MMID_USER_LOCATION_DATA_CHANGED] as? List<String>)?.takeIf { it.isNotEmpty() }
                    ?.also {
                        if (isResumed) {
                            startCheckAvatarTip()
                        }
                    }
            }
        }
    }
}
//</editor-fold>