package com.immomo.momo.maintab.session2.domain.model.type

import com.immomo.momo.maintab.session2.domain.model.SessionModel

interface ChatSessionModel : SessionModel {
    @Deprecated("有的时候会未被赋值")
    val chatId: String
    val desc: String

    val draftString: String
    val draftQuoteString: String

    val lastMessageType: Int
    val showMessageStatus: Boolean
    val lastMessageStatus: Int

    fun hasSpecialNotice(): Boolean

    fun hasDraft(): Boolean {
        return draftString.isNotEmpty() || draftQuoteString.isNotEmpty()
    }
}

