package com.immomo.momo.maintab.model;

import com.immomo.framework.location.UserLocationUtils;
import com.immomo.framework.storage.kv.KV;
import com.immomo.momo.MomoKit;
import com.immomo.momo.appconfig.model.AppConfigV2;
import com.immomo.momo.message.sayhi.SayHiArgs;
import com.immomo.momo.service.bean.CommonRequestParams;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.util.StringUtils;

import java.util.Map;

/**
 * Created by huang.liangjie on 2017/11/7.
 * <p>
 * Momo Tech 2011-2017 © All Rights Reserved.
 * <p>
 * Remain.        0.    1.     2
 * lastRemain     1     1.     2.            本批次是用第几种策略请求的（七天内/外），默认为1（七天内）
 */
public class SayHiListReqParam extends CommonRequestParams<SayHiListReqParam> {
    public static final int FROM_PAGE_NORMAL = 0; // 普通招呼页面
    public static final int FROM_PAGE_LIVE = 1; // 现场招呼页面
    public static final int FROM_PAGE_GIFT = 2; // 礼物招呼页面
    public static final int FROM_PAGE_HARASS = 3; // 骚扰招呼页面
    public static final int FROM_PAGE_ALL_LIST = 4; // 所有招呼页面（根据开关剔除骚扰招呼）
    public static final int FROM_SESSION_GIFT_HI = 5; // 消息列表礼物

    public int fromPage;
    public String remoteid; // 推荐到卡片第一位的momoid
    public long firstSayHiSessionTime; // 本地拥有招呼的最早的时间，传给API用
    public boolean hasMore = true;
    public boolean useCache;
    public boolean isFirst = true;
    public boolean isNewSayHi = false;   // 是新的打招呼的样式
    public long lastIntoSayHiPage = 0; // 上次进入页面的时间戳
    public String localReqUserIds;  // 请求的本地用户数据
    public boolean isLoadingMore = false; // 是否正在加载更多

    public SayHiListReqParam() {
        index = 0;
        count = SayHiArgs.PAGE_SIZE;
    }

    @Override
    public Map<String, String> toMap() {
        Map<String, String> map = super.toMap();
        User curentUser = MomoKit.getCurrentUser();
        map.put("lat", String.valueOf(curentUser != null ? UserLocationUtils.getLatitude() : ""));
        map.put("lnt", String.valueOf(curentUser != null ? UserLocationUtils.getLongitude() : ""));
        if (!StringUtils.isEmpty(remoteid)) {
            map.put("remoteid", remoteid);
        }
        map.put("last_time", String.valueOf(firstSayHiSessionTime / 1000));
        if (StringUtils.isNotBlank(localReqUserIds)) {  // 用于请求用户资料信息
            map.put("remoteIds", localReqUserIds);
        }
        return map;
    }

    public void toInitPageByApi() {
        this.index = 0;
        this.count = KV.getUserInt(AppConfigV2.SPKeys.KEY_HI_PAGE_COUNT, SayHiArgs.PAGE_SIZE);
        this._type = REQUEST_API;
        this.fromPage = FROM_PAGE_NORMAL;
        this.useCache = false;
        this.hasMore = true;
    }

    public void toInitLocal() {
        this.index = 0;
        this.count = SayHiArgs.PAGE_SIZE;
        this._type = REQUEST_LOCAL;
        this.fromPage = FROM_PAGE_NORMAL;
        this.useCache = false;
        this.hasMore = true;
        this.isFirst = true;
        this.isLoadingMore = false;
    }
}
