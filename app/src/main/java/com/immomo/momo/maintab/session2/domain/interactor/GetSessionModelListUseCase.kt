package com.immomo.momo.maintab.session2.domain.interactor

import com.immomo.android.mm.kobalt.domain.fx.Option
import com.immomo.android.mm.kobalt.domain.interactor.UseCase
import com.immomo.momo.maintab.session2.SessionUpdateBundle.ReloadInfo
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.data.manager.SessionMetadata
import com.immomo.momo.maintab.session2.defs.PaasSessionDefinition
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.maintab.session2.domain.repository.ISessionListRepository
import com.immomo.momo.service.sessions.SessionUserCache
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

/**
 * CREATED BY liu.chong
 * AT 2024/7/4
 */
interface SessionFilter {
    fun filterMetadata(metadata: SessionMetadata): Boolean
    fun filterSessionModel(sessionModel: SessionModel): Boolean
}

open class GetSessionModelListUseCase(
    dispatcher: CoroutineDispatcher,
    private val iSessionRepository: ISessionListRepository,
    private val sessionFilter: SessionFilter
) : UseCase<GetSessionModelListUseCase.SessionResult, GetSessionModelListUseCase.Param>(
    dispatcher
) {

    data class Param(
        val size: Int
    )

    data class SessionResult(
        /**
         * 是否有更多
         */
        val hasMore: Boolean,
        /**
         * Session列表数据
         */
        val sessionList: List<SessionModel>
    )

    override fun build(param: Option<Param>): Flow<SessionResult> {
        param.fold({
            throw IllegalArgumentException("获取Session 参数异常")
        }) { p ->
            return flow {
                iSessionRepository.findAllSessions(p.size + 1) {
                    sessionFilter.filterMetadata(it)
                }.let { r ->
                    if (r.size == p.size + 1) {
                        SessionResult(true, r.take(p.size))
                    } else {
                        SessionResult(false, r)
                    }
                }.also {
                    emit(it)
                }
            }
        }
    }

}