package com.immomo.momo.maintab.session2

import com.immomo.momo.maintab.session2.data.repository.SessionListRepository
import com.immomo.momo.maintab.session2.data.service.SessionChangeService
import com.immomo.momo.maintab.session2.domain.interactor.FetchUserInfoUseCase
import com.immomo.momo.maintab.session2.domain.interactor.GetSessionModelListUseCase
import com.immomo.momo.maintab.session2.domain.interactor.ObserveSessionChangeUseCase
import com.immomo.momo.maintab.session2.domain.interactor.SessionFilter
import com.immomo.momo.maintab.session2.domain.interactor.SessionLocalTopOperatorUseCase
import com.immomo.momo.maintab.session2.domain.interactor.SessionTopOperatorUseCase
import com.immomo.momo.maintab.session2.domain.interactor.SessionUnreadCountUseCase
import com.immomo.momo.maintab.session2.domain.repository.ISessionListRepository
import com.immomo.momo.maintab.session2.domain.service.ISessionChangeService
import com.immomo.momo.maintab.session2.presentation.activity.FoldMsgSessionListActivity
import com.immomo.momo.maintab.session2.presentation.fragment.SessionListInnerFragment
import com.immomo.momo.maintab.session2.presentation.viewmodel.SessionListInnerViewModel
import com.immomo.momo.maintab.session2.presentation.viewmodel.SessionListState
import com.immomo.momo.maintab.session2.presentation.viewmodel.SessionListViewModel
import com.immomo.momo.maintab.session2.presentation.viewmodel.SessionPaginationState
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.dsl.ScopeDSL
import org.koin.dsl.module

val sessionModule = module {
    scope<SessionListInnerFragment> {
        scoped<ISessionListRepository> { SessionListRepository() }
        scoped<ISessionChangeService> { SessionChangeService() }


        factory { ObserveSessionChangeUseCase(get(), get()) }

        factory { SessionPaginationState() }
        factory { SessionTopOperatorUseCase(get(), get()) }
        factory { SessionUnreadCountUseCase(get(), get()) }
        factory { SessionLocalTopOperatorUseCase(get(), get()) }
        viewModel { SessionListInnerViewModel(get(), get(), get(), get(),get()) }
    }
    scope<FoldMsgSessionListActivity> {
        scoped<SessionFilter> { FoldMsgSessionListActivity.MsgFoldFilter() }
        sessionList()
    }
}

fun ScopeDSL.sessionList() {
    scoped<ISessionListRepository> { SessionListRepository() }

    factory { SessionListState() }
    factory { GetSessionModelListUseCase(get(), get(), get()) }
    factory { FetchUserInfoUseCase(get(), get()) }
    viewModel { SessionListViewModel(get(), get(), get(), get()) }
}
