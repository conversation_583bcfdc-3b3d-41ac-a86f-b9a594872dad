package com.immomo.momo.maintab.session2

import android.util.Log
import com.cosmos.mdlog.MDLog
import com.immomo.momo.maintab.session2.data.database.SessionContent
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import kotlinx.coroutines.runBlocking

object SessionDefinitionManager {
    private val def2List = mutableListOf<SessionDefinition<SessionContent, SessionModel>>()
    private val def2Map = mutableMapOf<String, SessionDefinition<SessionContent, SessionModel>>()

    init {
        sessionDefs.forEach { def ->
            def as SessionDefinition<SessionContent, SessionModel>
            require(def.type !in def2Map) {
                "Duplicate registration on (type=${def.type}). \n" +
                        "Old: ${def2Map[def.type]} \nNew: $def"
            }
            def2List.add(def)
            def2Map[def.type] = def
        }
    }


    fun serialize(entity: SessionEntity): String? {
        return (entity.contentObj as? SessionContent)?.let {
            def2Map[entity.sessionType]?.contentParser?.serialize(it)
        }
    }

    fun deserialize(sessionType: String, src: String?): SessionContent? = runBlocking {
        def2Map[sessionType]?.contentParser?.deserialize(src)
    }

    fun findSessionKey(data: Any?, block: (SessionKey, Any) -> Unit) {
        if (data == null) return
        for (it in def2List) {
            val key = it.determineKey(data)
            if (key != null) {
                block(key, data)
                break
            }
        }
    }

    fun syncSession(session: SessionEntity, data: Any?): Boolean {
        return def2Map[session.sessionType]?.syncSession(session, data) == true
    }

    fun validateSession(session: SessionEntity, canRemoveUnreliable: Boolean) =
        def2Map[session.sessionType]?.validateSession(session, canRemoveUnreliable)

    fun createSession(sessionKey: SessionKey): SessionEntity =
        def2Map[sessionKey.type]?.createSession(sessionKey.id)
            ?: throw IllegalArgumentException("can not create session=$sessionKey")

    fun transformSessionKey(oldSessionId: String, oldSessionType: Int): SessionKey? {
        for (it in def2List) {
            val id = it.isTransformCompatible(oldSessionType, oldSessionId)
            if (id != null) {
                return SessionKey(it.type, id)
            }
        }
        Log.d("FADJALDJLASKD", "$oldSessionId, $oldSessionType not found")
        return null
    }

    fun fetchOldSession(sessionKey: SessionKey): SessionEntity? {
        return def2Map[sessionKey.type]?.transformOldSession(sessionKey.id)
    }

    fun removeOldSession(sessionKey: SessionKey) {
        def2Map[sessionKey.type]?.removeOldSession(sessionKey.id)
    }

    fun saveOldSession(session: SessionEntity) {
        def2Map[session.sessionType]?.saveOldSession(session)
    }


    fun entityToModel(session: SessionEntity): SessionModel? {
        return def2Map[session.sessionType]?.entityToModel(session)
    }
}