package com.immomo.momo.maintab.session2.defs

import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.android.module.specific.data.mapper.optionMap
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.utils.TimeUtils
import com.immomo.momo.maintab.session2.SessionContentParser
import com.immomo.momo.maintab.session2.data.database.ChatContent
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.data.manager.UnreadCountMessageInterceptor
import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.type.UserChatSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.UserChatTagModel
import com.immomo.momo.maintab.session2.domain.model.type.UserOnlineTagModel
import com.immomo.momo.maintab.sessionlist.SessionStickyHelper
import com.immomo.momo.message.helper.SessionTextHelper
import com.immomo.momo.messages.service.TextMatchMsgServiceV2
import com.immomo.momo.service.bean.*
import com.immomo.momo.service.sessions.LastMsgCache
import com.immomo.momo.service.sessions.SessionService
import com.squareup.moshi.JsonClass
import java.util.*

/**
 *
 * author: hongming.wei
 * data: 2023/8/20
 */

@JsonClass(generateAdapter = true)
class TextChatContent : ChatContent(TextChatSessionDefinition.Type) {
    var userAvatar: String? = null
    var userName: String? = null
    var userIsVip: Boolean = false
    var officialOperation: Int = 0

    override fun isChatInfoValid(): Boolean {
        return userName?.isNotEmpty() == true
    }

    var userOnlineTag: UserOnlineTag? = null
    var userLocationTimestamp: Long? = null

    var isLastMessageReceive: Boolean = false

    /**
     * 用户标签，通用，需要跟资料保持同步
     */
    @Transient
    var userChatTag: ChatTag? = null

    /**
     * 有没有红包
     */
    var isHongbao = false
    var isGift = false
    var isMissedFriendCall = false
    var missedFriendCallDesc: String? = null
    var isDianDianCard = false
    var isQuestionMatch = false

    var isType28 = false
    var lastType28Prompt: String? = null
    var type28AppId: String? = null

    /**
     * [xxxx]特殊前缀,如果最后一条消息是任务礼物，则设置 specialText
     */
    var specialText = ""

    /**
     * 类似  点点匹配 这种红色的前缀
     */
    var pushPrefix: String? = null

    var sessionBusinessType = Session.BUSINESS_NONE

    var onlineMsgTime: String? = null

    var sevenDaysIn: String? = null

    var sevenDaysOut: String? = null

    var distance: Int = -3
}


class TextChatSessionDefinition :
    ChatSessionDefinition<TextChatContent, UserChatSessionModel>(
        Type, SessionContentParser.moshiParser()
    ) {

    override fun createContent(): TextChatContent {
        return TextChatContent()
    }

    override fun TextChatContent.contentToModel(baseInfo: BaseSessionInfo): UserChatSessionModel = UserChatSessionModel(
        baseInfo = baseInfo,
        chatId = chatId.safe(),
        desc = desc.safe(),
        draftString = draftString.safe(),
        draftQuoteString = draftQuoteString.safe(),
        lastMessageType = lastMessageType.safe(0),
        showMessageStatus = showMessageStatus,
        lastMessageStatus = lastMessageStatus ?: 0,
        userAvatar = userAvatar.safe(),
        userName = userName.safe(),
        userIsVip = userIsVip,
        userOnlineTag = userOnlineTag.optionMap {
            UserOnlineTagModel(
                it.getName().safe(),
                it.getTagColor().safe(),
                it.getAction().safe(),
                it.canShowAnim(),
                it.getRoomPattern().safe(),
                false
            )
        },
        userLocationTimestamp = userLocationTimestamp.safe(0L),
        userChatTag = userChatTag.optionMap { UserChatTagModel(it.text.safe()) },

        isHongbao = isHongbao,
        isGift = isGift,
        isMissedFriendCall = isMissedFriendCall,
        missedFriendCallDesc = missedFriendCallDesc.safe(),
        isDianDianCard = isDianDianCard,
        isQuestionMatch = isQuestionMatch,
        isType28 = isType28,
        lastType28Prompt = lastType28Prompt.safe(),
        type28AppId = type28AppId.safe(),
        specialText = specialText,
        pushPrefix = pushPrefix.safe(),
        sessionBusinessType = sessionBusinessType.safe(0),
        onlineMsgTime = when {
            (baseInfo.lastMsgId.isNotEmpty() && distance != -2 && TimeUtils.isBeforeYesterday(baseInfo.lastMessageTime)) ->{
                onlineMsgTime.safe()
            }
            (baseInfo.lastMsgId.isNotEmpty() && distance == -2 && TimeUtils.isBeforeFirstWeek(baseInfo.lastMessageTime) == 0)-> {
                sevenDaysIn.safe()
            }
            (baseInfo.lastMsgId.isNotEmpty() && distance == -2 && TimeUtils.isBeforeFirstWeek(baseInfo.lastMessageTime) == 1)-> {
                sevenDaysOut.safe()
            }
            else -> {
                ""
            }
        },
        officialOperation = officialOperation
    )

    override fun getLastMessage(session: SessionEntity): Message? {
        return TextMatchMsgServiceV2.service.findLastMessage(session.sessionId)
    }

    override fun onReloadChatInfo(id: String, session: SessionEntity, forceReload: Boolean) {
        //用户头像，昵称，相关数据
        val content = session.content as? TextChatContent ?: return
        content.pendingReloadChatInfo = true

        SessionManager.getInfoCache().fetchUser(session, content.chatId, forceReload) { user ->
            this.content.castOrNull<TextChatContent>()?.also {
                it.pendingReloadChatInfo = false

                it.userAvatar = user.loadImageId
                it.userName = user.displayName ?: session.sessionId
                it.userIsVip = user.isMomoVip
                it.officialOperation = user.officialOperation

                //强制刷新的数据不对，跳过
                if (!forceReload) {
                    it.userOnlineTag = user.onlineTag
                    it.userLocationTimestamp = user.getLocationTimestamp()?.time

                    it.userChatTag = user.chatTag
                    //根据chatTag刷新onlyShowMessageContent
                    it.onlyShowMessageContent =
                        content.userChatTag?.text.isNullOrBlank().not()
                }
            } != null
        }
    }

    override fun saveOldSession(session: SessionEntity) {
        super.saveOldSession(session)
        val content = session.content.castOrNull<TextChatContent>() ?: return
        SessionService.getInstance().updateSession("tm_${session.sessionId}") {
            it.type = Session.TYPE_TEXT_CHAT
            it.chatId = session.sessionId

            it.lastmsgId = session.lastMsgId
            it.setOrderid(session.orderId)
            it.fetchtime = Date(session.lastFetchTime)

            it.draftString = content.draftString
            it.draftQuoteString = content.draftQuoteString

            it.foldType = session.foldType ?: FolderType.Default
            it.sessionBusinessType = content.sessionBusinessType
            it.recommendTime = session.recommendTime
            true
        }
    }

    override fun syncSession(session: SessionEntity, data: Any?): Boolean {
        val content = session.content as? TextChatContent ?: return false
        content.chatId = session.sessionId

        session.isSticky =
            SessionStickyHelper.getInstance().getOrderID("tm_${session.sessionId}") != -1L

        content.userName = content.chatId
        onReloadChatInfo(session.sessionId, session, false)

        return super.syncSession(session, data)
    }

    override fun updateSessionDescWithLastMessage(
        session: SessionEntity,
        content: TextChatContent,
        lastMessage: Message,
        updateProcessedTime: Boolean
    ) {
        super.updateSessionDescWithLastMessage(session, content, lastMessage, updateProcessedTime)

        if (lastMessage.isUpdateSession && !lastMessage.isImSpam) {
            session.lastMsgId = lastMessage.msgId ?: session.lastMsgId
            if (lastMessage.stopFloat == 0) {
                session.lastMsgTime = lastMessage.timestamp?.time ?: session.lastMsgTime
            }
            content.lastMessageType = lastMessage.contentType
            LastMsgCache.onSendNewMsg(lastMessage)
        }

        if (lastMessage.notShowInSession) {
            content.forcedDesc = lastMessage.recommendReason ?: ""
        } else if (lastMessage.isUpdateSession && !lastMessage.isImSpam) {
            content.forcedDesc = null
            content.distanceInfo = lastMessage.diatance
            content.lastMessageContent = SessionTextHelper.getMessageContent(lastMessage)

            content.isLastMessageReceive = lastMessage.receive
            content.onlyShowMessageContent =
                content.userChatTag?.text.isNullOrBlank().not()
        }

        updateLastMessageStatus(
            session,
            lastMessage.msgId,
            lastMessage.receive,
            lastMessage.status
        )
    }

    override fun transformOldSession(id: String): SessionEntity? {
        val oldSession = SessionService.getInstance().getSession("tm_$id") ?: return null

        val session = createSession(id)
        syncSession(session, null)
        val content = session.content.castOrNull<TextChatContent>() ?: return null

        session.lastMsgId = oldSession.lastmsgId
        session.lastMsgTime = oldSession.lastMessage?.timestampMillis ?: 0
        session.lastFetchTime = oldSession.fetchtime?.time ?: 0
        session.orderId = oldSession.orderId

        content.draftString = oldSession.draftString
        content.draftQuoteString = oldSession.draftQuoteString

        session.foldType = oldSession.foldType
        content.sessionBusinessType = content.sessionBusinessType

        updateSessionIndicatorWithEveryMessage(session, content, oldSession.lastMessage)
        oldSession.lastMessage?.let {
            updateSessionDescWithLastMessage(session, content, it, true)
        }
        return session
    }

    override fun updateSessionIndicatorWithEveryMessage(
        session: SessionEntity,
        content: TextChatContent,
        message: Message?
    ) {
        if (message?.status == Message.STATUS_CLOUD) return

        session.unreadMessageCount = UnreadCountMessageInterceptor.getCount(key(session.sessionId))

    }

    companion object {
        const val Type = "tm"

        @JvmStatic
        fun key(id: String) = SessionKey(Type, id)
    }

}