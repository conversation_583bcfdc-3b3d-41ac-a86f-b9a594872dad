package com.immomo.momo.maintab.session2.presentation.itemmodel

import android.text.TextUtils
import android.util.Pair
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.immomo.android.mm.cement2.AsyncCementModel
import com.immomo.android.mm.cement2.CementViewHolder
import com.immomo.android.mm.cement2.IViewHolderCreator
import com.immomo.framework.cement.CementModel
import com.immomo.framework.cement.SimpleCementAdapter
import com.immomo.framework.cement.eventhook.OnClickEventHook
import com.immomo.framework.cement.eventhook.OnLongClickEventHook
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.storage.preference.SPKeys
import com.immomo.mmstatistics.event.ClickEvent
import com.immomo.mmstatistics.event.ExposureEvent
import com.immomo.momo.R
import com.immomo.momo.appconfig.model.AppConfigV2
import com.immomo.momo.maintab.model.ActiveUser
import com.immomo.momo.maintab.session2.domain.model.type.ActiveUserSessionModel
import com.immomo.momo.maintab.sessionlist.ActiveUserItemModel
import com.immomo.momo.statistics.EVAction
import com.immomo.momo.statistics.EVPage
import com.immomo.momo.util.StringUtils

class ActiveUserExposure(
    val activeUserExposeSet: HashSet<ActiveUser> = HashSet()
) {
    fun getActiveExposeList(momoId: String?): Pair<Boolean, String?> {
        val idList: MutableList<String?> = ArrayList()
        var isInList = false
        if (activeUserExposeSet.size > 0) {
            for (user in activeUserExposeSet) {
                if (momoId != null && !isInList && TextUtils.equals(momoId, user.momoid)) {
                    isInList = true
                }
                if (!isInList) {
                    idList.add(user.momoid)
                }
                newMetaExposure(user)
            }
        }
        activeUserExposeSet.clear()
        return Pair(isInList, StringUtils.join(idList, ","))
    }

    private fun newMetaExposure(activeUser: ActiveUser) {
        if (!isOpenNewExposure()) {
            return
        }
        if (activeUser.isExpose) {
            return
        }
        val exposureEvent = ExposureEvent.create(ExposureEvent.Type.Normal)
            .page(EVPage.Msg.Chatlist)
            .action(EVAction.List.RecentlyOnline)
            .putExtra(USER_POS, activeUser.position)
            .putExtra("momo_id", activeUser.momoid)
            .putExtra("reason_text", activeUser.reason)
            .putExtra("is_more", "0")
        if (activeUser.type == ActiveUser.TYPE_COMMON) {
            exposureEvent.putExtra(TIME_TEXT, activeUser.title)
        } else {
            exposureEvent.putExtra(TIME_TEXT, activeUser.name)
        }
        exposureEvent.submit()
        activeUser.isExpose = true
    }

    private fun isOpenNewExposure(): Boolean {
        return KV.getUserInt(AppConfigV2.SPKeys.KEY_RELATION_RECORD_SWITCH, 0) == 1
    }

    companion object {
        private const val USER_POS = "user_pos"
        private const val TIME_TEXT = "time_text"
    }
}

class ActiveUserSessionItemModel(
    val session: ActiveUserSessionModel,
    private val topFoldMode: Boolean = false,//头像尺寸
    val exposureSet: ActiveUserExposure,
    @Deprecated("should not refer to view") val parentRv: RecyclerView,
    val onActiveHideClick: () -> Unit,
    val onActiveUserClick: (ActiveUser, Boolean) -> Unit
) : AsyncCementModel<ActiveUserSessionModel, ActiveUserSessionItemModel.ViewHolder>(session) {

    init {
        id(session.uniqueId)
    }

    override val layoutRes: Int
        get() = if (topFoldMode) R.layout.listitem_session_active_user_fold_mode else R.layout.listitem_session_active_user

    override val viewHolderCreator: IViewHolderCreator<ViewHolder>
        get() = object : IViewHolderCreator<ViewHolder> {
            override fun create(view: View): ViewHolder = ViewHolder(view)
        }


    private fun ViewHolder.bindEvent() {
        hideIv.setOnClickListener {
            onActiveHideClick()
        }

        val onScrollListener = object : RecyclerView.OnScrollListener() {
            var lastScrollTime = 0.0

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    validateExposeActiveUser(this@bindEvent)
                }
            }

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (lastScrollTime - System.currentTimeMillis() > 120) {
                    validateExposeActiveUser(this@bindEvent)
                }
                lastScrollTime = System.currentTimeMillis().toDouble()
            }
        }

        rvActive.addOnScrollListener(onScrollListener)
        parentRv.addOnScrollListener(onScrollListener)
    }

    private fun initAdapter(): SimpleCementAdapter {
        val activeAdapter = SimpleCementAdapter()
        activeAdapter.addEventHook(object : OnClickEventHook<ActiveUserItemModel.ViewHolder>(
            ActiveUserItemModel.ViewHolder::class.java
        ) {
            override fun onClick(
                view: View,
                viewHolder: ActiveUserItemModel.ViewHolder,
                position: Int,
                rawModel: CementModel<*>
            ) {
                if (rawModel !is ActiveUserItemModel) {
                    return
                }
                val activeUser = rawModel.activeUser
                onActiveUserClick(activeUser, false)
                ClickEvent.create()
                    .page(EVPage.Msg.Chatlist)
                    .action(EVAction.List.RecentlyOnline)
                    .putExtra("user_pos", position)
                    .putExtras(rawModel.logMap)
                    .submit()
            }

            override fun onBind(viewHolder: ActiveUserItemModel.ViewHolder): View? {
                return viewHolder.itemView
            }
        })
        activeAdapter.addEventHook(object : OnLongClickEventHook<ActiveUserItemModel.ViewHolder>(
            ActiveUserItemModel.ViewHolder::class.java
        ) {
            override fun onLongClick(
                view: View,
                viewHolder: ActiveUserItemModel.ViewHolder,
                position: Int,
                rawModel: CementModel<*>
            ): Boolean {
                if (rawModel !is ActiveUserItemModel) {
                    return false
                }
                val activeUser = rawModel.activeUser
                if (activeUser != null) {
                    onActiveUserClick(activeUser, true)
                    return true
                }
                return false
            }

            override fun onBind(viewHolder: ActiveUserItemModel.ViewHolder): View? {
                return viewHolder.itemView
            }
        })
        return activeAdapter
    }

    private fun ViewHolder.bindAdapter(
        activeAdapter: SimpleCementAdapter,
        topFoldMode: Boolean,
        activeUserList: List<ActiveUser>
    ) {
        // 比较列表的长度，然后比较列表中的item中每个元素的momoid
        var isTheSame = activeUserList.size == activeAdapter.itemCount
        val modelList: MutableList<ActiveUserItemModel> = ArrayList()
        for ((index, activeUser) in activeUserList.withIndex()) {
            if (isTheSame) {
                isTheSame =
                    if (index < activeAdapter.itemCount) {
                        val itemModel = activeAdapter.getModel(index) as? ActiveUserItemModel
                        if (itemModel?.activeUser != null) {
                            TextUtils.equals(itemModel.activeUser.momoid, activeUser.momoid)
                        } else {
                            false
                        }
                    } else {
                        false
                    }
            }
            modelList.add(
                ActiveUserItemModel(
                    topFoldMode, activeUser
                )
            )
        }

        if (!isTheSame) {
            activeAdapter.replaceAllModels(modelList)
            validateExposeActiveUser(this)
        }
    }

    override fun bindData(holder: ViewHolder) {
        super.bindData(holder)

        holder.tvTitle.text = KV.getUserStr(SPKeys.User.ActiveUser.KEY_ACTIVE_TITLE, "最近在线")

        holder.bindEvent()

        val activeAdapter = (holder.rvActive.adapter as? SimpleCementAdapter)
            ?: initAdapter().also { holder.rvActive.adapter = it }

        holder.bindAdapter(
            activeAdapter,
            topFoldMode,
            session.activeUsers.map { it.origin }
        )
    }


    /**
     * 将完全曝光的最近在线view加到待曝光列表里。
     */
    fun validateExposeActiveUser(holder: ViewHolder) {
        val start: Int =
            holder.activeLayoutManager.findFirstCompletelyVisibleItemPosition()
        val end: Int =
            holder.activeLayoutManager.findLastCompletelyVisibleItemPosition()
        if (start == RecyclerView.NO_POSITION && end == RecyclerView.NO_POSITION) {
            return
        }
        if (start >= 0 && end < session.activeUsers.size) {
            val activeUserList = session.activeUsers
            for (i in start until end + 1) {
                val activeUser = activeUserList[i]
                activeUser.origin.position = i
                exposureSet.activeUserExposeSet.add(activeUser.origin)
            }
        }
    }

    override fun unbind(holder: ViewHolder) {
        super.unbind(holder)
    }

    class ViewHolder(itemView: View) : CementViewHolder(itemView) {
        val activeLayoutManager: LinearLayoutManager = LinearLayoutManager(itemView.context)
        val rvActive: RecyclerView = itemView.findViewById(R.id.active_rv)

        val hideIv: ImageView = itemView.findViewById(R.id.iv_hide)
        val tvTitle: TextView = itemView.findViewById(R.id.title_tv)

        init {
            activeLayoutManager.orientation = LinearLayoutManager.HORIZONTAL
            rvActive.layoutManager = activeLayoutManager
            rvActive.itemAnimator = null
        }
    }
}