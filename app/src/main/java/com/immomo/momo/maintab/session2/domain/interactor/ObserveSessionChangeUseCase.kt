package com.immomo.momo.maintab.session2.domain.interactor

import com.immomo.android.mm.kobalt.domain.fx.Option
import com.immomo.android.mm.kobalt.domain.interactor.UseCase
import com.immomo.momo.maintab.session2.data.manager.SessionOp
import com.immomo.momo.maintab.session2.domain.service.ISessionChangeService
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow

class ObserveSessionChangeUseCase(
    dispatcher: CoroutineDispatcher,
    private val service: ISessionChangeService
) : UseCase<SessionOp, Unit>(dispatcher) {
    override fun build(param: Option<Unit>): Flow<SessionOp> {
        //TODO: make this buffer like
        return service.observe()
    }
}