package com.immomo.momo.maintab.task

import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.momo.protocol.http.UserApi

class FireSendTask(var remoteId: String?, var sign: String?, var source: String?) :
    MomoTaskExecutor.Task<Any, Any, Boolean>("") {
    override fun executeTask(vararg params: Any?): Bo<PERSON>an {
        return UserApi.getInstance().doFireSend(remoteId, sign, source)
    }

    override fun onTaskSuccess(result: Boolean?) {
        super.onTaskSuccess(result)
    }
}