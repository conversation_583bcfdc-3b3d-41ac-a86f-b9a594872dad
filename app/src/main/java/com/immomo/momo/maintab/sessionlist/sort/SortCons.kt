package com.immomo.momo.maintab.sessionlist.sort

import com.immomo.momo.maintab.sessionlist.sort.apt.SessionSortConfigV2Getter

object SortCons {
    val INTERVAL: Long
        get() {
            return SessionSortConfigV2Getter.get()
                .requestInterval() * 60 * 1000L
        }
    val MAX_TIME: Int
        get() {
            return SessionSortConfigV2Getter.get().requestTotalTimes()
        }

    object Type {
        const val TIME = "time"
        const val RECOMMEND = "recommend"
    }

    object Action {
        const val SET_SESSION_RECOMMEND = "session_recommend"
    }

    object Key {

        /*KV key 记录推荐请求时间，次数*/
        const val SORT_REQUEST_INFO = "sort_request_info"
        const val SESSION_RESPONSE = "session_response"
    }
}