

package com.immomo.momo.maintab;

import static com.immomo.android.module.feed.FeedSPKeys.User.PassNotes.KEY_IS_SHOW_FEED_PUBLISH_GUIDE;
import static com.immomo.android.module.feed.FeedSPKeys.User.PassNotes.KEY_IS_SHOW_SESSION_FEED_PUBLISH_GUIDE;
import static com.immomo.android.module.feedlist.presentation.feedUtils.FeedRecommendHelper.friend_feed_recommend;
import static com.immomo.framework.SPKeys.MainTab.KEY_EVENT_OPEN_FRAME;
import static com.immomo.momo.android.activity.WelcomeActivity.KEY_MODEL;
import static com.immomo.momo.android.activity.WelcomeActivity.MODEL_LOG;
import static com.immomo.momo.gotologic.GotoBase.GOTO_PARAMS_BACKURL;
import static com.immomo.momo.gotologic.GotoBase.GOTO_PARAMS_BTN_NAME;
import static com.immomo.momo.maingroup.manager.FrameConfigConst.FRAME_CHAT;
import static com.immomo.momo.maingroup.manager.FrameConfigConst.KEY_HOME_CHAT;
import static com.immomo.momo.maingroup.manager.FrameConfigConst.KEY_HOME_LIVE;
import static com.immomo.momo.maingroup.manager.FrameConfigConst.KEY_HOME_MAIN;
import static com.immomo.momo.maingroup.manager.FrameConfigConst.KEY_HOME_MORE;
import static com.immomo.momo.maingroup.manager.FrameConfigConst.KEY_HOME_TEMP;
import static com.immomo.momo.mvp.myinfonew.MyInfoConfigV2.KEY_SHOW_BOTTOM_AVATAR;
import static com.immomo.momo.newaccount.push.PushGotoProcessor.hideen_hometop;
import static com.immomo.momo.newaccount.push.PushGotoProcessor.tab;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.content.res.AssetManager;
import android.content.res.Configuration;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewStub;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.cosmos.mdlog.MDLog;
import com.cosmos.runtime.MomoRuntime;
import com.google.common.eventbus.Subscribe;
import com.immomo.LogTag;
import com.immomo.android.AppUseConstants;
import com.immomo.android.fep.prediction.Page;
import com.immomo.android.fep.prediction.PageAnalysis;
import com.immomo.android.login.multi.util.MultiAccountConfig;
import com.immomo.android.login.multi.util.RefreshMultiAccountRedPointReceiver;
import com.immomo.android.login.router.LoginRegisterRouter;
import com.immomo.android.module.feedlist.data.api.response.theme.CommonFeedTheme;
import com.immomo.android.module.feedlist.data.api.response.theme.common.CommonFeedSource;
import com.immomo.android.module.feedlist.presentation.feedUtils.FeedRecommendHelper;
import com.immomo.android.mvvm.common.utils.OneKeyLoginConfig;
import com.immomo.android.mvvm.common.utils.QuickLoginHelper;
import com.immomo.android.mvvm.luaview.view.LoginLuaActivity;
import com.immomo.android.mvvm.oppokeychain.OppoKeyChainTool;
import com.immomo.android.router.live.LiveMainTabChangeRouter;
import com.immomo.android.router.momo.EventKeys;
import com.immomo.android.router.momo.business.login.LoginRouterImpl;
import com.immomo.bugfix.oppo_wm.OppoCrashFix;
import com.immomo.framework.account.MessageManager;
import com.immomo.framework.base.BaseFragment;
import com.immomo.framework.base.BaseReceiver;
import com.immomo.framework.base.BaseTabGroupActivity;
import com.immomo.framework.base.BaseTabOptionFragment;
import com.immomo.framework.base.StepMonitor;
import com.immomo.framework.kotlin.ImageType;
import com.immomo.framework.statistics.pagespeed.AutoSpeed;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.framework.utils.StatusBarUtil;
import com.immomo.framework.utils.UIUtils;
import com.immomo.lcapt.evlog.EVLog;
import com.immomo.mgs.sdk.Mgs;
import com.immomo.mls.InitData;
import com.immomo.mls.MLSBundleUtils;
import com.immomo.mmstatistics.event.ClickEvent;
import com.immomo.mmstatistics.event.LaunchEvent;
import com.immomo.mmutil.log.Log4Android;
import com.immomo.mmutil.task.MMThreadExecutors;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.task.ThreadUtils;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.moarch.account.AccountKit;
import com.immomo.moarch.account.AccountUser;
import com.immomo.molive.foundation.livehome.TabGoto.TabGotoManager;
import com.immomo.molive.foundation.permission.LivePermissionCheckManager;
import com.immomo.molive.ui.livemain.LiveHomeFragment;
import com.immomo.molive.ui.push.ILiveTempView;
import com.immomo.molive.ui.push.LiveTabTempView;
import com.immomo.momo.ActivityLifecycleMonitor;
import com.immomo.momo.GotoKeys;
import com.immomo.momo.MomoKit;
import com.immomo.momo.OpenScreenGuideUtil;
import com.immomo.momo.R;
import com.immomo.momo.abtest.config.ABConfigManager;
import com.immomo.momo.agora.mr.LiveMonitor;
import com.immomo.momo.android.activity.WelcomeActivity;
import com.immomo.momo.android.broadcast.ReflushBusiUnreadReceiver;
import com.immomo.momo.android.broadcast.ReflushSessionUnreadReceiver;
import com.immomo.momo.android.service.Initializer;
import com.immomo.momo.android.service.test.UserTestingService;
import com.immomo.momo.android.synctask.FirstStartJob;
import com.immomo.momo.android.view.tips.TipManager;
import com.immomo.momo.android.view.tips.tip.ITip;
import com.immomo.momo.appconfig.model.AppConfigV2;
import com.immomo.momo.appstart.AppStartHelper;
import com.immomo.momo.audiomatch.worker.NearbyAudioWorker;
import com.immomo.momo.common.AppKit;
import com.immomo.momo.common.view.floatview.AllFloatManagerExecute;
import com.immomo.momo.crash.CrashLogger;
import com.immomo.momo.dataprotect.DataProtectManager;
import com.immomo.momo.eventbus.DataEvent;
import com.immomo.momo.eventbus.SimpleEvent;
import com.immomo.momo.feed.listener.CommentAtTextChangeListener;
import com.immomo.momo.feed.media.widget.CommentView;
import com.immomo.momo.feed.media.widget.RecommendReasonInfoView;
import com.immomo.momo.globalevent.GlobalEventManager;
import com.immomo.momo.gotologic.GotoDispatcher;
import com.immomo.momo.greet.GreetHelper;
import com.immomo.momo.guest.GuestBlockHelper;
import com.immomo.momo.guest.GuestConfig;
import com.immomo.momo.guest.gotohelper.AppEnterStackHelper;
import com.immomo.momo.guest.gotohelper.HiBoardMission;
import com.immomo.momo.home.TempHomeFragment;
import com.immomo.momo.home.group.BaseGroupFragment;
import com.immomo.momo.home.manager.FrameConfigManager;
import com.immomo.momo.homepage.HomeUniverseGuideUtils;
import com.immomo.momo.homepage.fragment.HomePageFragment;
import com.immomo.momo.homepage.helper.LikeSettingHelper;
import com.immomo.momo.innergoto.helper.ActivityHandler;
import com.immomo.momo.knock.KnockBusinessManager;
import com.immomo.momo.knock.config.KnockSettingConfigV2;
import com.immomo.momo.luaview.LuaViewActivity;
import com.immomo.momo.maingroup.IBusinessKeyEvent;
import com.immomo.momo.maingroup.IBusinessView;
import com.immomo.momo.maingroup.IBusinessViewForScroll;
import com.immomo.momo.maingroup.IHomeMainView;
import com.immomo.momo.maingroup.manager.FrameConfig;
import com.immomo.momo.maingroup.manager.FrameConfigConst;
import com.immomo.momo.maingroup.manager.TabConfigModel;
import com.immomo.momo.maingroup.manager.TabFrameConfig;
import com.immomo.momo.maintab.dialog.DarkModelGuidDialogActivity;
import com.immomo.momo.maintab.sessionlist.SessionListFragment;
import com.immomo.momo.maintab.sessionlist.SessionListReceiver;
import com.immomo.momo.maintab.sessionlist.SessionTabFragment;
import com.immomo.momo.maintab.usecase.AvatarCheckActivity;
import com.immomo.momo.maintab.usecase.TabBottomHelper;
import com.immomo.momo.maintab.view.TabItemView;
import com.immomo.momo.message.sayhi.utils.NewSayHiSessionFlowUtil;
import com.immomo.momo.message.view.BlockFeedbackAlertDialog;
import com.immomo.momo.message.view.DragBubbleView;
import com.immomo.momo.mvp.maintab.NotificationSourceHelper;
import com.immomo.momo.mvp.maintab.mainbubble.MainBubbleHelper;
import com.immomo.momo.mvp.maintab.mainbubble.MainTabBusinessHelper;
import com.immomo.momo.mvp.maintab.mainimpl.ExchangeKeyTask;
import com.immomo.momo.mvp.maintab.mainimpl.MainBroadcastProcessor;
import com.immomo.momo.mvp.maintab.mainimpl.MainInitTasksProcessor;
import com.immomo.momo.mvp.maintab.mainimpl.MainMKUpdateImpl;
import com.immomo.momo.mvp.maintab.mainimpl.MainSplashView;
import com.immomo.momo.mvp.maintab.mainimpl.MainTipsPresenter;
import com.immomo.momo.mvp.maintab.mainimpl.MainUGuideAndContactProcessor;
import com.immomo.momo.mvp.maintab.mainimpl.SystemBroadcastProcessor;
import com.immomo.momo.mvp.maintab.mainimpl.UserStateHelperKt;
import com.immomo.momo.mvp.maintab.mainimpl.appconfig.GreyModeAppConfig;
import com.immomo.momo.mvp.maintab.mainimpl.appconfig.apt.GreyModeAppConfigGetter;
import com.immomo.momo.mvp.maintab.mainimpl.appconfig.apt.SplashAppConfigGetter;
import com.immomo.momo.mvp.maintab.maininterface.IMainBroadcastProcessor;
import com.immomo.momo.mvp.maintab.maininterface.IMainBroadcastView;
import com.immomo.momo.mvp.maintab.maininterface.IMainInitTasksProcessor;
import com.immomo.momo.mvp.maintab.maininterface.IMainMKUpdate;
import com.immomo.momo.mvp.maintab.maininterface.IMainSplashView;
import com.immomo.momo.mvp.maintab.maininterface.IMainTipsView;
import com.immomo.momo.mvp.maintab.maininterface.IMainUGuideAndContactProcessor;
import com.immomo.momo.mvp.maintab.task.UpdateBasicUserInfoTask;
import com.immomo.momo.mvp.myinfonew.MultiHelper;
import com.immomo.momo.mvp.myinfonew.MyInfoLuaFragment;
import com.immomo.momo.mvp.myinfonew.util.VisitorBombEvent;
import com.immomo.momo.mvp.nearby.guide.NearbyLiveGuideTipHelper;
import com.immomo.momo.newaccount.channel.ChannelSyncConstant;
import com.immomo.momo.newaccount.channel.SyncChannelHelper;
import com.immomo.momo.newaccount.channel.bean.SyncChannelBean;
import com.immomo.momo.newaccount.channel.registerchannel.RegisterChannelBusiness;
import com.immomo.momo.newaccount.common.LoginAbConfigConstant;
import com.immomo.momo.newaccount.common.util.UploadGrowLog;
import com.immomo.momo.newaccount.push.PushGotoProcessor;
import com.immomo.momo.newaccount.push.event.PushGotoparams;
import com.immomo.momo.newaccount.recommendredstar.gotoimpl.RecommendRedStarGotoImpl;
import com.immomo.momo.performance.SimpleViewStubProxy;
import com.immomo.momo.permission.MgsPermissionHelper;
import com.immomo.momo.permission.PermissionUtil;
import com.immomo.momo.protocol.http.MiPushApi;
import com.immomo.momo.protocol.imjson.Bootstrap;
import com.immomo.momo.protocol.imjson.handler.InteractionNoticeHandler;
import com.immomo.momo.protocol.imjson.receiver.MessageKeys;
import com.immomo.momo.protocol.imjson.util.Debugger;
import com.immomo.momo.push.PushHelper;
import com.immomo.momo.statistics.EVAction;
import com.immomo.momo.statistics.EVPage;
import com.immomo.momo.statistics.online.OnlineManager;
import com.immomo.momo.test.qaspecial.TestItemsGenerator;
import com.immomo.momo.test.qaspecial.TestMsgGenerator;
import com.immomo.momo.universe.config.UniverseSessionABTest;
import com.immomo.momo.universe.im.UniUnreadManager;
import com.immomo.momo.universe.im.cons.ReceiverCons;
import com.immomo.momo.universe.im.cons.SessionCons;
import com.immomo.momo.universe.luaview.UniverseFeedLuaFragment;
import com.immomo.momo.universe.phonograph.PhonographManager;
import com.immomo.momo.universe.statistics.IUniverseLog;
import com.immomo.momo.universe.user.UniUserService;
import com.immomo.momo.universe.util.AudioUtil;
import com.immomo.momo.universe.util.UniverseCons;
import com.immomo.momo.util.AnimatorSafeUtil;
import com.immomo.momo.util.ColorUtils;
import com.immomo.momo.util.GotoParserUtil;
import com.immomo.momo.util.OaidSupplier;
import com.immomo.momo.util.SchemeUtil;
import com.immomo.momo.util.StringUtils;
import com.immomo.momo.util.TopCardUtils;
import com.immomo.momo.util.UnivAttractionDialogABTest;
import com.immomo.momo.util.fabricmomo.FabricLogger;
import com.immomo.momo.util.teenmode.TeenModeHelper;
import com.immomo.momo.videochat.friendvideo.friend.FriendQChatConstants;
import com.immomo.momo.videochat.friendvideo.friend.FriendQChatWorker;
import com.immomo.momo.videochat.friendvideo.single.ui.FriendQChatActivity;
import com.immomo.momoenc.APIKeyholder;
import com.immomo.momoenc.EncManager;

import java.util.HashMap;
import java.util.Map;
import java.util.Stack;
import java.util.concurrent.TimeUnit;

import de.greenrobot.event.EventBus;
import immomo.com.mklibrary.core.utils.MKKit;
import info.xudshen.android.appasm.AppAsm;
import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

@SuppressLint("LogUse")
public class MaintabActivity extends BaseTabGroupActivity implements
        IMainBroadcastView, LaunchEvent.Helper, IMainTipsView {
    public static final int REQUEST_USERGUID = 321;
    public static final int REQUEST_SHOW_SPLASH = 322;

    public static final int TAB_FEED = 0;
    public static final int TAB_LIVE_MK = 1;
    public static final int TAB_MESSAGE = 2;
    public static final int TAB_CONTACT = 3;
    public static final int TAB_SETTING = 4;

    public static final String KEY_TABINDEX = "tabindex";
    public static final String KEY_SON_TABINDEX = "sontabindex";
    //首帧重构子fragment对应business
    public static final String KEY_SON_BUSINESS = "son_tab_business";
    // 页面重绘后定位到子业务
    public static final String KEY_SAVE_INSTANCE_SON_BUSINESS = "key_save_instane_son_business";
    public static final String KEY_SON_TABKEY = "sontabkey";
    public static final String KEY_GOTO_NOTICE = "setting_goto_notice";
    public static final String KEY_GOTO = "goto";
    public static final String KEY_DEEPLINK_GOTO = "deeplink_goto";
    public static final String KEY_SOURCE = "source";
    public static final String KEY_BUSINESS_START_APP = "KEY_BUSINESS_START_APP";
    public static final String KEY_CALL_FROM_SDK = "KEY_CALL_FROM_SDK";
    public static final String KEY_NEED_GET_PROFILE = "KEY_NEED_GET_PROFILE";
    public static final String KEY_NEED_RECREATE = "KEY_NEED_RECREATE";
    public static final String KEY_IS_GUEST_MODE = "KEY_IS_GUEST_MODE";
    public static final String KEY_LAUNCH_LOGGED = "KEY_LAUNCH_LOGGED";
    public static final String KEY_PUSH_GREET_REMIND = "key_greet_remind_key";
    public static final String KEY_GOTO_WORLD_TAB = "key_goto_world_tab";
    public static final String KEY_GOTO_PARAMS_STR = "key_goto_params_str";

    public static final String ACTION_HOME_TO_OTHER_BROADCAST = "action_home_to_other_broadcast";

    public static final String TAG_OPEN_FRAME = "TAG_OPEN_FRAME";

    public static boolean NEED_RECREATE = false;

    public static boolean FRONT_PAGE_TILE_VISIBLE = false;
    private boolean isFirstResume = true;
    private boolean isGotoLoginPage = false;

    public boolean isFromThirdRegister = false;

    private static final String TASK_TAG = "maintabactivity_task_tag";

    private View bottomContaiver;
    private View bottomLayout;
    //是否已经显示小宇宙
    private static boolean hasShowStarView = false;

    /**
     * 访客四期访客模式可以进入Maintab，该变量标识是否是访客模式进入Maintab.
     */
    private boolean isInGuestModel = false;
    private CommentAtTextChangeListener mListener;

    private TabItemView nearBubbleContainer;
    private TabItemView liveBubbleContainer;
    private TabItemView sessionListBubbleContainer;
    private TabItemView focusBubbleContainer;
    private TabItemView profileBubbleContainer;
    private TabBottomHelper tabHelper = new TabBottomHelper();
    private ILiveTempView mLiveTabTempView;

    private final TabInfo[] mainTabs;
    //index和homekey映射
    private final Map<Integer, String> mHomeKeyIndices = new HashMap<>();
    //显示首页还是星球loading页
    private boolean isMaintabShow = true;
    private SimpleViewStubProxy<View> commentProxy;
    private SimpleViewStubProxy<View> recommendProxy;
    private GlobalEventManager.Subscriber subscriber;
    private static final String NTF_MK_NOTE_FEED_PUBLISH_GUIDE = "NTF_MK_NOTE_FEED_PUBLISH_GUIDE";
    private static final String NTF_MK_NOTE_SESSION_PUBLISH_GUIDE = "NTF_MK_NOTE_SESSION_PUBLISH_GUIDE";
    private boolean isUniverseTabLoad = false;

    {
        mainTabs = new TabInfo[]{
                new TabInfo(HomePageFragment.class, R.id.maintab_layout_nearby, true),
                new TabInfo(LiveHomeFragment.class, R.id.maintab_layout_live),
                new TabInfo(SessionTabFragment.class, R.id.maintab_layout_chat, true),
                new TabInfo(TempHomeFragment.class, R.id.maintab_layout_follow, true),
                new TabInfo(MyInfoLuaFragment.class, R.id.maintab_layout_profile)
        };

        mHomeKeyIndices.put(TAB_FEED, KEY_HOME_MAIN);
        mHomeKeyIndices.put(TAB_LIVE_MK, KEY_HOME_LIVE);
        mHomeKeyIndices.put(TAB_MESSAGE, KEY_HOME_CHAT);
        mHomeKeyIndices.put(TAB_CONTACT, KEY_HOME_TEMP);
        mHomeKeyIndices.put(TAB_SETTING, KEY_HOME_MORE);
    }

    /**
     * 修复webview 崩溃问题
     *
     * @return
     */
    @Override
    public AssetManager getAssets() {
        return getResources().getAssets();
    }

    /**
     * *********************************************************************************
     */
    private int mCurrentIndex = TAB_FEED;
    private IMainSplashView mSplashView = new MainSplashView(this);
    private IMainBroadcastProcessor mBroadcastProcessor;
    private IMainBroadcastProcessor mSystemBroadcastProcessor;
    private IMainUGuideAndContactProcessor mUserGuideAndContactProcessor = new MainUGuideAndContactProcessor(this);
    private IMainInitTasksProcessor mInitTaskProcessor = new MainInitTasksProcessor();
    private IMainMKUpdate mMKUpdate = new MainMKUpdateImpl();
    private MainTipsPresenter tipsPresenter = new MainTipsPresenter(this);
    private boolean mBackPressToBackground = false;
    private boolean isBubbleViewsInit = false;
    //页面根布局
    //    private CoordinatorLayout rootView;
    private DragBubbleView dragBubbleView;
    private boolean isFromLogout; //是否退出登录过来的
    private boolean isFirstCreate = true;
    private RefreshMultiAccountRedPointReceiver refreshMultiAccountRedPointReceiver;
    public String currentLiveBusiness = FrameConfigConst.FRAME_LIVE;

    private Disposable oppoKeyChainDisposable;

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        OppoCrashFix.fixAddViewCrash(this);
        super.onCreate(savedInstanceState);
        createMicroVideoParseSource();
        EventBus.getDefault().register(this);
        if (getIntent() != null) {
            isFromLogout = getIntent().getIntExtra(KEY_MODEL, MODEL_LOG) == WelcomeActivity.MODEL_LOGIN;
            isFromThirdRegister = getIntent().getBooleanExtra(AppUseConstants.IS_FROM_THIRD_REGISTER, false);
            if (PushHelper.isFromPush(getIntent())) {
                HiBoardMission.isFromPushClick = true;
            }
        }
        MomoKit.getApp().isMainTabLaunched.incrementAndGet();
        if (GuestConfig.getInstance().isGuestMode()) {
            isInGuestModel = true;
            onGuestCreate(savedInstanceState);
        } else {
            onNormalCreate(savedInstanceState);
            TeenModeHelper.get().start();
            OppoKeyChainTool.save2KeyChain();
            OnlineManager.checkStartLoginKnock(); // 检查敲敲是否应该发起请求
        }
        initMgsWebViewPool();
        initBridge();

        AnimatorSafeUtil.keepSafe();
    }

    @Nullable
    public CommentView getCommentView() {
        if (commentProxy == null) {
            return null;
        }
        View view = commentProxy.getStubView();
        if (view == null) {
            return null;
        }
        return view.findViewById(R.id.comment_layout);
    }

    @Nullable
    public RecommendReasonInfoView getRecommendView() {
        if (recommendProxy == null) {
            return null;
        }
        View view = recommendProxy.getStubView();
        if (view == null) {
            return null;
        }
        return view.findViewById(R.id.recommend_reason_view);
    }

    @Override
    public boolean showFragment(int index) {
        return super.showFragment(index);
    }

    private void initMgsWebViewPool() {
        boolean feedDiscard = KV.getSysBool(AppConfigV2.SPKeys.KEY_FEED_DISCARD, false);
        boolean messageDiscard = KV.getSysBool(AppConfigV2.SPKeys.KEY_MESSAGE_DISCARD, false);
        boolean initPoolEnable = KV.getSysBool(AppConfigV2.SPKeys.KEY_MGS_POOL_INIT_ENABLE, true);
        if (initPoolEnable && !(feedDiscard && messageDiscard)) {
            Mgs.initWebViewPool(KV.getSysInt(AppConfigV2.SPKeys.KEY_MGS_INSTANCE_NUMBER, 3));
        }
        MKKit.prepare();
    }

    /**
     * 设置桥接
     */
    private void initBridge() {
        subscriber = event -> {
            if (event == null) {
                return;
            }
            if (StringUtils.equalsNonNull(event.getName(), NTF_MK_NOTE_FEED_PUBLISH_GUIDE)) {
                if (event.getInt("notShowForever", 0) == 1) {
                    KV.saveUserValue(KEY_IS_SHOW_FEED_PUBLISH_GUIDE, false);
                } else {
                    KV.saveUserValue(KEY_IS_SHOW_FEED_PUBLISH_GUIDE, true);
                }
            } else if (StringUtils.equalsNonNull(event.getName(), NTF_MK_NOTE_SESSION_PUBLISH_GUIDE)) {
                if (event.getInt("notShowForever", 0) == 1) {
                    KV.saveUserValue(KEY_IS_SHOW_SESSION_FEED_PUBLISH_GUIDE, false);
                } else {
                    KV.saveUserValue(KEY_IS_SHOW_SESSION_FEED_PUBLISH_GUIDE, true);
                }
            } else if (StringUtils.equalsNonNull(event.getName(), UniverseCons.Event.UNIVERSE_OPEN_HOME_NOTI)) {
                if (HomeUniverseGuideUtils.INSTANCE.universeTabCheck()) {
                    finishMainTabTopStackActivity();
                }
            } else if (StringUtils.equalsNonNull(event.getName(), "switch_to_home_universe")) {
                Map<String, Object> params = event.getMsg();
                String source = params.get("source").toString();
                Object fromData = params.get("fromSource");
                String fromSource = null;
                if (fromData instanceof String) {
                    fromSource = fromData.toString();
                }
                if (UniverseSessionABTest.INSTANCE.isTest() && StringUtils.equalsNonNull(fromSource, "push")) { // 实验组跳消息帧
                    openFrame(FrameConfigConst.FRAME_CHAT);
                } else {
                    toUniverseTab(TextUtils.isEmpty(source) ? "other" : source);
                }
                if (params.containsKey("topFeedId")) {
                    String feedId = params.get("topFeedId").toString();
                    if (!TextUtils.isEmpty(feedId)) {
                        long delay = 100;
                        if (!isUniverseTabLoad) {
                            delay = 1000;
                        }
                        MomoMainThreadExecutor.postDelayed(hashCode(), () -> {
                            GlobalEventManager.getInstance().sendEvent
                                    (new GlobalEventManager.Event("UNIVERSE_BUSINESS_INSERT_TOPFEEDID")
                                            .dst(GlobalEventManager.EVN_LUA)
                                            .src(GlobalEventManager.EVN_NATIVE).msg(params));
                        }, delay);
                    }
                }
            } else if (StringUtils.equalsNonNull(event.getName(), UniverseCons.Event.UNIVERSE_HOME_TAB_LOADED)) {
                //小宇宙lua页面已经加载
                isUniverseTabLoad = true;
            } else if (StringUtils.equalsNonNull(event.getName(), "UNIVERSE_BACKHOME_UNIVTAB")) {
                //走小宇宙goto,二级跳转活动也，但某些场景返回存在中间页(小宇宙在首页)，关闭上层页面
                if (HomeUniverseGuideUtils.INSTANCE.universeTabCheck()) {
                    finishMainTabTopStackActivity();
                }
            } else if (StringUtils.equalsNonNull(event.getName(), "UNIVERSE_IS_SHOW_GRAVITY_NOTI")) {
                //lua先给原生发通知，原生收到通知以后在根据本地实验和缓存判断是否需要展示引力弹框，然后再给lua发通知
                //引力弹框只准对tab,消息右上角小宇宙入口和更多帧下拉小宇宙入口
                if (UnivAttractionDialogABTest.INSTANCE.isTestA() && !HomeUniverseGuideUtils.INSTANCE.universeHomeTabCheck()) {
                    GlobalEventManager.getInstance().sendEvent(
                            new GlobalEventManager.Event("UNIVERSE_SHOW_GRAVITY_NOTI")
                                    .dst(GlobalEventManager.EVN_LUA).src(GlobalEventManager.EVN_NATIVE)
                    );
                }
            } else if (StringUtils.equalsNonNull(event.getName(), "UNIVERSE_CHAT_REMARKS_NAME")) {
                String userId = event.getString("uid", "");
                String remark = event.getString("data", "");
                if (StringUtils.isEmpty(userId)) {
                    return;
                }
                universeChatRemarksName(userId, remark);
            } else if (StringUtils.equalsNonNull(event.getName(), "Notification_Refresh_People")) {
                GlobalEventManager.getInstance().sendEvent(
                        new GlobalEventManager.Event("Notification_NativeToLua_NearbyPeople_BequietChange")
                                .dst(GlobalEventManager.EVN_LUA).src(GlobalEventManager.EVN_NATIVE)
                );
            } else if (StringUtils.equalsNonNull(event.getName(), UniverseFeedLuaFragment.EVENT_SHOW_UNIVERSE_MSG_GUIDE)) {
                if (sessionListBubbleContainer != null) {
                    int pixels = UIUtils.getPixels(10);
                    TipManager.bindActivity(this)
                            .setTriangles(null, null, null, null)
                            .setBackground(UIUtils.getDrawable(R.drawable.bg_corner_10dp_4e7fff))
                            .setTextColor(UIUtils.getColor(R.color.white))
                            .setTouchToHideAll(true)
                            .setTextPadding(pixels, pixels, pixels, pixels)
                            .showTipView(sessionListBubbleContainer, "小宇宙的互动和消息搬到消息列表啦，快来看看~", 0, 0, ITip.Triangle.BOTTOM)
                            .autoHide(3000L);
                }
            }
        };
        GlobalEventManager.getInstance().register(subscriber, GlobalEventManager.EVN_NATIVE);
    }

    //跳转小宇宙tab
    public void toUniverseTab(String pageSource) {
        UniverseFeedLuaFragment.Companion.sendSourcepageEvent(pageSource);
        MomoMainThreadExecutor.postDelayed(hashCode(), () -> {
            int index = FrameConfigManager.INSTANCE.getContainerIndexByFrame(FrameConfigManager.INSTANCE.getHomeKeyByBusinessKey(FrameConfigConst.FRAME_UNIVERSE));
            if (index >= 0) {
                showFragment(index);
                BaseFragment currentFragment = getCurrentFragment();
                if (currentFragment instanceof IHomeMainView
                ) {
                    ((IHomeMainView) currentFragment).openFrame(FrameConfigConst.FRAME_UNIVERSE, null);
                    updateBottomBarTheme(FrameConfigManager.INSTANCE.getFrameConfigByKey(FrameConfigConst.FRAME_UNIVERSE));
                }
            }
        }, 200);
    }

    public void openFrame(String frameKey) {
        MomoMainThreadExecutor.postDelayed(TAG_OPEN_FRAME, () -> {
            int index = FrameConfigManager.INSTANCE.getContainerIndexByFrame(FrameConfigManager.INSTANCE.getHomeKeyByBusinessKey(frameKey));
            if (index >= 0) {
                showFragment(index);
                BaseFragment currentFragment = getCurrentFragment();
                if (currentFragment instanceof IHomeMainView) {
                    ((IHomeMainView) currentFragment).openFrame(frameKey, null);
                    updateBottomBarTheme(FrameConfigManager.INSTANCE.getFrameConfigByKey(frameKey));
                }
            }
        }, 200);
    }

    private void finishMainTabTopStackActivity() {
        try {
            Stack<Activity> stack = ActivityLifecycleMonitor.lifecycleStack;
            while (!stack.isEmpty()) {
                Activity activity = stack.peek();
                if (activity instanceof MaintabActivity) {
                    return;
                } else {
                    stack.pop();
                    if (null != activity) {
                        activity.finish();
                    }
                }
            }
        } catch (Exception e) {
            MDLog.printErrStackTrace(TASK_TAG, e);
        }
    }

    public void updateBottomBarTheme(FrameConfig config) {
        boolean dark = false;
        if (config != null) {
            bottomLayout.setBackgroundColor(ColorUtils.parseColor(config.getTabbarColor(), Color.WHITE));
        } else {
            bottomLayout.setBackgroundColor(Color.WHITE);
        }
        dark = (config != null && config.getStatusBarMode() == FrameConfigConst.BarThemeMode.DARK)
                || com.immomo.momo.util.MomoKit.INSTANCE.isAppDarkMode() || isLiveDark(config);
        if (com.immomo.momo.util.MomoKit.INSTANCE.isAppDarkMode() || isLiveDark(config)) {
            bottomLayout.setBackgroundColor(Color.BLACK);
        }
        setStatusBarTheme(dark);
        getWindow().setNavigationBarColor(dark ? Color.BLACK : Color.WHITE);
    }

    public void updateBottomBarTheme(boolean dark, int color) {
        setStatusBarTheme(dark);
        getWindow().setNavigationBarColor(dark ? Color.BLACK : Color.WHITE);
        updateBottomColor(dark, color);
    }

    public void updateBottomColor(boolean dark, int color) {
        if (!dark) {
            bottomLayout.setBackgroundColor(UIUtils.getColor(R.color.white));
            return;
        }
        bottomLayout.setBackgroundColor(color);
    }

    public void updateBottomBusinessTab(boolean dark, String homekey) {
        MainTabBusinessHelper.INSTANCE.onTabChanged(mHomeKeyIndices.get(mCurrentIndex), dark);
        if (mLiveTabTempView != null) {
            mLiveTabTempView.updateTheme(dark);
        }
    }

    @Override
    public Object getSystemService(@NonNull String name) {
        // FIX: 修复OppO手机上 WindowManager崩溃问题
        if (WINDOW_SERVICE.equals(name)) {
            WindowManager wm = OppoCrashFix.getRealWindowManager(getWindowManager());
            if (wm != null) {
                return wm;
            }
        }
        return super.getSystemService(name);
    }

    private void onGuestCreate(Bundle savedInstanceState) {
        setContentView(getLayoutRes());
        startInitializerService(savedInstanceState);
        initBottomContainerView();
        initDragBubbleView();
        registerReceiver();
        mInitTaskProcessor.startAppConfigGetTask();
        findTargetFragmentIndex(savedInstanceState);
        checkSplashAndInitFragments(savedInstanceState);
        // TODO: 2018/8/9 可JB挪走
        mMKUpdate.updateMKSession();
        gotoLogin(savedInstanceState != null);
        saveSyncChannelInfo();
    }

    private void onNormalCreate(Bundle savedInstanceState) {
        SessionRefreshTimeUtil.setSessionListInitTimestamp(System.nanoTime());
        if (!AppKit.getAccountManager().isOnline()) {
            // Note 处理访客模式下推送进入Maintab不是Visitormaintab的情况。
            finish();
            return;
        }
        if (savedInstanceState == null) {
            FabricLogger.logEvent(FabricLogger.EventType.Event_APP_OPENED);
            //冷启动请求一次
            FirstStartJob.INSTANCE.proceedAsync();
        }
        LiveMonitor.init();
        ABConfigManager.getInstance().sendIMParam();
        AppInitializer.initAppRelated(MomoKit.getApp());
        UniverseSessionABTest.INSTANCE.refresh();
        setContentView(getLayoutRes());
        startInitializerService(savedInstanceState);
        initBottomContainerView();
        asyncInitServiceAndTask();
        initDragBubbleView();
        registerReceiver();
        registerSystemReceiver();
        mInitTaskProcessor.startAppConfigGetTask();
        AppStartHelper.INSTANCE.getAppStartConfigAsync(true);
        findTargetFragmentIndex(savedInstanceState, true);
        checkSplashAndInitFragments(savedInstanceState);
        // TODO: 2018/8/9 可JB挪走
        mMKUpdate.updateMKSession();

        updateProfileBubble();

        initReceiver();
        initPushLive();
        saveSyncChannelInfo();
        MultiHelper.INSTANCE.logMultiAccountAndMsgStatus();
        MultiHelper.INSTANCE.requestMultiNotify(AccountKit.getAccountManager().getCurrentAccountUserId(), TASK_TAG);
        initCommentView();
        UserTestingService.INSTANCE.start(this);
        DarkModelGuidDialogActivity.checkShow(this);
        if (!NearbyAudioWorker.INSTANCE.isIdle()) {
            handlePageRecreate();
        }
    }

    private void updateProfileBubble() {
        if (KV.getUserInt(KEY_SHOW_BOTTOM_AVATAR, 0) != 0) {
            refreshAccountUnreadAvatar();
        }
        MainBubbleHelper.INSTANCE.checkProfileBubble();
        GlobalEventManager.getInstance().sendEvent(
                new GlobalEventManager.Event(MyInfoLuaFragment.KEY_EVENT_UPDATE_INFO_SUCCESS)
                        .dst(GlobalEventManager.EVN_LUA).src(GlobalEventManager.EVN_NATIVE));
    }

    private BaseReceiver.IBroadcastReceiveListener refreshListener =
            intent -> {
                if (intent.getAction().equals(RefreshMultiAccountRedPointReceiver.Companion.getACTION())) {
                    updateProfileBubble();
                }
            };

    private void initReceiver() {
        refreshMultiAccountRedPointReceiver = new RefreshMultiAccountRedPointReceiver(getBaseContext());
        refreshMultiAccountRedPointReceiver.setReceiveListener(refreshListener);
    }

    private void initCommentView() {
        commentProxy = new SimpleViewStubProxy<>((ViewStub) findViewById(R.id.viewstub_comment));
        recommendProxy = new SimpleViewStubProxy<>((ViewStub) findViewById(R.id.viewstub_recommend));
    }

    private void showNormalHomePage() {
        if (!isMaintabShow) {
            isMaintabShow = true;
            bottomContaiver.setAlpha(1);
            bottomContaiver.setVisibility(ViewStub.VISIBLE);
        }

    }

    public static boolean isStarViewVisibility() {
        return hasShowStarView;
    }

    private void initBottomContainerView() {
        nearBubbleContainer = (TabItemView) findViewById(R.id.maintab_layout_nearby);
        liveBubbleContainer = (TabItemView) findViewById(R.id.maintab_layout_live);
        sessionListBubbleContainer = (TabItemView) findViewById(R.id.maintab_layout_chat);
        focusBubbleContainer = (TabItemView) findViewById(R.id.maintab_layout_follow);
        profileBubbleContainer = (TabItemView) findViewById(R.id.maintab_layout_profile);

        foldScreenUpdateContainerWidth();

        bottomLayout = findViewById(R.id.tabwidget);
        bottomContaiver = findViewById(R.id.maintab_bottom_container);

        //init tabhelper
        initTabHelper();

        if (GreyModeAppConfigGetter.get().mainBottomBarSwitch() == 1) {
            GreyModeAppConfig.INSTANCE.setGrayMode(bottomContaiver);
        }
    }

    private void initTabHelper() {
        tabHelper.initViews(FrameConfigConst.KEY_HOME_MAIN, nearBubbleContainer);
        tabHelper.initViews(FrameConfigConst.KEY_HOME_LIVE, liveBubbleContainer);
        tabHelper.initViews(FrameConfigConst.KEY_HOME_CHAT, sessionListBubbleContainer);
        tabHelper.initViews(FrameConfigConst.KEY_HOME_TEMP, focusBubbleContainer);
        tabHelper.initViews(FrameConfigConst.KEY_HOME_MORE, profileBubbleContainer);

        tabHelper.setItemTempView(KEY_HOME_MAIN, R.layout.layout_bottom_bar_uni);
        tabHelper.setItemTempView(KEY_HOME_MORE, R.layout.layout_bottom_bar_uni);
        initLiveTempView();
    }

    private void initLiveTempView() {
        View mNormalLiveTabView = liveBubbleContainer.findViewById(R.id.ll_tab_item_tv_label);
        mLiveTabTempView = new LiveTabTempView(getBaseContext());
        String lightColor = getTabTextColor(FrameConfigConst.FRAME_LIVE, FrameConfigConst.ColorStr.C_Black);
        String darkColor = getTabTextColor(FrameConfigConst.FRAME_LIVE_DARK, FrameConfigConst.ColorStr.C_White_0_7);
        mLiveTabTempView.bindMainTab(mNormalLiveTabView, lightColor, darkColor);
        tabHelper.setItemTempView(KEY_HOME_LIVE, (LiveTabTempView) mLiveTabTempView);
    }

    private String getTabTextColor(String businessKey, String defaultColor) {
        String resultColor = defaultColor;
        FrameConfig frameConfig = FrameConfigManager.INSTANCE.getFrameConfigByKey(businessKey);
        if (frameConfig != null) {
            TabFrameConfig tabFrameConfig = frameConfig.getTab();
            if (tabFrameConfig != null) {
                resultColor = tabFrameConfig.getTabTextColor();
            }
        }
        if (com.immomo.momo.util.MomoKit.INSTANCE.isAppDarkMode()) {
            resultColor = "255, 255, 255, 1";
        }
        return resultColor;
    }

    public View getBottomContaiver() {
        return bottomContaiver;
    }

    private void registerReceiver() {
        mBroadcastProcessor = new MainBroadcastProcessor(this);
        mBroadcastProcessor.registerReceivers(this);
    }

    private void registerSystemReceiver() {
        mSystemBroadcastProcessor = new SystemBroadcastProcessor();
        mSystemBroadcastProcessor.registerReceivers(this);
    }

    private void unregisterReceiver() {
        if (mBroadcastProcessor != null) {
            mBroadcastProcessor.unregReceivers(this);
        }
        if (mSystemBroadcastProcessor != null) {
            mSystemBroadcastProcessor.unregReceivers(this);
        }
        if (refreshMultiAccountRedPointReceiver != null) {
            unregisterReceiver(refreshMultiAccountRedPointReceiver);
            refreshMultiAccountRedPointReceiver = null;
        }
    }

    @SuppressLint("CheckResult")
    private void gotoLogin(boolean isRestore) {
        if (isRestore) {
            return;
        }

        if (isFirstCreate) { //打点
            UploadGrowLog.getInstance().upload(LoginAbConfigConstant.KEY_SOURCE_WELCOME, "");
            isFirstCreate = false;
        }

        if (isFromLogout) { //退出登录后过来的跳转逻辑
            if (AccountKit.getAccountManager().getAccountList() == null || AccountKit.getAccountManager().getAccountList().isEmpty()) {
                GuestBlockHelper.handleGuestPhoneLogin(this, AppUseConstants.LOGIN_SOURCE_MULTI);
                isGotoLoginPage = true;
                return;
            }

            boolean gotoPhoneLogin = false;
            for (AccountUser tAccountUser : AccountKit.getAccountManager().getAccountList()) {
                if (tAccountUser != null && (tAccountUser.getLoginType() == AccountUser.THIRD_PARTY_LOGIN || tAccountUser.getLoginType() == AccountUser.PHONE_LOGIN)) {
                    gotoPhoneLogin = true;
                    break;
                }
            }
            if (gotoPhoneLogin) {
                GuestBlockHelper.handleGuestPhoneLogin(this, AppUseConstants.LOGIN_SOURCE_MULTI);
            } else {
                GuestBlockHelper.handleGuestAccountLogin(this, AppUseConstants.LOGIN_SOURCE_MULTI);
            }
            isGotoLoginPage = true;
            return;
        }
        //32bit机型，等待稍微加长一点
        int maxTimeout = (int) ((MomoRuntime.is64Bit() ? 1 : 1.2) * ABConfigManager.getInstance().openOppoKeyChainLoginTimeout());
        oppoKeyChainDisposable = OppoKeyChainTool.tryLoginViaKeyChain(this)
                .timeout(maxTimeout, TimeUnit.MILLISECONDS)
                .observeOn(MMThreadExecutors.INSTANCE.getMain().getScheduler())
                .subscribe(
                        aBoolean -> {
                            if (!aBoolean) {
                                processNormalLogin(false);
                            }
                        },
                        err -> {
                            processNormalLogin(false);
                        });
    }

    private void processNormalLogin(boolean isFirstLoad) {
        // 第一次打开app的跳转逻辑
        if (!KV.getSysBool(LoginRouterImpl.GUEST_LOGIN_HAS_SHOW, false)) {
            startLoginWithNumberCheck(true);
            return;
        }

        isGotoLoginPage =
                GuestBlockHelper.gotoWithoutPwdLoginOrQuickLogin(
                        this, AppUseConstants.LOGIN_SOURCE_AUTO, true);

        // 兜底检查启动
        startLoginWithNumberCheck(isFirstLoad);
    }

    private void startLoginWithNumberCheck(boolean isFirstLoad) {
        if (isGotoLoginPage || LoginLuaActivity.Companion.isStartedLogin().get()) return;
        MDLog.i(QuickLoginHelper.TAG, "isFirstLoad=" + isFirstLoad);
        OneKeyLoginConfig oneKeyConfig = OneKeyLoginConfig.Companion.getOneKeyConfig();
        if (isFirstLoad) {
            if (oneKeyConfig.getLoginDelayEnable()) {
                if (MomoKit.isIntlBuild()) {
                    gotoLoginPage();
                } else {
                    gotoLoginWithDelay(true, oneKeyConfig);
                }
            } else {
                gotoLoginPage();
            }
        } else {
            if (oneKeyConfig.isAutoLoginPage()) {
                if (oneKeyConfig.getLoginDelayEnable()) {
                    if (MomoKit.isIntlBuild()) {
                        gotoLoginPage();
                    } else {
                        gotoLoginWithDelay(true, oneKeyConfig);
                    }
                } else {
                    gotoLoginPage();
                }
            }
        }
    }

    private void gotoLoginWithDelay(boolean isFirstLoad, OneKeyLoginConfig oneKeyConfig) {
        QuickLoginHelper.INSTANCE.checkOneKeyNumberRequest(isFirstLoad, oneKeyConfig,
                this::gotoLoginPage, hashCode(), thisActivity());
        isGotoLoginPage = true;
    }

    private void gotoLoginPage() {
        if (LoginLuaActivity.Companion.isStartedLogin().get()) return;
        GuestBlockHelper.handleGuestPhoneLogin(thisActivity(), AppUseConstants.LOGIN_SOURCE_AUTO);
        isGotoLoginPage = true;
        PermissionUtil.getInstance().check13Notification(this);
    }

    /**
     * momo7.0
     * 一旦用户升级到 7.0 版本，自动将直播招呼提醒打开（接收直播招呼提醒）
     */
    private void initPushLive() {
        final String KEY_NOTIFY_LIVE_HI = "is_notify_live_hi";
        final boolean openLiveHi = KV.getUserBool(KEY_NOTIFY_LIVE_HI, false);
        if (!openLiveHi && AppKit.getAccountManager().isOnline()) {
            MomoTaskExecutor.executeTask(MomoTaskExecutor.EXECUTOR_TYPE_USER, getTaskTag(), new MomoTaskExecutor.Task() {
                @Override
                protected Object executeTask(Object[] params) throws Exception {
                    MiPushApi.getInstance().pushLive(false);
                    return null;
                }

                @Override
                protected void onTaskSuccess(Object result) {
                    KV.saveUserValue(KEY_NOTIFY_LIVE_HI, true);
                }
            });
        }
    }

    private int getLayoutRes() {
        return R.layout.activity_maintabs;
    }

    @Override
    public void onFragmentInstantiated(BaseTabOptionFragment fragment) {
    }

    private void initDragBubbleView() {
        dragBubbleView = (DragBubbleView) findViewById(R.id.dragView);
        //只有访客或者不需要设置主界面状态栏时，才设置一个 headerBarHeight
        if (!AppKit.getAccountManager().isOnline() || !StatusBarUtil.shouldSetStatusBar()) {
            dragBubbleView.initHeaderBar(StatusBarUtil.getStatusBarHeight(thisActivity()));
        }

        tabHelper.getItemMap().forEach((key, value) -> {
            if (FrameConfigManager.INSTANCE.containsFrameInHome(key, FRAME_CHAT)) {
                View layoutBubbleText = value.getRedTextLayout();
                final View sessionBubble = value.getViewRedText();

                layoutBubbleText.setOnTouchListener(new View.OnTouchListener() {
                    long clickTime = 0L;

                    @Override
                    public boolean onTouch(View v, MotionEvent event) {
                        singleClickMsgTab(event);
                        if (dragBubbleView != null) {
                            if (getCurrentFragment() instanceof IHomeMainView
                                    && FrameConfigManager.INSTANCE.containsFrameInHome(((IHomeMainView) getCurrentFragment()).getHomeKey(), FRAME_CHAT)) {
                                dragBubbleView.setDragFromType(((IHomeMainView) getCurrentFragment()).getHomeKey());
                                return dragBubbleView.handoverTouch(sessionBubble, event);
                            }
                        }
                        return false;
                    }

                    //单击消息tab
                    private void singleClickMsgTab(MotionEvent event) {
                        if (event.getAction() == MotionEvent.ACTION_DOWN) {
                            clickTime = System.currentTimeMillis();
                        } else if (event.getAction() == MotionEvent.ACTION_UP
                                || event.getAction() == MotionEvent.ACTION_CANCEL) {
                            if (System.currentTimeMillis() - clickTime < 300) {
                                clickTime = 0L;
                                if (getCurrentFragment() instanceof IHomeMainView) {
                                    ((IHomeMainView) getCurrentFragment()).onSingleTabClick();
                                }
                            }
                        }
                    }
                });
            }
        });
        dragBubbleView.setOnFinishListener((tag, view) -> {
            if (KEY_HOME_CHAT.equals(tag) || KEY_HOME_TEMP.equals(tag) || KEY_HOME_MAIN.equals(tag)) {
                Intent intent = new Intent(ReflushSessionUnreadReceiver.ReflushAll);
                LocalBroadcastManager.getInstance(thisActivity()).sendBroadcast(intent);
                ClickEvent.create().page(EVPage.Msg.Chatlist)
                        .action(EVAction.Nav.RedAllclean)
                        .putExtra("msg_num", "" +
                                MainBubbleHelper.INSTANCE.getCount(FrameConfigManager.INSTANCE.getHomeKeyByBusinessKey(FrameConfigConst.FRAME_CHAT)))
                        .submit();

                //遍历包含的business
                Intent clearBusiIntent = new Intent(ReflushBusiUnreadReceiver.Companion.getClearAll());
                clearBusiIntent.putExtra(ReflushBusiUnreadReceiver.Companion.getKEY_HOME(), tag);
                LocalBroadcastManager.getInstance(thisActivity()).sendBroadcast(clearBusiIntent);
            }
        });
    }

    private void startInitializerService(Bundle savedInstanceState) {
        if (null == savedInstanceState) {
            try {
                startService(new Intent(getApplicationContext(), Initializer.class));
            } catch (Throwable e) {
                FabricLogger.logException(e);
            }
        } else {
            //不是冷启动
        }
    }

    private void checkSplashAndInitFragments(Bundle savedInstanceState) {
        boolean isFromSaveInstance = false;
        if (savedInstanceState != null) {
            isFromSaveInstance = savedInstanceState.getBoolean("from_save", false);
        }

        if (GuestConfig.getInstance().isGuestMode()) {
            showFragment(mCurrentIndex, false);
        } else if (isShowSplashWhenCreate(isFromSaveInstance)) {
            boolean hasSplash = mSplashView.showSplash(true);
            if (!hasSplash) {
                showGuideOrAlertContact(isFromSaveInstance);
            }
            initBubbleViews();
            initBusinessTabs();
            showFragment(mCurrentIndex);
        } else {
            showGuideOrAlertContact(isFromSaveInstance);
            initBubbleViews();
            initBusinessTabs();
            showFragment(mCurrentIndex);
        }
    }

    private void initBubbleViews() {
        if (isBubbleViewsInit) return;
        //首帧

        if (nearBubbleContainer != null) {
            MainBubbleHelper.INSTANCE.addBubbleView(nearBubbleContainer, FrameConfigConst.KEY_HOME_MAIN);
        }

        // 关注帧
        if (focusBubbleContainer != null) {
            MainBubbleHelper.INSTANCE.addBubbleView(focusBubbleContainer, KEY_HOME_TEMP);
        }

        //消息帧
        if (sessionListBubbleContainer != null) {
            MainBubbleHelper.INSTANCE.addBubbleView(sessionListBubbleContainer, KEY_HOME_CHAT);
        }

        // 更多帧
        if (profileBubbleContainer != null) {
            MainBubbleHelper.INSTANCE.addBubbleView(profileBubbleContainer, KEY_HOME_MORE);
        }

        //直播帧
        if (liveBubbleContainer != null) {
            MainBubbleHelper.INSTANCE.addBubbleView(liveBubbleContainer, KEY_HOME_LIVE);
        }

        MainBubbleHelper.INSTANCE.checkMainBubbles();
        isBubbleViewsInit = true;
    }

    /**
     * 关联业务所需的tab视图
     */
    private void initBusinessTabs() {
        MainTabBusinessHelper.INSTANCE.addTabView(getTaskTag().toString(), KEY_HOME_MAIN, nearBubbleContainer);
        MainTabBusinessHelper.INSTANCE.addTabView(getTaskTag().toString(), KEY_HOME_MORE, profileBubbleContainer);
    }

    /**
     * update 2016-8-12 dongTao
     * 正式版AccountService.getDefault().handleResult方法会返回true，导致无法进入
     * processUserGuideResult函数，
     */
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQUEST_USERGUID:
                mUserGuideAndContactProcessor.processUserGuideResult(data);
                break;
            case REQUEST_SHOW_SPLASH:
                if (resultCode == Activity.RESULT_OK && data != null) {
                    boolean isNeedShowContactGuide = data.getBooleanExtra(SplashActivity.KEY_NEED_SHOW_CONTACT, false);
                    if (isNeedShowContactGuide) {
                        showGuideOrAlertContact(false);
                    }
                }
                break;
            default:
                BaseFragment currentFragment = getCurrentFragment();
                if (currentFragment != null) {
                    currentFragment.onActivityResult(requestCode, resultCode, data);
                }
                break;
        }

        if (null != mListener) {
            mListener.onSelectFriendResult(requestCode, resultCode, data);
        }
    }

    private void showGuideOrAlertContact(boolean isFromSaveInstance) {
        // 渠道引导时不显示 通讯录、隐私模式、基因开屏引导
        if (isFromSaveInstance || RegisterChannelBusiness.INSTANCE.getShouldShowChannelGuideForColdBoot()) {
            return;
        }
        mUserGuideAndContactProcessor.showGuideOrAlertContact();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (intent.getBooleanExtra(MaintabActivity.KEY_NEED_RECREATE, false) || MaintabActivity.NEED_RECREATE) {
            MaintabActivity.NEED_RECREATE = false;
            try {
                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M) {
                    recreate();
                } else {
                    startActivity(intent);
                    finish();
                }
            } catch (Throwable thr) {
                MDLog.printErrStackTrace(LogTag.COMMON, thr);
            }
            return;
        }

        setIntent(intent);
        findTargetFragmentIndex(null);
        saveSyncChannelInfo();
        try {
            checkSplashAndInitFragments(null);
        } catch (Exception e) {
            FabricLogger.logException(e);
            Log4Android.getInstance().e(e);
        }

        if (PushHelper.isFromPush(intent)) {
            HiBoardMission.isFromPushClick = true;
        }
    }

    /**
     * back出去后，未杀死唤醒。
     */
    private void onActivityHomeResume() {
        String homekey = FrameConfigManager.INSTANCE.getHomeKeyByBusinessKey(FrameConfigConst.FRAME_UNIVERSE);
        if (StringUtils.isNotEmpty(homekey)) {
            MainBubbleHelper.INSTANCE.checkBubbles(homekey);
        } else {
            MainBubbleHelper.INSTANCE.checkProfileBubble();
        }
        asyncInitServiceAndTask();
        if (SplashAppConfigGetter.get().home2BackgroundStrategy() != 1) {
            mSplashView.showSplash(false);
        }
        findTargetFragmentIndex(null);
        showFragment(mCurrentIndex);
        MessageManager.postMessage(new Bundle(), MessageKeys.MyInfo.Key_update_profile);
        MessageManager.postMessage(new Bundle(), MessageKeys.NearbyPeople.Key_update_nearby_people);
        MessageManager.postMessage(new Bundle(), MessageKeys.SessionList.Action_Home_Resume);
    }

    @Override
    protected boolean blockTabClick(int index) {
        if (TeenModeHelper.get().isTeenMode()) {
            if (index != TAB_LIVE_MK) {
                Toaster.show("青少年模式下无法使用");
                return true;
            }
        } else if (index == TAB_LIVE_MK) {
            try {
                return TabGotoManager.checkConfigTabGoto(thisActivity());
            } catch (Exception e) {
                return super.blockTabClick(index);
            }
        }
        return super.blockTabClick(index);
    }

    @Override
    protected void onTabclick(int index) {
        if (mCurrentIndex == index) {
            return;

        }
        if (GuestConfig.getInstance().isGuestMode()) {
            switch (index) {
                case 0:
                    AutoSpeed.getInstance().markMainFrameSwitchStart(AutoSpeed.MAIN_FRAME_HOME_TAB);
                    break;
                case 1:
                    GuestBlockHelper.handleNewGuest(this, AppUseConstants.LOGIN_SOURCE_LIVE);
                    break;
                case 2:
                    GuestBlockHelper.handleNewGuest(this, AppUseConstants.LOGIN_SOURCE_SESSION);
                    break;
                case 3:
                    GuestBlockHelper.handleNewGuest(this, AppUseConstants.LOGIN_SOURCE_FOLLOW);
                    break;
                case 4:
                    GuestBlockHelper.handleNewGuest(this, AppUseConstants.LOGIN_SOURCE_MORE);
                    break;
            }
            return;
        }
        switch (index) {
            case 0:
                AutoSpeed.getInstance().markMainFrameSwitchStart(AutoSpeed.MAIN_FRAME_HOME_TAB);
                break;
            case 1:
                AutoSpeed.getInstance().markMainFrameSwitchStart(AutoSpeed.MAIN_FRAME_LIVE_TAB);
                break;
            case 2:
                AutoSpeed.getInstance().markMainFrameSwitchStart(AutoSpeed.MAIN_FRAME_SESSION_TAB);
                break;
            case 3:
                AutoSpeed.getInstance().markMainFrameSwitchStart(AutoSpeed.MAIN_FRAME_FOLLOW_TAB);
                break;
            case 4:
                AutoSpeed.getInstance().markMainFrameSwitchStart(AutoSpeed.MAIN_FRAME_MY_INFO_TAB);
                break;
        }

        //mCurrentIndex切换tab前的下标，index切换tab后的下标,切换tab前的fragment的回调
        if (mCurrentIndex != index) {
            BaseFragment oldFragment = getCurrentFragment();
            if (oldFragment instanceof BaseGroupFragment) {
                BaseFragment childFragment = ((BaseGroupFragment) oldFragment).getCurrentFragment();
                if (childFragment instanceof IBusinessViewForScroll) {
                    ((IBusinessViewForScroll) childFragment).onFragmentUnSelectedByBottomTab();
                }
            }
        }

        String clickTabLogStr = MainBubbleHelper.INSTANCE.getLogType(index);
        int typeLogNum = MainBubbleHelper.INSTANCE.getLogTypeNum(index);
        if (StringUtils.isNotEmpty(clickTabLogStr)) {
            if (HomeUniverseGuideUtils.INSTANCE.showTabUniverse() && index == 3) {
                //TODO 跟产品确认是否还需要这个埋点 产品李粒诚
                UniverseFeedLuaFragment.Companion.sendSourcepageEvent(UniverseFeedLuaFragment.SOURCE_PAGE_TAB);
                EVLog.create(IUniverseLog.class).clickBottomTabUniverse("universe", String.valueOf(typeLogNum));
            } else {
                ClickEvent.create()
                        .page(EVPage.Other.HomePage)
                        .action(EVAction.Nav.Goto)
                        .putExtra("theme", clickTabLogStr)
                        .putExtra("type_num", typeLogNum)
                        .submit();
            }
        }
    }

    private boolean isShowSplashWhenCreate(boolean isFromSaveInstance) {
        if (isFromSaveInstance) {
            return mSplashView.hasValidSplash();
        } else {
            return !needSkipSplashAd() && getBackByGotoShowSplash(getIntent());
        }
    }

    /*
     * 6.2 通过goto回maintab是否需要播放广告
     * */
    private boolean getBackByGotoShowSplash(Intent intent) {
        return intent.getBooleanExtra(AppUseConstants.KEY_GOTO_MAINTAB_SHOW_SPLASH, true);
    }

    private boolean needSkipSplashAd() {
        Intent fromIntent = getIntent();
        if (fromIntent != null && fromIntent.getBooleanExtra(KEY_CALL_FROM_SDK, false)) {
            fromIntent.putExtra(KEY_CALL_FROM_SDK, false);
            return true;
        }
        return false;
    }

    private void findTargetFragmentIndex(Bundle savedInstanceState) {
        findTargetFragmentIndex(savedInstanceState, false);
    }

    private void findTargetFragmentIndex(Bundle savedInstanceState, boolean isFromGreate) {
        mCurrentIndex = getGoingIndex(getIntent());

        String sonGoingBus = getIntent() != null ? getIntent().getStringExtra(KEY_SON_BUSINESS) : null;
        if (TeenModeHelper.get().isTeenMode()) {
            mCurrentIndex = TAB_LIVE_MK;
        } else if (!TextUtils.isEmpty(sonGoingBus)) {
            String homeKey = FrameConfigManager.INSTANCE.getHomeKeyByBusinessKey(sonGoingBus);
            if (homeKey != null) {
                int index = FrameConfigManager.INSTANCE.getContainerIndexByFrame(homeKey);
                if (index >= 0) {
                    mCurrentIndex = index;
                }
            }
        } else if (savedInstanceState != null) {
            mCurrentIndex = savedInstanceState.getInt(KEY_TABINDEX, mCurrentIndex);
            NotificationSourceHelper.setSavedInstanceBusinessKey(savedInstanceState.getString(KEY_SAVE_INSTANCE_SON_BUSINESS));
        } else if (isFromGreate && StringUtils.isEmpty(NotificationSourceHelper.getSource())) { // 处理首帧配置跳转, 优先级高于每一帧里的默认帧配置, 低于push、goto跳转帧
            int goingIndex = getGoingIndexStartApp();
            if (goingIndex >= 0) {
                mCurrentIndex = goingIndex;
            }
        }

        // 修复 https://www.fabric.io/momo6/android/apps/com.immomo.momo/issues/5b13ae4a6007d59fcdc1caef?time=last-seven-days
        if (GuestConfig.getInstance().isGuestMode()) {
            mCurrentIndex = TAB_FEED; //访客模式默认第一帧
        }

        updateBottomBarIcon(mHomeKeyIndices.get(mCurrentIndex));
    }

    private int getGoingIndexStartApp() {
        int startIndex = FrameConfigManager.INSTANCE.getColdStartJump(mHomeKeyIndices);
        if (startIndex >= 0 &&
                checkUseDefaultBusiness(FrameConfigManager.INSTANCE.getStartConfig() != null ? FrameConfigManager.INSTANCE.getStartConfig().getMarkTime() : 0L)) {
            Intent intent = getIntent();
            if (intent == null) {
                intent = new Intent();
            }
            intent.putExtra(KEY_BUSINESS_START_APP,
                    FrameConfigManager.INSTANCE.getStartConfig() != null ? FrameConfigManager.INSTANCE.getStartConfig().getBusiness() : "");
            notifySonTab(intent);
            return startIndex;
        }
        return -1;
    }

    private boolean checkUseDefaultBusiness(long markTime) {
        try {
            long lastDate = KV.getUserLong(FrameConfigConst.KEY_FRAME_START_CONFIG_LAST_TIME, 0L);
            if (markTime != lastDate) {
                KV.saveUserValue(FrameConfigConst.KEY_FRAME_START_CONFIG_LAST_TIME, markTime);
                return true;
            } else {
                return false;
            }
        } catch (Throwable t) {
            MDLog.printErrStackTrace(TAG, t);
        }
        return false;
    }

    public void updateBottomBarIcon(String homeKey) {
        TabFrameConfig selectConfig = null;
        TabConfigModel defaultConfig = null;
        if (StringUtils.equalsNonNull(homeKey, KEY_HOME_LIVE)) {
            // 直播
            selectConfig = FrameConfigManager.INSTANCE.getTabConfig(currentLiveBusiness);
            defaultConfig = FrameConfigManager.INSTANCE.getOtherTabConfig(currentLiveBusiness);
        } else if (StringUtils.equalsNonNull(homeKey, KEY_HOME_MORE)) {
            //更多
            selectConfig = FrameConfigManager.INSTANCE.getTabConfig(FrameConfigConst.FRAME_MORE);
            defaultConfig = FrameConfigManager.INSTANCE.getOtherTabConfig(FrameConfigConst.FRAME_MORE);
        } else if (getCurrentFragment() instanceof IHomeMainView) {
            homeKey = ((IHomeMainView) getCurrentFragment()).getHomeKey();
            if (((IHomeMainView) getCurrentFragment()).getHomeCurrentFragment() instanceof IBusinessView) {
                String businessKey = ((IBusinessView) ((IHomeMainView) getCurrentFragment()).getHomeCurrentFragment()).getFrameKey();
                selectConfig = FrameConfigManager.INSTANCE.getTabConfig(businessKey);
                defaultConfig = FrameConfigManager.INSTANCE.getOtherTabConfig(businessKey);
            }
        }
        String finalHomeKey = homeKey;
        TabFrameConfig finalSelectConfig = selectConfig;
        TabConfigModel finalDefaultConfig = defaultConfig;
        MomoMainThreadExecutor.post(() -> updateBottomFromConfig(finalHomeKey, finalSelectConfig, finalDefaultConfig));
    }

    public void updateBottomBarIcon(String homeKey, String frameKey) {
        TabFrameConfig selectConfig = null;
        TabConfigModel defaultConfig = null;
        if (StringUtils.equalsNonNull(homeKey, KEY_HOME_LIVE)) {
            // 直播
            selectConfig = FrameConfigManager.INSTANCE.getTabConfig(currentLiveBusiness);
            defaultConfig = FrameConfigManager.INSTANCE.getOtherTabConfig(currentLiveBusiness);
        } else if (StringUtils.equalsNonNull(homeKey, KEY_HOME_MORE)) {
            //更多
            selectConfig = FrameConfigManager.INSTANCE.getTabConfig(FrameConfigConst.FRAME_MORE);
            defaultConfig = FrameConfigManager.INSTANCE.getOtherTabConfig(FrameConfigConst.FRAME_MORE);
        } else if (getCurrentFragment() instanceof IHomeMainView) {
            homeKey = ((IHomeMainView) getCurrentFragment()).getHomeKey();
            if (StringUtils.isNotEmpty(frameKey)) {
                selectConfig = FrameConfigManager.INSTANCE.getTabConfig(frameKey);
                defaultConfig = FrameConfigManager.INSTANCE.getOtherTabConfig(frameKey);
            }
        }
        String finalHomeKey = homeKey;
        TabFrameConfig finalSelectConfig = selectConfig;
        TabConfigModel finalDefaultConfig = defaultConfig;
        MomoMainThreadExecutor.post(() -> updateBottomFromConfig(finalHomeKey, finalSelectConfig, finalDefaultConfig));
    }

    private void updateBottomFromConfig(String selectedHomeKey, TabFrameConfig selectConfig, TabConfigModel defaultConfig) {
        if (StringUtils.isNotEmpty(selectedHomeKey)) {
            if (selectConfig != null && defaultConfig != null) {
                tabHelper.updateTab(selectedHomeKey, selectConfig, defaultConfig);
            }
            tabHelper.updateTabText();
        }

        updateBusinessBottomTabImg(selectedHomeKey, defaultConfig);
    }

    private void updateBusinessBottomTabImg(String selectedHomeKey, TabConfigModel defaultConfig) {
        if (KV.getUserInt(AppConfigV2.SPKeys.KEY_LIVE_TAB_SWITCH, 0) == 1) {
            TabItemView view = tabHelper.getTabItemView(KEY_HOME_LIVE);
            if (view != null) {
                view.updateImage(R.drawable.ic_tab_live_event_new);
            }
        }
    }

    private void saveRecommendId(Intent intent) {
        if (GuestConfig.getInstance().isGuestMode()) {
            return;
        }
        if (intent != null) {
            if (intent.getIntExtra(tab, 0) == PushGotoProcessor.NEARBYFEED_TAB) {
                FeedRecommendHelper.INSTANCE.saveNearbyFeedRecommendId(intent.getStringExtra(FeedRecommendHelper.nearby_feed_recommend));
            } else if (intent.getIntExtra(tab, 0) == PushGotoProcessor.NEARBYPEOPLE_TAB) {
                PushGotoProcessor.getInstance().saveNearbyPeopleRecommendId(intent.getStringExtra(PushGotoProcessor.nearby_people_recommend));
            } else if (intent.getIntExtra(tab, 0) == PushGotoProcessor.FRIEND_FEED_TAB) {
                FeedRecommendHelper.INSTANCE.saveFriendFeedRecommendId(intent.getStringExtra(friend_feed_recommend));
            }
            GreetHelper.setFromGreetRemindPush(intent.getBooleanExtra(KEY_PUSH_GREET_REMIND, false));
            intent.removeExtra(KEY_PUSH_GREET_REMIND);

            notifySonTab(intent);
        }
    }

    private void notifySonTab(Intent intent) {
        if (intent != null) {
            if (!StringUtils.isEmpty(intent.getStringExtra(KEY_SOURCE))) {
                String sonTabKey = intent.getStringExtra(KEY_SON_TABKEY);
                String sonBusiness = intent.getStringExtra(KEY_SON_BUSINESS);
                PushGotoparams pushGotoparams = new PushGotoparams(intent.getIntExtra(hideen_hometop, 0) == 1,
                        intent.getIntExtra(KEY_SON_TABINDEX, 0), StringUtils.notEmpty(sonTabKey) ? sonTabKey : "", sonBusiness);
                Bundle bundle = new Bundle();
                bundle.putString(KEY_GOTO_PARAMS_STR, intent.getStringExtra(KEY_GOTO_PARAMS_STR));
                pushGotoparams.setBundle(bundle);

                NotificationSourceHelper.setNotificationSource(intent.getStringExtra(KEY_SOURCE));
                NotificationSourceHelper.setPushGotoparams(pushGotoparams);
                NotificationSourceHelper.setNotificationWorldSource(intent.getStringExtra(KEY_GOTO_WORLD_TAB));
                intent.putExtra(KEY_SOURCE, "");
            }
            NotificationSourceHelper.setStartAppBusiness(intent.getStringExtra(KEY_BUSINESS_START_APP));
        }

    }

    /**
     * 异步初始化一些service和task，能够在异步完成初始化的尽量放入异步，提高启动速度和UI流畅性
     */
    private void asyncInitServiceAndTask() {
        ThreadUtils.execute(ThreadUtils.TYPE_INNER, new Runnable() {
            @Override
            public void run() {
                initLocalService();
                boolean emptyKeyInfo = (null == APIKeyholder.getInstance().getCurrentKeyInfo());
                if (EncManager.isOpenEncVersion() && emptyKeyInfo) {
                    MomoTaskExecutor.executeTask(MomoTaskExecutor.EXECUTOR_TYPE_INNER, getTaskTag(), new ExchangeKeyTask(new ExchangeKeyTask.TaskFinishCallback() {
                        @Override
                        public void onTaskFinish() {
                            initAsyncTasks();
                        }
                    }));
                } else {
                    initAsyncTasks();
                }
            }
        });
    }

    private void initAsyncTasks() {
        mInitTaskProcessor.startMainAsyncTasks(this);
    }

    private void initLocalService() {
        MomoKit.getApp().asyncLoginIMService();
        Bootstrap.initAlarmService(MomoKit.getContext(), "alarm_receiver");

        if (TestItemsGenerator.debugIsReceiveTestMsgAfterLogin) {
            TestMsgGenerator.getInstance().sendMsgAfterLogin();
        }
    }

    @Override
    protected void onResume() {

        if (Debugger.PUBLIC_TEST) {
            CrashLogger.log("MaintabActivity onResume");
        }
        if (GuestConfig.getInstance().isGuestMode()) {
            if (Debugger.PUBLIC_TEST) {
                CrashLogger.log("MaintabActivity onResume isGuestMode");
            }
            onGuestResume();
            onGuestModeObtainOAID();
        } else {
            // 应用在后台且创建Maintab时是访客模式，这时在其它app进行授权操作(登录/注册成功)后
            // 访客状态会更新为用户登录态，但界面是onGuestCreate()创建的，会有问题，需要重新创建
            if (isInGuestModel) {
                try {
                    if (Debugger.PUBLIC_TEST) {
                        CrashLogger.log("MaintabActivity isInGuestModel");
                    }
                    super.onResume();
                    if (Debugger.PUBLIC_TEST) {
                        CrashLogger.log("MaintabActivity isInGuestModel onResume");
                    }
                    isInGuestModel = false;
                    recreate();
                    return;
                } catch (Throwable thr) {
                    MDLog.printErrStackTrace(LogTag.COMMON, thr);
                }
            }
            onNormalResume();
        }
        isFirstResume = false;
        if (null != mListener) {
            mListener.onResume();
        }
        // Rifle.putUserKeyValue("MainTabShow", 1);

        AppEnterStackHelper.get().onOtherPagerResume(this);

        tipsPresenter.checkMyInfoNewTip(mCurrentIndex);
        MainTabBusinessHelper.INSTANCE.onResume();
        changeUniverseFloatViewStyle(MainTabBusinessHelper.INSTANCE.getCurDarkTheme());
        try {
            currentFragmentPage = getCurrentFragment() != null ? getCurrentFragment().getBehavior() : null;
            if (currentFragmentPage != null) {
                PageAnalysis.setFrom(currentFragmentPage);
            }
        } catch (Exception ignore) {
        }
    }

    private void onGuestResume() {
        if (Debugger.PUBLIC_TEST) {
            CrashLogger.log("MaintabActivity onGuestResume");
        }
        super.onResume();
        if (Debugger.PUBLIC_TEST) {
            CrashLogger.log("MaintabActivity onGuestResume onResume");
        }

        if (getCurrentFragment() == null) {
            showFragment(currentIndex);
        }
        isGotoLoginPage = false;
    }

    private void onNormalResume() {
        StepMonitor.clearRestoredStepChain();
        try {
            //FIX http://crashes.to/s/61a76753214
            //奇葩Crash，看堆栈是 AMS 找不到，try catch 住
            if (Debugger.PUBLIC_TEST) {
                CrashLogger.log("MaintabActivity onNormalResume");
            }
            super.onResume();
            if (Debugger.PUBLIC_TEST) {
                CrashLogger.log("MaintabActivity onNormalResume onResume");
            }
        } catch (Throwable thr) {
            CrashLogger.log("MaintabActivity onNormalResume onResume Crash info:" + Log.getStackTraceString(thr));
            MDLog.printErrStackTrace(LogTag.COMMON, thr);
        }
        showNormalHomePage();
        AppStartHelper.INSTANCE.getAppStartConfigAsync(false);

        if (getCurrentFragment() == null) {
            showFragment(currentIndex);
        }
        if (mBackPressToBackground) {
            onActivityHomeResume();
            mBackPressToBackground = false;
        } else if (!isFirstResume) {
            mSplashView.showSplashInterval();
        }
        if (!isFirstResume) {
            MomoKit.getApp().asyncWatchIMService();
            changeTeenMode();
        }
        NearbyLiveGuideTipHelper.showTip(this);

        //在首帧且不需要显示开屏引导时，出现权限弹框
        if (!RegisterChannelBusiness.INSTANCE.getShouldShowChannelGuide()
                && !isGotoLoginPage
                && OpenScreenGuideUtil.isRequestPermissionOnMaintab()
                && currentIndex == 0
                && isFirstResume
        ) {
        } else {
            onlineObtainOAIDPermission();
        }
        isGotoLoginPage = false;

        String message = isShowAvatarCheckDialog();
        if (!TextUtils.isEmpty(message)) {
            Intent intent = new Intent(this, AvatarCheckActivity.class);
            intent.putExtra(AvatarCheckActivity.SHOW_CONTENT, message);
            startActivity(intent);
        }

        RegisterChannelBusiness.INSTANCE.requestRegisterChannelGoto();
        RecommendRedStarGotoImpl.Companion.checkGotoRecommendRedStarActivity();
        if (RegisterChannelBusiness.INSTANCE.getShouldShowChannelGuide() && !RegisterChannelBusiness.INSTANCE.isShowedChannelGuide()) {
            RegisterChannelBusiness.INSTANCE.setShowedChannelGuide(true);
            GotoDispatcher.action(RegisterChannelBusiness.INSTANCE.getChannelGuideGoto(), this).execute();
        }

        updateBottomBarView(currentIndex, getCurrentFragment());
    }

    // 切换tab时判断是否直播精选，切换tab时getCurrentFragment可能还没更新，用config准确
    private boolean isLiveDark(@NonNull FrameConfig config) {
        return (config != null
                && StringUtils.equalsNonNull(FrameConfigManager.INSTANCE.getHomeKeyByBusinessKey(config.getBusiness()), FrameConfigConst.KEY_HOME_LIVE))
                && TextUtils.equals(FrameConfigConst.FRAME_LIVE_DARK, currentLiveBusiness); // 是否是精选帧
    }

    //拿不到config时 例如resume，可用getCurrentFragment
    private boolean isLiveDark(Fragment fragment) {
        return fragment instanceof LiveHomeFragment && TextUtils.equals(FrameConfigConst.FRAME_LIVE_DARK, currentLiveBusiness); // 是否是精选帧
    }

    private Page currentFragmentPage = null;

    @Override
    protected void onPause() {
        super.onPause();
        FRONT_PAGE_TILE_VISIBLE = false;
        if (null != mListener) {
            mListener.onPause();
        }
        currentFragmentPage = PageAnalysis.getFrom();
        MainTabBusinessHelper.INSTANCE.onStop(getTaskTag().toString());
    }

    @Override
    public void onBackPressed() {
        if (currentFragment != null && currentFragment.isCreated()) {
            if (currentFragment.onBackPressed()) {
                return;
            }
        }
        setMoveTaskToBack();
    }

    private void setMoveTaskToBack() {
        setIntent(null);
        try {
            moveTaskToBack(true);
            mCurrentIndex = 0;
            updateBottomBarIcon((mHomeKeyIndices.get(mCurrentIndex)));
            mBackPressToBackground = true;

            UserStateHelperKt.saveUserState();
            //            MThreadUtilsKt.whenNeedClean();
        } catch (Throwable e) {
            super.onBackPressed();
        }
    }

    @Override
    protected TabInfo[] getTabs() {
        //如果使用的是MK直播帧,则需要替换掉Tab的类
        boolean liveWithMK = KV.getUserBool(SPKeys.User.InitTask.KEY_LIVE_WITH_MK, false);
        Log4Android.getInstance().d("tang------是否使用MK作为直播帧 " + liveWithMK);
        return mainTabs;
    }

    private int getGoingIndex(Intent intent) {
        if (intent != null) {
            int goIndex = intent.getIntExtra(KEY_TABINDEX, currentIndex);
            saveRecommendId(getIntent());

            PushHelper.logLocalPushClick(intent, MaintabActivity.class);

            if (DataProtectManager.isShowing) {
                return mCurrentIndex;
            }

            String deepLinkAction = intent.getStringExtra(KEY_DEEPLINK_GOTO);
            if (StringUtils.notEmpty(deepLinkAction)) {
                String deepLinkBackUrl = intent.getStringExtra(GOTO_PARAMS_BACKURL);
                String deepLinkBtnName = intent.getStringExtra(GOTO_PARAMS_BTN_NAME);
                SchemeUtil.INSTANCE.executeDeepLinkGoto(deepLinkBackUrl, deepLinkBtnName, deepLinkAction, thisActivity());
                intent.putExtra(GOTO_PARAMS_BACKURL, "");
                intent.putExtra(GOTO_PARAMS_BTN_NAME, "");
                intent.putExtra(KEY_DEEPLINK_GOTO, "");
            } else {
                String gotoAction = intent.getStringExtra(KEY_GOTO);
                if (StringUtils.notEmpty(gotoAction) && HomeUniverseGuideUtils.INSTANCE.showTabUniverse()) {
                    GotoParserUtil.IGotoParser parser = GotoParserUtil.parse(gotoAction);
                    if (parser != null && parser.getAction().equals(GotoKeys.GOTO_USER_FEED)) {
                        goIndex = FrameConfigManager.INSTANCE.getContainerIndexByFrame(FrameConfigConst.FRAME_FOLLOW);
                        intent.putExtra(KEY_SON_BUSINESS, FrameConfigConst.FRAME_FOLLOW);
                    }
                }
                if (StringUtils.notEmpty(gotoAction) && !TeenModeHelper.get().isTeenMode()) {
                    MomoKit.getApp().removeGotoNotify();
                    EventBus.getDefault().post(new DataEvent<>(EventKeys.Push.ClickPushToMainTab, gotoAction));
                    ActivityHandler.executeAction(gotoAction, this);
                    intent.putExtra(KEY_GOTO, "");
                    intent.putExtra(KEY_TABINDEX, goIndex);
                }
            }

            boolean gotoNotice = intent.getBooleanExtra(KEY_GOTO_NOTICE, false);
            if (gotoNotice) {
                String luaUrl = InteractionNoticeHandler.Companion.luaUrl();
                InitData initData = MLSBundleUtils.createInitData(luaUrl);
                Intent luaIntent = new Intent(this, LuaViewActivity.class);
                luaIntent.putExtras(MLSBundleUtils.createBundle(initData));
                startActivity(luaIntent);
                intent.putExtra(KEY_GOTO_NOTICE, false);
            }

            if (FriendQChatWorker.isReceivedRequest()) {
                FriendQChatActivity.startReceivedRequestActivity(FriendQChatConstants.getChatType());
            }

            if (goIndex != currentIndex && goIndex < tabs.size() && goIndex >= 0) {
                return goIndex;
            }
        } else {
            NotificationSourceHelper.resetSource();
        }
        return mCurrentIndex;
    }

    /**
     * 保存通过deeplink 传递过来的渠道信息
     */
    private void saveSyncChannelInfo() {
        if (getIntent() != null && !StringUtils.isEmpty(getIntent().getStringExtra(ChannelSyncConstant.CHANNEL_ID))) {
            String chanleId = getIntent().getStringExtra(ChannelSyncConstant.CHANNEL_ID);
            String feedId = getIntent().getStringExtra(ChannelSyncConstant.CHANNEL_FEEDID);
            String remoteId = getIntent().getStringExtra(ChannelSyncConstant.CHANNEL_REMOTEID);
            SyncChannelBean syncChannelBean = new SyncChannelBean();
            syncChannelBean.setChannel(chanleId);
            syncChannelBean.setFeedId(feedId);
            syncChannelBean.setRemoteId(remoteId);
            SyncChannelHelper.getInstance().setSyncChannelBean(syncChannelBean);
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putInt(KEY_TABINDEX, mCurrentIndex);
        outState.putBoolean(KEY_NEED_GET_PROFILE, true);
        outState.putBoolean("from_save", true);
        if (getCurrentFragment() instanceof BaseGroupFragment && ((BaseGroupFragment) getCurrentFragment()).getCurrentFragment() instanceof IBusinessView) {
            outState.putString(KEY_SAVE_INSTANCE_SON_BUSINESS, ((IBusinessView) ((BaseGroupFragment) getCurrentFragment()).getCurrentFragment()).getFrameKey());
        }
    }

    @Override
    protected void onTabChanged(int index, BaseTabOptionFragment fragment) {
        super.onTabChanged(index, fragment);
        //        Log.i("NightModeHelper", "activity uiMode:" + this.getResources().getConfiguration().uiMode
        //                + "   app uiMode:" + AppCompatDelegate.getDefaultNightMode() + "\n"
        //                + "System uiMode:" + AppContext.getContext().getResources().getConfiguration().uiMode);

        MultiHelper.INSTANCE.requestMultiNotify(AccountKit.getAccountManager().getCurrentAccountUserId(), TASK_TAG);
        if (index != TAB_SETTING) {
            tipsPresenter.checkMyInfoNewTip(mCurrentIndex);
        }
        if (mCurrentIndex == TAB_MESSAGE && mCurrentIndex != index) {
            Intent intent = new Intent(SessionListReceiver.ActionChangeMainFragment);
            LocalBroadcastManager.getInstance(thisActivity()).sendBroadcast(intent);
        }
        if (mCurrentIndex != index && fragment != null && fragment instanceof LiveHomeFragment) {
            ((LiveHomeFragment) fragment).liveTabOnclick();
        }

        if (mCurrentIndex == TAB_FEED
                && mCurrentIndex != index
                && !(fragment instanceof HomePageFragment)) {
            Intent intent = new Intent(ACTION_HOME_TO_OTHER_BROADCAST);
            LocalBroadcastManager.getInstance(thisActivity()).sendBroadcast(intent);

        }

        if (mCurrentIndex != index && fragment != null && !(fragment instanceof LiveHomeFragment)) {
            AppAsm.getRouter(LiveMainTabChangeRouter.class).onLiveTabChanged();
        }

        if (index == TAB_MESSAGE && mCurrentIndex != index) {
            if (fragment instanceof SessionTabFragment && ((SessionTabFragment) fragment).getCurrentFragment() != null
                    && ((SessionTabFragment) fragment).getCurrentFragment() instanceof SessionListFragment) {
                ((SessionListFragment) ((SessionTabFragment) fragment).getCurrentFragment()).sessionTabOnclick();
            }
        }

        setupStatusBar();

        updateBottomBarView(index, fragment);

        mCurrentIndex = index;

        if (currentIndex == TAB_LIVE_MK) {
            if (KnockSettingConfigV2.Companion.isInNewKnockReflect()) {
                KnockBusinessManager.INSTANCE.removeKnockCardFromWindow(thisActivity(), true);
            } else {
                TopCardUtils.removeTopCard(thisActivity(), true);
            }
        } else {
            if (KnockSettingConfigV2.Companion.isInNewKnockReflect()) {
                KnockBusinessManager.INSTANCE.addKnockCardToWindow(thisActivity());
            } else {
                TopCardUtils.addTopCard(thisActivity());
            }
        }
    }

    private void updateBottomBarView(int index, Fragment fragment) {
        boolean dark;
        FrameConfig config = null;
        if (fragment instanceof LiveHomeFragment) {
            config = FrameConfigManager.INSTANCE.getFrameConfigByKey(currentLiveBusiness);
        } else if (fragment instanceof MyInfoLuaFragment) {
            config = FrameConfigManager.INSTANCE.getFrameConfigByKey(FrameConfigConst.FRAME_MORE);
        } else if (fragment instanceof BaseGroupFragment && ((BaseGroupFragment) fragment).getCurrentFragment() instanceof IBusinessView) {
            config = FrameConfigManager.INSTANCE.getFrameConfigByKey(((IBusinessView) ((BaseGroupFragment) fragment).getCurrentFragment()).getFrameKey());
        }
        if (config != null) {
            updateBottomBarTheme(config);
            dark = config.getStatusBarMode() == FrameConfigConst.BarThemeMode.DARK
                    || com.immomo.momo.util.MomoKit.INSTANCE.isAppDarkMode() || isLiveDark(config);
        } else {
            dark = com.immomo.momo.util.MomoKit.INSTANCE.isAppDarkMode() || isLiveDark(fragment);
            updateBottomBarTheme(dark, UIUtils.getColor(R.color.black));
        }
        updateBottomBarIcon(mHomeKeyIndices.get(index));
        updateBottomBusinessTab(dark, mHomeKeyIndices.get(index));
        changeUniverseFloatViewStyle(dark);
    }

    public void changeUniverseFloatViewStyle(Boolean isDark) {
        //小宇宙悬浮窗样式切换
        if (HomeUniverseGuideUtils.INSTANCE.universeTabCheck()) {
            AudioUtil.INSTANCE.setAudioDark(com.immomo.momo.util.MomoKit.INSTANCE.isAppDarkMode());
            PhonographManager.Companion.getManger().changeFloatViewStyle(com.immomo.momo.util.MomoKit.INSTANCE.isAppDarkMode());
        }
    }

    @Override
    protected void initStatusBar() {
        if (!StatusBarUtil.shouldSetStatusBar()) {
            return;
        }
        Window window = getWindow();
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);

        setWhiteStatusBar();
    }

    private void setWhiteStatusBar() {
        int color = getCustomStatusBarColor();
        if (StatusBarUtil.shouldSetStatusBar()) {
            getWindow().setStatusBarColor(color);
        }
        setStatusBarTheme(com.immomo.momo.util.MomoKit.INSTANCE.isDarkMode(this));
    }

    /**
     * 重写此方法，根据不同版本来设置状态栏颜色
     *
     * @return
     */
    @Override
    protected int getCustomStatusBarColor() {
        return UIUtils.getColor(R.color.transparent);
    }

    /**
     * Tab切换时，需要修改状态栏文字颜色
     */
    protected void setupStatusBar() {
        if (!StatusBarUtil.shouldSetStatusBar()) {
            return;
        }
        setWhiteStatusBar();
    }

    /**
     * WARNING!
     * DO NOT release or access global resources in {@link #onDestroy}
     * unless you know exactly what you are doing.
     */
    @Override
    protected void onDestroy() {
        EventBus.getDefault().unregister(this);
        MomoKit.getApp().isMainTabLaunched.decrementAndGet();
        unregisterReceiver();
        TipManager.unbindActivity(this);
        isFirstResume = false;
        if (mListener != null) {
            mListener.onDestory();
            mListener = null;
        }
        MomoMainThreadExecutor.cancelAllRunnables(TopCardUtils.TAG);
        MomoMainThreadExecutor.cancelAllRunnables(KnockBusinessManager.TAG);
        MomoMainThreadExecutor.cancelAllRunnables(hashCode());
        MomoTaskExecutor.cancleAllTasksByTag(TASK_TAG);
        MomoMainThreadExecutor.cancelAllRunnables(TAG_OPEN_FRAME);
        NewSayHiSessionFlowUtil.INSTANCE.onDestroy();
        LivePermissionCheckManager.getInstance().clearContextRef();
        AllFloatManagerExecute.exitAllFloatWindow();
        LikeSettingHelper.Companion.getInstance().clearLikeSettings();
        if (mLiveTabTempView != null) {
            mLiveTabTempView.onDestroy();
        }
        MainTabBusinessHelper.INSTANCE.release(getTaskTag().toString());
        tabHelper.onDestroy();
        hasShowStarView = false;
        GlobalEventManager.getInstance().unregister(subscriber, GlobalEventManager.EVN_NATIVE);
        isUniverseTabLoad = false;
        UserTestingService.INSTANCE.onDestroy();
        if (oppoKeyChainDisposable != null) {
            oppoKeyChainDisposable.dispose();
        }
        super.onDestroy();
    }

    @Override
    protected boolean isSupportSwipeBack() {
        return false;
    }

    @Override
    public void showBlockDialog(String avatar, String title, String text) {
        // 显示渠道引导时不显示拉黑处理弹框
        if (RegisterChannelBusiness.INSTANCE.getShouldShowChannelGuideForColdBoot()) {
            return;
        }
        BlockFeedbackAlertDialog dialog = new BlockFeedbackAlertDialog(thisActivity());
        dialog.updateData(avatar, title, text);
        showDialog(dialog);
    }

    @Override
    public void clearBubble() {
        Intent it = new Intent(SessionListReceiver.ActionIgnoreAllUnRead);
        LocalBroadcastManager.getInstance(thisActivity()).sendBroadcast(it);

        MainBubbleHelper.INSTANCE.clearBubbleView();
    }

    public void refreshAccountUnreadAvatar() {
        String currentMomoid = AppKit.getAccountManager().getCurrentAccountUserId();
        boolean hasUnread = false;
        if (MultiAccountConfig.INSTANCE.getPopRedPoint() > 0
                && (UniUnreadManager.INSTANCE.getTotalUnreadCount() <= 0
                || HomeUniverseGuideUtils.INSTANCE.universeHomeTabCheck())) {
            for (AccountUser user : AppKit.getAccountManager().getAccountList()) {
                if (user != null
                        && (user.hasLogin() || !TextUtils.isEmpty(user.getLoginWithoutPwdToken()))
                        && user.getUnReadMessage() == 1
                        && user.isReceiptNotification()
                        && MomoKit.getCurrentUser() != null
                        && !TextUtils.equals(user.getId(), currentMomoid)) {
                    TabItemView tabItemView = tabHelper.getTabItemView(FrameConfigConst.KEY_HOME_MORE);
                    if (tabItemView != null && tabItemView.getTempView().getVisibility() != View.VISIBLE) {
                        if (tabItemView.getItemImage().getVisibility() != View.VISIBLE) {
                            EVLog.create(VisitorBombEvent.class).onMultiAccountAvatarExposure(user.getId());
                        }
                        tabItemView.updateImage(user.getAvatar(), ImageType.ALBUM_96X96);
                    }
                    hasUnread = true;
                    break;
                }
            }
        }

        if (!hasUnread) {
            TabItemView tabItemView = tabHelper.getTabItemView(FrameConfigConst.KEY_HOME_MORE);
            if (tabItemView != null) {
                tabItemView.hideImage();
            }
        }

    }

    public void changeTeenMode() {
        if (TeenModeHelper.get().isTeenMode() && currentIndex != TAB_LIVE_MK) {
            showFragment(TAB_LIVE_MK);
        }
    }

    @Override
    public void onTrimMemory(int level) {
        switch (level) {
            case TRIM_MEMORY_COMPLETE:
                Log4Android.getInstance().i("MaintabActivity -> onTrimMemory : TRIM_MEMORY_COMPLETE");
            case TRIM_MEMORY_MODERATE:
                Log4Android.getInstance().i("MaintabActivity -> onTrimMemory : TRIM_MEMORY_MODERATE");
                boolean needgc = false;
                for (int tabIndex = 0; tabIndex < mainTabs.length; tabIndex++) {
                    if (tabIndex == FrameConfigManager.INSTANCE.getContainerIndexByFrame(FRAME_CHAT)) {
                        //消息帧不回收
                        continue;
                    } else if (tabIndex == currentIndex && isForeground()) {
                        //当前可见帧不回收
                        continue;
                    }
                    removeTab(tabIndex);
                    needgc = true;
                }
                if (needgc) {
                    System.gc();
                }
                break;
            case TRIM_MEMORY_BACKGROUND:
                Log4Android.getInstance().i("MaintabActivity -> onTrimMemory : TRIM_MEMORY_BACKGROUND");
                break;
            case TRIM_MEMORY_UI_HIDDEN:
                Log4Android.getInstance().i("MaintabActivity -> onTrimMemory : TRIM_MEMORY_UI_HIDDEN");
                break;
            case TRIM_MEMORY_RUNNING_CRITICAL:
                Log4Android.getInstance().i("MaintabActivity -> onTrimMemory : TRIM_MEMORY_RUNNING_CRITICAL");
                break;
            case TRIM_MEMORY_RUNNING_LOW:
                Log4Android.getInstance().i("MaintabActivity -> onTrimMemory : TRIM_MEMORY_RUNNING_LOW");
                break;
            case TRIM_MEMORY_RUNNING_MODERATE:
                Log4Android.getInstance().i("MaintabActivity -> onTrimMemory : TRIM_MEMORY_RUNNING_MODERATE");
                break;
        }
        super.onTrimMemory(level);
    }

    @NonNull
    @Override
    public LaunchEvent.Source getLaunchSource() {
        Intent intent = getIntent();
        if (intent == null || intent.getBooleanExtra(KEY_LAUNCH_LOGGED, false))
            return LaunchEvent.Source.Manual;

        if (PushHelper.isFromPush(intent)) return LaunchEvent.Source.Push;
        return LaunchEvent.Source.Manual;
    }

    @Override
    public Map<String, String> getLaunchExtra() {
        Intent intent = getIntent();
        if (intent == null || intent.getBooleanExtra(KEY_LAUNCH_LOGGED, false))
            return null;

        LaunchEvent.Source source = getLaunchSource();
        intent.putExtra(KEY_LAUNCH_LOGGED, true);
        switch (source) {
            case Push: {
                return PushHelper.getPushMap(intent);
            }
            case H5:
            case ThirdApp:
            default: {
                return null;
            }
        }
    }

    private String isShowAvatarCheckDialog() {
        String phoneMessage = AppAsm.getRouter(LoginRegisterRouter.class).getAvatarCheckPhoneDialogContent();
        boolean checkPhoneResult = !TextUtils.isEmpty(phoneMessage) && AppKit.getAccountManager().isOnline();
        if (checkPhoneResult) {
            return phoneMessage;
        }

        String thirdMessage = AppAsm.getRouter(LoginRegisterRouter.class).getAvatarCheckThirdDialogContent();
        boolean checkThirdResult = isFromThirdRegister && !TextUtils.isEmpty(thirdMessage) && AppKit.getAccountManager().isOnline();
        if (checkThirdResult) {
            return thirdMessage;
        }

        return null;
    }

    public int getmCurrentIndex() {
        return mCurrentIndex;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (getCurrentIndex() == TAB_FEED && getCurrentFragment() != null) {
            getCurrentFragment().onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
        mUserGuideAndContactProcessor.onRequestPermissionsResult(requestCode, permissions, grantResults);
        MgsPermissionHelper.callBackPermission(this, requestCode, grantResults);

        if (10001 == requestCode && PermissionUtil.getInstance().verifyPermissions(grantResults)) {
            GlobalEventManager.getInstance().sendEvent(
                    new GlobalEventManager.Event("Notification_nativeToLua_Universe_requestPermission")
                            .dst(GlobalEventManager.EVN_LUA).src(GlobalEventManager.EVN_NATIVE)
            );
        }
    }

    @Override
    public void onDebugReceiver(@Nullable Intent intent) {
        if (intent == null) {
            return;
        }

        String action = intent.getStringExtra("action");
        if (action == null) {
            return;
        }

        int index = intent.getIntExtra("index", 0);
        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
        switch (action) {
            case "showTab":
                showFragment(index);
                break;
            case "addTab":
                addTab(index, mainTabs[index]);
                break;
            case "removeTab":
                removeTab(index);
                break;
            case "trimMemory":
                int level = intent.getIntExtra("level", 5);
                onTrimMemory(level);
                break;
            default:
                break;
        }
        transaction.commitAllowingStateLoss();
    }

    /**
     * eventBus接受方法
     */
    @Subscribe
    public void onEvent(SimpleEvent event) {
        if (event.equals(com.immomo.momo.eventbus.EventKeys.Main.PRIVACY_GUIDE)) {
            if (mUserGuideAndContactProcessor != null) {
                mUserGuideAndContactProcessor.processPrivacyGuide();
            }
        } else if (event.equals(AppUseConstants.KEY_EVENT_NORMAL_LOGIN_PROCESS)) {
            processNormalLogin(true);
        } else if (event.equals(com.immomo.framework.SPKeys.System.KEY_EVENT_CHANGE_MODE)) {
            //            getDelegate().applyDayNight();
        }
    }

    @Subscribe
    public void onEvent(DataEvent<Object> event) {
        if (event.equals(KEY_EVENT_OPEN_FRAME)) {
            if (event.getData() instanceof String && StringUtils.isNotEmpty((String) event.getData())) {
                openFrame((String) event.getData());
            }
        }
    }

    private void createMicroVideoParseSource() {
        Observable.create(e -> {
                    try {
                        CommonFeedTheme.Companion.getMoshi().adapter(CommonFeedSource.class);
                    } catch (Exception ex) {
                        e.onError(ex);
                    }
                    e.onComplete();
                }).subscribeOn(Schedulers.from(MMThreadExecutors.INSTANCE.getUser()))
                .observeOn(MMThreadExecutors.INSTANCE.getMain().getScheduler())
                .subscribe(new Observer<Object>() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                    }

                    @Override
                    public void onNext(@NonNull Object o) {
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                    }

                    @Override
                    public void onComplete() {
                    }
                });
    }

    public String getShowingFrameKey() {
        if (getCurrentFragment() instanceof BaseGroupFragment
                && ((BaseGroupFragment) getCurrentFragment()).getCurrentFragment() instanceof IBusinessView) {
            return ((IBusinessView) ((BaseGroupFragment) getCurrentFragment()).getCurrentFragment()).getFrameKey();
        }
        return null;
    }

    public Fragment getShowingFragment() {
        if (getCurrentFragment() instanceof BaseGroupFragment) {
            return ((BaseGroupFragment) getCurrentFragment()).getCurrentFragment();
        }
        return null;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (getCurrentFragment() instanceof BaseGroupFragment
                && ((BaseGroupFragment) getCurrentFragment()).getCurrentFragment() instanceof IBusinessKeyEvent) {
            ((IBusinessKeyEvent) ((BaseGroupFragment) getCurrentFragment()).getCurrentFragment()).onKeyDown(keyCode, event);
        }
        return super.onKeyDown(keyCode, event);
    }

    private void universeChatRemarksName(String userId, String remark) {
        Observable.create(e -> {
                    UniUserService.INSTANCE.updateUserRemark(userId, remark);
                }).subscribeOn(Schedulers.from(MMThreadExecutors.INSTANCE.getUser()))
                .observeOn(MMThreadExecutors.INSTANCE.getMain().getScheduler())
                .subscribe(new Observer<Object>() {
                    @Override
                    public void onSubscribe(Disposable d) {
                    }

                    @Override
                    public void onNext(Object o) {
                    }

                    @Override
                    public void onError(Throwable e) {
                    }

                    @Override
                    public void onComplete() {
                    }
                });
        Bundle bundle = new Bundle();
        bundle.putString(SessionCons.Key.KEY_REMOTE_ID, userId);
        MessageManager.dispatchMessage(bundle, ReceiverCons.Session.UPDATE);

        Map<String, String> map = new HashMap<>();
        map.put("data", remark);
        map.put("uid", userId);
        EventBus.getDefault().post(new DataEvent<>(UniverseCons.Event.UNIVERSE_CHAT_REMARKS_NAME, map));
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        foldScreenUpdateContainerWidth();
        Map param = new HashMap();
        param.put("screenWidth", newConfig.screenWidthDp);
        param.put("screenHeight", newConfig.screenHeightDp);
        GlobalEventManager.getInstance().sendEvent(new GlobalEventManager.Event("FOLD_SCREEN_CONFIGURATION_CHANGED").msg(param)
                .dst(GlobalEventManager.EVN_LUA).src(GlobalEventManager.EVN_NATIVE)
        );
    }

    @Override
    protected void onNightModeChanged(int mode) {
        super.onNightModeChanged(mode);
        MKKit.initMkUa(MomoKit::getMKUserAgent);
    }

    private void foldScreenUpdateContainerWidth() {
        int width = UIUtils.getScreenWidth() / 5;
        nearBubbleContainer.getLayoutParams().width = width;
        liveBubbleContainer.getLayoutParams().width = width;
        sessionListBubbleContainer.getLayoutParams().width = width;
        focusBubbleContainer.getLayoutParams().width = width;
        profileBubbleContainer.getLayoutParams().width = width;
    }

    private void onGuestModeObtainOAID() {
        if (StringUtils.isNotEmpty(OaidSupplier.INSTANCE.getOaid())) {
            return;
        }

        if ((AccountKit.getAccountManager().isGuestModel()
                && !isFirstResume
                && KV.getSysBool(com.immomo.android.router.momo.SPKeys.System.KEY_HAS_CONFIRMED_AGREEMENT, false))) {

            OaidSupplier.INSTANCE.attemptObtainOAID();
        }
    }

    private void onlineObtainOAIDPermission() {
        if (StringUtils.isNotEmpty(OaidSupplier.INSTANCE.getOaid())) {
            return;
        }
        OaidSupplier.INSTANCE.attemptObtainOAID();
    }

    @Override
    protected void onPageRecreate() {
        super.onPageRecreate();
        handlePageRecreate();
    }

    private void handlePageRecreate() {
        GlobalEventManager.getInstance().sendEvent(new GlobalEventManager.Event(MomoKit.ON_DARK_MODE_CHANGE_RECREATE)
                .dst(GlobalEventManager.EVN_NATIVE).src(GlobalEventManager.EVN_NATIVE));
    }
}
