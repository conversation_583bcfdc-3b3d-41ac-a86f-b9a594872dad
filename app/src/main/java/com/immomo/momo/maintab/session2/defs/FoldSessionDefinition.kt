package com.immomo.momo.maintab.session2.defs

import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.molive.kotlin.extensions.isNotNullOrEmpty
import com.immomo.molive.kotlin.extensions.isNull
import com.immomo.momo.impaas.common.ext.type28Content
import com.immomo.momo.maintab.session2.SessionContentParser
import com.immomo.momo.maintab.session2.SessionDefinition
import com.immomo.momo.maintab.session2.data.database.SessionContent
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.type.FoldSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.GameBoxSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.HePaiSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.MsgFoldSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.NewBoySessionModel
import com.immomo.momo.maintab.session2.domain.model.type.OfficialSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.SpamSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.UnReplySessionModel
import com.immomo.momo.maintab.session2.domain.model.type.UserChatSessionModel
import com.immomo.momo.maintab.session2.utils.SessionFoldHelper
import com.immomo.momo.message.helper.MsgOfficialHelper
import com.immomo.momo.message.helper.SessionTextHelper
import com.immomo.momo.messages.service.SingleMsgService
import com.immomo.momo.service.bean.FolderType
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.sessions.SessionService
import com.immomo.momo.service.sessions.SessionUserCache
import com.immomo.momo.util.OnlineStatusUtils
import com.immomo.momo.util.StringUtils
import com.squareup.moshi.JsonClass
import java.util.Date
import kotlin.math.max

@JsonClass(generateAdapter = true)
class FoldContent : SessionContent("fold") {
    var onlyShowMessageContent: Boolean = false
    var lastMessageOwnerId: String? = null
    var lastMessageOwnerName: String? = null
    var lastMessageContent: String? = null

    @Transient
    var isFirstInit = true

    val desc: String
        get() {
            val sb = StringBuilder()
            if (!onlyShowMessageContent) {
                if (lastMessageOwnerName != null) {
                    sb.append(lastMessageOwnerName)
                }
            }
            if (lastMessageContent != null) {
                sb.append(lastMessageContent)
            }
            return sb.toString()
        }
    var isOnline: Boolean = false
}

class FoldSessionParam(
    val type: String,//准确来说叫 id
    val userName: String?,
    val lastMessageId: String?,
    val lastMsgTime: Long?,
    val message: Message?,
    val unreadCount: Int? = null
)

class FoldSessionDefinition : SessionDefinition<FoldContent, FoldSessionModel>(
    "fold",
    SessionContentParser.moshiParser()
) {

    override fun createContent(): FoldContent {
        return FoldContent()
    }

    override fun generateId(data: Any?): String? {
        return when (data) {
            is FoldSessionParam -> data.type
            else -> super.generateId(data)
        }
    }

    private fun syncOfficialSession(session: SessionEntity, data: FoldSessionParam): Boolean {
        val content = session.content as? FoldContent ?: return false
        if (data.message?.contentType ?: 0 == MsgOfficialHelper.MESSAGE_TYPE_GOTO) {
            if (content.isFirstInit) {
                content.isFirstInit = false
            }
            session.unreadMessageCount += 1
        } else if (data.message?.contentType ?: 0 == MsgOfficialHelper.MESSAGE_TYPE_OFFICIAL) {
            if (content.isFirstInit) {
                content.isFirstInit = false
            }
            session.unreadMessageCount = SessionManager.get().getAllOfficialUnread()
        } else {
            if (content.isFirstInit) {
                session.unreadMessageCount =
                    SessionService.getInstance()
                        .getFoldOuterUnreadedMessageCount(FolderType.Official)
                content.isFirstInit = false
            } else {
                session.unreadMessageCount += 1
            }
            //理论上有未读消息就不应该有静默消息
            if (session.unreadMessageCount > 0) {
                session.silentMessageCount = 0
            } else {
                session.silentMessageCount =
                    SessionService.getInstance().getFoldOuterSlientMessageCount(FolderType.Official)
            }
        }

        if (session.unreadMessageCount > SessionManager.get()
                .getAllOfficialUnread()
        ) {
            session.unreadMessageCount = SessionManager.get().getAllOfficialUnread()
        }
        session.lastMsgId = data.lastMessageId
        session.lastMsgTime = data.lastMsgTime ?: session.lastMsgTime
        if (session.lastMsgId == null) {
            session.lastFetchTime = System.currentTimeMillis()
        }

        data.message?.let { lastMessage ->
            content.onlyShowMessageContent = false
            if (StringUtils.isEmpty(lastMessage.remoteId)) {
                content.onlyShowMessageContent = true
            } else {
                content.lastMessageOwnerId = lastMessage.remoteId
                SessionManager.getInfoCache().fetchUser(session, lastMessage.remoteId) { user ->
                    //最后一条消息的id不一致
                    if (this.content.castOrNull<FoldContent>()?.lastMessageOwnerId
                        != lastMessage.remoteId
                    ) return@fetchUser false

                    lastMessage.owner = user
                    this.content.castOrNull<FoldContent>()?.lastMessageOwnerName =
                        getDisplayName(lastMessage)
                    true
                }
                content.lastMessageOwnerName = getDisplayName(lastMessage)
            }
            content.lastMessageContent = SessionTextHelper.getMessageContent(lastMessage)
            if (lastMessage.contentType == MsgOfficialHelper.MESSAGE_TYPE_OFFICIAL) {
                content.lastMessageContent = lastMessage.content
            }
        }
        return true
    }

    private fun syncUnReplySession(session: SessionEntity, data: FoldSessionParam): Boolean {
        val newestSessionId = SessionManager.get()
            .findAllFoldSessionIds(FolderType.Unreply, upToDate = true, lastMsgTimeDesc = true)
            .firstOrNull() ?: return false
        val model = SessionManager.get()
            .getSessionModelInCache("u_$newestSessionId")

        if (model != null) {
            session.lastMsgId = model.baseInfo.lastMsgId
            if (model.baseInfo.lastMessageTime > 0) {
                session.lastMsgTime = model.baseInfo.lastMessageTime
                session.lastFetchTime = model.baseInfo.lastMessageTime
            }
        } else {
            session.lastMsgId = data.lastMessageId
            if (data.lastMsgTime != null) {
                session.lastMsgTime = data.lastMsgTime
                session.lastFetchTime = data.lastMsgTime
            }
        }
        if (session.lastMsgId == null && session.lastFetchTime == 0L) {
            session.lastFetchTime = System.currentTimeMillis()
        }

        return true
    }


    private fun syncNewBoySession(session: SessionEntity, data: FoldSessionParam): Boolean {
        val findAllFoldSessionIds =
            SessionManager.get().findAllFoldSessionIds(FolderType.NewBOY, true)
        val newestSessionId = findAllFoldSessionIds.firstOrNull().isNull {
            (session.content as? FoldContent)?.let { content ->
                content.lastMessageContent = null
                content.lastMessageOwnerName = null
                content.isOnline = false
            }
            session.unreadMessageCount = 0
            session.silentMessageCount = 0
            return true
        }

        (session.content as? FoldContent)?.let { content ->
            content.isOnline = false
            findAllFoldSessionIds.take(10).forEach {
                if (OnlineStatusUtils.isUserOnline(
                        SessionUserCache.getUser(it)?.getLocationTimestamp()
                    )
                ) {
                    content.isOnline = true
                    return@forEach
                }
            }
            session.unreadMessageCount =
                SessionService.getInstance().getFoldOuterUnreadedMessageCount(FolderType.NewBOY)
        }

        val model = SessionManager.get()
            .getSessionModelInCache("u_$newestSessionId")

        model?.let {
            session.lastMsgId = it.baseInfo.lastMsgId
            if (it.baseInfo.lastMessageTime > 0) {
                session.lastMsgTime = it.baseInfo.lastMessageTime
                session.lastFetchTime = it.baseInfo.lastMessageTime
            }
            (session.content as? FoldContent)?.let { content ->
                session.lastMsgId?.let {
                    SingleMsgService.getInstance().getMessage(newestSessionId, it)
                        ?.let { lastMessage ->
                            content.lastMessageContent = lastMessage.content

                            (model as? UserChatSessionModel)?.userName?.let { name ->
                                content.lastMessageOwnerName = "${name}:"
                            }
                        }
                }
            }

        }
        return true
    }

    private fun syncHePaiSession(session: SessionEntity, data: FoldSessionParam): Boolean {
        val findAllFoldSessionIds =
            SessionManager.get().findAllFoldSessionIds(FolderType.HePai, true)
        val newestSessionId = findAllFoldSessionIds.firstOrNull().isNull {
            (session.content as? FoldContent)?.let { content ->
                content.lastMessageContent = null
                content.lastMessageOwnerName = null
                content.isOnline = false
            }
            session.unreadMessageCount = 0
            session.silentMessageCount = 0
            return true
        }

        (session.content as? FoldContent)?.let { content ->
            content.isOnline = false
            content.onlyShowMessageContent = true
            findAllFoldSessionIds.take(10).forEach {
                if (OnlineStatusUtils.isUserOnline(
                        SessionUserCache.getUser(it)?.getLocationTimestamp()
                    )
                ) {
                    content.isOnline = true
                    return@forEach
                }
            }
            session.unreadMessageCount =
                SessionService.getInstance().getFoldOuterUnreadedMessageCount(FolderType.HePai)
        }

        val model = SessionManager.get()
            .getSessionModelInCache("u_$newestSessionId")

        model?.let {
            session.lastMsgId = it.baseInfo.lastMsgId
            if (it.baseInfo.lastMessageTime > 0) {
                session.lastMsgTime = it.baseInfo.lastMessageTime
                session.lastFetchTime = it.baseInfo.lastMessageTime
            }
            (session.content as? FoldContent)?.let { content ->
                val size = SessionManager.get().findAllFoldSessionIds(FolderType.HePai).size
                content.lastMessageContent =
                    if (size > 0) "${size}人与你合拍，希望得到你的回复" else null
            }

        }
        return true
    }

    private fun syncMsgFolderSession(session: SessionEntity, data: FoldSessionParam): Boolean {
        var dataFinal = data
        if (dataFinal.message == null) {
            dataFinal = SessionFoldHelper.findLastMessageParam()
        }

        dataFinal.lastMessageId.isNull {
            SessionManager.get().deleteSession(KEY_FOLDED_MSG)
        }
        session.orderId =
            if (dataFinal.lastMsgTime.safe() > session.orderId) dataFinal.lastMsgTime.safe() else session.orderId
        session.lastMsgId = dataFinal.lastMessageId
        session.lastMsgTime = session.orderId
        session.unreadMessageCount = if (SessionFoldHelper.isBubbleTest) 0 else max(
            0,
            session.unreadMessageCount + data.unreadCount.safe()
        )
        session.content.castOrNull<FoldContent>()
            ?.also {
                it.onlyShowMessageContent = false
                it.lastMessageOwnerName =
                    dataFinal.userName.takeIf {
                        it.isNotNullOrEmpty()
                    }?.let { "$it:" } ?: dataFinal.message?.let { getDisplayName(it) }
                it.lastMessageContent =
                    dataFinal.message?.type28Content()?.content
                        ?: dataFinal.message?.content

                it.lastMessageOwnerId = dataFinal.message?.remoteId
                if (it.lastMessageOwnerId.isNullOrBlank()) {
                    it.onlyShowMessageContent = true
                    return true
                }
                if (it.lastMessageOwnerName.isNullOrBlank()) {
                    SessionManager.getInfoCache()
                        .fetchUser(session, it.lastMessageOwnerId) { user ->
                            //最后一条消息的id不一致
                            val content =
                                content.castOrNull<FoldContent>() ?: return@fetchUser false
                            if (content.lastMessageOwnerId != it.lastMessageOwnerId) return@fetchUser false
                            dataFinal.message?.owner = user
                            content.lastMessageOwnerName =
                                getDisplayName(dataFinal.message ?: return@fetchUser false)
                            content.onlyShowMessageContent =
                                content.lastMessageOwnerName.isNullOrBlank()
                            true
                        }
                }
            }

        return true
    }

    private fun syncGameBoxSession(session: SessionEntity, data: FoldSessionParam): Boolean {
        val content = session.content as? FoldContent ?: return false
        if (data.message?.contentType == MsgOfficialHelper.MESSAGE_TYPE_GOTO) {
            if (content.isFirstInit) {
                content.isFirstInit = false
            }
            session.unreadMessageCount += 1
        } else if (data.message?.contentType == MsgOfficialHelper.MESSAGE_TYPE_OFFICIAL) {
            if (content.isFirstInit) {
                content.isFirstInit = false
            }
            session.unreadMessageCount = SessionManager.get().getAllGameBoxUnread()
        } else {
            if (content.isFirstInit) {
                session.unreadMessageCount =
                    SessionService.getInstance()
                        .getFoldOuterUnreadedMessageCount(FolderType.GAMEBOX)
                content.isFirstInit = false
            } else {
                session.unreadMessageCount += 1
            }
            //理论上有未读消息就不应该有静默消息
            if (session.unreadMessageCount > 0) {
                session.silentMessageCount = 0
            } else {
                session.silentMessageCount =
                    SessionService.getInstance().getFoldOuterSlientMessageCount(FolderType.GAMEBOX)
            }
        }

        if (session.unreadMessageCount > SessionManager.get()
                .getAllGameBoxUnread()
        ) {
            session.unreadMessageCount = SessionManager.get().getAllGameBoxUnread()
        }
        session.lastMsgId = data.lastMessageId
        session.lastMsgTime = data.lastMsgTime ?: session.lastMsgTime
        if (session.lastMsgId == null) {
            session.lastFetchTime = System.currentTimeMillis()
        }

        data.message?.let { lastMessage ->
            content.onlyShowMessageContent = false
            if (StringUtils.isEmpty(lastMessage.remoteId)) {
                content.onlyShowMessageContent = true
            } else {
                content.lastMessageOwnerId = lastMessage.remoteId
                SessionManager.getInfoCache().fetchUser(session, lastMessage.remoteId) { user ->
                    //最后一条消息的id不一致
                    if (this.content.castOrNull<FoldContent>()?.lastMessageOwnerId
                        != lastMessage.remoteId
                    ) return@fetchUser false

                    lastMessage.owner = user
                    this.content.castOrNull<FoldContent>()?.lastMessageOwnerName =
                        getDisplayName(lastMessage)
                    true
                }
                content.lastMessageOwnerName = getDisplayName(lastMessage)
            }
            content.lastMessageContent = SessionTextHelper.getMessageContent(lastMessage)
            if (lastMessage.contentType == MsgOfficialHelper.MESSAGE_TYPE_OFFICIAL) {
                content.lastMessageContent = lastMessage.content
            }
        }
        return true
    }

    private fun syncSpamSession(session: SessionEntity, data: FoldSessionParam): Boolean {
        val findAllFoldSessionIds =
            SessionManager.get().findAllFoldSessionIds(FolderType.Spam, true)
        val newestSessionId = findAllFoldSessionIds.firstOrNull().isNull {
            (session.content as? FoldContent)?.let { content ->
                content.lastMessageContent = null
                content.lastMessageOwnerName = null
                content.isOnline = false
            }
            session.unreadMessageCount = 0
            session.silentMessageCount = 0
            return true
        }

        (session.content as? FoldContent)?.let { content ->
            content.isOnline = false
            content.onlyShowMessageContent = true
            findAllFoldSessionIds.take(10).forEach {
                if (OnlineStatusUtils.isUserOnline(
                        SessionUserCache.getUser(it)?.getLocationTimestamp()
                    )
                ) {
                    content.isOnline = true
                    return@forEach
                }
            }
            session.unreadMessageCount =
                SessionService.getInstance().getFoldOuterUnreadedMessageCount(FolderType.Spam)
        }

        val model = SessionManager.get()
            .getSessionModelInCache("u_$newestSessionId")

        model?.let {
            session.lastMsgId = it.baseInfo.lastMsgId
            if (it.baseInfo.lastMessageTime > 0) {
                session.lastMsgTime = it.baseInfo.lastMessageTime
                session.lastFetchTime = it.baseInfo.lastMessageTime
            }
            (session.content as? FoldContent)?.let { content ->
                val size = SessionManager.get().findAllFoldSessionIds(FolderType.Spam).size
                content.lastMessageContent = if (size > 0) "对方账号可能异常,请谨慎交谈" else null
            }

        }
        return true
    }

    override fun syncSession(session: SessionEntity, data: Any?): Boolean {
        if (super.syncSession(session, data)) return true

        if (data !is FoldSessionParam) return false

        return when (session.sessionId) {
            Session.ID.FolderOfficial -> syncOfficialSession(session, data)
            Session.ID.HI_UNREPLY -> syncUnReplySession(session, data)
            Session.ID.NEW_BOY -> syncNewBoySession(session, data)
            Session.ID.HEPAI -> syncHePaiSession(session, data)
            Session.ID.SPAM -> syncSpamSession(session, data)
            Session.ID.FOLDED_MSG -> syncMsgFolderSession(session, data)
            Session.ID.GAME_BOX -> syncGameBoxSession(session, data)
            else -> false
        }
    }

    override fun isTransformCompatible(oldSessionType: Int, oldSessionId: String): String? {
        return when (oldSessionId) {
            Session.ID.FolderOfficial,
            Session.ID.HI_UNREPLY
            -> oldSessionId

            else -> null
        }
    }

    override fun transformOldSession(id: String): SessionEntity? {
        val oldSession = SessionService.getInstance().getSession(id) ?: return null

        val session = createSession(id)
        syncSession(
            session,
            FoldSessionParam(
                id,
                oldSession.remoteUser?.displayName,
                oldSession.lastmsgId,
                oldSession.lastMessage?.timestamp?.time,
                oldSession.lastMessage
            )
        )
        return session
    }

    override fun saveOldSession(session: SessionEntity) {
        if (session.sessionId != Session.ID.FolderOfficial && session.sessionId != Session.ID.HI_UNREPLY) {
            return
        }
        SessionService.getInstance().updateSession(session.sessionId) {
            if (session.sessionId == Session.ID.FolderOfficial) {
                it.type = Session.TYPE_FOLDER_OFFICIAL
            } else if (session.sessionId == Session.ID.HI_UNREPLY) {
                it.type = Session.TYPE_UNREPLY
            }

            it.lastmsgId = session.lastMsgId
            it.setOrderid(session.orderId)
            it.fetchtime = Date(session.lastFetchTime)
            it.unreadMessageCount = session.unreadMessageCount
            it.silentMessageCount = session.silentMessageCount
            true
        }
    }

    override fun FoldContent.contentToModel(baseInfo: BaseSessionInfo): FoldSessionModel =
        when (baseInfo.sessionId) {
            Session.ID.FolderOfficial -> OfficialSessionModel(baseInfo, desc.safe())
            Session.ID.HI_UNREPLY -> UnReplySessionModel(baseInfo, desc.safe())
            Session.ID.NEW_BOY -> NewBoySessionModel(baseInfo, desc.safe(), isOnline.safe(false))
            Session.ID.HEPAI -> HePaiSessionModel(baseInfo, desc.safe())
            Session.ID.SPAM -> SpamSessionModel(
                baseInfo.apply { unreadMessageCount = 0 },
                desc.safe()
            )

            Session.ID.FOLDED_MSG -> MsgFoldSessionModel(baseInfo, desc.safe())
            Session.ID.GAME_BOX -> GameBoxSessionModel(baseInfo, desc.safe())
            else -> throw IllegalArgumentException("no match fold ${baseInfo.sessionId}")
        }

    companion object {
        @JvmField
        val UNREPLY = "fold_${Session.ID.HI_UNREPLY}"

        @JvmField
        val NEW_BOY = "fold_${Session.ID.NEW_BOY}"

        @JvmField
        val OFFICIAL = "fold_${Session.ID.FolderOfficial}"

        @JvmField
        val HEPAI = "fold_${Session.ID.HEPAI}"

        @JvmField
        val SPAM = "fold_${Session.ID.SPAM}"

        @JvmField
        val KEY_OFFICIAL = SessionKey("fold", Session.ID.FolderOfficial)

        @JvmField
        val KEY_NEW_BOY = SessionKey("fold", Session.ID.NEW_BOY)

        @JvmField
        val KEY_HEPAI = SessionKey("fold", Session.ID.HEPAI)

        @JvmField
        val KEY_SPAM = SessionKey("fold", Session.ID.SPAM)

        @JvmField
        val KEY_FOLDED_MSG = SessionKey("fold", Session.ID.FOLDED_MSG)

        @JvmField
        val KEY_GAME_BOX = SessionKey("fold", Session.ID.GAME_BOX)
    }
}
