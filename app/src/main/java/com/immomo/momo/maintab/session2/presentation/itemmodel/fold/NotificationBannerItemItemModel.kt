package com.immomo.momo.maintab.session2.presentation.itemmodel.fold

import android.content.Context
import android.view.View
import android.widget.ImageView
import com.immomo.android.mm.cement2.AsyncCementModel
import com.immomo.android.mm.cement2.CementViewHolder
import com.immomo.android.mm.cement2.IViewHolderCreator
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.kotlin.ImageType
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.R
import com.immomo.momo.maintab.session2.domain.model.FoldNotificationBannerModel
import com.immomo.momo.maintab.sessionlist.expose.IItemModelExposure
import com.immomo.momo.maintab.sessionlist.expose.TopFoldSessionLogHelper

/**
 * CREATED BY liu.chong
 * AT 2022/3/10
 */
class NotificationBannerItemItemModel(val model: FoldNotificationBannerModel) :
    AsyncCementModel<FoldNotificationBannerModel, NotificationBannerItemItemModel.VH>(model),
    IItemModelExposure {
    class VH(itemView: View) : CementViewHolder(itemView) {
        val img = itemView.findViewById<ImageView>(R.id.notification_banner_img)
    }

    override fun bindData(holder: VH) {
        super.bindData(holder)
        ImageLoader.load(model.url)
            .imageType(ImageType.URL)
            .cornerRadius(UIUtils.getPixels(6F))
            .into(holder.img)
    }

    override val layoutRes = R.layout.item_notification_banner_item
    override val viewHolderCreator: IViewHolderCreator<VH> = object : IViewHolderCreator<VH> {
        override fun create(view: View): VH {
            return VH(view)
        }
    }

    override fun onExposure(context: Context, position: Int, holder: CementViewHolder) {
        TopFoldSessionLogHelper.logBannerShow(position, state.id, state.url)
    }
}