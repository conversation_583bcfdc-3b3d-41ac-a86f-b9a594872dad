package com.immomo.momo.maintab.view

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.kotlin.ImageLoaderOptions
import com.immomo.framework.kotlin.ImageLoadingListener
import com.immomo.framework.kotlin.ImageType
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.storage.preference.SPKeys
import com.immomo.lcapt.evlog.EVLog
import com.immomo.momo.R
import com.immomo.momo.android.view.CircleImageView
import com.immomo.momo.gotologic.GotoDispatcher
import com.immomo.momo.maintab.sessionlist.bean.SessionSpaceBean
import com.immomo.momo.maintab.sessionlist.space.ISessionSpaceLog

class SessionSpaceView : FrameLayout, View.OnClickListener {
    private var root: View? = null
    private var mTvTitle: TextView? = null
    private var mTvMore: TextView? = null
    private var mTvName: TextView? = null
    private var mTvJoin: TextView? = null
    private var mTvOnline: TextView? = null
    private var mIvAvatar: CircleImageView? = null
    private var mIvClose: ImageView? = null
    private var mIvAvatars: SessionSpaceAvatarView? = null
    private var mContext: Context? = null
    private var bean: SessionSpaceBean? = null
    private var listener: SpaceListener? = null

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context)

    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
            context,
            attrs,
            defStyleAttr
    ) {
        init(context)
    }

    fun init(context: Context) {
        this.mContext = context
        root = LayoutInflater.from(context).inflate(R.layout.session_space_layout, this)
        mTvTitle = findViewById(R.id.tv_title)
        mTvMore = findViewById(R.id.tv_more)
        mTvName = findViewById(R.id.tv_name)
        mTvOnline = findViewById(R.id.tv_online)
        mTvJoin = findViewById(R.id.tv_join)
        mIvAvatar = findViewById(R.id.iv_avatar)
        mIvClose = findViewById(R.id.iv_close)
        mIvAvatars = findViewById(R.id.avatars)
        root?.setOnClickListener(this)
        mTvJoin?.setOnClickListener(this)
        mTvMore?.setOnClickListener(this)

        mIvClose?.setOnClickListener {
            logEvent(0)
            KV.saveUserValue(SPKeys.User.SessionSpace.KEY_CARD_DISMISS_TYPE, 1)
            KV.saveUserValue(SPKeys.User.SessionSpace.KEY_CARD_SHOW_LAST_TIME, System.currentTimeMillis())
            visibility = View.GONE
        }
    }

    private fun logEvent(type: Int) {
        bean?.let {
            val map: HashMap<String, String> = hashMapOf()
            map["biz"] = it.cardType
            if (it.cardType == "group") {
                map["groupid"] = it.cellId
            } else {
                map["roomid"] = it.cellId
            }
            when (type) {
                0 -> EVLog.create(ISessionSpaceLog::class.java).onCloseClick(map)
                1 -> EVLog.create(ISessionSpaceLog::class.java).onJoinClick(map)
                2 -> EVLog.create(ISessionSpaceLog::class.java).onContentClick(map)
                3 -> EVLog.create(ISessionSpaceLog::class.java).onCardShow(map)

            }
        }
    }

    fun setData(bean: SessionSpaceBean) {
        this.bean = bean
        root?.let {
            ImageLoader.load(bean.bgUrl)
                    .imageType(ImageType.URL)
                    .listener(object : ImageLoadingListener<Drawable> {
                        override fun onLoadCompleted(model: ImageLoaderOptions.Model, resource: Drawable) {
                            it.background = resource
                        }
                    })
                    .into()
        }
        mIvAvatar?.let {
            ImageLoader.load(bean.icon)
                    .imageType(ImageType.URL)
                    .into(it)
        }
        mTvTitle?.text = bean.tabText
        mTvName?.text = bean.title
        mTvOnline?.text = bean.subtitle
        mTvJoin?.text = bean.buttonText
        mIvAvatars?.setData(bean.avatars)
        logEvent(3)
    }

    fun setListener(listener: SpaceListener) {
        this.listener = listener
    }

    interface SpaceListener {
        fun onRootClick()
    }

    override fun onClick(v: View) {
        listener?.onRootClick()
        when (v) {
            mTvJoin -> {
                logEvent(1)
                GotoDispatcher.action(bean?.buttonGoto, mContext).execute()
            }
            root -> {
                logEvent(2)
                GotoDispatcher.action(bean?.clickGoto, mContext).execute()
            }
            mTvMore -> {
                GotoDispatcher.action(bean?.moreGoto, mContext).execute()
            }
        }
    }

}