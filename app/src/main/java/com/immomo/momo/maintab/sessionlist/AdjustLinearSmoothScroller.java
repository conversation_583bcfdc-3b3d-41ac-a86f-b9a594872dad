package com.immomo.momo.maintab.sessionlist;

import android.content.Context;
import android.util.DisplayMetrics;

import androidx.annotation.IntDef;
import androidx.recyclerview.widget.LinearSmoothScroller;

public class AdjustLinearSmoothScroller extends LinearSmoothScroller {

    private int scrollType;
    private static int time = 70;


    @IntDef({SNAP_TO_ANY, SNAP_TO_START, SNAP_TO_END})
    public @interface ScrollType {
    }


    public AdjustLinearSmoothScroller(Context context, @ScrollType int scrollType) {
        super(context);
        this.scrollType = scrollType;
    }


    //控制速度,单位英寸的时间
    @Override
    protected float calculateSpeedPerPixel(DisplayMetrics displayMetrics) {
        return super.calculateSpeedPerPixel(displayMetrics);
    }

    //控制总时间
    @Override
    protected int calculateTimeForScrolling(int dx) {
        return time;
    }

    public static void setTime(int milliseconds) {
        time = milliseconds;
    }

    @Override
    protected int getVerticalSnapPreference() {
        return scrollType;
    }
}
