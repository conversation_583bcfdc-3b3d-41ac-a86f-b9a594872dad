package com.immomo.momo.maintab.session2.defs

import com.immomo.momo.maintab.model.ActiveUser
import com.immomo.momo.maintab.model.SessionActiveUser
import com.immomo.momo.maintab.session2.SessionContentParser
import com.immomo.momo.maintab.session2.SessionDefinition
import com.immomo.momo.maintab.session2.data.database.SessionContent
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.data.util.SessionTabTestHelper
import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.type.ActiveUserModel
import com.immomo.momo.maintab.session2.domain.model.type.ActiveUserSessionModel
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.sessions.SessionService
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
class ActiveUserContent : SessionContent("activeUser") {
    var activeUsers: MutableList<ActiveUser>? = null
}

class ActiveUserSessionDefinition :
    SessionDefinition<ActiveUserContent, ActiveUserSessionModel>(
        "activeUser", SessionContentParser.moshiParser()
    ) {
    override fun createContent(): ActiveUserContent = ActiveUserContent()

    override fun generateId(data: Any?): String? {
        return when (data) {
            is SessionActiveUser -> Session.ID.ActiveUser
            else -> super.generateId(data)
        }
    }

    override fun syncSession(session: SessionEntity, data: Any?): Boolean {
        if (super.syncSession(session, data)) return true

        val content = session.content as? ActiveUserContent ?: return false

        val activeUser = data as? SessionActiveUser ?: return false
        content.activeUsers =
            activeUser.userList?.filterNotNull()?.toMutableList() ?: mutableListOf()

        session.lastFetchTime = if (activeUser.updateTime == 0L)
                    System.currentTimeMillis() else activeUser.updateTime * 1000
        return true
    }

    override fun isTransformCompatible(oldSessionType: Int, oldSessionId: String): String? {
        return if (oldSessionType == Session.TYPE_ACTIVE_USER)
            Session.ID.ActiveUser else null
    }

    override fun transformOldSession(id: String): SessionEntity? {
        val oldSession =
            SessionService.getInstance().getSession(Session.ID.ActiveUser) ?: return null

        val session = createSession(id)
        syncSession(session, oldSession.activeUser)
        return session
    }

    override fun ActiveUserContent.contentToModel(
        baseInfo: BaseSessionInfo
    ): ActiveUserSessionModel = ActiveUserSessionModel(
        baseInfo = baseInfo,
        activeUsers = activeUsers?.map { ActiveUserModel.from(it) } ?: emptyList()
    )

    companion object {
        @JvmField
        val Type = "activeUser"

        @JvmField
        val KEY_ACTIVE_USER = SessionKey(Type, Session.ID.ActiveUser)
    }
}