package com.immomo.momo.maintab;

import android.app.Activity;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.immomo.momo.MomoKit;
import com.immomo.momo.innergoto.helper.ActivityHandler;
import com.immomo.momo.push.PushHelper;
import com.immomo.momo.util.StringUtils;

/**
 * Created by huang.liang<PERSON>e on 2018/2/23.
 * <p>
 * Momo Tech 2011-2018 © All Rights Reserved.
 */

public class NotificationActionActivity extends AppCompatActivity {
    public static final String KEY_GOTO = "goto";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        String gotoAction = getIntent().getStringExtra(KEY_GOTO);
        if (StringUtils.notEmpty(gotoAction)) {
            PushHelper.logLocalPushClick(getIntent(), NotificationActionActivity.class);
            MomoKit.getApp().removeGotoNotify();
            Activity topActivity = MomoKit.getTopActivity();
            if (topActivity != null) {
                ActivityHandler.executeAction(gotoAction, topActivity);
            } else {
                ActivityHandler.executeAction(gotoAction, this);
            }
        }

        finish();
    }
}
