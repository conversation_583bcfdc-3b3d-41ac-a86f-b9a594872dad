package com.immomo.momo.maintab.session2.data.database;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Generated;

/**
 * 记录收进过合拍的session，收进过的不再收进
 */
@Entity(nameInDb = "hepai_fold_session",generateConstructors = false)
public class HepaiSessionEntity {
    @Id
    String sessionId;

    public HepaiSessionEntity() {
    }

    public HepaiSessionEntity(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getSessionId() {
        return this.sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
}
