package com.immomo.momo.maintab.session2.presentation.itemmodel

import android.content.Intent
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.widget.TextView
import com.immomo.android.mm.cement2.AsyncCementModel
import com.immomo.android.mm.cement2.CementViewHolder
import com.immomo.android.mm.cement2.IViewHolderCreator
import com.immomo.framework.utils.UIUtils
import com.immomo.framework.view.widget.LinesShimmerTextView
import com.immomo.molive.gui.common.view.DragBubbleView
import com.immomo.momo.R
import com.immomo.momo.homepage.view.FlipTextView
import com.immomo.momo.maintab.session2.domain.model.type.NewBoySessionModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.events.SessionDraggableViewTouchListener
import com.immomo.momo.maintab.sessionlist.util.SessionHelper
import com.immomo.momo.message.activity.NewBoySessionActivity
import com.immomo.momo.mvp.maintab.mainbubble.MainBubbleViewImpl
import com.immomo.momo.util.DateUtil
import java.util.Date


class NewBoySessionItemModel(
    val info: NewBoySessionModel,
    val onDrag: SessionDraggableViewTouchListener?
) :
    AsyncCementModel<NewBoySessionModel, NewBoySessionItemModel.ViewHolder>(info) {

    init {
        id(info.uniqueId)
    }

    override val layoutRes: Int
        get() = R.layout.item_new_boy_session

    override val viewHolderCreator: IViewHolderCreator<ViewHolder>
        get() = object : IViewHolderCreator<ViewHolder> {
            override fun create(view: View): ViewHolder = ViewHolder(view)
        }

    override fun bindData(holder: ViewHolder) {
        super.bindData(holder)
        fillTimeStamp(holder)
        var desc = "暂无新朋友"
        if (info.desc.isNotEmpty()) {
            desc = info.desc
        }
        holder.onlineStatusDot.visibility = if (info.isOnline) View.VISIBLE else View.GONE

        holder.viewDes.setText(
            desc,
            UIUtils.getColor(holder.itemView.context, R.color.color_aaaaaa_to_40fff),
            13f
        )
        if (info.baseInfo.unreadMessageCount > 0) {
            holder.statusTextView_new.visibility = View.VISIBLE
            holder.statusTextView_new.text =
                MainBubbleViewImpl.getBubbleMax999String(info.baseInfo.unreadMessageCount)
        } else {
            holder.statusTextView_new.visibility = View.GONE
        }

        holder.itemView.setOnClickListener {
            SessionHelper.SessionLogParams(
                "newfriends", info.baseInfo.unreadMessageCount, holder.adapterPosition, "0",
                false, null, 0, 0, "", "", holder.getTimeStr(), false
            ).also {
                SessionHelper.Log.logSessionClick(it)
            }
            it.context.startActivity(Intent(it.context, NewBoySessionActivity::class.java))
        }

        holder.statusTextView_new.setOnTouchListener { v, event ->
            onDrag?.onTouch(
                v,
                event,
                holder.adapterPosition,
                holder.statusTextView_new,
                DragBubbleView.DRAG_FROM_LIST
            ) ?: false
        }

        holder.tvTitle.setTextColor(
            UIUtils.getColor(
                if (com.immomo.momo.util.MomoKit.isDarkMode(holder.itemView.context))
                    R.color.color_80fff else R.color.color_text_3b3b3b
            )
        )
    }

    private fun fillTimeStamp(holder: ViewHolder) {
        var timeText: String? = ""
        if (info.baseInfo.lastMsgId.isNotEmpty()) {
            timeText = DateUtil.getTimeLineStringStyle3(Date(info.baseInfo.lastMessageTime))
        }
        holder.tvTimeStamp.text = timeText
    }

    class ViewHolder(itemView: View) : CementViewHolder(itemView) {
        val tvTitle: LinesShimmerTextView = itemView.findViewById(R.id.tv_title)
        val viewDes: FlipTextView = itemView.findViewById(R.id.view_desc)
        val tvTimeStamp: TextView = itemView.findViewById(R.id.tv_timestamp)
        val statusTextView_new: TextView = itemView.findViewById(R.id.chatlist_item_tv_status_new)
        val onlineStatusDot: View = itemView.findViewById(R.id.img_online_status_dot)


        init {
            viewDes.setTxtGravity(Gravity.LEFT)
            viewDes.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13f)
            viewDes.setDelayTime(1000L)
        }


        fun getTimeStr(): String {
            return if (tvTimeStamp.visibility == View.VISIBLE) {
                tvTimeStamp.text.toString()
            } else ""
        }
    }
}