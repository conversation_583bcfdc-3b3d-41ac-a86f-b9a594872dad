package com.immomo.momo.maintab

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import com.immomo.mmutil.StringUtils
import com.immomo.momo.MomoKit
import com.immomo.momo.certify.UserCertifySoundHelper
import com.immomo.momo.universe.phonograph.audio.AudioDownLoadListener
import com.immomo.momo.universe.phonograph.audio.AudioHandler
import com.immomo.momo.universe.phonograph.audio.AudioHandler.generateAudioUrl
import com.immomo.momo.universe.phonograph.audio.AudioHandler.isAudioExist
import java.io.File
import kotlin.math.abs
import kotlin.math.pow
import kotlin.math.sqrt

/**
 *
 * author: hongming.wei
 * data: 2021/10/15
 */
class SplashShakeEventListener(
    var threshold: Double = 5.0,
    var shakeSound: String = "",
    private var shakeEvent: ShakeEventListener
) : SensorEventListener {

    private val G: Double = 9.81
    private var isSendSoundPool = true
    private var powX: Double = 0.0
    private var powY: Double = 0.0
    private var powZ: Double = 0.0
    private var acc: Double = 0.0

    private var senSensorManager: SensorManager? = null
    private var senAccelerometer: Sensor? = null
    private var isAccSensorClose = true
    private var soundHelper: UserCertifySoundHelper? = null

    private var mAudioFile: String = ""

    init {
        try {
            senSensorManager =
                MomoKit.getContext().getSystemService(Context.SENSOR_SERVICE) as SensorManager?
            senAccelerometer = senSensorManager?.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
            senSensorManager?.registerListener(
                this,
                senAccelerometer,
                SensorManager.SENSOR_DELAY_NORMAL
            )
            soundHelper = UserCertifySoundHelper()
        } catch (ex: Exception) {
            // Do nothing
        }

        initSound(shakeSound)
    }

    fun stopSound() {
        senSensorManager?.unregisterListener(this)
        soundHelper?.release()
    }

    private fun initSound(shakeSound: String) {
        val audioUrl = generateAudioUrl(shakeSound)
        if (isAudioExist(audioUrl)) {
            mAudioFile = audioUrl
        } else {
            AudioHandler.downLoadAudio(audioUrl, object : AudioDownLoadListener {
                override fun onAudioDownloading() {

                }

                override fun onAudioDownSuccess(audioFile: File) {
                    //回调，下载音频文件成功
                    mAudioFile = audioFile.absolutePath
                }

                override fun onAudioDownFailed() {

                }
            })
        }
    }

    private fun startSound() {
        when {
            StringUtils.isNotEmpty(mAudioFile) -> {
                soundHelper?.play(mAudioFile)
            }
            StringUtils.isNotEmpty(shakeSound) -> {
                soundHelper?.play(shakeSound)
            }
            else -> {
            }
        }
    }

    override fun onSensorChanged(event: SensorEvent?) {
        // 只要能收到传感器事件，用户就没有关闭权限
        isAccSensorClose = false
        event?.let {
            powX = it.values[0].pow(2).toDouble()
            powY = it.values[1].pow(2).toDouble()
            powZ = it.values[2].pow(2).toDouble()
            acc = sqrt(powX + powY + powZ)
            if (abs(acc - G) > threshold && isSendSoundPool) {
                isSendSoundPool = false
                startSound()
                shakeEvent.sendShakeDetails()
            }
        }
    }

    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
    }

    interface ShakeEventListener {
        fun sendShakeDetails()
    }

    fun isAccSensorClose(): Boolean {
        return isAccSensorClose
    }
}