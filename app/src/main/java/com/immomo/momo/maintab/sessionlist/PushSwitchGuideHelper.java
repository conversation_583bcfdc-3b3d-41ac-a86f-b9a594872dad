package com.immomo.momo.maintab.sessionlist;

import android.app.Activity;
import android.app.Dialog;

import androidx.annotation.NonNull;

import com.cosmos.mdlog.MDLog;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.momo.LogTag;
import com.immomo.momo.sessionnotice.bean.TipsInfoCard;
import com.immomo.momo.sessionnotice.bean.TipsInfoCardV2;

/**
 * Created by z<PERSON>.ya<PERSON><PERSON> on 2019/5/29.
 */
public class PushSwitchGuideHelper {

    private PushSwitchGuideDialog guideDialog;
    private PushSwitchGuideDialogV2 guideDialogV2;
    public static boolean isShowGuideDialogV2 = false;

    public static boolean isShowGuideDialogV2() {
        return isShowGuideDialogV2;
    }

    public static void setShowGuideDialogV2(boolean isShowGuideDialogV2) {
        PushSwitchGuideHelper.isShowGuideDialogV2 = isShowGuideDialogV2;
    }

    /**
     * 展示通知引导弹框
     */
    public void showPushGuideDialog(Activity activity, @NonNull TipsInfoCard card) {
        if (activity == null || activity.isFinishing()) {
            return;
        }
        setShowGuideDialogV2(false);
        if (null == guideDialog) {
            guideDialog = new PushSwitchGuideDialog(activity, PushSwitchGuideDialog.SOURCE_SESSION);
        }
        if (!guideDialog.isShowing()) {
            guideDialog.setData(card);
            guideDialog.show();
        }
        KV.saveUserValue(SPKeys.PushGuide.KEY_LAST_DIALOG_PUSH_SWITCH_GUIDE_TIME, System.currentTimeMillis());
    }

    /**
     * 展示通知引导弹框
     */
    public void showPushGuideDialogV2(Activity activity, @NonNull TipsInfoCardV2 card) {
        if (activity == null || activity.isFinishing()) {
            return;
        }
        setShowGuideDialogV2(true);
        if (null == guideDialogV2) {
            guideDialogV2 = new PushSwitchGuideDialogV2(activity);
        }
        if (!guideDialogV2.isShowing()) {
            guideDialogV2.setData(card);
            guideDialogV2.show();
        }
        KV.saveUserValue(SPKeys.PushGuide.KEY_LAST_DIALOG_PUSH_SWITCH_GUIDE_TIME, System.currentTimeMillis());
    }

    public void clear(Activity activity) {
        clearDialog(guideDialog, activity);
        clearDialog(guideDialogV2, activity);
    }

    private void clearDialog(Dialog dialog, Activity activity) {
        if (dialog != null && dialog.isShowing() && activity != null && !activity.isFinishing() && !activity.isDestroyed()) {
            try {
                dialog.dismiss();
            } catch (Exception e) {
                MDLog.printErrStackTrace(LogTag.COMMON, e);
            }
        }
    }
}
