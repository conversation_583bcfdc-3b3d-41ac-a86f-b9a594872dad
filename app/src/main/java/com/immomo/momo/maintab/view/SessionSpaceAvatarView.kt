package com.immomo.momo.maintab.view

import android.content.Context
import android.graphics.*
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.View
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.kotlin.ImageLoaderOptions
import com.immomo.framework.kotlin.ImageLoadingListener
import com.immomo.framework.kotlin.ImageType
import com.immomo.framework.utils.UIUtils
import java.util.concurrent.atomic.AtomicBoolean

class SessionSpaceAvatarView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0)
    : View(context, attrs, defStyleAttr) {

    private val bitmaps: MutableList<Bitmap> = mutableListOf()
    private val isLoading: AtomicBoolean = AtomicBoolean(false)
    private var centerW: Float = 0f
    private var centerH: Float = 0f
    private var bitmapSize: Int = UIUtils.getPixels(16f)
    private val coverSize = UIUtils.getPixels(9f).toFloat()

    private val paint: Paint = Paint().apply {
        isAntiAlias = true
        isFilterBitmap = true
        xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_OVER)
    }

    private val paintWhite: Paint = Paint().apply {
        isAntiAlias = true
        color = Color.WHITE
    }


    fun setData(tempUrls: List<String>?) {
        if (tempUrls == null || tempUrls.isEmpty() || isLoading.get()) {
            return
        }

        isLoading.set(true)
        bitmaps.clear()

        val urls = if (tempUrls.size > 4) {
            tempUrls.subList(0, 4)
        } else {
            tempUrls
        }
        var count = urls.size

        urls.map {
            ImageLoader.load(it)
                    .imageType(ImageType.URL)
                    .size(bitmapSize)
                    .cornerRadius(bitmapSize / 2)
                    .listener(object : ImageLoadingListener<Drawable> {
                        override fun onLoadStarted(model: ImageLoaderOptions.Model, placeholderDrawable: Drawable?) {
                            count--
                        }

                        override fun onLoadCompleted(model: ImageLoaderOptions.Model, resource: Drawable) {
                            if (resource is BitmapDrawable && resource.bitmap != null) {
                                bitmaps.add(resource.bitmap)
                            }
                            if (count == 0) {
                                invalidate()
                            }
                        }
                    })
                    .into()
        }
    }

    override fun onDraw(canvas: Canvas?) {
        super.onDraw(canvas)

        if (bitmaps.isEmpty() || canvas == null) {
            isLoading.set(false)
            return
        }
        centerW = bitmapSize / 2f
        centerH = bitmapSize / 2f
        val tem = UIUtils.getPixels(1f)
        val whiteCircleSize = bitmapSize/2 + tem

        for (index: Int in 0 until bitmaps.size) {
            canvas.drawCircle(index * coverSize + centerW,centerH + tem,whiteCircleSize.toFloat(),paintWhite)
            canvas.drawBitmap(bitmaps[index], index * coverSize, tem.toFloat(), paint)
        }

        isLoading.set(false)
    }

}