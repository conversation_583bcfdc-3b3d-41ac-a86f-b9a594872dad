package com.immomo.momo.maintab.sessionlist.sort.usecase

import com.immomo.framework.rxjava.interactor.UseCase
import com.immomo.mmutil.task.MMThreadExecutors
import com.immomo.momo.service.sessions.ISessionRepository
import io.reactivex.Flowable

class SessionRecommendUseCase(private var sessionRepository: ISessionRepository) :
    UseCase<String, String>(MMThreadExecutors.User, MMThreadExecutors.Main) {
    override fun buildUseCaseFlowable(params: String?): Flowable<String> {
        return sessionRepository.requestSessionRecommend()
    }
}