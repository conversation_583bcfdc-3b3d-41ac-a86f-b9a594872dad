package com.immomo.momo.maintab.session2.domain.model.type

import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo

data class DiscussChatSessionModel(
    override val baseInfo: BaseSessionInfo,
    override val chatId: String,
    override val desc: String,
    override val draftString: String,
    override val draftQuoteString: String,
    override val lastMessageType: Int,
    override val showMessageStatus: <PERSON>olean,
    override val lastMessageStatus: Int,

    val discussAvatar: String,
    val discussName: String,

    val isHongbao: Boolean,
    val isGift: Boolean
) : ChatSessionModel {
    override fun hasSpecialNotice(): <PERSON><PERSON><PERSON> {
        return isHongbao || isGift
    }
}