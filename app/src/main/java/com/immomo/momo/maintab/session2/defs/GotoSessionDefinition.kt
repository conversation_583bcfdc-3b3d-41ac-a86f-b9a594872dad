package com.immomo.momo.maintab.session2.defs

import android.os.Bundle
import com.cosmos.mdlog.MDLog
import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.storage.preference.SPKeys.User.TextChat.KEY_TEXT_CHAT_SESSION_RED_TEXT
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.MomoKit
import com.immomo.momo.R
import com.immomo.momo.contentprovider.DBContentKeys
import com.immomo.momo.flashchat.contract.FlashChatConstants
import com.immomo.momo.impaas.common.ext.isTextChatMsg
import com.immomo.momo.maintab.session2.SessionContentParser
import com.immomo.momo.maintab.session2.SessionDefinition
import com.immomo.momo.maintab.session2.data.database.SessionContent
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.data.manager.SessionMessage
import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.type.GotoSessionModel
import com.immomo.momo.maintab.sessionlist.util.DelMsgIDsManager
import com.immomo.momo.message.helper.SessionTextHelper
import com.immomo.momo.messages.service.FlashSessionService
import com.immomo.momo.messages.service.TextChatSessionService
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.bean.message.Type35Content
import com.immomo.momo.service.bean.session.IMJGotoSessionContent
import com.immomo.momo.service.sessions.SessionService
import com.immomo.momo.textchat.contract.TextChatConstants
import com.immomo.momo.util.StringUtils
import com.squareup.moshi.JsonClass
import java.util.Date

@JsonClass(generateAdapter = true)
class GotoContent(
    var action: String? = null,
    var title: String? = null,
    var text: String? = null,
    var icon: String? = null,
    var source: Int? = null,//1 api else 0
    var sourceStr: String? = null,
    var hasRedPacketTxt: Boolean = false
) : SessionContent("goto")

class GotoSessionBundle(
    val sessionId: String?,
    val packetId: String?,
    val action: String?,
    val icon: String?,
    val text: String?,
    val title: String?,
    val fetchTime: Long,
    val unreadCount: Int,
    val showRedPoint: Boolean,
    val isFromApi: Boolean,
    val sourceStr: String? = "",
    val foldType: Int
) {
    constructor(bundleIn: Bundle) : this(
        sessionId = bundleIn.getString(DBContentKeys.IMJGotoSessionHandler.sessionid),
        packetId = bundleIn.getString(DBContentKeys.IMJGotoSessionHandler.packetId),
        action = bundleIn.getString(DBContentKeys.IMJGotoSessionHandler.action),
        icon = bundleIn.getString(DBContentKeys.IMJGotoSessionHandler.icon),
        text = bundleIn.getString(DBContentKeys.IMJGotoSessionHandler.text),
        title = bundleIn.getString(DBContentKeys.IMJGotoSessionHandler.title),
        fetchTime = bundleIn.getLong(DBContentKeys.IMJGotoSessionHandler.fetchTime),

        unreadCount = bundleIn.getInt(DBContentKeys.RecommendContactHandler.unreadCount, 0),
        showRedPoint = bundleIn.getInt(DBContentKeys.IMJGotoSessionHandler.redTip, 0) == 1,
        isFromApi = bundleIn.getBoolean(DBContentKeys.IMJGotoSessionHandler.source),
        sourceStr = bundleIn.getString(DBContentKeys.IMJGotoSessionHandler.sourceStr, ""),
        foldType = bundleIn.getInt("foldType", 0)
    )
}

class GotoSessionDefinition :
    SessionDefinition<GotoContent, GotoSessionModel>("goto", SessionContentParser.moshiParser()) {
    override fun createContent(): GotoContent {
        return GotoContent()
    }

    override fun generateId(data: Any?): String? {
        return when {
            data is GotoSessionBundle -> data.sessionId
            else -> super.generateId(data)
        }
    }

    private fun syncFlashChat(
        session: SessionEntity,
        lastMessage: Message,
        isFromIm: Boolean
    ): Boolean {
        val content = session.content as? GotoContent ?: return false

        if (lastMessage.isUpdateSession && !lastMessage.isImSpam) {
            session.lastMsgId = lastMessage.msgId ?: session.lastMsgId
            if (lastMessage.stopFloat == 0) {
                session.lastMsgTime = lastMessage.timestamp?.time ?: session.lastMsgTime
            }
        }

        //entrySession未读数+1，用作消息盒子来临时读取unread
        if (lastMessage.receive) {
            session.unreadMessageCount += 1
        }

        FlashChatConstants.initEntryInfo(content, lastMessage)

        if (!FlashChatConstants.UnlockSuccess.hasUnlock(lastMessage.remoteId)) {
            FlashSessionService.getInstance().updateOrInsertSessionByMsg(lastMessage)
        }

        if (lastMessage.notShowInSession) {
            //ignored
        } else if (lastMessage.isUpdateSession) {
            // 自己则没有名字
            var text = if (!lastMessage.receive
                || StringUtils.isEmpty(lastMessage.remoteId)
                || lastMessage.getMessageContent(Type35Content::class.java) != null
            ) {
                "" // 空消息体
            } else {
                getDisplayName(lastMessage)
            }
            text += SessionTextHelper.getMessageContent(lastMessage)
            content.text =
                if (text.isEmpty()) {
                    UIUtils.getString(R.string.flash_chat_default_entry_desc)
                } else text
            content.action = FlashChatConstants.getEntryGotoAction(isFromIm)
        }

        content.source = 1
        return true
    }

    private fun getTextBasedOnSession(session: SessionEntity, message: Message): String {
        return if (session.sessionId == Session.ID.TEXT_CHAT) {
            fetchUserAndUpdateMessage(session, message)
            determineMessageContent(message)
        } else {
            ""
        }
    }

    private fun fetchUserAndUpdateMessage(session: SessionEntity, message: Message) {
        if (!message.receive) {
            return
        }
        SessionManager.getInfoCache().fetchUser(session, message.remoteId) { user ->
            message.username = user.displayName ?: session.sessionId
            true
        }
    }

    private fun determineMessageContent(message: Message): String {
        return if (shouldReturnEmptyMessage(message)) {
            "" // 空消息体
        } else {
            getDisplayName(message)
        }
    }
    private fun shouldReturnEmptyMessage(message: Message): Boolean {
        return !message.receive ||
                message.contentType == Message.CONTENTTYPE_MESSAGE_NOTICE ||
                StringUtils.isEmpty(message.remoteId) ||
                message.getMessageContent(Type35Content::class.java) != null
    }

    private fun syncTextChat(
        session: SessionEntity,
        lastMessage: Message,
        isFromIm: Boolean,
        addUnread: Boolean = true
    ): Boolean {
        val content = session.content as? GotoContent ?: return false

        if (lastMessage.isUpdateSession && !lastMessage.isImSpam) {
            session.lastMsgId = lastMessage.msgId ?: session.lastMsgId
            if (lastMessage.stopFloat == 0) {
                session.lastMsgTime = lastMessage.timestamp?.time ?: session.lastMsgTime
            }
        }

        //entrySession未读数+1，用作消息盒子来临时读取unread
        if (!lastMessage.isImSpam && !lastMessage.isMessageNotice && lastMessage.receive && addUnread) {
            session.unreadMessageCount += 1
        }

        TextChatConstants.initEntryInfo(content, lastMessage)
        if (lastMessage.isTextChatMsg().not()) {
            //老版匹配不带该标识，更新由goto传递到Session库。
            //新版就是普通二人聊天，folderType = FolderType.TEXT_CHAT，Session逻辑会自动更新
            TextChatSessionService.getInstance().updateOrInsertSessionByMsg(lastMessage)
        }


        if (lastMessage.notShowInSession) {
            //ignored
        } else if (lastMessage.isUpdateSession && !lastMessage.isImSpam) {
            var text = getTextBasedOnSession(session, lastMessage)
            text += SessionTextHelper.getMessageContent(lastMessage) ?: ""
            content.text =
                if (text.isEmpty()) {
                    if (MomoKit.getCurrentUser() == null || MomoKit.getCurrentUser()?.isMale == true) {
                        UIUtils.getString(R.string.text_chat_default_entry_desc)
                    } else {
                        UIUtils.getString(R.string.text_chat_femal_entry_desc)
                    }
                } else text
            content.action = TextChatConstants.getEntryGotoAction(isFromIm)
        }

        //默认文案不展示未读数
        val updateUnread = if (MomoKit.getCurrentUser()?.isFemale == true) {
            content.text == UIUtils.getString(R.string.text_chat_femal_entry_desc)
        } else {
            content.text == UIUtils.getString(R.string.text_chat_default_entry_desc)
        }

        if (updateUnread) {
            session.unreadMessageCount = 0
        }
        content.source = 1
        return true
    }


    override fun syncSession(session: SessionEntity, data: Any?): Boolean {
        if (DelMsgIDsManager.getInstance().containsSession(session.sessionId)) {
            session.markAsDeleted = true
            return true
        }

        if (super.syncSession(session, data)) return true

        val sessionMessage = data.castOrNull<SessionMessage>()
        if (session.sessionId == Session.ID.FLASH_CHAT && sessionMessage != null) {
            when (sessionMessage) {
                is SessionMessage.Sync,
                is SessionMessage.Receive,
                is SessionMessage.Send -> {
                    sessionMessage.messageList.sortedBy { it.timestampExt }.forEach {
                        syncFlashChat(session, it, true)
                    }
                }

                is SessionMessage.ResetLast -> {
                    syncFlashChat(session, sessionMessage.message, false)
                }

                is SessionMessage.Delete,
                is SessionMessage.Update,
                is SessionMessage.UpdateStatus -> {
                    //ignore
                }
            }
            return true
        } else if (session.sessionId == Session.ID.TEXT_CHAT && sessionMessage != null) {
            when (sessionMessage) {
                is SessionMessage.Sync,
                is SessionMessage.Receive,
                is SessionMessage.Send -> {
                    sessionMessage.messageList.sortedBy { it.timestampExt }.forEach {
                        syncTextChat(session, it, true)
                    }
                }

                is SessionMessage.ResetLast -> {
                    syncTextChat(
                        session, sessionMessage.message,
                        isFromIm = false,
                        addUnread = false
                    )
                }

                is SessionMessage.Delete,
                is SessionMessage.Update,
                is SessionMessage.UpdateStatus -> {
                    //ignore
                }
            }
            return true
        }
        val bundle = data as? GotoSessionBundle ?: return false

        if (bundle.showRedPoint) {
            session.unreadMessageCount = 0
            session.silentMessageCount = 1
        } else if (FlashChatConstants.isFlashChatSession(bundle.sessionId) ||
            TextChatConstants.isTextChatSession(bundle.sessionId)
        ) {
            // 闪聊默认在之前的数据库的未读数基础上加
            session.unreadMessageCount = session.unreadMessageCount + bundle.unreadCount
            session.silentMessageCount = 0
        } else {
            //如果是来自api的话直接覆盖否则累加
            session.unreadMessageCount =
                if (bundle.isFromApi) bundle.unreadCount
                else (session.unreadMessageCount + bundle.unreadCount);
            session.silentMessageCount = 0
        }

        session.lastMsgId = bundle.packetId
        session.lastMsgTime = bundle.fetchTime
        session.lastFetchTime = bundle.fetchTime
        session.foldType = bundle.foldType

        val content = session.content as? GotoContent ?: return false
        content.action = bundle.action
        content.icon = bundle.icon
        content.text = bundle.text
        content.title = bundle.title
        content.source = if (bundle.isFromApi) 1 else 0
        content.sourceStr = bundle.sourceStr
        return true
    }

    override fun onReloadInfo(id: String, session: SessionEntity, forceReload: Boolean) {
        super.onReloadInfo(id, session, forceReload)
        if (session.sessionId == Session.ID.TEXT_CHAT) {
            onReloadTextChatInfo(session)
        }
    }

    private fun onReloadTextChatInfo(session: SessionEntity) {
        val content = session.content as? GotoContent ?: return
        content.hasRedPacketTxt =
            StringUtils.notEmpty(KV.getUserStr(KEY_TEXT_CHAT_SESSION_RED_TEXT, ""))
    }

    override fun isTransformCompatible(oldSessionType: Int, oldSessionId: String): String? {
        return if (oldSessionType == Session.TYPE_GOTO) {
            if (oldSessionId == MomoKit.getCurrentOrGuestMomoId() + "gotochat") "20gotochat"
            else oldSessionId
        } else null
    }

    override fun saveOldSession(session: SessionEntity) {
        val content = session.castOrNull<GotoContent>() ?: return

        SessionService.getInstance().updateSession(session.sessionId) {
            it.type = Session.TYPE_GOTO

            it.lastmsgId = session.lastMsgId
            it.setOrderid(session.orderId)
            it.fetchtime = Date(session.lastFetchTime)
            it.unreadMessageCount = session.unreadMessageCount
            it.silentMessageCount = session.silentMessageCount

            it.gotoAction = content.action
            it.gotoTitle = content.title
            it.gotoText = content.text
            it.gotoIcon = content.icon

            it.sessionContent = IMJGotoSessionContent().also { it.source = content.source ?: 0 }
            true
        }
    }

    override fun transformOldSession(id: String): SessionEntity? {
        var oldSession = SessionService.getInstance().getSession(id)
        // 天天庄园新版type消息时（20gotochat），同步更新旧版消息显示（需要删除旧版消息入口）
        if (id == "20gotochat" && oldSession == null) {
            oldSession = SessionService.getInstance()
                .getSession(MomoKit.getCurrentOrGuestMomoId() + "gotochat")
        }
        if (oldSession == null) return null

        val session = createSession(id)
        if (oldSession.lastMessage != null) {
            session.lastMsgTime =
                oldSession.lastMessage?.timestampMillis ?: System.currentTimeMillis()
            session.lastMsgId = oldSession.lastMessage?.msgId
        }
        session.lastFetchTime = oldSession.fetchtime?.time ?: System.currentTimeMillis()
        session.orderId = oldSession.orderId

        val content = session.content as GotoContent

        content.action = oldSession.gotoAction
        content.title = oldSession.gotoTitle
        content.text = oldSession.gotoText
        content.icon = oldSession.gotoIcon
        content.source = (oldSession.sessionContent as? IMJGotoSessionContent)?.source

        val lastMessage = oldSession.lastMessage
        if (session.sessionId == Session.ID.FLASH_CHAT && lastMessage != null) {
            syncFlashChat(session, lastMessage, true)
        } else if (session.sessionId == Session.ID.TEXT_CHAT && lastMessage != null) {
            syncTextChat(session, lastMessage, true)
        }

        session.unreadMessageCount = oldSession.unreadMessageCount
        session.silentMessageCount = oldSession.silentMessageCount

        return session
    }

    override fun onClearUnread(session: SessionEntity) {
        super.onClearUnread(session)
        if (session.sessionId == Session.ID.TEXT_CHAT) {
            clearTextChatUnread(session)
        }
    }

    private fun clearTextChatUnread(session: SessionEntity){
        val content = session.content as? GotoContent ?: return
        KV.saveUserValue(KEY_TEXT_CHAT_SESSION_RED_TEXT, "")
        content.hasRedPacketTxt = false
    }

    override fun GotoContent.contentToModel(
        baseInfo: BaseSessionInfo
    ): GotoSessionModel = GotoSessionModel(
        baseInfo = baseInfo,
        action = action.safe(),
        title = title.safe(),
        text = text.safe(),
        icon = icon.safe(),
        source = source.safe(-1),
        sourceStr = sourceStr.safe(),
        hasRedPacketTxt = hasRedPacketTxt
    )

    companion object {
        private val TYPE = "goto"
        fun sessionKeyFromId(id: Int): SessionKey {
            return SessionKey(TYPE, "${id}gotochat")
        }

        @JvmField
        val KEY_FLASH_CHAT = SessionKey(TYPE, Session.ID.FLASH_CHAT)

        @JvmField
        val FLASH_CHAT = KEY_FLASH_CHAT.value

        @JvmField
        val KEY_TEXT_CHAT = SessionKey(TYPE, Session.ID.TEXT_CHAT)

        @JvmField
        val TEXT_CHAT = KEY_TEXT_CHAT.value

        @JvmField
        val KEY_BRAIN_MATCH = SessionKey(TYPE, Session.ID.BRAIN_MATCH)

        @JvmField
        val BRAIN_MATCH = KEY_BRAIN_MATCH.value

        @JvmField
        val KEY_WHO_SAW_ME = SessionKey(TYPE, Session.ID.WHO_SAW_ME)//谁看过我运营位消息盒子
    }

    override fun removeOldSession(id: String) {
        SessionService.getInstance().deleteSession(id)
    }
}