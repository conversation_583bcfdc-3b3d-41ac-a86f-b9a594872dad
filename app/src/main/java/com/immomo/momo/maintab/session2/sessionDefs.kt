package com.immomo.momo.maintab.session2

import com.immomo.momo.maintab.session2.data.database.SessionContent
import com.immomo.momo.maintab.session2.defs.ActiveUserSessionDefinition
import com.immomo.momo.maintab.session2.defs.DebuggerSessionDefinition
import com.immomo.momo.maintab.session2.defs.DiscussChatSessionDefinition
import com.immomo.momo.maintab.session2.defs.FoldSessionDefinition
import com.immomo.momo.maintab.session2.defs.FriendNoticeSessionDefinition
import com.immomo.momo.maintab.session2.defs.GiftSayHiSessionDefinition
import com.immomo.momo.maintab.session2.defs.GotoSessionDefinition
import com.immomo.momo.maintab.session2.defs.GroupChatSessionDefinition
import com.immomo.momo.maintab.session2.defs.MaskChatSessionDefinition
import com.immomo.momo.maintab.session2.defs.PaasSessionDefinition
import com.immomo.momo.maintab.session2.defs.SayHiSessionDefinition
import com.immomo.momo.maintab.session2.defs.TextChatSessionDefinition
import com.immomo.momo.maintab.session2.defs.UniverseSessionDefinition
import com.immomo.momo.maintab.session2.defs.UserChatSessionDefinition
import com.immomo.momo.maintab.session2.defs.VChatSuperRoomSessionDefinition
import com.immomo.momo.maintab.session2.domain.model.SessionModel

val sessionDefs: List<SessionDefinition<out SessionContent, out SessionModel>> by lazy {
    val sessionDefinitions = mutableListOf(
        GiftSayHiSessionDefinition(),
        SayHiSessionDefinition(),
        DebuggerSessionDefinition(),
        UserChatSessionDefinition(),
        GroupChatSessionDefinition(),
        DiscussChatSessionDefinition(),
        VChatSuperRoomSessionDefinition(),
//        FlashChatSessionDefinition(),

        ActiveUserSessionDefinition(),
//        FriendDistanceSessionDefinition(),
        FriendNoticeSessionDefinition(),
        GotoSessionDefinition(),
        FoldSessionDefinition(),
        MaskChatSessionDefinition(),
        TextChatSessionDefinition(),
        UniverseSessionDefinition(),
        PaasSessionDefinition()
//        MatchSessionDefinition(),
//        OfficialSessionDefinition()
    )
    sessionDefinitions
}