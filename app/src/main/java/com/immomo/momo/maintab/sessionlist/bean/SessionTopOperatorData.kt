package com.immomo.momo.maintab.sessionlist.bean

import androidx.annotation.Keep
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import com.immomo.android.module.specific.data.mapper.safe
import java.io.Serializable

/**
 * 消息列表顶部的运营位
 */
@Keep
data class SessionTopOperatorData(
    @Expose @SerializedName("animationGuide") val animationGuide: AnimationGuide?,
    @Expose @SerializedName("list") val list: List<CardItemData>,
    @Expose @SerializedName("refreshIntervalSec") val refreshIntervalSec: Int,
    @Expose @SerializedName("version") val version: Long,
    @Expose @SerializedName("extend") val extend: HitEmotionExperiment?,
    @Transient var isFromLocalCache: Boolean = false, // 是否来自本地数据
    @Transient var requestTempTransparentParams: String? = "" // 客户端透传数据
) : Serializable {

    /**
     * 是否命中了新实验
     */
    fun isInEmotionExperiment() = extend?.hitEmotionExperiment.safe() == 1

}

@Keep
data class HitEmotionExperiment(
    @Expose @SerializedName("hitEmotionStyleExperiment") val hitEmotionExperiment: Int = 0
) : Serializable

/**
 * 引导的数据
 */
@Keep
data class AnimationGuide(
    @Expose @SerializedName("icon") val icon: String?,
    @Expose @SerializedName("title") val title: String?,
    @Expose @SerializedName("desc") val desc: String?,
    @Expose @SerializedName("duration") val duration: Int = 0
) : Serializable

/**
 * 每个卡片的数据
 */
@Keep
data class CardItemData(
    @Expose @SerializedName("funcTheme") val funcTheme: Int, // 1：普通静态卡片，2：摇动卡片
    @Expose @SerializedName("bumpCount") val bumpCount: Int, // 仅在funcTheme==2时生效
    @Expose @SerializedName("bizKey") val bizKey: String?,
    @Expose @SerializedName("version") val version: Long,
    @Expose @SerializedName("action") val action: String?,
    @Expose @SerializedName("logMap") val logMap: String?,
    @Expose @SerializedName("bgImg") val bgImg: CardBgImgData?,
    @Expose @SerializedName("title") val title: CardTitleData?,
    @Expose @SerializedName("subTitle") val subTitle: CardTitleData?,
    @Expose @SerializedName("bgColor") val bgColor: CardCommonDataNightDark?,
    @Expose @SerializedName("emotion") val emotion: CardBgImgData?,
    @Expose @SerializedName("button") val button: CardButtonData?,
    @Expose @SerializedName("avatars") val avatars: List<String>?,
    @Expose @SerializedName("avatarFrame") val avatarFrame: CardBgImgData?,
    @Expose @SerializedName("expand") val expand: CardExpandItemData?,
    @Expose @SerializedName("activityInfo") val activityInfo: ActivityInfo?,
    @Expose @SerializedName("desc") val desc: List<CardDescData>?,
    @Expose @SerializedName("icon") val icon: CardIconData?,
    @Transient var alreadyShownExpand: Boolean,
    @Transient var isReportCardState: Boolean
) : Serializable {
    fun isExpandState() = expand != null && !alreadyShownExpand
}

@Keep
data class ActivityInfo(
    @Expose @SerializedName("action") val action: String?,
    @Expose @SerializedName("delay") val delay: Long,
    @Expose @SerializedName("endFrame") val endFrame: Int?,
    @Expose @SerializedName("svga") val svga: String?,
    @Expose @SerializedName("darkSvga") val darkSvga: String?,
    @Expose @SerializedName("title") val title: CardTitleData?,
    @Expose @SerializedName("desc") val desc: CardDescData?
)

@Keep
data class CardButtonData(
    @Expose @SerializedName("text") val text: String,
    @Expose @SerializedName("action") val action: String,
    @Expose @SerializedName("bgColor") val bgColor: CardCommonDataNightDark?,
    @Expose @SerializedName("textColor") val textColor: CardCommonDataNightDark?,
    @Expose @SerializedName("bgImg") val bgImg: CardBgImgData?
) : Serializable

@Keep
data class CardBgImgData(
    @Expose @SerializedName("staticImg") val staticImg: String?,
    @Expose @SerializedName("animatedImg") val animatedImg: String?,
    @Expose @SerializedName("loopCount") val loopCount: Int,
    @Expose @SerializedName("light") val light: CardIconData?,
    @Expose @SerializedName("dark") val dark: CardIconData?
) : Serializable

@Keep
data class CardCommonDataNightDark(
    @Expose @SerializedName("light") val light: String?,
    @Expose @SerializedName("dark") val dark: String?
) : Serializable

@Keep
data class CardTitleData(
    @Expose @SerializedName("text") val text: String?,
    @Expose @SerializedName("color") val color: String?,
    @Expose @SerializedName("darkColor") val darkColor: String?,
    @Expose @SerializedName("bold") val bold: Int
) : Serializable

@Keep
data class CardDescData(
    @Expose @SerializedName("text") val text: String?,
    @Expose @SerializedName("color") val color: String?,
    @Expose @SerializedName("darkColor") val darkColor: String?,
    @Expose @SerializedName("bold") val bold: Int
) : Serializable

@Keep
data class CardIconData(
    @Expose @SerializedName("staticImg") val staticImg: String?,
    @Expose @SerializedName("animatedImg") val animatedImg: String?,
    @Expose @SerializedName("loopCount") val loopCount: Int
) : Serializable

/**
 * 卡片展开态数据
 */
@Keep
data class CardExpandItemData(
    @Expose @SerializedName("title") val title: CardTitleData?,
    @Expose @SerializedName("subTitle") val subTitle: CardTitleData?,
    @Expose @SerializedName("avatars") val avatars: List<String>?,
    @Expose @SerializedName("avatarFrame") val avatarFrame: CardBgImgData?,
    @Expose @SerializedName("cancelButton") val cancelButton: CardButtonData?,
    @Expose @SerializedName("submitButton") val submitButton: CardButtonData?,
    @Expose @SerializedName("action") val action: String?,
    @Expose @SerializedName("collapseInterval") val collapseInterval: Int?
) : Serializable {

    var costAnimationWaitTime: Long = 0L

    fun getSafeAniCostTime(collapseInterval: Long): Long {
        var spendDelayTime = collapseInterval
        if (costAnimationWaitTime > 0) {
            spendDelayTime = collapseInterval - costAnimationWaitTime
            if (spendDelayTime <= 0) {
                spendDelayTime = 1
            }
        }
        return spendDelayTime
    }
}