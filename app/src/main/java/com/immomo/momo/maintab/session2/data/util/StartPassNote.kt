package com.immomo.momo.maintab.session2.data.util

import android.content.Intent
import com.immomo.framework.storage.kv.KV
import com.immomo.mls.MLSBundleUtils
import com.immomo.momo.MomoKit
import com.immomo.momo.R
import com.immomo.momo.luaview.LuaViewActivity
import com.immomo.momo.maintab.session2.event.SessionInnerReceiver

/**
 * -----------------------------------------------------------------
 * Copyright (C)  sun
 * Create: 2022/6/6 11:08 AM
 * 跳转传纸条Lua页
 * -----------------------------------------------------------------
 */
object StartPassNote {
    fun start(action: String?) {
        KV.saveUserValue("KEY_IS_ENTER_SESSION_PASSNOTES", true)
        val activity = MomoKit.getTopActivity()
        action?.let {
            activity?.apply {
                SessionInnerReceiver.sendNoteReadBroadCast()
                val initData = MLSBundleUtils.createInitData(it)
                val intent = Intent(this, LuaViewActivity::class.java)
                intent.putExtras(MLSBundleUtils.createBundle(initData))
                startActivity(intent)
                overridePendingTransition(R.anim.anim_simple_fade_in, 0)
            }
        }
    }
}