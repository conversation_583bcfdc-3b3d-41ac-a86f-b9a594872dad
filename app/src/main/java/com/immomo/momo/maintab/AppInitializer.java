package com.immomo.momo.maintab;

import com.immomo.framework.account.MessageManager;
import com.immomo.momo.ExceptionCatcher;
import com.immomo.momo.MomoApplication;
import com.immomo.momo.MomoKit;
import com.immomo.momo.protocol.imjson.receiver.CityCardReceiver;
import com.immomo.momo.protocol.imjson.receiver.impl.GroupAudioChatReceiver;

/**
 * Created by tanjie on 2015-11-12.
 *
 * <AUTHOR>
 */
public class AppInitializer {

    public static void initAppRelated(MomoApplication momoApplication) {
        if (momoApplication.isCriticalInit()) {
            return;
        }
        MomoKit.initNotificationReceiver();
        MessageManager.registerReceiver(new CityCardReceiver());
        MessageManager.registerReceiver(new GroupAudioChatReceiver());

        //全局异常捕获器
        ExceptionCatcher exCatcher = ExceptionCatcher.getInstance();
        exCatcher.init(momoApplication);

        momoApplication.setIsCriticalInited(true);
    }
}
