package com.immomo.momo.maintab.sessionlist;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

/**
 * Created by huang.lian<PERSON><PERSON><PERSON> on 2018/5/15.
 * <p>
 * Momo Tech 2011-2018 © All Rights Reserved.
 */
public class SessionListReceiver extends BaseReceiver {
    public static final String ActionChangeMainFragment = MomoKit.getPackageName() + ".action.sessionlist.changeMainFragment";
    public static final String ActionRequestOnlineStatue = MomoKit.getPackageName() + ".action.sessionlist.request.onlineStatue";
    public static final String ActionIgnoreAllUnRead = MomoKit.getPackageName() + ".action.sessionlist.ignore.all.unread.message";
    public static final String ActionReLoadAll = MomoKit.getPackageName() + ".action.sessionlist.reload.all";
    public static final String KEY_SESSION_ID = "key_session_id";
    public static final String KEY_SWITCH_CHANGE = "key_switch_change";

    public SessionListReceiver(Context context) {
        super(context);
    }
}
