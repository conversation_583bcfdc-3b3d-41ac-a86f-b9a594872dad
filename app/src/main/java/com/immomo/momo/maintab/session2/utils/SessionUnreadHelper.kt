package com.immomo.momo.maintab.session2.utils

import com.immomo.momo.maintab.session2.data.manager.SessionManager

/**
 * CREATED BY liu.chong
 * AT 2025/1/16
 */
object SessionUnreadHelper {
    /**
     * 出了互动通知所有session的未读
     */
    fun getAllSessionUnread(): Int {
        return SessionManager.get().findAllSessions(filter = { mt ->
                    SessionFilterHelper.filterDisplaySessions(mt) &&
                    SessionFilterHelper.filterCountUnreadSessions(mt.sessionKey)
        }, mapper = { mt ->
            mt
        }).fold(0) { acc, mt ->
            acc + mt.unreadCount
        }
    }
}