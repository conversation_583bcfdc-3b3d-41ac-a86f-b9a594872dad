package com.immomo.momo.maintab.sessionlist

import com.immomo.android.module.nearbypeople.NearbyABTest.isNewBoyTest
import com.immomo.framework.storage.kv.KV
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.molive.kotlin.extensions.isNotNullOrEmpty
import com.immomo.momo.greendao.AppDBUtils
import com.immomo.momo.greendao.OldFriendEntityDao
import com.immomo.momo.maintab.session2.apt.SessionAppConfigV2Getter
import com.immomo.momo.maintab.session2.data.database.OldFriendEntity
import com.immomo.momo.maintab.session2.data.manager.SessionKey.Companion.fromString
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.defs.FoldSessionDefinition
import com.immomo.momo.maintab.sessionlist.NewBoySessionEnterHelper.Companion.getLastScanTime
import com.immomo.momo.messages.service.SingleMsgService
import com.immomo.momo.service.bean.FolderType
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.sessions.SessionService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

/**
 * 冷启时，对未回复的招呼进行迁移
 * 用户第一次安装通过扫描session表交给api校验未回复的招呼
 */
class NewBoySessionHelper {
    private var migrateJob: Job? = null

    private val isNewBoyTest = isNewBoyTest()


    /**
     * 首次安装通过api校验迁移
     *
     * @return
     * @throws Exception
     */
    private fun realMigrate() {
        //需要被校验的最大限制
        val maxCount = SessionAppConfigV2Getter.get().newfriendScanNum()
        val timeInterval = SessionAppConfigV2Getter.get().newFriendDefine() * 60 * 60 * 1000L

        val oldFriendDao =
            AppDBUtils.getInstance().getDao(OldFriendEntity::class.java) as OldFriendEntityDao

        val oldFriendList =
            oldFriendDao.queryBuilder().build().list().map { it.sessionId }.toSet()

        SessionManager.get().getNewBoyPossible(oldFriendList, maxCount).filter {
            val status = SingleMsgService.getInstance().isNewBoy(it, timeInterval)
            if (status == 0) {
                oldFriendDao.insertOrReplace(OldFriendEntity(it))
            }
            status == 1
        }.isNotNullOrEmpty {
            SessionService.getInstance()
                .updateSessionFolderType(FolderType.NewBOY, it.toTypedArray())
            SessionService.getInstance().updateFoldSession(Session.ID.NEW_BOY)
            NewBoySessionEnterHelper.updateScanTime(System.currentTimeMillis())
        }
    }

    fun destroy() {
        migrateJob?.cancel()
    }

    fun migrateToNewBoy() {
        if (migrateJob?.isActive == true || migrateJob?.isCompleted == true) return
        migrateJob = CoroutineScope(SupervisorJob() + MMDispatchers.User).launch {
            try {
                val hasNewBoyEnter = getNewBoyEnterStatus()
                if (isNewBoyTest
                    && System.currentTimeMillis() - getLastScanTime() > SessionAppConfigV2Getter.get()
                        .newfriendScanInterval()
                    && (hasNewBoyEnter || SessionManager.get()
                        .getSessionListSize() + 1 > SessionAppConfigV2Getter.get()
                        .newfriendScanMin())//getSessionListSize()+1,加的1表示互动通知cell
                ) {
                    realMigrate()
                }
            } catch (e: Exception) {
            }
        }
    }

    private fun getNewBoyEnterStatus(): Boolean {
        var hasEnter = SessionService.getInstance().checkExist(FoldSessionDefinition.NEW_BOY)
        if (!isNewBoyTest && hasEnter) {
            SessionService.getInstance()
                .updateSessionFolderType(FolderType.NewBOY, FolderType.Default)
            SessionManager.get().deleteSession(fromString(FoldSessionDefinition.NEW_BOY))
            (AppDBUtils.getInstance()
                .getDao(OldFriendEntity::class.java) as OldFriendEntityDao).deleteAll()
            hasEnter = false
        }
        return hasEnter
    }

}

class NewBoySessionEnterHelper {
    companion object {
        //是否满足展示"打出的招呼"入口条件
        private const val NEW_BOY_LAST_SCAN_TIME = "new_boy_last_scan_time"

        @kotlin.jvm.JvmStatic
        fun updateScanTime(lastScanTime: Long) {
            KV.saveUserValue(NEW_BOY_LAST_SCAN_TIME, lastScanTime)
        }

        @kotlin.jvm.JvmStatic
        fun getLastScanTime() = KV.getUserLong(NEW_BOY_LAST_SCAN_TIME, 0)

    }
}