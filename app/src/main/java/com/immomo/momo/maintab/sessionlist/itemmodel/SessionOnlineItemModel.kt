package com.immomo.momo.maintab.sessionlist.itemmodel

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.immomo.framework.cement.CementAdapter
import com.immomo.framework.cement.CementModel
import com.immomo.framework.cement.CementViewHolder
import com.immomo.framework.imageloader.ImageLoaderX
import com.immomo.framework.imageloader.ImageType
import com.immomo.momo.R
import com.immomo.momo.android.view.CircleImageView
import com.immomo.momo.maintab.model.TopEntryUser


class SessionOnlineItemModel(var user: TopEntryUser) : CementModel<SessionOnlineItemModel.ViewHolder>() {

    override fun getLayoutRes(): Int {
        return R.layout.item_session_online
    }

    override fun getViewHolderCreator(): CementAdapter.IViewHolderCreator<ViewHolder> {
        return CementAdapter.IViewHolderCreator { ViewHolder(it) }
    }

    override fun bindData(holder: ViewHolder) {
        super.bindData(holder)
        ImageLoaderX.load(user.avatar).type(ImageType.IMAGE_TYPE_URL).into(holder.iv_avatar)
        if (user.onlineStatusMark == 0){
            holder.iv_online.visibility = View.GONE
        } else {
            holder.iv_online.visibility = View.VISIBLE
        }
        holder.tv_desc.text = user.reason ?: ""
    }

    override fun unbind(holder: ViewHolder) {
        super.unbind(holder)
    }

    class ViewHolder(itemView: View) : CementViewHolder(itemView) {
        val iv_avatar = itemView.findViewById<CircleImageView>(R.id.iv_avatar)
        val iv_online = itemView.findViewById<ImageView>(R.id.iv_online)
        val tv_desc = itemView.findViewById<TextView>(R.id.tv_desc)
    }
}