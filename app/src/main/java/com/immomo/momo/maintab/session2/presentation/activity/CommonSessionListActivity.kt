package com.immomo.momo.maintab.session2.presentation.activity

import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.os.Handler
import android.util.Pair
import android.view.View
import androidx.core.util.toAndroidPair
import androidx.lifecycle.coroutineScope
import androidx.recyclerview.widget.RecyclerView
import com.immomo.android.mm.cement2.AsyncBuildSyntax
import com.immomo.android.mm.cement2.AsyncCementAdapter
import com.immomo.android.mm.cement2.AsyncCementModel
import com.immomo.android.mm.cement2.BuilderContext
import com.immomo.android.mm.cement2.CementModel
import com.immomo.android.mm.cement2.CementViewHolder
import com.immomo.android.mm.cement2.OnBuildCompleteListener
import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.android.mm.kobalt.presentation.view.cementBuilder
import com.immomo.android.mm.kobalt.presentation.viewmodel.Async
import com.immomo.android.mm.kobalt.presentation.viewmodel.KobaltView
import com.immomo.android.mm.kobalt.presentation.viewmodel.Loading
import com.immomo.android.mm.kobalt.presentation.viewmodel.pageViewModel
import com.immomo.android.module.specific.presentation.itemmodel.EmptyViewItemModel
import com.immomo.android.module.specific.presentation.itemmodel.LoadMoreItemModel
import com.immomo.android.module.specific.presentation.itemmodel.loadMoreState
import com.immomo.android.module.specific.presentation.view.KobaltRecyclerView
import com.immomo.framework.base.BaseActivity
import com.immomo.framework.utils.UIUtils
import com.immomo.framework.view.recyclerview.layoutmanager.LinearLayoutManagerWithSmoothScroller
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.momo.android.view.dialog.MSubTitleListDialog
import com.immomo.momo.brainmatch.session.presentation.itemmodel.BottomPaddingItemModel
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.domain.interactor.GetSessionModelListUseCase
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.SessionItemModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.SessionOnLongClickListener
import com.immomo.momo.maintab.session2.presentation.itemmodel.events.DefaultSessionOnAvatarClickListener
import com.immomo.momo.maintab.session2.presentation.itemmodel.events.DefaultSessionOnClickListener
import com.immomo.momo.maintab.session2.presentation.viewmodel.SessionListState
import com.immomo.momo.maintab.session2.presentation.viewmodel.SessionListViewModel
import com.immomo.momo.maintab.sessionlist.adapter.SessionListAdapter
import com.immomo.momo.statistics.logrecord.viewhelper.SessionListExposureLogHelper
import com.immomo.momo.statistics.logrecord.viewhelper.mode.ExposureModeWithHolder
import com.immomo.momo.voice.util.UIUtil
import kotlinx.coroutines.async


abstract class CommonSessionListActivity : BaseActivity(), KobaltView {
    companion object {
        const val ACTION_DELETE = "删除对话"
        val selectionTitleMap by lazy {
            listOf(ACTION_DELETE to "聊天记录将会被永久删除，请谨慎操作")
                .map { it.toAndroidPair() }
        }
    }

    private val sessionListRV by lazy {
        findViewById(kobaltRecyclerViewID()) as KobaltRecyclerView
    }

    /**
     * Session曝光延迟，可复写
     */
    open val sessionExposureDelay = 200L
    open val sessionPageSize = 20
    open val sessionListViewModel by pageViewModel(SessionListViewModel::class)
    private var logHelper: SessionListExposureLogHelper? = null
    private val sessionLm by lazy {
        object : LinearLayoutManagerWithSmoothScroller(this, VERTICAL, false) {
            //FIX: 重写此方法，是为了解决在 OPPO FindX 手机上，切换到Session列表时，自动滚动到 ActiveViewHolder 的问题
            override fun requestChildRectangleOnScreen(
                parent: RecyclerView,
                child: View,
                rect: Rect,
                immediate: Boolean,
                focusedChildVisible: Boolean
            ) = false

            override fun requestChildRectangleOnScreen(
                parent: RecyclerView,
                child: View,
                rect: Rect,
                immediate: Boolean
            ) = false
        }
    }
    private val scrollToTopListener = object : OnBuildCompleteListener {
        override fun onBuildComplete(context: BuilderContext) {
            sessionListRV.scrollToPosition(0)
            val _this = this
            lifecycle.coroutineScope.async(MMDispatchers.Main) {
                builder.removeOnBuildCompleteListener(_this)
            }
        }
    }

    abstract fun layoutResID(): Int

    abstract fun kobaltRecyclerViewID(): Int
    open fun onSessionClick(
        session: SessionModel, adapterPosition: Int,
        viewHolder: CementViewHolder
    ) = false

    open fun onSessionAvatarClick(session: SessionModel, adapterPosition: Int): Boolean = false

    override fun invalidate() {

    }

    final override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(layoutResID())
        initIntent(intent)
        initViews()
        initSubscribes()
        initData()
        sessionListViewModel.startLoop(this)
    }

    /**
     * 启动顺序：
     *          initIntent(intent)
     *         initViews()
     *         initPaasInfo()
     *         initSubscribes()
     *         initData()
     */
    open fun initIntent(intent: Intent?) {

    }

    /**
     * 启动顺序：
     *          initIntent(intent)
     *         initViews()
     *         initPaasInfo()
     *         initSubscribes()
     *         initData()
     */
    open fun initViews() {
        sessionListRV.visibleThreshold = 2
        sessionListRV.layoutManager = sessionLm
        sessionListRV.itemAnimator = null
        sessionListRV.adapter = builder.adapter
        logHelper = SessionListExposureLogHelper().also {
            it.thresholdTimeDelay = sessionExposureDelay
            it.setupWithRv(sessionListRV, builder.adapter)
        }
    }

    fun fullScreenOfStatusBar() {
        UIUtil.transparentStatusBar(this)
    }

    fun fullScreenOfNavigationBar() {
        UIUtil.transparentNavBar(this)
    }

    /**
     * 启动顺序：
     *          initIntent(intent)
     *         initViews()
     *         initPaasInfo()
     *         initSubscribes()
     *         initData()
     */
    open fun initSubscribes() {
        sessionListViewModel.selectSubscribe(SessionListState::firstAsync) {
            builder.triggerBuild()
        }
        sessionListViewModel.selectSubscribe(SessionListState::sessionList) {
            sessionListViewModel.addUserIdToRefresh(it.map { it.sessionKeyStr })
            builder.triggerBuild()
        }
        sessionListViewModel.selectSubscribe(SessionListState::loadMoreAsync) {
            builder.triggerBuild()
        }
        sessionListViewModel.selectSubscribe(SessionListState::scrollToTop) {
            builder.addOnBuildCompleteListener(scrollToTopListener)
        }
        SessionManager.get().observeSessionModelChange(this) {
            sessionListViewModel.addUserIdToRefresh(listOf(it.sessionKeyStr))
            sessionListViewModel.updateModel(it)
        }
    }

    /**
     * 启动顺序：
     *          initIntent(intent)
     *         initViews()
     *         initPaasInfo()
     *         initSubscribes()
     *         initData()
     */
    open fun initData() {
        sessionListViewModel.getSessionList()
    }

    override fun onResume() {
        super.onResume()
        logHelper?.exposureItems(sessionListRV)
    }


    open fun AsyncBuildSyntax.buildLoadingItemModel(): AsyncCementModel<*, *>? {
        return null
    }

    open fun AsyncBuildSyntax.buildSessionItemModel(model: SessionModel): AsyncCementModel<*, *> {
        return SessionItemModel(
            model,
            onItemClicked = object : DefaultSessionOnClickListener() {
                override fun onClicked(
                    view: View,
                    viewHolder: SessionItemModel.SessionViewHolder,
                    session: SessionModel,
                    adapterPosition: Int
                ) {
                    if (onSessionClick(session, adapterPosition, viewHolder).not()) {
                        super.onClicked(view, viewHolder, session, adapterPosition)
                    }
                }
            },
            onItemLongClicked = object : SessionOnLongClickListener {
                override fun onLongClicked(
                    view: View,
                    viewHolder: SessionItemModel.SessionViewHolder,
                    session: SessionModel,
                    adapterPosition: Int
                ) {
                    onSessionLongClick(view, viewHolder, session, adapterPosition)
                }

            },
            onAvatarClicked = object : DefaultSessionOnAvatarClickListener() {
                override fun onAvatarClicked(
                    view: View,
                    viewHolder: SessionItemModel.SessionViewHolder,
                    session: SessionModel,
                    adapterPosition: Int
                ) {
                    if (onSessionAvatarClick(session, adapterPosition).not()) {
                        super.onAvatarClicked(view, viewHolder, session, adapterPosition)
                    }
                }
            }
        )
    }

    open fun AsyncBuildSyntax.buildEmptyItemModel(): AsyncCementModel<*, *> {
        return EmptyViewItemModel("暂无消息").also { empty ->
            empty.setHeight(UIUtils.getPixels(530F))
        }
    }

    open fun AsyncBuildSyntax.buildLoadMoreItemModel(loadMoreAsync: Async<GetSessionModelListUseCase.SessionResult>): AsyncCementModel<*, *> {
        return LoadMoreItemModel(loadMoreAsync.loadMoreState) {
            sessionListViewModel.loadMore()
        }
    }

    open fun AsyncBuildSyntax.buildBottomSpaceItemModel(): AsyncCementModel<*, *> {
        return BottomPaddingItemModel("")
    }

    open fun onSessionLongClick(
        view: View,
        viewHolder: SessionItemModel.SessionViewHolder,
        model: SessionModel,
        adapterPosition: Int
    ) {
        val mAlertListDialog = MSubTitleListDialog(
            this,
            buildLongClickMenu()
        )
        mAlertListDialog.setOnItemSelectedListener { index: Int ->
            dealLongClickMenuAction(selectionTitleMap[index].first, model)
        }
        mAlertListDialog.setOnCancelListener {}
        mAlertListDialog.setOnShowListener {}
        mAlertListDialog.setSupportDark(true)
        showDialog(mAlertListDialog)
    }

    open fun buildLongClickMenu(): List<Pair<String, String>> = selectionTitleMap
    open fun dealLongClickMenuAction(action: String, model: SessionModel) {
        when (action) {
            ACTION_DELETE -> {
                sessionListViewModel.deleteSession(model.sessionKey)
            }
        }
    }

    open fun listAdapterFactory(): ((Handler) -> AsyncCementAdapter)? = null

    val builder by lazy {
        cementBuilder(
            sessionListViewModel, adapterFactory = listAdapterFactory() ?: {
                object : SessionListAdapter(sessionListRV, it) {
                    override fun onExposure(
                        context: Context,
                        position: Int,
                        holder: CementViewHolder
                    ) {
                        val model = getModel(position)
                        if (onInterruptSessionExposure(model, position, holder)) {
                            return
                        }
                        model.castOrNull<ExposureModeWithHolder>()
                            ?.onExposure(context, position, holder)
                    }
                }
            }
        ) {
            if (it.firstAsync is Loading) {
                this.buildLoadingItemModel()?.addToBuilder()
                return@cementBuilder
            }
            it.sessionList.forEach { m ->
                this.buildSessionItemModel(m).addToBuilder()
            }
            when {
                it.sessionList.isEmpty() -> {
                    this.buildEmptyItemModel().addToBuilder()
                }

                it.hasMore -> {
                    buildLoadMoreItemModel(it.loadMoreAsync).addToBuilder()
                }

                else -> {
                    //太靠底部不太合适，所以增加一个空项目
                    buildBottomSpaceItemModel().addToBuilder()
                }
            }
        }
    }

    open fun onInterruptSessionExposure(
        model: CementModel<*>?,
        position: Int,
        holder: CementViewHolder
    ): Boolean = false
}


