package com.immomo.momo.maintab.sessionlist.util

import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.storage.preference.SPKeys.User.SayHi.KEY_SAYHI_SESSION_RED_TEXT
import com.immomo.framework.storage.preference.SPKeys.User.TextChat.KEY_TEXT_CHAT_SESSION_RED_TEXT
import com.immomo.lcapt.evlog.EVLog
import com.immomo.mmutil.task.ThreadUtils
import com.immomo.momo.R
import com.immomo.momo.android.broadcast.ReflushVipReceiver
import com.immomo.momo.maintab.model.AbsSession
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.defs.GotoSessionDefinition
import com.immomo.momo.maintab.session2.defs.PaasSessionDefinition
import com.immomo.momo.maintab.session2.defs.UserChatContent
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.maintab.session2.domain.model.type.*
import com.immomo.momo.maintab.sessionlist.sort.ISessionSortLog
import com.immomo.momo.maintab.sessionlist.sort.apt.SessionSortConfigV2Getter
import com.immomo.momo.service.sessions.SessionService
import com.immomo.momo.service.sessions.ignoreUnreadCount
import com.immomo.momo.util.StringUtils

/**
 * <AUTHOR>
 * @data 2020-08-05.
 */

object SessionHelper {
    @JvmStatic
    fun ignoreUnreadCount(session: SessionModel?) {
        session?.let {
            ThreadUtils.execute(ThreadUtils.TYPE_RIGHT_NOW) {
                SessionService.getInstance().ignoreUnreadCount(it)
            }
        }
    }

    /**
     * session中是否要隐藏消息距离显示的条件
     */
    @JvmStatic
    fun hideMsgDistanceStrInSession(content: UserChatContent): Boolean {
        return content.distanceInfo < 0
                || content.isHongbao
                || content.isGift
                || content.isMissedFriendCall
                || content.isDianDianCard
                || content.isQuestionMatch
                || StringUtils.notEmpty(content.specialText)
                || content.canShowType28Prompt()
                || !content.isLastMessageReceive
                || content.userChatTag?.text.isNullOrBlank().not()
    }

    object SpecialText {
        @JvmStatic
        fun getSpecialTextColor(session: SessionModel): Int {
            val isSpecial = when (session) {
                is UserChatSessionModel -> {
                    session.isHongbao
                            || session.isGift
                            || session.isDianDianCard
                            || session.isQuestionMatch
                }

                is GroupChatSessionModel -> {
                    session.isHongbao || session.isGift
                }

                is DiscussChatSessionModel -> {
                    session.isHongbao || session.isGift
                }

                is VChatSuperRoomSessionModel -> {
                    session.hasVChatHongbao
                }

                is GiftSayHiSessionModel -> {
                    session.hasTopGift
                }

                is SayHiSessionModel -> {
                    session.hasRedPacketTxt || session.hasGiftTag
                }

                is GotoSessionModel -> {
                    session.hasRedPacketTxt
                }

                else -> false
            }
            return if (isSpecial) R.color.color_f7474b else R.color.color_323333_to_80f
        }

        @JvmStatic
        fun getSpecialText(session: SessionModel): String? {
            when (session) {
                is UserChatSessionModel -> {
                    // 红包 & 有人@我
                    return when {
                        session.isGift -> "有礼物"
                        session.isHongbao -> "红包"
                        session.isMissedFriendCall -> session.missedFriendCallDesc
                        session.isQuestionMatch ->
                            if (session.pushPrefix.isEmpty()) "点点匹配" else session.pushPrefix

                        session.isDianDianCard ->
                            if (session.pushPrefix.isEmpty()) "点点匹配" else session.pushPrefix

                        session.canShowType28Prompt() -> session.lastType28Prompt
                        session.hasDraft() -> "草稿"
                        StringUtils.notEmpty(session.specialText) -> session.specialText
                        else -> null
                    }
                }

                is GroupChatSessionModel -> {
                    // 红包 & 有人@我
                    return when {
                        session.isGift -> "有礼物"
                        session.isHongbao -> "红包"
                        session.isAtMe && session.atText.isNotEmpty() -> session.atText
                        session.hasDraft() -> "草稿"
                        else -> null
                    }
                }

                is DiscussChatSessionModel -> {
                    // 红包 & 有人@我
                    return when {
                        session.isGift -> "有礼物"
                        session.isHongbao -> "红包"
                        session.hasDraft() -> "草稿"
                        else -> null
                    }
                }

                is VChatSuperRoomSessionModel -> {
                    // 红包 & 有人@我
                    return when {
                        session.hasVChatHongbao -> "心心红包"
                        session.hasDraft() -> "草稿"
                        else -> null
                    }
                }

                is GiftSayHiSessionModel -> { // 礼物招呼
                    return when {
                        session.hasTopGift -> "置顶礼物"
                        else -> null
                    }
                }

                is SayHiSessionModel -> {
                    return when {
                        session.hasRedPacketTxt -> KV.getUserStr(KEY_SAYHI_SESSION_RED_TEXT, "")
                        session.hasGiftTag -> "有礼物招呼"
                        else -> null
                    }
                }

                is GotoSessionModel -> {
                    return when {
                        session.hasRedPacketTxt -> KV.getUserStr(KEY_TEXT_CHAT_SESSION_RED_TEXT, "")
                        else -> null
                    }
                }

                else -> return null
            }
        }
    }

    object ShimmerText {
        private val animedSessions = mutableListOf<String>()

        @JvmStatic
        fun markPlayed(chatId: String) {
            animedSessions.add(chatId)
        }

        @JvmStatic
        fun hasPlayed(chatId: String): Boolean {
            return animedSessions.contains(chatId)
        }
    }

    data class SessionLogParams(
        var whichItem: String,
        var newsNumber: Int,
        var position: Int,
        var remoteid: String,
        var isReddot: Boolean,
        var messageText: String?,
        var hiUserCount: Int,
        var hiUserCountUsable: Int,
        var preContent: String,
        var onlineText: String,
        var timeStr: String,
        var isRecommend: Boolean,
        var avatar: String = "",//头像是否模糊
        var source: String = "",
        var ifActivity: Boolean = false,
        var isDisplayFire:Int = 0
    ) {
        var sessionTagLogMap: String? = null
        var hasRedPacket = false

        var text: String? = null // session的副标题

        fun toMap(): MutableMap<String, String> {
            val paramsMap = mutableMapOf(
                Pair("which_item", whichItem),
                Pair("news_number", newsNumber.toString()),
                Pair("pos", "${position - 1}"),
                Pair("remoteid", remoteid.replace("gotochat", "")),
                Pair("is_reddot", if (isReddot) "1" else "0"),
                Pair("msg_text", messageText ?: ""),
                Pair("update_time", timeStr),
                Pair("online_time", onlineText),
                Pair("before_msg", preContent),
                Pair("is_recommend", if (isRecommend) "1" else "0"),
                Pair("avatar", avatar),
                Pair("source", source),
                Pair("is_display_fire", if (isDisplayFire == 1) "1" else "0")
            )
            sessionTagLogMap?.takeIf { it.isNotBlank() }?.also { // 用户标签上报
                paramsMap["sessionLogMap"] = it
            }
            if (whichItem == "sayhi") {
                val cellStatus: Int
                val headNumber: Int
                when {
                    hiUserCount > 0 -> {
                        cellStatus = 0
                        headNumber = hiUserCount
                    }

                    hiUserCountUsable > 0 -> {
                        cellStatus = 1
                        headNumber = hiUserCountUsable
                    }

                    else -> {
                        cellStatus = 2
                        headNumber = 0
                    }
                }
                paramsMap["cell_status"] = cellStatus.toString()
                paramsMap["head_number"] = headNumber.toString()
                if (StringUtils.isNotBlank(text)) {
                    paramsMap["text"] = text.safe()
                }
            } else if (whichItem == "box") {
                paramsMap["if_activity"] = if (ifActivity) "1" else "0"
            }

            return paramsMap
        }
    }

    object Log {
        @JvmStatic
        fun isP2pExposureLogEnable(): Boolean {
            return SessionSortConfigV2Getter.get().p2pExposureLogOpen() == 1
        }

        @JvmStatic
        fun getWhichItemByType(type: Int): String {
            return when (type) {
                AbsSession.TYPE_CHAT -> "personal"
                AbsSession.TYPE_SAYHI -> "sayhi"
                AbsSession.TYPE_GROUP -> "group"
                AbsSession.TYPE_DISCUSS -> "discussion"
                AbsSession.TYPE_GOTO -> "box"
                AbsSession.TYPE_FOLDER_OFFICIAL -> "subscribe"
                AbsSession.TYPE_FRIEND_NOTICE -> "remind"
                else -> ""
            }
        }

        @JvmStatic
        fun getWhichItemByType(session: SessionModel): String {
            return when (session) {
                is UserChatSessionModel -> "personal"
                is SayHiSessionModel -> "sayhi"
                is GiftSayHiSessionModel -> "gift_sayhi"
                is GroupChatSessionModel -> "group"
                is DiscussChatSessionModel -> "discussion"
                is GotoSessionModel -> "box"
                is OfficialSessionModel -> "subscribe"
                is FriendNoticeSessionModel -> "remind"
                is MsgFoldSessionModel -> "msg_fold_box"
                is GameBoxSessionModel -> "game_interact"
                is UniverseFoldSessionModel -> "microcosm"
                else -> ""
            }
        }

        @JvmStatic
        fun logSessionExposure(sessionLogParams: SessionLogParams) {
            sessionLogParams.remoteid =
                PaasSessionDefinition.userIdFromSessionId(sessionLogParams.remoteid)
            val paramsMap = sessionLogParams.toMap()
            EVLog.create(ISessionSortLog::class.java).logSessionExposure(paramsMap)
        }


        @JvmStatic
        fun logSessionClick(sessionLogParams: SessionLogParams) {
            sessionLogParams.remoteid =
                PaasSessionDefinition.userIdFromSessionId(sessionLogParams.remoteid)
            EVLog.create(ISessionSortLog::class.java).logSessionClick(sessionLogParams.toMap())
        }

        @JvmStatic
        fun logSessionLongClick(sessionLogParams: SessionLogParams) {
            sessionLogParams.remoteid =
                PaasSessionDefinition.userIdFromSessionId(sessionLogParams.remoteid)
            EVLog.create(ISessionSortLog::class.java).logSessionLongClick(sessionLogParams.toMap())
        }

        @JvmStatic
        fun logOptionDialogClick(session: SessionModel, position: String) {
            var remoteId = session.baseInfo.sessionId.replace("gotochat", "")
            remoteId = PaasSessionDefinition.userIdFromSessionId(remoteId)

            val whichItem = getWhichItemByType(session)
            EVLog.create(ISessionSortLog::class.java)
                .sessionOptionWindowDeleteClick(whichItem, remoteId, position)
        }
    }

    @JvmStatic
    fun hideWhoSawMeBox(action: String?) {
        when (action) {
            ReflushVipReceiver.ACTION_BECOMEVIP,
            ReflushVipReceiver.ACTION_BECOMESVIP -> {
                SessionManager.get().deleteSession(GotoSessionDefinition.KEY_WHO_SAW_ME)
            }
        }
    }
}