package com.immomo.momo.maintab.sessionlist.sort.data

import android.os.Parcel
import android.os.Parcelable
import com.immomo.android.module.specific.data.mapper.safe

class RecommendSessionsResponse() : Parcelable {
    var recommends: ArrayList<SessionInfo> = arrayListOf()
    var recommendTime = 0L

    constructor(parcel: Parcel) : this() {
        recommends = parcel.createTypedArrayList(SessionInfo) ?: arrayListOf()
        recommendTime = parcel.readLong()
    }

    class SessionInfo() : Parcelable {
        var momoid: String = ""
        var recommendTime = ""
        var chatTagStr = ""
        constructor(parcel: Parcel) : this() {
            momoid = parcel.readString() ?: ""
            recommendTime = parcel.readString() ?: ""
            chatTagStr = parcel.readString().safe()
        }

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeString(momoid)
            parcel.writeString(recommendTime)
            parcel.writeString(chatTagStr)
        }

        override fun describeContents(): Int {
            return 0
        }

        companion object CREATOR : Parcelable.Creator<SessionInfo> {
            override fun createFromParcel(parcel: Parcel): SessionInfo {
                return SessionInfo(parcel)
            }

            override fun newArray(size: Int): Array<SessionInfo?> {
                return arrayOfNulls(size)
            }
        }
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeTypedList(recommends)
        parcel.writeLong(recommendTime)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<RecommendSessionsResponse> {
        override fun createFromParcel(parcel: Parcel): RecommendSessionsResponse {
            return RecommendSessionsResponse(parcel)
        }

        override fun newArray(size: Int): Array<RecommendSessionsResponse?> {
            return arrayOfNulls(size)
        }
    }

}

