package com.immomo.momo.maintab.model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.immomo.momo.flashchat.datasource.bean.LastTimeFlashChat;
import com.immomo.momo.maintab.sessionlist.bean.SessionSpaceBean;

import java.util.List;

/**
 * Created by huang.lian<PERSON> on 2017/3/31.
 * <p>
 * Momo Tech 2011-2017 © All Rights Reserved.
 */

public class SessionActiveUser {
    @Expose
    @SerializedName("lists")
    public List<ActiveUser> userList;

    @Expose
    public int count;

    @Expose
    @SerializedName("activity_time")
    public long updateTime;

    @Expose
    public long interval;

    @Expose
    public int version;

    @Expose
    public String title;

    @Expose
    @SerializedName("top_entry")
    public ActiveUserTopEntry topEntry;

    @Expose
    @SerializedName("more_button")
    public ActiveUserMoreButton moreButton;

    @Expose
    @SerializedName("flash_chat")
    private LastTimeFlashChat flashChat;

    @Expose
    @SerializedName("card_list")
    public SessionSpaceBean spaceBean;

    public void setFlashChat(LastTimeFlashChat flashChat) {
        this.flashChat = flashChat;
    }

    public LastTimeFlashChat getFlashChat() {
        return flashChat;
    }

    public boolean isDataValid() {
        return userList != null && !userList.isEmpty();
    }
}


