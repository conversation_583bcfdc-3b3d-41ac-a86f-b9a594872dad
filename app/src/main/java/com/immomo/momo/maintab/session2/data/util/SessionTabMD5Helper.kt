package com.immomo.momo.maintab.session2.data.util

import com.immomo.framework.storage.kv.KV
import com.immomo.framework.storage.preference.SPKeys

/**
 *
 * author: hongming.wei
 * data: 2022/7/17
 */
class SessionTabMD5Helper {
    private val isChatBubble: Boolean = KV.getUserInt(SPKeys.System.AppMultiConfig.KEY_SESSION_LIST_SHOW_CHAT_BUBBLE, 0) == 1
    private val sessionTime: Int = KV.getUserInt(SPKeys.System.AppMultiConfig.KEY_SESSION_LIST_TIME_ON_LINE_ENABLE, 0)
    private val isChatTab: Boolean = KV.getUserInt(SPKeys.System.AppMultiConfig.KEY_SESSION_LIST_INTERACTION_ON_CHAT_TAB, 0) == 1

    companion object {

        private var instance: SessionTabMD5Helper? = null

        fun getInstance(): SessionTabMD5Helper?{
            if (instance == null) {
                synchronized(SessionTabMD5Helper.javaClass){
                    if (instance == null) {
                        instance = SessionTabMD5Helper()
                    }
                }
            }
            return instance
        }
    }

    fun destroy() {
        instance = null
    }

    fun isSingleChatBubble(): Boolean{
        return isChatBubble
    }

    fun getSessionTime(): Int {
        return sessionTime
    }

    fun isSessionInteraction(): Boolean {
        return isChatTab
    }



}