package com.immomo.momo.maintab.session2

import com.immomo.lcapt.evlog.anno.ClickPoint
import com.immomo.lcapt.evlog.anno.ExposurePoint
import com.immomo.lcapt.evlog.anno.Param

interface IFoldSessionLog {
    /**
     * 折叠消息的问号点击
     */
    @ClickPoint(requireId = "18331", page = "msg.chatlist", action = "list.question_mark ")
    fun logMsgFoldHelpClick(@Param("position") pos: Int)

    @ExposurePoint(requireId = "18352", page = "msg.fold_box", action = "content.read_no_answer")
    fun logFoldSessionShow(
        @Param("which_item") whichItem: String,
        @Param("msgText") msgText: String,
        @Param("remoteid") remoteId: String
    )

    @ClickPoint(requireId = "18350", page = "msg.fold_box", action = "content.read_no_answer")
    fun logFoldSessionClick(
        @Param("which_item") whichItem: String,
        @Param("msgText") msgText: String,
        @Param("remoteid") remoteId: String
    )

    @ClickPoint(requireId = "18334", page = "msg.subscribe", action = "list.one_click_read ")
    fun logCleanUnreadClick(@Param("which_item") whichItem: String)

    /**
     * 消息-游戏互动消息页内cell曝光
     */
    @ExposurePoint(requireId = "18468", page = "msg.gamelist", action = "list.card")
    fun logGameBoxListCellExp(
        @Param("remoteid") remoteid: String, @Param("msg_text") msgText: String,
        @Param("pos") pos: String, @Param("news_number") newsNumber: String
    )

    /**
     * 消息-游戏互动消息页内cell点击
     */
    @ClickPoint(requireId = "18469", page = "msg.gamelist", action = "list.card")
    fun logGameBoxListCellClick(
        @Param("remoteid") remoteid: String, @Param("msg_text") msgText: String,
        @Param("pos") pos: String, @Param("news_number") newsNumber: String
    )

    /**
     * 已读按钮点击-后再点击确定
     */
    @ClickPoint(requireId = "18485", page = "msg.gamelist", action = "list.one_click_read")
    fun logGameBoxUnreadClick()

}