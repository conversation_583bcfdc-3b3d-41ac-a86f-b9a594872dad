package com.immomo.momo.maintab.sessionlist.util

import com.cosmos.mdlog.MDLog
import com.immomo.mmstatistics.event.ClickEvent
import com.immomo.mmstatistics.event.ExposureEvent
import com.immomo.momo.maintab.session2.domain.topoperator.SessionTopOperatorManager
import com.immomo.momo.statistics.EVAction
import com.immomo.momo.statistics.EVPage
import com.mln.watcher.safe

/**
 * 打点记录
 */
object SessionTopOperatorRecord {

    @JvmStatic
    fun cardClick(type: String?, logMap: String?) {
        ClickEvent.create()
            .page(EVPage.Msg.Chatlist)
            .action(EVAction.Content.Match)
            .requireId("18444")
            .putExtra("type", type.safe())
            .putExtra("logMap", logMap.safe())
            .submit()
    }

    @JvmStatic
    fun cardExp(type: String?, isExpand: Int, logMap: String?) {
        ExposureEvent.create(ExposureEvent.Type.Normal)
            .page(EVPage.Msg.Chatlist)
            .action(EVAction.Content.Match)
            .requireId("19257")
            .putExtra("type", type.safe())
            .putExtra("is_expand", isExpand.toString())
            .putExtra("logMap", logMap.safe())
            .submit()
    }

    /**
     * 滑动方向
     */
    @JvmStatic
    fun cardScroll(oritation: Int) {
        val oritationString = if (oritation == 1) "right" else "left"
        ClickEvent.create()
            .page(EVPage.Msg.Chatlist)
            .action(EVAction.Content.Sliding)
            .requireId("18445")
            .putExtra("direction", oritationString)
            .submit()
        MDLog.i(
            SessionTopOperatorManager.TAG,
            "SessionTopOperatorRecord cardScroll=$oritationString"
        )
    }

    @JvmStatic
    fun cardCallClick(button: String?) {
        ClickEvent.create()
            .page(EVPage.Msg.Chatlist)
            .action(EVAction.Head.AudioEffent)
            .requireId("19245")
            .putExtra("button", button.safe())
            .submit()
    }

    @JvmStatic
    fun dialogJumpClick(button: String?) {
        ClickEvent.create()
            .page(EVPage.AudioTalk.ChatEnd)
            .action(EVAction.Window.AudioGuide)
            .requireId("19243")
            .putExtra("button", button.safe())
            .submit()
    }

    @JvmStatic
    fun dialogJumpExp() {
        ExposureEvent.create(ExposureEvent.Type.Normal)
            .page(EVPage.AudioTalk.ChatEnd)
            .action(EVAction.Window.AudioGuide)
            .requireId("19242")
            .submit()
    }

}