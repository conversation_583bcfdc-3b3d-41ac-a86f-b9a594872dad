package com.immomo.momo.maintab;

import android.text.TextUtils;

import com.cosmos.mdlog.MDLog;
import com.immomo.LogTag;
import com.immomo.downloader.DownloadManager;
import com.immomo.downloader.bean.DownloadConstant;
import com.immomo.downloader.bean.DownloadTask;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.mmutil.FileUtil;
import com.immomo.mmutil.StringUtils;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.task.ThreadUtils;
import com.immomo.momo.MomoKit;
import com.immomo.momo.service.bean.MainTabBottomTheme;
import com.immomo.momo.util.GsonUtils;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class MainTabReplaceIconUtils {
    private String themePath;
    private static final String CONFIG_JSON_FILE_NAME = "config.json";
    private File zipFile;
    private String configFilePath;
    private DownloadTask downloadTask;

    private static class MainTabReplaceIconUtilsHolder {
        private static MainTabReplaceIconUtils instance = new MainTabReplaceIconUtils();
    }

    public static MainTabReplaceIconUtils getInstance() {
        return MainTabReplaceIconUtilsHolder.instance;
    }

    private MainTabReplaceIconUtils() {
        themePath = MomoKit.getContext().getFilesDir() + "/theme/";
        File file = new File(themePath);
        if (!file.exists()) {
            file.mkdirs();
        }
        zipFile = FileUtil.newFile(file, "configFile.zip");
        configFilePath = themePath + "config/";

    }

    public void checkUpdateFile(String version, String zipUrl) {
        ThreadUtils.execute(ThreadUtils.TYPE_RIGHT_NOW, () -> {
            KV.saveSysValue(SPKeys.System.AppMultiConfig.KEY_IS_NEED_REPLACE_MAIN_TAB_ICON, false);
            if (StringUtils.isEmpty(version) || StringUtils.isEmpty(zipUrl)) {
                return;
            }

            String replaceMainTabIconOldUrl = KV.getSysStr(SPKeys.System.AppMultiConfig.KEY_REPLACE_MAIN_TAB_ICON_ZIP_URL, "");
            String replaceMainTabIconOldVersion = KV.getSysStr(SPKeys.System.AppMultiConfig.KEY_REPLACE_MAIN_TAB_ICON_VERSION, "");
            if (!StringUtils.equalsNonNull(replaceMainTabIconOldVersion, version)) {
                //版本不一致时，更新url
                downloadZip(zipUrl, version);
                return;
            }
            if (checkFileValid() == null) {
                downloadZip(replaceMainTabIconOldUrl, replaceMainTabIconOldVersion);
                return;
            }
            KV.saveSysValue(SPKeys.System.AppMultiConfig.KEY_IS_NEED_REPLACE_MAIN_TAB_ICON, true);
        });
    }


    /**
     * 判断是否有config.json文件，并验证完整性
     */
    public HashMap<String, MainTabBottomTheme.SkinConfig> checkFileValid() {
        File file = new File(configFilePath + CONFIG_JSON_FILE_NAME);
        if (!file.exists()) {
            return null;
        }
        HashMap<String, MainTabBottomTheme.SkinConfig> skinConfigHashMap = null;

        try {
            String configJson = FileUtil.readStr(file);
            if (StringUtils.isEmpty(configJson)) {
                return null;
            }

            MainTabBottomTheme mainTabBottomTheme = GsonUtils.g().fromJson(configJson, MainTabBottomTheme.class);
            if (mainTabBottomTheme == null || mainTabBottomTheme.skinConfig == null) {
                return null;
            }
            skinConfigHashMap = mainTabBottomTheme.skinConfig;

            for (Map.Entry<String, MainTabBottomTheme.SkinConfig> entry: skinConfigHashMap.entrySet()) {
                MainTabBottomTheme.SkinConfig skinConfig = entry.getValue();
                if (entry.getKey() == null || skinConfig == null) {
                    return null;
                }

                if (TextUtils.isEmpty(skinConfig.svgaSuffix) || StringUtils.isEmpty(skinConfig.bottomName)) {
                    return null;
                }
                skinConfig.svgaPath = configFilePath + skinConfig.svgaSuffix;
            }

        } catch (IOException e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
        return skinConfigHashMap;
    }

    public void downloadZip(String zipUrl, String version) {
        if (downloadTask == null) {
            String taskID = StringUtils.md5(zipUrl);
            downloadTask = new DownloadTask();
            downloadTask.isShowNotify = false;
            downloadTask.downloadType = DownloadConstant.DOWNLOAD_TYPE_COMMON;
            downloadTask.sourceUrl = zipUrl;
            downloadTask.taskID = taskID;
            downloadTask.savePath = zipFile.getAbsolutePath();
            downloadTask.setNeedCoo(false);
            DownloadManager.getInstance().add(downloadTask, new DownloadManager.DownloadListener() {
                @Override
                public void onStart(DownloadManager manager, DownloadTask task) {

                }

                @Override
                public void onProcess(DownloadManager manager, DownloadTask task) {

                }

                @Override
                public void onPause(DownloadManager manager, DownloadTask task) {

                }

                @Override
                public void onCancel(DownloadManager manager, DownloadTask task) {
                    downloadTask = null;
                }

                @Override
                public void onFailed(DownloadManager manager, DownloadTask task, int cause) {
                    downloadTask = null;
                }

                @Override
                public void onCompleted(DownloadManager manager, DownloadTask task) {
                    if (task == null || !StringUtils.equalsNonNull(taskID, task.taskID)) {
                        return;
                    }
                    downloadTask = null;
                    //删除解压后的文件夹
                    File file = new File(configFilePath);
                    FileUtil.deleteDir(file);
                    //压缩文件
                    MomoTaskExecutor.executeUserTask(zipUrl, new MomoTaskExecutor.Task() {
                        @Override
                        protected Object executeTask(Object[] objects) throws Exception {
                            boolean isUnzipSuccess = FileUtil.unzip(zipFile.getAbsolutePath(), themePath, true);
                            if (isUnzipSuccess) {
                                KV.saveSysValue(SPKeys.System.AppMultiConfig.KEY_REPLACE_MAIN_TAB_ICON_ZIP_URL, zipUrl);
                                KV.saveSysValue(SPKeys.System.AppMultiConfig.KEY_REPLACE_MAIN_TAB_ICON_VERSION, version);
                                KV.saveSysValue(SPKeys.System.AppMultiConfig.KEY_IS_NEED_REPLACE_MAIN_TAB_ICON, true);
                            }

                            return isUnzipSuccess;
                        }
                    });
                }
            });
        }


    }

}
