package com.immomo.momo.maintab.sessionlist.util;

import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.momo.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class DelMsgIDsManager {
    private List<String> delIDsList = new ArrayList<>();
    private static DelMsgIDsManager instance;

    private DelMsgIDsManager() {
        String delIDsStr = KV.getUserStr(SPKeys.System.AppMultiConfig.KEY_DEL_MSG_IDS, "");
        if (StringUtils.notEmpty(delIDsStr)) {
            this.delIDsList = Arrays.asList(delIDsStr.split(","));
        }
    }

    public static synchronized DelMsgIDsManager getInstance() {
        if (null == instance) {
            instance = new DelMsgIDsManager();
        }
        return instance;
    }

    public boolean containsSession(String sessionId) {
        return StringUtils.notEmpty(sessionId) && delIDsList.contains(sessionId.replace("gotochat", ""));
    }

    public void release() {
        instance = null;
    }
}
