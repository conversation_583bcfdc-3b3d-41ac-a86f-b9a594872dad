package com.immomo.momo.maintab.sessionlist.usecase

import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.rxjava.interactor.UseCase
import com.immomo.mmutil.task.MMThreadExecutors
import com.immomo.momo.android.service.test.data.UserTestingConfigData
import com.immomo.momo.protocol.http.core.HttpClient
import com.immomo.momo.util.GsonUtils
import io.reactivex.Flowable
import org.json.JSONObject

/**
 * 消息帧模块 行为回调
 */
class SessionTopActionUseCase(val bizKey: String?, val action: String?) :
    UseCase<String, String>(
        MMThreadExecutors.User,
        MMThreadExecutors.Main
    ) {
    override fun buildUseCaseFlowable(params: String?): Flowable<String> {
        return Flowable.fromCallable<String> { request() }
    }

    @Throws(Exception::class)
    private fun request(): String {
        return HttpClient.doPost(
            HttpClient.HttpsHost + "/v4/relation/message/module/action",
            HashMap<String?, String?>().apply {
                put("bizKey", bizKey.safe())
                put("action", action.safe())
            }
        )
    }
}