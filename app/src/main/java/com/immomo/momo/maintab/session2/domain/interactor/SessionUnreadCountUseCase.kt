package com.immomo.momo.maintab.session2.domain.interactor

import com.immomo.android.mm.kobalt.domain.exception.NoParamProvided
import com.immomo.android.mm.kobalt.domain.fx.Option
import com.immomo.android.mm.kobalt.domain.interactor.UseCase
import com.immomo.momo.maintab.session2.domain.repository.ISessionListRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow

/**
 * 消息帧顶部匹配运营位
 */
class SessionUnreadCountUseCase(
    dispatcher: CoroutineDispatcher,
    private val repository: ISessionListRepository
) : UseCase<Int, String>(dispatcher) {
    override fun build(param: Option<String>): Flow<Int> {
        return param.fold({ throw NoParamProvided() }) {
            repository.getAllUnreadCount()
        }
    }
}
