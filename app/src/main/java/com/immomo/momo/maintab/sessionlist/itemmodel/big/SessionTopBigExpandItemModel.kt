package com.immomo.momo.maintab.sessionlist.itemmodel.big

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.os.SystemClock
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.doOnEnd
import androidx.core.view.updateLayoutParams
import com.cosmos.mdlog.MDLog
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.rxjava.interactor.CommonSubscriber
import com.immomo.framework.view.dp
import com.immomo.mmutil.task.MomoMainThreadExecutor
import com.immomo.molive.kotlin.extensions.isNotNullOrEmpty
import com.immomo.momo.gotologic.GotoDispatcher
import com.immomo.momo.maintab.sessionlist.bean.CardBgImgData
import com.immomo.momo.maintab.sessionlist.bean.CardItemData
import com.immomo.momo.maintab.sessionlist.usecase.SessionTopActionUseCase
import com.immomo.momo.maintab.sessionlist.util.SessionTopOperatorRecord
import com.immomo.momo.util.MomoKit
import com.immomo.svgaplayer.view.MomoSVGAImageView
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 展开态入口
 */
class SessionTopBigExpandItemModel(itemModeOriginData: CardItemData) :
    BaseSessionTopBigItemModel(itemModeOriginData) {

    private val TAG_REQUEST_CARD_STATE = "request_card_state"
    private var lastStartAniWaitTime = 0L // 上次启动动画的时间
    private var isPostingRecord = AtomicBoolean() // 是否正在发送折叠记录

    private fun getTaskKey() = TAG_REQUEST_CARD_STATE + itemModeData.bizKey

    override fun bindData(holder: ViewHolder) {
        super.bindData(holder)
        isPostingRecord.set(false)
        refreshHolderView(holder)
    }

    override fun refreshHolderView(holder: ViewHolder) {
        super.refreshHolderView(holder)
        kotlin.runCatching {
            holder.normalTitleContainer.setPadding(AVATAR_CARD_PADDING_SIZE, 0, 0, 0)
            clearAnimation()
            if (itemModeData.alreadyShownExpand) {
                holder.itemView.updateLayoutParams<MarginLayoutParams> { // 展示大卡
                    width = MIDDLE_CARD_SIZE
                }
                releaseExpandViews(holder)
                refreshNormalViewData(holder)
                showNormalView(holder)
            } else {
                refreshNormalViewData(holder)
                bindExpandStateView(holder)
            }
        }
    }

    /**
     * 展开状态下的view
     */
    private fun bindExpandStateView(holder: ViewHolder) {
        val cardExpandItemData = itemModeData.expand ?: return
        holder.itemView.updateLayoutParams<MarginLayoutParams> { // 展示大卡
            width = BIG_CARD_SIZE
        }
        // 隐藏小卡状态
        holder.normalTitleContainer.visibility = View.GONE
        holder.itemBgSvga.visibility = View.GONE
        holder.btnTxtContainer.visibility = View.GONE
        holder.expandHeadContainer.visibility = View.VISIBLE
        holder.expandTitleContainer.visibility = View.VISIBLE
        holder.expandBottomBtnContainer.visibility = View.VISIBLE
        holder.expandBtnLeftSvga.stopAnimCompletely()
        holder.expandBtnRightSvga.stopAnimCompletely()
        holder.expandHeadAllContainer.visibility = View.VISIBLE
        setCommonTitle(holder.expandItemTitle, cardExpandItemData.title)
        setCommonTitle(holder.expandItemSubTitle, cardExpandItemData.subTitle)
        refreshHeadBanner(true, fromExpand = true) // 处理静态或者动态展开头像
        refreshHeadBanner(false) // 预加载头像
        buildBtnSvgaData(
            cardExpandItemData.cancelButton?.bgImg,
            holder.expandBtnLeftSvga,
            holder.expandBtnLeft,
            holder.expandBtnContainerLeft
        ) {
            val action = cardExpandItemData.cancelButton?.action
            if (action.isNotNullOrEmpty()) {
                GotoDispatcher.action(action, holder.itemView.context).execute()
            }
            startTransScaleOut(holder)
            SessionTopOperatorRecord.cardCallClick("hang_up")
            checkReportCardState("voice_expand_cancel", isPostingRecord)
        }
        buildBtnSvgaData(
            cardExpandItemData.submitButton?.bgImg,
            holder.expandBtnRightSvga,
            holder.expandBtnRight,
            holder.expandBtnContainerRight
        ) {
            val safeGoto = getSafeGoto(cardExpandItemData.submitButton?.action)
            if (safeGoto.isNotNullOrEmpty()) {
                GotoDispatcher.action(safeGoto, holder.itemView.context).execute()
            }
            startTransScaleOut(holder)
            SessionTopOperatorRecord.cardCallClick("answer")
            checkReportCardState("voice_expand_submit", isPostingRecord)
        }
        val collapseInterval = cardExpandItemData.collapseInterval.safe(5).toLong() * 1000
        if (collapseInterval > 0) {
            val spendDelayTime =
                cardExpandItemData.getSafeAniCostTime(collapseInterval) // 获取动画已经花费的时间
            MomoMainThreadExecutor.postDelayed(hashCode(), {
                startTransScaleOut(holder)
            }, spendDelayTime)
            if (!itemModeData.isReportCardState && !isPostingRecord.get()) {
                isPostingRecord.set(true)
                MomoMainThreadExecutor.postDelayed(getTaskKey(), {
                    checkReportCardState("voice_expand_auto_fold", isPostingRecord)
                }, collapseInterval)
            }
            lastStartAniWaitTime = SystemClock.uptimeMillis()
        }
    }

    private fun buildBtnSvgaData(
        bgImgData: CardBgImgData?,
        svgaView: MomoSVGAImageView,
        imageView: ImageView,
        container: ConstraintLayout,
        clickCallback: (() -> Unit)? = null
    ) {
        bgImgData ?: run {
            container.visibility = View.GONE
            return
        }
        val cardIconData = if (MomoKit.isDarkMode()) bgImgData.dark else bgImgData.light
        val animatedImg = cardIconData?.animatedImg.safe()
        val staticImg = cardIconData?.staticImg.safe()
        if (animatedImg.isNotBlank()) {
            svgaView.startSVGAAnim(animatedImg, -1)
            svgaView.visibility = View.VISIBLE
            imageView.visibility = View.GONE
        } else if (staticImg.isNotBlank()) {
            showCommonStaticIcon(imageView, staticImg)
            svgaView.visibility = View.GONE
            imageView.visibility = View.VISIBLE
        } else {
            svgaView.visibility = View.GONE
            imageView.visibility = View.GONE
        }
        container.setOnClickListener {
            clickCallback?.invoke()
        }
    }

    private fun startTransScaleOut(holder: ViewHolder) {
        MomoMainThreadExecutor.cancelAllRunnables(hashCode())
        itemModeData.alreadyShownExpand = true
        val titleTranslationOffset = (-28).dp
        val animator = ValueAnimator.ofFloat(0F, 1F)
        val animatorTime = 200L
        animator.duration = animatorTime
        animator.addUpdateListener {
            val factor = it.animatedValue as Float
            val w = BIG_CARD_SIZE - (BIG_CARD_SIZE - MIDDLE_CARD_SIZE) * factor
            MDLog.d(TAG, "w:$w,,factor:$factor")
            holder.itemView.updateLayoutParams<MarginLayoutParams> {
                width = w.toInt()
            }
            val fadeOutFactor = 1 - factor
            holder.normalTitleContainer.alpha = factor
            holder.expandTitleContainer.alpha = fadeOutFactor
            holder.normalTitleContainer.translationY = -titleTranslationOffset * fadeOutFactor
            holder.expandTitleContainer.translationY = titleTranslationOffset * factor
        }
        animator.doOnEnd {
            releaseExpandViews(holder)
        }
        animator.start()
        val normalHeadAniSet = AnimatorSet()
        val scaleXNormalHead =
            ObjectAnimator.ofFloat(holder.normalHeadAllContainer, "scaleX", 0f, 1f)
        val scaleYNormalHead =
            ObjectAnimator.ofFloat(holder.normalHeadAllContainer, "scaleY", 0f, 1f)
        val alphaNormalHead =
            ObjectAnimator.ofFloat(holder.normalHeadAllContainer, "alpha", 0f, 1f)
        val alphaNormalBottomBtn =
            ObjectAnimator.ofFloat(holder.btnTxtContainer, "alpha", 0f, 1f)
        val alphaNormalSvgaBg = ObjectAnimator.ofFloat(holder.itemBgSvga, "alpha", 0f, 1f)
        normalHeadAniSet.duration = animatorTime
        normalHeadAniSet.playTogether(
            scaleXNormalHead,
            scaleYNormalHead,
            alphaNormalHead,
            alphaNormalBottomBtn,
            alphaNormalSvgaBg
        )
        normalHeadAniSet.start()
        showNormalView(holder)
        runningAnimators.add(animator)
        runningAnimators.add(normalHeadAniSet)
    }

    private fun releaseExpandViews(holder: ViewHolder) {
        holder.expandTitleContainer.visibility = View.GONE
        holder.expandBtnContainerLeft.visibility = View.GONE
        holder.expandBtnContainerRight.visibility = View.GONE
        holder.expandBtnRightSvga.stopAnimCompletely()
        holder.expandBtnLeftSvga.stopAnimCompletely()
        holder.expandHeadSvga.stopAnimCompletely()
    }

    fun expandBgClickJump() {
        kotlin.runCatching {
            curViewHolder?.let {
                startTransScaleOut(it)
                checkReportCardState("voice_expand_click_module", isPostingRecord)
            }
        }
    }

    override fun unbind(holder: ViewHolder) {
        super.unbind(holder)
        isPostingRecord.set(false)
        MomoMainThreadExecutor.cancelAllRunnables(getTaskKey())
        clearAnimation()
    }

    private fun clearAnimation() {
        if (!itemModeData.alreadyShownExpand && lastStartAniWaitTime > 0) {
            itemModeData.expand?.also {
                it.costAnimationWaitTime += (SystemClock.uptimeMillis() - lastStartAniWaitTime)
            }
        }
        MomoMainThreadExecutor.cancelAllRunnables(hashCode())
        runningAnimators.forEach { it.cancel() }
        runningAnimators.clear()
    }

    /**
     * 检查上报卡片的状态
     */
    private fun checkReportCardState(action: String, isPostingRecord: AtomicBoolean) {
        MomoMainThreadExecutor.cancelAllRunnables(getTaskKey())
        if (!itemModeData.isReportCardState) {
            itemModeData.isReportCardState = true
            SessionTopActionUseCase(itemModeData.bizKey, action).execute(object :
                CommonSubscriber<String>() {
                override fun onError(exception: Throwable?) {}
            })
        }
        isPostingRecord.set(false)
    }

    override fun release() {
        super.release()
        MomoMainThreadExecutor.cancelAllRunnables(hashCode())
        MomoMainThreadExecutor.cancelAllRunnables(getTaskKey())
    }
}