package com.immomo.momo.maintab.session2.domain.model.type

import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo

data class VChatSuperRoomSessionModel(
    override val baseInfo: BaseSessionInfo,
    override val chatId: String,
    override val desc: String,
    override val draftString: String,
    override val draftQuoteString: String,
    override val lastMessageType: Int,
    override val showMessageStatus: Boolean,
    override val lastMessageStatus: Int,
    val vChatAvatar: String,
    val vChatName: String,
    /**
     * 是否有聊天室心心红包
     */
    val hasVChatHongbao: Boolean
    ) : ChatSessionModel {
    override fun hasSpecialNotice(): Bo<PERSON>an {
        return hasVChatHongbao
    }
}