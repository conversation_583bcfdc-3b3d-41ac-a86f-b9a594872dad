package com.immomo.momo.maintab.session2.data.util

import com.immomo.framework.storage.kv.KV
import com.immomo.framework.utils.NeedOffline
import com.immomo.momo.maintab.session2.data.database.SessionService
import com.immomo.momo.maintab.session2.defs.FriendNoticeSessionDefinition
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.maintab.session2.domain.model.type.FriendNoticeSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.GotoSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.UserChatSessionModel

/**
 * CREATED BY liu.chong
 * AT 2022/3/4
 */
class SessionTabTestHelper {
    companion object {
        const val GROUP_KEY = "klgroup"
        const val GROUP_A = "dqcagvyd_A"
        const val GROUP_B = "dqcagvyd_B"

        const val FOLD_TYPE_TOP_OPEN = 1

        const val KEY_MIN_FOLD_COUNT = "KEY_MIN_FOLD_COUNT"
        const val DEFAULT_MIN_FOLD_COUNT = 3

        const val KEY_FOLD_SESSION_NEED_CHECK = "KEY_FOLD_SESSION_NEED_CHECK"
        const val KEY_FOLD_SESSION_VERSION = "KEY_FOLD_SESSION_VERSION"
        const val KEY_FOLD_SESSION_MSG_BOX = "KEY_FOLD_SESSION_MSG_BOX"
        const val KEY_FOLD_SESSION_OFFICIAL = "KEY_FOLD_SESSION_OFFICIAL"

        fun saveConfig(version: Int, msgBoxWhiteConfig: Set<String>, foldOfficial: Set<String>) {
            if (KV.getUserInt(KEY_FOLD_SESSION_VERSION, 0) != version) {
                KV.saveUserValue(KEY_FOLD_SESSION_NEED_CHECK, true)
            }
            KV.saveUserValue(KEY_FOLD_SESSION_VERSION, version)
            KV.saveUserValue(KEY_FOLD_SESSION_MSG_BOX, msgBoxWhiteConfig.joinToString(","))
            KV.saveUserValue(KEY_FOLD_SESSION_OFFICIAL, foldOfficial.joinToString(","))
        }

        @JvmStatic
        fun getChatBubble(): Boolean {
            return SessionTabMD5Helper.getInstance()?.isSingleChatBubble() == true
        }

        @JvmStatic
        fun getSessionTime(): Int {
            return SessionTabMD5Helper.getInstance()?.getSessionTime() ?: 0
        }

        @JvmStatic
        fun isShowChatTab(): Boolean {
            return SessionTabMD5Helper.getInstance()?.isSessionInteraction() == true
        }

        @JvmStatic
        fun cleanSessionMemory() {
            SessionTabMD5Helper.getInstance()?.destroy()
        }
    }

    private val msgBoxIdWhiteList: Set<String> by lazy {
        KV.getUserStr(KEY_FOLD_SESSION_MSG_BOX, "").split(",").toHashSet()
    }
    private val officialIdBlackList: Set<String> by lazy {
        KV.getUserStr(KEY_FOLD_SESSION_OFFICIAL, "").split(",").toHashSet()
    }

    /**
     * 是否拉取到折叠名单配置
     */
    private fun isFoldConfigReady(): Boolean {
        return KV.getUserInt(KEY_FOLD_SESSION_VERSION, 0) > 0
    }

    /**
     * 根据sessionType跟sessionId
     */
    fun isTopFoldSession(foldV3: Int = 0, sessionType: String, sessionId: String): Boolean {
        if (foldV3 == FOLD_TYPE_TOP_OPEN) {
            return true
        }
        if (sessionType == FriendNoticeSessionDefinition.Type) {
            return true
        }
        if (sessionType == "goto" && !msgBoxIdWhiteList.contains(
                sessionId.replace(
                    "gotochat",
                    ""
                )
            )
        ) {
            return true
        }
        if (sessionType == "u" && officialIdBlackList.contains(sessionId)) {
            return true
        }
        return false
    }

    fun isTopFoldSession(session: SessionModel): Boolean {
        when {
            session.baseInfo.foldTypeV3 == FOLD_TYPE_TOP_OPEN -> return true
            session is FriendNoticeSessionModel -> return true
            session is GotoSessionModel -> {
                return !msgBoxIdWhiteList.contains(session.sessionKey.id.replace("gotochat", ""))
            }
            session is UserChatSessionModel -> {
                return officialIdBlackList.contains(session.sessionId)
            }
        }
        return false
    }

    @NeedOffline("整个顶部折叠的都需要下线")
    fun tryMarkTopFoldSession(sessionService: SessionService) {
        if (KV.getUserBool(KEY_FOLD_SESSION_NEED_CHECK, false).not()) {
            return
        }
        KV.saveUserValue(KEY_FOLD_SESSION_NEED_CHECK, false)
        sessionService.markTopFoldSession {
            isTopFoldSession(sessionType = it.sessionType, sessionId = it.sessionId)
        }
    }

}