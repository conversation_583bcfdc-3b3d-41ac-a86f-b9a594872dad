package com.immomo.momo.maintab.session2.presentation.itemmodel

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.os.SystemClock
import android.view.View
import android.view.animation.AccelerateInterpolator
import android.view.animation.LinearInterpolator
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.cosmos.mdlog.MDLog
import com.immomo.android.mm.cement2.AsyncCementModel
import com.immomo.android.mm.cement2.CementViewHolder
import com.immomo.android.mm.cement2.IViewHolderCreator
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.cement.CementModel
import com.immomo.framework.cement.SimpleCementAdapter
import com.immomo.framework.cement.eventhook.OnClickEventHook
import com.immomo.framework.utils.UIUtils
import com.immomo.framework.view.recyclerview.itemdecoration.LinearPaddingItemDecoration
import com.immomo.kotlin.extern.isNotNullOrEmpty
import com.immomo.mmutil.task.MomoMainThreadExecutor
import com.immomo.momo.R
import com.immomo.momo.gotologic.GotoDispatcher
import com.immomo.momo.maintab.session2.domain.topoperator.SessionTopOperatorManager
import com.immomo.momo.maintab.session2.domain.topoperator.SessionTopOperatorManager.TAG
import com.immomo.momo.maintab.session2.domain.topoperator.SessionTopOperatorManager.isShownGuild
import com.immomo.momo.maintab.session2.presentation.viewmodel.SessionListInnerViewModel
import com.immomo.momo.maintab.sessionlist.bean.CardItemData
import com.immomo.momo.maintab.sessionlist.bean.SessionTopOperatorData
import com.immomo.momo.maintab.sessionlist.itemmodel.SessionTopOperatorChildItemModel
import com.immomo.momo.maintab.sessionlist.itemmodel.SessionTopOperatorScaleChildItemModel
import com.immomo.momo.maintab.sessionlist.itemmodel.SessionTopOperatorShakeChildItemModel
import com.immomo.momo.maintab.sessionlist.itemmodel.base.BaseSessionTopOperatorChildModel
import com.immomo.momo.maintab.sessionlist.itemmodel.big.BaseSessionTopBigItemModel
import com.immomo.momo.maintab.sessionlist.itemmodel.big.SessionTopBigExpandItemModel
import com.immomo.momo.maintab.sessionlist.itemmodel.big.SessionTopBigNormalItemModel
import com.immomo.momo.maintab.sessionlist.util.SessionTopOperatorRecord
import com.immomo.svgaplayer.SVGAAnimListenerAdapter
import com.immomo.svgaplayer.view.MomoSVGAImageView

/**
 * 顶部运营位的item
 */
class SessionTopOperatorItemModel(
    private val sessionListVm: SessionListInnerViewModel,
    val lifecycleScope: LifecycleCoroutineScope
) : AsyncCementModel<String, SessionTopOperatorItemModel.ViewHolder>("SessionTopOperator") {

    private val operatorModelList: MutableList<BaseSessionTopOperatorChildModel<*>> = arrayListOf()

    private var isUnbindFromWindow = false // Itemview是否已经解绑
    private var isAlreadyShownGuid = false // 是否已经展示了动画引导

    private var innerScrollListener: RecyclerView.OnScrollListener? = null

    private var curViewHolder: ViewHolder? = null

    private var lastRecordExpTime = 0L

    private val LIST_ANIMATION_PRELOAD_TIME = 300L // 列表动画提前加载时间

    private var clickSafeTime = 1000

    private var lastClickTime = 0L

    private var inEmotionExperiment: Boolean = false // 是否在新实验

    init {
        id("SessionTopOperatorItemModel")
    }

    override val layoutRes: Int
        get() = R.layout.listitem_session_top_operator_item_model

    override val viewHolderCreator: IViewHolderCreator<ViewHolder>
        get() = object : IViewHolderCreator<ViewHolder> {
            override fun create(view: View): ViewHolder = ViewHolder(view)
        }

    private val adapterRV by lazy {
        val activeAdapter = SimpleCementAdapter()
        activeAdapter.addEventHook(object :
            OnClickEventHook<SessionTopOperatorChildItemModel.ViewHolder>(
                SessionTopOperatorChildItemModel.ViewHolder::class.java
            ) {
            override fun onClick(
                view: View,
                viewHolder: SessionTopOperatorChildItemModel.ViewHolder,
                position: Int,
                rawModel: CementModel<*>
            ) {
                kotlin.runCatching {
                    val currentTimeMillis = SystemClock.uptimeMillis()
                    if (currentTimeMillis - lastClickTime < clickSafeTime) {
                        return
                    }
                    if (rawModel !is SessionTopOperatorChildItemModel) {
                        return
                    }
                    lastClickTime = currentTimeMillis
                    val itemModeData = rawModel.itemModeData
                    itemModeData.action.isNotNullOrEmpty {
                        GotoDispatcher.action(it, view.context).execute()
                    }
                    SessionTopOperatorRecord.cardClick(
                        itemModeData.bizKey,
                        itemModeData.logMap
                    )
                }
            }

            override fun onBind(viewHolder: SessionTopOperatorChildItemModel.ViewHolder): View? {
                return viewHolder.itemView
            }
        })
        activeAdapter.addEventHook(object : OnClickEventHook<BaseSessionTopBigItemModel.ViewHolder>(
            BaseSessionTopBigItemModel.ViewHolder::class.java
        ) {
            override fun onClick(
                view: View,
                viewHolder: BaseSessionTopBigItemModel.ViewHolder,
                position: Int,
                rawModel: CementModel<*>
            ) {
                kotlin.runCatching {
                    val currentTimeMillis = SystemClock.uptimeMillis()
                    if (currentTimeMillis - lastClickTime < clickSafeTime) {
                        return
                    }
                    if (rawModel !is BaseSessionTopBigItemModel) {
                        return
                    }
                    lastClickTime = currentTimeMillis
                    val itemModeData = rawModel.itemModeData
                    if (itemModeData.isExpandState()) {
                        itemModeData.expand?.action?.takeIf { it.isNotBlank() }?.also {
                            GotoDispatcher.action(it, view.context).execute()
                            if (rawModel is SessionTopBigExpandItemModel) {
                                rawModel.expandBgClickJump()
                            }
                        }
                    } else {
                        itemModeData.action.isNotNullOrEmpty {
                            GotoDispatcher.action(it, view.context).execute()
                        }
                        SessionTopOperatorRecord.cardClick(itemModeData.bizKey, itemModeData.logMap)
                    }
                }
            }

            override fun onBind(viewHolder: BaseSessionTopBigItemModel.ViewHolder): View? {
                return viewHolder.itemView
            }
        })
        activeAdapter
    }

    private fun ViewHolder.bindEvent() {
        if (innerScrollListener == null) {
            innerScrollListener = buildScrollListener(this@bindEvent, true).also {
                rvOperatorCardList.addOnScrollListener(it)
            }
        } else {
            innerScrollListener?.also {
                rvOperatorCardList.removeOnScrollListener(it)
                rvOperatorCardList.addOnScrollListener(it)
            }
        }
    }

    private fun buildScrollListener(viewHolder: ViewHolder, isInnerScroll: Boolean = false) =
        object : RecyclerView.OnScrollListener() {

            private var initialX = 0
            private var isScrolling = false

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                when (newState) {
                    RecyclerView.SCROLL_STATE_DRAGGING -> {
                        // 开始滚动
                        isScrolling = true
                        if (isInnerScroll) {
                            initialX = recyclerView.computeHorizontalScrollOffset()
                        }
                    }

                    RecyclerView.SCROLL_STATE_IDLE -> {
                        validateExposeActiveUser(viewHolder)
                        // 滚动结束
                        if (isScrolling && isInnerScroll) {
                            val finalX = recyclerView.computeHorizontalScrollOffset()
                            if (finalX != initialX) {
                                SessionTopOperatorRecord.cardScroll(if (finalX > initialX) 0 else 1)
                            }
                        }
                        isScrolling = false
                    }
                }
            }
        }

    private fun bindAdapterData(
        viewHolder: ViewHolder, itemDatas: List<CardItemData>,
        runGuideAnimation: Boolean = false, needFilterRecord: Boolean = false
    ) {
        kotlin.runCatching {
            viewHolder.bindEvent()
            (viewHolder.rvOperatorCardList.adapter as? SimpleCementAdapter)
                ?: adapterRV.also { viewHolder.rvOperatorCardList.adapter = it }
            // 比较列表的长度，然后比较列表中的item中每个元素的momoid
            operatorModelList.clear()
            var isNeedCloseGuidAnimation = runGuideAnimation
            val stopGuideAnimation = {
                if (isNeedCloseGuidAnimation) {
                    closeGuidAnimation()
                    isNeedCloseGuidAnimation = false
                }
            }
            itemDatas.forEachIndexed { index, cardItemData ->
                when (cardItemData.funcTheme) {
                    2 -> {
                        operatorModelList.add(
                            SessionTopOperatorShakeChildItemModel(
                                cardItemData,
                                runGuideAnimation,
                                stopGuideAnimation
                            )
                        )
                    }

                    3 -> {
                        operatorModelList.add(
                            SessionTopOperatorScaleChildItemModel(
                                cardItemData,
                                runGuideAnimation,
                                stopGuideAnimation
                            )
                        )
                    }

                    4 -> { // 新增大卡类型
                        if (cardItemData.expand != null) {
                            operatorModelList.add(SessionTopBigExpandItemModel(cardItemData))
                        } else {
                            operatorModelList.add(SessionTopBigNormalItemModel(cardItemData))
                        }
                    }

                    else -> {
                        operatorModelList.add(
                            SessionTopOperatorChildItemModel(
                                cardItemData,
                                runGuideAnimation,
                                stopGuideAnimation
                            )
                        )
                    }
                }
            }
            adapterRV.replaceAllModels(operatorModelList)
            if (operatorModelList.size > 0) {
                viewHolder.itemView.post {
                    validateExposeActiveUser(viewHolder, needFilterRecord)
                }
            }
        }
    }

    private fun closeGuidAnimation() {
        kotlin.runCatching {
            operatorModelList.forEach {
                it.runGuideAnimation = false
            }
        }
    }

    override fun bindData(holder: ViewHolder) {
        super.bindData(holder)
        MDLog.i(TAG, "bindData${hashCode()}")
        curViewHolder = holder
        checkNetData(holder)
    }

    private fun checkNetData(holder: ViewHolder) {
        SessionTopOperatorManager.getApiDataWithLocal(sessionListVm,
            lifecycleScope, { isFromLocalCache, repData ->
                kotlin.runCatching {
                    if (repData.list.safe().isNotEmpty()) {
                        inEmotionExperiment = repData.isInEmotionExperiment()
                        if (repData.isInEmotionExperiment()) {
                            resizeItemModel(holder, 95f)
                        } else {
                            resizeItemModel(holder)
                        }
                        refreshModelUI(repData, holder)
                    } else {
                        if (!isFromLocalCache) {
                            resizeItemModel(holder, 0f)
                            bindAdapterData(
                                holder, repData.list.safe(), false, needFilterRecord = true
                            )
                        }
                    }
                }
            },
            {})
    }

    private fun resizeItemModel(holder: ViewHolder, heightSize: Float = 71f) {
        holder.itemView.layoutParams.also {
            val itemViewHeight = UIUtils.getPixels(heightSize)
            if (itemViewHeight != it.height) {
                it.height = UIUtils.getPixels(heightSize)
                holder.itemView.layoutParams = it
            }
        }
        if (heightSize == 95f) {
            holder.itemView.setPadding(0, 0, 0, UIUtils.getPixels(5f))
        } else {
            holder.itemView.setPadding(0, 0, 0, 0)
        }
    }

    private fun refreshModelUI(it: SessionTopOperatorData, holder: ViewHolder) {
        val animationGuide = it.animationGuide
        if (animationGuide != null && !isShownGuild && !isAlreadyShownGuid) {
            isAlreadyShownGuid = true
            SessionTopOperatorManager.saveSessionGuidKV()
            holder.rvOperatorCardList.visibility = View.INVISIBLE
            it.animationGuide.title.isNotNullOrEmpty {
                holder.hintText.text = it
            }
            it.animationGuide.desc.isNotNullOrEmpty {
                holder.hintSubtext.text = it
            }
            holder.hintSvga.startSVGAAnimWithListener(
                "animated_newGuide2.svga", 1,
                object : SVGAAnimListenerAdapter() {
                    override fun onFinished() {
                        super.onFinished()
                        holder.hintSvga.stepToPercentage(1.0, false)
                    }
                })
            // 播放收起动画
            var duration = animationGuide.duration - LIST_ANIMATION_PRELOAD_TIME
            if (duration.safe() <= 0) {
                duration = 1
            }
            holder.itemView.visibility = View.VISIBLE
            holder.hintContainer.visibility = View.VISIBLE
            MomoMainThreadExecutor.postDelayed(hashCode(), {
                startHintDismissAnimation(holder, it, duration)
            }, duration)
        } else {
            holder.itemView.visibility = View.VISIBLE
            holder.rvOperatorCardList.visibility = View.VISIBLE
            bindAdapterData(holder, it.list, false)
        }
    }

    private fun startHintDismissAnimation(
        holder: ViewHolder, sessionTopOperatorData: SessionTopOperatorData, durationDelay: Long
    ) {
        bindAdapterData(holder, sessionTopOperatorData.list, true)
        // 动画集合
        val animatorSet = AnimatorSet()
        val animatorUp = ObjectAnimator.ofFloat(
            holder.hintContainer, "translationY", 0f, -UIUtils.getPixels(15f).toFloat()
        )
        val animatorAlpha = ObjectAnimator.ofFloat(holder.hintContainer, "alpha", 1f, 0f)
        animatorSet.playTogether(animatorUp, animatorAlpha)
        animatorSet.interpolator = LinearInterpolator()
        if (durationDelay > 0) {
            animatorSet.startDelay = LIST_ANIMATION_PRELOAD_TIME
        } else {
            val finalDelayTime = LIST_ANIMATION_PRELOAD_TIME + durationDelay
            if (finalDelayTime > 0) {
                animatorSet.startDelay = finalDelayTime
            }
        }
        animatorSet.doOnStart {
            holder.rvOperatorCardList.visibility = View.VISIBLE
        }
        animatorSet.doOnEnd {
            holder.hintSvga.stopAnimCompletely()
            holder.hintContainer.visibility = View.GONE
        }
        animatorSet.interpolator = AccelerateInterpolator()
        animatorSet.duration = 300
        animatorSet.start()
    }

    /**
     * 将完全曝光的最近在线view加到待曝光列表里。
     */
    fun validateExposeActiveUser(holder: ViewHolder, needFilterRecord: Boolean = false) {
        kotlin.runCatching {
            if (isUnbindFromWindow) {
                return
            }
            val currentTimeMillis = System.currentTimeMillis()
            if (needFilterRecord && currentTimeMillis - lastRecordExpTime < 500) { // 过滤曝光频繁
                return
            }
            val start: Int = holder.activeLayoutManager.findFirstVisibleItemPosition()
            val end: Int = holder.activeLayoutManager.findLastVisibleItemPosition()
            if (start == RecyclerView.NO_POSITION && end == RecyclerView.NO_POSITION) {
                return
            }
            if (start >= 0 && end < operatorModelList.safe().size) {
                lastRecordExpTime = currentTimeMillis
                for (i in start until end + 1) {
                    if (operatorModelList.size > i) {
                        val childItemModel = operatorModelList[i]
                        childItemModel.itemModeData.also {
                            SessionTopOperatorRecord.cardExp(it.bizKey, if (it.isExpandState()) 1 else 0, it.logMap)
                        }
                    }
                }
            }
        }
    }

    /**
     * 标记当fragment resume时
     */
    fun onFragmentVisible(isResume: Boolean) {
        kotlin.runCatching {
            MDLog.i(TAG, "onFragmentResume${hashCode()}")
            if (isResume) {
                // 检查接口请求
                curViewHolder?.let {
                    validateExposeActiveUser(it) // 处理曝光
                    checkNetData(it)
                }
            }
            operatorModelList.forEach {
                if (inEmotionExperiment) {
                    adapterRV.notifyDataChanged(it)
                } else {
                    it.onFragmentVisible(isResume)
                }
            }
        }
    }

    override fun unbind(holder: ViewHolder) {
        super.unbind(holder)
        MDLog.i(TAG, "unbind${hashCode()}")
    }

    override fun attachedToWindow(holder: ViewHolder) {
        super.attachedToWindow(holder)
        if (isUnbindFromWindow) {
            operatorModelList.forEach { operatorModel ->
                if (inEmotionExperiment) {
                    adapterRV.notifyDataChanged(operatorModel)
                } else {
                    operatorModel.refreshHolderView()
                }
            }
            if (holder.hintContainer.visibility == View.VISIBLE) {
                holder.hintSvga.stepToPercentage(1.0, false)
            }
        }
        isUnbindFromWindow = false
        curViewHolder?.also { validateExposeActiveUser(it) }
        MDLog.i(TAG, "attachedToWindow${hashCode()}")
    }

    override fun detachedFromWindow(holder: ViewHolder) {
        super.detachedFromWindow(holder)
        isUnbindFromWindow = true
        MDLog.i(TAG, "detachedFromWindow${hashCode()}")
    }

    fun release() {
        kotlin.runCatching {
            MomoMainThreadExecutor.cancelAllRunnables(hashCode())
            curViewHolder?.hintSvga?.stopAnimCompletely()
            operatorModelList.forEach {
                it.release()
            }
        }
    }

    class ViewHolder(itemView: View) : CementViewHolder(itemView) {

        val activeLayoutManager: LinearLayoutManager = LinearLayoutManager(itemView.context)

        val rvOperatorCardList by lazy { itemView.findViewById<RecyclerView>(R.id.active_rv) }

        // 首次提示
        val hintContainer by lazy { itemView.findViewById<ConstraintLayout>(R.id.hint_container) }
        val hintSvga by lazy { itemView.findViewById<MomoSVGAImageView>(R.id.hint_svga) }
        val hintText by lazy { itemView.findViewById<TextView>(R.id.hint_title) }
        val hintSubtext by lazy { itemView.findViewById<TextView>(R.id.hint_subtitle) }

        init {
            hintContainer.setOnClickListener { }
            activeLayoutManager.orientation = LinearLayoutManager.HORIZONTAL
            rvOperatorCardList.layoutManager = activeLayoutManager
            rvOperatorCardList.itemAnimator = null
            rvOperatorCardList.addItemDecoration(
                LinearPaddingItemDecoration(
                    UIUtils.getPixels(15f),
                    UIUtils.getPixels(15f),
                    UIUtils.getPixels(5f)
                )
            )
        }
    }
}