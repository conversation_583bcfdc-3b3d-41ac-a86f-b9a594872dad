package com.immomo.momo.maintab.sessionlist.itemmodel

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import androidx.core.animation.doOnStart
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.maintab.sessionlist.bean.CardItemData

class SessionTopOperatorShakeChildItemModel(
    itemModeData: CardItemData,
    runHintAnimation: Boolean,
    override val onGuideAnimationFinished: () -> Unit
) :
    SessionTopOperatorChildItemModel(itemModeData, runHintAnimation, onGuideAnimationFinished) {

    private var bumpCount = itemModeData.bumpCount

    private var curBumpCount = 0

    private var needStopAnimation = false

    private var curAnimationSet: AnimatorSet? = null

    override fun startShackAnimation(holder: ViewHolder, onAnimationStart: (() -> Unit)?) {
        kotlin.runCatching {
            if (needStopAnimation) {
                return
            }
            if (bumpCount == -1) {
                bumpCount = Integer.MAX_VALUE
            }
            val view = holder.itemView
            view.clearAnimation()
            val translateSpace = -UIUtils.getPixels(7f).toFloat()
            val animatorUp = ObjectAnimator.ofFloat(view, "translationY", 0f, translateSpace)
            animatorUp.duration = 800
            // 从上往下平移 0.2 倍高度
            val animatorDown = ObjectAnimator.ofFloat(view, "translationY", translateSpace, 0f)
            animatorUp.duration = 200
            animatorUp.doOnStart {
                onAnimationStart?.invoke()
            }
            // 动画集合
            curAnimationSet = AnimatorSet().apply {
                playSequentially(animatorUp, animatorDown)
                if (runGuideAnimation && curBumpCount <= 0) {
                    startDelay = 500
                }
                addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        if (++curBumpCount < bumpCount) {
                            curAnimationSet?.cancel()
                            startShackAnimation(holder)
                        }
                    }
                })
                start()
            }
        }
    }

    override fun unbind(holder: ViewHolder) {
        kotlin.runCatching {
            needStopAnimation = true
            super.unbind(holder)
            curAnimationSet?.cancel()
        }
    }

    override fun onFragmentVisible(isResume: Boolean) {
        kotlin.runCatching {
            super.onFragmentVisible(isResume)
            if (curBumpCount < bumpCount) {
                if (isResume) {
                    if (needStopAnimation) {
                        needStopAnimation = false
                        curViewHolder?.also {
                            curAnimationSet?.apply {
                                if (isRunning) {
                                    cancel()
                                }
                            }
                            startShackAnimation(it)
                        }

                    }
                } else {
                    curAnimationSet?.apply {
                        if (isRunning) {
                            needStopAnimation = true
                            pause()
                            cancel()
                            curViewHolder?.itemView?.also {
                                it.clearAnimation()
                                it.y = 0f
                            }
                        }
                    }
                }
            }
        }
    }

}