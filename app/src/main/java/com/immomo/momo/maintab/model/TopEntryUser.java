package com.immomo.momo.maintab.model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.immomo.framework.common.UniqueHash;
import com.immomo.momo.microvideo.model.WithUniqueIdentity;

/**
 * Created by <PERSON><PERSON><PERSON>ya<PERSON><PERSON> on 2020/6/28.
 */

public class TopEntryUser implements WithUniqueIdentity<TopEntryUser> {

    @Expose
    public String momoid;

    @Expose
    public String avatar;

    @Expose
    @SerializedName("online_text")
    public String onlineText;

    @Expose
    @SerializedName("online_time")
    public long onlineTime;

    @Expose
    public String reason;

    @Expose
    @SerializedName("online_status_mark")
    public int onlineStatusMark;

    @Expose
    public String name;

    @Expose
    @SerializedName("btn_text")
    public String btnText;

    @Override
    public long uniqueId() {
        return UniqueHash.id(momoid);
    }

    @Override
    public Class<TopEntryUser> getClazz() {
        return TopEntryUser.class;
    }
}
