package com.immomo.momo.maintab.model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.google.gson.reflect.TypeToken;
import com.immomo.momo.util.GsonUtils;

import org.json.JSONObject;

/**
 * Created by lei.jialin on 2019/7/31.
 */
public class BubbleStartInfo {

    @Expose
    @SerializedName("is_success")
    private int isSuccess;

    @Expose
    private String toast;

    @Expose
    @SerializedName("bubble_dead_line")
    private long bubbleDeadLine;

    @Expose
    @SerializedName("bubble_remain_times")
    private int bubbleRemainTimes;

    @Expose
    @SerializedName("bubble_time_interval")
    private int bubbleTimeInterval;

    @Expose
    @SerializedName("cannot_bubble_toast")
    private String cannotBubbleToast;

    @Expose
    @SerializedName("bubble_eid")
    private int bubbleMoodId;

    @Expose
    @SerializedName("be_quiet")
    private int beQuiet;

    public static BubbleStartInfo parse(JSONObject dataJb) {
        if (dataJb == null) return null;
        return GsonUtils.g().fromJson(String.valueOf(dataJb.optJSONObject("bubble_up")), new TypeToken<BubbleStartInfo>() {
        }.getType());
    }

    public int getIsSuccess() {
        return isSuccess;
    }

    public void setIsSuccess(int isSuccess) {
        this.isSuccess = isSuccess;
    }


    public boolean isSuccess() {
        return isSuccess == 1;
    }

    public String getToast() {
        return toast;
    }

    public void setToast(String toast) {
        this.toast = toast;
    }

    public long getBubbleDeadLine() {
        return bubbleDeadLine;
    }

    public void setBubbleDeadLine(int bubbleDeadLine) {
        this.bubbleDeadLine = bubbleDeadLine;
    }

    public int getBubbleRemainTimes() {
        return bubbleRemainTimes;
    }

    public void setBubbleRemainTimes(int bubbleRemainTimes) {
        this.bubbleRemainTimes = bubbleRemainTimes;
    }

    public int getBubbleTimeInterval() {
        return bubbleTimeInterval;
    }

    public void setBubbleTimeInterval(int bubbleTimeInterval) {
        this.bubbleTimeInterval = bubbleTimeInterval;
    }

    public String getCannotBubbleToast() {
        return cannotBubbleToast;
    }

    public void setCannotBubbleToast(String cannotBubbleToast) {
        this.cannotBubbleToast = cannotBubbleToast;
    }

    public int getBubbleMoodId() {
        return bubbleMoodId;
    }

    public void setBubbleMoodId(int bubbleMoodId) {
        this.bubbleMoodId = bubbleMoodId;
    }

    public int getBeQuiet() {
        return beQuiet;
    }

    public void setBeQuiet(int beQuiet) {
        this.beQuiet = beQuiet;
    }
}
