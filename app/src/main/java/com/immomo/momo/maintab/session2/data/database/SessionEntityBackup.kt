package com.immomo.momo.maintab.session2.data.database

import com.immomo.momo.discuss.bean.Discuss
import com.immomo.momo.group.bean.Group
import com.immomo.momo.maintab.model.SessionActiveUser
import com.immomo.momo.maintab.session2.MsgDeprecated
import com.immomo.momo.protocol.imjson.util.Debugger
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.bean.User
import com.immomo.momo.service.bean.session.ISessionContent
import com.immomo.momo.voicechat.model.superroom.VChatSuperRoom

/**
 * 需要包含界面常更新的内容，不常更新的放到content里面
 * 需要包含索引的内容
 */
class SessionEntityBackup(
    //数据库字段：会话的id，作为主键
    val sessionId: Long,
    //群聊时groupid，单聊时momoid，讨论组时讨论组id
    val chatId: String,
    //最后一条消息拉取的时间，推荐使用格式：20110901125317，varchar(50)
    //用于部分消息类型Session.ID.FriendDistanceNotice，Session.ID.ActiveUser
    //请求数据的时间
    val fetchTime: String,
    //最后 一条消息的msgId ， varchar(50)
    val lastMsgId: String,
    //最后 一条消息的自增长id
    val orderId: Long,

    val unreadMessageCount: Int,
    val silentMessageCount: Int,

    //标识此会话的类型：例如是用户消息或者是招呼等, TYPE_CHAT, TYPE_GOTO
    val type: Int,
    //是否折叠,官方折叠,点点匹配折叠,从点点折叠变成正常session的标记
    val foldType: Boolean,
    //是否聊天置顶
    val sticky: Boolean,

    val id: Long
) {
    var remoteUser: User? = null
    var remoteGroup: Group? = null
    var remoteDiscuss: Discuss? = null
    var remoteVChatSuperRoom: VChatSuperRoom? = null

    //属于AbsSession.TYPE_ACTIVE_USER
    var activeUser: SessionActiveUser? = null

    @MsgDeprecated("remove out")
    var debugger: Debugger? = null

    //属于Session.TYPE_GOTO
    var gotoAction: String? = null
    var gotoTitle = ""
    var gotoText = ""
    var gotoIcon = ""
    var sessionContent: ISessionContent? = null

    /**
     * session关联业务类型, 只有红娘在用
     */
    var sessionBusinessType = Session.BUSINESS_NONE

    /**
     * 属于Session.TYPE_SAYHI
     * 招呼总的招呼人数(未读消息的打招呼人数)
     */
    var hiUserTotalCount = 0

    /**
     * 属于Session.TYPE_SAYHI
     * 招呼总的招呼人数(可以出现在滑动卡片堆栈里的)
     */
    var hiUserTotalCountUsable = 0

    /**
     * 属于Session.TYPE_FOLDER_MATCH，点点折叠消息
     *
     * 折叠起来的总数量
     * 点点匹配时表示折叠起来总的人数
     * 打出的招呼时表示收起的总人数
     */
    var folderTotalCount = 0

    /**
     * 草稿，直接操作数据库赋值
     */
    var draftString: String? = null

    /**
     * 草稿(被引用消息)，直接操作数据库赋值
     */
    var draftQuoteString: String? = null

    /**
     * 是否展示礼物气泡
     */
    @MsgDeprecated("8.9改版后没有了")
    var isPresentShow = false

    @MsgDeprecated("State状态，使这段代码只执行一次")
    var isLazy = true

    /**
     * 聊天状态：语音聊天，用于下一次回到聊天时，显示文字还是语音界面
     */
    @MsgDeprecated("没有用")
    var audioChat = false

    //<editor-fold desc="属于TYPE_CHAT">
    /**
     * 是否@我
     */
    var isAtMe = false
    var atText: String? = null

    /**
     * 有没有红包
     */
    var isHongbao = false
    var isRaiseFire = false
    var isGift = false
    var isMissedFriendCall = false
    var missedFriendCallDesc: String? = null
    var isDianDianCard = false
    var isQuestionMatch = false

    //是否正在视频聊天
    var isVideoChatting = false

    /**
     * 是否有聊天室心心红包
     */
    var hasVChatHongbao = false

    private val isType28 = false
    private val lastType28Prompt: String? = null
    private val type28AppId = ""

    /**
     * [xxxx]特殊前缀,如果最后一条消息是任务礼物，则设置 specialText
     */
    var specialText = ""

    /**
     * 类似  点点匹配 这种红色的前缀
     */
    var pushPrefix: String? = null
    //</editor-fold>
}