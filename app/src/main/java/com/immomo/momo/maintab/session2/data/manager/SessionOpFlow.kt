package com.immomo.momo.maintab.session2.data.manager

import kotlinx.coroutines.InternalCoroutinesApi
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.FlowCollector
import java.util.concurrent.ConcurrentLinkedQueue

class SessionOpFlow(private val maxBufferSize: Int = 32) : Flow<SessionOp> {
    private val bufferA: ConcurrentLinkedQueue<SessionOp> = ConcurrentLinkedQueue()
    private val bufferB: ConcurrentLinkedQueue<SessionOp> = ConcurrentLinkedQueue()
    private var current = true
    private val buffer
        get() = synchronized(this) {
            if (current) bufferA else bufferB
        }

    private fun swapBuffer() = synchronized(this) {
        val ret = if (current) bufferA else bufferB
        current = !current
        ret
    }

    private val checkChannel = Channel<Unit>(Channel.CONFLATED)

    @InternalCoroutinesApi
    override suspend fun collect(collector: FlowCollector<SessionOp>) {
        for (unit in checkChannel) {
            val previousBuffer = swapBuffer()
            if (previousBuffer.isEmpty()) continue

            collector.emitBuffer(previousBuffer.toList(), previousBuffer.size > maxBufferSize)
            previousBuffer.clear()
        }
    }

    private suspend fun FlowCollector<SessionOp>.emitBuffer(
        previousBuffer: List<SessionOp>,
        shouldDrop: Boolean
    ) {
        val start = if (shouldDrop) {
            previousBuffer.indexOfLast { it is SessionOp.All }.coerceAtLeast(0)
        } else 0
        (start until previousBuffer.size).forEach {
            emit(previousBuffer[it])
        }
    }

    fun tryEmit(element: SessionOp): Boolean {
        buffer.add(element)
        return checkChannel.offer(Unit)
    }

    override fun toString(): String {
        return "SessionOpFlow(" +
                "maxBufferSize=$maxBufferSize," +
                "bufferA=${bufferA.size}," +
                "bufferB=${bufferB.size}," +
                "current=$current," +
                "checkChannel=$checkChannel" +
                ")"
    }
}