package com.immomo.momo.maintab;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.Bitmap.CompressFormat;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.drawable.LayerDrawable;
import android.media.MediaMetadataRetriever;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Build;
import android.provider.Browser;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.resource.gif.GifDrawable;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.Target;
import com.cosmos.mdlog.MDLog;
import com.immomo.framework.imageloader.ImageLoaderUtil;
import com.immomo.framework.kotlin.ImageLoader;
import com.immomo.framework.kotlin.ImageType;
import com.immomo.framework.rxjava.interactor.CommonSubscriber;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.framework.utils.UIUtils;
import com.immomo.lcapt.evlog.EVLog;
import com.immomo.mmutil.NetUtils;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.mmutil.task.ThreadUtils;
import com.immomo.mmutil.web.WebRouterConfig;
import com.immomo.mmutil.web.WebRouterUtils;
import com.immomo.moarch.account.AccountKit;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoGlobalVariables;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.android.synctask.Callback;
import com.immomo.momo.android.synctask.LoadHttpImageThread;
import com.immomo.momo.android.view.MGifImageView;
import com.immomo.momo.businessmodel.usermodel.IUserModel;
import com.immomo.momo.innergoto.helper.ActivityHandler;
import com.immomo.momo.maintab.statistics.IOrientationSensorLog;
import com.immomo.momo.maintab.view.SlideTouchView;
import com.immomo.momo.mvp.common.model.ModelManager;
import com.immomo.momo.mvp.maintab.mainimpl.appconfig.SplashAppConfig;
import com.immomo.momo.mvp.maintab.mainimpl.appconfig.apt.SplashAppConfigGetter;
import com.immomo.momo.plugin.emote.LoadEmotionUtil;
import com.immomo.momo.protocol.http.AdApi;
import com.immomo.momo.protocol.http.AppApi;
import com.immomo.momo.protocol.http.core.HttpClient;
import com.immomo.momo.protocol.imjson.util.Debugger;
import com.immomo.momo.service.bean.Banner;
import com.immomo.momo.service.bean.SplashAdBean;
import com.immomo.momo.service.bean.SplashAdItem;
import com.immomo.momo.service.splashscreen.SplashPreLoader;
import com.immomo.momo.service.splashscreen.SplashScreenService;
import com.immomo.momo.util.DeviceUtils;
import com.immomo.momo.util.LoadCallback;
import com.immomo.momo.util.RXUtilsKt;
import com.immomo.momo.util.StringUtils;
import com.immomo.momo.util.fabricmomo.FabricLogger;
import com.immomo.momo.util.jni.BitmapUtil;
import com.immomo.momo.video.player.FullCropVideoView;
import com.immomo.momoenc.config.HttpConfigs;
import com.immomo.svgaplayer.SVGAAnimListenerAdapter;
import com.immomo.svgaplayer.view.MomoSVGAImageView;

import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.atomic.AtomicInteger;

import io.reactivex.Flowable;
import io.reactivex.disposables.Disposable;

public class SplashPresenter {

    public static final String EXCEPTION_NAME = "exceptionName";

    public static final String LOG_KEY_SHOWING_TYPE = "ad_show_type";
    public static final String TAG_PNG = "tag_png";

    //优先展示广告的index
    public static final String KEY_PRIORITY_INDEX = "key_priority_index";
    //正常广告的index
    public static final String KEY_SPLASH_INDEX = "key_splash_index";

    public static final int DEFAULT_SPLASH_INDEX = 0;

    private final List<String> allowTypes;

    private int duration = 1000;
    private boolean release = false;
    private boolean hasHide = false;
    private boolean isPlayingAd = false;
    private boolean splashClicked = false;
    private boolean rootViewCanClick = true;
    private float touchX = -1;
    private float touchY = -1;
    private View splashLayout;
    private TextView skipButton;
    private TextView tipsButton;
    private MomoSVGAImageView tipsBc;
    private View videoLayout;
    private View videoViewHolder;
    private FullCropVideoView videoView;
    private ImageView imgAdView;
    private MGifImageView gifImageView;
    private LinearLayout shakeLayout;
    private MomoSVGAImageView shakeSvg;
    private TextView shakeContent;
    private TextView shakeButtonContent;
    private View slideLayout;
    private MomoSVGAImageView slideSvg;
    private TextView slideContent;
    private TextView slideContentSecond;
    private SlideTouchView slideTouchView;

    private Activity activity;
    private SplashAdItem splashscreen;
    private OnSkipClickInterface skipCallback;
    //屏幕的宽高
    private float screenWidth;
    private float screenHeight;
    private int imgWidgetHeight;

    private Disposable mDisposable;
    private SplashShakeEventListener mListener;

    //1.全屏点击 2.按钮点击 3.摇一摇 4.全局滑动 5.局部滑动
    public static final int TYPE_CLICK_ALL = 1;
    public static final int TYPE_CLICK_BUTTON = 2;
    public static final int TYPE_SHAKE_BUTTON = 3;
    public static final int TYPE_SLIDE_ALL = 4;
    public static final int TYPE_SLIDE_PARTIAL = 5;


    public static long lastRequestTime = 0L;


    public SplashPresenter(Activity activity, List<String> allowTypes) {
        release = false;
        this.activity = activity;
        this.allowTypes = allowTypes;
        screenWidth = UIUtils.getScreenWidth();
        screenHeight = UIUtils.getRealScreenHeight();
        initView();
        readSplashInfo();
    }

    public static boolean isShowSplash(List<String> allowTypes) {
        if (!AccountKit.getAccountManager().isOnline()) {
            return false;
        }

        int requestInterval = SplashAppConfigGetter.get().requestInterval();
        if (Math.abs(System.currentTimeMillis() - lastRequestTime) > requestInterval * 60L * 1000) {
            lastRequestTime = System.currentTimeMillis();
            ThreadUtils.execute(ThreadUtils.TYPE_INNER, new CheckSplashImagesTask());
        }
        // 检查是否有符合条件的广告
        long lastShowedTime;
        SplashAdItem splash;

        if (SplashPreLoader.getPrepared()) {
            final SplashPreLoader.SplashPreData preData = SplashPreLoader.getPreparedData();
            lastShowedTime = preData.lastShowedTime;
            splash = preData.splashAdItem;
            SplashPreLoader.reset();
        } else {
            lastShowedTime = KV.getUserLong(SPKeys.User.SplashAD.KEY_LAST_SHOWED_TIME, SplashAdItem.TIME_NEVER_SHOWN);
            splash = SplashScreenService.getInstance().findSplashByDate(allowTypes, lastShowedTime);
        }

        boolean hasValidSplash = false;
        if (splash != null) {
            if (splash.is0DurationSplash()) {
                if (SplashScreenService.getInstance().getValidCPMSplashAd(allowTypes, false, 0) != null) {
                    MDLog.i(LogTag.Ad.Splash, "has valid cpm");
                    hasValidSplash = true;
                } else {
                    if (KV.getUserInt(SPKeys.User.SplashAD.KEY_SPLASH_EXCLUDE0S_SHOW_TIME, 0) != 1) {
                        /**
                         * 0s广告被检索到，同步更新全局的KEY_LAST_SHOWED_TIME
                         * */
                        KV.saveUserValue(SPKeys.User.SplashAD.KEY_LAST_SHOWED_TIME, System.currentTimeMillis());
                    }
                    updateSplashItemShowInfoAndIndex(splash);
                    checkNext0DurationAd(allowTypes, lastShowedTime, splash.getInterval(), null);

                    MDLog.i(LogTag.Ad.Splash, "0s splash");
                    postShowLog(splash, null);
                }
            } else {
                hasValidSplash = true;
            }

            MDLog.i(LogTag.Ad.Splash, "check splash：" + splash.toString());

        }

        return hasValidSplash;
    }

    public static void obtainSplashByList(List<String> allowTypes) {
        if (!AccountKit.getAccountManager().isOnline()) {
            return;
        }

        ThreadUtils.execute(ThreadUtils.TYPE_INNER, new ObtainSplashListDownloadTask(allowTypes));
    }

    public void showSkip() {
        if (isPlayingAd && skipButton != null) {
            skipButton.setVisibility(View.VISIBLE);
        }
    }

    public void show() {
        if (!release && splashscreen != null) {
            /**
             * SPKeys.User.SplashAD.KEY_LAST_SPLASH_SHOW_SUCCES 标记上次播放的广告状态
             * 如果上次播放成功标记为1则可以继续播放。
             * 如果上次播放失败标记仍然为0，说明上次播放发生了意外，此时将改广告设置成0s广告，不再展示。
             * */
            int lastShowState = KV.getUserInt(SPKeys.User.SplashAD.KEY_LAST_SHOW_SPLASH_STATE_ + splashscreen.getBannerId(), 1);
            if (lastShowState != 1) {
                setValidSplashTo0Duration();
                maintainShow();
                return;
            }

            markSplashPlaying();

            switch (splashscreen.showingType) {
                case SplashAdItem.AD_TYPE_PIC_FULL:
                    break;
                case SplashAdItem.AD_TYPE_GIF_FULL:
                    playGif();
                    break;
                case SplashAdItem.AD_TYPE_VIDEO_FULL:
                    playVideo();
                    break;
            }

            splashLayout.setVisibility(View.VISIBLE);

            /**
             * 这里和hide冗余设置，避免在播放过程中用户kill程序，hide()中未执行到导致错误设置广告为0s广告。
             */
            markSplashPlaySuccess();

            if (SplashAppConfigGetter.get().recordShowOpt() == 1) {
                if (isPlayingAd) {
                    KV.saveUserValue(SPKeys.User.SplashAD.KEY_LAST_SHOWED_TIME, System.currentTimeMillis());
                }
            } else {
                MomoKit.getApp().getMainLooperHandler().post(new Runnable() {
                    @Override
                    public void run() {
                        if (isPlayingAd) {
                            KV.saveUserValue(SPKeys.User.SplashAD.KEY_LAST_SHOWED_TIME, System.currentTimeMillis());
                        }
                    }
                });
            }
        }
    }

    public void disappear() {
        duration = 0;
        maintainShow();
    }

    private void hide() {
        if (hasHide) return;
        hasHide = true;
        ImageLoaderUtil.cancelImageTask(gifImageView);
        markSplashPlaySuccess();

        if (!release) {
            release();
        }
    }

    private View findViewById(int id) {
        return activity.findViewById(id);
    }

    /**
     * 释放资源
     */
    private void release() {
        if (!release) {
            release = true;

            if (videoView.isPlaying()) {
                stopPlay();
            }

            videoView = null;
            splashLayout = null;
            activity = null;
        }
    }

    public void destroy() {
        if (tipsBc != null) {
            tipsBc.stopAnimCompletely();
        }
        ImageLoader.cancel(TAG_PNG);
        ImageLoaderUtil.cancelImageTask(gifImageView);
        RXUtilsKt.dispose(mDisposable);
        if (mListener != null) {
            // 摇一摇关闭方向传感器埋点
            postOrientationSensorEvent();
            mListener.stopSound();
            mListener = null;
        }
        if (shakeSvg != null) {
            shakeSvg.stopAnimCompletely();
        }
        if (slideSvg != null) {
            slideSvg.stopAnimCompletely();
        }
    }

    private void initView() {
        splashLayout = findViewById(R.id.splash_layout_root); // pic_index_background
        skipButton = (TextView) findViewById(R.id.splash_tv_skip);
        tipsButton = (TextView) findViewById(R.id.tv_ad_skip_tips);
        tipsBc = (MomoSVGAImageView) findViewById(R.id.svg_ad_background);
        videoLayout = findViewById(R.id.video_layout);
        videoViewHolder = findViewById(R.id.placeholder);
        videoView = (FullCropVideoView) findViewById(R.id.splash_video_view);
        gifImageView = (MGifImageView) findViewById(R.id.splash_gif_view);
        imgAdView = (ImageView) findViewById(R.id.splash_img_view);
        shakeLayout = (LinearLayout) findViewById(R.id.ll_shake_skip_tips);
        shakeSvg = (MomoSVGAImageView) findViewById(R.id.shake_skip_tips_svg);
        shakeContent = (TextView) findViewById(R.id.tv_shake_content);
        shakeButtonContent = (TextView) findViewById(R.id.tv_shake_button_content);
        slideLayout = findViewById(R.id.rl_slide_ad);
        slideSvg = (MomoSVGAImageView) findViewById(R.id.svg_slide_ad);
        slideContent = (TextView) findViewById(R.id.tv_slide_content);
        slideContentSecond = (TextView) findViewById(R.id.tv_slide_content_second);
        slideTouchView = (SlideTouchView) findViewById(R.id.slide_touch_view);

        // 界面阻塞时间不可控导致 点击事件可能在activity制空之后出发，final变量保证activity还可被使用
        final Activity localActivity = activity;

        skipButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (skipCallback != null) {
                    String skipBannerId = splashscreen == null ? "" : splashscreen.getBannerId();
                    MDLog.i(LogTag.Ad.Splash, "@@@@@@skip bannerid:" + skipBannerId);
                    if (!StringUtils.isEmpty(skipBannerId)) {
                        //点击跳过广告统计数据
                        new SkipAdLogTask(skipBannerId).execute();
                    }
                    skipCallback.onSkipClicked();
                    setSkipSplash();
                }
                // 关闭广告
                duration = 0;
                maintainShow();
            }
        });

        splashLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (rootViewCanClick) {
                    gotoAdPage(localActivity);
                }
            }
        });

        splashLayout.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    touchX = event.getRawX();
                    touchY = event.getRawY();
                    MDLog.d(LogTag.Ad.Splash, "touch x:" + touchX + ", y:" + touchY);
                }
                return false;
            }
        });
    }

    private void gotoAdPage(Activity localActivity) {
        if (release || localActivity == null) {
            return;
        }

        boolean splashCanClick = !splashClicked
                && splashscreen != null
                && !StringUtils.isEmpty(splashscreen.getAction());
        if (splashCanClick) {
            splashClicked = true;

            onSplashClicked(splashscreen, localActivity);

            // clicklogs 新版本的统计，支持多个
            if (splashscreen.clickLogs != null) {
                Map<String, String> otherParam = new HashMap<>();
                otherParam.put(LOG_KEY_SHOWING_TYPE, String.valueOf(splashscreen.showingType));

                executeLogs(activity, splashscreen.clickLogs, otherParam);
            }

            // 停止播放视频
            if (videoView != null && videoView.isPlaying()) {
                stopPlay();
                maintainShow();
            }
        }

    }

    @NonNull
    private String replaceCoordinate(String action) {
        return action.replace("[CX]", String.valueOf(touchX)).replace("[CY]", String.valueOf(touchY));
    }

    private void initVideo() {
        videoView.setBackgroundColor(Color.WHITE);
        videoView.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
            @Override
            public void onPrepared(MediaPlayer mp) {
                if (release) {
                    return;
                }
                if (mp != null) {
                    mp.setOnInfoListener(new MediaPlayer.OnInfoListener() {
                        @Override
                        public boolean onInfo(MediaPlayer mp, int what, int extra) {
                            if (what == MediaPlayer.MEDIA_INFO_VIDEO_RENDERING_START
                                    && videoView != null) {
                                imgAdView.setVisibility(View.GONE);
                                gifImageView.setVisibility(View.GONE);
                                videoLayout.setVisibility(View.VISIBLE);
                                // video started; hide the placeholder.
                                videoView.setBackgroundColor(Color.TRANSPARENT);
                                videoViewHolder.setVisibility(View.GONE);
                                return true;
                            }
                            return false;
                        }
                    });
                    mp.setVolume(0, 0);
                    if (getAdType() == SplashAdItem.AD_TYPE_VIDEO_FULL) {
                        videoView.layoutDisplay(mp.getVideoWidth(), mp.getVideoHeight(), (int) screenWidth, (int) screenHeight);
                    }
                }
            }
        });

        videoView.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {

            @Override
            public void onCompletion(MediaPlayer mp) {
                duration = 0;
                maintainShow();
            }
        });

        videoView.setOnErrorListener(new MediaPlayer.OnErrorListener() {
            @Override
            public boolean onError(MediaPlayer mp, int what, int extra) {
                handleVideoError();
                return true;
            }
        });
    }

    private void handleVideoError() {
        MomoMainThreadExecutor.post(this.hashCode(), new Runnable() {
            @Override
            public void run() {
                if (videoView != null) {
                    stopPlay();
                    videoView.setBackgroundColor(Color.WHITE);
                    videoLayout.setVisibility(View.GONE);
                }
            }
        });
        duration = 0;
        maintainShow();
    }

    private int getAdType() {
        if (splashscreen != null) {
            return splashscreen.getAdType();
        }
        return SplashAdItem.AD_TYPE_PIC_FULL;
    }

    private void playVideo() {
        try {

            File videoFile = splashscreen.getStoreFile();
            if (!videoFile.exists()) {
                maintainShow();
                return;
            }
            updateSplashItemShowInfoAndIndex(splashscreen);

            MediaMetadataRetriever metadataRetriever = new MediaMetadataRetriever();
            metadataRetriever.setDataSource(videoFile.getAbsolutePath());

            // 处理开屏广告展示时间
            String durationStr = metadataRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
            if (StringUtils.isEmpty(durationStr)) {
                duration = 0;
                maintainShow();
                return;
            }

            videoView.setVideoPath(videoFile.getAbsolutePath());
            videoView.start();

            //            imgAdView.setVisibility(View.GONE);
            gifImageView.setVisibility(View.GONE);

            videoLayout.setVisibility(View.INVISIBLE);

            isPlayingAd = true;
            //展示打点
            postShowLog(splashscreen, activity);
            setInteraction(splashscreen);
        } catch (Throwable ex) {
            MDLog.printErrStackTrace(LogTag.Ad.Splash, ex);
            // 播放失败展示占位图, 默认展示一秒
            duration = 0;
            videoLayout.setVisibility(View.GONE);

            setValidSplashTo0Duration();

            maintainShow();

        }
    }

    private void stopPlay() {
        try {
            videoView.stopPlayback();
        } catch (Throwable ex) {
            MDLog.printErrStackTrace(LogTag.Ad.Splash, ex);
        }
    }

    private void playGif() {
        final File gifFile = splashscreen.getStoreFile();
        if (!gifFile.exists()) {
            duration = 0;
            maintainShow();
            return;
        }

        updateSplashItemShowInfoAndIndex(splashscreen);
        imgAdView.setVisibility(View.GONE);
        videoLayout.setVisibility(View.GONE);
        gifImageView.setVisibility(View.VISIBLE);
        isPlayingAd = true;

        ImageLoaderUtil.loadGifDrawable(gifFile, gifImageView, 0, 0, new RequestListener() {
            @Override
            public boolean onLoadFailed(@Nullable GlideException e, Object model, Target target, boolean isFirstResource) {
                // 播放失败展示占位图, 默认展示一秒
                duration = 1000;
                gifImageView.setVisibility(View.GONE);

                setValidSplashTo0Duration();
                maintainShow();

                return false;
            }

            @Override
            public boolean onResourceReady(Object resource, Object model, Target target, DataSource dataSource, boolean isFirstResource) {
                if (splashLayout == null) {
                    return false;
                }

                if (resource instanceof GifDrawable) {
                    com.bumptech.glide.load.resource.gif.GifDrawable gifDrawable = (GifDrawable) resource;
                    splashLayout.setBackgroundColor(UIUtils.getColor(R.color.white_ffffff));
                    // 设置开屏播放时长.
                    duration += LoadEmotionUtil.repeatPlayGif(1, gifDrawable, gifImageView, null);
                }

                //展示打点
                postShowLog(splashscreen, activity);
                maintainShow();
                return false;
            }
        });

        setInteraction(splashscreen);
    }

    private void readSplashInfo() {
        if (release) {
            return;
        }

        long lastShowedTime = KV.getUserLong(SPKeys.User.SplashAD.KEY_LAST_SHOWED_TIME, SplashAdItem.TIME_NEVER_SHOWN);
        splashscreen = SplashScreenService.getInstance().findSplashByDate(allowTypes, lastShowedTime);

        SplashAdItem currentCPTSplash = null;
        long offset;
        if (splashscreen != null) {
            offset = splashscreen.getInterval();

            currentCPTSplash = splashscreen;
            if (splashscreen.is0DurationSplash()) {
                MDLog.i(LogTag.Ad.Splash, "0s splash");

                // 空帧曝光
                postShowLog(splashscreen, activity);
                splashscreen = SplashScreenService.getInstance().getValidCPMSplashAd(allowTypes, true, 0);
            }
        } else {
            // 默认半个消息展示一次
            offset = 1800000; //30 * 60 * 1000;
        }
        checkNext0DurationAd(allowTypes, lastShowedTime, offset, currentCPTSplash);

        if (splashscreen == null) {
            maintainShow();
            return;
        }

        if (Debugger.isDebuggable()) {
            MDLog.i(LogTag.Ad.Splash, splashscreen.toString());
        }
        switch (splashscreen.getAdType()) {
            // 0 是老版本的兼容
            case 0:
            case SplashAdItem.AD_TYPE_PIC_FULL:
                showSplashPicture(splashscreen);
                break;
            case SplashAdItem.AD_TYPE_GIF_FULL:
                boolean hasGif = checkSplashGif(splashscreen);
                if (!hasGif) {
                    showSplashPicture(splashscreen);
                }
                break;
            case SplashAdItem.AD_TYPE_VIDEO_FULL:
                boolean hasVideo = checkSplashVideo(splashscreen);
                if (!hasVideo) {
                    showSplashPicture(splashscreen);
                } else {
                    initVideo();
                }
                break;
            default:
                duration = 0;
                maintainShow();
                return;
        }
    }

    /**
     * 检查下次CPT广告是否是空帧，如果是检查是否有合法CPM
     *
     * @param allowTypes
     * @param lastShowedTime
     * @param offset
     * @param currentCPTSplash
     */
    private static void checkNext0DurationAd(List<String> allowTypes, long lastShowedTime,
                                             long offset, @Nullable SplashAdItem currentCPTSplash) {
        // 下次为空帧请求CPM广告接口。
        String lastSplashId = null;
        if (currentCPTSplash != null) {
            lastSplashId = currentCPTSplash.getBannerId();
        }
        SplashAdItem nextSplash = SplashScreenService.getInstance().findSplashByDate(allowTypes, lastShowedTime,
                System.currentTimeMillis() + offset, lastSplashId);

        if (nextSplash != null) {
            MDLog.i(LogTag.Ad.Splash, "nextSplash:" + nextSplash.toString());
        }

        if (nextSplash != null && nextSplash.is0DurationSplash()) {
            checkValidCPM(allowTypes, CheckCPMSplashTask.ReqNextSplash0);
        }
    }

    /**
     * 检查本地已有CPM广告是否有效，如果无效请求新数据
     *
     * @param allowTypes
     * @param reqType
     */
    private static void checkValidCPM(List<String> allowTypes, int reqType) {
        // 当前为空帧判断本地是否有合格CPM广告
        int expireTime = KV.getUserInt(SPKeys.User.SplashAD.KEY_SPLASH_CPM_EXPIRE_TIME, 1800);
        SplashAdItem currentCPMSplash = SplashScreenService.getInstance().getValidCPMSplashAd(allowTypes, false, 0);
        SplashAdItem nextCPMSplash = SplashScreenService.getInstance().getValidCPMSplashAd(allowTypes, false, expireTime * 1000);

        MDLog.i(LogTag.Ad.Splash, currentCPMSplash != null ? currentCPMSplash.toString() : "currentCPMSplash is null");
        MDLog.i(LogTag.Ad.Splash, nextCPMSplash != null ? nextCPMSplash.toString() : "nextCPMSplash is null");

        // 当前无可用CPM或者expireTime时间之后无可用CPM重新请求
        if (currentCPMSplash == null || nextCPMSplash == null) {
            ThreadUtils.execute(ThreadUtils.TYPE_INNER, new CheckCPMSplashTask(reqType));
        }
    }

    /**
     * 根据类型设置各种互动广告
     */
    private void setInteraction(@NonNull SplashAdItem splashscreen) {
        if (splashscreen == null || !isPlayingAd) {
            return;
        }
        if (StringUtils.isNotEmpty(splashscreen.getSkipContent())) {
            skipButton.setText(splashscreen.getSkipContent());
        }
        final Activity localActivity = activity;
        if (splashscreen.getClickType() == TYPE_CLICK_BUTTON) {
            rootViewCanClick = false;
            showClickButton(splashscreen, localActivity);
        } else if (splashscreen.getClickType() == TYPE_CLICK_ALL) {
            rootViewCanClick = true;
            showClickButton(splashscreen, localActivity);
        } else if (splashscreen.getClickType() == TYPE_SHAKE_BUTTON) {
            rootViewCanClick = false;
            showSkip();
            showScreenShake(splashscreen);
        } else if (splashscreen.getClickType() == TYPE_SLIDE_ALL) {
            rootViewCanClick = false;
            showSlideView(splashscreen, true);
        } else if (splashscreen.getClickType() == TYPE_SLIDE_PARTIAL) {
            rootViewCanClick = false;
            showSlideView(splashscreen, false);
        }

    }

    private void showClickButton(@NonNull SplashAdItem splashscreen, Activity localActivity) {
        if (StringUtils.isNotEmpty(splashscreen.getButtonStyle())) {
            File svgaFile = splashscreen.getStoreSvgaFile();
            //没有缓存时使用本地按钮tipsButton，防止弱网不展示按钮
            if (!svgaFile.exists()) {
                tipsBc.setVisibility(View.GONE);
                showTipsButton(splashscreen, localActivity);
                return;
            }
            ViewGroup.LayoutParams params = tipsBc.getLayoutParams();
            params.height = (int) (UIUtils.getScreenWidth() * 0.3f);
            tipsBc.setLayoutParams(params);
            tipsBc.setVisibility(View.VISIBLE);
            tipsButton.setVisibility(View.GONE);
            tipsBc.setOnClickListener(v -> {
                gotoAdPage(localActivity);
            });
            if (splashscreen.getButtonStyle().endsWith("svga")) {
                tipsBc.loadSVGAAnimWithListener(svgaFile.getAbsolutePath(), -1, new SVGAAnimListenerAdapter() {
                    @Override
                    public void onLoadSuccess() {
                        super.onLoadSuccess();
                        //为了跳过、底部按钮 同时展示
                        showSkip();
                    }

                    @Override
                    public void loadResError(@NotNull String msg) {
                        super.loadResError(msg);
                        if (tipsBc != null) {
                            tipsBc.setVisibility(View.GONE);
                            showTipsButton(splashscreen, localActivity);
                        }
                    }
                }, true);
            } else {
                ImageLoader.loadAs(svgaFile, Bitmap.class)
                        .doOnCompleted((model, bitmap) -> {
                            if (tipsBc != null) {
                                tipsBc.setImageBitmap(bitmap);
                                showSkip();
                            }
                            return null;
                        }).doOnFailed((model, bitmap) -> {
                            if (tipsBc != null) {
                                tipsBc.setVisibility(View.GONE);
                                showTipsButton(splashscreen, localActivity);
                            }
                            return null;
                        })
                        .imageType(ImageType.LOCAL_PATH)
                        .submit(TAG_PNG);
            }
            return;
        }
        showTipsButton(splashscreen, localActivity);
    }

    private void showTipsButton(@NonNull SplashAdItem splashscreen, Activity localActivity) {
        showSkip();
        if (StringUtils.isNotEmpty(splashscreen.getButtonContent())) {
            tipsButton.setText(splashscreen.getButtonContent());
            tipsButton.setOnClickListener(v -> {
                gotoAdPage(localActivity);
            });
            tipsButton.setVisibility(View.VISIBLE);
        } else {
            tipsButton.setVisibility(View.GONE);
        }
    }

    /**
     * 展示图片开屏广告
     *
     * @param splashscreen
     */
    private void showSplashPicture(SplashAdItem splashscreen) {
        videoLayout.setVisibility(View.GONE);
        gifImageView.setVisibility(View.GONE);
        imgAdView.setVisibility(View.VISIBLE);
        if (splashscreen != null) {
            // 加载背景图
            loadImageBitmap(splashscreen.getBgFileName(),
                    splashscreen.getBgurl(), R.drawable.pic_bg_app, new LoadCallback<Bitmap>() {
                        @Override
                        public void loadFinish(Bitmap backgroudBitmap) {
                            boolean success = false;
                            updateSplashItemShowInfoAndIndex(splashscreen);
                            if (backgroudBitmap != null) {

                                if (getAdType() == SplashAdItem.AD_TYPE_PIC_FULL || getAdType() == SplashAdItem.AD_TYPE_GIF_FULL || getAdType() == SplashAdItem.AD_TYPE_VIDEO_FULL) {
                                    imgAdView.setImageBitmap(backgroudBitmap);
                                    splashscreen.showingType = SplashAdItem.AD_TYPE_PIC_FULL;
                                    duration += splashscreen.getDuration() * 1000;
                                    success = true;
                                } else {
                                    Bitmap scaledBitmap;
                                    try {
                                        scaledBitmap = scaledBitmap(backgroudBitmap);
                                    } catch (Exception e) {
                                        MDLog.printErrStackTrace(LogTag.Ad.Splash, e);
                                        scaledBitmap = backgroudBitmap;
                                    }
                                    if (scaledBitmap != null) {
                                        imgAdView.setImageBitmap(scaledBitmap);
                                        splashscreen.showingType = SplashAdItem.AD_TYPE_PIC_FULL;
                                        duration += splashscreen.getDuration() * 1000;
                                        success = true;
                                    }
                                }
                            }

                            if (success) {
                                isPlayingAd = true;
                                postShowLog(splashscreen, activity);
                                setInteraction(splashscreen);

                                if (SplashAppConfigGetter.get().recordShowOpt() == 1) {
                                    KV.saveUserValue(SPKeys.User.SplashAD.KEY_LAST_SHOWED_TIME, System.currentTimeMillis());
                                }
                            } else {
                                showSplashFail();
                            }
                            maintainShow();
                        }
                    });

        } else {
            showSplashFail();
            maintainShow();
        }
    }

    private void showSplashFail() {
        isPlayingAd = false;
        showDefaultBg();
        duration = 0;
    }

    private Bitmap scaledBitmap(Bitmap srcBitmap) {
        float originImgHeight = srcBitmap.getHeight();
        float originImgWidth = srcBitmap.getWidth();

        //屏幕宽高比例
        float screenScale = screenWidth / screenHeight;

        //广告图片控件需要的宽度
        float imgWidgetWidth = screenWidth;

        //更具不同的屏幕比例来设置广告图片控件和底部logo的高度
        if (screenScale <= 0.54) {
            imgWidgetHeight = (int) (screenHeight * (1f - 0.177f));
        } else if (screenScale <= 0.57) {
            imgWidgetHeight = (int) (screenHeight * (1f - 0.188f));
        } else {
            imgWidgetHeight = (int) (screenHeight * (1f - 0.247f));
        }

        //控件宽高比例
        final float widgetScale = imgWidgetWidth / imgWidgetHeight;
        //图片宽高比例
        final float imgSrcScale = originImgWidth / originImgHeight;

        float scle = 1f;
        float startX = 0;
        if (imgSrcScale < widgetScale) {
            //裁剪底部
            scle = imgWidgetWidth / originImgWidth;
            originImgHeight = originImgWidth / widgetScale;
            startX = 0;
        } else if (imgSrcScale > widgetScale) {
            //裁剪两边
            scle = imgWidgetHeight / originImgHeight;
            float afterImgSrcWidth = originImgHeight * widgetScale;
            startX = (originImgWidth - afterImgSrcWidth) * 0.5f;
            if (startX + afterImgSrcWidth < originImgWidth) {
                originImgWidth = afterImgSrcWidth;
            } else {
                startX = 0;
            }
        } else {
            return srcBitmap;
        }

        Matrix matrix = new Matrix();
        matrix.postScale(scle, scle);
        return Bitmap.createBitmap(srcBitmap, (int) startX, 0, (int) originImgWidth, (int) originImgHeight, matrix, true);
    }

    private void showDefaultBg() {
        LayerDrawable drawable = null;
        if (activity != null && activity.getResources() != null) {
            drawable = (LayerDrawable) activity.getResources().getDrawable(R.drawable.pic_bg_app);
        }
        if (drawable != null) {
            imgAdView.setImageDrawable(drawable);
        }
    }

    /**
     * 展示视频开屏广告
     * 如果本地没有文件，在wifi情况下下载
     *
     * @param splashscreen
     */
    private static boolean checkSplashVideo(SplashAdItem splashscreen) {
        if (!checkIsSupport()) {
            return false;
        }

        boolean canPlayVideo = false;
        if (!StringUtils.isEmpty(splashscreen.getVideoUrl())) {
            File videoFile = splashscreen.getStoreFile();
            if (videoFile.exists()) {
                splashscreen.showingType = splashscreen.getAdType();
                canPlayVideo = true;
            } else {
                if (NetUtils.isWifi()) {
                    // 下载时间较长，获取视频文件之后不立即播放。
                    new DownloadSplashFileThread(splashscreen.getVideoUrl(), SplashAdItem.getSplashVideoHome(), null).execute();
                }
            }
        }

        return canPlayVideo;
    }

    /**
     * 下载svga文件
     */
    private static boolean checkSplashSvga(SplashAdItem splashscreen) {
        if (!checkIsSupport()) {
            return false;
        }

        boolean canPlaySvga = false;
        if (!StringUtils.isEmpty(splashscreen.getButtonStyle())) {
            File svgaFile = splashscreen.getStoreSvgaFile();
            if (svgaFile.exists()) {
                canPlaySvga = true;
            } else {
                if (NetUtils.isWifi()) {
                    new DownloadSplashFileThread(splashscreen.getButtonStyle(), SplashAdItem.getSplashSvgahHome(), null).execute();
                }
            }
        }
        return canPlaySvga;
    }

    /**
     * 检查是否满足播放视频广告，gif广告的条件。
     * 1. 512MB内存以上。
     * 2. android 4.4及其以上
     * 3. 分辨率720p及更高。
     *
     * @return
     */
    private static boolean checkIsSupport() {
        if (DeviceUtils.getTotalMemory() > 512
                && Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT
                && UIUtils.getScreenWidth() >= 720) {
            return true;
        }
        return false;
    }

    /**
     * 检查Gif开屏广告文件
     * 如果本地没有文件，在wifi情况下下载
     *
     * @param splashscreen
     */
    private static boolean checkSplashGif(SplashAdItem splashscreen) {
        if (!checkIsSupport()) {
            return false;
        }

        boolean canPlayGif = false;
        if (!StringUtils.isEmpty(splashscreen.getGifUrl())) {
            File gifFile = splashscreen.getStoreFile();
            if (gifFile.exists()) {
                splashscreen.showingType = splashscreen.getAdType();
                canPlayGif = true;
            } else {
                if (NetUtils.isWifi()) {
                    // 下载时间较长，获取GIF文件之后不立即播放。
                    new DownloadSplashFileThread(splashscreen.getGifUrl(), SplashAdItem.getSplashGifHome(), null).execute();
                }
            }
        }
        return canPlayGif;
    }

    /**
     * 播放失败的广告，直接修改为0s广告，不会再展示
     */
    private void setValidSplashTo0Duration() {
        try {
            splashscreen.setDuration(0);
            updateSplashScreen(splashscreen);

        } catch (Throwable ex) {
            MDLog.printErrStackTrace(LogTag.Ad.Splash, ex);
        }
    }

    /**
     * 点击跳过广告后这是最大播放次数为0
     * 这样以后就以后查询的时候都差不多这个广告了。
     */
    private void setSkipSplash() {
        splashscreen.setMaxCount(0);
        updateSplashScreen(splashscreen);
    }

    private static void updateSplashScreen(SplashAdItem splashscreen) {
        ThreadUtils.execute(ThreadUtils.TYPE_RIGHT_NOW, new Runnable() {
            @Override
            public void run() {
                SplashScreenService.getInstance().update(splashscreen);
            }
        });
    }

    /**
     * 持续展示
     */
    private void maintainShow() {
        postDelayed(new Runnable() {
            @Override
            public void run() {
                hide();
                if (skipCallback != null) {
                    skipCallback.onShowCompleted();
                }
            }
        }, duration);
    }

    private static void loadSplashImage(final String fileName, final String bitmapUrl) {
        boolean exist = false;
        if (StringUtils.isEmpty(bitmapUrl)) {
            return;
        }

        try {
            Bitmap bitmap = getScaledBitmap(fileName);
            if (bitmap != null) {
                bitmap.recycle();
                exist = true;
            }
        } catch (Throwable e) {
        }

        // 图片已经存在
        if (exist) {
            return;
        }

        // 根据后缀名，判断图片格式。
        CompressFormat format = Bitmap.CompressFormat.PNG;
        if (bitmapUrl.endsWith(".jpg")) {
            format = Bitmap.CompressFormat.JPEG;
        }

        // 下载
        LoadHttpImageThread thread = new LoadHttpImageThread("temp",
                new ImageCallback(fileName, format), -1, null);
        thread.setUrl(bitmapUrl);
        ThreadUtils.execute(ThreadUtils.TYPE_INNER, thread);
    }

    private void loadImageBitmap(String filePath, String imageUrl, int resId, LoadCallback<Bitmap> callback) {
        mDisposable = Flowable.fromCallable(new Callable<Bitmap>() {
                    @Override
                    public Bitmap call() throws Exception {
                        Bitmap resultBitmap = null;
                        try {
                            if (!StringUtils.isEmpty(imageUrl)) {
                                MDLog.i(LogTag.Ad.Splash, "imageUrl=" + imageUrl);
                                resultBitmap = getScaledBitmap(filePath, true, resId);
                            }
                        } catch (Throwable ex) {
                            MDLog.printErrStackTrace(LogTag.Ad.Splash, ex);
                        } finally {
                            if (resultBitmap == null) {
                                loadSplashImage(filePath, imageUrl);
                            }
                        }

                        return resultBitmap;
                    }
                }).compose(RXUtilsKt.fixScheduler())
                .subscribeWith(new CommonSubscriber<Bitmap>() {
                    @Override
                    public void onNext(Bitmap bitmap) {
                        super.onNext(bitmap);
                        if (callback != null && !release) {
                            callback.loadFinish(bitmap);
                        }
                    }

                    @Override
                    public void onError(@org.jetbrains.annotations.Nullable Throwable exception) {
                        if (callback != null && !release) {
                            callback.loadFinish(null);
                        }
                    }
                });
    }

    private static Bitmap getScaledBitmap(String fileName) {
        return getScaledBitmap(fileName, false, -1);
    }

    private static Bitmap getScaledBitmap(String fileName, boolean addResOption, int res) {
        if (!TextUtils.isEmpty(fileName)) {
            String path = MomoKit.getContext().getFilesDir() + File.separator + fileName;
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            BitmapFactory.decodeFile(path, options);

            int deviceWidth = UIUtils.getScreenWidth();
            int deviceHeight = UIUtils.getRealScreenHeight();

            options.inSampleSize = BitmapUtil.calculateInSampleSize(
                    options.outWidth, options.outHeight, deviceWidth, deviceHeight);

            options.inJustDecodeBounds = false;
            // 只有小屏手机添加density兼容，大屏手机在放大后可能导致内存过大，超出 DisplayListCanvas 限制（100M）
            if (deviceWidth < 720 && addResOption) {
                options.inScreenDensity = UIUtils.getDisplayMetrics().densityDpi;
                TypedValue value = new TypedValue();
                Resources resources = UIUtils.getResources();
                resources.getValue(res, value, false);

                final int density = value.density;
                if (density == TypedValue.DENSITY_DEFAULT) {
                    options.inDensity = DisplayMetrics.DENSITY_DEFAULT;
                } else if (density != TypedValue.DENSITY_NONE) {
                    options.inDensity = density;
                }
                options.inTargetDensity = resources.getDisplayMetrics().densityDpi;
            }

            return BitmapFactory.decodeFile(path, options);
        }
        return null;
    }

    /**
     * 曝光统计
     *
     * @param splashscreen
     */
    private static void postShowLog(SplashAdItem splashscreen, Activity activity) {
        if (splashscreen.viewLogs != null) {
            // 增加曝光展示的类型。
            Map<String, String> otherParam = new HashMap<>();
            otherParam.put(LOG_KEY_SHOWING_TYPE, String.valueOf(splashscreen.showingType));
            executeLogs(activity, splashscreen.viewLogs, otherParam);
        }
    }

    private static void updateSplashItemShowInfoAndIndex(SplashAdItem splash) {
        ThreadUtils.execute(ThreadUtils.TYPE_RIGHT_NOW, new Runnable() {
            @Override
            public void run() {
                SplashScreenService.getInstance().updateSplashItemShowInfoAndIndex(splash, System.currentTimeMillis());
            }
        });
    }

    /**
     * 执行统计log
     *
     * @param logs
     */
    private static void executeLogs(Context context, final List<String> logs, Map<String, String> otherParams) {
        if (logs == null || logs.size() == 0) {
            return;
        }
        for (final String logStr : logs) {
            if (TextUtils.isEmpty(logStr)) {
                continue;
            }
            if (StringUtils.isStartWithHttpOrHttps(logStr)) {
                Runnable thread = new Runnable() {
                    @Override
                    public void run() {
                        String webUserAgent = "";
                        try {
                            webUserAgent = MomoGlobalVariables.getSystemWebAgent();
                        } catch (Throwable ex) {
                            MDLog.printErrStackTrace(LogTag.COMMON, ex);
                        }

                        Map<String, String> header = new HashMap<>();
                        header.put(HttpConfigs.HEADER_USERANGEN, "false");
                        header.put(HttpClient.HEADER_NATIVE_UA, webUserAgent);
                        try {
                            HttpClient.doThirdPartGet(logStr, null, header);
                        } catch (Exception e) {
                            MDLog.printErrStackTrace(LogTag.Ad.Splash, e);
                        }
                    }
                };
                ThreadUtils.execute(ThreadUtils.TYPE_RIGHT_NOW, thread);
            } else {
                ActivityHandler.executeAction(logStr, context, otherParams);
            }
        }
    }

    private void onSplashClicked(SplashAdItem splash, Activity localActivity) {
        String action = splash.getAction();
        if (StringUtils.isEmpty(action)) {
            return;
        }

        action = replaceCoordinate(action);

        // 浏览器类型
        switch (splash.getLinktype()) {
            case Banner.LINKTYPE_BROWSER_EXTERNAL:
                if (!StringUtils.isEmpty(action)) {
                    Intent viewIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(action));
                    viewIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    viewIntent.putExtra(Browser.EXTRA_APPLICATION_ID, MomoKit.getPackageName());
                    localActivity.startActivity(viewIntent);
                }
                break;
            case Banner.LINKTYPE_MOMO:
            case Banner.LINKTYPE_OTHERAPP:
                MDLog.i(LogTag.Ad.Splash, "banner.url=" + action);

                ActivityHandler.executeAction(action, localActivity);
                break;
            case Banner.LINKTYPE_BROWSER_INTERNAL:
                if (!StringUtils.isEmpty(action)) {
                    WebRouterConfig config = new WebRouterConfig.Builder().url(action).build();
                    WebRouterUtils.startOpenWebPage(localActivity, config);
                }
                break;
            default:
                MDLog.w(LogTag.Ad.Splash, "splash.linktype=" + splash.getLinktype() + ":" + action);
                break;
        }
    }

    public void setSkipCallback(OnSkipClickInterface skipCallback) {
        this.skipCallback = skipCallback;
    }

    private void markSplashPlaySuccess() {
        if (splashscreen != null) {
            KV.saveUserValue(SPKeys.User.SplashAD.KEY_LAST_SHOW_SPLASH_STATE_ + splashscreen.getBannerId(), 1);
        }

    }

    private void markSplashPlaying() {
        if (splashscreen != null) {
            KV.saveUserValue(SPKeys.User.SplashAD.KEY_LAST_SHOW_SPLASH_STATE_ + splashscreen.getBannerId(), 0);
        }
    }

    private boolean postDelayed(Runnable r, long delayMillis) {
        return splashLayout != null && splashLayout.postDelayed(r, delayMillis);
    }

    /**
     * 加载广告资源
     * Wifi下预加载gif和video
     *
     * @param adList
     */
    private static void loadSplashResource(List<SplashAdItem> adList) {
        for (int i = 0, size = adList.size(); i < size; i++) {
            SplashAdItem item = adList.get(i);
            loadSplashImage(item.getBgFileName(), item.getBgurl());
            loadSplashImage(item.getFootFileName(), item.getFootUrl());

            checkSplashSvga(item);
            checkSplashGif(item);
            checkSplashVideo(item);
        }
    }

    public interface OnSkipClickInterface {
        void onSkipClicked();

        void onShowCompleted();
    }

    private static class ImageCallback implements Callback<Bitmap> {
        private final String filename;
        private final CompressFormat format;

        ImageCallback(String filename, CompressFormat format) {
            this.filename = filename;
            this.format = format;
        }

        @Override
        public void callback(Bitmap bitmap) {
            if (bitmap == null) {
                MDLog.w(LogTag.Ad.Splash, filename + " download failed-------------------");
                return;
            }

            OutputStream os = null;

            try {
                os = MomoKit.getContext().openFileOutput(filename, Context.MODE_PRIVATE);
                bitmap.compress(format, 85, os);
                MDLog.d(LogTag.Ad.Splash, ("save file -> " + filename));
            } catch (Exception e) {
                MDLog.printErrStackTrace(LogTag.Ad.Splash, e);
            } finally {
                if (os != null) {
                    try {
                        os.close();
                    } catch (IOException e) {
                        FabricLogger.logException(e);
                    }
                }
                bitmap.recycle();
            }
        }
    }

    public static class CheckSplashImagesTask implements Runnable {
        private String webUserAgent = "";

        @Override
        public void run() {
            try {
                try {
                    webUserAgent = MomoGlobalVariables.getSystemWebAgent();
                } catch (Throwable ex) {
                    MDLog.printErrStackTrace(LogTag.COMMON, ex);
                }
                AtomicInteger version = new AtomicInteger(KV.getUserInt(SPKeys.User.SplashAD.KEY_SPVERSION, 0));
                int currentVersion = version.get();

                int sensorClose = KV.getSysInt(SPKeys.User.SplashAD.KEY_ORIENTATION_SENSOR_SWITCH, 0);
                SplashAdBean splashAdBean = AdApi.getInstance().getSplashConfigs(version, webUserAgent, sensorClose);

                IUserModel userModel = ModelManager.getInstance().getModel(IUserModel.class);
                // api确认，allow_banners的变化不会反应到版本号上，所以每次都要更新（保持原解析逻辑，下发了"allow_banners"JSONArray才更新）
                if (splashAdBean.getProfileConfigs() != null) {
                    userModel.saveShopSetting(splashAdBean.getProfileConfigs());
                }

                int newVersion = version.get();
                KV.saveUserValue(SPKeys.User.SplashAD.KEY_SPVERSION, version.get());
                KV.saveUserValue(SPKeys.User.SplashAD.KEY_SPLASH_CPM_EXPIRE_TIME, splashAdBean.getCpmExpireTime());
                KV.saveUserValue(SPKeys.User.SplashAD.KEY_SPLASH_EXCLUDE0S_SHOW_TIME, splashAdBean.getExclude0sShowTime());

                MDLog.i(LogTag.Ad.Splash, " CheckSplashImages->" + splashAdBean.toString());
                //只要新返回的code和上次的code不一致
                //无论服务器是下发空数据还是空列表,都直接更新本地数据库
                if (newVersion != currentVersion) {

                    //重制轮询index
                    // TODO by hlj 通过ContentValues 优化存储性能
                    KV.saveUserValue(SplashPresenter.KEY_PRIORITY_INDEX, SplashPresenter.DEFAULT_SPLASH_INDEX);
                    KV.saveUserValue(SplashPresenter.KEY_SPLASH_INDEX, SplashPresenter.DEFAULT_SPLASH_INDEX);
                    SplashScreenService.getInstance().save(splashAdBean);
                    List<SplashAdItem> adList = new ArrayList<>();
                    if (splashAdBean.getPriorityAdList() != null && splashAdBean.getPriorityAdList().size() > 0) {
                        adList.addAll(splashAdBean.getPriorityAdList());
                    }

                    if (splashAdBean.getNormalAdList() != null && splashAdBean.getNormalAdList().size() > 0) {
                        adList.addAll(splashAdBean.getNormalAdList());
                    }

                    loadSplashResource(adList);

                    if (!SplashAppConfig.INSTANCE.cpmRequestOptOpen()) {
                        // 检查本地是否有有效的CPM广告，如果没有请求新的cpm
                        checkValidCPM(userModel.getShopSetting().allowsAdTypes, CheckCPMSplashTask.ReqVersionChange);
                    }

                } else {
                    obtainSplashByList(userModel.getShopSetting().allowsAdTypes);
                }

                if (SplashAppConfig.INSTANCE.cpmRequestOptOpen()) {
                    // 检查本地是否有有效的CPM广告，如果没有请求新的cpm
                    checkValidCPM(userModel.getShopSetting().allowsAdTypes, CheckCPMSplashTask.ReqVersionChange);
                }
            } catch (Exception e) {
                MDLog.printErrStackTrace(LogTag.Ad.Splash, e);
            }
        }
    }

    private static class CheckCPMSplashTask implements Runnable {
        private String webUserAgent = "";
        private int reqType;

        public final static int ReqVersionChange = 1;
        public final static int ReqNextSplash0 = 2;

        public CheckCPMSplashTask(int reqType) {
            this.reqType = reqType;
        }

        @Override
        public void run() {
            try {
                try {
                    webUserAgent = MomoGlobalVariables.getSystemWebAgent();
                } catch (Throwable ex) {
                    MDLog.printErrStackTrace(LogTag.COMMON, ex);
                }

                int sensorClose = KV.getSysInt(SPKeys.User.SplashAD.KEY_ORIENTATION_SENSOR_SWITCH, 0);
                List<SplashAdItem> result = AdApi.getInstance().getCPMSplashAd(webUserAgent, reqType, sensorClose);
                if (result != null && !result.isEmpty()) {
                    boolean clearInvalidCpmOnly = (SplashAppConfigGetter.get().clearInvalidCpmOnly() == 1);
                    SplashScreenService.getInstance().insertCPMAd(result, clearInvalidCpmOnly);

                    loadSplashResource(result);
                }
            } catch (Exception e) {
                MDLog.printErrStackTrace(LogTag.Ad.Splash, e);
            }
        }
    }

    private static class SkipAdLogTask implements Runnable {
        private String skipBannerId = "";

        public SkipAdLogTask(String bannerId) {
            skipBannerId = bannerId;
        }

        @Override
        public void run() {
            try {
                AppApi.getInstance().skipAdLog(skipBannerId);
            } catch (Exception e) {
                MDLog.printErrStackTrace(LogTag.Ad.Splash, e);
            }

        }

        public void execute() {
            ThreadUtils.execute(ThreadUtils.TYPE_INNER, this);
        }
    }

    private static class ObtainSplashListDownloadTask implements Runnable {

        private List<String> allowTypes;

        public ObtainSplashListDownloadTask(List<String> allowTypes) {
            this.allowTypes = allowTypes;
        }

        @Override
        public void run() {
            try {
                if (allowTypes == null) {
                    allowTypes = ModelManager.getModel(IUserModel.class).getShopSetting().allowsAdTypes;
                }

                long lastShowedTime = KV.getUserLong(SPKeys.User.SplashAD.KEY_LAST_SHOWED_TIME, SplashAdItem.TIME_NEVER_SHOWN);
                List<SplashAdItem> allSplashScreens = SplashScreenService.getInstance().findSplashByListData(allowTypes, lastShowedTime);

                if (allSplashScreens != null && !allSplashScreens.isEmpty()) {
                    loadSplashResource(allSplashScreens);
                }

                if (SplashAppConfigGetter.get().loadResource() == 1) {
                    List<SplashAdItem> cpmList = SplashScreenService.getInstance().getAllValidCpm(allowTypes);
                    if (cpmList != null && !cpmList.isEmpty()) {
                        loadSplashResource(cpmList);
                    }
                }
            } catch (Exception ignore) {

            }
        }
    }

    private void showSlideView(SplashAdItem adItem, boolean isFullScreen) {
        showSkip();
        if (!isFullScreen) {
            narrowScrollArea();
        }
        slideSvg.startSVGAAnimWithListener("splash_slide.svga", -1, new SVGAAnimListenerAdapter() {
            @Override
            public void onStart() {
                super.onStart();
                slideLayout.setVisibility(View.VISIBLE);
                if (StringUtils.isNotEmpty(adItem.getSlideContent())) {
                    slideContent.setText(adItem.getSlideContent());
                }
                if (StringUtils.isNotEmpty(adItem.getSlideContentSecond())) {
                    slideContentSecond.setText(adItem.getSlideContentSecond());
                }
                slideTouchView.setMaxDis(Math.max(1, adItem.getSlideValve()));
                slideTouchView.setVisibility(View.VISIBLE);
                slideTouchView.setCallback(() -> {
                    final Activity localActivity = activity;
                    gotoAdPage(localActivity);
                });
            }
        });
    }

    private void narrowScrollArea() {
        ViewGroup.LayoutParams layoutParams = slideTouchView.getLayoutParams();
        layoutParams.height = (int) (UIUtils.getScreenHeight() * 0.4f);
        slideTouchView.setLayoutParams(layoutParams);
    }

    private void updateShakeLayoutHeight() {
        RelativeLayout.LayoutParams shakeLayoutParams = (RelativeLayout.LayoutParams) shakeLayout.getLayoutParams();
        shakeLayoutParams.height = (int) (UIUtils.getRealScreenHeight() * 0.4);
        shakeLayout.setLayoutParams(shakeLayoutParams);
    }

    private void showScreenShake(SplashAdItem splashscreen) {
        updateShakeLayoutHeight();
        shakeLayout.setVisibility(View.VISIBLE);
        shakeSvg.startSVGAAnimWithListener("splash_shake.svga", -1, new SVGAAnimListenerAdapter() {

            @Override
            public void onStart() {
                super.onStart();
                showScreenShakeData(splashscreen);
            }
        });
    }

    private void showScreenShakeData(SplashAdItem splashscreen) {
        shakeSvg.setBackgroundResource(R.drawable.bg_oval_323333);
        if (StringUtils.isNotEmpty(splashscreen.getShakeContent())) {
            shakeContent.setText(splashscreen.getShakeContent());
        } else {
            shakeContent.setText(UIUtils.getString(R.string.splash_screen_shake));
        }
        if (StringUtils.isNotEmpty(splashscreen.getShakeContentSecond())) {
            shakeButtonContent.setText(splashscreen.getShakeContentSecond());
        }
        double threshold = 5;
        if (StringUtils.isNotEmpty(splashscreen.getShakeValue())) {
            threshold = Double.parseDouble(splashscreen.getShakeValue());
        }
        String shakeSound = "";
        if (StringUtils.isNotEmpty(splashscreen.getShakeSound())) {
            shakeSound = splashscreen.getShakeSound();
        }
        initSensorEvent(threshold, shakeSound);
    }

    private void initSensorEvent(double shakeValue, String shakeSound) {
        final Activity localActivity = activity;
        mListener = new SplashShakeEventListener(shakeValue, shakeSound, () -> {
            if (duration > 0) {
                gotoAdPage(localActivity);
            }
        });
    }

    private void postOrientationSensorEvent() {
        int sensorStatus = mListener.isAccSensorClose() ?
                IOrientationSensorLog.SWITCH_CLOSE : IOrientationSensorLog.SWITCH_OPEN;
        EVLog.create(IOrientationSensorLog.class).logOrientationSensor(sensorStatus);

        KV.saveSysValue(SPKeys.User.SplashAD.KEY_ORIENTATION_SENSOR_SWITCH, mListener.isAccSensorClose() ? 1 : 0);
    }

    public boolean isPlayingAd() {
        return isPlayingAd;
    }
}
