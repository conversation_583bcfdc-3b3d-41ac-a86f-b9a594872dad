package com.immomo.momo.maintab.session2.defs

import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.kotlin.extern.isNullOrEmpty
import com.immomo.momo.maintab.session2.SessionContentParser
import com.immomo.momo.maintab.session2.SessionDefinition
import com.immomo.momo.maintab.session2.data.database.ChatContent
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.data.manager.SessionMessage
import com.immomo.momo.maintab.session2.domain.model.type.ChatSessionModel
import com.immomo.momo.service.bean.Message

abstract class ChatSessionDefinition<Content : ChatContent, Model : ChatSessionModel>(
    type: String,
    contentParser: SessionContentParser<Content>
) : SessionDefinition<Content, Model>(type, contentParser) {

    abstract fun getLastMessage(session: SessionEntity): Message?

    override fun validateSession(session: SessionEntity, canRemoveUnreliable: Boolean): Boolean {
        val content = (session.content as? Content) ?: return false

        //在上次App运行过程中出现过更新
        if (SessionManager.getService().isUnreliable(session.sessionKey)) {
            updateSessionIndicatorWithEveryMessage(session, content, null)
            try {
                getLastMessage(session)
            } catch (e: Exception) {
                null
            }?.let { message ->
                if ((message.isUpdateSession || message.notShowInSession)
                    && session.lastProcessedMsgTime < message.timestampExt
                ) {
                    updateSessionDescWithLastMessage(session, content, message)
                }
            }
            onReloadChatInfo(session.sessionId, session, false)

            if (canRemoveUnreliable) {
                SessionManager.getService().removeUnreliable(session.sessionKey)
            }
            return true
        }
        if (content.pendingReloadChatInfo || !content.isChatInfoValid()) {
            onReloadChatInfo(session.sessionId, session, false)
            return true
        }
        return false
    }

    override fun onReloadInfo(id: String, session: SessionEntity, forceReload: Boolean) {
        onReloadChatInfo(id, session, forceReload)
        val content = session.content.castOrNull<ChatContent>()
        //理论上不应该这么做，但是SessionItemModel中有部分代码依赖外部调用，所以必须这样
        if (forceReload) {
            content?.forceRefresh()
        }
    }

    abstract fun onReloadChatInfo(id: String, session: SessionEntity, forceReload: Boolean)

    /**
     * 更新Session的元数据，比如是否Fold，最后一条消息的Id和时间
     */
    open fun updateSessionMetadataWithEveryMessage(
        session: SessionEntity,
        content: Content,
        message: Message
    ) {
    }

    /**
     * 更新Session的描述，比如最后一条消息的时间，内容，已读未读
     */
    open fun updateSessionDescWithLastMessage(
        session: SessionEntity,
        content: Content,
        lastMessage: Message,
        updateProcessedTime: Boolean = true
    ) {
        if (updateProcessedTime) {
            session.lastProcessedMsgTime = lastMessage.timestampExt
        }
    }

    /**
     * 清空Session相关的展示，但是保留排序等状态
     */
    open fun clearSessionDesc(
        session: SessionEntity,
        content: Content
    ) {
        session.lastProcessedMsgTime = 0
        if (session.lastMsgId != null) {
            session.lastFetchTime = session.lastMsgTime
            session.lastMsgId = null
        }
        content.forcedDesc = ""
        content.showMessageStatus = false
    }

    /**
     * 更新Session的提示，比如是否有红包，是否有礼物
     */
    open fun updateSessionIndicatorWithEveryMessage(
        session: SessionEntity,
        content: Content,
        message: Message?
    ) {
    }

    private fun List<Message>.sorted() = this.sortedBy { it.timestampExt }
    private fun List<Message>.sortedByDescending() = this.sortedByDescending { it.timestampExt }

    /**
     * 消息高亮：可以跳过
     * 消息处理：不可跳过
     * 消息外漏：可以跳过
     */
    open fun onSyncMessage(
        session: SessionEntity,
        content: Content,
        data: SessionMessage.Sync
    ): Boolean {
        data.messageList.sorted().forEach { message ->
            updateSessionMetadataWithEveryMessage(session, content, message)
            updateSessionIndicatorWithEveryMessage(session, content, message)
            if (session.lastProcessedMsgTime <= message.timestampExt) {
                updateSessionDescWithLastMessage(session, content, message)
            }
        }

        //如果Session的Desc为空，尝试从最后一条消息倒序更新
        if (content.desc?.isNotEmpty() != true) {
            data.messageList.sortedByDescending().firstOrNull { message ->
                updateSessionDescWithLastMessage(session, content, message, false)
                content.desc?.isNotEmpty() == true
            }
        }
        return data.messageList.isNotEmpty()
    }

    /**
     * 消息高亮：不可跳过
     * 消息处理：不可跳过
     * 消息外漏：可以跳过
     */
    open fun onReceiveMessage(
        session: SessionEntity,
        content: Content,
        data: SessionMessage.Receive
    ): Boolean {
        data.messageList.sorted().forEach { message ->
            updateSessionMetadataWithEveryMessage(session, content, message)
            updateSessionIndicatorWithEveryMessage(session, content, message)
            updateSessionDescWithLastMessage(session, content, message)
        }
        return data.messageList.isNotEmpty()
    }

    /**
     * 消息高亮：可以跳过
     * 消息处理：不可跳过
     * 消息外漏：可以跳过
     */
    open fun onSendMessage(
        session: SessionEntity,
        content: Content,
        data: SessionMessage.Send
    ): Boolean {
        data.messageList.sorted().forEach { message ->
            updateSessionMetadataWithEveryMessage(session, content, message)
            updateSessionDescWithLastMessage(session, content, message)
        }
        return data.messageList.isNotEmpty()

    }

    /**
     * 消息外漏：不可跳过
     */
    open fun onUpdateMessage(
        session: SessionEntity,
        content: Content,
        data: SessionMessage.Update
    ): Boolean {
        updateSessionIndicatorWithEveryMessage(session, content, null)
        //是最后一条消息，或者比最后一条小心更新
        data.messageList.sorted().forEach { message ->
            if (session.lastMsgId == message.msgId ||
                session.lastProcessedMsgTime <= message.timestampExt
            ) {
                updateSessionDescWithLastMessage(session, content, message)
            }
        }
        return true
    }

    protected fun updateLastMessageStatus(
        session: SessionEntity,
        messageId: String?,
        isMessageReceive: Boolean?,
        messageStatus: Int
    ): Boolean {
        if (messageId == null || session.lastMsgId != messageId) return false

        val content = session.content as? ChatContent ?: return false
        if (content is UserChatContent && !content.forcedDesc.isNullOrEmpty()) {
            content.showMessageStatus = false
        } else {
            content.showMessageStatus =
                isMessageReceive == false || messageStatus == Message.STATUS_CLOUD
        }
        content.lastMessageStatus = messageStatus
        return true
    }

    /**
     * 消息外漏：不可跳过
     */
    open fun onUpdateMessageStatus(
        session: SessionEntity,
        content: Content,
        data: SessionMessage.UpdateStatus
    ): Boolean {
        updateSessionIndicatorWithEveryMessage(session, content, null)
        data.messageIds.firstOrNull {
            it == session.lastMsgId
        }?.let {
            updateLastMessageStatus(session, it, data.isMessageReceive, data.toStatus)
        }
        if (data.isUpdateAll && data.fromStatus != null
            && content.lastMessageStatus in data.fromStatus
        ) {
            updateLastMessageStatus(
                session, session.lastMsgId,
                data.isMessageReceive, data.toStatus
            )
        }
        return true
    }

    override fun syncSession(session: SessionEntity, data: Any?): Boolean {
        if (super.syncSession(session, data)) return true

        if (data !is SessionMessage) return false
        val content = session.content as? Content ?: return false
        return when (data) {
            is SessionMessage.Sync -> onSyncMessage(session, content, data)
            is SessionMessage.Receive -> onReceiveMessage(session, content, data)
            is SessionMessage.Send -> onSendMessage(session, content, data)
            is SessionMessage.Update -> onUpdateMessage(session, content, data)
            is SessionMessage.UpdateStatus -> onUpdateMessageStatus(session, content, data)
            is SessionMessage.Delete -> {
                updateSessionIndicatorWithEveryMessage(session, content, null)
                clearSessionDesc(session, content)
                true
            }
            is SessionMessage.ResetLast -> {
                //TODO: what should be done in this scenario
                updateSessionIndicatorWithEveryMessage(session, content, null)
                updateSessionDescWithLastMessage(session, content, data.message)
                true
            }
        }
    }
}