package com.immomo.momo.maintab.session2.data.database

import com.immomo.momo.maintab.session2.SessionDefinitionManager
import com.immomo.momo.protocol.imjson.util.Debugger
import java.util.*

class SessionSnapshot(
    val fields: Map<String, Any?>
)

object SessionSnapshotManager {
    private val isEnabled = Debugger.isDebuggable()

    private val declaredFields by lazy {
        SessionEntity::class.java.declaredFields.map {
            it.isAccessible = true
            it
        }
    }

    fun takeQuickSnapshot(entity: SessionEntity): Int = with(entity) {
        //强行赋值content
        content
        Objects.hash(
            sessionKey,
            sessionType,
            sessionId,
            foldType,
            SessionDefinitionManager.serialize(this),
            lastMsgId,
            lastMsgTime,
            recommendTime,
            isSticky,
            orderId,
            lastFetchTime,
            unreadMessageCount,
            silentMessageCount,
            lastProcessedMsgTime,
            lastUpdateId,
            markAsDeleted,
            cacheStatus
        )
    }

    fun takeSnapshot(entity: SessionEntity): SessionSnapshot? {
        if (!isEnabled) return null

        //强行赋值content
        entity.content
        return SessionSnapshot(declaredFields.associate { field ->
            field.name to field.get(entity).let {
                if (it is SessionContent) {
                    SessionDefinitionManager.serialize(entity)
                } else it
            }
        })
    }

    fun compare(s1: SessionSnapshot?, s2: SessionSnapshot?): CompareResult? {
        if (s1 == null || s2 == null) return null

        return s1.fields.keys.mapNotNull { key ->
            val f1 = s1.fields[key]
            val f2 = s2.fields[key]
            if (f1 != f2) {
                key to (f1 to f2)
            } else null
        }.let {
            if (it.isEmpty()) CompareResult(false)
            else CompareResult(true, it)
        }
    }

    data class CompareResult(
        val changed: Boolean,
        val changedFields: List<Pair<String, Pair<Any?, Any?>>> = emptyList()
    )
}