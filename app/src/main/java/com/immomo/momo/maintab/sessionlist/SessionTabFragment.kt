package com.immomo.momo.maintab.sessionlist

import android.content.Context
import android.content.res.Configuration
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.ImageView
import com.cosmos.mdlog.MDLog
import com.google.android.material.tabs.MomoTabLayout
import com.immomo.android.module.feedlist.presentation.fragment.FriendFeedListLuaFragment
import com.immomo.framework.account.MessageManager
import com.immomo.framework.base.BaseTabOptionFragment
import com.immomo.framework.base.tabinfo.FragmentTabInfo
import com.immomo.framework.base.tabinfo.HomePageTextTabInfo
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.kotlin.ImageLoaderOptions
import com.immomo.framework.kotlin.ImageLoadingCompletedAction
import com.immomo.framework.kotlin.ImageType
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.R
import com.immomo.momo.home.TempHomeFragment
import com.immomo.momo.home.group.BaseGroupFragment
import com.immomo.momo.home.manager.FrameConfigManager
import com.immomo.momo.home.manager.FrameConfigManager.containsFrameInHome
import com.immomo.momo.home.manager.FrameConfigManager.getBusinessIndexInHome
import com.immomo.momo.home.manager.MainTabEventCheckManager.onMainResume
import com.immomo.momo.maingroup.manager.FrameConfigConst
import com.immomo.momo.maintab.session2.presentation.fragment.SessionListInnerFragment
import com.immomo.momo.message.NewSayUIConfigV1
import com.immomo.momo.message.sayhi.utils.NewSayHiSessionFlowUtil
import com.immomo.momo.mvp.maintab.NotificationSourceHelper
import com.immomo.momo.util.StringUtils

/**
 * <AUTHOR>
 * 新消息页,包含快聊
 */

class SessionTabFragment : BaseGroupFragment() {

    private val TASK_TAG = this.hashCode() + 1
    private var mConext: Context? = null
    private var isPause = true
    private var needTabChange = false

    override fun getLayout(): Int {
        return R.layout.fragment_main_tab_session
    }

    override fun initViews(contentView: View?) {
        mConext = activity
    }

    // 和关注处于同一容器
    private fun showFriendTab() =
        FrameConfigManager.containsFrameInHome(homeKey, FrameConfigConst.FRAME_FOLLOW)

    override fun onLoad() {
        super.onLoad()
        currentTab = getDefaultIndex()
    }

    override fun updateBgImageView(darkTheme: Boolean) {
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        //取消下划线
        setNotShowIndicator()

        //背景图
        updateTopBg()

        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun sendSelectStatus(tab: MomoTabLayout.Tab, select: Boolean) {
        super.sendSelectStatus(tab, select)
        kotlin.runCatching {
            if (select && NewSayUIConfigV1.isUserNewUI()) {
                val homePageTextTabInfo =
                    tab.getTabInfo<MomoTabLayout.TabInfo>() as? HomePageTextTabInfo
                homePageTextTabInfo?.also {
                    if (SessionListFragment.MESSAGE_TAB_KEY == it.title && !isPause) {
                        NewSayHiSessionFlowUtil.checkSayhiForceFlow() // 检查是否展示
                    }
                }
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        updateTopBg()
    }

    private fun updateTopBg() {
        val bgImg = findViewById<View>(R.id.view_top) as? ImageView
        val bgConfig =
            if (isDarkTheme()) {
                FrameConfigManager.getHomeConfigByKey(homeKey)?.darkBgImage
            } else {
                FrameConfigManager.getHomeConfigByKey(homeKey)?.bgImage
            }

        if (StringUtils.notEmpty(bgConfig)) {
            bgImg?.let {
                ImageLoader.load(bgConfig)
                    .imageType(ImageType.URL)
                    .doOnCompleted(ImageLoadCallback(it)).into(it)
            }
        } else {
            bgImg?.let {
                try {
                    val defaultDrawable = resources.getDrawable(R.drawable.bg_main_session, null)
                    updateHomeBgLayoutParam(defaultDrawable, bgImg)
                } catch (ignore: Exception) {

                }
                bgImg.setImageResource(R.drawable.bg_main_session)
            }
        }
    }

    private class ImageLoadCallback(val img: ImageView) : ImageLoadingCompletedAction<Drawable> {
        override fun invoke(p1: ImageLoaderOptions.Model, p2: Drawable) {
            updateHomeBgLayoutParam(p2, img)
        }
    }

    companion object {
        private fun updateHomeBgLayoutParam(drawable: Drawable?, homeBg: View) {
            try {
                drawable?.let {
                    if (it.intrinsicWidth > 0 && it.intrinsicHeight > 0) {
                        val params = homeBg.layoutParams
                        if (params.width != UIUtils.getScreenWidth()) {
                            params.width = UIUtils.getScreenWidth()
                            params.height =
                                UIUtils.getScreenWidth() * it.intrinsicHeight / it.intrinsicWidth
                            homeBg.layoutParams = params
                        }
                    }
                }
            } catch (ignore: Exception) {
            }
        }
    }

    override fun onFragmentResume() {
        MDLog.d("Testxp", "onFragmentResume")
        super.onFragmentResume()
        isPause = false
        if (needTabChange) {
            needTabChange = false
            if (showFriendTab()) {
                currentTab = getPageIndexByClazz(FriendFeedListLuaFragment::class.java)
            }
        }
        var gotoparams = NotificationSourceHelper.getPushGotoparams()
        if (!StringUtils.isEmpty(NotificationSourceHelper.getSource())
            && gotoparams != null
            && StringUtils.isNotEmpty(gotoparams.businessKey)
        ) {
            NotificationSourceHelper.resetSource()
            openFrame(gotoparams.businessKey, null)
        } else if (!StringUtils.isEmpty(NotificationSourceHelper.getSource())
            && TempHomeFragment.FRIEND_FEED_LIST == NotificationSourceHelper.getSource()
        ) {
            NotificationSourceHelper.resetSource()
            if (showFriendTab()) {
                if (currentFragment !is FriendFeedListLuaFragment
                ) {
                    needTabChange = true
                }
            }
        } else if (StringUtils.notEmpty(NotificationSourceHelper.getSavedInstanceBusinessKey())
            && containsFrameInHome(homeKey, NotificationSourceHelper.getSavedInstanceBusinessKey())) {
            openFrame(NotificationSourceHelper.getSavedInstanceBusinessKey(), null)
            NotificationSourceHelper.resetSavedInstanceBusinessKey()
        } else if (getBusinessIndexInHome(NotificationSourceHelper.getStartAppBusiness()) >= 0) {
            openFrame(NotificationSourceHelper.getStartAppBusiness(), null)
            NotificationSourceHelper.resetStartAppBusiness()
        }

        onMainResume(tabs)
    }

    override fun onFragmentPause() {
        MDLog.d("Testxp", "onFragmentPause")
        super.onFragmentPause()
        isPause = true
        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
    }

    //</editor-fold>

    override val homeKey = FrameConfigConst.KEY_HOME_CHAT

    override fun onDetach() {
        super.onDetach()
        MessageManager.unregisterMessageReceiver(TASK_TAG)
        NewSayHiSessionFlowUtil.onDestroy()
    }


    private fun getPageIndexByClazz(clazz: Class<out BaseTabOptionFragment?>): Int {
        if (tabs == null || tabs.isEmpty()) {
            return 0
        }
        for (i in tabs.indices) {
            val tabInfo = getTabAt<FragmentTabInfo>(i)
            if (tabInfo != null && tabInfo.fragmentClazz == clazz) {
                return i
            }
        }
        return 0
    }
}