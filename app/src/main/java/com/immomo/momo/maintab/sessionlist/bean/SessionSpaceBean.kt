package com.immomo.momo.maintab.sessionlist.bean

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

/**
 * <AUTHOR>
 */
class SessionSpaceBean {
    /**
     * 区分是跳转群还是聊天室
     * GROUP VCHAT
     */
    @Expose
    @SerializedName("type")
    var cardType: String = ""

    @Expose
    @SerializedName("icon")
    var icon: String = ""

    @Expose
    @SerializedName("tabText")
    var tabText: String = ""

    @Expose
    @SerializedName("title")
    var title: String = ""

    @Expose
    @SerializedName("subtitle")
    var subtitle: String = ""

    @Expose
    @SerializedName("avatar")
    var avatars: List<String>? = null

    @Expose
    @SerializedName("moreGoto")
    var moreGoto: String = ""

    @Expose
    @SerializedName("buttonText")
    var buttonText: String = ""

    @Expose
    @SerializedName("buttonGoto")
    var buttonGoto: String = ""

    @Expose
    @SerializedName("clickGoto")
    var clickGoto: String = ""

    @Expose
    @SerializedName("image")
    var bgUrl: String = ""

    /**
     * 群或者聊天室id
     */
    @Expose
    @SerializedName("cellId")
    var cellId: String = ""

}