package com.immomo.momo.maintab.session2

import com.immomo.annotations.appconfig.AppConfigField
import com.immomo.annotations.appconfig.appconfigv2.AppConfigV2
import com.immomo.momo.maintab.session2.apt.SessionAppConfigV2Getter

@AppConfigV2
object SessionAppConfigV2 {

    @AppConfigField(
        mark = "333",
        key = "enable",
        defValue = "0",
        isSysValue = true
    )
    var enable: Int = 0

    @AppConfigField(
        mark = "333",
        key = "report_session_init",
        defValue = "0",
        isSysValue = true
    )
    var reportSessionInit: Int = 0

    @AppConfigField(
        mark = "333",
        key = "session_blank_interval",
        defValue = "5",
        isSysValue = true
    )
    var sessionBlankInterval: Int = 5

    /**
     * 附近的人-im新老关系划分
     */
    @AppConfigField(
        mark = "335",
        key = "im_newfriend_scan_interval",
        defValue = "48"
    )
    var newfriendScanInterval: Int = 48

    @AppConfigField(
        mark = "335",
        key = "im_newfriend_scan_num",
        defValue = "100"
    )
    var newfriendScanNum: Int = 100

    @AppConfigField(
        mark = "335",
        key = "im_newfriend_session_min",
        defValue = "16"
    )
    var newfriendScanMin: Int = 16

    @AppConfigField(
        mark = "335",
        key = "im_newfriend_define",
        defValue = "48"
    )
    var newFriendDefine: Int = 48

    @AppConfigField(
        mark = "376",
        key = "im_hepai_scan_interval",
        defValue = "24"
    )
    var hePaiScanInterval: Int = 24

    @AppConfigField(
        mark = "376",
        key = "im_hepai_scan_num",
        defValue = "100"
    )
    var hePaiScanNum: Int = 100

    @AppConfigField(
        mark = "376",
        key = "im_hepai_session_min",
        defValue = "15"
    )
    var hePaiSessionMin: Int = 15

    @AppConfigField(
        mark = "383",
        key = "disk_folder_scan_switch",
        defValue = "1"
    )
    var diskFolderScanSwitch: Int = 1

    @AppConfigField(
        mark = "383",
        key = "disk_folder_cache_switch",
        defValue = "1"
    )
    var diskFolderCacheSwitch: Int = 1

    @AppConfigField(
        mark = "383",
        key = "disk_folder_cache_time",
        defValue = "345600000"
    )
    var diskFolderScanTime: String = "345600000"

    @AppConfigField(
        mark = "383",
        key = "disk_folder_switch",
        defValue = "0"
    )
    var diskFolderSwitch: Int = 0

    @AppConfigField(
        mark = "383",
        key = "disk_folder_layer",
        defValue = "1"
    )
    var diskFolderLayer: Int = 1

    @AppConfigField(
        mark = "383",
        key = "disk_file_filter",
        defValue = "500"
    )
    var diskFileFilter: Int = 500

    @AppConfigField(
        mark = "409",
        key = "global_video_player_dns",
        defValue = "0"
    )
    var globalVideoPlayDns: Int = 0

    @AppConfigField(
        mark = "413",
        key = "switch_active_user_top",
        defValue = "0"
    )
    var activeUserTopSwitch: Int = 0


    @AppConfigField(
        mark = "414",
        key = "greet_push_guide_interval",
        defValue = "72"
    )
    var greetPushGuideInterval: Int = 0

    @AppConfigField(
        mark = "418",
        key = "im_resource_expire_time",
        defValue = "366"
    )
    var imResourceExpireTime: Int = 366

    @AppConfigField(
        mark = "427",
        key = "gift_bubble_request_config",
        defValue = "0"
    )
    var giftBubbleRequest: Int = 0

    @AppConfigField(
        mark = "433",
        key = "filter_string_switch",
        defValue = "0"
    )
    var filterStringSwitch: Int = 0

    @AppConfigField(
        mark = "437",
        key = "global_video_player_again",
        defValue = "0"
    )
    var globalVideoPlayAgain: Int = 0

    @AppConfigField(
        mark = "444",
        key = "chat_draft_switch",
        defValue = "0",
        isSysValue = true
    )
    var chatDraftSwitch: Int = 0

    @AppConfigField(
        mark = "481",
        key = "key_today_fate_pormat",
        defValue = "今日缘分",
        isSysValue = true
    )
    var keyTodayFate: String = "今日缘分"

    @AppConfigField(
        mark = "559",
        key = "group_member_card_lua",
        defValue = "0"
    )
    var isGroupCardLuaSwitch: Int = 0

    @AppConfigField(mark = "565", key = "msg_scan_qiaoqiao_switch", defValue = "1")
    var msgFoldScanSwitch = 1

    @AppConfigField(mark = "565", key = "session_fold_opt_num", defValue = "50")
    var msgFoldScanCount = 50

    @AppConfigField(mark = "565", key = "msg_scan_qiaoqiao_interval", defValue = "6")
    var msgFoldScanInterval = 6

    @AppConfigField(mark = "565", key = "db_monitor_open", defValue = "0")
    var dbMonitorOpen = 0

    @AppConfigField(mark = "565", key = "db_opt_open", defValue = "0")
    var dbOptOpen = 0

    private var checkTableConfig = -1
    fun isDbMonitorOpen(): Boolean {
        return SessionAppConfigV2Getter.get().dbMonitorOpen() == 1
    }

    fun isDbOptOpen(): Boolean {
        return SessionAppConfigV2Getter.get().dbOptOpen() == 1
    }
}

