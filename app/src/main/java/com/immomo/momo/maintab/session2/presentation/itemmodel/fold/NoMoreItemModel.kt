package com.immomo.momo.maintab.session2.presentation.itemmodel.fold

import android.view.View
import com.immomo.android.mm.cement2.AsyncCementModel
import com.immomo.android.mm.cement2.CementViewHolder
import com.immomo.android.mm.cement2.IViewHolderCreator
import com.immomo.momo.R

/**
 * CREATED BY liu.chong
 * AT 2022/3/9
 */
class NoMoreItemModel :
    AsyncCementModel<String, NoMoreItemModel.VH>("") {
    init {
        id("NoMoreItemModel")
    }

    class VH(itemView: View) : CementViewHolder(itemView) {
    }

    override val layoutRes = R.layout.item_fold_session_list_nomore
    override val viewHolderCreator: IViewHolderCreator<VH> = object : IViewHolderCreator<VH> {
        override fun create(view: View): VH {
            return VH(view)
        }
    }

}

