package com.immomo.momo.maintab.session2.domain.interactor

import com.immomo.android.mm.kobalt.domain.fx.Option
import com.immomo.android.mm.kobalt.domain.interactor.UseCase
import com.immomo.momo.maintab.model.UserOnlineStatus
import com.immomo.momo.maintab.session2.domain.repository.ISessionListRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

/**
 * CREATED BY liu.chong
 * AT 2024/7/11
 */
class FetchUserInfoUseCase(
    dispatcher: CoroutineDispatcher,
    private val iSessionListRepository: ISessionListRepository
) : UseCase<Map<String, UserOnlineStatus>, Set<String?>>(
    dispatcher
) {
    override fun build(param: Option<Set<String?>>): Flow<Map<String, UserOnlineStatus>> = flow {
        param.fold({}) {
            emit(iSessionListRepository.refreshSessionUserStatus(it))
        }
    }
}