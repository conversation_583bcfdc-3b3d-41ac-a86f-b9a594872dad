package com.immomo.momo.maintab.session2.presentation.itemmodel

import android.animation.Animator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.*
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.text.TextUtils
import android.view.View
import android.view.ViewStub
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.immomo.android.mm.cement2.AsyncCementModel
import com.immomo.android.mm.cement2.CementViewHolder
import com.immomo.android.mm.cement2.IViewHolderCreator
import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.framework.kotlin.*
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.utils.TimeUtils
import com.immomo.framework.utils.UIUtils
import com.immomo.framework.view.lineview.DrawLineRelativeLayout
import com.immomo.framework.view.widget.SessionMsgStatusViewProxy
import com.immomo.lcapt.evlog.EVLog
import com.immomo.lcapt.evlog.EVLog.create
import com.immomo.mmstatistics.event.ClickEvent
import com.immomo.mmstatistics.event.ExposureEvent
import com.immomo.mmutil.toast.Toaster
import com.immomo.molive.gui.common.view.DragBubbleView
import com.immomo.momo.MomoKit
import com.immomo.momo.R
import com.immomo.momo.android.view.tips.TipManager
import com.immomo.momo.android.view.tips.tip.ITip
import com.immomo.momo.android.view.util.ViewAvalableListener
import com.immomo.momo.android.view.viewanimator.SmallBounceAnimator
import com.immomo.momo.brainmatch.BrainMatchCons
import com.immomo.momo.brainmatch.IBrainMatchLog
import com.immomo.momo.common.ClickUtils
import com.immomo.momo.feedlist.widget.avatarview.CircleAvatarAnimView
import com.immomo.momo.greet.GreetHelper
import com.immomo.momo.group.bean.GroupPreference
import com.immomo.momo.group.bean.GroupPreference.NotificationMode
import com.immomo.momo.innergoto.helper.ActivityHandler
import com.immomo.momo.maintab.session2.SessionGameBoxAppConfigV2
import com.immomo.momo.maintab.session2.SessionGiftSayHiAppConfigV2
import com.immomo.momo.maintab.session2.apt.SessionAppConfigV2Getter
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.defs.*
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.maintab.session2.domain.model.type.*
import com.immomo.momo.maintab.session2.presentation.itemmodel.events.SessionDraggableViewTouchListener
import com.immomo.momo.maintab.sessionlist.sort.ISessionSortLog
import com.immomo.momo.maintab.sessionlist.util.*
import com.immomo.momo.maintab.sessionlist.util.SessionHelper.ShimmerText.markPlayed
import com.immomo.momo.maintab.view.OptSVGAListener
import com.immomo.momo.maintab.view.ShimmerContainer
import com.immomo.momo.message.activity.*
import com.immomo.momo.message.helper.MaskChatHelper
import com.immomo.momo.message.helper.MatchFolder
import com.immomo.momo.mk.util.BusinessNotifySwitchUtils
import com.immomo.momo.mvp.maintab.mainbubble.MainBubbleViewImpl
import com.immomo.momo.performance.SimpleViewStubProxy
import com.immomo.momo.protocol.imjson.util.Debugger
import com.immomo.momo.raisefire.util.RightTriangleDrawable
import com.immomo.momo.service.bean.FolderType
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.sessions.MessageServiceHelper
import com.immomo.momo.service.user.ProfileUserService
import com.immomo.momo.statistics.EVAction
import com.immomo.momo.statistics.EVPage
import com.immomo.momo.statistics.logrecord.viewhelper.mode.ExposureModeWithHolder
import com.immomo.momo.util.ColorUtils
import com.immomo.momo.util.OnlineStatusUtils
import com.immomo.momo.util.SimpleAnimatorListener
import com.immomo.momo.util.StringUtils
import com.immomo.momo.util.view.BadgeView
import com.immomo.momo.videochat.friendvideo.friend.FriendQChatConstants
import com.immomo.momo.videochat.friendvideo.friend.FriendQChatWorker
import com.immomo.momo.voicechat.util.VChatSuperRoomSessionCache
import com.immomo.svgaplayer.view.MomoSVGAImageView
import java.util.*


interface SessionOnClickListener {
    fun onClicked(
        view: View,
        viewHolder: SessionItemModel.SessionViewHolder,
        session: SessionModel,
        adapterPosition: Int
    )
}

interface SessionOnLongClickListener {
    fun onLongClicked(
        view: View,
        viewHolder: SessionItemModel.SessionViewHolder,
        session: SessionModel,
        adapterPosition: Int
    )
}

interface SessionOnAvatarClickListener {
    fun onAvatarClicked(
        view: View,
        viewHolder: SessionItemModel.SessionViewHolder,
        session: SessionModel,
        adapterPosition: Int
    )
}

class SessionItemModel<M : SessionModel>(
    val session: M,
    private val onItemClicked: SessionOnClickListener? = null,
    private val onItemLongClicked: SessionOnLongClickListener? = null,
    private val onAvatarClicked: SessionOnAvatarClickListener? = null,
    private val onDrag: SessionDraggableViewTouchListener? = null,
    private val onFireClick: ((chatId: String?, sign: String?) -> Unit)? = null
) : AsyncCementModel<M, SessionItemModel.SessionViewHolder>(session), ExposureModeWithHolder {
    //<editor-fold desc="Common Methods">
    init {
        id(session.uniqueId)
    }

    override val layoutRes: Int
        get() = R.layout.listitem_session

    override val viewHolderCreator: IViewHolderCreator<SessionViewHolder>
        get() = object : IViewHolderCreator<SessionViewHolder> {
            override fun create(view: View): SessionViewHolder =
                SessionViewHolder(view)
        }

    override fun unbind(holder: SessionViewHolder) {
        if (holder.shimerLabel.isInflate()) {
            holder.shimerLabel.getStubView().endAnim()
        }
        holder.stopNoticeAnim()
        super.unbind(holder)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun bindData(holder: SessionViewHolder) {
        super.bindData(holder)
        holder.stopNoticeAnim()
        holder._session = session
        holder.bottomVisible = false

        holder.bindClickEvents()
        holder.bindLongClickEvents()

        //填充置顶相关UI
        holder.fillSticky()
        //填充通知模式UI
        holder.fillNotificationMode()
        //填充标题，p2p为用户昵称, 头像, 填充消息详情
        holder.fillNameAndContent()
        //填充时间戳
        holder.fillTimeStamp()
        // 消息状态显示部分
        holder.fillStatus()
        //填充消息详情前闪光label
        holder.fillPreContentShimmerLabel()
        //填充消息前缀
        holder.fillPreContent()
        //填充用户状态部分
        holder.fillUserStatus()
        //重新检查shimmer文本是否需要增加分隔符
        holder.recheckShimmerDot()

        holder.scheduleDisplaySettingBubble()
        holder.setBottomLayout()

        holder.updateFireView()

        holder.dragLayout.setOnTouchListener { v, event ->
            onDrag?.onTouch(
                v,
                event,
                holder.adapterPosition,
                holder.statusTextView_new,
                DragBubbleView.DRAG_FROM_LIST
            ) ?: false
        }
    }

    private fun SessionViewHolder.bindClickEvents() {
        if (onItemClicked != null) {
            itemView.setOnClickListener {
                onItemClicked.onClicked(it, this, session, adapterPosition)
            }
        } else {
            itemView.setOnClickListener(null)
        }
    }

    private fun SessionViewHolder.bindLongClickEvents() {
        if (onItemLongClicked != null) {
            itemView.setOnLongClickListener {
                onItemLongClicked.onLongClicked(it, this, session, adapterPosition)
                true
            }
        } else {
            itemView.setOnLongClickListener(null)
        }
    }
    //</editor-fold>

    //<editor-fold desc="Sticky">
    private fun SessionViewHolder.fillSticky() {
        if (session.baseInfo.sticky && session !is SayHiSessionModel) {
            tv_sticky.stubView.visibility = View.VISIBLE
        } else {
            tv_sticky.stubView.visibility = View.GONE
        }
    }
    //</editor-fold>

    //<editor-fold desc="Notification Icon">
    private fun getNotifcationIcon(@NotificationMode notificationModel: Int): Drawable? =
        when (notificationModel) {
            GroupPreference.NOTIFICATION_MUTE -> {
                UIUtils.getDrawable(R.drawable.ic_common_mute)
            }

            GroupPreference.NOTIFICATION_CLOSE -> {
                UIUtils.getDrawable(R.drawable.ic_common_close_message)
            }

            else -> {
                null
            }
        }

    private fun SessionViewHolder.refreshNotificationIcon(
        @NotificationMode notificationModel: Int
    ) {
        val d = getNotifcationIcon(notificationModel)
        if (d != null) {
            d.setBounds(0, 0, UIUtils.getPixels(14f), UIUtils.getPixels(14f))
            nameView.setCompoundDrawablePadding(UIUtils.getPixels(4f))
        }
        nameView.setCompoundDrawables(null, null, d, null)
    }

    private fun SessionViewHolder.fillNotificationMode() {
        val notifyMode = MessageServiceHelper.getSessionNotificationMode(session.sessionKey)
        refreshNotificationIcon(notifyMode)
    }
    //</editor-fold>
//
//    private val discussAvatarTransform by lazy {
//        ImageTransform.Custom(object : ImageTransformFunc {
//            val border2 = UIUtils.getPixels(2F)
//
//            //TODO: 优化这个Bitmap操作
//            override fun transform(
//                pool: BitmapPool,
//                toTransform: Bitmap,
//                outWidth: Int,
//                outHeight: Int
//            ): Bitmap {
//                val m = Matrix()
//                val width = toTransform.width - border2 * 2
//                val height = toTransform.height - border2 * 2
//                m.postTranslate(-border2.toFloat(), -border2.toFloat())
//
//                val result = pool.get(width, height, toTransform.config ?: Bitmap.Config.ARGB_8888)
//                // We don't add or remove alpha, so keep the alpha setting of the Bitmap we were given.
//                TransformationUtils.setAlpha(toTransform, result)
//                val canvas = Canvas(result)
//                canvas.drawBitmap(toTransform, m, Paint(TransformationUtils.PAINT_FLAGS))
//                canvas.setBitmap(null)
//                return result
//            }
//        })
//    }

    private fun SessionViewHolder.setupFaceViewClick() {
        if (onAvatarClicked != null) {
            faceView.isClickable = true
            faceView.setOnClickListener {
                if (!ClickUtils.isFastClick()) {
                    if (SessionKey.fromString(session.sessionKeyStr).isMaskChat()) {
                        Toaster.show("气氛值达到100%，可解锁资料页")
                        return@setOnClickListener
                    }
                    onAvatarClicked.onAvatarClicked(it, this, session, adapterPosition)
                }
            }
        } else {
            faceView.isClickable = false
            faceView.setOnClickListener(null)
        }
    }

    private fun SessionViewHolder.fillNameAndContent() {
        //防止异步同步同时进行导致头像错乱
        ImageLoader.cancel(faceView.imgAvatar)
        nameView.setTextColor(
            if (com.immomo.momo.util.MomoKit.isDarkMode(nameView.context)) {
                UIUtils.getColor(R.color.color_80fff)
            } else {
                UIUtils.getColor(R.color.color_text_3b3b3b)
            }
        )

        faceView.isClickable = false

        // cell聊天室专属房间在玩状态重置
        contentView.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null)
        contentView.compoundDrawablePadding = 0
        when (session) {
            is UserChatSessionModel -> {
                nameView.text = session.userName
                nameView.setTextColor(
                    UIUtils.getColor(
                        if (session.userIsVip)
                            R.color.font_vip_name else {
                            if (com.immomo.momo.util.MomoKit.isDarkMode(contentView.context))
                                R.color.color_80fff else R.color.color_text_3b3b3b
                        }
                    )
                )
                var avatar = session.userAvatar
                if (SessionKey.fromString(session.sessionKeyStr).isMaskChat()) {
                    avatar = KV.getUserStr(
                        MaskChatHelper.KEY_MOMO_MASK_CHAT_AVATAR,
                        MaskChatHelper.otherAvatar
                    )
                }

                ImageLoader.load(avatar)
                    .imageType(ImageType.ALBUM_250X250)
                    .placeholder(R.drawable.ic_common_def_header)
                    .canUseNight(true)
                    .into(faceView.imgAvatar)

                setupFaceViewClick()
                if (MatchFolder.isNewMatchFolder(session) && session.baseInfo.unreadMessageCount == 0) {
                    contentView.text = UIUtils.getString(R.string.diandian_both_like_to_chat)
                } else if (FriendQChatWorker.isReceivedRequest() && session.sessionId == FriendQChatConstants.getRemoteMomoid()) {
                    contentView.text = FriendQChatWorker.getReceiveRequestHint()
                } else {
                    contentView.text = if (!session.hasSpecialNotice() && session.hasDraft()) {
                        session.draftString
                    } else {
                        session.desc
                    }
                    if (ProfileUserService.getInstance().get(session.sessionId)?.getDeny()
                            ?.isDenied() == true
                    ) {
                        contentView.text = "该账号可能异常，请谨慎交谈"
                    }
                    if (session.baseInfo.foldType == FolderType.Spam) {
                        contentView.visibility = View.GONE
                    } else {
                        contentView.visibility = View.VISIBLE
                    }
                }
            }

            is GroupChatSessionModel -> {
                nameView.text = session.groupName
                nameView.setTextColor(
                    UIUtils.getColor(
                        if (session.groupIsVip) R.color.font_vip_name else {
                            if (com.immomo.momo.util.MomoKit.isDarkMode(nameView.context))
                                R.color.color_80fff else R.color.color_text_3b3b3b
                        }
                    )
                )

                ImageLoader.load(session.groupAvatar)
                    .imageType(ImageType.ALBUM_250X250)
                    .canUseNight(true)
                    .placeholder(R.drawable.ic_common_def_header)
                    .into(faceView.imgAvatar)

                setupFaceViewClick()

                contentView.text = if (!session.hasSpecialNotice() && session.hasDraft()) {
                    session.draftString
                } else session.desc
            }

            is DiscussChatSessionModel -> {
                nameView.text = session.discussName

                ImageLoader.load(session.discussAvatar)
                    .imageType(ImageType.ALBUM_250X250)
                    .placeholder(R.drawable.ic_common_def_header)
                    .canUseNight(true)
//                    .imageTransform(discussAvatarTransform)
                    .into(faceView.imgAvatar)

                setupFaceViewClick()

                contentView.text = if (!session.hasSpecialNotice() && session.hasDraft()) {
                    session.draftString
                } else session.desc
            }

            is VChatSuperRoomSessionModel -> {
                nameView.text = session.vChatName
                ImageLoader.load(session.vChatAvatar)
                    .imageType(ImageType.ALBUM_250X250)
                    .canUseNight(true)
                    .placeholder(R.drawable.ic_common_def_header)
                    .into(faceView.imgAvatar)
                setupFaceViewClick()

                // 如果有状态消息，忽略别的消息
                val roomStatus = VChatSuperRoomSessionCache.getStatus(session.sessionId)
                if (roomStatus != null && !TextUtils.isEmpty(roomStatus.desc)) {
                    contentView.setCompoundDrawablesWithIntrinsicBounds(
                        UIUtils.getDrawable(R.drawable.ic_vchat_my_room_icon),
                        null,
                        null,
                        null
                    )
                    contentView.compoundDrawablePadding = 6
                    contentView.text = roomStatus.desc
                } else {
                    contentView.text = session.desc
                }
            }

            is GotoSessionModel -> {
                nameView.text = session.title
                ImageLoader.load(session.icon)
                    .imageType(ImageType.URL)
                    .placeholder(R.drawable.ic_common_def_header)
                    .canUseNight(true)
                    .into(faceView.imgAvatar)
                contentView.text = session.text
            }

            is OfficialSessionModel -> {
                nameView.text = "订阅内容"
                ImageLoader.load(R.drawable.ic_header_rss)
                    .canUseNight(true)
                    .into(faceView.imgAvatar)

                contentView.text = session.desc
            }

            is GameBoxSessionModel -> {
                nameView.text = SessionGameBoxAppConfigV2.getConfigText()
                ImageLoader.load(SessionGameBoxAppConfigV2.getConfigIcon())
                    .placeholder(R.drawable.ic_session_fold_game_box)
                    .into(faceView.imgAvatar)

                contentView.text = session.desc
            }

            is SayHiSessionModel -> {
                nameView.text = UIUtils.getString(R.string.title_say_hi_message_receive)
                ImageLoader.load(R.drawable.ic_icon_hi_notice)
                    .canUseNight(true)
                    .into(faceView.imgAvatar)

                contentView.text = session.desc
            }

            is GiftSayHiSessionModel -> {
                nameView.text = SessionGiftSayHiAppConfigV2.getConfigText()
                ImageLoader.load(SessionGiftSayHiAppConfigV2.getConfigIcon())
                    .placeholder(R.drawable.ic_session_sayhi_gift_icon)
                    .into(faceView.imgAvatar)
                contentView.text = session.desc
            }

            is UniverseFoldSessionModel -> {
                nameView.text = "小宇宙"
                ImageLoader.load("https://s.momocdn.com/s1/u/jbbfhcija/im_session_universe.png")
                    .canUseNight(true)
                    .into(faceView.imgAvatar)
                contentView.text = session.desc
            }

            is FriendNoticeSessionModel -> {
                nameView.text = "好友提醒"
                ImageLoader.load(R.drawable.ic_icon_friend_notice)
                    .canUseNight(true)
                    .into(faceView.imgAvatar)

                contentView.text = session.text
            }

            is DebuggerSessionModel -> {
                nameView.text = Debugger.getInstance().name
                ImageLoader.load(Debugger.getInstance().loadImageId)
                    .imageType(ImageType.ALBUM_250X250)
                    .canUseNight(true)
                    .placeholder(R.drawable.ic_common_def_header)
                    .into(faceView.imgAvatar)

                contentView.text = session.desc
            }

            else -> {
                nameView.text = session.toString()
                contentView.text = ""
            }
        }
        bottomVisible = bottomVisible || StringUtils.isNotEmpty(contentView.text)
    }

    private fun SessionViewHolder.fillTimeStamp() {
        // 时间显示部分 [以用户消息时间戳为显示内容]
        var timeText: String? = ""

        if (session is UserChatSessionModel && session.onlineMsgTime.isNotEmpty()) {
            timeText = session.onlineMsgTime
        } else if (session.baseInfo.lastMsgId.isNotEmpty()) {
            timeText = TimeUtils.getTimeLineStringStyle3(Date(session.baseInfo.lastMessageTime))
        } else if (session is VChatSuperRoomSessionModel && session.baseInfo.lastFetchTime != 0L) {
            timeText = TimeUtils.getTimeLineStringStyle3(Date(session.baseInfo.orderId))
        } else if (session is FriendNoticeSessionModel && session.baseInfo.lastFetchTime != 0L) {
            timeText = TimeUtils.getTimeLineStringStyle3(Date(session.baseInfo.lastFetchTime))
        } else if (session is UniverseFoldSessionModel && session.lastMsgTime > 0) { // 小宇宙未读数
            timeText = TimeUtils.getTimeLineStringStyle3(Date(session.lastMsgTime))
        }
        timeStampView.text = timeText
    }

    private fun SessionViewHolder.fillStatus() {
        // 初始化
        statusTextView_new.visibility = View.GONE
        if (presentView.isInflate) {
            presentView.stubView.visibility = View.GONE
        }
        statusPointView.visibility = View.GONE
        timeStampView.visibility = View.VISIBLE
        if (videochatLogo.isInflate) {
            videochatLogo.stubView.visibility = View.GONE
        }
        if (msgStatusView.isInflate) {
            msgStatusView.stubView.visibility = View.GONE
        }

        if (FriendQChatWorker.isReceivedRequest()
            && session is UserChatSessionModel
            && session.baseInfo.sessionId == FriendQChatConstants.getRemoteMomoid()
        ) {
            videochatLogo.stubView.visibility = View.VISIBLE
            bottomVisible = true
        } else if (session.hasUnread()) {
            bottomVisible = true
            // 静默的对话，只显示圆点
            if (session.baseInfo.silentMessageCount > 0 || (session.sessionId == Session.ID.FolderOfficial && session.baseInfo.unreadMessageCount > 0)
                || (session.sessionId == Session.ID.GAME_BOX && session.baseInfo.unreadMessageCount > 0)
            ) {
                statusTextView_new.visibility = View.GONE
                statusPointView.visibility =
                    if (session.baseInfo.foldType == FolderType.Spam) View.GONE else View.VISIBLE
            } else {
                statusPointView.visibility = View.GONE
                //spam折叠列表中不显示未读数
                statusTextView_new.visibility =
                    if (session.baseInfo.foldType == FolderType.Spam) View.GONE else View.VISIBLE
                var oldV = 0
                if (statusTextView_new.getTag(R.id.tag_item_value) != null) {
                    oldV = statusTextView_new.getTag(R.id.tag_item_value) as? Int ?: 0
                }
                var sid: String? = null
                if (statusTextView_new.getTag(R.id.tag_item_session_id) != null) {
                    sid = statusTextView_new.getTag(R.id.tag_item_session_id) as? String
                }
                statusTextView_new.text = when (session) {
                    is UniverseFoldSessionModel -> { // 小宇宙未读数
                        MainBubbleViewImpl.getBubbleMax99String(session.baseInfo.unreadMessageCount)
                    }

                    else -> {
                        MainBubbleViewImpl.getBubbleMax999String(session.baseInfo.unreadMessageCount)
                    }
                }
                //播放动画:只有旧值大于新值时，代表有新消息，才播放
                if (session.baseInfo.unreadMessageCount > oldV && sid == session.baseInfo.sessionKey) {
                    val bounce = SmallBounceAnimator()
                    bounce.prepare(statusTextView_new)
                    bounce.start()
                }
                statusTextView_new.setTag(R.id.tag_item_value, session.baseInfo.unreadMessageCount)
                statusTextView_new.setTag(R.id.tag_item_session_id, session.baseInfo.sessionKey)
            }
        } else if (session is ChatSessionModel) {
            if (session.lastMessageType == Message.CONTENTTYPE_MESSAGE_NOTICE) {
                // 自己发送的notice 不显示状态图标，6.8 兼容消息撤回效果
                //            statusLayout.setVisibility(View.GONE);
                if (msgStatusView.isInflate) {
                    msgStatusView.stubView.visibility = View.GONE
                }
            } else if (session.showMessageStatus) {
                msgStatusView.fillStatus(session)
                bottomVisible = true
            } else {
                if (msgStatusView.isInflate) {
                    msgStatusView.stubView.visibility = View.GONE
                }
            }
        }

        if (session !is ChatSessionModel || (!session.hasSpecialNotice() && session.hasDraft())) {
            if (msgStatusView.isInflate) {
                msgStatusView.stubView.visibility = View.GONE
            }
        }
    }

    private fun SessionViewHolder.fillPreContentShimmerLabel() {
        val chatTag = session.castOrNull<UserChatSessionModel>()?.userChatTag?.orNull()
        if (chatTag != null && StringUtils.isNotBlank(chatTag.text)) {
            shimerLabel.visibility = View.VISIBLE
            bottomVisible = true
            val shimmerText = shimerLabel.getView(R.id.session_pre_content_shimmer_text) as TextView
            shimmerText.text = chatTag.text
            if (!SessionHelper.ShimmerText.hasPlayed(session.baseInfo.sessionId)) {
                shimerLabel.stubView.onRealEnd = {
                    markPlayed(session.baseInfo.sessionId)
                }
                shimerLabel.stubView.startAnim(1500, Random().nextInt(3000).toLong(), 0)
            }
        } else {
            shimerLabel.setVisibility(View.GONE)
        }
    }

    private fun logType28Prompt() {
        if (session is UserChatSessionModel
            && session.canShowType28Prompt()
            && !hasLogFateTodayShow.contains(session.sessionId)
        ) {
            //区别今日缘分
            if (session.lastType28Prompt == SessionAppConfigV2Getter.get().keyTodayFate()) {
                ExposureEvent.create(ExposureEvent.Type.Normal)
                    .page(EVPage.Growth.FateToday)
                    .action(EVAction.List.FateTodayShow)
                    .putExtra("remote_id", session.sessionId)
                    .putExtra("appid", session.type28AppId)
                    .submit()
                hasLogFateTodayShow.add(session.sessionId)
            }
        }
    }

    private fun SessionViewHolder.fillPreContent() {
        specialTextView.setTextColor(
            specialTextView.context.getColor(
                SessionHelper.SpecialText.getSpecialTextColor(
                    session
                )
            )
        )
        var specialTextStr = SessionHelper.SpecialText.getSpecialText(session)

        if (SessionKey.fromString(session.sessionKeyStr).isMaskChat()) {
            specialTextStr = "匿名闪聊"
        }
        if (!TextUtils.isEmpty(specialTextStr)) {
            //Special有文案的情况
            groupVideoTextview.visibility = View.GONE
            specialTextView.visibility = View.VISIBLE
            specialTextView.text = specialTextStr
            blackPointView.visibility = View.VISIBLE
            bottomVisible = true

            logType28Prompt()
            return
        }
        //Special没有文案的情况
        specialTextView.visibility = View.GONE
        if (session is GroupChatSessionModel && session.isVideoChatting) {
            groupVideoTextview.visibility = View.VISIBLE
            bottomVisible = true
            blackPointView.visibility = View.VISIBLE
            return
        }
        groupVideoTextview.visibility = View.GONE
        blackPointView.visibility = View.GONE
    }

    private fun SessionViewHolder.fillUserStatus() {
        if (session is UserChatSessionModel) {
//            //填充头像相关在线label装饰
            val showAvatarAni = avatarOnlineDecoration(session)
            fillUserTags(session)
            fillUserAvatarFrame(session, showAvatarAni)
            fillUserTextIconTags(session)
        } else {
            textIconSessionTag?.visibility = View.GONE
            onlineStatusDot.setVisibility(View.GONE)
            tvOnlineTag.setVisibility(View.GONE)
            officialId.stubView.visibility = View.GONE
            faceView.hideAnimView()
            chatlistItemCellTagsView?.visibility = View.GONE
            imgAvatarFrame?.visibility = View.GONE
        }
    }

    private fun SessionViewHolder.fillUserAvatarFrame(
        session: UserChatSessionModel, showAvatarAni: Boolean
    ) {
        imgAvatarFrame?.also { // 头像框
            it.visibility = session.avatarFrame?.getAvatarFrame()
                ?.takeIf { it.isNotBlank() && !showAvatarAni }?.let { frame ->
                    ImageLoader.load(frame).imageType(ImageType.URL).into(it)
                    View.VISIBLE
                } ?: View.GONE
        }
    }


    private fun getDefaultColor(str: String?): Int {
        return if ("聊天室" == str) Color.rgb(47, 207, 240) else Color.rgb(255, 94, 142)
    }

    private fun SessionViewHolder.avatarOnlineDecoration(session: UserChatSessionModel): Boolean {
        var showAnimation = false
        val onlineTag = session.userOnlineTag.orNull()
        if (onlineTag != null && onlineTag.action.isNotEmpty()) {
            val action = onlineTag.action
            onlineStatusDot.setVisibility(View.GONE)
            tvOnlineTag.setVisibility(if (onlineTag.name.isNotEmpty()) View.VISIBLE else View.GONE)
            tvOnlineTag.setText(onlineTag.name)
            val drawable = tvOnlineTag.getBackground() as GradientDrawable
            (drawable.mutate() as GradientDrawable).setColor(
                ColorUtils.parseColor(
                    onlineTag.tagColor,
                    getDefaultColor(onlineTag.name)
                )
            )
            if (onlineTag.showAnim) {
                faceView.setAnimColor(
                    ColorUtils.parseColor(
                        onlineTag.tagColor,
                        Color.rgb(255, 94, 142)
                    )
                )
                faceView.startAnim()
                showAnimation = true
            } else {
                faceView.hideAnimView()
            }
            faceView.setOnClickListener {
                var replaceAction = action
                if (session.baseInfo.foldType == FolderType.Unreply) {
                    onlineTagClickLog(session, "sayhi_list")
                    var userOnlineTag = session.userOnlineTag.orNull()
                    if (userOnlineTag != null && !StringUtils.isEmpty(userOnlineTag.roomPattern)) {
                        when (userOnlineTag.roomPattern) {
                            "CHAT_ROOM", "MARRY", "CHESS" -> replaceAction =
                                action.replace("session_bubble", "sayhi_list_avatar")

                            "GAME_WOLF", "PARTY" -> replaceAction =
                                action.replace("guide-union", "sayhi_list_avatar")
                        }
                    }
                } else {
                    onlineTagClickLog(session, "msg_list")
                }
                ActivityHandler.executeAction(
                    OnlineStatusUtils.getLiveAction(
                        replaceAction,
                        OnlineStatusUtils.ACTION_SESSION_LIST
                    ), itemView.context
                )
            }
        } else if (OnlineStatusUtils.isUserOnline(Date(session.userLocationTimestamp))) {
            tvOnlineTag.setVisibility(View.GONE)
            onlineStatusDot.setVisibility(View.VISIBLE)
            faceView.hideAnimView()
        } else {
            tvOnlineTag.setVisibility(View.GONE)
            onlineStatusDot.setVisibility(View.GONE)
            faceView.hideAnimView()
        }
        if (session.officialOperation == 1) {
            officialId.stubView.visibility = View.VISIBLE
        } else {
            officialId.stubView.visibility = View.GONE
        }
        return showAnimation
    }

    private fun SessionViewHolder.fillUserTags(session: UserChatSessionModel) {
        session.cellTagUniformLabels?.badgeModels?.also {
            chatlistItemCellTags.addInflateListener {
                chatlistItemCellTagsView = it
            }
            chatlistItemCellTags.visibility = View.VISIBLE
            chatlistItemCellTagsView?.setData(it)
            chatlistItemCellTagsView?.visibility = if (it.isNotEmpty()) View.VISIBLE else View.GONE
        } ?: kotlin.run {
            chatlistItemCellTagsView?.visibility = View.GONE
        }
    }

    /**
     * 火花 tag
     */
    private fun SessionViewHolder.fillUserTextIconTags(session: UserChatSessionModel) {
        session.textIconTag?.also { textIconTagData ->
            textIconStub.addInflateListener {
                textIconSessionTag = it
                textIconSessionTagBg = it.findViewById(R.id.bg_view)
                textIconSessionTagIcon = it.findViewById(R.id.icon)
                textIconSessionTagText = it.findViewById(R.id.text_content)
            }
            textIconStub.visibility = View.VISIBLE
            textIconTagData.bgColors?.map { colorStr ->
                ColorUtils.parseColor(colorStr, UIUtils.getColor(R.color.C_23))
            }?.toIntArray()?.takeIf { it.isNotEmpty() }?.also {
                val bg = GradientDrawable()
                bg.shape = GradientDrawable.RECTANGLE
                bg.colors = it
                bg.setOrientation(GradientDrawable.Orientation.LEFT_RIGHT)
                bg.cornerRadius = UIUtils.getPixels(10f).toFloat()
                textIconSessionTagBg?.background = bg
            }
            textIconTagData.icon?.takeIf { it.isNotEmpty() }?.also { imageUrl ->
                textIconSessionTagIcon?.also { ImageLoader.load(imageUrl).into(it) }
            }
            textIconSessionTagText?.text = textIconTagData.text ?: ""
        } ?: kotlin.run {
            textIconSessionTag?.visibility = View.GONE
        }
    }

    private fun onlineTagClickLog(session: UserChatSessionModel?, showPage: String) {
        if (session != null) {
            val userOnlineTag = session.userOnlineTag.orNull()
            if (userOnlineTag != null && userOnlineTag.showAnim) {
                create(ISessionSortLog::class.java).breathingLightClick(
                    session.sessionId,
                    showPage,
                    userOnlineTag.roomPattern,
                    ""
                )
            }
        }
    }


    private fun SessionViewHolder.recheckShimmerDot() {
        var shouldDot = false
        if (shimerLabel.getVisibility() != View.VISIBLE) {
            return
        }
        if (groupVideoTextview.getVisibility() == View.VISIBLE) {
            shouldDot = true
        }
        if (specialTextView.getVisibility() == View.VISIBLE
            && !specialTextView.getText().toString().isEmpty()
        ) {
            shouldDot = true
        }
        if (contentView.getVisibility() == View.VISIBLE
            && !contentView.getText().toString().isEmpty()
        ) {
            shouldDot = true
        }
        val view = shimerLabel.getView(R.id.black_point)
        if (view != null) {
            view.visibility = if (shouldDot) View.VISIBLE else View.GONE
        }
    }

    //<editor-fold desc="Settings Icon">
    /**
     * 修改：Session.TYPE_GOTO类型的消息ui样式
     *
     * @param session
     */
    private fun SessionViewHolder.scheduleDisplaySettingBubble() {
        moreSettingIv.setVisibility(View.GONE)
        timeStampView.setVisibility(View.VISIBLE)

        val moreType: SessionItemMoreType = ensureSessionMoreType()
        if (moreType == SessionItemMoreType.None) {
            return
        }
        moreSettingIv.setVisibility(View.VISIBLE)
        timeStampView.setVisibility(View.GONE)
        doDisplaySettingBubble(moreType)
        moreSettingIv.setOnClickListener(View.OnClickListener {
            if (itemLayout == null) {
                return@OnClickListener
            }
            if (!TextUtils.isEmpty(session.baseInfo.sessionId)) {
                ClickEvent.create().page(EVPage.Msg.Chatlist).action(EVAction.List.More)
                    .putExtra("remoteid", session.baseInfo.sessionId.replace("gotochat", ""))
                    .submit()
            }
            itemLayout.performLongClick()
        })
    }

    private fun ensureSessionMoreType(): SessionItemMoreType {
        if (session is GotoSessionModel && session.source == 1) {
            return SessionItemMoreType.Imj_Goto
        } else if (session is SayHiSessionModel) {
            // 新版招呼，且在reminder实验组内
            return SessionItemMoreType.Sayhi
        } else if (session is GiftSayHiSessionModel) {
            // 新版招呼，且在reminder实验组内
            return SessionItemMoreType.Sayhi
        }
        return SessionItemMoreType.None
    }

    private fun SessionViewHolder.doDisplaySettingBubble(moreType: SessionItemMoreType) {
        val typeGotoSessionId = session.castOrNull<GotoSessionModel>()?.businessId

        // 是否有屏蔽小铃铛的图标
        var isTypeGotSessionReceiveMsg = false
        if (moreType == SessionItemMoreType.Imj_Goto) {
            isTypeGotSessionReceiveMsg =
                BusinessNotifySwitchUtils.getIntance().getSwitchStatus(typeGotoSessionId)
        } else if (moreType == SessionItemMoreType.Sayhi) {
            isTypeGotSessionReceiveMsg = !GreetHelper.isGreetNotRemindSettingMode()
        }
        // 复用群组的屏蔽图标
        if (isTypeGotSessionReceiveMsg) {
            refreshNotificationIcon(GroupPreference.NOTIFICATION_OPEN)
        } else {
            refreshNotificationIcon(GroupPreference.NOTIFICATION_MUTE)
        }
        doDisplaySettingBubbleInner(moreType)
    }

    private fun SessionViewHolder.doDisplaySettingBubbleInner(moreType: SessionItemMoreType) {
        //这里有个显示逻辑，在第一次显示的时候，只有最新的goto消息才能显示气泡。
        var showRecordKey = ""
        var btnText = ""
        var forceShowBubble = false
        if (moreType == SessionItemMoreType.Sayhi) {
            forceShowBubble = GreetHelper.needShowGreetRemindGuide()
            btnText = UIUtils.getString(R.string.sayhi_more_tips_long_press_to_avoid_harras)
        }
        val haveBubble = (forceShowBubble
                || !TextUtils.isEmpty(showRecordKey) && !KV.getSysBool(showRecordKey, false))
        if (haveBubble && !TextUtils.isEmpty(btnText)) {
            innerShowBubbleTextOnMore(btnText, moreType, showRecordKey)
            afterMoreActionBubbleDisplayed(moreType, showRecordKey)
        }
    }

    private fun SessionViewHolder.innerShowBubbleTextOnMore(
        btnText: String,
        moreType: SessionItemMoreType,
        showRecordKey: String
    ) {
        val activity = MomoKit.getActivityWithView(moreSettingIv)
        if (activity == null || activity.isDestroyed || activity.isFinishing) {
            return
        }
        TipManager.bindActivity(activity).checkViewCanShowTip(moreSettingIv, ViewAvalableListener {
            if (activity.isDestroyed || activity.isFinishing) {
                return@ViewAvalableListener
            }
            // 修复RightTriangleDrawable不能调大小的问题
            val drawable = RightTriangleDrawable()
            drawable.setColor(UIUtils.getColor(R.color.color_4E7FFF))
            drawable.setHeight(30)
            drawable.setWidth(40)
            val tip = TipManager.bindActivity(activity)
                .setBackground(UIUtils.getDrawable(R.drawable.bg_corner_5dp_4e7fff))
                .setTriangles(null, null, drawable, null)
                .setTouchToHideAll(true)
                .showTipView(moreSettingIv, btnText, ITip.Triangle.RIGHT)
            tip?.autoHide(3000L)
        })
    }

    private fun SessionViewHolder.setBottomLayout() {
        bottomLayout.visibility = if (bottomVisible) View.VISIBLE else View.GONE
    }

    private fun afterMoreActionBubbleDisplayed(
        moreType: SessionItemMoreType,
        showRecordKey: String
    ) {
        if (!TextUtils.isEmpty(showRecordKey)) {
            KV.saveSysValue(showRecordKey, true)
        }
        if (moreType == SessionItemMoreType.Sayhi) {
            GreetHelper.setNeedShowGreetRemindGuide(false)
        }
    }

    private fun SessionViewHolder.updateFireView() {
        if (session is UserChatSessionModel) {
            if (StringUtils.isNotEmpty(session.fireIcon)) {
                imgFire?.let {
                    it.visibility = View.VISIBLE
                    it.alpha = 1f
                    if (com.immomo.momo.util.MomoKit.isDarkMode(imgFire.context)) {
                        ImageLoader.load(session.fireIconDark).into(it)
                    } else {
                        ImageLoader.load(session.fireIcon).into(it)
                    }
                    imgFire.setOnClickListener {
                        onFireClick?.invoke(session.chatId, session.fireSign)
                        imgFire.animate()
                            .alpha(0f)
                            .setDuration(200)
                            .setListener(object : SimpleAnimatorListener() {
                                override fun onAnimationEnd(animation: Animator) {
                                    imgFire.visibility = View.GONE
                                    timeStampView.visibility = View.VISIBLE
                                    setRow1Margin(0f)
                                }
                            }).start()
                    }
                    if (statusPointView.visibility == View.VISIBLE || statusTextView_new.visibility == View.VISIBLE) {
                        (it.layoutParams as? RelativeLayout.LayoutParams)?.marginEnd =
                            UIUtils.getPixels(10f)
                    } else {
                        (it.layoutParams as? RelativeLayout.LayoutParams)?.marginEnd =
                            UIUtils.getPixels(0f)
                    }
                    it.requestLayout()
                }
                timeStampView.visibility = View.GONE
                setRow1Margin(55f)
            } else {
                imgFire?.visibility = View.GONE
                setRow1Margin(0f)
            }
        } else {
            scheduleDisplaySettingBubble()
            imgFire?.visibility = View.GONE
            setRow1Margin(0f)
        }
    }

    private fun SessionViewHolder.setRow1Margin(marginEnd: Float) {
        (viewRow1?.layoutParams as? LinearLayout.LayoutParams)?.let {
            it.marginEnd = UIUtils.getPixels(marginEnd)
            viewRow1.layoutParams = it
        }
    }
    //</editor-fold>

    class SessionViewHolder(
        itemView: View
    ) : CementViewHolder(itemView), INoticeAnimItem {
        internal var _session: SessionModel? = null
        var bottomVisible = false

        // List显示部分
        val mHeadAnim =
            SimpleViewStubProxy<MomoSVGAImageView>(itemView.findViewById<ViewStub?>(R.id.session_avatar_anim_stub))
        val faceView: CircleAvatarAnimView = itemView.findViewById(R.id.chatlist_item_iv_face)
        val nameView: TextView = itemView.findViewById(R.id.chatlist_item_tv_name)
        val statusTextView_new: TextView = itemView.findViewById(R.id.chatlist_item_tv_status_new)
        val videochatLogo: SimpleViewStubProxy<ImageView> =
            SimpleViewStubProxy(itemView.findViewById(R.id.chatlist_item_iv_videochat_vs))
        val presentView: SimpleViewStubProxy<ImageView> =
            SimpleViewStubProxy(itemView.findViewById(R.id.chatlist_item_iv_present_vs))
        val timeStampView: TextView = itemView.findViewById(R.id.chatlist_item_tv_timestamp)
        val contentView: TextView = itemView.findViewById(R.id.chatlist_item_tv_content)
        val specialTextView: TextView = itemView.findViewById(R.id.chatlist_item_tv_special)
        val shimerLabel: SimpleViewStubProxy<ShimmerContainer> =
            SimpleViewStubProxy(itemView.findViewById(R.id.chatlist_item_shimmer_label_vs))

        val bottomLayout: View = itemView.findViewById(R.id.chatlist_item_layout_bottom_part)

        val groupVideoTextview: SimpleViewStubProxy<TextView> =
            SimpleViewStubProxy(itemView.findViewById(R.id.chatlist_item_tv_groupvideo_vs))
        val itemLayout: DrawLineRelativeLayout = itemView.findViewById(R.id.item_layout)
        val tv_sticky: SimpleViewStubProxy<TextView> =
            SimpleViewStubProxy(itemView.findViewById(R.id.chatlist_item_sticky_vs))

        val officialId =
            SimpleViewStubProxy<TextView>(itemView.findViewById(R.id.chatlist_item_official_id_vs))
        val chatlistItemCellTags =
            SimpleViewStubProxy<BadgeView>(itemView.findViewById(R.id.chatlist_item_cell_tags))
        val statusPointView: ImageView = itemView.findViewById(R.id.chatlist_item_iv_status_point)
        val dragLayout: View = itemView.findViewById(R.id.chatlist_item_layout_righttop_part)
        val blackPointView: SimpleViewStubProxy<ImageView> =
            SimpleViewStubProxy(itemView.findViewById(R.id.chatlist_item_black_point_vs))
        val msgStatusView: SessionMsgStatusViewProxy =
            SessionMsgStatusViewProxy(itemView.findViewById(R.id.chatlist_item_layout_status_vs))

        //在线状态的点点
        val onlineStatusDot: View = itemView.findViewById(R.id.img_online_status_dot)

        //直播状态
        val tvOnlineTag: TextView = itemView.findViewById(R.id.text_online_tag)

        val moreSettingIv: ImageView = itemView.findViewById(R.id.iv_more)
        var chatlistItemCellTagsView: BadgeView? = null
        val textIconStub: SimpleViewStubProxy<ConstraintLayout> =
            SimpleViewStubProxy(itemView.findViewById(R.id.chatlist_item_fire_tag)) // 集火花
        var textIconSessionTag: ConstraintLayout? = null // 火花
        var textIconSessionTagBg: View? = null
        var textIconSessionTagIcon: ImageView? = null
        var textIconSessionTagText: TextView? = null
        val imgFire: ImageView? = itemView.findViewById(R.id.session_fire)
        val viewRow1: View? = itemView.findViewById(R.id.chatlist_item_layout_top_part)
        val imgAvatarFrame: ImageView? = itemView.findViewById(R.id.ic_avatar_frame) // 头像挂件

        override fun itemIdentification(): Int = when (_session) {
            is FriendNoticeSessionModel -> {
                INoticeAnimItem.ID_FRIEND_NOTICE
            }

            is SayHiSessionModel -> {
                INoticeAnimItem.ID_HI_NOTICE
            }

            else -> -1
        }

        override fun playNoticeAnim(): Boolean {
            if (_session?.hasUnread() == true) {
                mHeadAnim.visibility = View.VISIBLE
                val anim = mHeadAnim.stubView
                if (anim.isAnimating) {
                    return false
                }
                anim.startSVGAAnimWithListener(
                    when {
                        itemIdentification() == INoticeAnimItem.ID_FRIEND_NOTICE -> INoticeAnimManager.URL_FRIEND_NOTICE_ANIM
                        itemIdentification() == INoticeAnimItem.ID_HI_NOTICE -> INoticeAnimManager.URL_HI_NOTICE_ANIM
                        else -> ""
                    },
                    2,
                    object : OptSVGAListener(2) {
                        override fun onCompleted() {
                            NoticeAnimManager.markPlayed(itemIdentification())
                        }
                    }
                )
                return true
            }
            return false
        }

        override fun stopNoticeAnim(): Boolean {
            var validateStop = false
            if (mHeadAnim.isInflate) {
                val animStubView = mHeadAnim.stubView
                if (animStubView.isAnimating) {
                    validateStop = true
                }
                animStubView.stopAnimCompletely()
            }
            return validateStop
        }

        private fun getStrFromView(stringBuilder: StringBuilder, stubView: TextView?) {
            if (stubView == null) return

            val text = stubView.text
            if (StringUtils.isNotEmpty(text)) {
                if (stringBuilder.isNotEmpty()) {
                    stringBuilder.append(";")
                }
                stringBuilder.append(text)
            }
        }

        fun getPreContent(): String {
            val stringBuilder = StringBuilder()
            if (specialTextView.visibility == View.VISIBLE) {
                getStrFromView(stringBuilder, specialTextView)
            }
            if (groupVideoTextview.visibility == View.VISIBLE) {
                getStrFromView(stringBuilder, groupVideoTextview.stubView)
            }
            if (shimerLabel.visibility == View.VISIBLE) {
                val shimmerText =
                    shimerLabel.getView(R.id.session_pre_content_shimmer_text) as? TextView
                getStrFromView(stringBuilder, shimmerText)
            }
            if (contentView.visibility == View.VISIBLE) {
                val text = contentView.text.toString()
                val split = text.split("·".toRegex()).toTypedArray()
                if (StringUtils.isNotEmpty(text) && split.size > 1) {
                    if (stringBuilder.isNotEmpty()) {
                        stringBuilder.append(";")
                    }
                    stringBuilder.append(split[0])
                }
            }
            return stringBuilder.toString()
        }

        fun getTimeStr(): String {
            return if (timeStampView.visibility == View.VISIBLE) {
                timeStampView.text.toString()
            } else ""
        }

        fun getOnlineText(): String {
            return ""
        }
    }

    companion object {
        //??有问题
        private val hasLogFateTodayShow = mutableListOf<String>()
    }

    override fun onCompletelyExposure(context: Context, position: Int, holder: CementViewHolder) {
        //nothing
    }

    override fun onExposure(context: Context, position: Int, holder: CementViewHolder) {
        if (session.baseInfo.foldType == FolderType.Unreply) {
            EVLog.create(ISessionSortLog::class.java)
                .secondSessionExposureUnreply(session.sessionId, session.sessionId)
            if (session != null && session is UserChatSessionModel) {
                val userOnlineTag = session.userOnlineTag.orNull()
                if (userOnlineTag != null && userOnlineTag.needLog && userOnlineTag.showAnim) {
                    EVLog.create(ISessionSortLog::class.java).breathingLightShow(
                        session.sessionId,
                        "sayhi_list",
                        userOnlineTag.roomPattern,
                        "",
                        session.sessionId + "_sayhi_list"
                    )
                }
            }
        } else if (session.baseInfo.foldType == FolderType.NewBOY) {
            EVLog.create(ISessionSortLog::class.java)
                .secondSessionExposureNewBoy(session.sessionId, session.sessionId)

        } else if (session.baseInfo.foldType == FolderType.HePai) {
            EVLog.create(ISessionSortLog::class.java)
                .secondSessionExposureHePai(session.sessionId, session.sessionId)
        } else if (PaasSessionDefinition.businessFromSessionId(session.sessionId) == BrainMatchCons.BID) {
            EVLog.create(IBrainMatchLog::class.java).logSessionShow(
                PaasSessionDefinition.userIdFromSessionId(session.sessionId),
                position
            )
        }
    }
}