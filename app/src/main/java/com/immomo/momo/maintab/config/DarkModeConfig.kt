package com.immomo.momo.maintab.config

import com.immomo.android.router.momo.AppConfigRouter
import com.immomo.annotations.appconfig.AppConfigField
import com.immomo.annotations.appconfig.appconfigv1.AppConfigV1
import com.immomo.framework.storage.kv.KV
import com.immomo.momo.maintab.config.DarkModeConfig.KEY_558_DARK_MODE_GUIDE
import com.immomo.momo.maintab.config.DarkModeConfig.KEY_558_DARK_MODE_MANUFACTURER
import com.immomo.momo.maintab.config.DarkModeConfig.KEY_558_DARK_MODE_SWITCH
import com.immomo.momo.maintab.config.DarkModeConfig.KEY_558_DARK_MODO_SWITCH_SWITCH
import com.immomo.momo.maintab.config.apt.NightModeConfigV1Getter
import com.immomo.momo.setting.tools.NightModelHelper.KEY_NIGHT_MODEL_STATUS
import com.immomo.momo.setting.tools.NightModelHelper.OFF_STATUS
import com.immomo.momo.util.DeviceUtils
import com.immomo.momo.util.StringUtils
import org.json.JSONObject

object DarkModeConfig {
    const val KEY_558_DARK_MODO_SWITCH_SWITCH = "558_dark_modo_switch_switch"
    const val KEY_558_DARK_MODE_SWITCH = "558_dark_mode_switch"
    const val KEY_558_DARK_MODE_GUIDE = "558_dark_mode_guide"
    const val KEY_558_DARK_MODE_MANUFACTURER = "558_dark_mode_manufacturer"
    private fun darkModeSwitchSwitch(): Int {
        return KV.getSysInt(KEY_558_DARK_MODO_SWITCH_SWITCH, 1)
    }

    private fun darkModeSwitch(): Int {
        return KV.getSysInt(KEY_558_DARK_MODE_SWITCH, 0)
    }

    private fun darkModeGuide(): Int {
        return KV.getSysInt(KEY_558_DARK_MODE_GUIDE, 0)
    }

    private fun manufactureList(): String {
        return KV.getSysStr(KEY_558_DARK_MODE_MANUFACTURER, "")
    }

    /**
     * 暗黑引导打开了
     */
    fun darkModeGuideOpen() = isGuideOpen

    private var inited = false
    private var isGuideOpen = false
    private var _configSupport = false
    fun configSupport(): Boolean {
        try {
            if (!inited) {
                inited = true
                val allowModeSwitch = (darkModeSwitchSwitch() == 1)
                if (allowModeSwitch) {
                    isGuideOpen = (darkModeGuide() == 1)

                    val existDarkMode = KV.getSysInt(KEY_NIGHT_MODEL_STATUS, OFF_STATUS)
                    if (existDarkMode == OFF_STATUS) {
                        val manufactureWhiteList = manufactureList().split(",")
                        val manufacture = DeviceUtils.getManufacturer().toLowerCase()
                        val inWhiteList = manufactureWhiteList?.find {
                            StringUtils.notEmpty(it) && manufacture.contains(it)
                        } != null

                        val modeOpen = (darkModeSwitch() == 1)
                        val blockFromV1 = (NightModeConfigV1Getter.get().shield() == 1)

                        _configSupport = modeOpen && inWhiteList && !blockFromV1
                    } else {
                        _configSupport = true
                    }
                }
            }
        } catch (ignore: Exception) {
        }
        return _configSupport
    }
}

@AppConfigV1
object NightModeConfigV1 {
    @AppConfigField(key = "shield", mark = "4000116", defValue = "1", isSysValue = true)
    val shield = 1
}


class NightModeConfigV2 : AppConfigRouter.ConfigHandler {
    override fun getMark(): String {
        return "558"
    }

    override fun getType(): AppConfigRouter.Type {
        return AppConfigRouter.Type.V2
    }

    override fun handle(jsonObject: JSONObject) {
        try {
            val allowModeSwitch = jsonObject.optInt("dark_mode_switch_switch")
            KV.saveSysValue(KEY_558_DARK_MODO_SWITCH_SWITCH, allowModeSwitch)

            val darkMode = jsonObject.optInt("dark_mode_switch")
            KV.saveSysValue(KEY_558_DARK_MODE_SWITCH, darkMode)

            val maniufactureList = jsonObject.optString("dark_mode_manufacturer")
            KV.saveSysValue(KEY_558_DARK_MODE_MANUFACTURER, maniufactureList)

            val darkModeGuide = jsonObject.optInt("dark_mode_guide")
            KV.saveSysValue(KEY_558_DARK_MODE_GUIDE, darkModeGuide)
        } catch (ignore: Exception) {

        }
    }
}