package com.immomo.momo.maintab.sessionlist.dialog

import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.view.animation.AnimationSet
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatDialog
import androidx.constraintlayout.widget.ConstraintLayout
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.R
import com.immomo.momo.audiomatch.model.AudioPopupModel
import com.immomo.momo.maintab.sessionlist.util.SessionTopOperatorRecord
import com.immomo.momo.util.MomoKit
import com.mln.watcher.safe

/**
 * 连麦引导弹窗
 */
class SessionTopEntryGuidDialog(
    context: Context,
    private val popupModel: AudioPopupModel,
    private val onClickOk: (() -> Unit)? = null,
    private val onClickClose: (() -> Unit)? = null
) :
    AppCompatDialog(context, R.style.customDialog), View.OnClickListener {

    companion object {

        const val KEY_IS_SHOW_DIALOG = "key_is_show_session_top_audio_guide_dialog"

        fun isShownDialog() = KV.getUserBool(KEY_IS_SHOW_DIALOG, false)

        fun onShownDialog() = KV.saveUserValue(KEY_IS_SHOW_DIALOG, true)
    }

    private val animationSet = AnimationSet(true)
    var guideContainer: ConstraintLayout? = null
    var headIcon: ImageView? = null
    var tvOK: TextView? = null
    var tvClose: TextView? = null
    var ivClose: ImageView? = null
    var tvTitle: TextView? = null
    var tvDesc: TextView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_session_top_entry_guild)
        setCanceledOnTouchOutside(false)
        window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN)
        guideContainer = findViewById(R.id.heart_beat_container)
        headIcon = findViewById(R.id.head_icon)
        tvOK = findViewById(R.id.tv_ok)
        tvClose = findViewById(R.id.tv_close)
        ivClose = findViewById(R.id.iv_close)
        tvDesc = findViewById(R.id.tv_desc)
        tvTitle = findViewById(R.id.tv_content)
        headIcon?.also {
            (if (MomoKit.isDarkMode()) {
                popupModel.darkImgUrl.safe()
            } else {
                popupModel.imgUrl.safe()
            }).takeIf { it.isNotBlank() }?.also { imgUrl ->
                ImageLoader.load(imgUrl).into(it)
            }
        }
        tvTitle?.text = popupModel.title.safe()
        tvDesc?.text = popupModel.text.safe()
        tvOK?.text = popupModel.submitText.safe()
        tvClose?.text = popupModel.cancelText.safe()

        tvOK?.setOnClickListener(this)
        ivClose?.setOnClickListener(this)
        tvClose?.setOnClickListener(this)
        setOnDismissListener {
            animationSet.cancel()
            guideContainer?.clearAnimation()
        }
        guideContainer?.layoutParams?.also {
            it.width = UIUtils.getScreenWidth() - UIUtils.getPixels(60f)
            guideContainer?.layoutParams = it
        }
        animationSet.duration = 300
        val alphaAnimation = AlphaAnimation(0.0f, 1.0f)
        val scaleAnimation = ScaleAnimation(
            0f,
            1f,
            0f,
            1f,
            Animation.RELATIVE_TO_SELF,
            0.5f,
            Animation.RELATIVE_TO_SELF,
            0.5f
        )
        alphaAnimation.duration = 300
        scaleAnimation.duration = 300
        animationSet.addAnimation(alphaAnimation)
        animationSet.addAnimation(scaleAnimation)
        guideContainer?.startAnimation(animationSet)
        onShownDialog()
        SessionTopOperatorRecord.dialogJumpExp()
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.tv_ok -> {
                dismiss()
                onClickOk?.invoke()
                SessionTopOperatorRecord.dialogJumpClick("have_look")
            }

            R.id.iv_close -> {
                dismiss()
                SessionTopOperatorRecord.dialogJumpClick("close")
            }

            R.id.tv_close -> { // 退出
                dismiss()
                onClickClose?.invoke()
                SessionTopOperatorRecord.dialogJumpClick("exit")
            }
        }
    }

}