package com.immomo.momo.maintab.view;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.widget.RelativeLayout;


/**
 * <AUTHOR>
 * @time 2025年02月14日
 */
public class SessionListPageLayout extends RelativeLayout {


    private float initX, initY;
    private OnMoveListener onMoveListener;


    public SessionListPageLayout(Context context) {
        super(context);
    }

    public SessionListPageLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public SessionListPageLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (ev.getAction() == MotionEvent.ACTION_DOWN) {
            initX = ev.getX();
            initY = ev.getY();
        }
        float deltaX = ev.getX() - initX;
        float deltaY = ev.getY() - initY;
        if (onMoveListener != null) {
            onMoveListener.onMove(initX, initY, deltaX, deltaY);
        }
        try {
            return super.dispatchTouchEvent(ev);
        } catch (IllegalArgumentException ex) {
            Log.e("SessionListPageLayout",
                    "dispatchTouchEvent: " + ex.getMessage(), ex);
        }
        return false;
    }

    public void setOnMoveListener(OnMoveListener onMoveListener) {
        this.onMoveListener = onMoveListener;
    }

    public interface OnMoveListener {
        void onMove(float initX, float initY, float deltaX, float deltaY);
    }
}
