package com.immomo.momo.maintab.sessionlist.bean

import androidx.annotation.Keep
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

@Keep
data class MaskChatData(
    @Expose
    @SerializedName("notice")
    var notice: String = "",
    @Expose
    @SerializedName("avatars")
    var avatars: List<String> = emptyList(),
    @Expose
    @SerializedName("title")
    var title: String = "",

    @Expose
    @SerializedName("des")
    var des: String = "",

    @Expose
    @SerializedName("tips")
    var tips: String = "",

    @Expose
    @SerializedName("chance")
    var chance: Int = 0,

    @Expose
    @SerializedName("action")
    var action: String = "",

    @Expose
    @SerializedName("source")
    var source: String = "",

    @Expose
    @SerializedName("extraKey")
    var extraKey: String = "",

    @Expose
    @SerializedName("items")
    var items: Items?,

    @Expose
    @SerializedName("canClaimFreeAccelerateCard")
    var canClaimFreeAccelerateCard: Boolean = false,

    @Expose
    @SerializedName("newUser")
    var newUser: Int = 0,

    @Expose
    @SerializedName("originData")
    var originData: String? = ""

    )

@Keep
data class Items(
    @Expose
    @SerializedName("remainAccelerateCount") var remainAccelerateCount: Int = 0,
    @Expose
    @SerializedName("freeAccelerateRemainSecond") var freeAccelerateRemainSecond: Int = 0,
    @Expose
    @SerializedName("fingerGuessCardRemainSec") var fingerGuessCardRemainSec: Int = 0,
    @Expose
    @SerializedName("matchCardPrice") var matchCardPrice: Int = 0,
    @Expose
    @SerializedName("accelerateCardPrice") var accelerateCardPrice: Int = 0,
    @Expose
    @SerializedName("cardPackagePrice") var cardPackagePrice: Int = 0
)

