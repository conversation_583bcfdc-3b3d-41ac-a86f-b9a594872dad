package com.immomo.momo.maintab

import android.app.Activity
import android.content.Context
import android.os.Build
import android.os.Environment
import android.text.TextUtils
import android.view.View
import android.view.WindowManager
import androidx.annotation.Nullable
import com.cosmos.mdlog.MDLog
import com.immomo.momo.LogTag
import com.immomo.momo.mulog.MUAppBusiness
import com.immomo.momo.mulog.MULog
import com.immomo.momo.mulog.MULogConstants
import com.immomo.momo.mulog.MURealtimeLog
import com.immomo.momo.mulog.pair.MUPairItem
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.lang.reflect.Method
import java.util.*


object NotchUtils {
    private const val VIVO_NOTCH = 0x00000020//是否有刘海
    private const val VIVO_FILLET = 0x00000008//是否有圆角

    fun openFullScreen(acitivty: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            val window = acitivty.window
            if (window != null) {
                try {
                    val attributes = window.attributes
                    if (attributes != null) {
                        attributes.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
                        window.attributes = attributes
                        innerOpenFullScreen(acitivty)
                    }
                } catch (e: Throwable) {
                    uploadSplashLog(e.message)
                }
            }
        } else if (isNotchScreen(acitivty)) {
            innerOpenFullScreen(acitivty)
        }
    }

    private fun innerOpenFullScreen(activity: Activity?) {
        val decorView = activity?.window?.decorView
        if (decorView != null) {
            var systemUiVisibility = decorView.systemUiVisibility
            val flags = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_FULLSCREEN
            systemUiVisibility = systemUiVisibility or flags
            decorView.systemUiVisibility = systemUiVisibility
        }
    }


    private fun isNotchScreen(context: Activity?): Boolean {
        val manufacturer = Build.MANUFACTURER.toLowerCase()
        return when (manufacturer) {
            "xiaomi" -> isNotchScreenForMiUi()
            "vivo" -> isNotchScreenForVivo(context)
            "oppo" -> isNotchScreenForOppo(context)
            "huawei" -> isNotchScreenForHuawei(context)
            else -> false
        }
    }


    private fun isNotchScreenForHuawei(context: Context?): Boolean {
        var ret = false
        try {
            val cl = context?.classLoader
            val hwNotchSizeUtil = cl?.loadClass("com.huawei.android.util.HwNotchSizeUtil")
            val get = hwNotchSizeUtil?.getMethod("hasNotchInScreen")
            ret = get?.invoke(hwNotchSizeUtil) as Boolean
        } catch (e: ClassNotFoundException) {

            uploadSplashLog("getNotchSize ClassNotFoundException")
        } catch (e: NoSuchMethodException) {
            uploadSplashLog("getNotchSize NoSuchMethodException")
        } catch (e: Exception) {
            uploadSplashLog("getNotchSize Exception")
        } finally {
            return ret
        }
    }

    private fun isNotchScreenForOppo(context: Context?): Boolean {
        var result = false
        if (context != null && context.packageManager != null) {
            result = context.packageManager.hasSystemFeature("com.oppo.feature.screen.heteromorphism")
        }
        return result
    }


    private fun isNotchScreenForVivo(context: Context?): Boolean {
        var ret = false
        try {
            val classLoader = context?.classLoader
            val feature = classLoader?.loadClass("android.util.FtFeature")
            val method = feature?.getMethod("isFeatureSupport", Int::class.java)
            ret = method?.invoke(feature, VIVO_FILLET) as Boolean || method.invoke(feature, VIVO_NOTCH) as Boolean
        } catch (e: ClassNotFoundException) {
            uploadSplashLog("getNotchSize ClassNotFoundException")
        } catch (e: NoSuchMethodException) {
            uploadSplashLog("getNotchSize NoSuchMethodException")
        } catch (e: Exception) {
            uploadSplashLog("getNotchSize Exception")
        } finally {
            return ret
        }
    }


    /**
     * 是否小米的刘海屏，判断方法参见 https://dev.mi.com/console/doc/detail?pId=1293
     * 此方法应该结合 [.isMIUI] 使用
     *
     * @return
     */
    private fun isNotchScreenForMiUi(): Boolean {
        var isNotch = false
        val value = readSystemProperty("ro.miui.notch", null)
        if (value.equals("1")) {
            isNotch = true
        }
        return isNotch
    }


    private fun readSystemProperty(key: String, defval: String?): String? {
        val properties = Properties()

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            try {
                properties.load(FileInputStream(File(Environment.getRootDirectory(), "build.prop")))
                return properties.getProperty(key, defval)
            } catch (e: IOException) {
            }

        } else {
            //8.0以上系统，不能直接读取 build.prop，会报 Permission Denied 错误，只能反射来获取
            try {
                val clzSystemProperties = Class.forName("android.os.SystemProperties")
                val getMethod = clzSystemProperties.getDeclaredMethod("get", String::class.java)
                val value = inVoKeGetLowerCaseName(properties, getMethod, key)
                return if (!TextUtils.isEmpty(value)) value else defval
            } catch (ex: Exception) {
                uploadSplashLog(ex.message)
            }

        }
        return defval
    }

    @Nullable
    private fun inVoKeGetLowerCaseName(p: Properties, get: Method, key: String): String? {
        var name: String? = p.getProperty(key)
        if (name == null) {
            try {
                name = get.invoke(null, key) as String
            } catch (ex: Exception) {
                MDLog.e(LogTag.Ad.Splash, ex.message)
            }

        }
        if (name != null) name = name.toLowerCase()
        return name
    }


    fun uploadSplashLog(errorMsg: String?) {
        MDLog.e(LogTag.Ad.Splash, if (TextUtils.isEmpty(errorMsg)) "" else errorMsg)
        MULog.business(MULogConstants.BUSINESS_MOMO_BASIC)
                .secondLBusiness(MUAppBusiness.Basic.SPLASH)
                .addBodyItem(MUPairItem.errorMsg(errorMsg))
                .commit()

    }
}