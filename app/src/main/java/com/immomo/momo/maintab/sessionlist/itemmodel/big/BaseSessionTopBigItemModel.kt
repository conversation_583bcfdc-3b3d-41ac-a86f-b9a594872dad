package com.immomo.momo.maintab.sessionlist.itemmodel.big

import android.animation.Animator
import android.graphics.drawable.Drawable
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.cosmos.mdlog.MDLog
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.cement.CementAdapter
import com.immomo.framework.cement.CementViewHolder
import com.immomo.framework.kotlin.ImageLoaderOptions
import com.immomo.framework.kotlin.ImageLoadingListener
import com.immomo.framework.view.dp
import com.immomo.momo.R
import com.immomo.momo.android.view.RoundCornerFrameLayout
import com.immomo.momo.maintab.sessionlist.bean.CardItemData
import com.immomo.momo.maintab.sessionlist.itemmodel.base.BaseSessionTopOperatorChildModel
import com.immomo.momo.util.MomoKit
import com.immomo.svgaplayer.SVGAAnimListenerAdapter
import com.immomo.svgaplayer.bean.BaseInsertBean
import com.immomo.svgaplayer.bean.InsertImgBean
import com.immomo.svgaplayer.view.MomoSVGAImageView
import com.mm.mediasdk.utils.UIUtils

/**
 * 大卡片
 */
open class BaseSessionTopBigItemModel(
    itemModeOriginData: CardItemData
) : BaseSessionTopOperatorChildModel<BaseSessionTopBigItemModel.ViewHolder>(
    itemModeOriginData, false
) {

    companion object {

        const val TAG = "SessionTopChildBigItemModel"

        val BIG_CARD_SIZE = 250.dp
        val MIDDLE_CARD_SIZE = 190.dp   // 有头像中卡
        val SMALL_CARD_SIZE = 145.dp // 没头像小卡态
        val SMALL_CARD_PADDING_SIZE = 15.dp // 没头像小卡态
        val AVATAR_CARD_PADDING_SIZE = 55.dp // 有头像中卡态
    }

    val runningAnimators = mutableListOf<Animator>() // 正在运行的动画

    private var runHintAnimationFilterAttach = true

    var curViewHolder: ViewHolder? = null

    var isDetachFromWindow = false

    override fun getLayoutRes() = R.layout.listitem_session_top_child_big_item_model

    override fun getViewHolderCreator() =
        CementAdapter.IViewHolderCreator { itemView: View -> ViewHolder(itemView) }

    /**
     * 使用bindCustomData方法进行重写
     */
    override fun bindData(holder: ViewHolder) {
        super.bindData(holder)
        MDLog.i(TAG, "bindData=${itemModeData.bizKey}")
        curViewHolder = holder
        // 设置卡片背景
        setViewBgColor(holder.cardContainer, itemModeData.bgColor)
        showCommonStaticIcon(holder.itemBgIcon, itemModeData.bgImg, object :
            ImageLoadingListener<Drawable> {
            override fun onLoadCompleted(model: ImageLoaderOptions.Model, resource: Drawable) {
                super.onLoadCompleted(model, resource)
                holder.cardContainer.setBackgroundColor(UIUtils.getColor(R.color.transparent))
            }
        })
    }

    fun refreshNormalViewData(holder: ViewHolder) {
        setCommonTitle(holder.normalItemTitle, itemModeData.title)
        setCommonTitle(holder.normalItemSubTitle, itemModeData.subTitle)
        setButtonView(holder.btnTxtContainer, holder.btnTxt)
    }

    fun showNormalView(holder: ViewHolder) {
        holder.normalHeadAllContainer.scaleX = 1f
        holder.normalHeadAllContainer.scaleY = 1f
        holder.normalHeadAllContainer.alpha = 1f
        holder.btnTxtContainer.alpha = 1f
        holder.itemBgSvga.alpha = 1f
        holder.normalTitleContainer.alpha = 1f
        holder.normalTitleContainer.translationY = 0f
        holder.expandHeadSvga.stopAnimCompletely()
        holder.expandHeadAllContainer.visibility = View.GONE
        holder.expandTitleContainer.visibility = View.GONE
        holder.expandBottomBtnContainer.visibility = View.GONE
        holder.normalTitleContainer.visibility = View.VISIBLE
        holder.btnTxtContainer.visibility =
            if (itemModeData.button != null) View.VISIBLE else View.GONE
        val emotion = itemModeData.emotion
        if (emotion != null) {
            if (MomoKit.isDarkMode()) {
                emotion.dark?.animatedImg
            } else {
                emotion.light?.animatedImg
            }.safe().takeIf { it.isNotBlank() }?.also {
                holder.itemBgSvga.stopAnimCompletely()
                holder.itemBgSvga.startSVGAAnim(it, -1)
                holder.itemBgSvga.visibility = View.VISIBLE
            } ?: kotlin.run {
                holder.itemBgSvga.stopAnimCompletely()
                holder.itemBgSvga.visibility = View.GONE
            }
        } else {
            holder.itemBgSvga.stopAnimCompletely()
            holder.itemBgSvga.visibility = View.GONE
        }
        refreshHeadBanner(true)
    }

    final override fun refreshHolderView() {
        curViewHolder?.also { refreshHolderView(it) }
    }

    open fun refreshHolderView(holder: ViewHolder) {
        kotlin.runCatching {
            isDetachFromWindow = false
        }
    }

    fun refreshHeadBanner(startPlay: Boolean, fromExpand: Boolean = false) {
        val holder = curViewHolder ?: return
        val avatarFrame =
            if (fromExpand) itemModeData.expand?.avatarFrame else itemModeData.avatarFrame
        val avatars = if (fromExpand) itemModeData.expand?.avatars else itemModeData.avatars
        val headIcon = if (fromExpand) holder.expandHeadIcon else holder.normalHeadIcon
        val headIconBg = if (fromExpand) holder.expandHeadIconBg else holder.normalHeadIconBg
        val itemHeadSvga = if (fromExpand) holder.expandHeadSvga else holder.itemHeadSvga
        itemHeadSvga.stopAnimCompletely()
        val headContainer =
            if (fromExpand) holder.expandHeadContainer else holder.normalHeadContainer
        avatarFrame?.let {
            if (MomoKit.isDarkMode()) it.dark else it.light
        }?.also {
            val animatedImg = it.animatedImg.safe()
            if (animatedImg.isNotBlank()) { // 动态svga
                buildSvgaHead(animatedImg, avatars, startPlay, itemHeadSvga)
                headContainer.visibility = View.INVISIBLE
                itemHeadSvga.visibility = View.VISIBLE
            } else { // 静态的背景
                showCommonStaticIcon(headIcon, avatars?.firstOrNull()) // 头像
                showCommonStaticIcon(headIconBg, it.staticImg.safe()) // 头像背景
                headContainer.visibility = View.VISIBLE
                itemHeadSvga.visibility = View.INVISIBLE
            }
        } ?: kotlin.run {
            headContainer.visibility = View.INVISIBLE
            itemHeadSvga.visibility = View.GONE
        }
        if (!startPlay) {
            headContainer.visibility = View.INVISIBLE
            itemHeadSvga.visibility = View.INVISIBLE
        }
    }

    private fun buildSvgaHead(
        headAnimatedImg: String,
        avatars: List<String>?,
        startPlay: Boolean,
        itemHeadSvga: MomoSVGAImageView
    ) {
        val baseInsertBeans = arrayListOf<BaseInsertBean>()
        avatars?.forEachIndexed { index, s ->
            val avatar = InsertImgBean("head_0${index + 1}", s.safe(), true)
            baseInsertBeans.add(avatar)
        }
        if (baseInsertBeans.isNotEmpty()) {
            itemHeadSvga.insertBeanList(baseInsertBeans)
        }
        if (startPlay) { // 开始播放
            itemHeadSvga.startSVGAAnim(headAnimatedImg, -1)
            itemHeadSvga.visibility = View.VISIBLE
        } else { // 停了再第一帧
            itemHeadSvga.startSVGAAnimWithListener(headAnimatedImg, -1, object :
                SVGAAnimListenerAdapter() {
                override fun onStart() {
                    super.onStart()
                    itemHeadSvga.stepToFrame(0, false)
                }
            })
        }
    }

    override fun onFragmentVisible(isResume: Boolean) {
        curViewHolder?.also {
            MDLog.i(TAG, "isResume=$isResume  ${itemModeData.bizKey}")
            if (isResume) {
                refreshHolderView(it)
            } else {
                releaseAnimation(it, true)
            }
        }
    }

    private fun releaseAnimation(it: ViewHolder, fromResume: Boolean = false) {
        kotlin.runCatching {
            if (it.itemHeadSvga.visibility == View.VISIBLE) {
                if (fromResume) {
                    it.itemHeadSvga.stepToFrame(0, false)
                } else {
                    it.itemHeadSvga.stopAnimCompletely()
                }
            }
            if (it.expandHeadSvga.visibility == View.VISIBLE) {
                if (fromResume) {
                    it.itemHeadSvga.stepToFrame(0, false)
                } else {
                    it.expandHeadSvga.stopAnimCompletely()
                }
            }
            if (it.expandBtnRightSvga.visibility == View.VISIBLE) {
                if (fromResume) {
                    it.expandBtnRightSvga.pauseAnimation()
                } else {
                    it.expandBtnRightSvga.stopAnimCompletely()
                }
            }
            if (it.expandBtnLeftSvga.visibility == View.VISIBLE) {
                if (fromResume) {
                    it.expandBtnLeftSvga.pauseAnimation()
                } else {
                    it.expandBtnLeftSvga.stopAnimCompletely()
                }
            }
            if (it.itemBgSvga.visibility == View.VISIBLE) {
                if (fromResume) {
                    it.itemBgSvga.pauseAnimation()
                } else {
                    it.itemBgSvga.stopAnimCompletely()
                }
            }
        }
    }

    override fun unbind(holder: ViewHolder) {
        super.unbind(holder)
        MDLog.i(TAG, "unbind=${itemModeData.bizKey}")
        releaseAnimation(holder)
        holder.itemView.clearAnimation()
    }

    override fun attachedToWindow(holder: ViewHolder) {
        super.attachedToWindow(holder)
        MDLog.i(TAG, "attachedToWindow=${itemModeData.bizKey}")
        runHintAnimationFilterAttach = false
        refreshHolderView(holder)
    }

    override fun detachedFromWindow(holder: ViewHolder) {
        super.detachedFromWindow(holder)
        MDLog.i(TAG, "detachedFromWindow=${itemModeData.bizKey}")
        isDetachFromWindow = true
    }

    override fun release() {
        super.release()
        MDLog.i(TAG, "release=${itemModeData.bizKey}")
        kotlin.runCatching {
            curViewHolder?.also {
                it.itemBgSvga.stopAnimCompletely()
                it.itemHeadSvga.stopAnimCompletely()
                it.expandHeadSvga.stopAnimCompletely()
                it.expandBtnRightSvga.stopAnimCompletely()
                it.expandBtnLeftSvga.stopAnimCompletely()
            }
            runningAnimators.forEach {
                it.cancel()
            }
            runningAnimators.clear()
        }
    }

    class ViewHolder(itemView: View) : CementViewHolder(itemView) {

        val cardContainer by lazy { itemView.findViewById<ConstraintLayout>(R.id.card_container) }

        val itemBgIcon by lazy { itemView.findViewById<ImageView>(R.id.item_bg_img) }
        val itemBgSvga by lazy { itemView.findViewById<MomoSVGAImageView>(R.id.item_bg_svga) }

        val expandHeadAllContainer by lazy { itemView.findViewById<ConstraintLayout>(R.id.expand_head_all_container) }
        val expandHeadContainer by lazy { itemView.findViewById<ConstraintLayout>(R.id.expand_head_container) }
        val expandHeadIconBg by lazy { itemView.findViewById<ImageView>(R.id.expand_head_icon_bg) }
        val expandHeadIcon by lazy { itemView.findViewById<ImageView>(R.id.expand_head_icon) }
        val expandHeadSvga by lazy { itemView.findViewById<MomoSVGAImageView>(R.id.expand_head_svga) }
        val normalHeadAllContainer by lazy { itemView.findViewById<ConstraintLayout>(R.id.normal_head_all_container) }
        val normalHeadContainer by lazy { itemView.findViewById<ConstraintLayout>(R.id.normal_head_container) }
        val normalHeadIconBg by lazy { itemView.findViewById<ImageView>(R.id.normal_head_icon_bg) }
        val normalHeadIcon by lazy { itemView.findViewById<ImageView>(R.id.normal_head_icon) }
        val itemHeadSvga by lazy { itemView.findViewById<MomoSVGAImageView>(R.id.normal_head_svga) }

        val normalTitleContainer by lazy { itemView.findViewById<ConstraintLayout>(R.id.normal_title_container) }
        val normalItemTitle by lazy { itemView.findViewById<TextView>(R.id.normal_item_title) }
        val normalItemSubTitle by lazy { itemView.findViewById<TextView>(R.id.normal_item_subtitle) }

        val expandTitleContainer by lazy { itemView.findViewById<ConstraintLayout>(R.id.expand_title_container) }
        val expandItemTitle by lazy { itemView.findViewById<TextView>(R.id.expand_item_title) }
        val expandItemSubTitle by lazy { itemView.findViewById<TextView>(R.id.expand_item_subtitle) }

        val btnTxtContainer by lazy { itemView.findViewById<RoundCornerFrameLayout>(R.id.btn_txt_container) }
        val btnTxt by lazy { itemView.findViewById<TextView>(R.id.btn_txt) }

        val expandBottomBtnContainer by lazy { itemView.findViewById<ConstraintLayout>(R.id.expand_btn_container) }
        val expandBtnContainerLeft by lazy { itemView.findViewById<ConstraintLayout>(R.id.expand_btn_container_left) }
        val expandBtnContainerRight by lazy { itemView.findViewById<ConstraintLayout>(R.id.expand_btn_container_right) }
        val expandBtnLeft by lazy { itemView.findViewById<ImageView>(R.id.expand_btn_left) }
        val expandBtnLeftSvga by lazy { itemView.findViewById<MomoSVGAImageView>(R.id.expand_btn_left_svga) }
        val expandBtnRight by lazy { itemView.findViewById<ImageView>(R.id.expand_btn_right) }
        val expandBtnRightSvga by lazy { itemView.findViewById<MomoSVGAImageView>(R.id.expand_btn_right_svga) }

    }
}