package com.immomo.momo.maintab.sessionlist.migrate

import com.cosmos.mdlog.MDLog
import com.immomo.framework.storage.kv.KV
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.momo.universe.config.UniverseSessionABTest
import com.immomo.momo.maintab.session2.SessionUpdateBundle.ReloadInfo
import com.immomo.momo.maintab.session2.data.manager.SessionKey.Companion.fromString
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.defs.UniverseSessionDefinition
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 小宇宙招呼数据合并
 */
class SessionUniverseMigrateHelper {

    private val TAG = "SessionUniverseMigrateHelper"

    private var migrateJob: Job? = null

    private var migratePostRefreshJob: Job? = null

    private val KEY_UNIVERSE_SESSION_CONFIG_IS_IN_EXP =
        "key_universe_session_config_is_in_exp" // 是否命中过实验

    private val UNIVERSE_KEY = UniverseSessionDefinition.UNIVERSE

    fun migrate() {
        if (migrateJob?.isActive == true || migrateJob?.isCompleted == true) return
        migrateJob = CoroutineScope(SupervisorJob() + MMDispatchers.User).launch {
            kotlin.runCatching {
                MDLog.i(TAG, "小宇宙migrate")
                if (UniverseSessionABTest.isTest()) { // 实验开启
                    KV.saveUserValue(KEY_UNIVERSE_SESSION_CONFIG_IS_IN_EXP, true)
                    // 需要查询是否有小宇宙招呼，没有小宇宙session则创建
                    val session = SessionManager.get().getSession(UNIVERSE_KEY, false).first
                    if (session == null) { // 如果不存在礼物招呼session
                        MDLog.i(TAG, "如果不存在小宇宙session")
                        SessionManager.get()
                            .syncSession(ReloadInfo(fromString(UNIVERSE_KEY)), true)
                    } else {
                        delay(1000)
                        SessionManager.get().syncSession(
                            ReloadInfo(fromString(UNIVERSE_KEY)), false
                        )
                    }
                } else { // 实验下线
                    val universeSessionIsExp =
                        KV.getUserBool(KEY_UNIVERSE_SESSION_CONFIG_IS_IN_EXP, false) // 礼物招呼是否在实验
                    MDLog.i(TAG, "universeSessionIsExp=$universeSessionIsExp")
                    if (universeSessionIsExp) { // 如果命中过实验需要恢复数据
                        SessionManager.get().deleteSession(UniverseSessionDefinition.KEY_UNIVERSE)
                    }
                    KV.saveUserValue(KEY_UNIVERSE_SESSION_CONFIG_IS_IN_EXP, false)
                }
            }
        }
    }

    fun destroy() {
        migrateJob?.cancel()
        migratePostRefreshJob?.cancel()
    }

}
