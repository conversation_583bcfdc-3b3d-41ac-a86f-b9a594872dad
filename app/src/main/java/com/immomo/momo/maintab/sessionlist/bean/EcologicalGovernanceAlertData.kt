package com.immomo.momo.maintab.sessionlist.bean

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

/**
 * 生态治理数据
 */
class EcologicalGovernanceAlertData {

    @Expose
    @SerializedName("cover")
    var cover: String? = null

    @Expose
    @SerializedName("title")
    var title: String? = null

    @Expose
    @SerializedName("desc")
    var desc: String? = null

    @Expose
    @SerializedName("button")
    var button: Button? = null

    class Button {
        @Expose
        @SerializedName("text")
        var text: String? = null

        @Expose
        @SerializedName("action")
        var action: String? = null
    }
}

