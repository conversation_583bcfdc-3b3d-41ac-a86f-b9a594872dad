package com.immomo.momo.maintab.usecase;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.immomo.android.router.momo.bean.IUser;
import com.immomo.framework.rxjava.executor.PostExecutionThread;
import com.immomo.framework.rxjava.executor.ThreadExecutor;
import com.immomo.framework.rxjava.interactor.UseCase;
import com.immomo.momo.maintab.model.SayHiListReqParam;
import com.immomo.momo.service.sessions.ISessionRepository;

import java.util.List;

import io.reactivex.Flowable;

/**
 * 带有招呼里面的用户的在线状态的usecase
 * <AUTHOR>
 */
public class LoadSayHiSessionWithStateUseCase extends UseCase<List<IUser>, SayHiListReqParam> {

    private ISessionRepository sessionRepository;


    public LoadSayHiSessionWithStateUseCase(@NonNull ThreadExecutor threadExecutor,
            ISessionRepository repository, @NonNull PostExecutionThread postExecutionThread) {
        super(threadExecutor, postExecutionThread);
        this.sessionRepository = repository;
    }

    @NonNull
    @Override
    protected Flowable<List<IUser>> buildUseCaseFlowable(@Nullable SayHiListReqParam sayHiListReqParam) {
        return sessionRepository.loadUnSpamSayHiSession(sayHiListReqParam);
    }
}
