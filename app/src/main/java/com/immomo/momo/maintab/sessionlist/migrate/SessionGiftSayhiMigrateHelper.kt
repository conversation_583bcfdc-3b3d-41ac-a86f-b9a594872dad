package com.immomo.momo.maintab.sessionlist.migrate

import com.cosmos.mdlog.MDLog
import com.immomo.framework.storage.kv.KV
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.momo.im.GiftSayHiAppConfigV1
import com.immomo.momo.maintab.session2.SessionUpdateBundle.ReloadInfo
import com.immomo.momo.maintab.session2.data.manager.SessionKey.Companion.fromString
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.defs.GiftSayHiSessionDefinition
import com.immomo.momo.maintab.session2.defs.SayHiSessionDefinition
import com.immomo.momo.service.sessions.SessionService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 礼物招呼数据合并
 */
class SessionGiftSayhiMigrateHelper {

    private val TAG = "SessionGiftSayhiMigrateHelper"

    private var migrateJob: Job? = null

    private var migratePostRefreshJob: Job? = null

    private val KEY_GIFT_SESSION_CONFIG_IS_IN_EXP = "key_gift_session_config_is_in_exp" // 是否命中过实验

    fun destroy() {
        migrateJob?.cancel()
        migratePostRefreshJob?.cancel()
    }

    fun migrate() {
        if (migrateJob?.isActive == true || migrateJob?.isCompleted == true) return
        migrateJob = CoroutineScope(SupervisorJob() + MMDispatchers.User).launch {
            kotlin.runCatching {
                MDLog.i(TAG, "migrate")
                if (GiftSayHiAppConfigV1.isOpenExp()) { // 实验开启
                    KV.saveUserValue(KEY_GIFT_SESSION_CONFIG_IS_IN_EXP, true)
                    // 需要查询是否有礼物招呼，没有礼物session则创建
                    val session =
                        SessionManager.get()
                            .getSession(GiftSayHiSessionDefinition.SAYHI, false).first
                    if (session == null) { // 如果不存在礼物招呼session
                        MDLog.i(TAG, "如果不存在礼物招呼session")
                        refreshNewGiftSayHi()
                    } else {
                        migratePostRefreshJob =
                            CoroutineScope(SupervisorJob() + MMDispatchers.User).launch {
                                delay(1000)
                                SessionManager.get().syncSession(
                                    ReloadInfo(fromString(GiftSayHiSessionDefinition.SAYHI)), false
                                )
                            }
                    }
                } else { // 实验下线
                    val giftSessionIsExp =
                        KV.getUserBool(KEY_GIFT_SESSION_CONFIG_IS_IN_EXP, false) // 礼物招呼是否在实验
                    MDLog.i(TAG, "giftSessionIsExp=$giftSessionIsExp")
                    if (giftSessionIsExp) { // 如果命中过实验需要恢复数据
                        val giftSession = SessionManager.get()
                            .getSession(GiftSayHiSessionDefinition.SAYHI, false).first
                        if (giftSession != null) {
                            MDLog.i(TAG, "giftSession=${giftSession.sessionKey}")
                            val lastMsgTime = giftSession.lastMsgTime
                            val session = SessionManager.get()
                                .getSession(SayHiSessionDefinition.SAYHI, false).first
                            if (session != null && lastMsgTime > session.lastMsgTime) {
                                MDLog.i(
                                    TAG,
                                    "lastMsgTime=${lastMsgTime}    session.lastMsgTime=${session.lastMsgTime}"
                                )
                                SessionManager.get().updateSession(
                                    fromString(SayHiSessionDefinition.SAYHI), false
                                ) {
                                    it.lastMsgTime = lastMsgTime
                                    true
                                }
                            }
                        }
                    }
                    KV.saveUserValue(KEY_GIFT_SESSION_CONFIG_IS_IN_EXP, false)
                }
                return@runCatching
            }
        }
    }

    /**
     * 创建一个新的礼物session cell
     */
    private fun refreshNewGiftSayHi() {
        val hiUserCount = SessionService.getInstance().allGiftSayHiUserNormalPartSpamCount
        if (hiUserCount > 0) { // 同时触发礼物和普通招呼的数据刷新
            SessionManager.get()
                .getSession(
                    SayHiSessionDefinition.SAYHI,
                    false
                ).first?.also { oldSession ->
                    SessionManager.get().updateSession(
                        fromString(GiftSayHiSessionDefinition.SAYHI), true
                    ) {
                        it.lastMsgId = oldSession.lastMsgId
                        it.lastMsgTime = oldSession.lastMsgTime
                        it.lastFetchTime = oldSession.lastFetchTime
                        true
                    }
                    MDLog.i(TAG, "oldSession.lastMsgTime=${oldSession.lastMsgTime}")
                }
            MDLog.i(TAG, "hiUserCount=$hiUserCount")
            migratePostRefreshJob = CoroutineScope(SupervisorJob() + MMDispatchers.User).launch {
                delay(1000)
                SessionManager.get().syncSession(
                    ReloadInfo(fromString(SayHiSessionDefinition.SAYHI)), true
                )
                SessionManager.get().syncSession(
                    ReloadInfo(fromString(GiftSayHiSessionDefinition.SAYHI)), true
                )
            }
        }
    }

}
