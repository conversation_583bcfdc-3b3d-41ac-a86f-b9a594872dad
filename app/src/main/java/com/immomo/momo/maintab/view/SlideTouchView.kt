package com.immomo.momo.maintab.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.PointF
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import com.immomo.momo.maintab.PointUtils

class SlideTouchView : View {

    var callback: Callback? = null

    private var downPoint: PointF = PointF(0f, 0f)

    var maxDis = 3

    constructor(context: Context?) : this(context, null)

    constructor(context: Context?, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : this(
        context,
        attrs,
        defStyleAttr,
        0
    )

    constructor(
        context: Context?,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int
    ) : super(context, attrs, defStyleAttr, defStyleRes)

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                downPoint.set(event.x, event.y)
            }
            MotionEvent.ACTION_MOVE -> {
                val dis = PointUtils.getDistance(event.x, event.y, downPoint)
                if (dis >= maxDis) {
                    callback?.onScrollSuccess()
                    callback = null
                }
            }
        }
        return true
    }

    interface Callback {
        fun onScrollSuccess()
    }
}
