package com.immomo.momo.maintab.session2.domain.model.type

import com.immomo.momo.maintab.model.ActiveUser
import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.SessionModel

data class ActiveUserSessionModel(
    override val baseInfo: BaseSessionInfo,
    val activeUsers: List<ActiveUserModel>
) : SessionModel

data class ActiveUserModel(
    val type: Int,
    val name: String?,
    val title: String?,
    val reason: String?,
    val tipIcon: String?,
    val avatar: String?
) {
    lateinit var origin: ActiveUser

    companion object {
        fun from(origin: ActiveUser) = ActiveUserModel(
            type = origin.type,
            name = origin.name,
            title = origin.title,
            reason = origin.reason,
            tipIcon = origin.tip_icon,
            avatar = origin.avatar
        ).also {
            it.origin = origin
        }
    }
}
