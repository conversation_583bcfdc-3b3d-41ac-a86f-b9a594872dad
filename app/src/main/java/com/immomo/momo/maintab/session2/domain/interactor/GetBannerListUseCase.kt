package com.immomo.momo.maintab.session2.domain.interactor

import com.immomo.android.mm.kobalt.domain.exception.NoParamProvided
import com.immomo.android.mm.kobalt.domain.fx.Option
import com.immomo.android.mm.kobalt.domain.interactor.UseCase
import com.immomo.momo.maintab.session2.domain.model.FoldNotificationBannerModel
import com.immomo.momo.maintab.session2.domain.model.FoldNotificationBannersModel
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.maintab.session2.domain.repository.ISessionListRepository
import com.immomo.momo.maintab.session2.domain.repository.SessionListReqParam
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow

class GetBannerListUseCase(
    dispatcher: CoroutineDispatcher,
    private val repository: ISessionListRepository
) : UseCase<FoldNotificationBannersModel, String>(dispatcher) {
    override fun build(param: Option<String>): Flow<FoldNotificationBannersModel> {
        return param.fold({ throw NoParamProvided() }) {
            repository.getBannerList()
        }
    }
}