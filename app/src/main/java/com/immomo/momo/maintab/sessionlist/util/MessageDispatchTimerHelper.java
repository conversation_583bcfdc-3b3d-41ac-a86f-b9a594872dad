package com.immomo.momo.maintab.sessionlist.util;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import com.cosmos.mdlog.MDLog;
import com.immomo.LogTag;
import com.immomo.molive.AppManager;

/**
 * Created by chenxin on 2019/5/24.
 */
public abstract class MessageDispatchTimerHelper {
    long mUpdateInterval;
    protected volatile long mLastUpdateTime;
    boolean mDebug;
    protected Handler mHandler;
    protected Handler.Callback mHandlerCallback;

    public MessageDispatchTimerHelper(long updateInterval) {
        this(updateInterval, Looper.myLooper());
    }

    public MessageDispatchTimerHelper(long updateInterval, Looper looper) {
        this.mHandlerCallback = new Handler.Callback() {
            public boolean handleMessage(Message msg) {
                MessageDispatchTimerHelper.this.mLastUpdateTime = System.currentTimeMillis();
                if (MessageDispatchTimerHelper.this.mDebug) {
                    MessageDispatchTimerHelper.this.handleUpdate();
                    MessageDispatchTimerHelper.this.mHandler.removeCallbacksAndMessages((Object)null);
                } else {
                    try {
                        MessageDispatchTimerHelper.this.handleUpdate();
                        MessageDispatchTimerHelper.this.mHandler.removeCallbacksAndMessages((Object)null);
                    } catch (Exception e) {
                        MDLog.printErrStackTrace(LogTag.COMMON, e);
                    }
                }

                return true;
            }
        };
        this.mUpdateInterval = updateInterval;
        this.mDebug = AppManager.getInstance().isDebug();
        this.mLastUpdateTime = 0L;
        this.mHandler = new Handler(looper, this.mHandlerCallback);
    }

    public void notifyTimer() {
        if (System.currentTimeMillis() - this.mLastUpdateTime < this.mUpdateInterval) {
            if (!this.mHandler.hasMessages(0)) {
                this.mHandler.sendEmptyMessageDelayed(0, this.mUpdateInterval);
            }
        } else {
            this.notifyTimerImmediately();
        }

    }

    public void notifyTimerImmediately() {
        this.mHandler.removeMessages(0);
        this.mHandler.sendEmptyMessage(0);
        this.mLastUpdateTime = System.currentTimeMillis();
    }

    public void notifyImmediatelySync() {
        this.mHandler.handleMessage(Message.obtain(this.mHandler, 0));
    }

    public void reset() {
        this.mLastUpdateTime = 0L;
        this.mHandler.removeCallbacksAndMessages((Object)null);
    }

    public abstract void handleUpdate();
}
