package com.immomo.momo.maintab.session2

import com.immomo.annotations.appconfig.AppConfigField
import com.immomo.annotations.appconfig.appconfigv2.AppConfigV2
import com.immomo.momo.maintab.session2.apt.SessionTopOperatorCloseAppConfigV2Getter

@AppConfigV2
object SessionTopOperatorCloseAppConfigV2 {

    @AppConfigField(mark = "567", key = "close_session_top_operator", defValue = "0", isSysValue = false)
    var close = 0

    @JvmStatic
    fun isCloseFunc() = SessionTopOperatorCloseAppConfigV2Getter.get().close() == 0

}

