package com.immomo.momo.maintab.session2.data.database

import android.database.Cursor
import com.immomo.momo.maintab.session2.data.database.UnreadCountEntity.Table.*
import com.immomo.momo.service.daobase.BaseDao
import com.immomo.momo.service.daobase.insertOrReplaceFields
import com.tencent.wcdb.database.SQLiteDatabase

class UnreadCountEntityDao(
    db: SQLiteDatabase
) : BaseDao<UnreadCountEntity, String>(
    db,
    TableName,
    F_Id
), UnreadCountEntity.Table {
    override fun assemble(cursor: Cursor?): UnreadCountEntity {
        return UnreadCountEntity().also { assemble(it, cursor) }
    }

    override fun assemble(entity: UnreadCountEntity?, cursor: Cursor?) {
        if (entity == null || cursor == null) return

        entity.id = cursor.getString(cursor.getColumnIndex(F_Id))
        entity.sessionKey = cursor.getString(cursor.getColumnIndex(F_SessionKey))
        entity.type = cursor.getInt(cursor.getColumnIndex(F_Type))
        entity.sessionType = cursor.getString(cursor.getColumnIndex(F_SessionType))
        entity.sessionId = cursor.getString(cursor.getColumnIndex(F_SessionId))
        entity.count = cursor.getInt(cursor.getColumnIndex(F_Count))
        entity.maxMessageTimestamp = cursor.getLong(cursor.getColumnIndex(F_MaxMessageTimestamp))
    }

    private fun buildParams(entity: UnreadCountEntity): Map<String, Any?> {
        val map = mutableMapOf<String, Any?>()
        map[F_Id] = entity.id
        map[F_SessionKey] = entity.sessionKey
        map[F_Type] = entity.type
        map[F_SessionType] = entity.sessionType
        map[F_SessionId] = entity.sessionId
        map[F_Count] = entity.count
        map[F_MaxMessageTimestamp] = entity.maxMessageTimestamp
        return map
    }

    override fun insert(entity: UnreadCountEntity?) {
        if (entity == null) return
        insertFields(buildParams(entity))
    }

    fun insertOrReplace(entity: UnreadCountEntity?) {
        if (entity == null) return
        insertOrReplaceFields(buildParams(entity))
    }

    override fun update(entity: UnreadCountEntity?) {
        if (entity == null) return

        updateFields(buildParams(entity), arrayOf(F_Id), arrayOf(entity.id))
    }

    override fun deleteInstence(entity: UnreadCountEntity?) {
        if (entity == null) return

        delete(entity.id)
    }

    companion object {
        fun createTable(db: SQLiteDatabase) {
            val sql = ("CREATE TABLE IF NOT EXISTS $TableName (" +
                    "$F_Id TEXT PRIMARY KEY NOT NULL UNIQUE, " +
                    "$F_SessionKey TEXT NOT NULL, " +
                    "$F_Type INTEGER NOT NULL, " +
                    "$F_SessionType TEXT, " +
                    "$F_SessionId TEXT, " +
                    "$F_Count INTEGER NOT NULL, " +
                    "$F_MaxMessageTimestamp INTEGER NOT NULL" +
                    ")") // 任务类型
            val indexSql =
                "CREATE INDEX IF NOT EXISTS IDX_${TableName}_${F_SessionType}_${F_SessionId} ON \"$TableName\"" +
                        " (\"$F_SessionType\" ASC,\"$F_SessionId\" ASC)"
            try {
                db.execSQL(sql)
                db.execSQL(indexSql)
            } catch (ignored: Exception) {
            }
        }

        fun dropTable(db: SQLiteDatabase) {
            try {
                db.execSQL("DROP TABLE IF EXISTS $TableName")
            } catch (ignored: Exception) {
            }
        }
    }
}