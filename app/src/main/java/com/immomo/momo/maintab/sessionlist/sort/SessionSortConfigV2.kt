package com.immomo.momo.maintab.sessionlist.sort

import com.immomo.annotations.appconfig.AppConfigField
import com.immomo.annotations.appconfig.appconfigv2.AppConfigV2

/**
 * 推荐session相关配置
 */
@AppConfigV2
class SessionSortConfigV2 {
    @AppConfigField(mark = "318", key = "online_label_text", defValue = "在线")
    var onlineLabelText = ""

    @AppConfigField(mark = "318", key = "online_just_now_label_text", defValue = "刚刚在线")
    var onlineJustNowText = ""

    @AppConfigField(mark = "318", key = "new_relation_text_1", defValue = "认识了?天")
    var newRelationText1 = ""

    @AppConfigField(mark = "318", key = "new_relation_text_2", defValue = "?天没聊了")
    var newRelationText2 = ""

    @AppConfigField(mark = "318", key = "num_session_upload", defValue = "80")
    var numSessionUpload = 80

    /**
     * 防止session曝光量过大，加开关控制
     */
    @AppConfigField(mark = "318", key = "session_p2p_expose_open", defValue = "1")
    var p2pExposureLogOpen = 1

    /**
     *  推荐session单日请求次数上限
     */
    @AppConfigField(mark = "318", key = "recommend_request_total_time", defValue = "5")
    var requestTotalTimes = 5

    /**
     * 推荐session请求时间间隔
     */
    @AppConfigField(mark = "318", key = "recommend_request_interval", defValue = "30")
    var requestInterval = 30

}