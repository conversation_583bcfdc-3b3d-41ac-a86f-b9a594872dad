package com.immomo.momo.maintab.task;

import com.cosmos.mdlog.MDLog;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.mmstatistics.event.ExposureEvent;
import com.immomo.mmstatistics.event.TaskEvent;
import com.immomo.mmutil.StringUtils;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoKit;
import com.immomo.momo.guest.gotohelper.AppEnterStackHelper;
import com.immomo.momo.innergoto.helper.ActivityHandler;
import com.immomo.momo.protocol.http.UserApi;
import com.immomo.momo.statistics.EVAction;
import com.immomo.momo.statistics.EVPage;

/**
 * 进入首页，-1屏的网络请求
 * Created by zhao.ya<PERSON><PERSON> on 2019/6/6.
 */
public class HiBoardTask extends MomoTaskExecutor.Task<Object, Object, HiBoardInfo> {

    public HiBoardTask() {
    }

    @Override
    protected HiBoardInfo executeTask(Object... objects) throws Exception {
        logRequest();
        return UserApi.getInstance().getHiBoardInfo();
    }

    @Override
    protected void onTaskSuccess(HiBoardInfo hiBoardInfo) {
        super.onTaskSuccess(hiBoardInfo);
        logIssue();
        KV.saveUserValue(SPKeys.HiBoard.KEY_LAST_HI_BOARD_SHOW_TIME, System.currentTimeMillis());
        if (hiBoardInfo == null || StringUtils.isEmpty(hiBoardInfo.getContent())) {
            logError();
            AppEnterStackHelper.get().handleNext();
            return;
        }
        if (!AppEnterStackHelper.get().needDelayGoto(hiBoardInfo.getContent())) {
            ActivityHandler.executeAction(hiBoardInfo.getContent(), MomoKit.getContext());
        }
        logPopup();
    }

    @Override
    protected void onTaskError(Exception e) {
        MDLog.printErrStackTrace(LogTag.COMMON, e);
        logError();
        AppEnterStackHelper.get().handleNext();
        KV.saveUserValue(SPKeys.HiBoard.KEY_LAST_HI_BOARD_SHOW_TIME, System.currentTimeMillis());
    }

    /**
     * 负1屏内容弹出量
     */
    private void logPopup() {
        ExposureEvent.create(ExposureEvent.Type.Normal)
                .page(EVPage.Other.HomePage)
                .action(EVAction.Window.FirstChannelAll)
                .submit();
    }

    /**
     * 负1屏内容未下发错误的量
     */
    private void logError() {
        TaskEvent.create()
                .page(EVPage.Other.HomePage)
                .action(EVAction.Window.FirstChannelIssuedError)
                .type("windowsendfail")
                .status(TaskEvent.Status.Fail)
                .submit();
    }

    /**
     * 负1屏内容下发量
     */
    private void logIssue() {
        TaskEvent.create()
                .page(EVPage.Other.HomePage)
                .action(EVAction.Window.FirstChannelIssued)
                .type("windowsend")
                .status(TaskEvent.Status.Success)
                .submit();
    }

    /**
     * 负1屏客户端请求量
     */
    private void logRequest() {
        TaskEvent.create()
                .page(EVPage.Other.HomePage)
                .action(EVAction.Window.FirstChannelRequest)
                .type("windowrequest")
                .status(TaskEvent.Status.Success)
                .submit();
    }

}