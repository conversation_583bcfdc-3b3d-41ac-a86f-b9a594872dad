package com.immomo.momo.maintab.session2.data.database

import com.cosmos.mdlog.MDLog
import com.immomo.momo.maintab.session2.SessionDefinitionManager
import com.immomo.momo.util.FormatUtils
import com.squareup.moshi.JsonClass

val SessionEntity.content: SessionContent?
    get() {
        if (contentObj != null) return contentObj as? SessionContent

        contentObj = SessionDefinitionManager.deserialize(sessionType, contentStr)
        return contentObj as? SessionContent
    }

fun SessionEntity.isInCache(mark: Int) = (cacheStatus and mark) == mark

fun SessionEntity.checkInCache(mark: Int, skipCheck: Boolean = false, ifNot: () -> Boolean) {
    if (skipCheck || (cacheStatus and mark) != mark) {
        if (ifNot()) {
            cacheStatus = cacheStatus or mark
        }
    }
}

fun SessionEntity.setInCache(mark: Int) {
    cacheStatus = cacheStatus or mark
}

fun SessionEntity.removeInCache(mark: Int) {
    cacheStatus = cacheStatus xor mark
}

abstract class SessionContent(val type: String) {
    var forceRefreshId: Int = 0

    open fun idx1(): String? = null
    open fun idx2(): String? = null
    open fun idx3(): String? = null
    open fun idx4(): String? = null
    open fun idx5(): String? = null

    fun forceRefresh() {
        forceRefreshId++
    }
}

@JsonClass(generateAdapter = true)
open class ChatContent(type: String) : SessionContent(type) {
    var chatId: String? = null
    var pendingReloadChatInfo: Boolean = false
    open fun isChatInfoValid(): Boolean = true

    var forcedDesc: String? = null

    var onlyShowMessageContent: Boolean = false
    var distanceInfo: Float = -1F
    var lastMessageOwnerId: String? = null
    var lastMessageOwnerName: String? = null
    var lastMessageContent: String? = null

    open val desc: String?
        get() {
            if (forcedDesc != null) return forcedDesc

            val sb = StringBuilder()
            if (!onlyShowMessageContent) {
                if (distanceInfo >= 0) {
                    sb.append(FormatUtils.formatFloat(distanceInfo / 1000F) + "km·")
                }
                if (lastMessageOwnerName != null) {
                    sb.append(lastMessageOwnerName)
                }
            }
            if (lastMessageContent != null) {
                sb.append(lastMessageContent)
            }
            return sb.toString()
        }

    var draftString: String? = null
    var draftQuoteString: String? = null

    var lastMessageType: Int? = null
    var showMessageStatus: Boolean = false
    var lastMessageStatus: Int? = null

    override fun idx1(): String? = chatId
}