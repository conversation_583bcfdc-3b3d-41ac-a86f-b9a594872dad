package com.immomo.momo.maintab.session2.presentation.itemmodel.events

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.view.View
import androidx.fragment.app.Fragment
import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.framework.base.BaseActivity
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.storage.preference.SPKeys
import com.immomo.mmstatistics.event.ClickEvent
import com.immomo.mmutil.toast.Toaster
import com.immomo.momo.MomoKit
import com.immomo.momo.R
import com.immomo.momo.android.view.dialog.MAlertDialog
import com.immomo.momo.common.utils.CheckDoubleClick
import com.immomo.momo.gotologic.GotoDispatcher
import com.immomo.momo.impaas.common.ext.interceptClick
import com.immomo.momo.maintab.model.SayHiListReqParam
import com.immomo.momo.maintab.session2.SessionUpdateBundle
import com.immomo.momo.maintab.session2.apt.SessionAppConfigV2Getter
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.maintab.session2.domain.model.type.DebuggerSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.DiscussChatSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.FriendNoticeSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.GameBoxSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.GiftSayHiSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.GotoSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.GroupChatSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.OfficialSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.SayHiSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.UniverseFoldSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.UserChatSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.VChatSuperRoomSessionModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.SessionItemModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.SessionOnClickListener
import com.immomo.momo.maintab.sessionlist.util.SessionHelper
import com.immomo.momo.message.ChatHelper
import com.immomo.momo.message.NewSayUIConfigV1
import com.immomo.momo.message.activity.ChatActivity
import com.immomo.momo.message.activity.GameBoxFolderListActivity
import com.immomo.momo.message.activity.GroupChatActivity
import com.immomo.momo.message.activity.InnerGiftHiSessionListActivity
import com.immomo.momo.message.activity.MultiChatActivity
import com.immomo.momo.message.activity.OfficialFolderListActivity
import com.immomo.momo.message.bean.ChatBusinessType
import com.immomo.momo.message.sayhi.activity.HiCardStackActivity
import com.immomo.momo.message.sayhi.activity.reflect.NewSayHiSessionListActivity
import com.immomo.momo.service.bean.SayhiSession
import com.immomo.momo.service.bean.Session
import com.immomo.momo.statistics.EVAction
import com.immomo.momo.statistics.EVPage
import com.immomo.momo.universe.notifacation.presentation.UniSessionListActivity
import com.immomo.momo.voicechat.util.IntentUtils

internal val SessionItemModel.SessionViewHolder.activity: Activity?
    get() {
        return when (val context = itemView.context) {
            is Fragment -> context.activity
            is Activity -> context
            else -> null
        }
    }
internal fun SessionItemModel.SessionViewHolder.logClick(
    session: SessionModel,
    whichItem: String,
    remoteid: String
) {
    val preContent = getPreContent()
    val onlineText = getOnlineText()
    val timeText = getTimeStr()
    val isRecommend: Boolean = session.baseInfo.recommendTime > 0

    val sessionLogParams = SessionHelper.SessionLogParams(
        whichItem = whichItem,
        newsNumber = session.baseInfo.unreadMessageCount,
        position = adapterPosition,
        remoteid = remoteid,
        isReddot = session.baseInfo.silentMessageCount > 0,
        messageText = if (session is GotoSessionModel) session.text else null,
        hiUserCount = if (session is SayHiSessionModel) session.hiUserTotalCount else if (session is GiftSayHiSessionModel) session.hiUserTotalCount else 0,
        hiUserCountUsable = if (session is SayHiSessionModel) session.hiUserTotalCountUsable else if (session is GiftSayHiSessionModel) session.hiUserTotalCountUsable else 0,
        preContent = preContent,
        onlineText = onlineText ?: "",
        timeStr = timeText,
        isRecommend = isRecommend,
        avatar = "1",
        ifActivity = if(session is GotoSessionModel) session.hasRedPacketTxt else false
    )
    if (session is SayHiSessionModel && NewSayUIConfigV1.isUserNewUI()) {
        sessionLogParams.text = (if (session.hasGiftTag) "有礼物招呼·" else "") + session.desc
    } else if (session is UniverseFoldSessionModel) { // 小宇宙折叠消息
        sessionLogParams.messageText = session.desc
    }
    sessionLogParams.hasRedPacket =
        if (session is SayHiSessionModel) session.hasRedPacketTxt else false
    SessionHelper.Log.logSessionClick(sessionLogParams)
}

open class DefaultSessionOnClickListener : SessionOnClickListener {
    private val TAG: String = "DefaultSessionOnClickListener"
    override fun onClicked(
        view: View,
        viewHolder: SessionItemModel.SessionViewHolder,
        session: SessionModel,
        adapterPosition: Int
    ) {
        when (session) {
            is UserChatSessionModel -> {
                handleUserChatSessionClick(view, viewHolder, session)
            }
            is GroupChatSessionModel -> {
                viewHolder.logClick(session, "group", session.sessionId)
                SessionManager.get().syncSession(
                    SessionUpdateBundle.ClearUnread(session.sessionKey)
                )

                val groupChatIntent = Intent(view.context, GroupChatActivity::class.java)
                groupChatIntent.putExtra(GroupChatActivity.REMOTE_GROUP_ID, session.sessionId)
                if (session.isGift) {
                    groupChatIntent.putExtra(GroupChatActivity.KEY_SCROLL_TYPE, 2)
                } else if (session.isAtMe) {
                    groupChatIntent.putExtra(GroupChatActivity.KEY_SCROLL_TYPE, 1)
                }
                view.context.startActivity(groupChatIntent)
            }
            is DiscussChatSessionModel -> {
                viewHolder.logClick(session, "discussion", session.sessionId)
                SessionManager.get().syncSession(
                    SessionUpdateBundle.ClearUnread(session.sessionKey)
                )

                val discussIntent = Intent(view.context, MultiChatActivity::class.java)
                discussIntent.putExtra(MultiChatActivity.REMOTE_DISCUSS_ID, session.sessionId)
                view.context.startActivity(discussIntent)
            }
            is VChatSuperRoomSessionModel -> {
                val onJumpToVChat = {
                    SessionManager.get()
                        .syncSession(SessionUpdateBundle.ClearUnread(session.sessionKey))

                    IntentUtils.startActivity(view.context, session.sessionId, "session")
                }
                if (KV.getUserBool(
                        SPKeys.User.VoiceChat.KEY_VCHAT_ENTER_FROM_SESSIONS_TIPS, false
                    )
                ) {
                    onJumpToVChat()
                } else {
                    MAlertDialog.makeSingleButtonDialog(
                        viewHolder.activity,
                        R.string.vchat_super_room_enter_form_sessions_tips_content,
                        R.string.vchat_super_room_enter_form_sessions_tips_conform
                    ) { _, _ ->
                        KV.saveUserValue(
                            SPKeys.User.VoiceChat.KEY_VCHAT_ENTER_FROM_SESSIONS_TIPS,
                            true
                        )
                        onJumpToVChat()
                    }.also { dialog ->
                        dialog.setSupportDark(true)
                        viewHolder.activity.castOrNull<BaseActivity>()?.showDialog(dialog)
                            ?: dialog.show()
                    }
                }
            }
            is GotoSessionModel -> {
                if (CheckDoubleClick.isFastDoubleClick()) {
                    return
                }
                viewHolder.logClick(session, "box", session.businessId)
                if (!TextUtils.isEmpty(session.action)) {
                    GotoDispatcher.action(session.action, view.context).execute()
                }
                if (session.baseInfo.unreadMessageCount > 0 || session.baseInfo.silentMessageCount > 0) {
                    SessionManager.get().syncSession(
                        SessionUpdateBundle.ClearUnread(session.sessionKey)
                    )
                    MomoKit.getApp().removeGotoSessionNotify()
                }
            }
            is OfficialSessionModel -> {
                viewHolder.logClick(session, "subscribe", "0")

                SessionManager.get().syncSession(
                    SessionUpdateBundle.ClearUnread(session.sessionKey)
                )
                view.context.startActivity(
                    Intent(view.context, OfficialFolderListActivity::class.java)
                )
            }
            is GameBoxSessionModel -> { // 游戏互动通知
                viewHolder.logClick(session, "game_interact", "0")

                SessionManager.get().syncSession(
                    SessionUpdateBundle.ClearUnread(session.sessionKey)
                )
                view.context.startActivity(
                    Intent(view.context, GameBoxFolderListActivity::class.java)
                )
            }
            is SayHiSessionModel -> {
                viewHolder.logClick(session, "sayhi", "0")
                SessionManager.get().syncSession(
                    SessionUpdateBundle.ClearUnread(session.sessionKey)
                )

                if (NewSayUIConfigV1.isUserNewUI()) {
                    view.context.startActivity(Intent(view.context, NewSayHiSessionListActivity::class.java))
                } else {
                    view.context.startActivity(
                        Intent(
                            view.context,
                            HiCardStackActivity::class.java
                        )
                    )
                }
            }
            is GiftSayHiSessionModel -> {
                viewHolder.logClick(session, "gift_sayhi", "0")
                SessionManager.get().syncSession(
                    SessionUpdateBundle.ClearUnread(session.sessionKey)
                )
                InnerGiftHiSessionListActivity.start(
                    view.context, SayhiSession.FROM_TYPE_GIFT, SayHiListReqParam.FROM_SESSION_GIFT_HI
                )
            }
            is FriendNoticeSessionModel -> {
                viewHolder.logClick(session, "remind", "0")
                Toaster.show("此业务已下线")
            }
            is UniverseFoldSessionModel -> { // 小宇宙点击
                viewHolder.logClick(session, "microcosm", "0")
                UniSessionListActivity.start(view.context)
            }
            is DebuggerSessionModel -> {
                val loggerIntent = Intent(view.context, ChatActivity::class.java)
                loggerIntent.putExtra(ChatActivity.REMOTE_USER_ID, session.baseInfo.sessionId)
                view.context.startActivity(loggerIntent)
            }
        }
    }

    private fun handleUserChatSessionClick(view: View,
                                           viewHolder: SessionItemModel.SessionViewHolder,
                                           session: UserChatSessionModel) {
        viewHolder.logClick(session, "personal", session.sessionId)
        SessionManager.get().syncSession(
            SessionUpdateBundle.ClearUnread(session.sessionKey)
        )

        handleJump(session, view.context)

        if (viewHolder.specialTextView.text.isNotEmpty() && session.lastType28Prompt == viewHolder.specialTextView.text) {
            if (session.lastType28Prompt == SessionAppConfigV2Getter.get().keyTodayFate()) {
                ClickEvent.create()
                    .page(EVPage.Growth.FateToday)
                    .action(EVAction.List.FateTodayClick)
                    .putExtra("remote_id", session.sessionId)
                    .putExtra("appid", session.type28AppId)
                    .submit()
            }
        }
    }

    open fun handleJump(
        session: UserChatSessionModel,
        context: Context
    ) {
        if (session.interceptClick(context)) {
            return
        }
        val intent = Intent()
        intent.setClass(context, ChatActivity::class.java)

        intent.putExtra(ChatActivity.REMOTE_USER_ID, session.sessionId)
        intent.putExtra(ChatHelper.KEY_NEED_PULL_FEED_UPDATE, !session.isDianDianCard)
        intent.putExtra(
            ChatHelper.KEY_BUSINESS_TYPE,
            if (session.sessionBusinessType == Session.BUSINESS_KLIAO_MATCH) ChatBusinessType.CHAT_BUSINESS_KLIAO_MATCH else ""
        )
        context.startActivity(intent)
    }
}