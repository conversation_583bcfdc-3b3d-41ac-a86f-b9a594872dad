package com.immomo.momo.maintab.sessionlist;

import static com.immomo.momo.android.view.dialog.MSubTitleListDialog.toPairList;
import static com.immomo.momo.maintab.model.ActiveUser.TYPE_NOT_DISMISS;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.os.HandlerThread;
import android.text.TextUtils;
import android.util.Pair;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.UiThread;

import com.cosmos.mdlog.MDLog;
import com.google.gson.reflect.TypeToken;
import com.immomo.android.router.momo.business.vchat.VChatMiscRouter;
import com.immomo.framework.imageloader.ImageLoaderUtil;
import com.immomo.framework.imageloader.ImageType;
import com.immomo.framework.rxjava.executor.impl.ExecutorFactory;
import com.immomo.framework.rxjava.interactor.CommonSubscriber;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.mls.InitData;
import com.immomo.mls.MLSBundleUtils;
import com.immomo.mmstatistics.event.ClickEvent;
import com.immomo.mmstatistics.event.ExposureEvent;
import com.immomo.mmutil.NetUtils;
import com.immomo.mmutil.app.AppContext;
import com.immomo.mmutil.log.Log4Android;
import com.immomo.mmutil.task.MMThreadExecutors;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.task.ThreadUtils;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.molive.foundation.eventcenter.event.MomoImEvent;
import com.immomo.molive.foundation.eventcenter.eventdispatcher.NotifyDispatcher;
import com.immomo.momo.IMConfigs;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.android.synctask.Callback;
import com.immomo.momo.android.view.dialog.MAlertDialog;
import com.immomo.momo.android.view.dialog.MSubTitleListDialog;
import com.immomo.momo.businessmodel.usermodel.IUserModel;
import com.immomo.momo.common.AppKit;
import com.immomo.momo.common.ClickUtils;
import com.immomo.momo.eventbus.DataEvent;
import com.immomo.momo.eventbus.EventKeys;
import com.immomo.momo.exception.MomoServerException;
import com.immomo.momo.globalevent.GlobalEventManager;
import com.immomo.momo.greet.GreetHelper;
import com.immomo.momo.group.bean.Group;
import com.immomo.momo.home.manager.FrameConfigManager;
import com.immomo.momo.home.manager.HomeUnreadManager;
import com.immomo.momo.im.GiftSayHiAppConfigV1;
import com.immomo.momo.innergoto.helper.ActivityHandler;
import com.immomo.momo.luaview.LuaViewActivity;
import com.immomo.momo.maingroup.IBaseForBusinessView;
import com.immomo.momo.maingroup.IHomeBaseProvider;
import com.immomo.momo.maingroup.manager.FrameConfigConst;
import com.immomo.momo.maintab.model.AbsSession;
import com.immomo.momo.maintab.model.ActiveUser;
import com.immomo.momo.maintab.model.IActiveUserRepository;
import com.immomo.momo.maintab.model.SessionActiveUser;
import com.immomo.momo.maintab.model.SessionOnlineBean;
import com.immomo.momo.maintab.model.UserOnlineStatus;
import com.immomo.momo.maintab.session2.SessionUpdateBundle;
import com.immomo.momo.maintab.session2.data.database.SessionContent;
import com.immomo.momo.maintab.session2.data.database.SessionContentKt;
import com.immomo.momo.maintab.session2.data.manager.SessionKey;
import com.immomo.momo.maintab.session2.data.manager.SessionManager;
import com.immomo.momo.maintab.session2.defs.FoldSessionDefinition;
import com.immomo.momo.maintab.session2.defs.GiftSayHiSessionDefinition;
import com.immomo.momo.maintab.session2.defs.SayHiSessionDefinition;
import com.immomo.momo.maintab.session2.defs.UserChatSessionDefinition;
import com.immomo.momo.maintab.session2.domain.model.SessionModel;
import com.immomo.momo.maintab.session2.domain.model.type.GotoSessionModel;
import com.immomo.momo.maintab.session2.domain.model.type.GroupChatSessionModel;
import com.immomo.momo.maintab.session2.domain.model.type.HePaiSessionModel;
import com.immomo.momo.maintab.session2.domain.model.type.NewBoySessionModel;
import com.immomo.momo.maintab.session2.domain.model.type.UserChatSessionModel;
import com.immomo.momo.maintab.session2.presentation.itemmodel.SessionItemModel;
import com.immomo.momo.maintab.session2.presentation.itemmodel.events.DefaultSessionOnLongClickListenerKt;
import com.immomo.momo.maintab.session2.presentation.viewmodel.SessionListInnerViewModel;
import com.immomo.momo.maintab.sessionlist.bean.MaskChatQuitData;
import com.immomo.momo.maintab.sessionlist.bean.SessionCountBean;
import com.immomo.momo.maintab.sessionlist.bean.SessionSpaceBean;
import com.immomo.momo.maintab.sessionlist.bean.UnreadData;
import com.immomo.momo.maintab.sessionlist.enterbar.usecase.SessionEnterBarResponse;
import com.immomo.momo.maintab.sessionlist.enterbar.usecase.SessionEnterBarUseCase;
import com.immomo.momo.maintab.sessionlist.migrate.HePaiSessionHelper;
import com.immomo.momo.maintab.sessionlist.migrate.SessionFoldMigrateHelper;
import com.immomo.momo.maintab.sessionlist.migrate.SessionGiftSayhiMigrateHelper;
import com.immomo.momo.maintab.sessionlist.migrate.SessionUniverseMigrateHelper;
import com.immomo.momo.maintab.sessionlist.sort.SortCons;
import com.immomo.momo.maintab.sessionlist.sort.SortManager;
import com.immomo.momo.maintab.sessionlist.sort.usecase.SessionRecommendUseCase;
import com.immomo.momo.maintab.sessionlist.space.apt.SessionSpaceConfigV2Getter;
import com.immomo.momo.maintab.sessionlist.task.RefreshOnlineStatusTask;
import com.immomo.momo.maintab.sessionlist.util.ActiveUserDataInstance;
import com.immomo.momo.maintab.sessionlist.util.LiveTimerHelper;
import com.immomo.momo.maintab.sessionlist.util.SessionHelper;
import com.immomo.momo.maintab.sessionlist.util.SessionUserHelper;
import com.immomo.momo.maintab.usecase.ChangeGreetRemindTask;
import com.immomo.momo.message.ChatHelper;
import com.immomo.momo.message.activity.ChatActivity;
import com.immomo.momo.message.bean.ChatBusinessType;
import com.immomo.momo.message.task.UnreadUploadRunnable;
import com.immomo.momo.messages.service.DiscussMsgService;
import com.immomo.momo.messages.service.FlashChatService;
import com.immomo.momo.messages.service.GroupMsgService;
import com.immomo.momo.messages.service.MaskChatService;
import com.immomo.momo.messages.service.SingleMsgService;
import com.immomo.momo.messages.service.TextChatService;
import com.immomo.momo.mk.util.BusinessNotifySwitchUtils;
import com.immomo.momo.mulog.pair.MUPairItem;
import com.immomo.momo.mvp.common.model.ModelManager;
import com.immomo.momo.mvp.contacts.model.FriendsModel;
import com.immomo.momo.mvp.maintab.mainimpl.appconfig.apt.SessionFoldTestConfigV2Getter;
import com.immomo.momo.protocol.http.InteractionNoticeApi;
import com.immomo.momo.protocol.http.SessionApi;
import com.immomo.momo.protocol.http.UserApi;
import com.immomo.momo.protocol.imjson.dispatch.MsgLogUtil;
import com.immomo.momo.protocol.imjson.event.IMEventReporter;
import com.immomo.momo.protocol.imjson.event.IMOfflineEvent;
import com.immomo.momo.protocol.imjson.handler.InteractionNoticeHandler;
import com.immomo.momo.protocol.imjson.receiver.MessageKeys;
import com.immomo.momo.service.bean.FolderType;
import com.immomo.momo.service.bean.Message;
import com.immomo.momo.service.bean.Session;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.service.sessions.ISessionRepository;
import com.immomo.momo.service.sessions.MessageServiceHelper;
import com.immomo.momo.service.sessions.SessionService;
import com.immomo.momo.service.sessions.SessionUserCache;
import com.immomo.momo.service.user.UserService;
import com.immomo.momo.service.vchatroom.VChatSuperRoomService;
import com.immomo.momo.sessionnotice.bean.NoticeMsg;
import com.immomo.momo.sessionnotice.bean.PushSwitchTipsInfo;
import com.immomo.momo.statistics.EVAction;
import com.immomo.momo.statistics.EVPage;
import com.immomo.momo.textchat.activity.TextChatSessionListActivity;
import com.immomo.momo.textchat.util.TChatConst;
import com.immomo.momo.universe.apt.UniAppConfigV1Getter;
import com.immomo.momo.universe.im.UniUnreadManager;
import com.immomo.momo.universe.im.cons.SessionCons;
import com.immomo.momo.util.GsonUtils;
import com.immomo.momo.util.RXUtilsKt;
import com.immomo.momo.util.StringUtils;
import com.immomo.momo.videochat.friendvideo.friend.FriendQChatWorker;
import com.immomo.momo.voicechat.model.VChatAction;
import com.immomo.momo.voicechat.model.superroom.VChatSuperRoom;
import com.immomo.momo.voicechat.model.superroom.VChatSuperRoomStatus;
import com.immomo.momo.voicechat.util.VChatSuperRoomSessionCache;

import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.TimeUnit;

import de.greenrobot.event.EventBus;
import info.xudshen.android.appasm.AppAsm;
import io.reactivex.Flowable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import io.reactivex.subscribers.DisposableSubscriber;

/**
 * Created by tanjie on 5/7/16.
 */
public class SessionListPresenter implements ISessionListPresenter, GlobalEventManager.Subscriber {
    // gotoTabIndex：1 or 2 ，默认0  跳到lua页面第几个tab，默认第一个tab
    public static void toInteractActivity(Context context, int gotoTabIndex) {
        String luaUrl = interactionLuaUrl();
        String realUrl = luaUrl + "&page_tab_index=" + (gotoTabIndex <= 0 ? 1 : gotoTabIndex);
        InitData initData = MLSBundleUtils.createInitData(realUrl);
        Intent luaIntent = new Intent(context, LuaViewActivity.class);
        luaIntent.putExtras(MLSBundleUtils.createBundle(initData));
        context.startActivity(luaIntent);
    }

    public final static String STR_DELETE = "删除对话";
    public final static String STR_MASK_DELETE = "删除匿名闪聊";
    public final static String STR_DELETE_NOTICE = "删除通知";
    public final static String STR_DELETE_FRIEND_NOTICE = "删除提醒";
    public final static String STR_PEEK = "悄悄查看";
    public final static String STR_RECEIVE_MSG_CLOSE = "不再接收";
    public final static String STR_DELETE_SPAM = "删除";
    public final static String STR_RECEIVE_MSG_OPEN = "接收消息";
    public final static String STR_GREET_NOT_REMINDER_OFF = "开启提醒";
    public final static String STR_GREET_NOT_REMINDER_ON = "今日不再提醒";
    private static final String KEY_RELOAD_SESSIONS = "needReloadSessions";

    private final int InitTaskTag = hashCode() + 1;
    private final int LoadNoticeTaskTag = SessionListPresenter.class.hashCode() + 2;
    private final int OtherTaskTag = hashCode() + 3;
    private final int refreshUserStatusTag = hashCode() + 4;
    private final int getPushSwitchTipsInfoTag = hashCode() + 5;
    private final int getPushSwitchDialogInfoTag = hashCode() + 6;
    private final int setGotoSessionSwitchTaskTag = hashCode() + 7;
    private final int tagGreetReminder = hashCode() + 10;
    private final int spaceIntervalUnit = 60 * 60 * 1000;
    private final int maskChatQuitTag = hashCode() + 13;

    private WeakReference<ISessionListView2> mIView;
    @Nullable
    private SessionListInnerViewModel sessionListVm;

    private Log4Android log = Log4Android.getInstance();
    private int unreadMessageCount; //消息页面的未读红点（未读消息+小宇宙+小纸条+...）
    private IUserModel mUserModel;
    private boolean hasMoreSession = true;
    private NoticeMsg noticeMsg;
    private int unReadNoticeCount;
    private IActiveUserRepository activeUserRepository;
    @NonNull
    private final SessionManager sessionManager;

    private FriendsModel mFriendsModel;

    private boolean isFirstStart = true;

    /////////////////////////////处理用户在线状态////////////////////////
    //List间隔多久刷新一次
    private static int intervalTimeList = 5 * 1000;
    //user/group间隔多久之间会添加到list
    private int intervalTimeUserAndGroup = 180 * 1000;
    //最大容量,超过这个容量直接请求，不用考虑间隔时间
    private static final int LAST_REFRESH_SIZE = 10;
    //最后一次刷新胶囊时间
    private long lastMillionFloatRefresh = 0L;
    // 刷新胶囊间隔，默认 15 分钟
    private long refreshCapsuleInterval = 900;

    //最后一次添加当前界面session到请求列表的时间
    private long lastAddUserInViewTime = 0L;
    //需要被刷新的session列表
    private CopyOnWriteArraySet<String> needRefreshOnlinTagSet = new CopyOnWriteArraySet<>();
    //刷新任务
    private MomoTaskExecutor.Task refreshOnlineStatusTask;
    private Disposable disposable;

    //将消息发送给直播时，做时间限制，一秒一次。
    private LiveTimerHelper liveTimerHelper;
    private HandlerThread mLiveHandlerThread;
    private boolean nextTimeRequestOnlineStatue;
    public final String BusinessNotifySwitchTag = "SessionList" + hashCode();
    private final SessionEnterBarUseCase mSessionEnterBarUseCase;
    private SessionRecommendUseCase mSessionRecommendUseCase;
    // 如果是从RecentOnlineActivity返回且删除过用户，则需要刷新一下
    private boolean isNeedRefreshActiveUser = false;
    private Disposable mContactCountDisposable;
    private Disposable mNewBodyAndHePaiDisposable;
    private Disposable mTextChatChangeDisposable;

    //打出的招呼实验
    private boolean isFoldHiTest = SessionFoldTestConfigV2Getter.get().msgFoldAll() == 1;
    private UnReplySessionHelper migrateUnReplyHelper;
    private SpamSessionHelper spamSessionHelper;
    private NewBoySessionHelper newBoySessionHelper;
    private HePaiSessionHelper hePaiSessionHelper;
    private SessionFoldMigrateHelper sessionFoldMigrateHelper;
    private SessionGiftSayhiMigrateHelper sessionGiftSayhiMigrateHelper;
    private SessionUniverseMigrateHelper sessionUniverseMigrateHelper; // 小宇宙合并数据
    private boolean searchMenuShow;
    private boolean isPageInResumeState = false; // 页面是否处于resume状态

    public SessionListPresenter(@NonNull SessionManager sessionManager, ISessionListView2 iSessionListView) {
        mIView = new WeakReference<>(iSessionListView);
        mUserModel = ModelManager.getInstance().getModel(IUserModel.class);
        this.sessionManager = sessionManager;
        mFriendsModel = new FriendsModel();
        activeUserRepository = ModelManager.getInstance().getModel(IActiveUserRepository.class);
        mSessionEnterBarUseCase = new SessionEnterBarUseCase(ExecutorFactory.f().getUserExecutor()
                , ExecutorFactory.f().getUIThread());
        GlobalEventManager.getInstance().register(this, GlobalEventManager.EVN_NATIVE);
    }

    public void setSessionListVm(SessionListInnerViewModel sessionListVm) {
        this.sessionListVm = sessionListVm;
    }

    private ISessionListView2 getView() {
        return mIView.get();
    }

    private static String interactionLuaUrl() {
        return InteractionNoticeHandler.Companion.luaUrl();
    }

    /**
     * 刷新Session的用户信息
     *
     * @param type
     * @param dataId
     */
    @Override
    public void refreshSessionProfile(final int type, final String dataId) {
        if (TextUtils.isEmpty(dataId)) {
            return;
        }
        String sessionKeyStr = MessageServiceHelper.getSessionIdByType(dataId, type);
        sessionManager.syncSession(new SessionUpdateBundle.ReloadInfo(
                SessionKey.Companion.fromString(sessionKeyStr)
        ));
    }

    /**
     * 收到黑名单添加广播
     *
     * @param remoteUserId 被拉黑用户的MOMOID
     */
    @Override
    public void processBlockListAction(String remoteUserId) {
        if (TextUtils.isEmpty(remoteUserId)) {
            return;
        }
        MDLog.d(LogTag.COMMON, "拉黑用户 " + remoteUserId);
        sessionManager.syncSession(new SessionUpdateBundle.ReloadInfo(
                SayHiSessionDefinition.KEY_SAYHI));
        if (GiftSayHiAppConfigV1.isOpenExp()) {
            sessionManager.syncSession(new SessionUpdateBundle.ReloadInfo(
                    GiftSayHiSessionDefinition.KEY_SAYHI));
        }
        MomoKit.getApp().removeSayhiNotify();
    }

    @Override
    public void ignoreAllUnread(boolean needAnimation, boolean showTip) {
        ignoreNotice(false);
        ignoreAllUnreadMessages(needAnimation, showTip);
    }

    @Override
    public void ignoreNotice(boolean updateCount) {
        ignoreNotice(updateCount, false, true);
    }

    /**
     * 忽略互动通知消息
     * 新策略与旧策略均使用
     *
     * @param updateCount               更新未读数
     * @param reload                    重新加载互动通知消息
     * @param updateNewInteractionState 更新新版策略的状态
     */
    private void ignoreNotice(boolean updateCount, boolean reload, boolean updateNewInteractionState) {
        //使用新策略，更新新版策略数据状态
        if (updateNewInteractionState) {
            //新版本未读数与内容均由IM协议下发，因此更新数量或重新加载数据均调用更新方法
            readAllInteractionNotice(false);
            if (UniAppConfigV1Getter.get().interactive() == 1) {
                UniUnreadManager.INSTANCE.clearUniInteractUnread(SessionCons.ID.INTERACT_ID);
            }
        }
    }

    /**
     * 聊天消息刷新，如果Session列表有，则直接刷新，不需要DB处理，没有的话，再去DB处理
     *
     * @param bundle
     * @param sessionType
     */
    private void onReceiveChatMessages(Bundle bundle, int sessionType) {
        List<Message> list = bundle.getParcelableArrayList(MessageKeys.Key_MessageArray);

        if (list == null && sessionType != Session.TYPE_VCHAT_SUPER_ROOM) {
            IMEventReporter.eventOfflineTime(IMOfflineEvent.MSG_UI_NOTIFY_CHAT_LIST_EMPTY,
                    MUPairItem.id("SessionListPresenter"),
                    MUPairItem.type(sessionType));
            return;
        }
        sendMessage2Live(list);
        String sessionKeyStr = MessageServiceHelper.getSessionIdByBundle(bundle);
        //        MsgLogUtil.logEvent(FabricLogger.NewMsgEvent.NEW_MSG_UI_SessionP_SUCCESS, false, list.size());

        SessionModel session = getView().getSessionModelInUi(sessionKeyStr);

        if (session != null) {//在UI中，直接刷新
            //来新消息的时候添加到刷新列表里面去
            if ((sessionType == AbsSession.TYPE_CHAT || sessionType == AbsSession.TYPE_GROUP || sessionType == AbsSession.TYPE_MASK_CHAT)
                    && getView().isForeground()) {
                addSessionToRefreshList(session);
            }

            refreshUnreadMessageCount();
        }
    }

    /**
     * 把消息封装下，发送给直播
     *
     * @param list
     */
    private void sendMessage2Live(List<Message> list) {
        if (liveTimerHelper == null) {
            initLiveMessageTimer();
        }
        if (list == null) {
            return;
        }
        for (Message message : list) {
            if (message.fwdlive) {
                liveTimerHelper.addData(message);
            }
        }
    }

    private void initLiveMessageTimer() {
        if (mLiveHandlerThread == null) {
            mLiveHandlerThread = new HandlerThread("live_message_handler");
            mLiveHandlerThread.start();
        }
        liveTimerHelper = new LiveTimerHelper<Message>(1000, mLiveHandlerThread.getLooper()) {
            @Override
            public void pushData(Message message) {
                User currentUser = MomoKit.getCurrentUser();
                if (message == null || currentUser == null || TextUtils.equals(message.remoteId, currentUser.momoid)) {
                    //过滤掉自己发送的消息。
                    return;
                }
                if (message.chatType == Message.CHATTYPE_USER
                        && (message.contentType == Message.CONTENTTYPE_MESSAGE_AUDIO
                        || message.contentType == Message.CONTENTTYPE_MESSAGE_TEXT
                        || message.contentType == Message.CONTENTTYPE_MESSAGE_IMAGE
                        || message.contentType == Message.CONTENTTYPE_MESSAGE_VIDEO)) {
                    if (AppContext.DEBUGGABLE) {
                        MDLog.d(LogTag.Message.Message, "收到与直播相关的消息，包装下发给直播");
                    }
                    final MomoImEvent momoImEvent = new MomoImEvent();
                    switch (message.contentType) {
                        case Message.CONTENTTYPE_MESSAGE_AUDIO:
                            momoImEvent.msgType = MomoImEvent.TYPE_VOICE;
                            break;
                        case Message.CONTENTTYPE_MESSAGE_IMAGE:
                            momoImEvent.msgType = MomoImEvent.TYPE_PIC;
                            break;
                        case Message.CONTENTTYPE_MESSAGE_VIDEO:
                            momoImEvent.msgType = MomoImEvent.TYPE_VIDEO;
                            break;
                        default:
                            momoImEvent.msgType = MomoImEvent.TYPE_NORMAL;
                    }
                    momoImEvent.content = message.getContent();
                    momoImEvent.msgID = message.msgId;
                    momoImEvent.time = message.messageTime;
                    momoImEvent.momoID = message.remoteId;
                    User user = UserService.getInstance().get(message.remoteId);
                    if (user != null) {
                        if (!TextUtils.isEmpty(user.remarkName)) {
                            momoImEvent.name = user.remarkName;
                        } else if (!TextUtils.isEmpty(user.name)) {
                            momoImEvent.name = user.name;
                        } else {
                            momoImEvent.name = user.momoid;
                        }
                        momoImEvent.photoUrl = ImageLoaderUtil.getImageUrlWithId((String) user.getLoadImageId(), ImageType.IMAGE_TYPE_ALBUM_SMALL);
                    } else {
                        momoImEvent.name = message.remoteId;
                    }
                    momoImEvent.clickGoto = "[聊天|goto_chat|" + message.remoteId + "]";
                    MomoMainThreadExecutor.post(new Runnable() {
                        @Override
                        public void run() {
                            NotifyDispatcher.dispatch(momoImEvent);
                        }
                    });
                }
            }
        };
    }

    @UiThread
    @Override
    public boolean onMessageReceive(Bundle bundle, String action) {
        MsgLogUtil.logDebug("收到消息 action=%s, time=%d:", action, System.currentTimeMillis());
        if (!AppKit.getAccountManager().isOnline()
                && !MessageKeys.Action_UserMessge.equals(action)
                && !ISessionListView2.Action_SessionChanged.equals(action)) {
            //访客模式下只收取Action_UserMessge消息
            return true;
        }

        switch (action) {
            case MessageKeys.SessionList.Action_Refresh_All:
                break;
            case MessageKeys.SessionList.Action_Home_Resume:
                refreshUnreadMessageCount();
                break;
            case MessageKeys.Action_UserMessge:
            case MessageKeys.Action_UserLocalMessage:
                //接收到聊天消息后
                onReceiveChatMessages(bundle, Session.TYPE_CHAT);
                break;
            case MessageKeys.ACTION_MASK_CHAT_UserMessage:
                onReceiveChatMessages(bundle, Session.TYPE_MASK_CHAT);
                break;
            case MessageKeys.Action_GroupMessge:
            case MessageKeys.Action_GroupLocalMessage:
                onReceiveChatMessages(bundle, Session.TYPE_GROUP);
                break;
            case MessageKeys.FlashChat.Action_FlashChat_Message:
            case MessageKeys.ACTION_TEXT_CHAT_UserMessage:
                //接收到聊天消息后
                onReceiveChatMessages(bundle, Session.TYPE_GOTO);
                break;
            case MessageKeys.Action_DiscussMessage:
            case MessageKeys.Action_DiscussLocalMessage:
                onReceiveChatMessages(bundle, Session.TYPE_DISCUSS);
                break;
            case MessageKeys.Action_HiMessage:
                break;
            case MessageKeys.Action_MessgeStatus:
                break;
            case ISessionListView2.Action_SessionChanged: {
                String oldSessionId = bundle.getString(ISessionListView2.Key_SessionId);
                SessionManager.get().syncSession(new SessionUpdateBundle.ReloadInfo(SessionKey.fromCompatible(oldSessionId)));
            }
            break;
            case ISessionListView2.Action_VideoChat: {
                String sessionId = bundle.getString(ISessionListView2.Key_SessionId);
                updateVideoChatSession(sessionId);
                break;
            }
            case ISessionListView2.Action_SyncFinished:
                break;
            case MessageKeys.Action_GotoSession:
                break;
            case MessageKeys.Action_UpdateMessage: {
                break;
            }

            case MessageKeys.Action_NoticeStatusChanged:
                loadInteractionData();
                updateUnreadWithFollow();
                break;
            case MessageKeys.Action_Feed:
                updateUnreadWithFollow();
                break;
            case MessageKeys.Action_ContactNotice: {
                int unread = bundle.getInt(MessageKeys.Key_Uncount_ContactNotice);
                Log4Android.getInstance().i("tang--------收到新的好友通知 " + unread);
                refreshContactUnreadCount(unread);
                break;
            }
            case MessageKeys.Action_StarQChat_Invite_Msg:
                break;
            case ISessionListView2.Action_SessionChangedFromVChat: {
                String seesionID = bundle.getString(SessionListFragment.Key_SessionId);
                sessionManager.syncSession(new SessionUpdateBundle.ReloadInfo(SessionKey.fromString(seesionID)));
                break;
            }
            case com.immomo.android.module.vchat.MessageKeys.VoiceChat.ACTION_VOICE_CHAT_MESSAGE: {
                // 收到im消息，把内存中状态信息置空
                String vid = bundle.getString(com.immomo.android.module.vchat.MessageKeys.VCHAT_SUPER_ROOM_ID);
                VChatSuperRoomSessionCache.delete(vid);
                onReceiveChatMessages(bundle, Session.TYPE_VCHAT_SUPER_ROOM);
                break;
            }
            case com.immomo.android.module.vchat.MessageKeys.VoiceChat.ACTION_VOICE_CHAT_DELETE_SESSION: {
                String sessionId = bundle.getString(com.immomo.android.module.vchat.MessageKeys.VoiceChat.ACTION_VOICE_CHAT_DELETE_SESSION_ID);
                if (sessionListVm != null) {
                    sessionListVm.removeSession(SessionKey.Companion.fromString(sessionId));
                }
                break;
            }
            case com.immomo.android.module.vchat.MessageKeys.VoiceChat.ACTION_VOICE_CHAT_SUPER_ROOM: {
                int actionType = bundle.getInt(com.immomo.android.module.vchat.MessageKeys.VoiceChat.KEY_VCHAT_ACTION_TYPE);
                if (actionType == VChatAction.SUPER_ROOM_STATUS) {
                    VChatSuperRoomStatus chatSuperRoomStatus = bundle.getParcelable(com.immomo.android.module.vchat.MessageKeys.VoiceChat.KEY_SUPER_ROOM_STATUS);
                    if (chatSuperRoomStatus == null || TextUtils.isEmpty(chatSuperRoomStatus.getVid())) {
                        break;
                    }
                    // 如果第一个状态消息来的时候session中还没有对应的cell，过滤这个session
                    final String sessionId = MessageServiceHelper.getSessionIdOfVChatSuperRoom(chatSuperRoomStatus.getVid());
                    SessionModel session = getView().getSessionModelInUi(sessionId);
                    if (session == null) {
                        break;
                    }
                    VChatSuperRoomSessionCache.saveOrUpdateStatus(chatSuperRoomStatus.getVid(), chatSuperRoomStatus);
                    onReceiveVChatSuperRoomStatusChange(chatSuperRoomStatus.getVid(), false);
                } else if (actionType == VChatAction.CREATE_OR_ATTACH_SUPER_ROOM) {
                    String vid = bundle.getString(com.immomo.android.module.vchat.MessageKeys.VoiceChat.KEY_VCHAT_VID);
                    final String sessionId = MessageServiceHelper.getSessionIdOfVChatSuperRoom(vid);
                    if (TextUtils.isEmpty(sessionId) || TextUtils.isEmpty(vid)) {
                        break;
                    }
                    SessionModel session = getView().getSessionModelInUi(sessionId);
                    if (session != null) {
                        break;
                    }
                    onReceiveVChatSuperRoomStatusChange(vid, true);
                }
                break;
            }
            case IMConfigs.Action_IMJWarning: {
                if (getView() == null) {
                    break;
                }
                String warn = bundle.getString(IMConfigs.Key_IMWarning_Message);
                String type = bundle.getString(IMConfigs.Key_IMWarning_Type);
                if (warn != null && IMConfigs.WARNING_TYPE_NETWORK.equals(type) && !NetUtils.isNetworkAvailable()) {
                    getView().hidePushGuideTips();
                }
                break;
            }
            case MessageKeys.Action_Game_Business: {
                String packet = bundle.getString(MessageKeys.Key_Game_Business);
                try {
                    Map<String, Object> map = GsonUtils.g().fromJson(packet, new TypeToken<Map<String, Object>>() {
                    }.getType());
                    GlobalEventManager.Event event = new GlobalEventManager.Event("kBusinessGameReceivedMessage").dst("lua").src(GlobalEventManager.EVN_NATIVE).msg(map);
                    GlobalEventManager.getInstance().sendEvent(event);
                } catch (Exception e) {
                    MDLog.printErrStackTrace(LogTag.Message.SessionList, e);
                }

                break;
            }
            case MessageKeys.ACTION_INTERACTION_NOTICE:
                //接收到了互动通知消息
                updateInteractionNoticeData();
                break;
            case SortCons.Action.SET_SESSION_RECOMMEND:
                //目前无后续处理方法，如上浮需求变化可添加
                break;
            case MessageKeys.ACTION_REMOVE_SESSION_UI:
                break;
            case MessageKeys.MaskChat.ACTION_MASK_NORMAL_SESSION:
                removeMaskChatSessionItemModel(bundle);
                break;
            case MessageKeys.TextChat.ACTION_PASS_TEST:
                String remoteId = bundle.getString("remoteId");
                onTextChatSessionChange(remoteId);
                break;
            case MessageKeys.TextChat.ACTION_MATCH_SUCCESS:
                String remoteId1 = bundle.getString("remoteId");
                TChatConst.UnlockSuccess.INSTANCE.remove(remoteId1);
                break;
            case MessageKeys.ACTION_ECOLOGICAL_GOVERNANCE_ALERT:
                ISessionListView2 view = getView();
                if (view != null && isPageInResumeState) {
                    view.checkEcologicalAlert();
                }
                break;
            case MessageKeys.Action_ChatWebAction: // 处理火花相关业务
                try {
                    String chatData = bundle.getString(MessageKeys.Key_Chat_Web);
                    if (StringUtils.isNotBlank(chatData)) {
                        JSONObject jsonObject = new JSONObject(chatData).optJSONObject("data");
                        if (jsonObject != null) {
                            String business = jsonObject.optString("business");
                            if (StringUtils.equalsNonNull(business, "fire")) {
                                String momoid = jsonObject.optString("remoteId");
                                if (StringUtils.isNotBlank(momoid)) {
                                    nextTimeRequestOnlineStatue = true;
                                    forceRefreshOnlineStatueNextTime(UserChatSessionDefinition.key(momoid).getValue());
                                    nextTimeRequestOnlineStatue = false;
                                }
                            }
                        }
                    }
                } catch (Throwable throwable) {
                    MDLog.printErrStackTrace(LogTag.COMMON, throwable);
                }
                break;
            case MessageKeys.ACTION_SESSION_FORCE_DESC_REFRESH:
                String descData = bundle.getString(MessageKeys.KEY_SESSION_DESC);
                String momoid = bundle.getString(MessageKeys.KEY_SESSION_MOMOID);
                if (StringUtils.isNotBlank(descData) && StringUtils.isNotBlank(momoid)) {
                    SessionUserHelper.INSTANCE.updateSessionDesc(momoid, descData);
                }
                break;
            default:
                break;
        }
        return false;
    }

    private void removeMaskChatSessionItemModel(Bundle bundle) {
        String remoteId = bundle.getString(MessageKeys.MaskChat.KEY_remoteId, "");
        if (StringUtils.isNotEmpty(remoteId)) {
            sessionListVm.removeSession(SessionKey.fromString(remoteId));
        }
    }

    private void updateUnreadWithFollow() {
        ISessionListView2 iView = mIView.get();
        if (iView != null
                && iView.getViewParentFragment() instanceof IBaseForBusinessView
                && StringUtils.equalsNonNull(((IBaseForBusinessView) iView.getViewParentFragment()).getHomeKey(),
                FrameConfigManager.INSTANCE.getHomeKeyByBusinessKey(FrameConfigConst.FRAME_FOLLOW))) {
            refreshUnreadMessageCount();
        }
    }

    private void onReceiveVChatSuperRoomStatusChange(final String vid, boolean newOrder) {
        final String sessionId = MessageServiceHelper.getSessionIdOfVChatSuperRoom(vid);

        ThreadUtils.execute(ThreadUtils.TYPE_RIGHT_NOW, new Runnable() {
            public void run() {
                try {
                    VChatSuperRoom tempRoom = SessionApi.getInstance().downloadVChatSuperRoomProfile(vid);
                    if (tempRoom != null) {
                        SessionModel adapterSession = getView().getSessionModelInUi(sessionId);
                        if (tempRoom.isExpired()) {
                            if (adapterSession != null) {
                                AppAsm.getRouter(VChatMiscRouter.class).undoSuperRoomSesssionSticky(tempRoom.getVid());
                            }
                            return;
                        }

                        VChatSuperRoomService.getInstance().saveOrUpdate(tempRoom);
                        SessionUserCache.update(vid, tempRoom);

                        sessionManager.syncSession(new SessionUpdateBundle.ReloadInfo(
                                SessionKey.fromString(sessionId)), true);
                    }
                } catch (Exception e) {
                    MDLog.printErrStackTrace(LogTag.Message.SessionList, e);
                }
            }
        });
    }

    @Override
    public void getPushSwitchTipsInfo() {
        MomoTaskExecutor.cancleAllTasksByTag(getPushSwitchTipsInfoTag);
        MomoTaskExecutor.executeUserTask(getPushSwitchTipsInfoTag, new getPushSwitchTipsInfoTask());
    }

    @Override
    public void getPushSwitchDialogInfo() {
        MomoTaskExecutor.cancleAllTasksByTag(getPushSwitchDialogInfoTag);
        MomoTaskExecutor.executeUserTask(getPushSwitchDialogInfoTag, new getPushSwitchDialogInfoTask());
    }

    public void maskChatQuit(Callback<MaskChatQuitData> callback) {
        MomoTaskExecutor.cancleAllTasksByTag(maskChatQuitTag);
        MomoTaskExecutor.executeUserTask(maskChatQuitTag, new getMaskChatQuitTask(callback));
    }

    private class getMaskChatQuitTask extends MomoTaskExecutor.Task<Object, Object, MaskChatQuitData> {
        private Callback<MaskChatQuitData> mCallback = null;

        public getMaskChatQuitTask(Callback<MaskChatQuitData> callback) {
            mCallback = callback;
        }

        @Override
        protected MaskChatQuitData executeTask(Object... objects) throws Exception {
            return UserApi.getInstance().getMaskChatQuitInfo("msg_frame");
        }

        @Override
        protected void onTaskSuccess(MaskChatQuitData maskChatData) {
            super.onTaskSuccess(maskChatData);
            if (maskChatData == null) {
                Toaster.show("退出失败,请重试～");
                return;
            }
            mCallback.callback(maskChatData);
        }

        @Override
        protected void onTaskError(Exception e) {
            if (e instanceof MomoServerException) {
                if (((MomoServerException) e).errorCode == -100) {
                    mCallback.callback(null);
                } else {
                    super.onTaskError(e);
                }
            } else {
                super.onTaskError(e);
            }

        }
    }


    @Override
    public void initData() {
        loadMoreSessions(true, false);
        if (AppKit.getAccountManager().isOnline()) {
            loadInteractionData();
        }
        initRefreshTimeData();
    }

    private void refreshUserOnlineStatus() {
        //定时刷新
        disposable = Flowable.interval(0, intervalTimeList, TimeUnit.MILLISECONDS)
                .onBackpressureDrop()
                .subscribeOn(Schedulers.from(ExecutorFactory.f().getUserExecutor()))
                .observeOn(ExecutorFactory.f().getUIThread().getScheduler())
                .subscribeWith(new DisposableSubscriber<Long>() {
                    @Override
                    public void onNext(Long aLong) {
                        try {
                            if (Math.abs(System.currentTimeMillis() - lastAddUserInViewTime) > intervalTimeUserAndGroup) {
                                if (getView().isForeground()) {
                                    addSeesionInViewToRefreshList();
                                }
                                lastAddUserInViewTime = System.currentTimeMillis();
                            }
                            if (!needRefreshOnlinTagSet.isEmpty() && getView().isForeground()) {
                                batchRequestOnlineStatue();
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {

                    }

                    @Override
                    public void onComplete() {

                    }
                });
    }

    public void addSeesionInViewToRefreshList() {
        getView().getVisibleChatSessionModel().stream().forEach(this::addSessionToRefreshList);
    }

    @Override
    public void addSessionInViewToRefreshListByScroll() {
        try {
            getView().getVisibleChatSessionModel().stream().forEach(this::addSessionToRefreshListOnlyUser);
        } catch (Throwable throwable) {
            MDLog.printErrStackTrace(LogTag.COMMON, throwable);
        }
    }

    private void initRefreshTimeData() {
        int listIntervalTime = KV.getSysInt(SPKeys.System.AppMultiConfig.KEY_SESSION_ONLINE_STATUS_REFRESH, intervalTimeList);
        int userIntervalTime = KV.getSysInt(SPKeys.System.AppMultiConfig.KEY_SESSION_ONLINE_STATUS_AGE, intervalTimeUserAndGroup);
        intervalTimeList = Math.max(listIntervalTime * 1000, intervalTimeList);
        intervalTimeUserAndGroup = Math.max(userIntervalTime * 1000, intervalTimeUserAndGroup);
    }

    //<editor-fold desc="Description">


    @Override
    public void refreshContactUnreadCount(int noticeCount) {
        if (noticeCount < 0) {
            getContactNoticeUnread(this::refreshUnreadCount);
            return;
        }
        refreshUnreadCount(noticeCount);
    }

    @Override
    public void initContactUnreadCount() {
        getContactNoticeUnread(this::refreshUnreadCount);
    }

    @Override
    public void getContactNoticeUnread(ContactNoticeUnreadCountCallback callback) {
        RXUtilsKt.dispose(mContactCountDisposable);
        mContactCountDisposable =
                Flowable.fromCallable(() -> {
                            if (mFriendsModel != null) {
                                return mFriendsModel.getContactNoticeUnread();
                            } else {
                                return 0;
                            }
                        }).subscribeOn(Schedulers.from(MMThreadExecutors.INSTANCE.getUser()))
                        .observeOn(MMThreadExecutors.INSTANCE.getMain().getScheduler())
                        .subscribeWith(new CommonSubscriber<Integer>() {
                            @Override
                            public void onNext(Integer count) {
                                super.onNext(count);
                                if (callback != null) {
                                    callback.onContactNoticeUnreadCount(count);
                                }
                            }

                        });

    }

    private void refreshUnreadCount(int count) {
        if (mIView != null && mIView.get() != null) {
            mIView.get().updateContactUnreadCount(count);
        }
    }

    //</editor-fold>

    @Override
    public void requestRecommendSessions() {
        if (!SortManager.INSTANCE.canRequestRecommendToday()) {
            return;
        }
        if (mSessionRecommendUseCase != null) {
            mSessionRecommendUseCase.dispose();
        }
        mSessionRecommendUseCase = new SessionRecommendUseCase(ModelManager.getModel(ISessionRepository.class));
        mSessionRecommendUseCase.execute(new CommonSubscriber<String>() {
            @Override
            public void onNext(String o) {
                super.onNext(o);
                SortManager.INSTANCE.recordRequest();
            }
        });
    }

    @Override
    public void reloadAll() {
        MomoTaskExecutor.cancleAllTasksByTag(InitTaskTag);
        loadMoreSessions(true, true);
    }

    @Override
    public void onResume() {
        isPageInResumeState = true;
        addSeesionInViewToRefreshList();
        refreshUserOnlineStatus();
        requestRecommendSessions();
        if (Math.abs(System.currentTimeMillis() - lastMillionFloatRefresh)
                > refreshCapsuleInterval * 1000) {
            pullSessionEnterBar();
        }
    }

    @Override
    public void onPause() {
        isPageInResumeState = false;
        if (disposable != null && !disposable.isDisposed()) {
            disposable.dispose();
        }
        MomoTaskExecutor.cancleAllTasksByTag(hashCode());
    }

    @Override
    public void onDestroy() {
        sessionListVm = null;

        GlobalEventManager.getInstance().unregister(this, GlobalEventManager.EVN_NATIVE);
        MomoMainThreadExecutor.cancelAllRunnables(this);
        MomoMainThreadExecutor.cancelAllRunnables(hashCode());
        if (mSessionEnterBarUseCase != null) {
            mSessionEnterBarUseCase.dispose();
        }
        if (mSessionRecommendUseCase != null) {
            mSessionRecommendUseCase.dispose();
        }
        RXUtilsKt.dispose(mContactCountDisposable);
        RXUtilsKt.dispose(mNewBodyAndHePaiDisposable);
        if (migrateUnReplyHelper != null) {
            migrateUnReplyHelper.destroy();
        }
        if (newBoySessionHelper != null) {
            newBoySessionHelper.destroy();
        }
        if (hePaiSessionHelper != null) {
            hePaiSessionHelper.destroy();
        }
        if (spamSessionHelper != null) {
            spamSessionHelper.destroy();
        }
        if (sessionFoldMigrateHelper != null) {
            sessionFoldMigrateHelper.destroy();
        }
        if (sessionGiftSayhiMigrateHelper != null) {
            sessionGiftSayhiMigrateHelper.destroy();
        }
        if (sessionUniverseMigrateHelper != null) {
            sessionUniverseMigrateHelper.destroy();
        }
    }

    /**
     * 忽略所有未读消息条目
     */
    private void ignoreAllUnreadMessages(boolean needAnimation, boolean showTip) {
        unreadMessageCount = 0;
        HomeUnreadManager.INSTANCE.setSessionFragmentUnread(unreadMessageCount);

        ISessionListView2 iView = mIView.get();
        if (iView == null) {
            return;
        }
        iView.clearAllUnread();

        iView.showBottomBubble(HomeUnreadManager.INSTANCE.getHomeUnreadCount(((IHomeBaseProvider) iView.getViewParentFragment()).getHomeKey()), showTip);
        MomoKit.getApp().removeAllNotify();
        if (needAnimation) {
            getView().clearDragBubble();
        }
        MomoKit.getApp().sendNotifyIconCount(unreadMessageCount);
        ThreadUtils.execute(ThreadUtils.TYPE_RIGHT_NOW, new Runnable() {
            @Override
            public void run() {
                try {
                    MessageServiceHelper.getInstance().clearUnreadMessage();
                    // 告知服务器 用户已经忽略所有未读消息
                    SessionApi.getInstance().setAllReaded();
                } catch (Exception e) {
                    log.e(e);
                }
            }
        });
        ThreadUtils.execute(ThreadUtils.TYPE_RIGHT_NOW, new UnreadUploadRunnable());
    }

    @Override
    public void refreshUnreadMessageCount() {
        ISessionListView2 iView = mIView.get();
        if (iView == null || sessionListVm == null) {
            return;
        }
        //sessionUnread 如果为-1 表示只有通知类未读。如果外层没有其他未读，就展示红点
        int sessionUnread = sessionListVm.getUnreadCount();
        int otherUnread = 0;
        if (otherUnread == 0) {//没有其他消息了，sessionUnread是多少，最终是多少
            unreadMessageCount = sessionUnread;
        } else {//还有其他未读，sessionUnread的-1 就忽略，当做0处理
            unreadMessageCount = Math.max(0, sessionUnread) + otherUnread;
        }

        HomeUnreadManager.INSTANCE.setSessionFragmentUnread(unreadMessageCount);
        int homeUnreadCont = HomeUnreadManager.INSTANCE.getHomeUnreadCount(((IHomeBaseProvider) iView.getViewParentFragment()).getHomeKey());
        iView.showBottomBubble(homeUnreadCont, true);

        EventBus.getDefault().post(new DataEvent<>(EventKeys.TabTest.TAB_SESSION_REFRESH_UNREAD_COUNT, new UnreadData(unreadMessageCount)));
        if (FriendQChatWorker.isReceivedRequest()) {
            iView.showVideoTagView();
            iView.hideBottomBubble();
        } else {
            iView.hideVideoTagView();
        }
        MomoKit.getApp().sendNotifyIconCount(unreadMessageCount);
    }

    @Override
    public void pullSessionEnterBar() {
        mSessionEnterBarUseCase.execute(new CommonSubscriber<SessionEnterBarResponse>() {
            @Override
            public void onNext(SessionEnterBarResponse response) {
                ISessionListView2 view = getView();
                if (view != null) {
                    lastMillionFloatRefresh = System.currentTimeMillis();
                    if (response != null && response.getInterval() > 0) {
                        refreshCapsuleInterval = response.getInterval();
                    }
                    view.configEnterBar(response);
                }
            }

            @SuppressLint("MissingSuperCall")
            @Override
            public void onError(Throwable exception) {

            }

            @Override
            public void onComplete() {
            }
        }, "");
    }

    private void onTextChatSessionChange(String remoteId) {
        RXUtilsKt.dispose(mTextChatChangeDisposable);
        mTextChatChangeDisposable = Flowable.fromCallable(() -> {
                    TChatConst.UnlockSuccess.INSTANCE.markUnlockSuccess(remoteId);
                    TextChatService.getInstance().moveTextChatToNormalSession(remoteId);
                    return true;
                })
                .delay(500, TimeUnit.MILLISECONDS)
                .subscribeOn(Schedulers.from(MMThreadExecutors.INSTANCE.getUser()))
                .observeOn(MMThreadExecutors.INSTANCE.getMain().getScheduler())
                .subscribeWith(new CommonSubscriber<Boolean>() {
                    @Override
                    public void onError(@Nullable Throwable exception) {
                        super.onError(exception);
                        MDLog.d(TextChatSessionListActivity.TAG, "pass test and move session error:" + exception.getMessage());
                    }
                });
    }

    /**
     * Wrapper
     *
     * @param intent
     */
    private void startActivity(Intent intent) {
        Context context = getContext();
        if (context != null) {
            context.startActivity(intent);
        }
    }

    private Context getContext() {
        ISessionListView2 iView = mIView.get();
        if (iView == null) return MomoKit.getContext();
        return iView.getBaseActivity();
    }

    @Override
    public void showAlertDialog(@NonNull SessionItemModel.SessionViewHolder viewHolder,
                                int posInAdapter,
                                @NonNull String[] actionList,
                                boolean dontNeedSubTitle,
                                @NonNull SessionModel session,
                                String typeGotoSessionId) {
        final ISessionListView2 iView = mIView.get();
        if (iView == null || sessionListVm == null) return;
        MSubTitleListDialog mAlertListDialog = new MSubTitleListDialog(iView.getBaseActivity(),
                toPairList(actionList, dontNeedSubTitle ? null : selectionTitleMap()));
        mAlertListDialog.setSupportDark(true);
        final String finalTypeGotoSession = typeGotoSessionId;
        mAlertListDialog.setOnItemSelectedListener(index -> {
            if (iView == null || sessionListVm == null) return;
            switch (actionList[index]) {
                case STR_DELETE:
                    SessionHelper.Log.logOptionDialogClick(session, "1");
                    sessionListVm.removeSession(session.getSessionKey());
                    break;
                case STR_MASK_DELETE:
                    maskChatQuit(new Callback<MaskChatQuitData>() {
                        @Override
                        public void callback(MaskChatQuitData chatQuitData) {
                            sessionListVm.removeSession(session.getSessionKey());
                        }
                    });

                    break;
                case STR_PEEK:
                    onClickPeek(session, iView);
                    break;
                case STR_DELETE_NOTICE:
                    MAlertDialog mAlertDialog = MAlertDialog.makeConfirm(iView.getBaseActivity(), "将删除所有通知消息，此操作不可恢复，请确认",
                            (dialog, which) -> {
                                sessionListVm.removeSession(session.getSessionKey());
                                refreshUnreadMessageCount();
                            });
                    mAlertDialog.setTitle("删除通知");
                    mAlertDialog.setSupportDark(true);
                    mAlertDialog.show();
                    break;
                case STR_DELETE_FRIEND_NOTICE:
                    MAlertDialog mConfirmDialog = MAlertDialog.makeConfirm(iView.getBaseActivity(),
                            "将删除所有好友提醒，此操作不可恢复，请确认",
                            (dialog, which) -> deleteFriendNoticeEntry(session));
                    mConfirmDialog.setTitle("删除通知");
                    mConfirmDialog.setSupportDark(true);
                    mConfirmDialog.show();
                    break;
                case STR_RECEIVE_MSG_CLOSE:
                    onClickReceiveMsg(session, finalTypeGotoSession, false);
                    break;
                case STR_RECEIVE_MSG_OPEN:

                    onClickReceiveMsg(session, finalTypeGotoSession, true);
                    break;
                case STR_GREET_NOT_REMINDER_OFF:
                    forbidGreetRemind(false);
                    break;
                case STR_GREET_NOT_REMINDER_ON:
                    forbidGreetRemind(true);
                    break;
                default:
                    break;
            }
        });
        mAlertListDialog.setOnCancelListener(dialog -> SessionHelper.Log.logOptionDialogClick(session, "2"));
        mAlertListDialog.setOnShowListener(dialog -> {
            DefaultSessionOnLongClickListenerKt.logLongClick(viewHolder, session);
        });

        mAlertListDialog.show();
    }

    @Override
    public void showHepaiMsgSwitchDialog(
            @NonNull SessionModel session,
            String typeGotoSessionId) {
        boolean isTypeGotSessionReceiveMsg =
                BusinessNotifySwitchUtils.getIntance().getSwitchStatus(typeGotoSessionId);
        String[] actionList = new String[1];
        if (isTypeGotSessionReceiveMsg)
            actionList[0] = SessionListPresenter.STR_RECEIVE_MSG_CLOSE;
        else
            actionList[0] = SessionListPresenter.STR_RECEIVE_MSG_OPEN;
        final ISessionListView2 iView = mIView.get();
        if (iView == null || sessionListVm == null) return;
        MSubTitleListDialog mAlertListDialog = new MSubTitleListDialog(iView.getBaseActivity(),
                toPairList(actionList, null));
        final String finalTypeGotoSession = typeGotoSessionId;
        mAlertListDialog.setOnItemSelectedListener(index -> {
            switch (actionList[index]) {
                case STR_RECEIVE_MSG_CLOSE:
                    onClickReceiveMsg(session, finalTypeGotoSession, false);
                    break;
                case STR_RECEIVE_MSG_OPEN:
                    onClickReceiveMsg(session, finalTypeGotoSession, true);
                    break;
                default:
                    break;
            }
        });
        mAlertListDialog.setSupportDark(true);
        mAlertListDialog.show();
    }

    @Override
    public void showSpamMsgSwitchDialog(@NonNull SessionModel model, @Nullable String typeGotoSessionId) {
        String[] actionList = new String[1];
        actionList[0] = SessionListPresenter.STR_DELETE_SPAM;
        final ISessionListView2 iView = mIView.get();
        if (iView == null || sessionListVm == null) return;
        MSubTitleListDialog mAlertListDialog = new MSubTitleListDialog(iView.getBaseActivity(),
                toPairList(actionList, null));
        mAlertListDialog.setOnItemSelectedListener(index -> {
            switch (actionList[index]) {
                case STR_DELETE_SPAM:
                    sessionListVm.removeSession(model.getSessionKey());

                    spamSessionHelper.clearAllSpamMsg();

                    break;
                default:
                    break;
            }
        });
        mAlertListDialog.setSupportDark(true);
        mAlertListDialog.show();
    }

    private void onClickPeek(SessionModel sessionModel, ISessionListView2 iView) {
        if (!(sessionModel instanceof UserChatSessionModel)) return;
        UserChatSessionModel userChatSessionModel = (UserChatSessionModel) sessionModel;

        if (mUserModel.getCurrentUser().isMomoVip()) {
            Intent intent = new Intent(iView.getBaseActivity(), ChatActivity.class);
            intent.putExtra(ChatActivity.REMOTE_USER_ID, userChatSessionModel.getSessionId());
            intent.putExtra(ChatHelper.KEY_VIEWMODEL, ChatHelper.VIEWMODEL_PEEK);
            intent.putExtra(ChatHelper.KEY_BUSINESS_TYPE,
                    userChatSessionModel.getSessionBusinessType() == Session.BUSINESS_KLIAO_MATCH ? ChatBusinessType.CHAT_BUSINESS_KLIAO_MATCH : "");
            iView.getBaseActivity().startActivity(intent);
        } else {
            iView.showBuyVipDialog();
        }
    }

    @Override
    public void showSearchMenu() {
        searchMenuShow = true;
        if (getView() != null) {
            getView().showSearchMenu();
        }
    }

    @Override
    public boolean isSearchMenuShow() {
        return searchMenuShow;
    }

    private void onClickReceiveMsg(SessionModel session, String finalTypeGotoSession, boolean isOpen) {
        if (session instanceof GotoSessionModel) {
            showGotoSessionSecondConfirm(finalTypeGotoSession, ((GotoSessionModel) session).getTitle(), isOpen);
        } else if (session instanceof HePaiSessionModel) {
            showGotoSessionSecondConfirm(finalTypeGotoSession, "合拍", isOpen);
        }
    }

    private void deleteFriendNoticeEntry(SessionModel session) {
        if (sessionListVm == null) return;
        sessionListVm.removeSession(session.getSessionKey());
    }

    private void forbidGreetRemind(boolean notRemindOn) {
        if (ClickUtils.isFastClick()) {
            return;
        }
        MomoTaskExecutor.cancleAllTasksByTag(tagGreetReminder);
        Context context = getContext();
        Activity activity = context instanceof Activity ? (Activity) context : null;
        MomoTaskExecutor.executeUserTask(tagGreetReminder, new ChangeGreetRemindTask(notRemindOn, GreetHelper.isFromGreetRemindPush() ? "push" : "hand", activity));
    }

    private void showGotoSessionSecondConfirm(String businessId, String title, boolean isOpen) {
        typeGotoSessionReceiveMsgLog(businessId, isOpen);
        BusinessNotifySwitchUtils.getIntance().showAlertDialog(getContext(), businessId, title, isOpen, true, new BusinessNotifySwitchUtils.AlertDialogListener() {

            @Override
            public void onLeftBtnClick(DialogInterface dialog, int which) {

            }

            @Override
            public void onRigthBtnClick(DialogInterface dialog, int which) {
                ClickEvent.create()
                        .page(EVPage.Msg.Chatlist)
                        .action(EVAction.List.Open)
                        .putExtra("open_is", isOpen ? "1" : "0")
                        .putExtra("service_is", "0")
                        .putExtra("business", businessId)
                        .submit();
            }
        }, true);
        ExposureEvent.create(ExposureEvent.Type.Normal)
                .page(EVPage.Msg.Chatlist)
                .action(EVAction.Float.News)
                .putExtra("openpop_is", isOpen ? "1" : "0")
                .putExtra("work_is", "0")
                .putExtra("service", businessId)
                .submit();
    }

    private Map<String, String> titleMap;

    private Map<String, String> selectionTitleMap() {
        if (titleMap != null) {
            return titleMap;
        }
        titleMap = new HashMap<>();
        titleMap.put(STR_MASK_DELETE, "删除会直接退出当前匿名闪聊对话");
        titleMap.put(STR_DELETE, "聊天记录将会被永久删除，请谨慎操作");               //"删除对话";
        titleMap.put(STR_DELETE_NOTICE, "");                                   //"删除通知";
        titleMap.put(STR_DELETE_FRIEND_NOTICE, "");                            //"删除提醒";
        titleMap.put(STR_PEEK, "进入对话页面，不告知对方你是否已读");                                            //"悄悄查看";
        titleMap.put(STR_RECEIVE_MSG_OPEN, "");                                //"不再接收";
        titleMap.put(STR_RECEIVE_MSG_CLOSE, "");                               //"接收消息";
        titleMap.put(STR_GREET_NOT_REMINDER_ON, "你可以将招呼设置为今日不再提醒");// 招呼不再提醒
        titleMap.put(STR_GREET_NOT_REMINDER_OFF, "已关闭今天的招呼提醒，你可以现在开启");    // 招呼不再提醒
        return titleMap;
    }

    private void typeGotoSessionReceiveMsgLog(String typeGotoSessionId, boolean isOpen) {
        ClickEvent.create().page(EVPage.Msg.Chatlist)
                .action(EVAction.List.RemindOpen)
                .putExtra("which_item", typeGotoSessionId)
                .putExtra("close_open", isOpen ? 1 : 0)
                .putExtra("shell_service", "shell")
                .submit();
    }

    @Override
    public void onViewDestroyed() {
        MomoTaskExecutor.cancleAllTasksByTag(InitTaskTag);
        MomoTaskExecutor.cancleAllTasksByTag(LoadNoticeTaskTag);
        MomoTaskExecutor.cancleAllTasksByTag(OtherTaskTag);
        MomoTaskExecutor.cancleAllTasksByTag(refreshUserStatusTag);
        MomoTaskExecutor.cancleAllTasksByTag(getPushSwitchDialogInfoTag);
        MomoTaskExecutor.cancleAllTasksByTag(setGotoSessionSwitchTaskTag);
        MomoTaskExecutor.cancleAllTasksByTag(tagGreetReminder);
        if (disposable != null) {
            disposable.dispose();
        }
        if (mLiveHandlerThread != null) {
            mLiveHandlerThread.quit();
        }
        BusinessNotifySwitchUtils.getIntance().removeCallbackListenerByTag(BusinessNotifySwitchTag);
    }

    @Override
    public void onGlobalEventReceived(GlobalEventManager.Event event) {
    }

    /**
     * 当收到对话状态发生改变时对界面进行更新
     *
     * @param sessionId
     */
    private void updateVideoChatSession(String sessionId) {
        sessionManager.updateSessionForJava(SessionKey.Companion.fromString(sessionId), false, entity -> {
            SessionContent chatContent = SessionContentKt.getContent(entity);
            if (chatContent != null) {
                chatContent.forceRefresh();
                return true;
            }
            return false;
        });
    }

    /**
     * 只记录单聊群聊
     *
     * @param model
     */
    private void addSessionToRefreshListOnlyUser(SessionModel model) {
        if (model instanceof UserChatSessionModel) {
            addChatToRefreshList(model.getSessionKeyStr(), model.getSessionId());
        } else if (model instanceof GroupChatSessionModel) {
            addGroupToRefreshList(model.getSessionKeyStr(), model.getSessionId());
        }
    }

    private void addSessionToRefreshList(SessionModel model) {
        if (model instanceof UserChatSessionModel) {
            addChatToRefreshList(model.getSessionKeyStr(), model.getSessionId());
        } else if (model instanceof GroupChatSessionModel) {
            addGroupToRefreshList(model.getSessionKeyStr(), model.getSessionId());
        } else if (model instanceof NewBoySessionModel) {
            asyncNewBodyAndHePaiSession(FolderType.NewBOY);
        } else if (model instanceof HePaiSessionModel) {
            asyncNewBodyAndHePaiSession(FolderType.HePai);
        }
    }

    private void asyncNewBodyAndHePaiSession(int type) {
        mNewBodyAndHePaiDisposable = Flowable.fromCallable(() -> SessionService.getInstance().findFoldSessions(type, 10))
                .subscribeOn(Schedulers.from(MMThreadExecutors.INSTANCE.getUser()))
                .observeOn(MMThreadExecutors.INSTANCE.getMain().getScheduler())
                .subscribeWith(new CommonSubscriber<List<UserChatSessionModel>>() {

                    @Override
                    public void onError(@Nullable Throwable exception) {
                        super.onError(exception);
                    }

                    @Override
                    public void onNext(List<UserChatSessionModel> userChatSessionModels) {
                        super.onNext(userChatSessionModels);
                        for (UserChatSessionModel foldSession : userChatSessionModels) {
                            addChatToRefreshList(foldSession.getSessionKeyStr(), foldSession.getSessionId());
                        }
                    }
                });
    }

    /**
     * 添加单聊到刷新列表
     */
    private void addChatToRefreshList(String sessionKeyStr, String sessionId) {
        User user = SessionUserCache.getUser(sessionId);
        if (user != null && !user.official) {
            addToOnlineStatueList(sessionKeyStr, user.lastStatusRefreshTime);
        }
    }

    private void addGroupToRefreshList(String sessionKeyStr, String sessionId) {
        Group remoteGroup = SessionUserCache.getGroup(sessionId);
        if (remoteGroup != null) {
            addToOnlineStatueList(sessionKeyStr, remoteGroup.getLastStatusRefreshTime());
        }
    }

    private void addToOnlineStatueList(String sessionKeyStr, long lastRefreshTime) {
        if (!TextUtils.isEmpty(sessionKeyStr) && Math.abs(System.currentTimeMillis() - lastRefreshTime) > intervalTimeUserAndGroup) {
            needRefreshOnlinTagSet.add(sessionKeyStr);
        }
        if (needRefreshOnlinTagSet.size() >= LAST_REFRESH_SIZE || nextTimeRequestOnlineStatue) {
            batchRequestOnlineStatue();
        }
    }

    @Override
    public void forceRefreshOnlineStatueNextTime(String sessionId) {
        // 只处理单聊页面返回的广播，  onResume里会再次刷新adapter的
        if (!TextUtils.isEmpty(sessionId)) {
            addToOnlineStatueList(sessionId, 0);
            nextTimeRequestOnlineStatue = true;
        }
    }

    @Override
    public synchronized void batchRequestOnlineStatue() {
        if (needRefreshOnlinTagSet.isEmpty()) return;

        nextTimeRequestOnlineStatue = false;
        // MomoTaskExecutor.cancleAllTasksByTag(refreshUserStatusTag);
        MomoTaskExecutor.executeUserTask(refreshUserStatusTag, new RefreshOnlineStatusTask(needRefreshOnlinTagSet) {
            @Override
            protected void onTaskSuccess(SessionOnlineBean onlineBean) {
                super.onTaskSuccess(onlineBean);
                if (onlineBean != null) {
                    HashMap<String, UserOnlineStatus> userOnlineList = onlineBean.getUserOnlineList();
                    if (userOnlineList != null && !userOnlineList.isEmpty()) {
                        SessionUserHelper.INSTANCE.onSessionUserUpdate(userOnlineList);
                    }
                }
            }
        });
    }

    @Override
    public void migrateSpamSession(boolean init) {
        if (spamSessionHelper == null) {
            spamSessionHelper = new SpamSessionHelper();
        }
        if (!init) {
            spamSessionHelper.resetMigrateStatus();
        }
        spamSessionHelper.migrateToSpam();
    }

    @Override
    public void addMigrateSpamIds(List<String> remoteIds) {
        if (spamSessionHelper == null) {
            spamSessionHelper = new SpamSessionHelper();
        }
        spamSessionHelper.getAddParamRemoteIds().addAll(remoteIds);
    }

    @Override
    public void loadMoreSessions(boolean isInit, boolean clearbefore) {
        if (!isInit && !hasMoreSession) {
            return;
        }
        MomoTaskExecutor.executeTask(ThreadUtils.TYPE_MESSAGE, InitTaskTag, new LoadMoreTask(isInit, clearbefore));
    }

    private class LoadMoreTask extends MomoTaskExecutor.Task<Object, Object, List<Session>> {

        private boolean isInitTask;//是否是初始化启动
        private boolean resumeActiveUser;//是否需要刷新最近在线用户
        //是否已经有打出的招呼入口session
        private boolean hasUnreplyEnter = false;

        public LoadMoreTask(boolean isInitTask, boolean clearBefore) {
            super();
            this.isInitTask = isInitTask;
        }

        public LoadMoreTask(boolean isInitTask, boolean clearBefore, boolean resumeActiveUser) {
            super();
            this.isInitTask = isInitTask;
            this.resumeActiveUser = resumeActiveUser;
        }

        @Override
        protected List<Session> executeTask(Object... params) throws Exception {
            // NOTE 临时解决偶发死锁问题，不要删除
            FlashChatService.getInstance();
            SingleMsgService.getInstance();
            GroupMsgService.getInstance();
            DiscussMsgService.getInstance();
            MaskChatService.getInstance();

            if (isInitTask) {
                sessionManager.waitForInit();

                hasUnreplyEnter = getUnreplyEnterStatus();
                UnReplySessionEnterHelper.hasUnreplyEnter = hasUnreplyEnter;
                UnreplySessionCacheHelper.resetUnreplyCache();
            }
            //注意，翻页时，一定要用session的数量,adapter中包含了 两个header
            return new ArrayList<>();
        }

        @Override
        protected void onTaskSuccess(List<Session> sessions) {
            final ISessionListView2 iView = mIView.get();
            if (iView == null) {
                return;
            }
            if (isInitTask) {
                // TODO by hlj 打散，每一个方法增加返回值是否命中要处理，如果为false；进行下一个检测，如果为true，略过后面？
                migrateUnreplySession();
                migrateNewBoySession();//下线了
                migrateHePaiSession();//下线了
                migrateSpamSession(true);
                migrateFoldSession();
                migrateGiftSayHISession();
                migrateUniverseSession();
            }
            if (resumeActiveUser) {
                MomoTaskExecutor.executeUserTask(LoadNoticeTaskTag, new RefreshActiveUserTask());
            }
            refreshUnreadMessageCount();
            if (isInitTask) {
                iView.hideLoadingTip();
            }
        }

        @Override
        protected void onTaskError(Exception e) {
            super.onTaskError(e);
        }

        //判读是否有打出招呼入口，非实验时把入口删除,并把foldertype还原
        private boolean getUnreplyEnterStatus() {
            boolean hasEnter = SessionService.getInstance().checkExist(FoldSessionDefinition.UNREPLY);
            if (!isFoldHiTest && hasEnter) {
                SessionService.getInstance().updateSessionFolderType(FolderType.Unreply, FolderType.Default);
                sessionManager.deleteSession(SessionKey.fromString(FoldSessionDefinition.UNREPLY));
                UnReplySessionEnterHelper.setShowUnreplyEnter(false);
                hasEnter = false;
            }
            return hasEnter;
        }

        /**
         * 打出的招呼数据迁移，条件：
         * 1.实验组
         * 2.有入口 或者 无入口但是session数量>=10
         */
        private void migrateUnreplySession() {
            if (isFoldHiTest && !hasUnreplyEnter && UnReplySessionEnterHelper.isShowUnreplyEnter()) {
                if (migrateUnReplyHelper == null) {
                    migrateUnReplyHelper = new UnReplySessionHelper();
                }
                migrateUnReplyHelper.migrateToUnreply();
            }
        }

        private void migrateNewBoySession() {
            if (newBoySessionHelper == null) {
                newBoySessionHelper = new NewBoySessionHelper();
            }
            newBoySessionHelper.migrateToNewBoy();
        }

        private void migrateHePaiSession() {
            if (hePaiSessionHelper == null) {
                hePaiSessionHelper = new HePaiSessionHelper();
            }
            hePaiSessionHelper.migrateToHePai();
        }

        private void migrateFoldSession() {
            if (sessionFoldMigrateHelper == null) {
                sessionFoldMigrateHelper = new SessionFoldMigrateHelper();
            }
            sessionFoldMigrateHelper.migrate();
        }

        /**
         * 礼物session初始化
         */
        private void migrateGiftSayHISession() {
            if (sessionGiftSayhiMigrateHelper == null) {
                sessionGiftSayhiMigrateHelper = new SessionGiftSayhiMigrateHelper();
            }
            sessionGiftSayhiMigrateHelper.migrate();
        }

        /**
         * 小宇宙session初始化
         */
        private void migrateUniverseSession() {
            if (sessionUniverseMigrateHelper == null) {
                sessionUniverseMigrateHelper = new SessionUniverseMigrateHelper();
            }
            sessionUniverseMigrateHelper.migrate();
        }
    }

    private boolean isViewAttatched() {
        return mIView != null && mIView.get() != null;
    }

    //<editor-fold desc="ActiveUser Tasks">
    @Override
    public void refreshActiveUser() {
        long lastTime = KV.getUserLong(SPKeys.User.ActiveUser.KEY_LAST_REFRESH_ACTIVE_USER, 0L);
        long interval = KV.getUserLong(SPKeys.User.ActiveUser.KEY_REFRESH_ACTIVE_USER_CONFIG, 3 * 60L);

        long timeOffset = System.currentTimeMillis() - lastTime;
        if (isFirstStart || timeOffset > interval * 1000) {
            MomoTaskExecutor.executeUserTask(LoadNoticeTaskTag, new RefreshActiveUserTask());
            isFirstStart = false;
        } else if (isNeedRefreshActiveUser) {
            MomoTaskExecutor.executeUserTask(LoadNoticeTaskTag, new RefreshActiveUserTask());
            isNeedRefreshActiveUser = false;
        }
    }

    @Override
    public void setNeedRefreshActiveUser(boolean isNeed) {
        isNeedRefreshActiveUser = isNeed;
    }

    @Override
    public void hideActiveUser() {
        MomoTaskExecutor.executeUserTask(LoadNoticeTaskTag, new HideActiveUseTask());
    }

    @Override
    public void removeActiveUser(ActiveUser user) {
        MomoTaskExecutor.executeUserTask(LoadNoticeTaskTag, new RemoveActiveUserTask(user));
    }

    @Override
    public void exposeActiveUser(Pair<Boolean, String> params) {
        if (StringUtils.notEmpty(params.second)) {
            MomoTaskExecutor.executeUserTask(OtherTaskTag, new ExposeActiveUserTask(params.second));
        }
    }

    RefreshActiveUserTask refreshActiveUserTask;

    private class RefreshActiveUserTask extends MomoTaskExecutor.Task<Object, Object, SessionActiveUser> {

        public RefreshActiveUserTask() {
        }

        @Override
        protected void onPreTask() {
            super.onPreTask();
            if (refreshActiveUserTask != null) {
                refreshActiveUserTask.cancel(true);
            }
            refreshActiveUserTask = this;
        }

        @Override
        protected SessionActiveUser executeTask(Object... params) throws Exception {
            int totalCount = -1;
            int coreCount = -1;
            SessionCountBean bean = SessionManager.get().getSpecialCount();
            if (bean != null) {
                totalCount = bean.getAllSessionCount();
                coreCount = bean.getChatSessionCount();
            }
            return activeUserRepository.getActiveUserSession(totalCount, coreCount);
        }

        @Override
        protected void onTaskSuccess(SessionActiveUser o) {
            super.onTaskSuccess(o);
            ActiveUserDataInstance.INSTANCE.setMoreButton(o.moreButton);
            if (o.spaceBean != null) {
                setSpaceViewData(o.spaceBean);
            }
        }

        @Override
        protected void onTaskError(Exception e) {
            //            super.onTaskError(e);
            MDLog.d(LogTag.Message.Message, "onTaskError");
        }
    }

    @Override
    public void doActiveUserClicked(ActiveUser activeUser) {
        try {
            if (activeUser != null && !StringUtils.isEmpty(activeUser.getAction())) {
                ActivityHandler.executeAction(activeUser.getAction(), getView().getBaseActivity());

                if (activeUser.getType() != TYPE_NOT_DISMISS) { // 点击之后就清除本地的单个数据
                    MomoTaskExecutor.executeUserTask(LoadNoticeTaskTag, new InactiveUserTask(activeUser));
                }

            }
        } catch (Exception ex) {
            log.e(ex);
        }
    }

    private void setSpaceViewData(SessionSpaceBean bean) {
        int intervalTime = SessionSpaceConfigV2Getter.get().showInterval();
        long lastTime = KV.getUserLong(SPKeys.User.SessionSpace.KEY_CARD_SHOW_LAST_TIME, 0L);
        int type = KV.getUserInt(SPKeys.User.SessionSpace.KEY_CARD_DISMISS_TYPE, -1);
        int exposureIntervalTime = SessionSpaceConfigV2Getter.get().exposureInterval();
        if (lastTime == 0) {
            getView().showSpaceView(bean);
        } else {
            if (type == 1) {
                if (System.currentTimeMillis() - lastTime > exposureIntervalTime * spaceIntervalUnit) {
                    getView().showSpaceView(bean);
                }
            } else if (type == 2) {
                if ((System.currentTimeMillis() - lastTime > intervalTime * spaceIntervalUnit) || SessionSpaceConfigV2Getter.get().leaveClose() == 0) {
                    getView().showSpaceView(bean);
                }
            } else {
                getView().showSpaceView(bean);
            }
        }
    }

    private class InactiveUserTask extends MomoTaskExecutor.Task<Object, Object, Boolean> {
        private ActiveUser activeUser;

        public InactiveUserTask(ActiveUser activeUser) {
            this.activeUser = activeUser;
        }

        @Override
        protected Boolean executeTask(Object... params) throws Exception {
            boolean isEmpty = false;
            if (activeUser != null) {
                activeUserRepository.inActiveOneUser(activeUser);

                // 检测是否队列为空
                isEmpty = activeUserRepository.isCacheEmpty();
            }
            return isEmpty;
        }

        @Override
        protected void onTaskSuccess(Boolean o) {
            super.onTaskSuccess(o);
        }

        @Override
        protected void onTaskError(Exception e) {
        }
    }

    private class RemoveActiveUserTask extends MomoTaskExecutor.Task<Object, Object, String> {
        private ActiveUser activeUser;

        public RemoveActiveUserTask(ActiveUser activeUser) {
            this.activeUser = activeUser;
        }

        @Override
        protected String executeTask(Object... params) throws Exception {
            if (activeUser != null) {
                activeUserRepository.removeActiveOneUser(activeUser);
            }
            return "";
        }

        @Override
        protected void onTaskSuccess(String o) {
            super.onTaskSuccess(o);
            if (activeUser != null) {
                Toaster.show("屏蔽成功");
            }
        }

        @Override
        protected void onTaskError(Exception e) {
            Toaster.show("屏蔽失败，请稍后重试");
        }
    }

    private class HideActiveUseTask extends MomoTaskExecutor.Task<Object, Object, Object> {

        @Override
        protected Object executeTask(Object... params) throws Exception {
            UserApi.getInstance().hideActiveUser();
            activeUserRepository.clear();
            return null;
        }

        @Override
        protected void onTaskSuccess(Object o) {
            super.onTaskSuccess(o);
        }
    }

    private static class ExposeActiveUserTask extends MomoTaskExecutor.Task<Object, Object, Object> {
        private final String idListStr;

        public ExposeActiveUserTask(String idListStr) {
            this.idListStr = idListStr;
        }

        @Override
        protected Object executeTask(Object... objects) throws Exception {
            SessionApi.getInstance().exposeActiveUser(idListStr);
            return null;
        }

        @Override
        protected void onTaskError(Exception e) {
            // Note 不需要展示错误提示
        }
    }
    //</editor-fold>

    private class getPushSwitchTipsInfoTask extends MomoTaskExecutor.Task<Object, Object, PushSwitchTipsInfo> {

        public getPushSwitchTipsInfoTask() {
        }

        @Override
        protected PushSwitchTipsInfo executeTask(Object... objects) throws Exception {
            return UserApi.getInstance().getPushSwitchTipsInfo();
        }

        @Override
        protected void onTaskSuccess(PushSwitchTipsInfo tipsInfo) {
            super.onTaskSuccess(tipsInfo);
            if (tipsInfo == null) {
                tipInfoFial();
                return;
            }
            if (getView() != null) {
                getView().onGetPushSwitchTipsInfoSuccess(tipsInfo);
            }
        }

        @Override
        protected void onTaskError(Exception e) {
            tipInfoFial();
        }

        private void tipInfoFial() {
            if (getView() != null) {
                getView().onGetPushSwitchTipsInfoFail();
            }
        }
    }

    private class getPushSwitchDialogInfoTask extends MomoTaskExecutor.Task<Object, Object, PushSwitchTipsInfo> {

        public getPushSwitchDialogInfoTask() {
        }

        @Override
        protected PushSwitchTipsInfo executeTask(Object... objects) throws Exception {
            return UserApi.getInstance().getPushSwitchTipsInfo();
        }

        @Override
        protected void onTaskSuccess(PushSwitchTipsInfo tipsInfo) {
            super.onTaskSuccess(tipsInfo);
            if (tipsInfo == null) {
                return;
            }
            if (getView() != null) {
                getView().onGetPushSwitchDialogInfoSuccess(tipsInfo);
            }
        }

        @Override
        protected void onTaskError(Exception e) {
        }
    }


    interface ContactNoticeUnreadCountCallback {
        void onContactNoticeUnreadCount(int count);
    }


    //<editor-fold desc="InteractionNotice 互动通知相关">

    /**
     * 获取互动通知消息数据
     */
    public void loadInteractionData() {
        updateInteractionNoticeData();
    }

    @Override
    public void showOrHideBottomTips(Boolean isShow) {
        if (getView() != null) {
            getView().showOrHideBottomTips(isShow);
        }
    }

    /**
     * 更新互动通知数据
     * 页面初始化或互动通知设置变化或接收到新IM消息时调用
     */
    private void updateInteractionNoticeData() {
        String text = KV.getUserStr(
                SPKeys.User.NoticeMsg.KEY_INTERACTION_NOTICE_TEXT,
                getContext() == null ? "暂无新通知消息" : getContext().getString(R.string.session_notice_desc_default)
        );
        unReadNoticeCount = KV.getUserInt(SPKeys.User.NoticeMsg.KEY_INTERACTION_NOTICE_UNREAD_COUNT, 0);
        int gotoTabType = KV.getUserInt(SPKeys.User.NoticeMsg.KEY_INTERACTION_NOTICE_TAB, 0);
        if (sessionListVm != null) {
            sessionListVm.updateNoticeHeader(unReadNoticeCount, text, gotoTabType);
        }
        //更新页签未读数
        refreshUnreadMessageCount();
    }

    /**
     * 跳转至互动通知页面
     */
    public void gotoInteractionAc(int gotoTabIndex) {
        Context context = getContext();
        if (context != null) {
            toInteractActivity(context, gotoTabIndex);
        }
    }

    /**
     * 设置未读数为0，调用已读接口
     * 进入页面、拖拽气泡
     *
     * @param readOnly 是否仅更新已读数
     */
    private void readAllInteractionNotice(boolean readOnly) {
        //调用已读接口
        MomoTaskExecutor.executeUserTask(OtherTaskTag, new UpdateReadTask());
        //更新本地未读数
        KV.saveUserValue(SPKeys.User.NoticeMsg.KEY_INTERACTION_NOTICE_UNREAD_COUNT, 0);
        if (readOnly) {
            return;
        }
        unReadNoticeCount = 0;
        String text = KV.getUserStr(SPKeys.User.NoticeMsg.KEY_INTERACTION_NOTICE_TEXT, getContext().getString(R.string.session_notice_title));
        int gotoTabType = KV.getUserInt(SPKeys.User.NoticeMsg.KEY_INTERACTION_NOTICE_TAB, 0);
        if (sessionListVm != null) {
            sessionListVm.updateNoticeHeader(unReadNoticeCount, text, gotoTabType);
        }
        refreshUnreadMessageCount();
    }

    /**
     * 互动通知已读任务
     */
    private class UpdateReadTask extends MomoTaskExecutor.Task<Object, Object, Boolean> {

        @Override
        protected Boolean executeTask(Object... objects) throws Exception {
            boolean success = InteractionNoticeApi.Companion.updateRead();
            return success;
        }

        @Override
        protected void onTaskError(Exception e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
            //            super.onTaskError(e);
        }

        @Override
        protected void onTaskSuccess(Boolean success) {
            super.onTaskSuccess(success);
        }
    }

    //</editor-fold>
}
