package com.immomo.momo.maintab.session2

import com.immomo.annotations.appconfig.AppConfigField
import com.immomo.annotations.appconfig.appconfigv1.AppConfigV1
import com.immomo.momo.maintab.session2.apt.SessionTopOperatorAppConfigV1Getter

@AppConfigV1
object SessionTopOperatorAppConfigV1 {

    @AppConfigField(
        mark = "4000117",
        key = "enable",
        defValue = "1",
        isSysValue = false
    )
    var enable: Int = 1

    @JvmStatic
    fun isEnable() = (SessionTopOperatorAppConfigV1Getter.get().enable() == 1) && SessionTopOperatorCloseAppConfigV2.isCloseFunc()

}

