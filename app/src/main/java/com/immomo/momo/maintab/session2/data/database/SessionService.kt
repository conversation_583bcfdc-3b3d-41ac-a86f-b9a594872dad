package com.immomo.momo.maintab.session2.data.database

import androidx.core.database.getIntOrNull
import com.cosmos.mdlog.MDLog
import com.immomo.framework.common.UniqueHash
import com.immomo.framework.storage.kv.KV
import com.immomo.momo.MomoKit
import com.immomo.momo.greendao.AppDBUtils
import com.immomo.momo.greendao.SessionEntityDao
import com.immomo.momo.maintab.session2.SessionDefinitionManager
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.data.manager.SessionMetadata
import com.immomo.momo.maintab.session2.data.manager.UnreadCountMessageInterceptor
import com.immomo.momo.maintab.session2.data.mapper.toMetadata
import com.immomo.momo.maintab.session2.data.util.SessionTabTestHelper
import com.immomo.momo.maintab.session2.defs.UserChatSessionDefinition
import com.immomo.momo.maintab.sessionlist.util.DelMsgIDsManager
import com.immomo.momo.service.bean.FolderType
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.sessions.SessionDao
import org.greenrobot.greendao.internal.SqlUtils
import org.greenrobot.greendao.query.WhereCondition
import java.util.Collections
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.absoluteValue

class SessionService {
    private val oldDb
        get() = MomoKit.getApp().sqliteInstance

    private val sessionEntityDao
        get() = AppDBUtils.getInstance().getDao(SessionEntity::class.java) as? SessionEntityDao
            ?: throw IllegalStateException("failed to get session entity dao")

    private val unreadCountEntityDao by lazy {
        UnreadCountEntityDao(
            oldDb ?: throw IllegalStateException("failed to oldDb")
        )
    }

    fun getSession(sessionKey: String) = getSession(SessionKey.fromString(sessionKey))

    fun getSession(sessionKey: SessionKey): SessionEntity? {
        val session = sessionEntityDao.load(sessionKey.value)
        if (session != null) {
            session.setInCache(SessionEntity.CACHE_DATABASE)
            return session
        }

        return SessionDefinitionManager.fetchOldSession(sessionKey)
    }

    fun loadAllKeys(): MutableMap<String, SessionMetadata> {
        val map = sessionEntityDao.queryBuilder()
            .where(SessionEntityDao.Properties.MarkAsDeleted.notEq(true))
            .build().forCurrentThread()
            .list()
            .filter {
                !it.sessionKey.startsWith("l_")
            }
            .filter {
                !DelMsgIDsManager.getInstance().containsSession(it.sessionId)
            }
            .map {
                it.sessionKey to it.toMetadata()
            }.toMap().toMutableMap()

        val sql = SqlUtils.createSqlSelect(
            SessionDao.TABLE_NAME,
            "T",
            arrayOf(
                Session.DBFIELD_SESSIONID, Session.DBFIELD_TYPE,
                Session.DBFIELD_FOLDER, Session.DBFIELD_RECOMMEND_TIME, Session.DBFIELD_ORDERID,Session.DBFIELD_UNREADEDCOUNT
            ),
            false
        )
        oldDb?.rawQuery(sql, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                do {
                    val sessionId = cursor.getString(0)
                    val sessionType = cursor.getInt(1)
                    val foldType = cursor.getIntOrNull(2)
                    val recommendTime = cursor.getLong(3)
                    val time = cursor.getLong(4)
                    val unreadCount = cursor.getInt(5)

                    val translated = SessionDefinitionManager.transformSessionKey(
                        sessionId, sessionType
                    )?.value ?: continue
                    if (translated !in map) {
                        val key = SessionKey.fromString(translated)
                        if (key != SessionKey.INVALID) {
                            map[translated] = SessionMetadata(
                                key.value,
                                key.type,
                                key.id,
                                foldType = foldType,
                                foldV3 = 0,
                                recommendTime = recommendTime,
                                orderId = time,
                                markedAsDeleted = false,
                                lastMsgUpdateTime = 0L,
                                unreadCount = UnreadCountMessageInterceptor.getCount(key),
                                silenceCount = UnreadCountMessageInterceptor.getSilentCount(key)
                            )
                        }
                    }
                } while (cursor.moveToNext())
            }
        }
        return map
    }

    fun saveSession(sessionEntity: SessionEntity) {
        sessionEntity.contentStr = SessionDefinitionManager.serialize(sessionEntity)
        sessionEntityDao.insertOrReplace(sessionEntity)

        try {
            SessionDefinitionManager.saveOldSession(sessionEntity)
        } catch (e: Exception) {
            MDLog.d("yeyc---", "saveSession error-----:$e")
        }
    }

    fun deleteSession(session: SessionEntity) {
        sessionEntityDao.deleteByKey(session.sessionKey)

        SessionDefinitionManager.removeOldSession(SessionKey.fromString(session.sessionKey))
    }

    //<editor-fold desc="Session Unreliable">

    private val unreliableSessionSet =
        UnreliableSessionSet(KEY_UNRELIABLE_SESSION_LIST, sparseLocate = true)

    fun removeUnreliable(sessionKeyStr: String) {
        unreliableSessionSet.remove(sessionKeyStr)
    }

    fun addUnreliable(sessionKey: SessionKey) {
        unreliableSessionSet.add(sessionKey)
    }

    fun isUnreliable(sessionKeyStr: String) =
        unreliableSessionSet.isUnreliable(sessionKeyStr)

    //</editor-fold>

    //<editor-fold desc="Unread Count">

    fun findUnread(sessionKey: SessionKey, isSilent: Boolean) = try {
        unreadCountEntityDao.get(
            UnreadCountEntity.key(
                sessionKey.value,
                if (isSilent) UnreadCountEntity.TYPE_SILENT_UNREAD else UnreadCountEntity.TYPE_UNREAD
            )
        )
    } catch (e: Exception) {
        null
    }

    fun saveUnread(
        sessionKey: SessionKey,
        count: Int,
        isSilent: Boolean,
        maxMessageTimestamp: Long
    ) {
        //silent不做存储
        if (isSilent) return

        var fromDb = true
        val entity = findUnread(sessionKey, isSilent)
            ?: UnreadCountEntity(
                sessionKey.value,
                if (isSilent) UnreadCountEntity.TYPE_SILENT_UNREAD else UnreadCountEntity.TYPE_UNREAD
            ).also { fromDb = false }

        entity.sessionId = sessionKey.id
        entity.sessionType = sessionKey.type
        var isChanged = false
        if (entity.count != count) {
            isChanged = true
            entity.count = count
        }
        if (entity.maxMessageTimestamp != maxMessageTimestamp) {
            isChanged = true
            entity.maxMessageTimestamp = maxMessageTimestamp
        }
        if (!fromDb) {
            unreadCountEntityDao.insertOrReplace(entity)
        } else if (isChanged) {
            unreadCountEntityDao.update(entity)
        }
        removeUnreliableCount(sessionKey, isSilent)
    }

    private val unreliableUnreadCountSet =
        UnreliableSessionSet(KEY_UNRELIABLE_UNREAD_COUNT_LIST)

    private fun removeUnreliableCount(sessionKey: SessionKey, isSilent: Boolean) {
        if (isSilent) {
            //ignore
        } else {
            unreliableUnreadCountSet.remove(sessionKey)
        }
    }

    fun addUnreliableCount(sessionKey: SessionKey, isSilent: Boolean) {
        if (isSilent) {
            //ignore
        } else {
            unreliableUnreadCountSet.add(sessionKey)
        }
    }

    fun isUnreliableCount(sessionKey: SessionKey, isSilent: Boolean) =
        if (isSilent) {
            //ignore
            true
        } else {
            unreliableUnreadCountSet.isUnreliable(sessionKey)
        }
    //</editor-fold>

    //<editor-fold desc="Business Part">

    fun countUnreadByType(
        sessionType: String,
        oldList: List<Session?>,
        withoutFold: Boolean = false
    ): Int {
        val defaultConditions = arrayOf<WhereCondition>(
            SessionEntityDao.Properties.MarkAsDeleted.notEq(true)
        )
        val moreConditions : Array<WhereCondition> = if (withoutFold) {
            defaultConditions.plus(SessionEntityDao.Properties.FoldType.eq(FolderType.Default))
        } else {
            defaultConditions
        }
        val map = sessionEntityDao.queryBuilder()
            .where(SessionEntityDao.Properties.SessionType.eq(sessionType), *moreConditions)
            .build().forCurrentThread()
            .list().map {
                it.sessionId to it.unreadMessageCount
            }.toMap().toMutableMap()
        oldList.forEach { session ->
            if (session != null && session.sessionId !in map) {
                map[session.sessionId] = session.unreadMessageCount
            }
        }
        return map.values.sum()
    }

    fun findKliaoMatchSession(orderId: Long, count: Int, oldList: List<Session?>): List<String> {
        val sessionKeys = sessionEntityDao.queryBuilder().where(
            SessionEntityDao.Properties.MarkAsDeleted.notEq(true),
            SessionEntityDao.Properties.SessionType.eq(UserChatSessionDefinition.Type),
            SessionEntityDao.Properties.ContentIdx1.eq(Session.BUSINESS_KLIAO_MATCH),
            SessionEntityDao.Properties.OrderId.lt(orderId)
        ).build().forCurrentThread().list()
            .map { it.sessionKey }.toMutableSet()

        sessionKeys.addAll(oldList.mapNotNull {
            if (it?.type == Session.TYPE_CHAT) it.sessionId else null
        })
        return sessionKeys.toList()
    }

    fun markTopFoldSession(filter: (SessionEntity) -> Boolean) {
        val list = sessionEntityDao.queryBuilder().list()
        list.asSequence()
            .forEach {
                it.foldTypeV3 = if (filter(it)) SessionTabTestHelper.FOLD_TYPE_TOP_OPEN else 0
                sessionEntityDao.update(it)
            }

    }

    //</editor-fold>

    /**
     * [kvKey]: 存储的kv值
     * [sparseLocate]: 是否分散存储
     */
    class UnreliableSessionSet(
        private val kvKey: String,
        private val sparseLocate: Boolean = false
    ) {
        private val sessionSet = Collections.newSetFromMap(ConcurrentHashMap<String, Boolean>())
        private val groupedSessionSet = ConcurrentHashMap<Int, MutableSet<String>>().also {
            (0 until SECTION_SIZE).forEach { key ->
                it[key.toInt()] = Collections.newSetFromMap(ConcurrentHashMap<String, Boolean>())
            }
        }

        init {
            if (sparseLocate) {
                groupedSessionSet.forEach { (idx, set) ->
                    try {
                        val unreliableSessionList = KV.getUserStr("${kvKey}_$idx", "")
                        set.addAll(unreliableSessionList.splitToSequence(",")
                            .filter { key -> key.isNotBlank() }
                            .toHashSet())
                        sessionSet.addAll(set)
                    } catch (ignored: Exception) {
                    }
                }
            } else {
                try {
                    val unreliableSessionList = KV.getUserStr(kvKey, "")
                    sessionSet.addAll(unreliableSessionList.splitToSequence(",")
                        .filter { key -> key.isNotBlank() }
                        .toHashSet())
                } catch (ignored: Exception) {
                }
            }
        }

        fun isUnreliable(sessionKey: SessionKey) = isUnreliable(sessionKey.value)

        @Synchronized
        fun isUnreliable(sessionKeyStr: String) = sessionKeyStr in sessionSet

        /**
         * 忽略UniqueHash计算出负数的情况
         */
        private fun hashedIdx(sessionKeyStr: String) =
            UniqueHash.id(sessionKeyStr).absoluteValue.and(SECTION_MASK).toInt()

        fun add(sessionKey: SessionKey) {
            add(sessionKey.value)
        }

        @Synchronized
        fun add(sessionKeyStr: String) {
            if (sessionKeyStr in sessionSet) return

            sessionSet.add(sessionKeyStr)
            if (sparseLocate) {
                val idx = hashedIdx(sessionKeyStr)
                groupedSessionSet[idx]?.let {
                    it.add(sessionKeyStr)
                    KV.saveUserValue("${kvKey}_$idx", it.joinToString(","))
                }
            } else {
                KV.saveUserValue(kvKey, sessionSet.joinToString(","))
            }
        }

        fun remove(sessionKey: SessionKey) {
            remove(sessionKey.value)
        }

        @Synchronized
        fun remove(sessionKeyStr: String) {
            if (sessionKeyStr !in sessionSet) return

            sessionSet.remove(sessionKeyStr)
            if (sparseLocate) {
                val idx = hashedIdx(sessionKeyStr)
                groupedSessionSet[idx]?.let {
                    it.remove(sessionKeyStr)
                    KV.saveUserValue("${kvKey}_$idx", it.joinToString(","))
                }
            } else {
                KV.saveUserValue(kvKey, sessionSet.joinToString(","))
            }
        }

        companion object {
            private const val SECTION_SIZE = 16L
            private const val SECTION_MASK = SECTION_SIZE - 1
        }
    }

    companion object {
        private const val KEY_UNRELIABLE_SESSION_LIST =
            "KEY_UNRELIABLE_SESSION_LIST"
        private const val KEY_UNRELIABLE_UNREAD_COUNT_LIST =
            "KEY_UNRELIABLE_UNREAD_COUNT_LIST"
        private const val KEY_UNRELIABLE_SILENT_UNREAD_COUNT_LIST =
            "KEY_UNRELIABLE_SILENT_UNREAD_COUNT_LIST"
    }
}