package com.immomo.momo.maintab.sessionlist.itemmodel

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.graphics.Typeface
import android.view.View
import android.view.animation.AnticipateOvershootInterpolator
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.doOnEnd
import com.cosmos.mdlog.MDLog
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.cement.CementAdapter
import com.immomo.framework.cement.CementViewHolder
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.kotlin.ImageType
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.R
import com.immomo.momo.maintab.session2.domain.topoperator.SessionTopOperatorManager
import com.immomo.momo.maintab.sessionlist.bean.CardDescData
import com.immomo.momo.maintab.sessionlist.bean.CardItemData
import com.immomo.momo.maintab.sessionlist.bean.CardTitleData
import com.immomo.momo.maintab.sessionlist.itemmodel.base.BaseSessionTopOperatorChildModel
import com.immomo.momo.maintab.view.AlphaBannerTextContainer
import com.immomo.momo.util.ColorUtils
import com.immomo.momo.util.MomoKit
import com.immomo.svgaplayer.SVGAAnimListenerAdapter
import com.immomo.svgaplayer.view.MomoSVGAImageView

open class SessionTopOperatorChildItemModel(
    itemModeOriginData: CardItemData,
    isRunGuideAnimation: Boolean = false, // 展示引导动画
    open val onGuideAnimationFinished: () -> Unit
) : BaseSessionTopOperatorChildModel<SessionTopOperatorChildItemModel.ViewHolder>(
    itemModeOriginData,
    isRunGuideAnimation
) {

    private val animationDelay = 100L

    private var curBgLoopCount: Int = 0 // 背景当前轮询总数
    private var curIconLoopCount: Int = 0 // 背景当前轮询总数

    private var runHintAnimationFilterAttach = true

    init {
    }

    open var curViewHolder: ViewHolder? = null

    private var isDetachFromWindow = false

    override fun bindData(holder: ViewHolder) {
        super.bindData(holder)
        MDLog.i(SessionTopOperatorManager.TAG, "bindData=${itemModeData.bizKey}")
        curViewHolder = holder
        kotlin.runCatching {
            if (runGuideAnimation) {
                holder.itemView.alpha = 0f
                refreshHolderView(holder, true)
                startGuidAnimation(holder) { // 有引导结束先放抖动动画
                    onGuideAnimationFinished.invoke()
                    startShackAnimation(holder) { // 延时两秒开始，抖动动画开始再放扫光和icon动画
                        refreshHolderView(holder)
                    }
                }
            } else {
                refreshHolderView(holder)
                startShackAnimation(holder)
            }
        }
    }

    override fun refreshHolderView() {
        curViewHolder?.also { refreshHolderView(it) }
    }

    open fun refreshHolderView(holder: ViewHolder, isPreloadContent: Boolean = false) {
        if (MomoKit.isDarkMode(holder.itemBgIcon.context)) {
            holder.itemBgIcon.setAlpha(0.1f)
            holder.itemBgSvga.setAlpha(0.1f)
        } else {
            holder.itemBgIcon.setAlpha(1f)
            holder.itemBgSvga.setAlpha(1f)
        }
        kotlin.runCatching {
            itemModeData.bgImg?.also { // 背景数据
                holder.cardContainer.visibility = View.VISIBLE
                val animatedImg = it.animatedImg.safe()
                val staticImg = it.staticImg.safe()
                val loopCount = it.loopCount // 循环总数
                if (loopCount != 0 && animatedImg.isNotBlank()) { // svga
                    showStaticIcon(holder.itemBgIcon, staticImg)
                    var needBgLoopCnt = 0
                    if (loopCount > 0) { // 按剩余次数播放
                        val remainBgLoopCnt = loopCount - curBgLoopCount
                        if (remainBgLoopCnt > 0) {
                            needBgLoopCnt = remainBgLoopCnt
                        }
                    } else { // 无限轮播
                        needBgLoopCnt = loopCount
                    }
                    holder.itemBgSvga.stopAnimCompletely()
                    MDLog.i(SessionTopOperatorManager.TAG, "bg needBgLoopCnt=$needBgLoopCnt")
                    if (!isPreloadContent && needBgLoopCnt != 0) {
                        holder.itemBgSvga.startSVGAAnimWithListener(animatedImg, needBgLoopCnt,
                            object : SVGAAnimListenerAdapter() {

                                override fun onStart() {
                                    super.onStart()
                                    holder.itemBgIcon.visibility = View.INVISIBLE
                                    holder.itemBgSvga.visibility = View.VISIBLE
                                }

                                override fun onRepeat() {
                                    super.onRepeat()
                                    curBgLoopCount++
                                }

                                override fun onFinished() {
                                    super.onFinished()
                                    if (curBgLoopCount == loopCount - 1) {
                                        curBgLoopCount = loopCount
                                    }
                                    showStaticIcon(holder.itemBgIcon, staticImg)
                                }
                            })
                    }
                } else if (staticImg.isNotBlank()) {
                    showStaticIcon(holder.itemBgIcon, staticImg)
                }
            } ?: onBgImageEmpty(holder)
            itemModeData.icon?.also { // 左边的图标
                holder.itemIcon.visibility = View.VISIBLE
                val animatedImg = it.animatedImg.safe()
                val staticImg = it.staticImg.safe()
                val loopCount = it.loopCount
                if (loopCount != 0 && animatedImg.isNotBlank()) { // svga
                    showStaticIcon(holder.itemIcon, staticImg)
                    var needIconLoopCnt = 0
                    if (loopCount > 0) { // 按剩余次数播放
                        val remainBgLoopCnt = loopCount - curIconLoopCount
                        if (remainBgLoopCnt > 0) {
                            needIconLoopCnt = remainBgLoopCnt
                        }
                    } else { // 无限轮播
                        needIconLoopCnt = loopCount
                    }
                    MDLog.i(SessionTopOperatorManager.TAG, "icon needIconLoopCnt=$needIconLoopCnt")
                    holder.itemSvga.stopAnimCompletely()
                    if (!isPreloadContent && needIconLoopCnt != 0) {
                        holder.itemSvga.startSVGAAnimWithListener(animatedImg, needIconLoopCnt,
                            object : SVGAAnimListenerAdapter() {
                                override fun onStart() {
                                    super.onStart()
                                    holder.itemIcon.visibility = View.INVISIBLE
                                    holder.itemSvga.visibility = View.VISIBLE
                                }

                                override fun onRepeat() {
                                    super.onRepeat()
                                    curIconLoopCount++
                                }

                                override fun onFinished() {
                                    super.onFinished()
                                    if (curIconLoopCount == loopCount - 1) {
                                        curIconLoopCount = loopCount
                                    }
                                    showStaticIcon(holder.itemIcon, staticImg)
                                }
                            })
                    }
                } else if (staticImg.isNotBlank()) {
                    showStaticIcon(holder.itemIcon, staticImg)
                }
            } ?: onIconEmpty(holder)
            itemModeData.title?.also { // 标题
                setTitle(holder.itemTitle, it)
            } ?: onTitleEmpty(holder)
            refreshDescAnimationContent(holder.itemSubtitle, itemModeData.desc, isPreloadContent)
            isDetachFromWindow = false
        }
    }

    protected fun setTitle(
        itemTitle: TextView,
        it: CardTitleData?
    ) {
        if (it == null) {
            itemTitle.visibility = View.GONE
            return
        }
        itemTitle.visibility = View.VISIBLE
        itemTitle.text = it.text.safe()
        if (it.bold == 1) {
            val typeface = Typeface.create(Typeface.DEFAULT, Typeface.BOLD)
            itemTitle.typeface = typeface
        } else {
            val typeface = Typeface.create(Typeface.DEFAULT, Typeface.NORMAL)
            itemTitle.typeface = typeface
        }
        val textColor = ColorUtils.parseColor(
            if (MomoKit.isDarkMode(itemTitle.context)) it.darkColor.safe() else it.color.safe(),
            R.color.color_323333_to_80f
        )
        itemTitle.setTextColor(textColor)
    }

    protected fun refreshDescAnimationContent(descTextContainer: AlphaBannerTextContainer,descTextList:List<CardDescData>?, useSingleFirst: Boolean = false) {
        descTextList?.also { // 副标题
            descTextContainer.visibility = View.VISIBLE
            var cardDescDataList = it.safe()
            if (useSingleFirst) {
                val cardDescData = it.firstOrNull()
                if (cardDescData != null) {
                    cardDescDataList = arrayListOf(cardDescData)
                }
            }
            descTextContainer.setCardDescDatas(cardDescDataList)
        } ?: onDescEmpty(descTextContainer)
    }

    private fun showStaticIcon(staticImgView: ImageView, imgUrl: String?) {
        if (imgUrl.safe().isNotBlank()) {
            ImageLoader.load(imgUrl)
                .imageType(ImageType.URL)
                .into(staticImgView)
            staticImgView.visibility = View.VISIBLE
        }
    }

    /**
     * 当副标题数据为空时
     */
    private fun onDescEmpty(descTextContainer: AlphaBannerTextContainer) {
        descTextContainer.visibility = View.GONE
    }

    /**
     * 当标题数据为空时
     */
    private fun onTitleEmpty(holder: ViewHolder) {
        holder.itemSubtitle.visibility = View.INVISIBLE
    }

    /**
     * 当图标不可见时
     */
    private fun onIconEmpty(holder: ViewHolder) {
        holder.itemIcon.visibility = View.INVISIBLE
    }

    /**
     * 当背景为空的情况
     */
    private fun onBgImageEmpty(holder: ViewHolder) {
        holder.cardContainer.visibility = View.INVISIBLE
    }

    open fun startShackAnimation(holder: ViewHolder, onAnimationStart: (() -> Unit)? = null) {

    }

    override fun getLayoutRes() = R.layout.listitem_session_top_operator_child_item_model

    override fun getViewHolderCreator(): CementAdapter.IViewHolderCreator<ViewHolder> {
        return CementAdapter.IViewHolderCreator { itemView: View -> ViewHolder(itemView) }
    }

    override fun onFragmentVisible(isResume: Boolean) {
        curViewHolder?.also {
            MDLog.i(
                SessionTopOperatorManager.TAG,
                "SessionTopOperatorChildItemModel isResume=$isResume  ${itemModeData.bizKey}"
            )
            if (isResume) {
                refreshHolderView(it)
            } else {
                releaseAnimation(it)
            }
        }
    }

    private fun releaseAnimation(it: ViewHolder) {
        if (it.itemSvga.visibility == View.VISIBLE) {
            it.itemSvga.stopAnimCompletely()
        }
        if (it.itemBgSvga.visibility == View.VISIBLE) {
            it.itemBgSvga.stopAnimCompletely()
        }
        it.itemSubtitle.clearTextAnimation()
    }

    override fun unbind(holder: ViewHolder) {
        super.unbind(holder)
        MDLog.i(SessionTopOperatorManager.TAG, "unbind=${itemModeData.bizKey}")
        releaseAnimation(holder)
        holder.itemView.clearAnimation()
    }

    override fun attachedToWindow(holder: ViewHolder) {
        super.attachedToWindow(holder)
        MDLog.i(SessionTopOperatorManager.TAG, "attachedToWindow=${itemModeData.bizKey}")
        if (runGuideAnimation && runHintAnimationFilterAttach) {
            MDLog.i(SessionTopOperatorManager.TAG, "过滤了attachedToWindow=${itemModeData.bizKey}")
            return
        }
        runHintAnimationFilterAttach = false
        refreshHolderView(holder)
    }

    override fun detachedFromWindow(holder: ViewHolder) {
        super.detachedFromWindow(holder)
        isDetachFromWindow = true
    }

    private fun startGuidAnimation(holder: ViewHolder, onAnimationEnd: (() -> Unit)? = null) {
        val itemView = holder.itemView
        itemView.clearAnimation()
        val height = UIUtils.getPixels(71f)
        val animatorUp = ObjectAnimator.ofFloat(itemView, "translationY", height.toFloat(), 0f)
        animatorUp.interpolator = AnticipateOvershootInterpolator()
        val animatorAlpha = ObjectAnimator.ofFloat(itemView, "alpha", 0f, 1f)
        animatorAlpha.duration = 1000
        animatorUp.duration = 1500
        // 动画集合
        val animatorSet = AnimatorSet()
        animatorSet.playTogether(animatorUp, animatorAlpha)
        animatorSet.startDelay = holder.layoutPosition * animationDelay
        animatorSet.doOnEnd {
            itemView.visibility = View.VISIBLE
            onAnimationEnd?.invoke()
        }
        animatorSet.start()
    }

    class ViewHolder(itemView: View) : CementViewHolder(itemView) {
        val itemBgSvg2 by lazy { itemView.findViewById<MomoSVGAImageView>(R.id.item_bg_svga_2) }
        val itemTitle2 by lazy { itemView.findViewById<TextView>(R.id.item_title2) }
        val itemSubtitle2 by lazy { itemView.findViewById<AlphaBannerTextContainer>(R.id.item_subtitle2) }

        val cardContainer by lazy { itemView.findViewById<ConstraintLayout>(R.id.card_container) }

        val itemBgIcon by lazy { itemView.findViewById<ImageView>(R.id.item_bg_img) }
        val itemBgSvga by lazy { itemView.findViewById<MomoSVGAImageView>(R.id.item_bg_svga) }

        val itemIcon by lazy { itemView.findViewById<ImageView>(R.id.item_icon) }
        val itemSvga by lazy { itemView.findViewById<MomoSVGAImageView>(R.id.item_svga) }
        val itemTitle by lazy { itemView.findViewById<TextView>(R.id.item_title) }
        val itemSubtitle by lazy { itemView.findViewById<AlphaBannerTextContainer>(R.id.item_subtitle) }
    }
}