package com.immomo.momo.maintab.session2.data.mapper

import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.momo.maintab.session2.SessionDefinitionManager
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.manager.SessionMetadata
import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.SessionModel

internal fun SessionEntity.toBaseInfo() = BaseSessionInfo(
    sessionKey = sessionKey,
    sessionId = sessionId,
    foldType = foldType.safe(0),
    foldTypeV3 = foldTypeV3.safe(0),
    lastMsgId = lastMsgId.safe(),
    lastMessageTime = lastMsgTime.safe(),
    recommendTime = recommendTime,
    orderId = orderId,
    unreadMessageCount = unreadMessageCount,
    silentMessageCount = silentMessageCount,
    lastFetchTime = lastFetchTime,
    sticky = isSticky,
    forceRefreshId = content?.forceRefreshId.safe(0),
    cacheStatus = cacheStatus,
    markedAsDeleted = markAsDeleted
)


internal fun SessionEntity.toModel(): SessionModel? =
    SessionDefinitionManager.entityToModel(this)

fun SessionEntity.toMetadata(): SessionMetadata = SessionMetadata(
    sessionKey,
    sessionType,
    sessionId,
    foldType,
    foldTypeV3.safe(),
    recommendTime,
    orderId,
    markAsDeleted,
    lastMsgTime,
    unreadMessageCount,
    silentMessageCount
)