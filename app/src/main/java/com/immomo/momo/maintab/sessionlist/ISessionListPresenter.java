package com.immomo.momo.maintab.sessionlist;

import android.os.Bundle;
import android.util.Pair;

import androidx.annotation.NonNull;

import com.immomo.momo.maintab.model.ActiveUser;
import com.immomo.momo.maintab.session2.domain.model.SessionModel;
import com.immomo.momo.maintab.session2.presentation.itemmodel.SessionItemModel;

import org.jetbrains.annotations.Nullable;

import java.util.List;

public interface ISessionListPresenter {

    /**
     * 刷新会话资料
     *
     * @param chatType
     * @param dataId
     */
    void refreshSessionProfile(int chatType, String dataId);

    void processBlockListAction(String stringExtra);

    void ignoreAllUnread(boolean needAnimation, boolean showTip);

    void ignoreNotice(boolean updateCount);

    boolean onMessageReceive(Bundle bundle, String action);

    void initData();

    void loadMoreSessions(boolean isInit, boolean clearAll);

    void onViewDestroyed();

    void initContactUnreadCount();

    void addSessionInViewToRefreshListByScroll();

    void refreshContactUnreadCount(int noticeCount);

    void getContactNoticeUnread(SessionListPresenter.ContactNoticeUnreadCountCallback callback);

    void refreshActiveUser();

    void onResume();

    void onPause();

    void onDestroy();

    void hideActiveUser();

    void removeActiveUser(ActiveUser user);

    void exposeActiveUser(Pair<Boolean, String> params);

    void getPushSwitchTipsInfo();

    void getPushSwitchDialogInfo();

    void forceRefreshOnlineStatueNextTime(String sessionId);

    void refreshUnreadMessageCount();

    void pullSessionEnterBar();

    void setNeedRefreshActiveUser(boolean isNeed);

    void requestRecommendSessions();

    void reloadAll();

    void doActiveUserClicked(ActiveUser activeUser);

    void batchRequestOnlineStatue();

    void gotoInteractionAc(int gotoTabIndex);

    void showAlertDialog(@NonNull SessionItemModel.SessionViewHolder viewHolder,
                         int positionInAdapter,
                         @NonNull String[] actionList,
                         boolean dontNeedSubtitle,
                         @NonNull SessionModel model,
                         @Nullable String typeGotoSessionId);

    void showHepaiMsgSwitchDialog(
                              @NonNull SessionModel model,
                              @Nullable String typeGotoSessionId);

    void showSpamMsgSwitchDialog(
            @NonNull SessionModel model,
            @Nullable String typeGotoSessionId);

    void showSearchMenu();

    boolean isSearchMenuShow();

    void loadInteractionData();

    void showOrHideBottomTips(Boolean isShow);

    void migrateSpamSession(boolean init);

    void addMigrateSpamIds(List<String> remoteids);

}
