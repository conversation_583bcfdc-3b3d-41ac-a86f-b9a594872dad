package com.immomo.momo.maintab.session2

import com.immomo.annotations.appconfig.AppConfigField
import com.immomo.annotations.appconfig.appconfigv1.AppConfigV1
import com.immomo.framework.utils.NeedOffline
import com.immomo.momo.maintab.session2.apt.SessionAppConfigV1Getter

@AppConfigV1
object SessionAppConfigV1 {

    @AppConfigField(
        mark = "4000056",
        key = "hitNewOldRelationShip",
        defValue = "0",
        isSysValue = false
    )
    var newBoyTest: Int = 0

    //1为用户命中消息帧实验，0为没有命中
    @AppConfigField(
        mark = "4000083",
        key = "hit_message_exp",
        defValue = "0",
        isSysValue = false
    )
    var messageExp: Int = 0


    //1为命中消息盒子实验，0为没有命中
    @AppConfigField(
        mark = "4000083",
        key = "hit_message_box_exp",
        defValue = "0",
        isSysValue = false
    )
    var messageBoxExp: Int = 0

    @AppConfigField(
        mark = "4000083",
        key = "create_button_switch",
        defValue = "0",
        isSysValue = false
    )
    var buttonSwitch: Int = 0


    @AppConfigField(mark = "4000074", key = "sessionNote", defValue = "0")
    var sessionNote = 0

    /**
     * 敲敲、今日缘分折叠需求 2024-0704
     */
    @AppConfigField(mark = "4000115", key = "hitExp", defValue = "false")
    var sessionFoldOpen = "false"
    /**
     * 敲敲、今日缘分红点需求 2024-1008
     */
    @AppConfigField(mark = "4000115", key = "hitBubbleExp", defValue = "false")
    var silenceBubbleExp = "false"
    /**
     * 敲敲、今日缘分折叠需求 2024-0704
     */
    @AppConfigField(mark = "4000115", key = "dayLimit", defValue = "3")
    var sessionFoldThreshold = 0

    @NeedOffline("需要整体下线")
    fun silenceBubbleExp(): Boolean {//已关闭，全量为false
        return false
    }
}

