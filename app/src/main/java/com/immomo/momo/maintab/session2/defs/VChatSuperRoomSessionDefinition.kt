package com.immomo.momo.maintab.session2.defs

import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.android.router.vchat.VChatConfigRouter
import com.immomo.momo.group.bean.GroupPreference
import com.immomo.momo.maintab.session2.SessionContentParser
import com.immomo.momo.maintab.session2.data.database.ChatContent
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.manager.LastUnreadMessageInterceptor
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.data.manager.UnreadCountMessageInterceptor
import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.type.VChatSuperRoomSessionModel
import com.immomo.momo.maintab.sessionlist.SessionStickyHelper
import com.immomo.momo.message.helper.SessionTextHelper
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.sessions.LastMsgCache
import com.immomo.momo.service.sessions.MessageServiceHelper
import com.immomo.momo.service.sessions.SessionService
import com.immomo.momo.util.StringUtils
import com.squareup.moshi.JsonClass
import info.xudshen.android.appasm.AppAsm
import java.util.*

@JsonClass(generateAdapter = true)
class VChatSuperRoomContent
    : ChatContent("v") {
    var vChatAvatar: String? = null
    var vChatUserAvatar: String? = null
    var vChatName: String? = null

    override fun isChatInfoValid(): Boolean {
        return vChatName?.isNotEmpty() == true
    }

    /**
     * 是否有聊天室心心红包
     */
    var hasVChatHongbao: Boolean = false
}

class VChatSuperRoomSessionDefinition
    : ChatSessionDefinition<VChatSuperRoomContent, VChatSuperRoomSessionModel>(
    "v",
    SessionContentParser.moshiParser()
) {
    override fun createContent(): VChatSuperRoomContent {
        return VChatSuperRoomContent()
    }

    override fun getLastMessage(session: SessionEntity): Message? {
        return null
    }

    override fun updateSessionDescWithLastMessage(
        session: SessionEntity,
        content: VChatSuperRoomContent,
        lastMessage: Message,
        updateProcessedTime: Boolean
    ) {
        super.updateSessionDescWithLastMessage(session, content, lastMessage, updateProcessedTime)

        // update session order params
        if (lastMessage.isUpdateSession && !lastMessage.isImSpam) {
            session.lastMsgId = lastMessage.msgId ?: session.lastMsgId
            if (lastMessage.stopFloat == 0) {
                session.lastMsgTime = lastMessage.timestamp?.time ?: session.lastMsgTime
            }
            content.lastMessageType = lastMessage.contentType
            LastMsgCache.onSendNewMsg(lastMessage)
        }

        if (lastMessage.notShowInSession) {
            content.forcedDesc = lastMessage.recommendReason
        } else {
            content.lastMessageOwnerId = lastMessage.remoteId
            SessionManager.getInfoCache().fetchUser(session, lastMessage.remoteId) { user ->
                //最后一条消息的id不一致
                if (this.content.castOrNull<ChatContent>()?.lastMessageOwnerId
                    != lastMessage.remoteId
                ) return@fetchUser false

                lastMessage.owner = user
                this.content.castOrNull<ChatContent>()?.lastMessageOwnerName =
                    getDisplayName(lastMessage)
                true
            }

            // 关闭了消息提醒，通过最后一条消息内容的位置，提醒未读数量
            if (!AppAsm.getRouter(VChatConfigRouter::class.java)
                    .isPushOpened(session.sessionId) && session.silentMessageCount > 0
            ) {
                content.forcedDesc = session.silentMessageCount.toString() + "条消息未读"
            } else {
                content.forcedDesc = null
            }
            content.onlyShowMessageContent = false
            content.distanceInfo = -1F

            if (lastMessage.contentType == Message.CONTENTTYPE_MESSAGE_HIDE_VCHAT_HONGBAO
                || lastMessage.contentType == Message.CONTENTTYPE_MESSAGE_SHOW_VCHAT_HONGBAO
            ) {
                content.onlyShowMessageContent = true
            } else if (lastMessage.receive
                && lastMessage.contentType != Message.CONTENTTYPE_MESSAGE_NOTICE
                && !StringUtils.isEmpty(lastMessage.remoteId)
            ) {
                content.lastMessageOwnerName =
                    if (lastMessage.vchatMember != null && lastMessage.vchatMember.mysteryFlag == 1) {
                        lastMessage.vchatMember.name + ": "
                    } else getDisplayName(lastMessage)
            } else {
                content.onlyShowMessageContent = false
            }

            content.lastMessageContent = SessionTextHelper.getMessageContent(lastMessage)
        }
    }

    override fun updateSessionIndicatorWithEveryMessage(
        session: SessionEntity,
        content: VChatSuperRoomContent,
        message: Message?
    ) {
        if (message?.status == Message.STATUS_CLOUD) return

        session.unreadMessageCount =
            UnreadCountMessageInterceptor.getCount(key(session.sessionId))
        //理论上有未读消息就不应该有静默消息
        if (session.unreadMessageCount > 0) {
            session.silentMessageCount = 0
        } else {
            session.silentMessageCount =
                UnreadCountMessageInterceptor.getSilentCount(key(session.sessionId))
        }
        // FIX：初次登录时，还没有获取到群组的消息屏蔽开关，屏蔽的群，会有未读消息数量，当获得开关配置后，不再显示未读数，导致气泡数对不上
        // 解决方案：如果发现Session是屏蔽状态，则清空Session的未读数
        if (MessageServiceHelper.getSessionNotificationMode(
                SessionKey.fromString(session.sessionKey)
            ) == GroupPreference.NOTIFICATION_CLOSE
        ) {
            session.unreadMessageCount = 0
        }

        //Gift && momoId 是当前用户时
        content.hasVChatHongbao = LastUnreadMessageInterceptor.getUnread(
            key(session.sessionId),
            Message.CONTENTTYPE_MESSAGE_SHOW_VCHAT_HONGBAO
        )?.isValid() == true
    }

    override fun onReloadChatInfo(id: String, session: SessionEntity, forceReload: Boolean) {
        val content = session.content as? VChatSuperRoomContent ?: return
        content.pendingReloadChatInfo = true
        content.chatId = id
        if (session.orderId == 0L) {
            session.lastFetchTime = System.currentTimeMillis()
        }

        SessionManager.getInfoCache()
            .fetchVChatSuperRoom(session, content.chatId, forceReload) { superRoom ->
                this.content.castOrNull<VChatSuperRoomContent>()?.also {
                    it.pendingReloadChatInfo = false

                    it.vChatName = "聊天室·${superRoom.displayName}"
                    it.vChatAvatar = superRoom.cover

                    if (superRoom.cover?.isEmpty() != true) {
                        SessionManager.getInfoCache()
                            .fetchUser(session, superRoom.founderMomoid) { user ->
                                this.content.castOrNull<VChatSuperRoomContent>()?.also { content2 ->
                                    content2.vChatUserAvatar = user.avatar
                                } != null
                            }
                    }
                } != null
            }

        if (!AppAsm.getRouter(VChatConfigRouter::class.java)
                .isPushOpened(session.sessionId) && session.silentMessageCount > 0
        ) {
            content.forcedDesc = session.silentMessageCount.toString() + "条消息未读"
        } else {
            content.forcedDesc = null
        }
    }

    override fun syncSession(session: SessionEntity, data: Any?): Boolean {
        val content = session.content as? VChatSuperRoomContent ?: return false
        content.chatId = session.sessionId

        session.isSticky =
            SessionStickyHelper.getInstance().getOrderID("v_${session.sessionId}") != -1L

        content.vChatName = content.chatId
        onReloadChatInfo(session.sessionId, session, false)

        return super.syncSession(session, data)
    }

    override fun isTransformCompatible(oldSessionType: Int, oldSessionId: String): String? {
        return if (oldSessionId.startsWith("v")) oldSessionId.removePrefix("v_") else null
    }

    override fun removeOldSession(id: String) {
        SessionService.getInstance().deleteSession("v_$id")
    }

    override fun VChatSuperRoomContent.contentToModel(baseInfo: BaseSessionInfo): VChatSuperRoomSessionModel {
        return VChatSuperRoomSessionModel(
            baseInfo,
            chatId.safe(),
            desc.safe(),
            draftString.safe(),
            draftQuoteString.safe(),
            lastMessageType.safe(0),
            showMessageStatus,
            lastMessageStatus.safe(-1),
            vChatAvatar = vChatAvatar ?: vChatUserAvatar ?: "",
            vChatName = vChatName.safe(),
            hasVChatHongbao = hasVChatHongbao
        )
    }

    companion object {
        const val Type = "v"

        fun key(id: String) = SessionKey(Type, id)
    }
}

