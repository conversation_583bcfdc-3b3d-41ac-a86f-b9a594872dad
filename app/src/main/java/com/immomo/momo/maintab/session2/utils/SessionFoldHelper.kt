package com.immomo.momo.maintab.session2.utils

import android.app.AlertDialog
import android.app.Dialog
import android.content.Context
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.coroutineScope
import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.utils.NeedOffline
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.molive.kotlin.extensions.isNotNullOrEmpty
import com.immomo.momo.android.view.dialog.MAlertDialog
import com.immomo.momo.maintab.session2.SessionAppConfigV1
import com.immomo.momo.maintab.session2.SessionUpdateBundle.ClearUnread
import com.immomo.momo.maintab.session2.apt.SessionAppConfigV1Getter
import com.immomo.momo.maintab.session2.apt.SessionAppConfigV2Getter
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.data.manager.SessionManager.Companion.get
import com.immomo.momo.maintab.session2.data.mapper.toModel
import com.immomo.momo.maintab.session2.defs.FoldSessionParam
import com.immomo.momo.maintab.session2.defs.UserChatContent
import com.immomo.momo.service.bean.FolderType
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.bean.message.Type28Content
import com.immomo.momo.service.sessions.SessionService
import com.immomo.momo.service.sessions.ignoreUnreadCount
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit

/**
 * CREATED BY liu.chong
 * AT 2024/7/4
 */
object SessionFoldHelper {
    const val TAG = "SessionFold-"
    private const val PROMOTE_TODAY_FATE = "今日缘分"
    private const val PROMOTE_TODAY_KNOCK = "来自敲敲"

    private val PROMPT_FOLDED = arrayOf(PROMOTE_TODAY_FATE, PROMOTE_TODAY_KNOCK)
    const val KEY_SCAN_TIME = "key_session_scan_time"
    @NeedOffline
    var isBubbleTest = false
        private set
    /**
     * 是否是折叠消息，目前敲敲、今日缘分为需要折叠的消息
     */
    private fun isFoldMsg(msg: Message): Boolean {
        return msg.messageContent.castOrNull<Type28Content>()?.prompt in PROMPT_FOLDED
    }

    /**
     * 是否需要静默红点。实验组用户，如果是敲敲、今日缘分的消息，就给静默展示红点了
     */
    fun isNeedSilent(msg: Message): Boolean {
        return isBubbleTest && isFoldMsg(msg)
    }

    private fun foldDaysThresholdConfig() =
        SessionAppConfigV1Getter.get().sessionFoldThreshold().takeIf { it > 0 } ?: 3


    private fun shouldFoldByTime(msg: Message): Boolean {
        val threshold = foldDaysThresholdConfig()
        val interval = TimeUnit.DAYS.toMillis(threshold.toLong())
        return System.currentTimeMillis() - msg.timestampMillis > interval
    }


    /**
     * 满足3个条件
     * 1. type是28
     * 2. promt是敲敲、今日缘分
     * 3. 超过n天
     * 另外关于已读，清除了session气泡，即便消息是未读、也认为是已读，所以这里就不判断消息是否是未读了
     */
    fun shouldFold(msg: Message): Boolean {
        if (msg.contentType != Message.CONTENTTYPE_MESSAGE_MGS_GROUP) {
            return false
        }
        if (!isFoldMsg(msg)) {
            return false
        }
        return shouldFoldByTime(msg)
    }

    fun findLastMessageParam(): FoldSessionParam {
        SessionManager.get().findAllFoldSessionIds(
            FolderType.FOLDED_MSG, upToDate = true
        ).asSequence().mapNotNull {
            SessionManager.get().getSession(SessionKey("u", it).value, false).first
        }.filter {
            !it.markAsDeleted
        }.firstOrNull()?.let { entity ->
            val content = entity.content.castOrNull<UserChatContent>()
            return FoldSessionParam(
                type = Session.ID.FOLDED_MSG,
                userName = content?.userName.safe(),
                lastMessageId = entity.lastMsgId,
                lastMsgTime = entity.lastMsgTime,
                message = Message(entity.lastMsgId).also {
                    it.remoteId = entity.sessionId
                    it.setContent(content?.lastMessageOwnerName
                        ?.takeIf { it.isNotNullOrEmpty() }
                        ?.let { "$it:${content.lastMessageContent}" }
                        ?: content?.lastMessageContent.safe()
                    )
                }
            )
        }
        return FoldSessionParam(
            type = Session.ID.FOLDED_MSG,
            userName = null,
            lastMessageId = null,
            lastMsgTime = null,
            message = null
        )
    }

    /**
     * 通知外显cell重新更新最后一条未读
     * 方式是，指定cell的id，但是不给message，更新时就会自己查最新的message
     */
    fun notifyMsgFoldSessionUpdateLastMsg() {
        SessionManager.get().syncSession(
            FoldSessionParam(
                Session.ID.FOLDED_MSG,
                null,
                null,
                null,
                null
            ), createWhenAbsent = false
        )
    }

    fun helpDialog(ctx: Context): Dialog {
        val dialog = MAlertDialog(ctx)
        val days = foldDaysThresholdConfig()
        dialog.setMessage("已针对超过${days}天不回复的今日缘分和敲敲聊天消息进行折叠")
        dialog.setButton(
            AlertDialog.BUTTON_NEGATIVE, "我知道了"
        ) { d, _ ->
            d.dismiss()
        }
        dialog.setSupportDark(true)
        dialog.setCanceledOnTouchOutside(true)
        return dialog
    }

    fun getClearUnreadDialog(context: Context, onConfirm: () -> Unit): Dialog {
        val dialog = MAlertDialog(context)
        dialog.setMessage("消息气泡会清除，但消息不会丢失，也不会显示“已读”")
        dialog.setTitle("忽略未读")
        dialog.setButton(
            AlertDialog.BUTTON_NEGATIVE, "取消"
        ) { d, _ ->
            d.dismiss()
        }
        dialog.setButton(
            AlertDialog.BUTTON_POSITIVE, "确定"
        ) { _, _ ->
            onConfirm()
        }
        dialog.setSupportDark(true)
        dialog.setCanceledOnTouchOutside(true)
        return dialog
    }

    @JvmOverloads
    @JvmStatic
    fun clearAllOfficialUnread(
        lifecycleOwner: LifecycleOwner,
        sessionFoldType: Int,
        onItemCleared: (SessionKey) -> Unit
    ) {
        lifecycleOwner.lifecycle.coroutineScope.launchWhenResumed {
            withContext(MMDispatchers.User) {
                get().findAllSessions(filter = { it.foldType == sessionFoldType }) {
                    it.sessionKey
                }.map {
                    SessionKey.fromString(it)
                }.mapNotNull {
                    get().getSession(it.value, false).first?.toModel()
                }.filter {
                    it.hasUnread()
                }.forEach {
                    SessionService.getInstance().ignoreUnreadCount(it)
                    get().syncSession(ClearUnread(it.sessionKey))
                    withContext(MMDispatchers.Main) {
                        onItemCleared(it.sessionKey)
                    }
                }

            }

        }

    }

    fun hasScanedToday(): Boolean {
        val last = KV.getUserLong(KEY_SCAN_TIME, 0)
        val current = System.currentTimeMillis()
        if (current - last > TimeUnit.HOURS.toMillis(
                SessionAppConfigV2Getter.get().msgFoldScanInterval().toLong()
            )
        ) {
            KV.saveUserValue(KEY_SCAN_TIME, current)
            return false
        }
        return true
    }

    fun initTestInfo() {
        isBubbleTest = SessionAppConfigV1.silenceBubbleExp()
    }
}
