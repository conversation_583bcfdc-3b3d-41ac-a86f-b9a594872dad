package com.immomo.momo.maintab.model

import androidx.annotation.Keep
import com.immomo.android.module.fundamental.Badge.model.BaseBadgeModel
import com.immomo.momo.service.bean.AvatarFrame

/**
 * session小标
 */
@Keep
data class UserBadgeWrapper(var badgeModels: List<BaseBadgeModel>? = null)

@Keep
data class UserAvatarFrameWrapper(var avatarFrameModels: AvatarFrame? = null) {
    fun getAvatarFrame(): String? {
        return avatarFrameModels?.safeAvatarFrame
    }
}