package com.immomo.momo.maintab.session2.defs

import android.text.TextUtils
import com.alibaba.fastjson.JSON
import com.cosmos.mdlog.MDLog
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.utils.UIUtils
import com.immomo.mmutil.task.MomoMainThreadExecutor
import com.immomo.momo.R
import com.immomo.momo.greet.GreetHelper
import com.immomo.momo.im.GiftSayHiAppConfigV1
import com.immomo.momo.maintab.session2.SessionContentParser
import com.immomo.momo.maintab.session2.SessionDefinition
import com.immomo.momo.maintab.session2.SessionGiftSayHiAppConfigV2
import com.immomo.momo.maintab.session2.SessionUpdateBundle.ReloadInfo
import com.immomo.momo.maintab.session2.data.database.SessionContent
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.database.isInCache
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.data.manager.SessionKey.Companion.fromCompatible
import com.immomo.momo.maintab.session2.data.manager.SessionManager.Companion.get
import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.type.GiftSayHiSessionModel
import com.immomo.momo.message.sayhi.SayHiArgs
import com.immomo.momo.message.sayhi.SayHiStackCache
import com.immomo.momo.messages.service.SingleMsgService
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.SayhiSession
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.bean.message.Type15Content
import com.immomo.momo.service.sessions.LastMsgCache
import com.immomo.momo.service.sessions.SessionService
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
class GiftSayHiContent(
    var hiUserTotalCount: Int = 0,
    var hiUserTotalCountUsable: Int = 0,
    var hiUnreadCount: Int = 0,
    var lastMsgRemoteId: String? = null,
    var desc: String? = null,
    var hasTopGift: Boolean = false, // 是否有指定礼物没有处理
    var hiUserCount: Int = 0 //所有招呼人数
) : SessionContent(SessionKey.KEY_GIFT_HI)

/**
 * 礼物招呼
 */
class GiftSayHiSessionDefinition : SessionDefinition<GiftSayHiContent, GiftSayHiSessionModel>(
    SessionKey.KEY_GIFT_HI, SessionContentParser.moshiParser()
) {
    override fun createContent(): GiftSayHiContent {
        return GiftSayHiContent()
    }

    override fun generateId(data: Any?): String? {
        return if (GiftSayHiAppConfigV1.isOpenExp() && data is SayhiSession && data.isFromGift) { // 如果是礼物招呼
            Session.ID.GiftSayhiSession
        } else {
            return super.generateId(data)
        }
    }

    override fun onClearUnread(session: SessionEntity) {
        val content = session.content as? GiftSayHiContent ?: return

        refreshUnread(session)
        refreshContentCnt(content)
        refreshDesc(content)
        content.hasTopGift = false
    }

    private fun refreshContentCnt(content: GiftSayHiContent) {
        content.hiUserTotalCount = if (SayHiArgs.isOpenBlock())
            SingleMsgService.getInstance().allGiftSayHiUnreadWithPartSpamUserCount else
            SingleMsgService.getInstance().allSayhiGiftUnreadedUserCount
        content.hiUserTotalCountUsable = if (SayHiArgs.isOpenBlock())
            SingleMsgService.getInstance().allGiftSayHiWithPartSpamUserCount else
            SingleMsgService.getInstance().allGiftSayhiUsableUserCount
        content.hiUserCount = SessionService.getInstance().allGiftSayHiUserNormalPartSpamCount
    }

    override fun onReloadInfo(id: String, session: SessionEntity, forceReload: Boolean) {
        syncSession(session, null, false)
    }

    // 更新招呼Session
    /** 【 PS 】
     * 收到招呼如果第一次是普通消息或者live消息,两个不互通,所以第一次进来是什么以后收
     * 到另一个类型的都不相互切货,因此只在insert的时候保存。
     *
     * 但是如果之前是live招呼或者普通招呼,收到一条礼物消息,都需要把这个对应的
     * sayhisession收到礼物类型的招呼里面去,因此在任何时候收到礼物都需要更新这个session的类型。
     */
    private fun syncSession(
        session: SessionEntity, lastMessage: Message?, isHarass: Boolean
    ): Boolean {
        val content = session.content as? GiftSayHiContent ?: return false
        refreshUnread(session)
        refreshContentCnt(content)
        refreshDesc(content)
        //仅更新最新一条招呼的消息
        if (lastMessage != null && lastMessage.isUpdateSession && !lastMessage.isImSpam
            && session.lastMsgTime <= lastMessage.timestampMillis
        ) {
            session.lastMsgId = lastMessage.msgId ?: session.lastMsgId
            if (lastMessage.stopFloat == 0) {
                session.lastMsgTime = lastMessage.timestamp?.time ?: session.lastMsgTime
            }
        }
        // NOTICE类型消息不显示在招呼界面
        if (lastMessage?.contentType != Message.CONTENTTYPE_MESSAGE_NOTICE
            && lastMessage?.contentType != Message.CONTENTTYPE_MESSAGE_HARASS
            && !isHarass
        ) {
            LastMsgCache.onSendNewMsg(lastMessage)
        }
        (lastMessage?.messageContent as? Type15Content)?.also {
            if (it.greetTopType == 1) { // 当前为置顶消息
                content.hasTopGift = true
                content.desc = "有人给您送置顶礼物，快去看看吧~"
            }
        }
        if (lastMessage != null && lastMessage.contentType == Message.CONTENTTYPE_MESSAGE_GIFT) { // 如果收到到的是礼物消息则需要刷新
            MomoMainThreadExecutor.cancelAllRunnables(hashCode())
            MomoMainThreadExecutor.postDelayed(hashCode(), {
                get().syncSession(ReloadInfo(fromCompatible(Session.ID.SayhiSession)))
            }, 500)
        }
        return true
    }

    /**
     * 刷新消息未读数
     */
    private fun refreshUnread(session: SessionEntity) {
        var unreadLiveHiMsgCount = SingleMsgService.getInstance()
            .getAllSayhiInnerNeedCountSessionNumNew(SayhiSession.FROM_TYPE_GIFT)
        if (unreadLiveHiMsgCount < 0) {
            unreadLiveHiMsgCount = 0
        }
        //理论上有未读消息就不应该有静默消息
        if (GreetHelper.isGreetNotRemindSettingMode()) {
            session.silentMessageCount = unreadLiveHiMsgCount
        } else {
            session.unreadMessageCount = unreadLiveHiMsgCount
            if (session.unreadMessageCount > 0) {
                session.silentMessageCount = 0
            }
        }
    }

    /**
     * 刷新内容
     */
    private fun refreshDesc(content: GiftSayHiContent) {
        val hiUserTotalCountLimit = SessionGiftSayHiAppConfigV2.getConfigUnreadContentLimit()
        content.desc = if (GreetHelper.isGreetNotRemindSettingMode()) {
            content.hasTopGift = false
            "今日不再提醒新的招呼"
        } else if (content.hiUserTotalCount > 0) {
            val hiUserTotalCount = content.hiUserTotalCount
            if (hiUserTotalCount >= hiUserTotalCountLimit) {
                "又有${hiUserTotalCount}人给你打招呼，快去看看吧~"
            } else {
                "有${hiUserTotalCount}人给你打招呼，快去看看吧"
            }
        } else if (content.hiUserTotalCountUsable > 0) {
            "当前还有${content.hiUserTotalCountUsable}个招呼暂未处理"
        } else {
            UIUtils.getString(R.string.no_more_unread_sayhi_user_now)
        }
    }

    override fun syncSession(session: SessionEntity, data: Any?): Boolean {
        if (super.syncSession(session, data)) return true
        val content = session.content as? GiftSayHiContent ?: return false

        val sayhiSession =
            data as? SayhiSession ?: return syncSession(session, null, false)
        val isHarass =
            if (SayHiArgs.isOpenBlock()) sayhiSession.isSessionSpamAll else sayhiSession.isHarassGreeting
        // 收到骚扰折叠招呼时消息的招呼session未创建的情况不需要创建招呼session.
        if (!session.isInCache(SessionEntity.CACHE_DATABASE) && isHarass) {
            session.markAsDeleted = true
            return true
        }

        var lastMessage: Message? = sayhiSession.lastMessage
        val momoId = if (SayHiArgs.isOpenBlock())
            SessionService.getInstance().findLastSayHiWithPartSpamSessionId() else
            SessionService.getInstance().findLastSayHiSessionId()
        if (!TextUtils.isEmpty(momoId)) {
            //不是同一个人，则强制更新
            if (content.lastMsgRemoteId == null || content.lastMsgRemoteId != momoId) {
                lastMessage = SingleMsgService.getInstance().getHiLastCommonMsgWithoutNotice(momoId)
            }
        }
        return syncSession(session, lastMessage, isHarass)
    }

    override fun GiftSayHiContent.contentToModel(
        baseInfo: BaseSessionInfo
    ): GiftSayHiSessionModel = GiftSayHiSessionModel(
        baseInfo,
        hiUserTotalCount = hiUserTotalCount,
        hiUserTotalCountUsable = hiUserTotalCountUsable,
        desc = desc.safe(),
        hasTopGift = hasTopGift
    )

    companion object {
        @JvmField
        val KEY_SAYHI = SessionKey(SessionKey.KEY_GIFT_HI, Session.ID.GiftSayhiSession)

        @JvmField
        val SAYHI = SessionKey.KEY_GIFT_HI + "_" + Session.ID.GiftSayhiSession
    }
}