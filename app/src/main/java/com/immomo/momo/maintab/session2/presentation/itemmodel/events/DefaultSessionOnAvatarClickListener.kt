package com.immomo.momo.maintab.session2.presentation.itemmodel.events

import android.content.Intent
import android.view.View
import com.immomo.android.module.vchat.SPKeys
import com.immomo.framework.storage.kv.KV
import com.immomo.momo.GotoKeys
import com.immomo.momo.discuss.activity.DiscussProfileActivity
import com.immomo.momo.gotologic.GotoDispatcher
import com.immomo.momo.group.activity.GroupProfileActivity
import com.immomo.momo.innergoto.log.CompleteGoto.Companion.getSimpleNewGoto
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.maintab.session2.domain.model.type.DiscussChatSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.GroupChatSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.UserChatSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.VChatSuperRoomSessionModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.SessionItemModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.SessionOnAvatarClickListener
import com.immomo.momo.router.ProfileGotoOptions
import com.immomo.momo.router.ProfileRouter
import com.immomo.momo.router.RefreshTag
import com.immomo.momo.voicechat.util.IntentUtils
import info.xudshen.android.appasm.AppAsm

open class DefaultSessionOnAvatarClickListener : SessionOnAvatarClickListener {
    override fun onAvatarClicked(
        view: View,
        viewHolder: SessionItemModel.SessionViewHolder,
        session: SessionModel,
        adapterPosition: Int
    ) {
        when (session) {
            is UserChatSessionModel -> {
                val options = ProfileGotoOptions(session.sessionId)
                options.requestTypeTag = RefreshTag.LOCAL
                options.infoSource = "im"
                AppAsm.getRouter(ProfileRouter::class.java)
                    .gotoProfile(viewHolder.activity, options)
            }
            is GroupChatSessionModel -> {
                val gotoStr = getSimpleNewGoto(
                    "",
                    GotoKeys.GOTO_GROUP_PROFILE,
                    object : HashMap<String, String>() {
                        init {
                            put(
                                GroupProfileActivity.INTENT_KEY_GID,
                                session.sessionId
                            )
                            put(
                                GroupProfileActivity.INTENT_KEY_TAG,
                                GroupProfileActivity.INTENT_KEY_TAG_LOCAL
                            )
                        }
                    })
                GotoDispatcher.action(gotoStr, viewHolder.activity).execute()
            }
            is DiscussChatSessionModel -> {
                val intent = Intent()
                intent.setClass(view.context, DiscussProfileActivity::class.java)
                intent.putExtra(
                    DiscussProfileActivity.INTENT_KEY_TAG,
                    DiscussProfileActivity.INTENT_KEY_TAG_LOCAL
                )
                intent.putExtra(DiscussProfileActivity.INTENT_KEY_DID, session.sessionId)
                view.context.startActivity(intent)
            }
            is VChatSuperRoomSessionModel -> {
                val params: MutableMap<String, String> = HashMap()
                params["vid"] = session.sessionId
                params["havaRedDot"] =
                    if (KV.getUserBool(SPKeys.User.VoiceChat.KEY_SUPER_ROOM_RED_DOT, true))
                        "1" else "0"
                IntentUtils.gotoLuaPageWithParams(
                    IntentUtils.URL_CHAT_LUA_MIDDLE_PAGE,
                    view.context, params
                )
            }
        }
    }
}