package com.immomo.momo.maintab.session2.data.manager

import android.util.Log
import com.immomo.android.router.momo.business.vchat.VChatMiscRouter
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.momo.discuss.bean.Discuss
import com.immomo.momo.discuss.service.DiscussService
import com.immomo.momo.group.bean.Group
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.protocol.http.DiscussApi
import com.immomo.momo.protocol.http.GroupApi
import com.immomo.momo.protocol.http.SessionApi
import com.immomo.momo.protocol.http.UserApi
import com.immomo.momo.service.bean.User
import com.immomo.momo.service.group.GroupService
import com.immomo.momo.service.sessions.SessionUserCache
import com.immomo.momo.service.user.UserService
import com.immomo.momo.service.vchatroom.VChatSuperRoomService
import com.immomo.momo.voicechat.model.superroom.VChatSuperRoom
import info.xudshen.android.appasm.AppAsm
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlin.time.ExperimentalTime
import kotlin.time.milliseconds

internal interface Task<R> {
    val taskId: String

    val onComplete: (result: R) -> Unit
}

@OptIn(ExperimentalTime::class, FlowPreview::class)
internal class BufferedTaskProcessor<T : Task<*>> {
    private val taskChannel = Channel<T>(capacity = Channel.UNLIMITED)
    private val taskBuffers = mutableListOf<T>()
    private var processJob: Job? = null

    fun offer(task: T): Boolean = taskChannel.offer(task)

    fun process(scope: CoroutineScope, block: suspend CoroutineScope.(List<T>) -> Unit) {
        scope.launch {
            for (task in taskChannel) {
                taskBuffers.add(task)

                processJob?.cancel()
                processJob = async {
                    delay(300.milliseconds)
                    val copied = ArrayList(taskBuffers)
                    taskBuffers.clear()

                    async(coroutineContext + SupervisorJob()) {
                        try {
                            block(copied)
                        } catch (e: Exception) {
                            Log.e(
                                "SessionInfoCache",
                                "reduce failed ${<EMAIL>}", e
                            )
                        }
                    }
                }
            }
        }
    }
}

internal class FetchUserTask(
    val remoteId: String,
    override val onComplete: (result: User) -> Unit
) : Task<User> {
    override val taskId: String = remoteId
}

internal class FetchGroupTask(
    val remoteId: String,
    override val onComplete: (result: Group) -> Unit
) : Task<Group> {
    override val taskId: String = remoteId
}

internal class FetchDiscussTask(
    val remoteId: String,
    override val onComplete: (result: Discuss) -> Unit
) : Task<Discuss> {
    override val taskId: String = remoteId
}

internal class FetchVChatSuperRoomTask(
    val remoteId: String,
    override val onComplete: (result: VChatSuperRoom) -> Unit
) : Task<VChatSuperRoom> {
    override val taskId: String = remoteId
}


class SessionInfoCache(private val sessionManager: SessionManager) {
    val sessionInfoScope = CoroutineScope(SupervisorJob() + MMDispatchers.User)

    private val fetchUserTaskProcessor = BufferedTaskProcessor<FetchUserTask>()
    private val fetchGroupTaskProcessor = BufferedTaskProcessor<FetchGroupTask>()
    private val fetchDiscussTaskProcessor = BufferedTaskProcessor<FetchDiscussTask>()
    private val fetchVChatSuperRoomTaskProcessor = BufferedTaskProcessor<FetchVChatSuperRoomTask>()

    init {
        fetchUserTaskProcessor.process(sessionInfoScope) { tasks ->
            val userMap = tasks.map { it.remoteId to User(it.remoteId) }.toMap()
            val users = userMap.values.toList()
            UserApi.getInstance().downloadSimpleUsers(users, "")
            UserService.getInstance().saveUserSimple(users)

            tasks.forEach { task ->
                userMap[task.remoteId]?.let { task.onComplete(it) }
            }
        }

        fetchGroupTaskProcessor.process(sessionInfoScope) { tasks ->
            val taskMap = tasks.groupBy { it.remoteId }
            val groupMap = tasks.map { it.remoteId to Group() }.toMap()
            groupMap.map { (groupId, group) ->
                if (GroupApi.getInstance().downloadGroupProfile(groupId, group) <= 0) return@map

                GroupService.getInstance().saveOrUpdate(group, true)

                taskMap[groupId]?.forEach { task -> task.onComplete(group) }
            }
        }

        fetchDiscussTaskProcessor.process(sessionInfoScope) { tasks ->
            val taskMap = tasks.groupBy { it.remoteId }
            val discussMap = tasks.map { it.remoteId to Discuss() }.toMap()
            discussMap.map { (discussId, discuss) ->
                if (DiscussApi.getInstance()
                        .downloadDiscussProfile(discussId, discuss) <= 0
                ) return@map

                DiscussService.getInstance().saveDiscuss(discuss, false)

                taskMap[discussId]?.forEach { task -> task.onComplete(discuss) }
            }
        }

        fetchVChatSuperRoomTaskProcessor.process(sessionInfoScope) { tasks ->
            val taskMap = tasks.groupBy { it.remoteId }
            taskMap.keys.map { vId ->
                val tempRoom = SessionApi.getInstance().downloadVChatSuperRoomProfile(vId)
                    ?: return@map
                if (tempRoom.isExpired) {
                    AppAsm.getRouter(VChatMiscRouter::class.java)
                        .undoSuperRoomSesssionSticky(tempRoom.vRoomId)
                    return@map
                }
                VChatSuperRoomService.getInstance().saveOrUpdate(tempRoom)

                taskMap[vId]?.forEach { task -> task.onComplete(tempRoom) }
            }
        }
    }

    fun reset() {
        sessionInfoScope.cancel()
    }

    fun fetchUser(
        source: SessionEntity,
        remoteId: String?,
        forceReload: Boolean = false,
        onFetched: SessionEntity.(User) -> Boolean
    ) {
        if (remoteId == null) return

        val user: User? = SessionUserCache.getUser(remoteId, forceReload)
        Log.d("FAJDLAJS", "fetchUser $remoteId ${user?.displayName}")
        if (user?.displayName?.isNotEmpty() == true) {
            source.onFetched(user)
        } else {
            val sessionKey = source.sessionKey
            fetchUserTaskProcessor.offer(FetchUserTask(remoteId) { remote ->
                sessionManager.withSession<SessionEntity>(SessionKey.fromString(sessionKey)) {
                    Log.d("FAJDLAJS", "fetched $remoteId ${remote.displayName}")
                    it.onFetched(remote)
                }
            })
        }
    }

    fun fetchGroup(
        source: SessionEntity,
        remoteId: String?,
        forceReload: Boolean = false,
        onFetched: SessionEntity.(Group) -> Boolean
    ) {
        if (remoteId == null) return

        val group: Group? = SessionUserCache.getGroup(remoteId, forceReload)
        if (group?.displayName?.isNotEmpty() == true) {
            source.onFetched(group)
        } else {
            val sessionKey = source.sessionKey
            fetchGroupTaskProcessor.offer(FetchGroupTask(remoteId) { remote ->
                sessionManager.withSession<SessionEntity>(SessionKey.fromString(sessionKey)) {
                    it.onFetched(remote)
                }
            })
        }
    }


    fun fetchDiscuss(
        source: SessionEntity,
        remoteId: String?,
        forceReload: Boolean = false,
        onFetched: SessionEntity.(Discuss) -> Boolean
    ) {
        if (remoteId == null) return

        val discuss = SessionUserCache.getDiscuss(remoteId, forceReload)
        Log.d("FAJDLAJS", "fetchDiscuss $remoteId ${discuss?.displayName}")
        if (discuss?.displayName?.isNotEmpty() == true) {
            source.onFetched(discuss)
        } else {
            val sessionKey = source.sessionKey
            fetchDiscussTaskProcessor.offer(FetchDiscussTask(remoteId) { remote ->
                Log.d("FAJDLAJS", "fetched $remoteId ${remote.displayName}")
                sessionManager.withSession<SessionEntity>(SessionKey.fromString(sessionKey)) {
                    it.onFetched(remote)
                }
            })
        }
    }

    fun fetchVChatSuperRoom(
        source: SessionEntity,
        remoteId: String?,
        forceReload: Boolean = false,
        onFetched: SessionEntity.(VChatSuperRoom) -> Boolean
    ) {
        if (remoteId == null) return

        val room = SessionUserCache.getVChatSuperRoom(remoteId, forceReload)
        Log.d("FAJDLAJS", "fetchVChat $remoteId ${room?.displayName}")
        if (room?.displayName?.isNotEmpty() == true) {
            source.onFetched(room)
        } else {
            val sessionKey = source.sessionKey
            fetchVChatSuperRoomTaskProcessor.offer(FetchVChatSuperRoomTask(remoteId) { remote ->
                Log.d("FAJDLAJS", "fetched $remoteId ${remote.displayName}")
                sessionManager.withSession<SessionEntity>(SessionKey.fromString(sessionKey)) {
                    it.onFetched(remote)
                }
            })
        }
    }
}
