package com.immomo.momo.maintab.session2.data.manager

import android.util.Log
import com.immomo.momo.common.AppKit
import com.immomo.momo.maintab.session2.defs.*
import com.immomo.momo.messages.service.*
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.message.Type16Content
import com.immomo.momo.service.bean.message.Type19Content
import com.immomo.momo.service.bean.message.Type28Content
import com.immomo.momo.service.bean.message.Type30Content
import com.immomo.momo.videochat.friendvideo.friend.FriendQChatInfo
import java.util.concurrent.ConcurrentHashMap

class LastUnreadResult(
    var msgId: String? = null,
    var maxMessageTimestamp: Long = 0,
    var data: Any? = null
) {
    fun update(msgId: String, timestamp: Long, block: () -> Any?) {
        if (timestamp >= maxMessageTimestamp) {
            this.msgId = msgId
            this.maxMessageTimestamp = timestamp
            this.data = block()
        }
    }

    fun isValid() = msgId != null
}

abstract class AbsLastUnreadFilter {
    abstract val key: String

    abstract val messageDataExtractor: ((Message) -> Any?)?

    private val unreadResultMap = ConcurrentHashMap<SessionKey, LastUnreadResult>()

    abstract fun initUnreadResult(sessionKey: SessionKey): LastUnreadResult

    fun getUnread(sessionKey: SessionKey): LastUnreadResult {
        return unreadResultMap.getOrPut(sessionKey) {
            try {
                initUnreadResult(sessionKey)
            } catch (e: Exception) {
                Log.e("SessionMessageProcessor", "failed to init unread", e)
                LastUnreadResult(null, 0, null)
            }
        }
    }

    fun invalidate(sessionKey: SessionKey) {
        unreadResultMap.remove(sessionKey)
    }

    fun doFilter(
        unreadResult: LastUnreadResult,
        sessionKey: SessionKey,
        message: Message
    ) {
        if (!message.receive) return
        if (message.status != Message.STATUS_RECEIVER_READED) {
            unreadResult.update(message.msgId, message.timestampExt) {
                messageDataExtractor?.invoke(message)
            }
        } else if (unreadResult.msgId == message.msgId) {
            //最新一条未读被至为已读
            invalidate(sessionKey)
        }
    }

    open fun doFilter(sessionKey: SessionKey, message: Message) {
        doFilter(getUnread(sessionKey), sessionKey, message)
    }
}

open class LastUnreadMessageFilter(
    override val key: String,
    val messageType: Int,
    override val messageDataExtractor: ((Message) -> Any?)? = null
) : AbsLastUnreadFilter() {

    override fun initUnreadResult(sessionKey: SessionKey): LastUnreadResult {
        val message = when (sessionKey.type) {
            DebuggerSessionDefinition.Type -> null
            UserChatSessionDefinition.Type -> {
                SingleMsgServiceV2.service.findUnreadByType(sessionKey.id, messageType)
            }
            MaskChatSessionDefinition.Type -> {
                MaskMsgServiceV2.service.findUnreadByType(sessionKey.id, messageType)
            }
            TextChatSessionDefinition.Type -> {
                TextMatchMsgServiceV2.service.findUnreadByType(sessionKey.id, messageType)
            }
            GroupChatSessionDefinition.Type -> {
                GroupMsgServiceV2.service.findUnreadByType(sessionKey.id, messageType)
            }
            DiscussChatSessionDefinition.Type -> {
                DiscussMsgServiceV2.service.findUnreadByType(sessionKey.id, messageType)
            }
            VChatSuperRoomSessionDefinition.Type -> {
                VChatSuperRoomMsgServiceV2.service.findUnreadByType(sessionKey.id, messageType)
            }
            else -> {
                Log.d("LastUnreadMessage", "$sessionKey is not supported")
                null
            }
        }
        return LastUnreadResult().also {
            if (message != null) doFilter(it, sessionKey, message)
        }
    }
}

class LastUnreadGiftMessageFilter(key: String) :
    LastUnreadMessageFilter(key, Message.CONTENTTYPE_MESSAGE_GIFT) {

    override fun doFilter(sessionKey: SessionKey, message: Message) {
        if (when (sessionKey.type) {
                UserChatSessionDefinition.Type -> true
                MaskChatSessionDefinition.Type -> true
                TextChatSessionDefinition.Type -> true
                GroupChatSessionDefinition.Type,
                DiscussChatSessionDefinition.Type ->
                    message.receiveId == AppKit.getAccountManager().currentAccountUserId
                else -> false
            }
        ) {
            super.doFilter(sessionKey, message)
        }
    }
}

class LastUnreadAtMessageFilter(
    override val key: String,
    override val messageDataExtractor: ((Message) -> Any?) = {
        it.atText
    }
) : AbsLastUnreadFilter() {

    override fun initUnreadResult(sessionKey: SessionKey): LastUnreadResult {
        val message = when (sessionKey.type) {
            GroupChatSessionDefinition.Type -> {
                GroupMsgServiceV2.service.findAtMessage(sessionKey.id)
            }
            else -> throw IllegalArgumentException("can not use $sessionKey")
        }
        return LastUnreadResult().also {
            if (message != null) doFilter(it, sessionKey, message)
        }
    }

    override fun doFilter(sessionKey: SessionKey, message: Message) {
        if (!message.isAtMe) return
        super.doFilter(sessionKey, message)
    }
}

class LastUnreadMessageInterceptor : MessageInfoCache.Interceptor {
    private val filtersByKey = mutableMapOf<String, AbsLastUnreadFilter>()

    private val filtersByMessageType = mutableMapOf<Int, MutableList<LastUnreadMessageFilter>>()
    private val filtersNotByMessageType = mutableListOf<AbsLastUnreadFilter>()

    private fun addFilter(filter: AbsLastUnreadFilter) {
        filtersByKey[filter.key] = filter
        if (filter is LastUnreadMessageFilter) {
            filtersByMessageType.getOrPut(filter.messageType) { mutableListOf() }.add(filter)
        } else {
            filtersNotByMessageType.add(filter)
        }
    }

    private fun addFilters(vararg filters: AbsLastUnreadFilter) {
        filters.forEach { addFilter(it) }
    }

    private fun getFilterByKey(key: String) = filtersByKey[key]

    init {
        addFilters(
            LastUnreadGiftMessageFilter("unread_gift"),
            LastUnreadMessageFilter("unread_hongbao", Message.CONTENTTYPE_MESSAGE_HONGBAO),
            LastUnreadMessageFilter(
                "unread_friend_chat",
                Message.CONTENTTYPE_MESSAGE_FRIEND_QCHAT
            ) {
                FriendQChatInfo.getGotoDesc(
                    it.getMessageContent(Type19Content::class.java)?.gotoStr
                )
            },
            LastUnreadMessageFilter(
                "unread_question_match",
                Message.CONTENTTYPE_MESSAGE_QUESTION_MATCH
            ) {
                it.getMessageContent(Type30Content::class.java)?.sessText
            },
            LastUnreadMessageFilter("unread_diandian", Message.CONTENTTYPE_MESSAGE_DIANDIAN) {
                it.getMessageContent(Type16Content::class.java)?.pushPrefix
            },
            LastUnreadMessageFilter("unread_mgs_group", Message.CONTENTTYPE_MESSAGE_MGS_GROUP) {
                it.getMessageContent(Type28Content::class.java)?.let { content ->
                    content.prompt to content.appId
                }
            },
            LastUnreadMessageFilter(
                "unread_show_vchat_hongbao",
                Message.CONTENTTYPE_MESSAGE_SHOW_VCHAT_HONGBAO
            ),
            LastUnreadAtMessageFilter("unread_atMe")
        )
    }

    override fun onIntercept(sessionMessage: SessionMessage) {
        val sessionKey = sessionMessage.sessionKey ?: return

        when (sessionMessage) {
            is SessionMessage.Update,
            is SessionMessage.Receive,
            is SessionMessage.Sync -> {
                sessionMessage.messageList.forEach { message ->
                    filtersByMessageType[message.contentType]?.forEach { filter ->
                        filter.doFilter(sessionKey, message)
                    }
                    filtersNotByMessageType.forEach { filter ->
                        filter.doFilter(sessionKey, message)
                    }
                }
            }
            is SessionMessage.Send -> {
                //发送消息不计入已读未读
            }
            is SessionMessage.ResetLast,
            is SessionMessage.Delete,
            is SessionMessage.UpdateStatus -> {
                //无法计算，清除缓存
                filtersByKey.values.forEach {
                    it.invalidate(sessionKey)
                }
            }
        }
    }

    override fun reset() {
    }

    companion object {
        private fun self(isLetschat: Boolean) =
                SessionManager.get().messageInfoCache.interceptor<LastUnreadMessageInterceptor>()

        fun getUnread(sessionKey: SessionKey, messageType: Int) = when (messageType) {
            Message.CONTENTTYPE_MESSAGE_GIFT ->
                self(false).getFilterByKey("unread_gift")?.getUnread(sessionKey)
            Message.CONTENTTYPE_MESSAGE_HONGBAO ->
                self(false).getFilterByKey("unread_hongbao")?.getUnread(sessionKey)
            Message.CONTENTTYPE_MESSAGE_FRIEND_QCHAT ->
                self(false).getFilterByKey("unread_friend_chat")?.getUnread(sessionKey)
            Message.CONTENTTYPE_MESSAGE_QUESTION_MATCH ->
                self(false).getFilterByKey("unread_question_match")?.getUnread(sessionKey)
            Message.CONTENTTYPE_MESSAGE_DIANDIAN ->
                self(false).getFilterByKey("unread_diandian")?.getUnread(sessionKey)
            Message.CONTENTTYPE_MESSAGE_MGS_GROUP ->
                self(false).getFilterByKey("unread_mgs_group")?.getUnread(sessionKey)
            Message.CONTENTTYPE_MESSAGE_SHOW_VCHAT_HONGBAO ->
                self(false).getFilterByKey("unread_show_vchat_hongbao")?.getUnread(sessionKey)
            else -> null
        }

        fun getUnreadAtMessage(sessionKey: SessionKey) =
            self(false).getFilterByKey("unread_atMe")?.getUnread(sessionKey)
    }
}
