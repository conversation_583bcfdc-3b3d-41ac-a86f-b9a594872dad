package com.immomo.momo.maintab.session2

import com.immomo.annotations.appconfig.AppConfigField
import com.immomo.annotations.appconfig.appconfigv2.AppConfigV2
import com.immomo.momo.maintab.session2.apt.SessionGiftSayHiAppConfigV2Getter

/**
 * 礼物招呼盒子
 */
@AppConfigV2
object SessionGiftSayHiAppConfigV2 {

    @AppConfigField(
        mark = "572",
        key = "gift_hi_icon",
        defValue = "https://s.momocdn.com/s1/u/ebcbiifjc/hello_gift_icon.png",
        isSysValue = true
    )
    var icon: String = "https://s.momocdn.com/s1/u/ebcbiifjc/hello_gift_icon.png"

    @AppConfigField(
        mark = "572",
        key = "gift_hi_title",
        defValue = "收到的礼物招呼",
        isSysValue = true
    )
    var title: String = "收到的礼物招呼"

    @AppConfigField(
        mark = "572",
        key = "gift_hi_send_msg",
        defValue = "😃,👋,❤️,谢谢啦,你好呀",
        isSysValue = true
    )
    var sendMsg: String = "😃,👋,❤️,谢谢啦,你好呀"

    @AppConfigField(
        mark = "572",
        key = "unread_content_limit",
        defValue = "5",
        isSysValue = true
    )
    var unreadContentLimit: Int = 5

    @AppConfigField(
        mark = "572",
        key = "gift_msg_query_cnt",
        defValue = "20",
        isSysValue = true
    )
    var giftMsgQueryCnt: Int = 20

    @AppConfigField(
        mark = "572",
        key = "gift_hi_page_load_more_cnt",
        defValue = "20",
        isSysValue = true
    )
    var giftHiPageLoadMoreCnt: Int = 20

    @AppConfigField(
        mark = "572",
        key = "gift_hi_keep_push_card",
        defValue = "0",
        isSysValue = true
    )
    var giftHiKeepPushCard: Int = 0

    private var configSendMsg: List<String>? = null

    private var giftMsgQueryCntFinal: Int =  SessionGiftSayHiAppConfigV2Getter.get().giftMsgQueryCnt()

    private var giftHiPageLoadMoreCntFinal: Int =  SessionGiftSayHiAppConfigV2Getter.get().giftHiPageLoadMoreCnt()

    /**
     * 配置的icon
     */
    @JvmStatic
    fun getConfigIcon() = SessionGiftSayHiAppConfigV2Getter.get().icon()

    /**
     * 配置的文本
     */
    @JvmStatic
    fun getConfigText() = SessionGiftSayHiAppConfigV2Getter.get().title()

    /**
     * 获取发送配置文本
     */
    @JvmStatic
    fun getCardSendMsgs():List<String>? {
        if (configSendMsg == null) {
            configSendMsg = arrayListOf()
            kotlin.runCatching {
                val cardSendMsgs: String = SessionGiftSayHiAppConfigV2Getter.get().sendMsg()
                configSendMsg = cardSendMsgs.split(",")
            }
        }
        return configSendMsg
    }

    /**
     * 配置的未读文案切换内容的大小
     */
    @JvmStatic
    fun getConfigUnreadContentLimit() = SessionGiftSayHiAppConfigV2Getter.get().unreadContentLimit()

    /**
     * 查询的礼物消息配置数
     */
    @JvmStatic
    fun getConfigGiftMsgQueryCntFinal() = giftMsgQueryCntFinal

    /**
     * 获取列表加载更多配置个数
     */
    @JvmStatic
    fun getConfigGiftHiPageLoadMoreCntFinal() = giftHiPageLoadMoreCntFinal

    /**
     * 始终保持push卡片不消失
     */
    @JvmStatic
    fun isGiftHiKeepPushCard() = SessionGiftSayHiAppConfigV2Getter.get().giftHiKeepPushCard() == 1
}

