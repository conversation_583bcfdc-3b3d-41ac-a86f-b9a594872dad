package com.immomo.momo.maintab.sessionlist.sort.data

import androidx.annotation.Keep
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

class RequestSessionRecommendParams {
    var remoteSessions = mutableMapOf<String, LocalSessionInfo>()
}
@Keep
class LocalSessionInfo {
    @Expose
    @SerializedName("newMsgTime")
    var newMsgTime = 0L
    @Expose
    @SerializedName("unreadCount")
    var unreadCount = 0
    @Expose
    @SerializedName("source")
    var source = ""
}