package com.immomo.momo.maintab;

import com.immomo.mmutil.task.ThreadUtils;

/**
 * Created by tanjie on 16/1/11.
 */
public class TaskSequencer {

    private TaskWrapper mHead;
    private TaskWrapper mCurrent;

    public TaskSequencer addTask(TaskWrapper task) {

        if (mHead == null) {
            mHead = task;
        }

        if (mCurrent == null) {
            mCurrent = task;
        } else {
            mCurrent.next = task;
            mCurrent = task;
        }
        return this;
    }

    public void start() {
        ThreadUtils.execute(ThreadUtils.TYPE_INNER, new Runnable() {
            @Override
            public void run() {
                TaskWrapper taskWrapper = mHead;
                while (null != taskWrapper) {
                    if (taskWrapper.needRunInner()) {
                        taskWrapper.innerRun();
                    }
                    taskWrapper = taskWrapper.next;
                }
            }
        });
    }

    public static abstract class TaskWrapper {
        TaskWrapper next;
        /**
         * 由子类实现，用于判断是否需要执行innerRun方法
         *
         * @return 是否执行innerRun
         */
        protected abstract boolean needRunInner();

        protected abstract void innerRun();
    }
}
