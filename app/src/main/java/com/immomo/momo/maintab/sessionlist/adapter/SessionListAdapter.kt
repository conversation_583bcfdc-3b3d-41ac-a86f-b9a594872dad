package com.immomo.momo.maintab.sessionlist.adapter

import android.content.Context
import android.os.Handler
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.immomo.android.mm.cement2.AsyncCementModel
import com.immomo.android.mm.cement2.CementViewHolder
import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.kotlin.extern.isNullOrEmpty
import com.immomo.lcapt.evlog.EVLog
import com.immomo.momo.R
import com.immomo.momo.maintab.session2.ITopFoldSessionLog
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.maintab.session2.domain.model.type.*
import com.immomo.momo.maintab.session2.presentation.itemmodel.*
import com.immomo.momo.maintab.session2.presentation.itemmodel.SessionItemModel.SessionViewHolder
import com.immomo.momo.maintab.sessionlist.expose.ExposeAsyncCementAdapter
import com.immomo.momo.maintab.sessionlist.expose.IItemModelExposure
import com.immomo.momo.maintab.sessionlist.expose.IItemSessionLogParamsProvider
import com.immomo.momo.maintab.sessionlist.sort.ISessionSortLog
import com.immomo.momo.maintab.sessionlist.util.SessionHelper
import com.immomo.momo.maintab.sessionlist.util.SessionHelper.SessionLogParams
import com.immomo.momo.message.NewSayUIConfigV1
import com.immomo.momo.message.sayhi.utils.IGreetLog
import com.immomo.momo.service.bean.FolderType
import com.immomo.momo.service.bean.Session
import com.immomo.momo.util.StringUtils

/**
 * Created by tangyuchun on 2018/7/24.
 */
open class SessionListAdapter(
    private val recyclerView: RecyclerView,
    diffHandler: Handler,
    val isInTestNotice: Int = 0
) : ExposeAsyncCementAdapter(diffHandler) {
    // 在当前页面可见范围的position集合。
    private val animatedSet: MutableSet<Int> = HashSet()

    //是否曝光聊天类的session p2p，群组，讨论组
    private val isExposureMsgSession: Boolean = SessionHelper.Log.isP2pExposureLogEnable()

    private val lastExposureTimeMap: MutableMap<String, Long> = HashMap()

    override fun onBindViewHolder(holder: CementViewHolder, position: Int, payloads: List<Any?>) {
        super.onBindViewHolder(holder, position, payloads)
        val model = getModel(position)
        if (model is AsyncCementModel<*, *> && model.state is SessionModel) {
            recordDragBubble(model.state as SessionModel, position)
        }
    }

    //<editor-fold desc="Drag Bubble">
    private fun recordDragBubble(session: SessionModel, posInAdapter: Int) {
        // 添加气泡集合
        if (session is ActiveUserSessionModel) {
            return
        }
        if (session.baseInfo.silentMessageCount <= 0
            && session.baseInfo.unreadMessageCount > 0
        ) {
            animatedSet.add(posInAdapter)
        } else {
            animatedSet.remove(posInAdapter)
        }
    }

    fun getAnimateView(bubblePosInList: Int): View? {
        var bubbleView: View? = null
        animatedSet.remove(bubblePosInList)
        val lm = recyclerView.layoutManager as? LinearLayoutManager ?: return null
        val itemView = lm.findViewByPosition(bubblePosInList)
        if (itemView != null) {
            bubbleView = itemView.findViewById(R.id.chatlist_item_tv_status_new)
        }
        return bubbleView
    }

    val dragAnimateList: List<Int>
        get() {
            val clearBubbleList: MutableList<Int> = ArrayList()
            if (animatedSet.isNotEmpty()) {
                val lm = recyclerView.layoutManager.castOrNull<LinearLayoutManager>()
                    ?: return emptyList()
                val firstVisiblePosInList = lm.findFirstVisibleItemPosition()
                val lastVisiblePosInList = lm.findLastVisibleItemPosition()
                val iterator = animatedSet.iterator()
                while (iterator.hasNext()) {
                    val i = iterator.next()
                    if (i in firstVisiblePosInList..lastVisiblePosInList) {
                        clearBubbleList.add(i)
                    } else {
                        iterator.remove()
                    }
                }
            }
            return clearBubbleList
        }

    fun removeAnimateItem(index: Int) {
        animatedSet.remove(index)
    }
    //</editor-fold>

    fun resetExposureData() {
        lastExposureTimeMap.clear()
    }

    override fun onExposure(context: Context, position: Int, holder: CementViewHolder) {
        val model = getModel(position)
        val childViewHolder = recyclerView.findViewHolderForAdapterPosition(position)
        if (model is SessionNoticeItemModel) {
            //互动通知为单独的item
            if (isInTestNotice == 1) {
                EVLog.create(ITopFoldSessionLog::class.java).foldedNotificationItemShow(
                    "notice",
                    0,
                    model.state.count,
                    position,
                    "",
                    model.state.desc,
                    "0",
                    "互动通知"
                )
            } else {
                var source = if (model.info.gotoTabType == 1) {
                    "feed_interact"
                } else {
                    "microcosm"
                }
                exposureLog(
                    false,
                    SessionLogParams(
                        "notice",
                        model.state.count,
                        position,
                        "0",
                        false,
                        null,
                        0,
                        0,
                        "",
                        "",
                        "",
                        true,
                        "",
                        source
                    )
                )

            }
        } else if (model is SessionItemModel<*> && childViewHolder is SessionViewHolder) {
            //经典session类型
            if (isInTestNotice == 1) {
                var whichItem = ""
                var buzName = ""
                var sessionId = model.state.sessionId

                when (model.state) {
                    is GotoSessionModel -> {
                        whichItem = "box"
                        buzName = model.state.title
                        sessionId = sessionId.replace("gotochat", "")
                    }
                    is UserChatSessionModel -> {//官方号
                        whichItem = "personal"
                        buzName = model.state.userName
                    }
                    is FriendNoticeSessionModel -> {
                        // 好友提醒入口点击
                        whichItem = "remind"
                        buzName = "好友提醒"
                    }
                }
                EVLog.create(ITopFoldSessionLog::class.java).foldedNotificationItemShow(
                    whichItem,
                    if (model.state.baseInfo.silentMessageCount > 0) 1 else 0,
                    model.state.baseInfo.unreadMessageCount,
                    position,
                    holder.castOrNull<SessionViewHolder>()?.timeStampView?.text?.toString() ?: "",
                    holder.castOrNull<SessionViewHolder>()?.contentView?.text?.toString() ?: "",
                    sessionId,
                    buzName
                )
            } else {
                exposureItem(model, childViewHolder, position)
                if (model.session is UserChatSessionModel && model.session.baseInfo.foldType == FolderType.Default) {
                    val userOnlineTag = model.session.userOnlineTag.orNull()
                    if (userOnlineTag != null && userOnlineTag.showAnim) {
                        EVLog.create(ISessionSortLog::class.java).breathingLightShow(
                                model.session.sessionId,
                                "msg_list",
                                userOnlineTag.roomPattern,
                                "",
                                model.session.sessionId + "_msg_list"
                        )
                    }
                }
            }
        } else if (model is UnReplySessionItemModel) {
            exposureLog(
                false,
                SessionLogParams(
                    "sayhi_to",
                    0,
                    position,
                    "0",
                    false,
                    null,
                    0,
                    0,
                    "",
                    "",
                    "",
                    false
                )
            )
        } else if (model is NewBoySessionItemModel) {
            exposureLog(
                false,
                SessionLogParams(
                    "newfriends",
                    model.info.baseInfo.unreadMessageCount,
                    position,
                    "0",
                    false,
                    null,
                    0,
                    0,
                    "",
                    "",
                    (childViewHolder as? NewBoySessionItemModel.ViewHolder)?.getTimeStr() ?: "",
                    false
                )
            )
        } else if (model is HePaiSessionItemModel) {
            exposureLog(
                false,
                SessionLogParams(
                    "shootwithu",
                    model.info.baseInfo.unreadMessageCount,
                    position,
                    "0",
                    false,
                    null,
                    0,
                    0,
                    "",
                    "",
                    (childViewHolder as? HePaiSessionItemModel.ViewHolder)?.getTimeStr() ?: "",
                    false
                )
            )
        } else if (model is IItemModelExposure) {
            model.onExposure(context, position, holder)
        }else if (model is IItemSessionLogParamsProvider) {
            exposureLog(false, model.sessionLogParams(context, position, holder))
        }
    }

    fun onCompletelyExposure(i: Int) {
        //ignored
    }

    protected open fun exposureItem(
        itemModel: SessionItemModel<*>,
        holder: SessionViewHolder,
        position: Int
    ) {
        val session = itemModel.session
        //参数拼凑
        var isMsgSession = false
        val whichItem = SessionHelper.Log.getWhichItemByType(session)
        val unreadCount = session.baseInfo.unreadMessageCount
        val remoteId = session.sessionId
        val hasRedDot = session.baseInfo.silentMessageCount > 0
        val lastMsg = if (session is GotoSessionModel) session.text else null
        val hiUserCount = 0
        val hiUserCountUsable = 0
        val preContent = holder.getPreContent()
        val onlineText = holder.getOnlineText() ?: ""
        val timeText = holder.getTimeStr()
        val isRecommend = session.baseInfo.recommendTime > 0
        val ifActivity = if (session is GotoSessionModel) session.hasRedPacketTxt else false
        val sessionTagLogMap = if (session is UserChatSessionModel) session.sessionTagLogMap else null
        val sessionLogParams = SessionLogParams(
            whichItem,
            unreadCount,
            position,
            remoteId,
            hasRedDot,
            lastMsg,
            hiUserCount,
            hiUserCountUsable,
            preContent,
            onlineText,
            timeText,
            isRecommend,
            ifActivity = ifActivity
        )
        if (session is UserChatSessionModel) {
            isMsgSession = true
            sessionLogParams.avatar = "1"
            sessionLogParams.isDisplayFire = if (StringUtils.isNotEmpty(session.fireIcon)) 1 else 0
        } else if (session is SayHiSessionModel) {
            sessionLogParams.remoteid = "0"
            sessionLogParams.hiUserCount = session.hiUserTotalCount
            sessionLogParams.hiUserCountUsable = session.hiUserTotalCountUsable
            sessionLogParams.hasRedPacket = session.hasRedPacketTxt
            if (NewSayUIConfigV1.isUserNewUI()) {
                sessionLogParams.text =
                    (if (session.hasGiftTag) "有礼物招呼·" else "") + session.desc
            }
            if (sessionLogParams.hasRedPacket) {
                EVLog.create(IGreetLog::class.java).showSayHiSessionRedPacket()
            }
        } else if (session is GiftSayHiSessionModel) {
            sessionLogParams.remoteid = "0"
            sessionLogParams.hiUserCount = session.hiUserTotalCount
            sessionLogParams.hiUserCountUsable = session.hiUserTotalCountUsable
        } else if (session is GroupChatSessionModel || session is DiscussChatSessionModel) {
            isMsgSession = true
        } else if (session is GotoSessionModel) {
            sessionLogParams.remoteid = session.businessId
        } else if (Session.ID.FolderOfficial == session.sessionId) {
            sessionLogParams.remoteid = "0"
        } else if (Session.ID.GAME_BOX == session.sessionId) {
            sessionLogParams.remoteid = "0"
        } else if (session is FriendNoticeSessionModel) {
            sessionLogParams.remoteid = "0"
        } else if (session is UniverseFoldSessionModel) {
            sessionLogParams.remoteid = "0"
            sessionLogParams.newsNumber = session.getUnreadCnt()
            sessionLogParams.messageText = session.desc
        } else {
            return
        }
        if (!sessionTagLogMap.isNullOrEmpty()) {
            sessionLogParams.sessionTagLogMap = sessionTagLogMap
        }
        exposureLog(isMsgSession, sessionLogParams)
    }

    /**
     * @param isMsgSession 是否是消息类的曝光，消息类包括p2p 群组 讨论组
     */
    private fun exposureLog(isMsgSession: Boolean, params: SessionLogParams) {
        if (isMsgSession && !isExposureMsgSession) {
            //如果是消息类型session，开关没有打开，不上报
            return
        }
        val whichItem = params.whichItem
        val remoteid = params.remoteid
        if (!isLogValid(whichItem, remoteid)) {
            return
        }
        SessionHelper.Log.logSessionExposure(params)
    }

    private fun isLogValid(whichItem: String, remoteid: String): Boolean {
        val key = whichItem + remoteid
        val currentTime = System.currentTimeMillis()
        //2s只曝光一次
        val lastExposureTime = lastExposureTimeMap[key]
        if (lastExposureTime != null && Math.abs(currentTime - lastExposureTime) < 2 * 1000) {
            return false
        }
        lastExposureTimeMap[key] = currentTime
        return true
    }
}