package com.immomo.momo.maintab.model;

import android.text.TextUtils;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.immomo.momo.service.bean.AvatarFrame;
import com.immomo.momo.util.DateUtil;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Transient;

import java.util.Date;
import org.greenrobot.greendao.annotation.Generated;

/**
 * Created by huang.liang<PERSON>e on 2017/4/1.
 * <p>
 * Momo Tech 2011-2017 © All Rights Reserved.
 */

@Entity(nameInDb = "active_user", generateConstructors = false)
public class ActiveUser {
    public static final int TYPE_COMMON = 10; //可长按，点击后消失，第一行文案为时间
    public static final int TYPE_NOT_DISMISS = 11; // 不可长按，点击后不消失,文案全由api控制（例如点点）
    public static final int TYPE_DISMISS = 12; // 可长按，点击后消失，文案全由api控制（例如生日）

    @Id(autoincrement = true)
    private Long id;

    @Expose
    private String momoid;
    @Expose
    private String name;
    @Expose
    private String avatar;
    @Expose
    private long start_time;
    @Expose
    private long end_time;
    @Expose
    private String tip_icon;
    @Expose
    @SerializedName("theme")
    private int type; //10:默认数据样式，11：功能样式（点点、生日）
    @Expose
    private String reason;
    @Expose
    private int priority;
    @Expose
    @SerializedName("goto")
    private String action;
    @Transient
    private AvatarFrame avatarFrame; // 头像挂件

    // 统计需求字段
    @Expose
    private String log_str;

    @Transient
    private String title;

    @Transient
    private int position;

    @Transient
    private boolean isExpose;

    public ActiveUser() {
    }

    public boolean isExpose() {
        return isExpose;
    }

    public void setExpose(boolean expose) {
        isExpose = expose;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public Long getId() {
        return id;
    }

    public String getMomoid() {
        return momoid;
    }

    public void setMomoid(String momoid) {
        this.momoid = momoid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public long getStart_time() {
        return start_time;
    }

    public void setStart_time(long start_time) {
        this.start_time = start_time;
    }

    public long getEnd_time() {
        return end_time;
    }

    public void setEnd_time(long end_time) {
        this.end_time = end_time;
    }

    public String getTip_icon() {
        return tip_icon;
    }

    public void setTip_icon(String tip_icon) {
        this.tip_icon = tip_icon;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getTitle() {
        if (TextUtils.isEmpty(title)) {
            Date time = DateUtil.parseTimeStampToDate(start_time);
            if (time != null) {
                title = DateUtil.betweenWithCurDate(time);
            } else {
                title = getName();
            }
        }
        return title;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLog_str() {
        return this.log_str;
    }

    public void setLog_str(String log_str) {
        this.log_str = log_str;
    }

    public AvatarFrame getAvatarFrame() {
        return avatarFrame;
    }

    public void setAvatarFrame(AvatarFrame avatarFrame) {
        this.avatarFrame = avatarFrame;
    }
}
