package com.immomo.momo.maintab.sessionlist.enterbar.usecase;

import androidx.annotation.Keep;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.immomo.momo.homepage.model.MillionEntranceInfo;

@Keep
public class SessionEnterBarResponse {

    @Expose
    @SerializedName("floatingWindow")
    private MillionEntranceInfo floatingWindow;

    @Expose
    @SerializedName("interval")
    private int interval;


    public MillionEntranceInfo getFloatingWindow() {
        return floatingWindow;
    }

    public void setFloatingWindow(MillionEntranceInfo floatingWindow) {
        this.floatingWindow = floatingWindow;
    }

    public int getInterval() {
        return interval;
    }

    public void setInterval(int interval) {
        this.interval = interval;
    }
}
