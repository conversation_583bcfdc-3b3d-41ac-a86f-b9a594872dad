package com.immomo.momo.maintab.sessionlist.util

import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.storage.kv.KV
import com.immomo.mmutil.task.MomoMainThreadExecutor
import com.immomo.momo.maintab.sessionlist.util.INoticeAnimItem.Companion.PRE_KEY_PLAY_MARK
import java.util.concurrent.TimeUnit

/**
 * CREATED BY liu.chong
 * AT 2021/6/25
 */
interface INoticeAnimManager {
    companion object {
        const val URL_INTERACT_NOTICE_ANIM =
            "https://s.momocdn.com/s1/u/decebbijf/icon_interact_notice.svga"
        const val URL_FRIEND_NOTICE_ANIM =
            "https://s.momocdn.com/s1/u/decebbijf/icon_friend_notice.svga"
        const val URL_HI_NOTICE_ANIM =
            "https://s.momocdn.com/s1/u/decebbijf/1625551652184-icon_hi_notice.svga"
    }

    fun bindRecyclerView(recyclerView: RecyclerView, adapter: RecyclerView.Adapter<*>)
    fun onResume()
    fun onPause()
}

interface INoticeAnimItem {
    companion object {
        /**
         * 互动通知
         */
        const val ID_INTERACTION_NOTICE = 1

        /**
         * 好友通知
         */
        const val ID_FRIEND_NOTICE = 2

        /**
         * 招呼通知
         */
        const val ID_HI_NOTICE = 3
        const val PRE_KEY_PLAY_MARK = "NOTICE_ANIM_"
    }

    fun itemIdentification(): Int

    /**
     * 成功发起播放，才会返回true
     * 正在播放中，返回false
     * 不满足播放条件，返回false
     */
    fun playNoticeAnim(): Boolean
    fun stopNoticeAnim(): Boolean
}

class NoticeAnimManager : INoticeAnimManager {
    private var isResumed = false

    companion object {
        const val TAG = "NOTICE_ANIM"
        fun markPlayed(itemIdentification: Int) {
            KV.saveUserValue(PRE_KEY_PLAY_MARK + itemIdentification, System.currentTimeMillis())
        }

        fun canPlay(itemIdentification: Int): Boolean {
            KV.getUserLong(
                PRE_KEY_PLAY_MARK + itemIdentification,
                0
            ).compareTo(
                System.currentTimeMillis() - TimeUnit.DAYS.toMillis(1)
            ).takeIf {
                it <= 0
            }?.let {
                return true
            } ?: return false
        }
    }

    var recyclerView: RecyclerView? = null
    var adapter: RecyclerView.Adapter<*>? = null
    var resumeStateNeedCheck = false

    /*Should be called when LifeCycle onCreate*/
    override fun bindRecyclerView(recyclerView: RecyclerView, adapter: RecyclerView.Adapter<*>) {
        this.adapter = adapter
        this.recyclerView = recyclerView
        adapter.registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
            override fun onChanged() {
                checkPlayAnim()
            }

            override fun onItemRangeChanged(positionStart: Int, itemCount: Int) {
                checkPlayAnim()
            }

            override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                checkPlayAnim()
            }
        })
        recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    checkPlayAnim()
                }
            }
        })
    }

    override fun onResume() {
        isResumed = true
        resumeStateNeedCheck = true
        recyclerView?.post {
            checkPlayAnim()
        }
    }

    /**
     * 产品需求，滑动的时候排在第一个的item即便是播放过，也不会播放下一个item的动画
     * 而切换页面回来后会播放下一个动画。
     * 所以页面切换会每一个都检测，哪个可以播放就播放哪一个。而滑动则仅播放第一个，第一个播放过了，就不播放了
     */
    private fun checkPlayAnim() {

        MomoMainThreadExecutor.cancelAllRunnables(TAG)
        MomoMainThreadExecutor.postDelayed(TAG, {
            performCheck(resumeStateNeedCheck)
            resumeStateNeedCheck = false
        }, 400)
    }

    fun performCheck(fromResume: Boolean) {
        if (isResumed.not()) {
            return
        }
        forEachVisibleItem {
            val itemIdentification = it.itemIdentification()
            if (itemIdentification < 0) {
                //item不具备条件
                return@forEachVisibleItem false
            }
            if (fromResume) {
                if (canPlay(itemIdentification) && it.playNoticeAnim()) {
                    return@forEachVisibleItem true
                }
                return@forEachVisibleItem false
            } else {
                if (canPlay(itemIdentification)) {
                    it.playNoticeAnim()
                }
                return@forEachVisibleItem true
            }
        }
    }

    private fun forEachVisibleItem(block: (INoticeAnimItem) -> Boolean) {
        recyclerView?.let {
            val linearLayoutManager = it.layoutManager as? LinearLayoutManager
            val firstVisiblePosition =
                linearLayoutManager?.findFirstCompletelyVisibleItemPosition().safe(-1)
            val lastVisiblePosition =
                linearLayoutManager?.findLastCompletelyVisibleItemPosition().safe(-1)
            if (firstVisiblePosition < 0 || firstVisiblePosition > lastVisiblePosition) {
                return
            }
            for (i in firstVisiblePosition..lastVisiblePosition) {
                linearLayoutManager?.findViewByPosition(i)?.let { item ->
                    recyclerView?.getChildViewHolder(item)
                }?.takeIf { holder ->
                    holder is INoticeAnimItem
                }?.castOrNull<INoticeAnimItem>()?.let { item ->
                    if (block(item)) {
                        return
                    }
                }
            }
        }
    }


    override fun onPause() {
        isResumed = false
        MomoMainThreadExecutor.cancelAllRunnables(TAG)
        forEachVisibleItem {
            if (it.stopNoticeAnim()) {
                markPlayed(it.itemIdentification())
                return@forEachVisibleItem true
            }
            return@forEachVisibleItem false
        }
    }
}