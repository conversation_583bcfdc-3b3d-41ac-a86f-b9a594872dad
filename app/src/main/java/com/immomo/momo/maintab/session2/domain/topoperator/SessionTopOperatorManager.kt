package com.immomo.momo.maintab.session2.domain.topoperator

import android.app.Activity
import android.view.View
import androidx.lifecycle.LifecycleCoroutineScope
import com.cosmos.mdlog.MDLog
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.model.businessmodel.PaginationResultFileCache
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.utils.UIUtils
import com.immomo.mmutil.FileUtil
import com.immomo.mmutil.StringUtils
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.momo.MomoKit
import com.immomo.momo.R
import com.immomo.momo.android.view.tips.TipManager
import com.immomo.momo.android.view.tips.tip.ITip
import com.immomo.momo.android.view.tips.triangle.BottomTriangleDrawable
import com.immomo.momo.maintab.session2.SessionTopOperatorAppConfigV1
import com.immomo.momo.maintab.session2.presentation.viewmodel.SessionListInnerViewModel
import com.immomo.momo.maintab.sessionlist.bean.SessionTopOperatorData
import com.immomo.momo.util.GsonUtils
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * 缓存运营位数据
 */
object SessionTopOperatorManager {

    const val TAG = "SessionTopOperatorItemModel"

    private const val FILE_TAG = "MomoInnerSessionTopOperator_"

    private const val KEY_IS_SHOWN_SESSION_TOP_OPERATOR_SEARCH_HINT =
        "key_is_shown_session_top_operator_search_hint" // 搜索引导

    private const val KEY_IS_SHOWN_SESSION_TOP_OPERATOR_GUIDE =
        "key_is_shown_session_top_operator_guide"

    /**
     * 搜索引导是否已经展示了
     */
    private var isSearchGuidShown = false

    private var saveJob: Job? = null

    /**
     * 是否展示过引导
     */
    @JvmStatic
    var isShownGuild = false

    var isABTestOpenSwitch = false // 是否打开了实验

    fun initABTestConfig() {
        reset()
        isABTestOpenSwitch = SessionTopOperatorAppConfigV1.isEnable()
        isShownGuild = isSessionGuidKVShow()
    }

    @JvmStatic
    fun clearSearchTipKV() {
        KV.removeUserValue(KEY_IS_SHOWN_SESSION_TOP_OPERATOR_SEARCH_HINT)
    }

    private fun saveSearchTipKVShow() {
        KV.saveUserValue(KEY_IS_SHOWN_SESSION_TOP_OPERATOR_SEARCH_HINT, true)
    }

    private fun isSearchTipKVShow() =
        KV.getUserBool(KEY_IS_SHOWN_SESSION_TOP_OPERATOR_SEARCH_HINT, false)

    @JvmStatic
    fun clearSessionGuidKV() {
        KV.removeUserValue(KEY_IS_SHOWN_SESSION_TOP_OPERATOR_GUIDE)
    }

    fun saveSessionGuidKV() {
        KV.saveUserValue(KEY_IS_SHOWN_SESSION_TOP_OPERATOR_GUIDE, true)
    }

    private fun isSessionGuidKVShow() =
        KV.getUserBool(KEY_IS_SHOWN_SESSION_TOP_OPERATOR_GUIDE, false) // 是否已经展示了引导

    @JvmStatic
    fun saveData(repData: String) {
        runBlocking {
            saveJob?.cancelAndJoin()
            saveJob = runBlocking {
                launch(MMDispatchers.User) {
                    kotlin.runCatching {
                        PaginationResultFileCache.saveData(getCacheFileName(), repData.safe())
                    }
                }
            }
        }
    }

    /**
     * 请求本地数据
     */
    fun getLocalData(): SessionTopOperatorData? {
        kotlin.runCatching {
            val file: File? =
                PaginationResultFileCache.getJsonFileForRead(getCacheFileName())
            if (file != null && file.exists()) {
                val jsonStr = FileUtil.readStr(file)
                if (!StringUtils.isEmpty(jsonStr)) {
                    val topOperatorData = GsonUtils.g().fromJson<SessionTopOperatorData>(
                        jsonStr, SessionTopOperatorData::class.java
                    )
                    topOperatorData.isFromLocalCache = true
                    return topOperatorData
                }
            }
        }.onFailure {
            MDLog.printErrStackTrace(FILE_TAG, it)
        }
        return null
    }

    fun removeCacheData() {
        GlobalScope.launch(MMDispatchers.User) {
            PaginationResultFileCache.clear(getCacheFileName())
        }
    }

    private fun getCacheFileName() = "${FILE_TAG}${MomoKit.getCurrentOrGuestMomoId()}"

    fun checkShowTip(tipView: View?) {
        kotlin.runCatching {
            tipView ?: return
            if (!isABTestOpenSwitch || isSearchGuidShown || isSearchTipKVShow()) {
                isSearchGuidShown = true
                return
            }
            MDLog.i(TAG, "checkShowTip")
            val context = tipView.context as? Activity
            if (context == null || TipManager.isDestroy(context)) {
                return
            }
            saveSearchTipKVShow()
            TipManager.bindActivity(context).checkViewCanShowTip(tipView) { v: View? ->
                kotlin.runCatching {
                    val triangleDrawable = BottomTriangleDrawable()
                    triangleDrawable.setColor(UIUtils.getColor(R.color.default_tip_color))
                    val padding = UIUtils.getPixels(12f)
                    if (TipManager.isDestroy(context)) {
                        return@checkViewCanShowTip
                    }
                    TipManager.bindActivity(context)
                        .setTriangles(null, null, null, triangleDrawable)
                        .setBackground(UIUtils.getDrawable(R.drawable.bg_corner_10dp_4e7fff))
                        .setTextColor(UIUtils.getColor(R.color.white))
                        .setTouchToHideAll(true)
                        .setMarginEdge(UIUtils.getPixels(15f))
                        .setTextPadding(padding, padding, padding, padding)
                        .showTipView(
                            tipView, "搜索功能移动到这里了哦",
                            UIUtils.getPixels(-7f),
                            UIUtils.getPixels(0f),
                            ITip.Triangle.TOP
                        )?.autoHide(3000L)
                }
            }
        }
    }

    private var curDataVersion = AtomicLong(-1L) // 当前数据的版本号

    private var isRequestingData = AtomicBoolean()

    private var isGetApiFilterLocalCache = AtomicBoolean() // 是否已经获取到了接口数据

    private var lastRequestTimeSkip = 0

    private var lastRequestDataTime = 0L

    private var requestTransparentParams : String? = "" // 请求透传数据

    private var isColdBoot = "1" // 标记是否是首次冷启动

    var repCacheData: SessionTopOperatorData? = null

    /**
     * 从服务器获取数据数据
     */
    fun getApiDataWithLocal(
        sessionListVm: SessionListInnerViewModel,
        lifecycleScope: LifecycleCoroutineScope,
        onLocalDataSuccess: (isFromLocalCache: Boolean, SessionTopOperatorData) -> Unit,
        onNoNeedGetData: (SessionTopOperatorData) -> Unit
    ) {
        val currentTimeMillis = System.currentTimeMillis()
        if (isRequestingData.get() || currentTimeMillis - lastRequestDataTime < lastRequestTimeSkip) {
            repCacheData?.also {
                onNoNeedGetData(it)
            }
            MDLog.i(TAG, "开始请求API数据被拦截")
            return
        }
        isRequestingData.set(true)
        lastRequestDataTime = currentTimeMillis
        MDLog.i(TAG, "开始请求API数据")
        val reqMap = hashMapOf<String, String>()
        reqMap["isColdBoot"] = isColdBoot
        reqMap["transportParams"] = requestTransparentParams ?: ""
        sessionListVm.getTopOperatorData(reqMap, {
            lastRequestTimeSkip = it.refreshIntervalSec * 1000
            isGetApiFilterLocalCache.set(true) // 获取到了接口数据，过滤掉本地保存的数据
            lifecycleScope.launch {
                requestTransparentParams = it.requestTempTransparentParams ?: ""
                if (curDataVersion.get() != it.version) {
                    MDLog.i(TAG, "使用API数据成功，数据为${it.version}")
                    onLocalDataSuccess(false, it)
                    repCacheData = it
                    curDataVersion.set(it.version) // 记录当前版本使用本地数据成功
                } else {
                    MDLog.i(TAG, "过滤API数据，数据为${it.version}")
                }
                isRequestingData.set(false) // 过滤正在请求数据的情况
                isColdBoot = "0"
            }
        }, {
            lastRequestDataTime = 0 // 请求失败需要再次请求
            isRequestingData.set(false)
            MDLog.i(TAG, "获取API数据失败${it.message}")
        })
        // 请求本地数据占位
        if (!isGetApiFilterLocalCache.get()) { // 没请求到本地数据时处理
            sessionListVm.getTopOperatorLocalData({
                lifecycleScope.launch {
                    if (curDataVersion.get() != it.version && !isGetApiFilterLocalCache.get()) {
                        curDataVersion.set(it.version) // 记录当前版本使用本地数据成功
                        MDLog.i(TAG, "使用本地数据成功，数据为${it.version}")
                        onLocalDataSuccess(true, it)
                        repCacheData = it
                    } else {
                        MDLog.i(TAG, "过滤本地数据，数据为${it.version}")
                    }
                }
            }, {
                MDLog.i(TAG, "获取本地数据失败，数据为空")
            })
        }
    }

    /**
     * 重置状态
     */
    fun reset() {
        isSearchGuidShown = false
        isABTestOpenSwitch = false
        isShownGuild = false
        repCacheData = null
        lastRequestDataTime = 0L
        lastRequestTimeSkip = 0
        isGetApiFilterLocalCache = AtomicBoolean()
        isRequestingData = AtomicBoolean()
        curDataVersion = AtomicLong(-1L)
        requestTransparentParams = ""
        isColdBoot = "1"
    }

}