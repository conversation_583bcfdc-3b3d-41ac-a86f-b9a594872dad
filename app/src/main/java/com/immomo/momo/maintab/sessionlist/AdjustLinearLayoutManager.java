package com.immomo.momo.maintab.sessionlist;

import android.content.Context;
import android.util.AttributeSet;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;


/**
 * 处理smoothScrollToPosition可以把需要滚动的item滚到顶部
 */
public class AdjustLinearLayoutManager extends LinearLayoutManager {

    private int scrollType = LinearSmoothScroller.SNAP_TO_START;
    private int time = 70;

    public AdjustLinearLayoutManager(Context context) {
        super(context);
    }

    public AdjustLinearLayoutManager(Context context, int orientation, boolean reverseLayout) {
        super(context, orientation, reverseLayout);
    }

    public AdjustLinearLayoutManager(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }


    public void setTotalTime(int time) {
        this.time = time;
    }

    public void setScrollType(@AdjustLinearSmoothScroller.ScrollType int scrollType) {
        this.scrollType = scrollType;
    }

    @Override
    public void smoothScrollToPosition(RecyclerView recyclerView, RecyclerView.State state, int position) {
        AdjustLinearSmoothScroller.setTime(time);
        AdjustLinearSmoothScroller scroller = new AdjustLinearSmoothScroller(recyclerView.getContext(), scrollType);
        scroller.setTargetPosition(position);
        startSmoothScroll(scroller);
    }

}
