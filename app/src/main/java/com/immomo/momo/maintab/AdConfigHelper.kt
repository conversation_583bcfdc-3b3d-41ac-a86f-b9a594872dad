package com.immomo.momo.maintab

import com.cosmos.mdlog.MDLog
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.momo.LogTag
import com.immomo.momo.businessmodel.usermodel.IUserModel
import com.immomo.momo.mvp.common.model.ModelManager
import com.immomo.momo.protocol.http.AdApi
import com.immomo.momo.service.bean.UserProfileConfigs
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.json.JSONArray
import java.util.*

object AdConfigHelper {

    fun updateAllowBanners() = GlobalScope.launch(MMDispatchers.User) {
        try {
            val profileConfigs: UserProfileConfigs? = AdApi.getInstance().updateAllowBanners()

            // 保持原解析逻辑，下发了"allow_banners"JSONArray才更新
            if (profileConfigs != null) {
                val userModel = ModelManager.getInstance().getModel(IUserModel::class.java)
                userModel.saveShopSetting(profileConfigs)
            }
        } catch (e: Exception) {
            MDLog.printErrStackTrace(LogTag.COMMON, e)
        }
    }

    fun parseUserProfileConfigs(bannersArray: JSONArray?): UserProfileConfigs? {
        if (bannersArray == null) return null

        val profileConfigs = UserProfileConfigs().apply {
            allowsAdTypes = ArrayList()
        }
        for (i in 0 until bannersArray.length()) {
            profileConfigs.allowsAdTypes.add(bannersArray.optString(i))
        }
        return profileConfigs
    }

}