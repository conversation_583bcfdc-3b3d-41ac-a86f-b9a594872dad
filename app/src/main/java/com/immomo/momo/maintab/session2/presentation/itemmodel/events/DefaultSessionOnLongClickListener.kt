package com.immomo.momo.maintab.session2.presentation.itemmodel.events

import android.view.View
import com.cosmos.mdlog.MDLog
import com.immomo.moarch.account.AccountKit
import com.immomo.momo.greet.GreetHelper
import com.immomo.momo.maintab.session2.data.manager.SessionKey.Companion.fromString
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.maintab.session2.domain.model.type.FriendNoticeSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.GiftSayHiSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.GotoSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.SayHiSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.UniverseFoldSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.UserChatSessionModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.SessionItemModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.SessionOnLongClickListener
import com.immomo.momo.maintab.sessionlist.SessionListPresenter
import com.immomo.momo.maintab.sessionlist.util.SessionHelper
import com.immomo.momo.mk.util.BusinessNotifySwitchUtils

interface OnShowSessionLongClickDialog {
    fun showDialog(
        viewHolder: SessionItemModel.SessionViewHolder,
        actionList: Array<String>,
        dontNeedSubtitle: Boolean,
        positionInAdapter: Int,
        typeGotoSessionId: String?
    )
}

internal fun SessionItemModel.SessionViewHolder.logLongClick(
    session: SessionModel
) {
    val preContent = getPreContent()
    val onlineText = getOnlineText()
    val timeText = getTimeStr()
    val isRecommend: Boolean = session.baseInfo.recommendTime > 0

    val sessionLogParams = SessionHelper.SessionLogParams(
        SessionHelper.Log.getWhichItemByType(session),
        session.baseInfo.unreadMessageCount,
        adapterPosition,
        session.baseInfo.sessionId,
        session.baseInfo.silentMessageCount > 0,
        if (session is GotoSessionModel) session.text else null,
        if (session is SayHiSessionModel) session.hiUserTotalCount else if (session is GiftSayHiSessionModel) session.hiUserTotalCount else 0,
        if (session is SayHiSessionModel) session.hiUserTotalCountUsable else if (session is GiftSayHiSessionModel) session.hiUserTotalCountUsable else 0,
        preContent ?: "",
        onlineText ?: "",
        timeText ?: "",
        isRecommend
    )
    SessionHelper.Log.logSessionLongClick(sessionLogParams)
}


open class DefaultSessionOnLongClickListener(val showDialog: OnShowSessionLongClickDialog) :
    SessionOnLongClickListener {
    override fun onLongClicked(
        view: View,
        viewHolder: SessionItemModel.SessionViewHolder,
        session: SessionModel,
        adapterPosition: Int
    ) {
        MDLog.d("SessionLongClick", "$session")
        var typeGotoSessionId: String? = null
        var dontNeedSubTitle = false
        val actionList = when {
            session is UserChatSessionModel && session.baseInfo.unreadMessageCount > 0 -> {
                if (fromString(session.sessionKeyStr).isMaskChat()) {
                    arrayOf(SessionListPresenter.STR_MASK_DELETE)
                } else if (!AccountKit.getAccountManager().isOnline) {
                    arrayOf(SessionListPresenter.STR_DELETE)
                } else {
                    arrayOf(SessionListPresenter.STR_PEEK, SessionListPresenter.STR_DELETE)
                }
            }
            session is FriendNoticeSessionModel -> {
                arrayOf(SessionListPresenter.STR_DELETE_FRIEND_NOTICE)
            }
            session is GotoSessionModel -> {
                typeGotoSessionId = session.businessId
                dontNeedSubTitle = true
                if (session.source == 1) {
                    val isTypeGotSessionReceiveMsg =
                        BusinessNotifySwitchUtils.getIntance().getSwitchStatus(typeGotoSessionId)
                    arrayOf(
                        if (isTypeGotSessionReceiveMsg) SessionListPresenter.STR_RECEIVE_MSG_CLOSE
                        else SessionListPresenter.STR_RECEIVE_MSG_OPEN,
                        SessionListPresenter.STR_DELETE
                    )
                } else arrayOf(SessionListPresenter.STR_DELETE)
            }
            session is SayHiSessionModel -> {
                if (GreetHelper.isGreetNotRemindSettingMode()) {
                    arrayOf(SessionListPresenter.STR_GREET_NOT_REMINDER_OFF)
                } else {
                    arrayOf(SessionListPresenter.STR_GREET_NOT_REMINDER_ON)
                }
            }
            session is GiftSayHiSessionModel -> { // 礼物招呼
                if (GreetHelper.isGreetNotRemindSettingMode()) {
                    arrayOf(SessionListPresenter.STR_GREET_NOT_REMINDER_OFF)
                } else {
                    arrayOf(SessionListPresenter.STR_GREET_NOT_REMINDER_ON)
                }
            }
            session is UniverseFoldSessionModel -> { // 小宇宙长按
                return
            }
            else -> {
                if (fromString(session.sessionKeyStr).isMaskChat()) {
                    arrayOf(SessionListPresenter.STR_MASK_DELETE)
                } else {
                    arrayOf(SessionListPresenter.STR_DELETE)
                }

            }
        }
        showDialog.showDialog(
            viewHolder,
            actionList,
            dontNeedSubTitle,
            adapterPosition,
            typeGotoSessionId
        )
    }
}