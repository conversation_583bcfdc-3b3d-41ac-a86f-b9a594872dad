package com.immomo.momo.maintab.session2.presentation.itemmodel

import android.annotation.SuppressLint
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import com.immomo.android.mm.cement2.AsyncCementModel
import com.immomo.android.mm.cement2.CementViewHolder
import com.immomo.android.mm.cement2.IViewHolderCreator
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.storage.preference.SPKeys
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.R
import com.immomo.momo.maintab.session2.domain.model.SessionNoticeInfoModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.events.SessionDraggableViewTouchListener
import com.immomo.momo.maintab.sessionlist.util.INoticeAnimItem
import com.immomo.momo.maintab.sessionlist.util.INoticeAnimManager
import com.immomo.momo.maintab.sessionlist.util.NoticeAnimManager
import com.immomo.momo.maintab.sessionlist.util.SessionHelper
import com.immomo.momo.maintab.view.OptSVGAListener
import com.immomo.momo.message.view.DragBubbleView
import com.immomo.momo.mvp.maintab.mainbubble.MainBubbleViewImpl
import com.immomo.momo.performance.SimpleViewStubProxy
import com.immomo.svgaplayer.view.MomoSVGAImageView

class SessionNoticeItemModel(
    val info: SessionNoticeInfoModel,
    val onTouchListener: SessionDraggableViewTouchListener?,
    val onClickListener: () -> Unit
) : AsyncCementModel<SessionNoticeInfoModel, SessionNoticeItemModel.ViewHolder>(info) {

    init {
        id("session-notice")
    }

    override val layoutRes: Int
        get() = R.layout.include_session_notice_entry

    override val viewHolderCreator: IViewHolderCreator<ViewHolder>
        get() = object : IViewHolderCreator<ViewHolder> {
            override fun create(view: View): ViewHolder = ViewHolder(view)
        }

    override fun unbind(holder: ViewHolder) {
        super.unbind(holder)
        holder.stopNoticeAnim()
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun bindData(holder: ViewHolder) {
        super.bindData(holder)
        holder.noticeIvUnreadCount.setOnTouchListener { v, event ->
            onTouchListener?.onTouch(
                v,
                event,
                holder.adapterPosition,
                v,
                DragBubbleView.DRAG_FROM_SESSION_HEADER
            ) ?: false
        }

        var source = if (info.gotoTabType == 1) {
            "feed_interact"
        } else {
            "microcosm"
        }
        holder.itemView.setOnClickListener {
            SessionHelper.SessionLogParams(
                "notice", info.count, holder.adapterPosition, "0",
                false, null, 0, 0,
                "", "", "", false, "", source
            ).also {
                SessionHelper.Log.logSessionClick(it)
            }
            onClickListener()
        }

        holder.refreshNotice(info)
    }


    private fun ViewHolder.refreshNotice(info: SessionNoticeInfoModel) {
        val title = KV.getUserStr(SPKeys.User.NoticeMsg.KEY_INTERACTION_NOTICE_TITLE, "")
        if (TextUtils.isEmpty(title)) {
            noticeTvTitle.setText(R.string.session_notice_title)
        } else {
            noticeTvTitle.text = title
        }
        noticeTvTitle.setTextColor(
            UIUtils.getColor(
                if (com.immomo.momo.util.MomoKit.isDarkMode(noticeTvDesc.context))
                    R.color.color_80fff else R.color.color_text_3b3b3b
            )
        )
        if (info.count > 0) {
            noticeIvUnreadCount.text = MainBubbleViewImpl.getBubbleMax999String(info.count)
            noticeIvUnreadCount.visibility = View.VISIBLE
        } else {
            noticeIvUnreadCount.text = ""
            noticeIvUnreadCount.visibility = View.GONE
        }
        if (!TextUtils.isEmpty(info.desc)) {
            noticeTvDesc.text = info.desc
        } else {
            noticeTvDesc.setText(R.string.session_notice_desc_default)
        }
    }


    class ViewHolder(itemView: View) : CementViewHolder(itemView), INoticeAnimItem {
        val noticeTvTitle: TextView = itemView.findViewById(R.id.notice_tv_title)
        val noticeIvUnreadCount: TextView = itemView.findViewById(R.id.notice_iv_unread_count)
        val noticeTvDesc: TextView = itemView.findViewById(R.id.notice_tv_desc)
        val mHeadAnim: SimpleViewStubProxy<MomoSVGAImageView> =
            SimpleViewStubProxy(itemView.findViewById(R.id.session_avatar_anim_stub))

        /**
         * 获取当前是否显示未读气泡
         *
         * @return 0-没有气泡 1-有气泡
         */
        fun isBubbleShowed(): Int {
            return if (noticeIvUnreadCount.getVisibility() == View.GONE) 0 else 1
        }

        override fun itemIdentification(): Int {
            return INoticeAnimItem.ID_INTERACTION_NOTICE
        }

        override fun playNoticeAnim(): Boolean {
            val bubbleShowed = isBubbleShowed() == 1
            if (!bubbleShowed) {
                return false
            }
            val anim: MomoSVGAImageView = mHeadAnim.getStubView()
            if (anim.isAnimating) {
                return false
            }
            anim.startSVGAAnimWithListener(
                INoticeAnimManager.URL_INTERACT_NOTICE_ANIM,
                2,
                object : OptSVGAListener(2) {
                    override fun onCompleted() {
                        NoticeAnimManager.markPlayed(itemIdentification())
                    }
                }
            )
            return true
        }

        override fun stopNoticeAnim(): Boolean {
            if (!mHeadAnim.isInflate()) {
                return false
            }
            val anim: MomoSVGAImageView = mHeadAnim.getStubView()
            if (anim.isAnimating) {
                anim.stopAnimCompletely()
                return true
            }
            return false
        }
    }
}
