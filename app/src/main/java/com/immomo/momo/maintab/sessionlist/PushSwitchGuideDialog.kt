package com.immomo.momo.maintab.sessionlist

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.view.View
import android.view.ViewTreeObserver
import android.view.Window
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.immomo.framework.imageloader.ImageLoaderX
import com.immomo.framework.imageloader.ImageType
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.storage.preference.SPKeys
import com.immomo.framework.utils.UIUtils
import com.immomo.mmstatistics.event.ClickEvent
import com.immomo.mmstatistics.event.Event
import com.immomo.mmstatistics.event.ExposureEvent
import com.immomo.momo.R
import com.immomo.momo.permission.PermissionUtil
import com.immomo.momo.sessionnotice.bean.TipsInfoCard
import com.immomo.momo.statistics.EVAction
import com.immomo.momo.statistics.EVPage
import com.immomo.momo.util.MomoKit

class PushSwitchGuideDialog(context: Context, val source: Int = SOURCE_SESSION) : Dialog(context, R.style.customDialog), View.OnClickListener {

    companion object {
        const val SOURCE_SESSION = 0//session
        const val SOURCE_FLASH = 1//闪聊
    }

    private var guideContainer: View
    private var ivAlbum: ImageView
    private var tvTitle: TextView
    private var tvDesc: TextView
    private var btnOk: Button
    private var cancelTv: TextView

    init {
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        setCancelable(false)
        setCanceledOnTouchOutside(false)

        setContentView(R.layout.dialog_push_switch_guide)

        guideContainer = findViewById(R.id.guide_container)
        ivAlbum = findViewById(R.id.iv_album)
        tvTitle = findViewById(R.id.tv_title)
        tvDesc = findViewById(R.id.tv_desc)
        btnOk = findViewById(R.id.btn_ok)
        cancelTv = findViewById(R.id.cancel_tv)
        btnOk.setOnClickListener(this)
        cancelTv.setOnClickListener(this)
        guideContainer.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                guideContainer.viewTreeObserver.removeOnGlobalLayoutListener(this)
                val params = guideContainer.layoutParams
                params?.let {
                    it.width = (UIUtils.getScreenWidth() * 0.84f).toInt()
                    guideContainer.layoutParams = it
                }
            }
        })
    }

    fun setData(card: TipsInfoCard) = with(card) {
        if (!icon.isNullOrEmpty()) {
            ImageLoaderX.load(icon).type(ImageType.IMAGE_TYPE_URL).cornerRadius(20, 20, 0, 0).into(ivAlbum)
        }
        tvTitle.text = title
        tvDesc.text = desc
        btnOk.text = buttonText
    }

    override fun show() {
        super.show()
        if (source == SOURCE_SESSION) {
            logExposure()
        } else if (source == SOURCE_FLASH) {
            logFlashExposure()
        }
    }

    private fun logExposure() = ExposureEvent.create(ExposureEvent.Type.Normal)
            .page(EVPage.Msg.Chatlist)
            .action(EVAction.Window.PushSettingAll)
            .submit()

    private fun logFlashExposure() = ExposureEvent.create(ExposureEvent.Type.Normal)
            .page(EVPage.Msg.LiveChat)
            .action(EVAction.Float.LivechatPhoneNotice)
            .submit()

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.btn_ok -> {
                if (source == SOURCE_FLASH) {
                    logFlashClick(EVAction.Float.LivechatPhoneNoticeAccept)
                } else if (source == SOURCE_SESSION) {
                    logClick(EVAction.Window.PushSettingOpen)
                }
                val activity = context as? Activity
                if (PermissionUtil.biggerAndroid13() && activity is Activity) {
                    PermissionUtil.getInstance().check13Notification(activity)
                } else {
                    MomoKit.gotoApplicationNotifySettings()
                }
                dismiss()
            }
            R.id.cancel_tv -> {
                if (source == SOURCE_FLASH) {
                    logFlashClick(EVAction.Float.LivechatPhoneNoticeRefuse)
                    //拒绝，7天后再提示
                    KV.saveUserValue(SPKeys.PushGuide.KEY_FLASH_NEXT_DIALOG_PUSH_SWITCH_GUIDE_TIME, System.currentTimeMillis() + 7 * 24 * 3600000L)
                } else if (source == SOURCE_SESSION) {
                    logClick(EVAction.Window.PushSettingClose)
                }
                dismiss()
            }
        }
    }

    private fun logClick(action: Event.Action) = ClickEvent.create()
            .page(EVPage.Msg.Chatlist)
            .action(action)
            .submit()

    private fun logFlashClick(action: Event.Action) = ClickEvent.create()
            .page(EVPage.Msg.LiveChat)
            .action(action)
            .submit()

}