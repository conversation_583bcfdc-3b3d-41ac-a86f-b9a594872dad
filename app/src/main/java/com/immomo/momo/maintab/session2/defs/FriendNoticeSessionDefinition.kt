package com.immomo.momo.maintab.session2.defs

import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.momo.maintab.session2.SessionContentParser
import com.immomo.momo.maintab.session2.SessionDefinition
import com.immomo.momo.maintab.session2.data.database.SessionContent
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.type.FriendNoticeSessionModel
import com.immomo.momo.message.bean.FriendNoticeNew
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.sessions.SessionService
import com.squareup.moshi.JsonClass
import java.util.*

@JsonClass(generateAdapter = true)
class FriendNoticeContent(
    var text: String? = null
) : SessionContent("friendNotice")

// "好友提醒"
class FriendNoticeSessionDefinition :
    SessionDefinition<FriendNoticeContent, FriendNoticeSessionModel>(
        "friendNotice",
        SessionContentParser.moshiParser()
    ) {
    override fun createContent(): FriendNoticeContent {
        return FriendNoticeContent()
    }

    override fun generateId(data: Any?): String? {
        return when (data) {
            is FriendNoticeNew -> Session.ID.FriendNotice
            else -> super.generateId(data)
        }
    }

    override fun syncSession(session: SessionEntity, data: Any?): Boolean {
        if (super.syncSession(session, data)) return true

        val content = session.content.castOrNull<FriendNoticeContent>() ?: return false
        if (data !is FriendNoticeNew) return false

        session.lastFetchTime = data.time.time
        session.silentMessageCount += 1

        content.text = data.text
        return true
    }

    override fun isTransformCompatible(oldSessionType: Int, oldSessionId: String): String? {
        return if (oldSessionType == Session.TYPE_FRIEND_NOTICE)
            Session.ID.FriendNotice else null
    }

    override fun transformOldSession(id: String): SessionEntity? {
        val oldSession = SessionService.getInstance().getSession(id) ?: return null

        val session = createSession(id)
        val content = session.content.castOrNull<FriendNoticeContent>() ?: return null

        session.lastFetchTime = oldSession.fetchtime?.time ?: System.currentTimeMillis()
        session.silentMessageCount = oldSession.silentMessageCount

        content.text = oldSession.gotoText
        return session
    }

    override fun saveOldSession(session: SessionEntity) {
        val content = session.content.castOrNull<FriendNoticeContent>() ?: return

        SessionService.getInstance().updateSession(session.sessionId) {
            it.type = Session.TYPE_FRIEND_NOTICE
            it.chatId = session.sessionId

            it.setOrderid(session.orderId)
            it.fetchtime = Date(session.lastFetchTime)
            it.unreadMessageCount = session.unreadMessageCount
            it.silentMessageCount = session.silentMessageCount

            it.gotoText = content.text
            true
        }
    }

    override fun FriendNoticeContent.contentToModel(
        baseInfo: BaseSessionInfo
    ): FriendNoticeSessionModel = FriendNoticeSessionModel(baseInfo, text.safe())


    companion object {
        @JvmField
        val Type = "friendNotice"

        @JvmField
        val KEY_FRIEND_NOTICE = SessionKey(Type, Session.ID.FriendNotice)
    }
}