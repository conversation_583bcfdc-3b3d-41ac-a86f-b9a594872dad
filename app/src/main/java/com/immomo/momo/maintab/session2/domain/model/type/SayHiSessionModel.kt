package com.immomo.momo.maintab.session2.domain.model.type

import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.SessionModel

data class SayHiSessionModel(
    override val baseInfo: BaseSessionInfo,
    val hiUserTotalCount: Int,
    val hiUserTotalCountUsable: Int,
    val desc: String,
    val hasRedPacketTxt: <PERSON>olean,
    val hasGiftTag: Boolean     // 是否在新招呼实验有礼物招呼
) : SessionModel

