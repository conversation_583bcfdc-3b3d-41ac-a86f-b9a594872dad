package com.immomo.momo.maintab.session2.presentation.activity

import android.content.Context
import android.content.Intent
import com.immomo.android.mm.cement2.CementModel
import com.immomo.android.mm.cement2.CementViewHolder
import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.android.mm.kobalt.presentation.viewmodel.withState
import com.immomo.lcapt.evlog.EVLog
import com.immomo.momo.R
import com.immomo.momo.maintab.session2.IFoldSessionLog
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.data.manager.SessionMetadata
import com.immomo.momo.maintab.session2.defs.FoldSessionDefinition
import com.immomo.momo.maintab.session2.domain.interactor.SessionFilter
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.SessionItemModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.SessionItemModel.SessionViewHolder
import com.immomo.momo.maintab.session2.presentation.viewmodel.SessionListState
import com.immomo.momo.maintab.session2.utils.SessionFoldHelper
import com.immomo.momo.message.activity.ChatActivity
import com.immomo.momo.service.bean.FolderType
import com.immomo.momo.util.MomoKit

class FoldMsgSessionListActivity : CommonSessionListActivity() {
    companion object {
        @JvmStatic
        fun start(context: Context) {
            val starter = Intent(context, FoldMsgSessionListActivity::class.java)
            context.startActivity(starter)
        }
    }

    private val backBt by lazy {
        findViewById(R.id.fold_session_header_back)
    }

    class MsgFoldFilter : SessionFilter {
        override fun filterMetadata(metadata: SessionMetadata): Boolean {
            return metadata.foldType == FolderType.FOLDED_MSG && !metadata.markedAsDeleted
        }

        override fun filterSessionModel(sessionModel: SessionModel): Boolean {
            return sessionModel.baseInfo.foldType == FolderType.FOLDED_MSG && !sessionModel.baseInfo.markedAsDeleted
        }
    }

    override fun layoutResID(): Int = R.layout.activity_fold_msg_session_list

    override fun kobaltRecyclerViewID(): Int = R.id.fold_session_session_list
    override fun initViews() {
        fullScreenOfStatusBar()
        fullScreenOfNavigationBar()
        super.initViews()
        backBt.setOnClickListener {
            finish()
        }
    }

    override fun isLightTheme(): Boolean {
        return !MomoKit.isDarkMode(this)
    }

    override fun initSubscribes() {
        super.initSubscribes()
        sessionListViewModel.selectSubscribe(SessionListState::onSessionRemoved) {
            it.fold({}) {
                withState(sessionListViewModel) {
                    if (it.sessionList.isEmpty()) {
                        SessionManager.get().deleteSession(FoldSessionDefinition.KEY_FOLDED_MSG)
                    } else {
                        SessionFoldHelper.notifyMsgFoldSessionUpdateLastMsg()
                    }
                }
            }
        }
    }

    override fun dealLongClickMenuAction(action: String, model: SessionModel) {
        when (action) {
            ACTION_DELETE -> {
                sessionListViewModel.deleteSession(model.sessionKey)
            }
        }
    }

    override fun onInterruptSessionExposure(
        model: CementModel<*>?,
        position: Int,
        holder: CementViewHolder
    ): Boolean {
        model.castOrNull<SessionItemModel<SessionModel>>()
            ?.state?.also {
                EVLog.create(IFoldSessionLog::class.java)
                    .logFoldSessionShow(
                        "personal",
                        holder.castOrNull<SessionViewHolder>()
                            ?.contentView?.text?.toString()
                            ?.substringAfter("km·") ?: "",
                        it.sessionId
                    )
            }

        return true
    }

    override fun onSessionClick(
        session: SessionModel,
        adapterPosition: Int,
        viewHolder: CementViewHolder
    ): Boolean {
        EVLog.create(IFoldSessionLog::class.java)
            .logFoldSessionClick(
                "personal",
                viewHolder.castOrNull<SessionViewHolder>()
                    ?.contentView?.text?.toString()
                    ?.substringAfter("km·") ?: "",
                session.baseInfo.sessionId
            )
        val chatIntent = Intent(this, ChatActivity::class.java)
        chatIntent.putExtra(ChatActivity.REMOTE_USER_ID, session.sessionId)
        startActivity(chatIntent)
        return true
    }
}