package com.immomo.momo.maintab.model

import androidx.annotation.Keep
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import java.io.Serializable

/**
 * 集火花标签
 */
@Keep
data class TextIconSessionTag(
    @Expose @SerializedName("text") val text: String? = "",
    @Expose @SerializedName("icon") val icon: String? = "",
    @Expose @SerializedName("bg_colors") val bgColors: List<String>?
) : Serializable