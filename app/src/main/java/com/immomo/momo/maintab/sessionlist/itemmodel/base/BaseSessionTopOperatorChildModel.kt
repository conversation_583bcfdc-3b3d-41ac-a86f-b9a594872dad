package com.immomo.momo.maintab.sessionlist.itemmodel.base

import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.cement.CementModel
import com.immomo.framework.cement.CementViewHolder
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.kotlin.ImageLoadingListener
import com.immomo.framework.kotlin.ImageType
import com.immomo.framework.rxjava.interactor.CommonSubscriber
import com.immomo.mmutil.task.MomoMainThreadExecutor
import com.immomo.momo.R
import com.immomo.momo.android.view.RoundCornerFrameLayout
import com.immomo.momo.gotologic.GotoDispatcher
import com.immomo.momo.maintab.sessionlist.bean.CardBgImgData
import com.immomo.momo.maintab.sessionlist.bean.CardCommonDataNightDark
import com.immomo.momo.maintab.sessionlist.bean.CardItemData
import com.immomo.momo.maintab.sessionlist.bean.CardTitleData
import com.immomo.momo.maintab.sessionlist.usecase.SessionTopActionUseCase
import com.immomo.momo.util.ColorUtils
import com.immomo.momo.util.MomoKit
import java.util.concurrent.atomic.AtomicBoolean

abstract class BaseSessionTopOperatorChildModel<T : CementViewHolder?>(
    open val itemModeData: CardItemData,
    open var runGuideAnimation: Boolean = false
) : CementModel<T>() {

    open fun refreshHolderView() {

    }

    open fun onFragmentVisible(isResume: Boolean) {

    }

    fun setCommonTitle(itemTitle: TextView, cardTitleData: CardTitleData?) {
        if (cardTitleData == null) {
            itemTitle.visibility = View.INVISIBLE
            return
        }
        itemTitle.visibility = View.VISIBLE
        itemTitle.text = cardTitleData.text.safe()
        if (cardTitleData.bold == 1) {
            val typeface = Typeface.create(Typeface.DEFAULT, Typeface.BOLD)
            itemTitle.typeface = typeface
        } else {
            val typeface = Typeface.create(Typeface.DEFAULT, Typeface.NORMAL)
            itemTitle.typeface = typeface
        }
        val textColor = ColorUtils.parseColor(
            if (MomoKit.isDarkMode()) cardTitleData.darkColor.safe() else cardTitleData.color.safe(),
            R.color.color_323333_to_80f
        )
        itemTitle.setTextColor(textColor)
    }

    /**
     * 设置按钮的 UI
     */
    fun setButtonView(btnContainer: RoundCornerFrameLayout, btnTitle: TextView) {
        val cardTitleData = itemModeData.button ?: run {
            btnContainer.visibility = View.GONE
            return
        }
        val darkMode = MomoKit.isDarkMode()
        btnTitle.text = cardTitleData.text.safe()
        val textColorData = cardTitleData.textColor
        val textColor = ColorUtils.parseColor(
            if (darkMode) textColorData?.dark.safe() else textColorData?.light.safe(),
            R.color.color_323333_to_80f
        )
        btnTitle.setTextColor(textColor)
        val bgColorData = cardTitleData.bgColor
        val bgColor = ColorUtils.parseColor(
            if (darkMode) bgColorData?.dark.safe() else bgColorData?.light.safe(),
            R.color.color_70w_to_10w
        )
        btnContainer.setBackgroundColor(bgColor)
        val btnAction = getSafeGoto(cardTitleData.action)
        if (btnAction.isNotBlank()) {
            btnContainer.isEnabled = true
            btnContainer.isClickable = true
            btnContainer.setOnClickListener {
                GotoDispatcher.action(btnAction, btnContainer.context).execute()
            }
        } else {
            btnContainer.isEnabled = false
            btnContainer.isClickable = false
            btnContainer.setOnClickListener(null)
        }
    }

    /**
     * 设置背景颜色
     */
    fun setViewBgColor(btnContainer: View, bgColorData: CardCommonDataNightDark?) {
        val darkMode = MomoKit.isDarkMode()
        val bgColor = ColorUtils.parseColor(
            if (darkMode) bgColorData?.dark.safe() else bgColorData?.light.safe(),
            R.color.color_70w_to_10w
        )
        btnContainer.setBackgroundColor(bgColor)
    }

    /**
     * 这只静态背景
     */
    fun showCommonStaticIcon(
        imgView: ImageView,
        bgImgData: CardBgImgData?,
        listener: ImageLoadingListener<Drawable>? = null
    ) {
        bgImgData?.also {
            val staticImg = if (MomoKit.isDarkMode()) {
                it.dark?.staticImg
            } else {
                it.light?.staticImg
            }
            showCommonStaticIcon(imgView, staticImg, listener)
        }
    }

    fun showCommonStaticIcon(
        staticImgView: ImageView,
        imgUrl: String?,
        listener: ImageLoadingListener<Drawable>? = null
    ) {
        if (imgUrl.safe().isNotBlank()) {
            ImageLoader.load(imgUrl).imageType(ImageType.URL).listener(listener).into(staticImgView)
        }
    }

    /**
     * 获取安全的 goto
     */
    fun getSafeGoto(customGoto: String?): String {
        var finalGoto = customGoto.safe()
        if (finalGoto.isBlank()) {
            finalGoto = itemModeData.action.safe()
        }
        return finalGoto
    }

    open fun release() {

    }

}