package com.immomo.momo.maintab.session2.defs

import com.cosmos.mdlog.MDLog
import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.android.module.specific.data.mapper.optionMap
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.utils.TimeUtils
import com.immomo.momo.impaas.common.CommonIMPaasHandler
import com.immomo.momo.impaas.common.IMPaasCons
import com.immomo.momo.impaas.common.ext.foldType
import com.immomo.momo.impaas.common.ext.lastMsgById
import com.immomo.momo.impaas.common.ext.onSessionRemove
import com.immomo.momo.impaas.common.ext.sessionOnReceiveMessage
import com.immomo.momo.maintab.session2.SessionContentParser
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.data.manager.SessionMessage
import com.immomo.momo.maintab.session2.data.manager.UnreadCountMessageInterceptor
import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.type.UserChatSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.UserChatTagModel
import com.immomo.momo.maintab.session2.domain.model.type.UserOnlineTagModel
import com.immomo.momo.maintab.session2.sessionKey
import com.immomo.momo.message.helper.SessionTextHelper
import com.immomo.momo.protocol.imjson.saas.common.BidHandlersManager
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.sessions.LastMsgCache

/**
 * CREATED BY liu.chong
 * AT 2024/5/7
 * SessionKey:"ps_bid&action$momoid" ex. "ps_biz_brainMatch&brain_match_msg$342411895"
 * 但sessionKey对下划线有逻辑影响，故转义后为："ps_biz$brainMatch&brain$match$msg$342411895",用来表示session类型、business、action及陌陌id
 *
 * 注意，后续需要交由handler中处理逻辑的，可以参考lastMsgById的处理方式，在ISessionInfoHandler中增加接口，为当前definition提供实现
 */
class PaasSessionDefinition : ChatSessionDefinition<UserChatContent, UserChatSessionModel>(
    Type,
    SessionContentParser.moshiParser()
) {
    companion object {
        const val Type = "ps"
        fun isPaasSession(sessionType: String): Boolean {
            return sessionType == Type
        }

        fun sessionId(business: String, action: String, id: String): String {
            return "${business.replace("_", "$")}&${action.replace("_", "$")}$$id"
        }

        fun sessionKey(business: String, action: String, id: String): SessionKey {
            return SessionKey("ps", sessionId(business, action, id))
        }

        fun businessFromSessionId(sessionId: String): String {
            return sessionId.substringBeforeLast("$").split("&")[0].replace("$", "_")
        }

        fun actionFromSessionId(sessionId: String): String {
            return sessionId.substringBeforeLast("$").split("&")[1].replace("$", "_")
        }

        fun userIdFromSessionId(sessionId: String): String {
            return sessionId.substringAfterLast("$")
        }

        fun findBidHandlerFromSessionId(sessionId: String): CommonIMPaasHandler {
            return BidHandlersManager.findHandler(
                businessFromSessionId(sessionId),
                actionFromSessionId(sessionId)
            ).castOrNull<CommonIMPaasHandler>()
                ?: throw IllegalStateException("sessionId:$sessionId 未匹配到预定义的CommonImPaasHandler")
        }

        fun SessionEntity.mapBidHandler(): CommonIMPaasHandler {
            return findBidHandlerFromSessionId(
                sessionId
            )
        }
    }

    override fun createSession(id: String): SessionEntity {
        val bid = businessFromSessionId(id)
        val action = actionFromSessionId(id)
        val momoId = userIdFromSessionId(id)
        MDLog.d(IMPaasCons.TAG, "创建Session，bid:$bid,action:$action,momoId:$momoId")
        return super.createSession(id)
    }

    override fun getLastMessage(session: SessionEntity): Message? {
        return session.mapBidHandler().lastMsgById(
            userIdFromSessionId(
                session.sessionId
            )
        )
    }

    override fun onClearUnread(session: SessionEntity) {
        super.onClearUnread(session)
        session.unreadMessageCount = 0
        session.silentMessageCount = 0
    }

    override fun onReloadChatInfo(id: String, session: SessionEntity, forceReload: Boolean) {
        //用户头像，昵称，相关数据
        val content = session.content as? UserChatContent ?: return
        content.pendingReloadChatInfo = true

        SessionManager.getInfoCache().fetchUser(session, content.chatId, forceReload) { user ->
            this.content.castOrNull<UserChatContent>()?.also {
                it.pendingReloadChatInfo = false

                it.userAvatar = user.loadImageId
                it.userName = user.displayName ?: session.sessionId
                it.userIsVip = user.isMomoVip
                it.officialOperation = user.officialOperation

                //强制刷新的数据不对，跳过
                if (!forceReload) {
                    it.userOnlineTag = user.onlineTag
                    it.userLocationTimestamp = user.getLocationTimestamp()?.time

                    it.userChatTag = user.chatTag
                    //根据chatTag刷新onlyShowMessageContent
                    it.onlyShowMessageContent =
                        content.userChatTag?.text.isNullOrBlank().not()
                }
            } != null
        }
    }

    override fun syncSession(session: SessionEntity, data: Any?): Boolean {
        val content = session.content as? UserChatContent ?: return false
        content.chatId = userIdFromSessionId(session.sessionId)
        content.userName = content.chatId
        onReloadChatInfo(content.chatId.safe(), session, false)
        return super.syncSession(session, data)
    }

    override fun updateSessionDescWithLastMessage(
        session: SessionEntity,
        content: UserChatContent,
        lastMessage: Message,
        updateProcessedTime: Boolean
    ) {
        super.updateSessionDescWithLastMessage(session, content, lastMessage, updateProcessedTime)

        if (lastMessage.isUpdateSession && !lastMessage.isImSpam) {
            session.lastMsgId = lastMessage.msgId ?: session.lastMsgId
            if (lastMessage.stopFloat == 0) {
                session.lastMsgTime = lastMessage.timestamp?.time ?: session.lastMsgTime
            }
            content.lastMessageType = lastMessage.contentType
            LastMsgCache.onSendNewMsg(lastMessage)
        }

        if (lastMessage.notShowInSession) {
            content.forcedDesc = lastMessage.recommendReason ?: ""
        } else if (lastMessage.isUpdateSession && !lastMessage.isImSpam) {
            content.forcedDesc = null
            content.distanceInfo = lastMessage.diatance
            content.lastMessageContent = SessionTextHelper.getMessageContent(lastMessage)

            content.isLastMessageReceive = lastMessage.receive
            content.onlyShowMessageContent =
                content.userChatTag?.text.isNullOrBlank().not()
        }

        updateLastMessageStatus(
            session,
            lastMessage.msgId,
            lastMessage.receive,
            lastMessage.status
        )
    }

    override fun createContent(): UserChatContent {
        return UserChatContent()
    }

    override fun onReceiveMessage(
        session: SessionEntity,
        content: UserChatContent,
        data: SessionMessage.Receive
    ): Boolean {
        return super.onReceiveMessage(session, content, data).also {
            session.mapBidHandler().sessionOnReceiveMessage(session, content, data)
        }
    }

    override fun removeOldSession(id: String) {
        BidHandlersManager.findHandler(
            businessFromSessionId(id),
            actionFromSessionId(id)
        )?.castOrNull<CommonIMPaasHandler>()
            ?.onSessionRemove(id)
    }

    override fun onUpdateMessageStatus(
        session: SessionEntity,
        content: UserChatContent,
        data: SessionMessage.UpdateStatus
    ): Boolean {
        return super.onUpdateMessageStatus(session, content, data)
    }

    override fun updateSessionIndicatorWithEveryMessage(
        session: SessionEntity,
        content: UserChatContent,
        message: Message?
    ) {
        super.updateSessionIndicatorWithEveryMessage(session, content, message)
        if (message?.status == Message.STATUS_CLOUD) return

        session.unreadMessageCount = UnreadCountMessageInterceptor.getCount(
            session.sessionKey()
        )

    }

    override fun updateSessionMetadataWithEveryMessage(
        session: SessionEntity,
        content: UserChatContent,
        message: Message
    ) {
        super.updateSessionMetadataWithEveryMessage(session, content, message)
        session.foldType = session.mapBidHandler().foldType(message)
    }

    override fun UserChatContent.contentToModel(
        baseInfo: BaseSessionInfo
    ): UserChatSessionModel = UserChatSessionModel(
        baseInfo = baseInfo,
        chatId = chatId.safe(),
        desc = desc.safe(),
        draftString = draftString.safe(),
        draftQuoteString = draftQuoteString.safe(),
        lastMessageType = lastMessageType.safe(0),
        showMessageStatus = showMessageStatus,
        lastMessageStatus = lastMessageStatus ?: 0,
        userAvatar = userAvatar.safe(),
        userName = userName.safe(),
        userIsVip = userIsVip,
        userOnlineTag = userOnlineTag.optionMap {
            UserOnlineTagModel(
                it.getName().safe(),
                it.getTagColor().safe(),
                it.getAction().safe(),
                it.canShowAnim(),
                it.getRoomPattern().safe(),
                needExposeOnline
            )
        },
        userLocationTimestamp = userLocationTimestamp.safe(0L),
        userChatTag = userChatTag.optionMap { UserChatTagModel(it.text.safe()) },

        isHongbao = isHongbao,
        isGift = isGift,
        isMissedFriendCall = isMissedFriendCall,
        missedFriendCallDesc = missedFriendCallDesc.safe(),
        isDianDianCard = isDianDianCard,
        isQuestionMatch = isQuestionMatch,
        isType28 = isType28,
        lastType28Prompt = lastType28Prompt.safe(),
        type28AppId = type28AppId.safe(),
        specialText = specialText,
        pushPrefix = pushPrefix.safe(),
        sessionBusinessType = sessionBusinessType.safe(0),
        onlineMsgTime = when {
            (baseInfo.lastMsgId.isNotEmpty() && distance != -2 && TimeUtils.isBeforeYesterday(
                baseInfo.lastMessageTime
            )) -> {
                onlineMsgTime.safe()
            }

            (baseInfo.lastMsgId.isNotEmpty() && distance == -2 && TimeUtils.isBeforeFirstWeek(
                baseInfo.lastMessageTime
            ) == 0) -> {
                sevenDaysIn.safe()
            }

            (baseInfo.lastMsgId.isNotEmpty() && distance == -2 && TimeUtils.isBeforeFirstWeek(
                baseInfo.lastMessageTime
            ) == 1) -> {
                sevenDaysOut.safe()
            }

            else -> {
                ""
            }
        },
        officialOperation = officialOperation,
        cellTagUniformLabels = null
    )

}