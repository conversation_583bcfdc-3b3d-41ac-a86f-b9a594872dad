package com.immomo.momo.maintab.statistics

import com.immomo.lcapt.evlog.anno.Param
import com.immomo.lcapt.evlog.anno.TaskPoint

interface IOrientationSensorLog {

    companion object {
        const val SWITCH_CLOSE = 0
        const val SWITCH_OPEN = 1
    }

    @TaskPoint(
        page = "community.splash",
        action = "orientation.swtich",
        type = "orientation",
        requireId = "18529"
    )
    fun logOrientationSensor(@Param("switch") switch: Int = SWITCH_CLOSE)
}