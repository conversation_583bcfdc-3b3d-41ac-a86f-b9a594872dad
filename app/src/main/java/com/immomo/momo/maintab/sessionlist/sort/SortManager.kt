package com.immomo.momo.maintab.sessionlist.sort

import com.immomo.framework.storage.kv.KV
import com.immomo.momo.maintab.sessionlist.sort.apt.SessionSortConfigV2Getter
import com.immomo.momo.util.DateUtil
import kotlin.random.Random

object SortManager {
    private var lastRequestTime = 0L
    private const val INTERVAL_FILTER = 2000L
    private const val TIME_RELEASE = 4 * 60 * 60 * 1000L
    private const val TIME_HASH = 30 * 60 * 1000L

    /**
     * 检查频次
     * 一天最多n次，间隔x
     */
    fun canRequestRecommendToday(): Boolean {
        val currentMill = System.currentTimeMillis()
        if (currentMill - lastRequestTime < INTERVAL_FILTER) {
            return false
        }
        lastRequestTime = currentMill
        val lastInfo = KV.getUserLong(SortCons.Key.SORT_REQUEST_INFO, 0L)
        val lastTime = lastInfo / 1000
        val times = lastInfo % 1000
        if (currentMill - lastTime * 1000 < SortCons.INTERVAL) {
            //距离上次请求还不到时间间隔
            return false
        }
        var timeToday = 0L
        //做一个随机散列，避免用户在4点过度集中
        val delta = Random.nextLong(TIME_HASH)
        if (DateUtil.isSameDay(
                lastTime * 1000 - TIME_RELEASE - delta,
                currentMill - TIME_RELEASE - delta
            )
        ) {
            timeToday = times
        }
        return timeToday < SortCons.MAX_TIME
    }

    /**
     * 满足比对 当天+次数 的需求，存储方式为 时间戳+次数。
     * 后三位用来存储次数，高位用来存储秒级时间戳
     * 如1620272849002，表示1620272849这一天，第二次。
     */
    fun recordRequest() {
        val lastInfo = KV.getUserLong(SortCons.Key.SORT_REQUEST_INFO, 0L)

        val lastTime = lastInfo / 1000
        val secondNow = System.currentTimeMillis() / 1000

        var times = 0L//今日累计次数
        if (DateUtil.isSameDay(lastTime * 1000, secondNow * 1000)) {//不为0 说明之前有记录
            times = lastInfo % 1000L
        }
        times++
        KV.saveUserValue(SortCons.Key.SORT_REQUEST_INFO, secondNow * 1000 + times)
    }

    @JvmStatic
    fun numSessionUpload(): Int {
        return SessionSortConfigV2Getter.get().numSessionUpload()
    }
}