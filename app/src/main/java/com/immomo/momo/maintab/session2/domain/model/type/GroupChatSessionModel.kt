package com.immomo.momo.maintab.session2.domain.model.type

import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo

data class GroupChatSessionModel(
    override val baseInfo: BaseSessionInfo,
    override var chatId: String,
    override var desc: String,
    override val draftString: String,
    override val draftQuoteString: String,
    override val lastMessageType: Int,
    override val showMessageStatus: Boolean,
    override val lastMessageStatus: Int,

    val groupAvatar: String,
    val groupName: String,
    val groupIsVip: Boolean,


    val isHongbao: Boolean,
    val isGift: Boolean,

    val isVideoChatting: <PERSON>olean,

    val isAtMe: Boolean,
    val atText: String
) : ChatSessionModel {
    override fun hasSpecialNotice(): Boolean {
        return isHongbao || isGift || (isAtMe && atText.isNotEmpty())
    }
}