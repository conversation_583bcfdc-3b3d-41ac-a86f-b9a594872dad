package com.immomo.momo.maintab.sessionlist.migrate

import com.immomo.framework.storage.kv.KV
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.molive.kotlin.extensions.isNotNullOrEmpty
import com.immomo.momo.greendao.AppDBUtils
import com.immomo.momo.greendao.HepaiSessionEntityDao
import com.immomo.momo.maintab.session2.apt.SessionAppConfigV2Getter
import com.immomo.momo.maintab.session2.data.database.HepaiSessionEntity
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.defs.FoldSessionDefinition
import com.immomo.momo.messages.service.SingleMsgService
import com.immomo.momo.service.bean.FolderType
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.sessions.SessionService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

class HePaiSessionHelper {
    private var migrateJob: Job? = null

    fun destroy() {
        migrateJob?.cancel()
    }

    fun migrateToHePai() {
        if (migrateJob?.isActive == true || migrateJob?.isCompleted == true) return
        migrateJob = CoroutineScope(SupervisorJob() + MMDispatchers.User).launch {
            try {
                val hasHePaiEnter = getHePaiEnterStatus()
                if (System.currentTimeMillis() - HePaiSessionEnterHelper.getLastScanTime() > SessionAppConfigV2Getter.get()
                        .hePaiScanInterval() * 60L * 60L * 1000L
                    && (hasHePaiEnter || SessionManager.get()
                        .getSessionListSize() + 1 > SessionAppConfigV2Getter.get()
                        .hePaiSessionMin())
                ) {
                    realMigrate()
                }
            } catch (e: Exception) {
                // nothing
            }
        }
    }

    /**
     * 首次安装通过api校验迁移
     *
     * @return
     * @throws Exception
     */
    private fun realMigrate() {
        //需要被校验的最大限制
        val maxCount = SessionAppConfigV2Getter.get().hePaiScanNum()
        val hepaiOldDao =
            AppDBUtils.getInstance().getDao(HepaiSessionEntity::class.java) as HepaiSessionEntityDao
        val hepaiOldList =
            hepaiOldDao.queryBuilder().build().list().map { it.sessionId }.toSet()

        SessionManager.get().getHePaiPossible(hepaiOldList, maxCount).filter {
            val status = SingleMsgService.getInstance().isFoldHePai(it)
            if (!status) {
                hepaiOldDao.insertOrReplace(HepaiSessionEntity(it))
            }
            status
        }.isNotNullOrEmpty {
            if (it.size > SessionAppConfigV2Getter.get().hePaiSessionMin()) {
                SessionService.getInstance()
                    .updateSessionFolderType(FolderType.HePai, it.toTypedArray())
                SessionService.getInstance().updateFoldSession(Session.ID.HEPAI)
                HePaiSessionEnterHelper.updateScanTime(System.currentTimeMillis())
            }
        }
    }


    private fun getHePaiEnterStatus(): Boolean {
        return SessionService.getInstance().checkExist(FoldSessionDefinition.HEPAI)
    }
}

class HePaiSessionEnterHelper {
    companion object {
        private const val HE_PAI_LAST_SCAN_TIME = "he_pai_last_scan_time"

        @kotlin.jvm.JvmStatic
        fun updateScanTime(lastScanTime: Long) {
            KV.saveUserValue(HE_PAI_LAST_SCAN_TIME, lastScanTime)
        }

        @kotlin.jvm.JvmStatic
        fun getLastScanTime() = KV.getUserLong(HE_PAI_LAST_SCAN_TIME, 0)

    }
}