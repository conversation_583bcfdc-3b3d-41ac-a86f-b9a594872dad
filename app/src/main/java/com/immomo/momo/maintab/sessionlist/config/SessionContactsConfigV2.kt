package com.immomo.momo.maintab.sessionlist.config

import com.immomo.annotations.appconfig.AppConfigField
import com.immomo.annotations.appconfig.appconfigv2.AppConfigV2
import com.immomo.momo.maintab.sessionlist.config.apt.SessionContactsConfigV2Getter

@AppConfigV2
class SessionContactsConfigV2 {

    /**
     * 关闭联系人红点
     */
    @AppConfigField(
        key = "hide_contact_red",
        mark = "555",
        defValue = "0",
        isSysValue = true
    )
    val closeRed = 0

    companion object {
        @JvmStatic
        fun isCloseRed() = SessionContactsConfigV2Getter.get().closeRed() == 1
    }

}