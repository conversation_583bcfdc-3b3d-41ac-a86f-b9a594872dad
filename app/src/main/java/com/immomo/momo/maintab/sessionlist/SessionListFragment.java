package com.immomo.momo.maintab.sessionlist;

import static com.immomo.momo.flashchat.contract.FlashChatConstants.Event.ACTION_UPDATE_FLOAT;
import static com.immomo.momo.maintab.sessionlist.SpamSessionHelper.SPAM_TO_UPDATE_UNREAD;
import static com.immomo.momo.pay.PayConst.VipSource.SOURCE_PEEK_MESSAGE;
import static com.immomo.momo.universe.util.UniverseCons.Event.EVENT_NOTIFICATION_CHANGE;

import android.app.Activity;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;

import com.cosmos.mdlog.MDLog;
import com.google.android.material.tabs.MomoTabLayout;
import com.google.common.eventbus.Subscribe;
import com.immomo.android.module.feed.statistics.IFollowFeedLog;
import com.immomo.android.module.feedlist.presentation.feedUtils.FeedListMessageHelper;
import com.immomo.android.router.pay.PayConst;
import com.immomo.framework.account.MessageManager;
import com.immomo.framework.base.BaseReceiver.IBroadcastReceiveListener;
import com.immomo.framework.base.BaseTabOptionFragment;
import com.immomo.framework.base.IStepConfigDataProvider;
import com.immomo.framework.rxjava.interactor.CommonSubscriber;
import com.immomo.framework.statistics.pagespeed.AutoSpeed;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.framework.utils.StatusBarUtil;
import com.immomo.framework.utils.UIUtils;
import com.immomo.framework.utils.mfrpermission.MfrPermission;
import com.immomo.framework.utils.mfrpermission.MfrPermissionAlertHelper;
import com.immomo.framework.view.TopTipView;
import com.immomo.framework.view.TopTipViewStubProxy;
import com.immomo.framework.view.toolbar.CompatAppbarLayout;
import com.immomo.lcapt.evlog.EVLog;
import com.immomo.mmstatistics.event.ClickEvent;
import com.immomo.mmstatistics.event.Event;
import com.immomo.mmstatistics.event.ExposureEvent;
import com.immomo.mmstatistics.event.PVEvent;
import com.immomo.mmutil.NetUtils;
import com.immomo.mmutil.log.Log4Android;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.task.ThreadUtils;
import com.immomo.moarch.account.AccountKit;
import com.immomo.momo.IMConfigs;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.android.broadcast.DraftReceiver;
import com.immomo.momo.android.broadcast.FriendNoticeReceiver;
import com.immomo.momo.android.broadcast.ReflushGroupProfileReceiver;
import com.immomo.momo.android.broadcast.ReflushMyDiscussListReceiver;
import com.immomo.momo.android.broadcast.ReflushSessionUnreadReceiver;
import com.immomo.momo.android.broadcast.ReflushUserProfileReceiver;
import com.immomo.momo.android.broadcast.ReflushVChatSuperRoomProfileReceiver;
import com.immomo.momo.android.view.dialog.MAlertDialog;
import com.immomo.momo.android.view.tips.TipManager;
import com.immomo.momo.android.view.tips.tip.ITip;
import com.immomo.momo.android.view.tips.triangle.TopTriangleDrawable;
import com.immomo.momo.android.view.util.ViewAvalableListener;
import com.immomo.momo.appconfig.model.AppMultiConfig;
import com.immomo.momo.businessmodel.statistics.BusinessConfig;
import com.immomo.momo.businessmodel.statistics.SessionImConfig;
import com.immomo.momo.common.AppKit;
import com.immomo.momo.common.ClickUtils;
import com.immomo.momo.eventbus.DataEvent;
import com.immomo.momo.eventbus.EventKeys;
import com.immomo.momo.eventbus.SimpleEvent;
import com.immomo.momo.fullsearch.activity.FullSearchActivity;
import com.immomo.momo.globalevent.GlobalEventManager;
import com.immomo.momo.gotologic.GotoDispatcher;
import com.immomo.momo.home.manager.FrameConfigManager;
import com.immomo.momo.homepage.model.MillionEntranceInfo;
import com.immomo.momo.maingroup.IBaseForBusinessView;
import com.immomo.momo.maingroup.IBusinessView;
import com.immomo.momo.maingroup.IBusinessViewForScroll;
import com.immomo.momo.maingroup.IHomeBaseProvider;
import com.immomo.momo.maingroup.manager.FrameConfigConst;
import com.immomo.momo.maintab.MaintabActivity;
import com.immomo.momo.maintab.model.ActiveUserTopEntry;
import com.immomo.momo.maintab.session2.SessionUpdateBundle;
import com.immomo.momo.maintab.session2.data.manager.SessionKey;
import com.immomo.momo.maintab.session2.data.manager.SessionManager;
import com.immomo.momo.maintab.session2.defs.GotoSessionDefinition;
import com.immomo.momo.maintab.session2.defs.SayHiSessionDefinition;
import com.immomo.momo.maintab.session2.defs.UniverseSessionDefinition;
import com.immomo.momo.maintab.session2.domain.model.SessionModel;
import com.immomo.momo.maintab.session2.domain.topoperator.SessionTopOperatorManager;
import com.immomo.momo.maintab.session2.presentation.fragment.SessionListInnerFragment;
import com.immomo.momo.maintab.session2.presentation.itemmodel.events.SessionDraggableViewTouchListener;
import com.immomo.momo.maintab.sessionlist.adapter.SessionListAdapter;
import com.immomo.momo.maintab.sessionlist.bean.SessionSpaceBean;
import com.immomo.momo.maintab.sessionlist.bean.UnreadData;
import com.immomo.momo.maintab.sessionlist.config.SessionContactsConfigV2;
import com.immomo.momo.maintab.sessionlist.enterbar.usecase.SessionEnterBarResponse;
import com.immomo.momo.maintab.sessionlist.sort.SortCons;
import com.immomo.momo.maintab.sessionlist.space.apt.SessionSpaceConfigV2Getter;
import com.immomo.momo.maintab.sessionlist.util.EcologicalGovernanceManager;
import com.immomo.momo.maintab.sessionlist.util.SettingGotoHelper;
import com.immomo.momo.maintab.view.SessionListPageLayout;
import com.immomo.momo.maintab.view.SessionSpaceView;
import com.immomo.momo.message.helper.MaskChatRecordHelper;
import com.immomo.momo.message.sayhi.task.GetSayHiRedDotTask;
import com.immomo.momo.message.sayhi.utils.NewSayHiSessionFlowUtil;
import com.immomo.momo.message.view.DragBubbleView;
import com.immomo.momo.million_entrance.MillionEntranceManager;
import com.immomo.momo.million_entrance.scroller.IMillionFloatScrollValueFeed;
import com.immomo.momo.mvp.common.presenter.ITipsPresenter.TipsMessage;
import com.immomo.momo.mvp.contacts.activity.ContactLuaActivity;
import com.immomo.momo.mvp.maintab.NotificationSourceHelper;
import com.immomo.momo.mvp.maintab.mainbubble.MainBubbleHelper;
import com.immomo.momo.pay.PayVipBootHelper;
import com.immomo.momo.performance.SimpleViewStubProxy;
import com.immomo.momo.permission.PermissionUtil;
import com.immomo.momo.protocol.imjson.receiver.MessageKeys;
import com.immomo.momo.recentonline.view.RecentOnlineActivity;
import com.immomo.momo.service.bean.Session;
import com.immomo.momo.service.sessions.SessionService;
import com.immomo.momo.sessionnotice.bean.PushSwitchTipsInfo;
import com.immomo.momo.sessionnotice.bean.TipsInfoBar;
import com.immomo.momo.setting.activity.NetCheckerActivity;
import com.immomo.momo.statistics.EVAction;
import com.immomo.momo.statistics.EVPage;
import com.immomo.momo.universe.config.UniverseSessionABTest;
import com.immomo.momo.universe.domain.interact.CommonUniUserInfoUseCase;
import com.immomo.momo.universe.im.UniUnreadManager;
import com.immomo.momo.universe.im.cons.SessionCons;
import com.immomo.momo.universe.router.UniverseRouter;
import com.immomo.momo.universe.user.model.UniBatchUserInfoModel;
import com.immomo.momo.util.GotoParserUtil;
import com.immomo.momo.util.StringUtils;
import com.immomo.momo.voicechat.model.VChatAction;
import com.immomo.svgaplayer.SVGAAnimListenerAdapter;
import com.immomo.svgaplayer.view.MomoSVGAImageView;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import de.greenrobot.event.EventBus;
import info.xudshen.android.appasm.AppAsm;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableObserver;
import io.reactivex.subjects.PublishSubject;

public class SessionListFragment extends BaseTabOptionFragment implements ISessionListView2,
        MessageManager.MessageSubscriber, IStepConfigDataProvider<BusinessConfig>,
        IBusinessView, IBusinessViewForScroll {

    private static final long NO_CLICK_TIME_DELTA = 400;//milliseconds
    private static final int SPACE_INTERVAL = 60 * 1000;//milliseconds
    private static final String KEY_MASK_CHAT_TRUTH_TIPS = "key_mask_chat_truth_tips";
    public static final String MESSAGE_TAB_KEY = "消息";
    private static final String TASK_REFRESH_UNIVERSE_UNREAD = "task_refresh_universe_unread"; // 刷新小宇宙未读数
    private static final String TASK_REFRESH_UNIVERSE_PROFILE = "task_refresh_universe_profile"; // 刷新小宇宙资料

    private final int AllSessionMsgTag = hashCode() + 2;
    private final int LocalSessionMsgTag = hashCode() + 3;
    private final int HomeResumeTag = hashCode() + 4;
    private final int RefreshAllTag = hashCode() + 5;
    private final int NoticeMsgTag = hashCode() + 6;
    private final int VChatSuperRoom = hashCode() + 9;
    private final int NetworkInvalidTag = hashCode() + 10;

    private boolean isReceiverInit = false;
    // private TopTipView topTipView;
    private TopTipViewStubProxy topTipViewProxy;
    private SimpleViewStubProxy viewStubPushGuide;
    private SimpleViewStubProxy viewStubSpace;
    private ImageView ivPushGuideClose;
    private TextView ivPushGuideOpen;
    private TextView ivPushGuideTitle;
    private TextView ivPushGuideDesc;
    private ReflushUserProfileReceiver reflushUserProfileReceiver;
    private ReflushGroupProfileReceiver reflushGroupProfileReceiver;
    private ReflushMyDiscussListReceiver reflushMyDiscussListReceiver;
    private ReflushSessionUnreadReceiver reflushSessionUnreadReceiver;
    private DraftReceiver draftReceiver;
    private FriendNoticeReceiver friendNoticeReceiver;
    private SessionListReceiver sessionListReceiver;
    private DragBubbleView dragView;
    private ConstraintLayout tipView;

    private FrameLayout innerFragmentContainer;
    private SessionListPageLayout listPageLayout;
    private SessionListInnerFragment innerFragment;

    private ISessionListPresenter mPresenter;

    private PublishSubject<Boolean> onTabClickSubject = PublishSubject.create();
    private CompositeDisposable onTabClickDisposables = new CompositeDisposable();

    private PushSwitchGuideHelper pushSwitchGuideHelper;
    private GlobalEventManager.Subscriber subscriber;
    // 胶囊位
    private MillionEntranceManager mMillionEntranceManager;
    private IMillionFloatScrollValueFeed mMillionFloatScrollValueFeed;
    private MillionEntranceInfo entranceInfo;
    private boolean hideEntrance = false;

    /**
     * 事件 - 原生通知入口刷新，lua业务引起入口变动
     */
    private final static String EVENT_NTF_SOUL_MATCH_NATIVE_ENTRY_UPDATE = "NTF_SOUL_MATCH_NATIVE_ENTRY_UPDATE";
    /**
     * 事件 - 原生通知入口留言人数减一
     */
    private final static String NTF_SOUL_MATCH_NATIVE_ENTRY_LEAVE_MSG_PEOPLE_UPDATE = "NTF_SOUL_MATCH_NATIVE_ENTRY_LEAVE_MSG_PEOPLE_UPDATE";
    private final static String EVENT_CREATE_OR_APPLY_VCHAT_SUPER_ROOM = "createOrDropSuperRoomSuccess";
    public final static String EVENT_NOTIFICATION_UPDATE_UNI_BUBBLE = "notification_update_uni_bubble";
    //接收Lua发送置顶事件
    public final static String GROUP_SETTING_CHAT_UP = "GROUP_SETTING_CHAT_UP";
    public final static String EVENT_UNIVERSE_PROFILE_REFRESH = "event_universe_profile_refresh";
    // push引导浮层
    public final static int TYPE_PUSH_GUIDE_TIPS = 1;
    // push引导弹窗
    public final static int TYPE_PUSH_GUIDE_DIALOG = 2;

    private PVEvent.Helper helper;
    //动态未读提示
    private ActiveUserTopEntry topEntryData;
    private SessionSpaceView spaceView;
    private boolean hasClickFromSpace = false;
    private boolean isFirstEnter = true;
    private int chatCount = -1;

    private long lastUpdateTime = 0;
    private long redDotInterval = 600L;
    private CommonUniUserInfoUseCase commonUniUserInfoUseCase;


    //当在基因实验是布局使用不带toolbar的布局，用于处理父menu不生效。其他控件完全一样，后续更新要注意
    @Override
    protected int getLayout() {
        return R.layout.fragment_session_list;
    }

    @Override
    protected boolean isNeedLazyLoad() {
        return false;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        SessionManager manager = SessionManager.get();
        if (mPresenter == null) {
            mPresenter = new SessionListPresenter(manager, this);
        }
        manager.init();
        MomoKit.getApp().sessionListFragmentLive = true;

        EventBus.getDefault().register(this);
    }

    public ISessionListPresenter getPresenter() {
        return mPresenter;
    }

    @Override
    public void onDestroy() {
        if (mMillionEntranceManager != null) {
            mMillionEntranceManager.release();
        }
        releaseProfileUserUseCase();
        EcologicalGovernanceManager.INSTANCE.release();
        AccountKit.getAccountManager().unregisterAccountEventListener(getTaskTag());
        EventBus.getDefault().unregister(this);
        super.onDestroy();
        if (mPresenter != null) {
            mPresenter.onDestroy();
        }
    }

    //<editor-fold desc="contact menu">
    @Nullable
    private View jumpContactMenu = null;
    @Nullable
    private TextView unreadContactTv = null;
    @Nullable
    private TextView unreadUniTv = null;
    @Nullable
    private View jumpSearchMenu;
    private View truthMenu; // 蒙面按钮
    @Nullable
    private View jumpGlobalSearchMenu; // 全局搜索
    private boolean showSearch = false;
    private MomoSVGAImageView maskBtn;
    private TextView maskBtnTv;
    private String maskSvgaUrl = "mask_truth.svga";

    @Override
    public void updateContactUnreadCount(int count) {
        if (unreadContactTv == null) return;
        if (SessionContactsConfigV2.isCloseRed()) return;
        unreadContactTv.setVisibility(count > 0 ? View.VISIBLE : View.INVISIBLE);
        unreadContactTv.setText(String.valueOf(count));
    }
    //</editor-fold>


    private void initListView() {
        SessionDraggableViewTouchListener onDragListener = (v, event, adapterPosition, dragView, dragFromType) -> {
            ISessionListView2 sessionView = SessionListFragment.this;
            if (sessionView.getDragView() != null) {
                dragView.setTag(adapterPosition);

                sessionView.getDragView().setDragFromType(dragFromType);
                sessionView.onDragBubbleOnTouch();
                return sessionView.getDragView().handoverTouch(dragView, event);
            }
            return false;
        };

        innerFragmentContainer = findViewById(R.id.session_list_inner_container);
        innerFragment = new SessionListInnerFragment(mPresenter, onDragListener, sessionListInnerViewModel -> {
            ((SessionListPresenter) mPresenter).setSessionListVm(sessionListInnerViewModel);
            return null;
        });
        getChildFragmentManager().beginTransaction()
                .replace(R.id.session_list_inner_container, innerFragment)
                .commitAllowingStateLoss();
    }

    @Override
    protected void initViews(View contentView) {
        listPageLayout = findViewById(R.id.session_list_page_layout);
        if (listPageLayout != null) {
            listPageLayout.setOnMoveListener(SessionListFragment.this::onMove);
        }

        setupForMainStatusBar();
        topTipViewProxy = new TopTipViewStubProxy((ViewStub) contentView.findViewById(R.id.tip_view_vs));
        viewStubPushGuide = new SimpleViewStubProxy((ViewStub) contentView.findViewById(R.id.tip_push_switch_guide));
        viewStubSpace = new SimpleViewStubProxy((ViewStub) contentView.findViewById(R.id.tip_space_vs));
        tipView = findViewById(R.id.cl_tip);
        setTitle(R.string.sessions);
        initListView();
        if (getToolbar() != null) {
            getToolbar().setNavigationIcon(null);
        }
        dragView = findViewById(R.id.dragView);

        //只有访客或者不需要设置主界面状态栏时，才设置一个 headerBarHeight
        if (!AppKit.getAccountManager().isOnline() || !StatusBarUtil.shouldSetStatusBar()) {
            dragView.initHeaderBar(StatusBarUtil.getStatusBarHeight(getActivity()));
        }

        dragView.setOnFinishListener(new DragBubbleView.OnFinishListener() {
            @Override
            public void onFinish(String tag, View view) {

                switch (tag) {
                    case DragBubbleView.DRAG_FROM_LIST:
                        Integer position = (Integer) view.getTag();
                        if (null != position) {
                            innerFragment.ignoreSessionUnread(position);
                        }
                        break;
                    case DragBubbleView.DRAG_FROM_SESSION_HEADER:
                        mPresenter.ignoreNotice(true);
                        break;
                    default:
                        break;
                }
            }
        });

        float top = getResources().getDimension(R.dimen.tab_layout_height) + (StatusBarUtil.shouldSetStatusBar() ? StatusBarUtil.getStatusBarHeight(getActivity()) : 0f);
        contentView.setPadding(0, (int) top, 0, 0);
    }

    @Override
    public void showOrHideBottomTips(Boolean isShow) {
        if (tipView != null) {
            tipView.setVisibility(isShow ? View.VISIBLE : View.GONE);
        }
    }

    private void updateDragView() {
        // TODO by hlj 在消息帧率增加快聊改变了原有消息帧结构，使用下面代码确保气泡拖拽位置不错位。
        if (!dragView.isInitHeaderBar()) {
            dragView.initHeaderBar(StatusBarUtil.getStatusBarHeight(getActivity()) + findToolbar().getHeight());
        }
    }

    private void setupForMainStatusBar() {
        if (!AppKit.getAccountManager().isOnline()) {
            return;
        }
        if (StatusBarUtil.shouldSetStatusBar() && toolbarHelper != null && toolbarHelper.getAppBarLayout() != null) {
            int statusHarHeight = StatusBarUtil.getStatusBarHeight(getActivity());
            toolbarHelper.getAppBarLayout().setPadding(0, statusHarHeight, 0, 0);
        }
    }

    private void initEvents() {
        topTipViewProxy.addInflateListener(view -> {
            final TopTipView localTopTipView = view;
            localTopTipView.setTopTipEventListener(new TopTipView.TopTipEventListener() {
                @Override
                public void onTopTipClick(View view, TipsMessage tipsMsg) {
                    if (view == localTopTipView && tipsMsg != null) {
                        switch (tipsMsg.getId()) {
                            case TipsMessage.ID_IMJSON_OTHER:
                                // 判断非网络原因启动重连
                                if (NetUtils.isNetworkAvailable()) {
                                    MomoKit.getApp().asyncWatchIMService();
                                }
                                // 断网情况下进行网络检测
                                startActivity(new Intent(getContext(), NetCheckerActivity.class));
                                break;
                            case TipsMessage.ID_FEED:
                                MAlertDialog alertDialog = MAlertDialog.makeConfirm(getActivity(),
                                        R.string.tips_feed, R.string.tips_btn_nevermind, R.string.tips_btn_goto, ((dialog, which) -> {
                                            closeDialog();
                                        }),
                                        (dialog, which) -> {
                                            gotoNoticeSetting();
                                        }
                                );
                                alertDialog.setSupportDark(true);
                                showDialog(alertDialog);
                                KV.saveUserValue("tips_" + tipsMsg.getId(), true);
                                topTipViewProxy.removeTips(tipsMsg);
                                break;
                            case TipsMessage.ID_HIMSG:
                                alertDialog = MAlertDialog.makeConfirm(getActivity(),
                                        R.string.tips_hi, R.string.tips_btn_nevermind, R.string.tips_btn_goto, ((dialog, which) -> {
                                            closeDialog();
                                        }),
                                        (dialog, which) -> {
                                            gotoNoticeSetting();
                                        });
                                alertDialog.setSupportDark(true);
                                showDialog(alertDialog);
                                KV.saveUserValue("tips_" + tipsMsg.getId(), true);
                                topTipViewProxy.removeTips(tipsMsg);
                                break;
                            default:
                                break;
                        }
                    }
                }

                @Override
                public void onTopTipShown(View view, TipsMessage tipsMessage) {
                }

                @Override
                public void onTopTipHide(View view, TipsMessage tipsMessage) {
                }
            });
        });
        innerFragment.addOnScrollListener(new RecyclerView.OnScrollListener() {
            private boolean drawLineFlag = false;

            private long lastCheckStateTime = 0;

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);

                //draw line
                boolean flag = recyclerView.canScrollVertically(-1);
                View appbar = toolbarHelper.getAppBarLayout();
                if (drawLineFlag != flag &&
                        appbar != null && appbar instanceof CompatAppbarLayout) {
                    ((CompatAppbarLayout) appbar).showShadow(flag);
                    drawLineFlag = flag;
                }
                long curCheckTime = SystemClock.uptimeMillis();
                if (curCheckTime - lastCheckStateTime > 120) {
                    mPresenter.addSessionInViewToRefreshListByScroll();
                    lastCheckStateTime = curCheckTime;
                }
            }
        });
        /**
         * 注册点击事件
         */
        Disposable disposable = onTabClickSubject
                .buffer(onTabClickSubject.debounce(NO_CLICK_TIME_DELTA, TimeUnit.MILLISECONDS))
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new DisposableObserver<List<Boolean>>() {
                    @Override
                    public void onComplete() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        Log4Android.getInstance().e(e);
                    }

                    @Override
                    public void onNext(List<Boolean> clicks) {
                        if (innerFragment == null) return;
                        if (clicks.size() == 1) {
                            innerFragment.smoothScrollToPosition(0);
                        } else if (clicks.size() > 1) {
                            innerFragment.switchToNextUnread();
                        }
                    }
                });
        onTabClickDisposables.add(disposable);

        //接收答题成功后的回调
        subscriber = new GlobalEventManager.Subscriber() {
            @Override
            public void onGlobalEventReceived(GlobalEventManager.Event event) {
                if (event == null) {
                    return;
                }
                if (StringUtils.equalsNonNull(event.getName(), EVENT_CREATE_OR_APPLY_VCHAT_SUPER_ROOM)) {
                    if (event.getMsg() == null) {
                        return;
                    }
                    try {
                        Map msgMap = event.getMsg();
                        String vid = (String) msgMap.get("vid");
                        Bundle bundle = new Bundle(2);
                        bundle.putString(com.immomo.android.module.vchat.MessageKeys.VoiceChat.KEY_VCHAT_VID, vid);
                        bundle.putInt(com.immomo.android.module.vchat.MessageKeys.VoiceChat.KEY_VCHAT_ACTION_TYPE, VChatAction.CREATE_OR_ATTACH_SUPER_ROOM);
                        mPresenter.onMessageReceive(bundle, com.immomo.android.module.vchat.MessageKeys.VoiceChat.ACTION_VOICE_CHAT_SUPER_ROOM);
                    } catch (Exception e) {
                        MDLog.printErrStackTrace(LogTag.SessionList.SessionListUpdatre, e);
                    }
                } else if (StringUtils.equalsNonNull(event.getName(), EVENT_NOTIFICATION_CHANGE)
                        || StringUtils.equalsNonNull(event.getName(), SPAM_TO_UPDATE_UNREAD)) {
                    mPresenter.refreshUnreadMessageCount();
                    if (UniverseSessionABTest.INSTANCE.isTest()
                            && StringUtils.equalsNonNull(event.getName(), EVENT_NOTIFICATION_CHANGE)) { // 小宇宙未读数刷新
                        MomoMainThreadExecutor.cancelAllRunnables(TASK_REFRESH_UNIVERSE_UNREAD);
                        MomoMainThreadExecutor.postDelayed(TASK_REFRESH_UNIVERSE_UNREAD, () -> {
                            SessionManager.get().syncSession(new SessionUpdateBundle.ReloadInfo(SessionKey.fromString(UniverseSessionDefinition.UNIVERSE)), true);
                            MDLog.i(com.immomo.momo.universe.im.cons.LogTag.MSG, TASK_REFRESH_UNIVERSE_UNREAD);
                        }, 100);
                    }
                } else if (StringUtils.equalsNonNull(event.getName(), GROUP_SETTING_CHAT_UP)) {
                    //接收置顶聊天
                    ThreadUtils.execute(ThreadUtils.TYPE_RIGHT_NOW, () -> {
                        Map<String, Object> msgMap = event.getMsg();
                        boolean push = (boolean) msgMap.get("push");
                        String gid = (String) msgMap.get("gid");
                        if (gid == null) {
                            return;
                        }
                        long setTime = 0L;
                        try {
                            setTime = msgMap.get("set_time") == null ? 0L : Long.parseLong(String.valueOf(msgMap.get("set_time")));
                        } catch (Exception e) {
                            e.printStackTrace();
                            setTime = 0L;
                        }
                        if (push) {
                            SessionStickyHelper.getInstance().makeSessionSticky(gid, SessionStickyHelper.ChatType.TYPE_GROUP, setTime);
                        } else {
                            SessionStickyHelper.getInstance().removeSessionSticky(gid, SessionStickyHelper.ChatType.TYPE_GROUP);
                        }
                    });
                } else if (StringUtils.equalsNonNull(event.getName(), EVENT_UNIVERSE_PROFILE_REFRESH)) { // 刷新用户资料
                    onRefreshUniProfile(event);
                }
            }
        };
        GlobalEventManager.getInstance().register(subscriber, GlobalEventManager.EVN_NATIVE);
    }

    private void onRefreshUniProfile(GlobalEventManager.Event event) {
        try {
            Map<String, Object> msgMap = event.getMsg();
            String uid = (String) msgMap.get("uid");
            if (StringUtils.isNotBlank(uid)) {
                releaseProfileUserUseCase();
                MomoMainThreadExecutor.cancelAllRunnables(TASK_REFRESH_UNIVERSE_PROFILE);
                MomoMainThreadExecutor.postDelayed(TASK_REFRESH_UNIVERSE_PROFILE, () -> {
                    commonUniUserInfoUseCase = new CommonUniUserInfoUseCase();
                    commonUniUserInfoUseCase.execute(new CommonSubscriber<UniBatchUserInfoModel>() {
                        @Override
                        public void onNext(UniBatchUserInfoModel uniBatchUserInfoModel) {
                            super.onNext(uniBatchUserInfoModel);
                            if (uniBatchUserInfoModel != null
                                    && uniBatchUserInfoModel.getUserInfos() != null
                                    && uniBatchUserInfoModel.getUserInfos().size() > 0) {
                                AppAsm.getRouter(UniverseRouter.class).refreshUniverseSession();
                            }
                        }

                        @Override
                        public void onError(@Nullable Throwable exception) {}
                    }, uid);
                }, 500);
            }
        } catch (Exception e) {
            MDLog.printErrStackTrace(TAG, e);
        }
    }

    @Override
    protected void onLoad() {
        registerIMTips();
        initData();
        initEvents();
        initReceivers();
        hideLoadingTip();
    }

    private void registerIMTips() {
        topTipViewProxy.registerImTips();
    }

    private void releaseReceivers() {
        MessageManager.unregisterMessageReceiver(AllSessionMsgTag);
        MessageManager.unregisterMessageReceiver(LocalSessionMsgTag);
        MessageManager.unregisterMessageReceiver(HomeResumeTag);
        MessageManager.unregisterMessageReceiver(RefreshAllTag);
        MessageManager.unregisterMessageReceiver(NoticeMsgTag);
        MessageManager.unregisterMessageReceiver(VChatSuperRoom);
        MessageManager.unregisterMessageReceiver(NetworkInvalidTag);
        unregisterReceiver(reflushUserProfileReceiver);
        unregisterReceiver(reflushGroupProfileReceiver);
        unregisterReceiver(reflushMyDiscussListReceiver);
        unregisterReceiver(reflushSessionUnreadReceiver);
        unregisterReceiver(draftReceiver);
        unregisterReceiver(friendNoticeReceiver);
        if (reflushSessionUnreadReceiver != null) {
            LocalBroadcastManager.getInstance(getActivity()).unregisterReceiver(reflushSessionUnreadReceiver);
            reflushSessionUnreadReceiver = null;
        }
        if (sessionListReceiver != null) {
            LocalBroadcastManager.getInstance(getActivity()).unregisterReceiver(sessionListReceiver);
            sessionListReceiver = null;
        }
    }

    private void initReceivers() {
        if (!isReceiverInit) {
            isReceiverInit = true;
            SessionListBroadcastListener broadcastListener = new SessionListBroadcastListener(this);
            MessageManager.registerMessageReceiver(AllSessionMsgTag, this, 400, MessageKeys.Actions_AllSessions);
            MessageManager.registerMessageReceiver(LocalSessionMsgTag, this, 400,
                    Action_SessionGotPresent, Action_SessionChanged, Action_SyncFinished,
                    MessageKeys.Action_Logger,
                    MessageKeys.Action_UserLocalMessage,
                    MessageKeys.Action_GroupLocalMessage,
                    MessageKeys.Action_DiscussLocalMessage, Action_VideoChat,
                    MessageKeys.Action_UpdateMessage,
                    MessageKeys.ACTION_REMOVE_SESSION_UI,
                    MessageKeys.FlashChat.SessionMatchInvite,
                    MessageKeys.FlashChat.Action_FlashChat_Message,
                    MessageKeys.FlashChat.SearchMatchFailed,
                    MessageKeys.FlashChat.SearchMatchSuccess,
                    Action_SessionChangedFromVChat,
                    MessageKeys.ACTION_MASK_CHAT_UserMessage,
                    MessageKeys.MaskChat.ACTION_MASK_NORMAL_SESSION,
                    MessageKeys.ACTION_TEXT_CHAT_UserMessage,
                    MessageKeys.TextChat.ACTION_PASS_TEST,
                    MessageKeys.TextChat.ACTION_MATCH_SUCCESS);
            MessageManager.registerMessageReceiver(HomeResumeTag, this, 400, MessageKeys.SessionList.Action_Home_Resume);
            MessageManager.registerMessageReceiver(RefreshAllTag, this, 400, MessageKeys.SessionList.Action_Refresh_All);
            MessageManager.registerMessageReceiver(NoticeMsgTag, this, 400,
                    MessageKeys.Action_Feed,
                    MessageKeys.Action_NoticeStatusChanged,
                    MessageKeys.ACTION_FRIEND_NOTICE_NEW,
                    MessageKeys.Action_ContactNotice,
                    MessageKeys.Action_StarQChat_Invite_Msg,
                    MessageKeys.Action_Game_Business,
                    SortCons.Action.SET_SESSION_RECOMMEND,
                    MessageKeys.ACTION_INTERACTION_NOTICE,
                    MessageKeys.Action_ChatWebAction,
                    MessageKeys.ACTION_SESSION_FORCE_DESC_REFRESH,
                    MessageKeys.ACTION_ECOLOGICAL_GOVERNANCE_ALERT);
            MessageManager.registerMessageReceiver(VChatSuperRoom, this, 400,
                    com.immomo.android.module.vchat.MessageKeys.VoiceChat.ACTION_VOICE_CHAT_SUPER_ROOM,
                    com.immomo.android.module.vchat.MessageKeys.VoiceChat.ACTION_VOICE_CHAT_MESSAGE,
                    com.immomo.android.module.vchat.MessageKeys.VoiceChat.ACTION_VOICE_CHAT_DELETE_SESSION);

            MessageManager.registerMessageReceiver(NetworkInvalidTag, this, 400, IMConfigs.Action_IMJWarning);

            reflushUserProfileReceiver = new ReflushUserProfileReceiver(getActivity());
            reflushUserProfileReceiver.setReceiveListener(broadcastListener);

            reflushGroupProfileReceiver = new ReflushGroupProfileReceiver(getActivity());
            reflushGroupProfileReceiver.setReceiveListener(broadcastListener);

            reflushMyDiscussListReceiver = new ReflushMyDiscussListReceiver(getActivity());
            reflushMyDiscussListReceiver.setReceiveListener(broadcastListener);

            reflushSessionUnreadReceiver = new ReflushSessionUnreadReceiver(getActivity());
            reflushSessionUnreadReceiver.setReceiveListener(broadcastListener);

            draftReceiver = new DraftReceiver(getActivity());
            draftReceiver.setReceiveListener(broadcastListener);

            friendNoticeReceiver = new FriendNoticeReceiver(getActivity());
            friendNoticeReceiver.setReceiveListener(broadcastListener);

            IntentFilter filter = new IntentFilter(ReflushSessionUnreadReceiver.ReflushAll);
            LocalBroadcastManager.getInstance(getActivity()).registerReceiver(reflushSessionUnreadReceiver, filter);

            sessionListReceiver = new SessionListReceiver(getActivity());
            sessionListReceiver.setReceiveListener(broadcastListener);

            IntentFilter sessionFilter = new IntentFilter();
            sessionFilter.addAction(SessionListReceiver.ActionRequestOnlineStatue);
            sessionFilter.addAction(SessionListReceiver.ActionChangeMainFragment);
            sessionFilter.addAction(SessionListReceiver.ActionIgnoreAllUnRead);
            sessionFilter.addAction(SessionListReceiver.ActionReLoadAll);
            LocalBroadcastManager.getInstance(getActivity()).registerReceiver(sessionListReceiver, sessionFilter);
        }
    }

    @Override
    public boolean onMessageReceive(Bundle bundle, String action) {
        //接收到消息处理
        return mPresenter.onMessageReceive(bundle, action);
    }

    @Override
    public void hideLoadingTip() {
        findViewById(R.id.tv_loading_tip).setVisibility(View.GONE);
    }

    @Override
    public DragBubbleView getDragView() {
        return dragView;
    }

    @Override
    public void clearBubble() {
        dragView.clearOnFinishListener();
        dragView.addOnFinishListener(DragBubbleView.Listener_Tag_Disappear, new DragBubbleView.OnFinishListener() {
            @Override
            public void onFinish(String tag, View view) {
                if (DragBubbleView.Listener_Tag_Disappear.equals(tag)) {
                    clearOneBubble();
                }
            }
        });
    }

    //<editor-fold desc="Bubble ">
    public void clearOneBubble() {
        SessionListAdapter adapter = innerFragment.getAdapter();
        if (adapter == null) return;

        try {
            if (index < clearBubbleList.size()) {
                Integer key = clearBubbleList.get(index);
                View bombView = adapter.getAnimateView(key);
                index++;
                disappearBubble(bombView);
            } else {
                clearBubbleList.clear();
                resetBubble();
                index = 0;
            }

        } catch (Exception ex) {
            resetBubble();
        }
    }

    private List<Integer> clearBubbleList = new ArrayList<>();
    private int index = 0;

    @Override
    public void clearDragBubble() {
        SessionListAdapter adapter = innerFragment.getAdapter();
        if (adapter == null) return;

        index = 0;
        clearBubble();
        clearBubbleList = adapter.getDragAnimateList();
        clearOneBubble();
    }
    //</editor-fold>

    @Override
    public void resetBubble() {
        dragView.clear();
    }

    @Override
    public void disappearBubble(View bubbleView) {
        dragView.disappear(bubbleView);
    }

    @Override
    public void onDragBubbleOnTouch() {
        updateDragView();
    }

    @Override
    public void onDestroyView() {
        if (!onTabClickDisposables.isDisposed()) {
            onTabClickDisposables.dispose();
        }

        mPresenter.onViewDestroyed();
        if (pushSwitchGuideHelper != null) {
            pushSwitchGuideHelper.clear(getActivity());
            pushSwitchGuideHelper = null;
        }
        super.onDestroyView();
        releaseReceivers();
        topTipViewProxy.unregisterImTips();
        topTipViewProxy = null;
        MomoKit.getApp().sessionListFragmentLive = false;
        MomoMainThreadExecutor.cancelAllRunnables(this);
        MomoMainThreadExecutor.cancelAllRunnables(TASK_REFRESH_UNIVERSE_UNREAD);
        MomoMainThreadExecutor.cancelAllRunnables(TASK_REFRESH_UNIVERSE_PROFILE);
        GlobalEventManager.getInstance().unregister(subscriber, GlobalEventManager.EVN_NATIVE);
    }

    @Override
    public Activity getBaseActivity() {
        return getActivity();
    }

    @Override
    public void removeTips(int tipsID) {
        topTipViewProxy.removeTips(tipsID);
    }

    boolean firstFragmentResume = true;

    @Override
    protected void onFragmentResume() {
        super.onFragmentResume();
        innerFragment.onFragmentResume();

        if (mMillionEntranceManager != null) {
            mMillionEntranceManager.resume();
        }

        //目前menu是在BaseTabOptionFragment#dispatchResume里面inflate的，所以右上角未读数的初始化要放在这里
        if (AppKit.getAccountManager().isOnline() && mPresenter != null) {
            mPresenter.initContactUnreadCount();
        }

        if (AccountKit.getAccountManager().isOnline()) {
            checkSearchMenuShow(showSearch);
        }
        checkTruthMenu();
        showMaskAnimation();
        checkGlobalSearchShow();
        if (getActivity() instanceof MaintabActivity) {
            if (!StringUtils.isEmpty(NotificationSourceHelper.getSource())) {
                scrollToTop();
            }
        }
        setSpaceViewStatus();
        mPresenter.refreshActiveUser();
        mPresenter.onResume();
        AutoSpeed.getInstance().markMainFrameSwitchEnd(AutoSpeed.MAIN_FRAME_SESSION_TAB);

        // 由于懒加载机制,sessionList没有到前台的时候就已经被调用了一次onFragmentResume
        if (firstFragmentResume) {
            firstFragmentResume = false;
        }

        MomoMainThreadExecutor.postDelayed(this, new CheckNotificationRunnable(this), 200);

        isFirstEnter = false;

        if (getParentFragment() instanceof IBaseForBusinessView) {
            ((IBaseForBusinessView) getParentFragment()).setTabText(getFrameKey(), 0);
        }

        checkEcologicalAlert();
        if (jumpGlobalSearchMenu != null && jumpGlobalSearchMenu.getVisibility() == View.VISIBLE
                && SessionTopOperatorManager.isShownGuild()) {
            SessionTopOperatorManager.INSTANCE.checkShowTip(jumpGlobalSearchMenu);
        }

        getSayHiSessionRedDot();
    }

    @Override
    public void checkEcologicalAlert() {
        EcologicalGovernanceManager.INSTANCE.checkAlertShow(getContext());
    }

    private void getSayHiSessionRedDot() {
        if (lastUpdateTime > 0 && (System.currentTimeMillis() - lastUpdateTime) / 1000L < redDotInterval) {
            return;
        }
        lastUpdateTime = System.currentTimeMillis();
        MomoTaskExecutor.executeUserTask(getTaskTag(), new GetSayHiRedDotTask((interval) -> {
            redDotInterval = interval;
            SessionManager.get().syncSession(new SessionUpdateBundle.ReloadInfo(SayHiSessionDefinition.KEY_SAYHI));
            SessionManager.get().syncSession(new SessionUpdateBundle.ReloadInfo(GotoSessionDefinition.KEY_TEXT_CHAT));
            return null;
        }));
    }

    private void setSpaceViewStatus() {
        if (viewStubSpace.getVisibility() == View.VISIBLE &&
                !hasClickFromSpace && SessionSpaceConfigV2Getter.get().leaveClose() == 1) {
            int intervalTime = SessionSpaceConfigV2Getter.get().leaveInterval();
            long lastTime = KV.getUserLong(SPKeys.User.SessionSpace.KEY_CARD_SHOW_LAST_TIME, 0L);
            if (System.currentTimeMillis() - lastTime > intervalTime * SPACE_INTERVAL) {
                viewStubSpace.setVisibility(View.GONE);
                KV.saveUserValue(SPKeys.User.SessionSpace.KEY_CARD_DISMISS_TYPE, 2);
            }
        }
    }

    @Override
    protected void onFragmentPause() {
        super.onFragmentPause();
        innerFragment.onFragmentPause();

        if (mMillionEntranceManager != null) {
            mMillionEntranceManager.pause();
        }

        mPresenter.onPause();
        if (viewStubSpace.getVisibility() == View.VISIBLE && SessionSpaceConfigV2Getter.get().leaveClose() == 1) {
            KV.saveUserValue(SPKeys.User.SessionSpace.KEY_CARD_SHOW_LAST_TIME, System.currentTimeMillis());
        }
        EcologicalGovernanceManager.INSTANCE.cancelAlert();
    }

    @Override
    public BusinessConfig getStepConfigData() {
        return SessionImConfig.INSTANCE;
    }

    @NonNull
    @Override
    public String getFrameKey() {
        return FrameConfigConst.FRAME_CHAT;
    }

    @Override
    public int getRightMenuRes() {
        if (!AppKit.getAccountManager().isOnline()) {
            return -1;
        } else {
            return R.layout.layout_menu_session_list;
        }
    }

    @Override
    public void setRightMenusEvent() {
        View menuLayout;
        if (getParentFragment() instanceof IBaseForBusinessView) {
            menuLayout = ((IBaseForBusinessView) getParentFragment()).getRightMenusLayout(getFrameKey());
            if (menuLayout != null) {
                jumpContactMenu = menuLayout.findViewById(R.id.action_jump_contact);
                jumpSearchMenu = menuLayout.findViewById(R.id.action_search);
                truthMenu = menuLayout.findViewById(R.id.action_truth);
                jumpGlobalSearchMenu = menuLayout.findViewById(R.id.session_global_search);
                checkJumpContactMenu();
                checkTruthMenu();
                checkSearchMenuShow(showSearch);
                checkGlobalSearchShow();
            }
        }
    }

    private void checkGlobalSearchShow() {
        if (jumpGlobalSearchMenu != null) {
            jumpGlobalSearchMenu.setVisibility(SessionTopOperatorManager.INSTANCE.isABTestOpenSwitch() ? View.VISIBLE : View.GONE);
            jumpGlobalSearchMenu.setOnClickListener(v -> FullSearchActivity.start(getActivity()));
        }
    }


    private View.OnClickListener jumpContactOnClickListener = v -> {
        getPresenter().getContactNoticeUnread(count -> {
            ContactLuaActivity.startActivity(getContext(), count);
        });

    };

    private View.OnClickListener truthOnclickListener = v -> {
        if (ClickUtils.isFastClick()) { // 防止快速点击
            return;
        }
        MaskChatRecordHelper.INSTANCE.onMaskBtnPress();
        String userStr = KV.getUserStr(
                SPKeys.System.AppMultiConfig.KEY_SESSION_LIST_MASK_CHAT_ACTION, "");
        GotoDispatcher.action(userStr, getContext()).execute();
    };

    private void showMaskAnimation() {
        if (KV.getUserInt(SPKeys.System.AppMultiConfig.KEY_SESSION_LIST_MASK_CHAT, 0) < 1) {
            return;
        }
        MaskChatRecordHelper.INSTANCE.onMaskBtnShow();
        if (KV.getUserInt(SPKeys.System.AppMultiConfig.KEY_SESSION_LIST_MASK_CHAT_CHANCE, 0) > 0
                && maskBtn != null
                && truthMenu.getVisibility() == View.VISIBLE) {
            maskBtn.startSVGAAnimWithListener(maskSvgaUrl, 1, new SVGAAnimListenerAdapter() {
                @Override
                public void onFinished() {
                    maskBtn.stepToPercentage(0.9, false);
                }
            });
            showTruthTips(truthMenu);
        }
    }


    private View.OnClickListener jumpSearchClickListener = v -> FullSearchActivity.start(getActivity());


    private void checkSearchMenuShow(boolean visible) {
        if (jumpSearchMenu != null) {
            jumpSearchMenu.setVisibility(visible ? View.VISIBLE : View.GONE);
            jumpSearchMenu.setOnClickListener(jumpSearchClickListener);

        }
    }

    private void showTruthTips(View targetView) {
        Activity activity = this.getActivity();
        boolean tipShown = KV.getUserBool(KEY_MASK_CHAT_TRUTH_TIPS, false);
        String tipsStr = KV.getUserStr(SPKeys.System.AppMultiConfig.KEY_SESSION_LIST_MASK_CHAT_TIPS, "");
        if (StringUtils.isEmpty(tipsStr)) {
            tipsStr = "来匿名闪聊，话题超多聊不完！";
        }
        if (!tipShown && !TipManager.isDestroy(activity)) {
            String finalTipsStr = tipsStr;
            TipManager.bindActivity(activity).checkViewCanShowTip(targetView, new ViewAvalableListener() {
                @Override
                public void onViewAvalable(View v) {
                    TopTriangleDrawable triangleDrawable = new TopTriangleDrawable();
                    triangleDrawable.setColor(UIUtils.getColor(R.color.color_4e7fff));
                    triangleDrawable.setWidth(UIUtils.getPixels(9));
                    triangleDrawable.setHeight(UIUtils.getPixels(5));
                    if (TipManager.isDestroy(activity)) {
                        return;
                    }
                    TipManager.bindActivity(activity)
                            .setTriangles(null, triangleDrawable, null, null)
                            .setMarginEdge(5)
                            .setBackground(UIUtils.getDrawable(R.drawable.bg_18dp_round_corner_4e7fff))
                            .setTextColor(UIUtils.getColor(R.color.white))
                            .setTextPadding(UIUtils.getPixels(15f), UIUtils.getPixels(10f), UIUtils.getPixels(15f), UIUtils.getPixels(10f))
                            .setTouchToHideAll(true)
                            .showTipView(targetView, finalTipsStr, 0, 0, ITip.Triangle.TOP)
                            .autoHide(3000);
                    KV.saveUserValue(KEY_MASK_CHAT_TRUTH_TIPS, true);
                }
            });

        }
    }

    private void checkTruthMenu() {
        if (truthMenu != null) {
            maskBtn = truthMenu.findViewById(R.id.bt_truth);
            maskBtnTv = truthMenu.findViewById(R.id.tv_mask);
            String maskSvga;
            if (com.immomo.momo.util.MomoKit.INSTANCE.isDarkMode(getActivity())) {
                maskSvga = KV.getUserStr(SPKeys.System.AppMultiConfig.KEY_SESSION_LISt_MASK_CHAT_DARK_SVGA, "");
            } else {
                maskSvga = KV.getUserStr(SPKeys.System.AppMultiConfig.KEY_SESSION_LIST_MASK_CHAT_SVGA, "");
            }
            String maskText = KV.getUserStr(SPKeys.System.AppMultiConfig.KEY_SESSION_LIST_MASK_CHAT_TEXT, "");
            if (!StringUtils.isEmpty(maskSvga)) {
                maskSvgaUrl = maskSvga;
            }
            if (StringUtils.isEmpty(maskText)) {
                maskBtnTv.setVisibility(View.GONE);
            } else {
                maskBtnTv.setVisibility(View.VISIBLE);
                maskBtnTv.setText(maskText);
            }
            maskBtn.loadSVGAAnimWithListener(maskSvgaUrl, 1, new SVGAAnimListenerAdapter() {
                @Override
                public void loadResError(@NonNull String msg) {
                    maskBtn.setImageResource(R.drawable.ic_truth_bt);
                }

                @Override
                public void onLoadSuccess() {
                    maskBtn.stepToPercentage(0.9, false);
                }
            }, false);
            truthMenu.setOnClickListener(truthOnclickListener);
            if (KV.getUserInt(SPKeys.System.AppMultiConfig.KEY_SESSION_LIST_MASK_CHAT, 0) < 1) {
                truthMenu.setVisibility(View.GONE);
                return;
            }
            truthMenu.setVisibility(View.VISIBLE);

            showTruthTips(truthMenu);
        }
    }

    private boolean checkJumpContactMenu() {
        if (jumpContactMenu != null) {
            jumpContactMenu.setVisibility(View.VISIBLE);
            unreadContactTv = jumpContactMenu.findViewById(R.id.badge_tv);
            jumpContactMenu.setOnClickListener(jumpContactOnClickListener);
        }
        return jumpContactMenu != null && unreadContactTv != null;
    }


    @Override
    public Fragment getViewParentFragment() {
        return getParentFragment();
    }

    private int getTotalUnreadCount() {
        return UniUnreadManager.INSTANCE.getMsgUnreadCount() + UniUnreadManager.INSTANCE.getMeteorUnreadCount()
                + UniUnreadManager.INSTANCE.getInteractUnreadCount() + UniUnreadManager.INSTANCE.getVoteUnreadCount();
    }

    @Override
    public void onParentPageScrolledShow(float positionOffset, int positionOffsetPixels) {
        if (getParentFragment() instanceof IBaseForBusinessView) {
            View view = (((IBaseForBusinessView) getParentFragment()).getRightMenusLayout(getFrameKey()));
            if (view != null) {
                view.setVisibility(View.VISIBLE);
                view.setAlpha(positionOffset);
            }
        }
    }

    @Override
    public void onParentPageScrolledHide(float positionOffset, int positionOffsetPixels) {
        if (getParentFragment() instanceof IBaseForBusinessView) {
            View view = (((IBaseForBusinessView) getParentFragment()).getRightMenusLayout(getFrameKey()));
            if (view != null) {
                view.setAlpha(positionOffset);
            }
        }
    }

    @Override
    public void onFragmentSelected(@NonNull MomoTabLayout.Tab tab) {
        if (getParentFragment() instanceof IBaseForBusinessView) {
            ((IBaseForBusinessView) getParentFragment()).setTabText(getFrameKey(), 0);
            if (StringUtils.equalsNonNull(((IBaseForBusinessView) getParentFragment()).getHomeKey(),
                    FrameConfigManager.INSTANCE.getHomeKeyByBusinessKey(FrameConfigConst.FRAME_FOLLOW))) {
                //跟关注帧处于一个容器
                EVLog.create(IFollowFeedLog.class).clickTabChanged(((IBaseForBusinessView) getParentFragment()).getScrollType(),
                        chatCount + "",
                        ((IBaseForBusinessView) getParentFragment()).getIndexByKey(getFrameKey()) + "");
            }
        }

    }

    @Override
    public void onFragmentUnSelected(@NonNull MomoTabLayout.Tab tab) {
        if (getParentFragment() instanceof IBaseForBusinessView) {
            ((IBaseForBusinessView) getParentFragment()).setTabText(getFrameKey(), chatCount);
        }
    }

    @Override
    public void onFragmentUnSelectedByBottomTab() {
        //
    }

    @Override
    public void onSingleTabClick() {
        scrollToTop();
    }

    @Override
    public void clearUnread() {
    }

    private static class CheckNotificationRunnable implements Runnable {
        private WeakReference<SessionListFragment> sessionRef;
        private int id = "CheckNotificationRunnable".hashCode();

        public CheckNotificationRunnable(SessionListFragment sessionListFragment) {
            this.sessionRef = new WeakReference<>(sessionListFragment);
        }

        @Override
        public void run() {
            SessionListFragment sessionListFragment = sessionRef.get();
            if (sessionListFragment == null) {
                return;
            }
            if (sessionListFragment.getActivity() == null || sessionListFragment.getActivity() != MomoKit.getTopActivity()) {
                return;
            }

            Activity activity = sessionListFragment.getActivity();
            if (activity instanceof MaintabActivity && ((MaintabActivity) activity).getCurrentIndex() != 2) {
                return;
            }

            if (EcologicalGovernanceManager.INSTANCE.isAlertShowing()) { // 是否生态治理弹窗是否在展示
                return;
            }

            if (MomoKit.getApp().currUnreadMsgCount > 0
                    && !MfrPermission.Notification.check(MomoKit.getContext())
                    && (System.currentTimeMillis() - KV.getUserLong("last_alerted_notification_forbidden_time", 0L)) >
                    KV.getUserLong(MfrPermissionAlertHelper.KEY_NOTIFICATION_ALERT_PERIOD,
                            MfrPermissionAlertHelper.DEFAULT_NOTIFICATION_ALERT_PERIOD)) {
                KV.saveUserValue("last_alerted_notification_forbidden_time", System.currentTimeMillis());

                MfrPermissionAlertHelper.showRetionaleDialog(MfrPermission.Notification);
            }
        }

        @Override
        public boolean equals(Object obj) {
            if (obj == this) {
                return true;
            }
            if (obj instanceof CheckNotificationRunnable) {
                return this.id == ((CheckNotificationRunnable) obj).id;
            }
            return false;
        }

        @Override
        public int hashCode() {
            return Objects.hash(id);
        }
    }

    private void initData() {
        mPresenter.initData();
        SessionTopOperatorManager.INSTANCE.initABTestConfig();
    }


    @Override
    public void scrollToTop() {
        onTabClickSubject.onNext(true);
    }

    @Override
    public void showBuyVipDialog() {
        PayVipBootHelper.startPayVipPage(getActivity(), PayConst.VipType.VIP_TYPE, SOURCE_PEEK_MESSAGE);
    }

    @Override
    public void showBottomBubble(int unreadMessageCount, boolean showTip) {
        if (getParentFragment() instanceof IHomeBaseProvider) {
            int count = SessionService.validateUnreadCount(unreadMessageCount);
            MainBubbleHelper.INSTANCE.updateBubbleView(((IHomeBaseProvider) getParentFragment()).getHomeKey(), count);
        }

    }

    @Override
    public void hideBottomBubble() {
        if (getParentFragment() instanceof IHomeBaseProvider) {
            MainBubbleHelper.INSTANCE.hideTextBubble(((IHomeBaseProvider) getParentFragment()).getHomeKey());
        }
    }

    @Override
    public void showVideoTagView() {
        if (getParentFragment() instanceof IHomeBaseProvider) {
            MainBubbleHelper.INSTANCE.showVideoChatView(((IHomeBaseProvider) getParentFragment()).getHomeKey(), R.drawable.agora_icon_session_video);
        }
    }

    @Override
    public void hideVideoTagView() {
        if (getParentFragment() instanceof IHomeBaseProvider) {
            MainBubbleHelper.INSTANCE.hideVideoChatView(((IHomeBaseProvider) getParentFragment()).getHomeKey());
        }
    }

    /**
     * 临时修复：{@link BaseTabOptionFragment#isForeground()} 有错误，冷启动App后，立即切换到消息帧，{@link #isForeground()} 返回永远为false
     */
    private boolean mForegroundFlag = false;

    @Override
    public void onResume() {
        super.onResume();
        mForegroundFlag = true;
    }

    @Override
    public void onPause() {
        super.onPause();
        mForegroundFlag = false;
        EcologicalGovernanceManager.INSTANCE.cancelAlert();
    }

    @Override
    public boolean isForegroundNow() {
        return mForegroundFlag;
    }

    @Override
    public boolean isDestroyed() {
        return getBaseActivity() == null || getBaseActivity().isFinishing();
    }

    private static class SessionListBroadcastListener implements IBroadcastReceiveListener {
        private WeakReference<SessionListFragment> mFragmentRef;

        public SessionListBroadcastListener(SessionListFragment fragment) {
            mFragmentRef = new WeakReference<>(fragment);
        }

        @Override
        public void onReceive(Intent intent) {
            final String action = intent.getAction();
            if (StringUtils.isEmpty(action)) {
                return;
            }
            SessionListFragment fragment = mFragmentRef.get();
            if (fragment == null || fragment.mPresenter == null) {
                return;
            }
            if (ReflushGroupProfileReceiver.ACTION.equals(action)) {

                final String gid = intent.getStringExtra(ReflushGroupProfileReceiver.KEY_GID);
                fragment.mPresenter.refreshSessionProfile(Session.TYPE_GROUP, gid);

            } else if (ReflushUserProfileReceiver.ACTION.equals(action)) {

                final String momoID = intent.getStringExtra(ReflushUserProfileReceiver.KEY_MOMOID);
                fragment.mPresenter.refreshSessionProfile(Session.TYPE_CHAT, momoID);
            } else if (ReflushMyDiscussListReceiver.ACTION_REFLUSH_PROFILE.equals(action)) {

                final String did = intent.getStringExtra(ReflushMyDiscussListReceiver.KEY_DID);
                fragment.mPresenter.refreshSessionProfile(Session.TYPE_DISCUSS, did);

            } else if (ReflushSessionUnreadReceiver.ReflushAll.equals(intent.getAction())) {
                fragment.updateDragView();
                if (fragment.isSameHomeWithFollow()) {
                    FeedListMessageHelper.INSTANCE.cleanFeedUnread();
                }
                fragment.mPresenter.ignoreAllUnread(true, false);
            } else if (ReflushSessionUnreadReceiver.ReflushNotice.equals(action)) {
                fragment.mPresenter.ignoreNotice(true);
            } else if (SessionListReceiver.ActionChangeMainFragment.equals(action)) {
                if (fragment.innerFragment == null) return;
                fragment.mPresenter.exposeActiveUser(fragment.innerFragment.getActiveExposeList());
            } else if (ReflushVChatSuperRoomProfileReceiver.ACTION.equals(action)) {
                final String vid = intent.getStringExtra(ReflushVChatSuperRoomProfileReceiver.KEY_VID);
                fragment.mPresenter.refreshSessionProfile(Session.TYPE_VCHAT_SUPER_ROOM, vid);
            } else if (SessionListReceiver.ActionRequestOnlineStatue.equals(action)) {
                final String sessionId = intent.getStringExtra(SessionListReceiver.KEY_SESSION_ID);
                boolean switchChange = intent.getBooleanExtra(SessionListReceiver.KEY_SWITCH_CHANGE, false);
                fragment.mPresenter.forceRefreshOnlineStatueNextTime(sessionId);
            } else if (SessionListReceiver.ActionIgnoreAllUnRead.equals(action)) {
                fragment.mPresenter.ignoreAllUnread(false, false);
            } else if (SessionListReceiver.ActionReLoadAll.equals(action)) {
                fragment.mPresenter.reloadAll();
            }
        }
    }

    @Subscribe
    public void onEvent(DataEvent<Object> event) {
        if (event.equals(EventKeys.Block.ADD_BLOCK)) {
            mPresenter.processBlockListAction((String) event.getData());
        } else if (StringUtils.equalsNonNull(event.getAction(), EventKeys.Session.SESSION_RECENT_ONLINE_DELETE) && RecentOnlineActivity.SESSION_RECENT_ONLINE_DELETE_TAG.equals(event.getData())) {
            mPresenter.setNeedRefreshActiveUser(true);
        } else if (event.equals(com.immomo.android.router.momo.EventKeys.Microcosm.INTERACT_MSG)) {
            UniUnreadManager.INSTANCE.clearUniInteractUnread(SessionCons.ID.INTERACT_ID);
        } else if (event.equals(EventKeys.TabTest.TAB_SESSION_REFRESH_UNREAD_COUNT)) {
            if (event.getData() instanceof UnreadData) {
                int count = ((UnreadData) event.getData()).getUnreadCount();

                if (getParentFragment() instanceof IBaseForBusinessView) {
                    if (!isSelected() && ((IBaseForBusinessView) getParentFragment()).getContainsTabs().size() > 1) {
                        //多于两帧或和通知分帧可展示红点数字
                        ((IBaseForBusinessView) getParentFragment()).setTabText(getFrameKey(), Math.max(count, 0));
                    }
                }

                chatCount = Math.max(count, 0);
            }
        } else if (event.equals(EventKeys.Session.SESSION_UPDATE_SPAM_ITEM)) {
            if (event.getData() instanceof String) {
                List<String> remoteIds = new ArrayList<>();
                remoteIds.add((String) event.getData());
                if (mPresenter != null) {
                    mPresenter.addMigrateSpamIds(remoteIds);
                    mPresenter.migrateSpamSession(false);
                }
            }
        }
    }

    @Subscribe
    public void onEvent(SimpleEvent event) {
        if (event.equals(ACTION_UPDATE_FLOAT)) {
            if (mPresenter != null) {
                mPresenter.pullSessionEnterBar();
            }
        }
    }

    //和关注处于同一容器
    private boolean isSameHomeWithFollow() {
        return getParentFragment() instanceof IBaseForBusinessView && StringUtils.equalsNonNull(((IBaseForBusinessView) getParentFragment()).getHomeKey(),
                FrameConfigManager.INSTANCE.getHomeKeyByBusinessKey(FrameConfigConst.FRAME_FOLLOW));
    }


    private boolean isShouldShowPushGuideDialog = false;

    public void sessionTabOnclick() {
        showPushGuide();
        if (innerFragment != null && innerFragment.isResumed()) {
            NewSayHiSessionFlowUtil.INSTANCE.checkSayhiForceFlow();
        }
    }

    private void showPushGuide() {
        initPushGuideDialog();
        if (!PushSwitchGuideHelper.isShowGuideDialogV2()) {
            //session列表顶部tip
            initPushGuideTips();
        }
    }

    private void initPushGuideTips() {
        if (viewStubPushGuide != null && viewStubPushGuide.getVisibility() == View.VISIBLE) {
            return;
        }
        if (com.immomo.momo.util.MomoKit.INSTANCE.getSystemNotificationStatus() != 0) {
            showBizGuide();
            return;
        }

        int timeInterval = KV.getUserInt(SPKeys.PushGuide.KEY_NOTIFICATION_TIPS_FREQUENCY, 0);
        if (timeInterval < PushSwitchTipsInfo.MIN_UPDATE_FREQUENCY || timeInterval > PushSwitchTipsInfo.MAX_UPDATE_FREQUENCY) {
            timeInterval = PushSwitchTipsInfo.DEFAULT_UPDATE_FREQUENCY;
        }
        long lastPushTipsGuideTime = KV.getUserLong(SPKeys.PushGuide.KEY_LAST_TIPS_PUSH_SWITCH_GUIDE_TIME, 0L);
        boolean isShouldShowTips = System.currentTimeMillis() - lastPushTipsGuideTime > timeInterval * 3600000L;
        if (mPresenter != null && isShouldShowTips) {
            mPresenter.getPushSwitchTipsInfo();
        } else {
            showBizGuide();
        }
    }

    /**
     * 当系统通知关闭时，展示提醒引导用户前往开启
     */
    private void initPushGuideDialog() {
        if (com.immomo.momo.util.MomoKit.INSTANCE.getSystemNotificationStatus() != 0) {
            return;
        }
        isShouldShowPushGuideDialog = isShouldShowPushGuideDialog();
        if (mPresenter != null && isShouldShowPushGuideDialog) {
            mPresenter.getPushSwitchDialogInfo();
        }
    }

    private boolean isShouldShowPushGuideDialog() {
        long timeInterval = KV.getUserInt(SPKeys.PushGuide.KEY_NOTIFICATION_DIALOG_FREQUENCY, 0);
        if (timeInterval < PushSwitchTipsInfo.MIN_UPDATE_FREQUENCY || timeInterval > PushSwitchTipsInfo.MAX_UPDATE_FREQUENCY) {
            timeInterval = PushSwitchTipsInfo.DEFAULT_UPDATE_FREQUENCY;
        }
        long lastPushDialogGuideTime = KV.getUserLong(SPKeys.PushGuide.KEY_LAST_DIALOG_PUSH_SWITCH_GUIDE_TIME, 0L);
        boolean isShouldShowDialog = System.currentTimeMillis() - lastPushDialogGuideTime > timeInterval * 3600000;
        return isShouldShowDialog;
    }

    @Override
    public void onGetPushSwitchTipsInfoSuccess(@NonNull PushSwitchTipsInfo tipsInfo) {
        if (tipsInfo.getTipBar() == null) {
            return;
        }
        //横幅与新样式弹窗互斥
        if (isShouldShowPushGuideDialog && tipsInfo.getCardV2() != null) {
            return;
        }
        TipsInfoBar tipBar = tipsInfo.getTipBar();
        KV.saveUserValue(SPKeys.PushGuide.KEY_NOTIFICATION_TIPS_FREQUENCY, tipBar.getUpdateFrequency());

        setStubPushGuideUI(tipBar.getTitle(), tipBar.getDesc(), tipBar.getButtonText());

        ivPushGuideClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                logClick(EVAction.Float.PushSettingClose);
                hidePushGuideTips();
                KV.saveUserValue(SPKeys.PushGuide.KEY_LAST_TIPS_PUSH_SWITCH_GUIDE_TIME, System.currentTimeMillis());
            }
        });

        ivPushGuideOpen.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                logClick(EVAction.Float.PushSettingOpen);
                if (PermissionUtil.biggerAndroid13()) {
                    PermissionUtil.getInstance().check13Notification(getActivity());
                } else {
                    com.immomo.momo.util.MomoKit.INSTANCE.gotoApplicationDetailsSettings();
                }
                hidePushGuideTips();
                KV.saveUserValue(SPKeys.PushGuide.KEY_LAST_TIPS_PUSH_SWITCH_GUIDE_TIME, System.currentTimeMillis());
            }
        });
        if (showGuideView()) {
            logExposure(EVAction.Float.PushSettingAll);
        }
    }

    @Override
    public void onGetPushSwitchTipsInfoFail() {
        showBizGuide();
    }

    //ChatGuideConfig 下发的引导信息，动用<开启推送引导>样式，优先级低于<开启推送引导>
    private void showBizGuide() {
        AppMultiConfig.ChatGuideConfig config = AppMultiConfig.ChatGuideConfig.fromJson(KV.getUserStr(SPKeys.User.Chat.KEY_CHAT_GUIDE, null));
        if (config == null || TextUtils.isEmpty(config.title)) {
            return;
        }
        GotoParserUtil.IGotoParser gotoParser = GotoParserUtil.parse(config.btnGoto);
        setStubPushGuideUI(config.title, config.desc, gotoParser.getTitle());
        ivPushGuideClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                hidePushGuideTips();
                KV.removeUserValue(SPKeys.User.Chat.KEY_CHAT_GUIDE);
            }
        });

        ivPushGuideOpen.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                logClick(EVAction.Top.NoReply);
                GotoDispatcher.action(config.btnGoto, getActivity()).execute();
                hidePushGuideTips();
                KV.removeUserValue(SPKeys.User.Chat.KEY_CHAT_GUIDE);
            }
        });
        if (showGuideView()) {
            logExposure(EVAction.Top.NoReply);
        }
    }

    private boolean showGuideView() {
        if (innerFragmentContainer != null) {
            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) innerFragmentContainer.getLayoutParams();
            lp.topMargin = UIUtils.getPixels(40f);
            innerFragmentContainer.setLayoutParams(lp);
        }
        if (viewStubPushGuide.getVisibility() == View.VISIBLE) {
            return false;
        }
        viewStubPushGuide.setVisibility(View.VISIBLE);
        return true;
    }

    private void setStubPushGuideUI(String title, String desc, String btnText) {

        ivPushGuideClose = viewStubPushGuide.getStubView().findViewById(R.id.iv_close);
        ivPushGuideOpen = viewStubPushGuide.getStubView().findViewById(R.id.tv_open);
        ivPushGuideTitle = viewStubPushGuide.getStubView().findViewById(R.id.tv_title);
        ivPushGuideDesc = viewStubPushGuide.getStubView().findViewById(R.id.tv_desc);

        ivPushGuideTitle.setText(title);
        ivPushGuideDesc.setText(desc);
        ivPushGuideOpen.setText(btnText);
    }

    @Override
    public void onGetPushSwitchDialogInfoSuccess(@NonNull PushSwitchTipsInfo tipsInfo) {
        if (tipsInfo.getCard() != null) {
            KV.saveUserValue(SPKeys.PushGuide.KEY_NOTIFICATION_DIALOG_FREQUENCY, tipsInfo.getCard().getUpdateFrequency());
            if (pushSwitchGuideHelper == null) {
                pushSwitchGuideHelper = new PushSwitchGuideHelper();
            }
            pushSwitchGuideHelper.showPushGuideDialog(getActivity(), tipsInfo.getCard());
        } else if (tipsInfo.getCardV2() != null) {
            KV.saveUserValue(SPKeys.PushGuide.KEY_NOTIFICATION_DIALOG_FREQUENCY, tipsInfo.getCardV2().getUpdateFrequency());
            if (pushSwitchGuideHelper == null) {
                pushSwitchGuideHelper = new PushSwitchGuideHelper();
            }
            pushSwitchGuideHelper.showPushGuideDialogV2(getActivity(), tipsInfo.getCardV2());
        }
    }

    @Override
    public void hidePushGuideTips() {
        if (viewStubPushGuide != null && viewStubPushGuide.getVisibility() == View.VISIBLE) {
            viewStubPushGuide.setVisibility(View.GONE);
        }
        if (innerFragmentContainer != null) {
            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) innerFragmentContainer.getLayoutParams();
            lp.topMargin = 0;
            innerFragmentContainer.setLayoutParams(lp);
        }
    }

    @Override
    public void configEnterBar(SessionEnterBarResponse response) {
        entranceInfo = response.getFloatingWindow();
        floatWindowCheck();
    }

    @Override
    public void clearAllUnread() {
        innerFragment.clearAllUnread();
    }

    @Override
    public SessionModel getSessionModelInUi(String sessionKey) {
        return innerFragment.getSessionModel(sessionKey);
    }

    @Override
    public List<SessionModel> getVisibleChatSessionModel() {
        return innerFragment.getVisibleChatSessionModel();
    }

    @Override
    public void showSpaceView(SessionSpaceBean bean) {
        if (viewStubSpace.getVisibility() == View.GONE) {
            viewStubSpace.setVisibility(View.VISIBLE);
            spaceView = viewStubSpace.getStubView().findViewById(R.id.space_view);
            spaceView.setListener(() -> hasClickFromSpace = true);
        }
        if (spaceView != null) {
            KV.saveUserValue(SPKeys.User.SessionSpace.KEY_CARD_DISMISS_TYPE, -1);
            spaceView.setData(bean);
        }
    }

    @Override
    public void showSearchMenu() {
//        checkSearchMenuShow(mPresenter.isSearchMenuShow());
    }

    /**
     * 曝光埋点
     */
    private void logExposure(Event.Action action) {
        ExposureEvent.create(ExposureEvent.Type.Normal)
                .page(EVPage.Msg.Chatlist)
                .action(action)
                .submit();
    }

    /**
     * 点击埋点
     */
    private void logClick(Event.Action action) {
        ClickEvent.create()
                .page(EVPage.Msg.Chatlist)
                .action(action)
                .submit();
    }

    @Nullable
    @Override
    public Event.Page getPVPage() {
        return EVPage.Msg.Chatlist;
    }

    /**
     * 获得异步任务需要的唯一ID
     *
     * @return
     */
    public Object getTaskTag() {
        return this.getClass().getName() + '@' + Integer.toHexString(this.hashCode());
    }

    private void gotoNoticeSetting() {
        SettingGotoHelper.INSTANCE.gotoNoticeSetting(getContext());
    }

    public MillionEntranceManager getMillionEntranceManager() {
        if (mMillionEntranceManager == null) {
            mMillionEntranceManager = MillionEntranceManager.obtain("message_frame");
        }
        return mMillionEntranceManager;
    }

    protected void onMove(float initX, float initY, float deltaX, float deltaY) {
        if (mMillionFloatScrollValueFeed != null && Math.abs(deltaY) > Math.abs(deltaX)) {
            mMillionFloatScrollValueFeed.onScrolled((int) initY, (int) deltaY);
        }
    }

    protected void floatWindowCheck() {
        try {
            if (entranceInfo == null) {
                return;
            }
            MillionEntranceManager millionEntranceManager = getMillionEntranceManager();
            if (!hideEntrance && entranceInfo != null && (!TextUtils.isEmpty(entranceInfo.getBgImg())
                    || !TextUtils.isEmpty(entranceInfo.getBackTitle())
                    || !TextUtils.isEmpty(entranceInfo.getBackSubtitle()))) {
                boolean isAdd = millionEntranceManager.isAdded();
                if (!isAdd) {
                    mMillionFloatScrollValueFeed = millionEntranceManager
                            .addMillionFloatViewToParent(getWrapperLayout());
                }
                millionEntranceManager.logExp(entranceInfo);
                millionEntranceManager.refreshData(entranceInfo);
            } else {
                boolean remove = millionEntranceManager.remove(getWrapperLayout());
                if (remove) {
                    mMillionFloatScrollValueFeed = null;
                    mMillionEntranceManager = null;
                }
            }
        } catch (Throwable e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
    }

    public void setHideEntrance(boolean hideEntrance) {
        this.hideEntrance = hideEntrance;
    }

    public ViewGroup getWrapperLayout() {
        return listPageLayout;
    }

    private void releaseProfileUserUseCase() {
        if (commonUniUserInfoUseCase != null) {
            commonUniUserInfoUseCase.dispose();
        }
    }
}
