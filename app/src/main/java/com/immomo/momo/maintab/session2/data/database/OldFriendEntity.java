package com.immomo.momo.maintab.session2.data.database;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Generated;

@Entity(nameInDb = "old_friend_session",generateConstructors = false)
public class OldFriendEntity {
    @Id
    String sessionId;

    public OldFriendEntity() {
    }

    public OldFriendEntity(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getSessionId() {
        return this.sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
}
