package com.immomo.momo.maintab;

/**
 * Created by ruan<PERSON><PERSON> on 30/11/2017.
 */

public class SessionRefreshTimeUtil {

    public static final long FORCE_REFRESH_PROTECT_TIME = 3_000_000_000L; // nano time

    private static long SESSION_LIST_INIT_TIMESTAMP = System.nanoTime();

    public static long getSessionListInitTimestamp() {
        return SESSION_LIST_INIT_TIMESTAMP;
    }

    public static void setSessionListInitTimestamp(long sessionListInitTimestamp) {
        SESSION_LIST_INIT_TIMESTAMP = sessionListInitTimestamp;
    }

}
