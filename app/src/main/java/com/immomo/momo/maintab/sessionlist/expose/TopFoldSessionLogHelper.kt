package com.immomo.momo.maintab.sessionlist.expose

import com.immomo.lcapt.evlog.EVLog
import com.immomo.momo.maintab.session2.ITopFoldSessionLog

/**
 * 过滤两秒重复曝光
 * CREATED BY liu.chong
 * AT 2022/3/14
 */
object TopFoldSessionLogHelper {
    private const val TIME_INTERVAL = 2000

    private val topFoldedTimeMap = hashMapOf<String, Long>()
    private val notificationsTimeMap = hashMapOf<String, Long>()
    private val bannerTimeMap = hashMapOf<String, Long>()


    fun logNotificationSessionClick(
        whichItem: String,
        isRedDot: Boolean,
        newsNumber: Int,
        position: Int,
        updateTimeStr: String,
        msgText: String,
        remoteID: String,
        bizName: String
    ) {
        EVLog.create(ITopFoldSessionLog::class.java).foldedNotificationItemClick(
            whichItem,
            if (isRedDot) 1 else 0,
            newsNumber,
            position,
            updateTimeStr,
            msgText,
            remoteID,
            bizName
        )
    }

    fun logNotificationSessionShow(
        whichItem: String,
        isRedDot: Boolean,
        newsNumber: Int,
        position: Int,
        updateTimeStr: String,
        msgText: String,
        remoteID: String,
        bizName: String
    ) {
        val key = whichItem + remoteID
        notificationsTimeMap.getOrPut(key) { 0 }
            .let {
                if (System.currentTimeMillis() - it > TIME_INTERVAL) {
                    notificationsTimeMap[key] = System.currentTimeMillis()

                }
            }
    }

    fun logBannerClick(position: Int, id: String, url: String) {
        EVLog.create(ITopFoldSessionLog::class.java).foldedNotificationBannerClick(
            url, id, position
        )
    }

    fun logBannerShow(position: Int, id: String, url: String) {
        bannerTimeMap.getOrPut(id) { 0 }
            .let {
                if (System.currentTimeMillis() - it > TIME_INTERVAL) {
                    bannerTimeMap[id] = System.currentTimeMillis()
                    EVLog.create(ITopFoldSessionLog::class.java).foldedNotificationBannerShow(
                        url, id, position
                    )
                }
            }
    }
}