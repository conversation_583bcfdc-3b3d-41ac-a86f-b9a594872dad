package com.immomo.momo.maintab.session2.data.repository

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import com.immomo.framework.account.MessageManager
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.storage.preference.SPKeys
import com.immomo.framework.utils.UIUtils
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.momo.R
import com.immomo.momo.android.broadcast.ReflushSessionUnreadReceiver
import com.immomo.momo.maintab.model.SessionOnlineBean
import com.immomo.momo.maintab.model.UserOnlineStatus
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.data.manager.SessionMetadata
import com.immomo.momo.maintab.session2.data.response.toModel
import com.immomo.momo.maintab.session2.domain.model.FoldNotificationBannersModel
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.maintab.session2.domain.model.SessionNoticeInfoModel
import com.immomo.momo.maintab.session2.domain.model.SessionPaginationModel
import com.immomo.momo.maintab.session2.domain.repository.ISessionListRepository
import com.immomo.momo.maintab.session2.domain.repository.SessionListReqParam
import com.immomo.momo.maintab.session2.utils.SessionUnreadHelper
import com.immomo.momo.maintab.sessionlist.bean.SessionTopOperatorData
import com.immomo.momo.maintab.sessionlist.task.RefreshOnlineStatusTask
import com.immomo.momo.protocol.http.SessionApi
import com.immomo.momo.protocol.imjson.receiver.MessageKeys
import com.immomo.momo.util.MomoKit
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine


class SessionListRepository : ISessionListRepository {
    override fun getSessionList(param: SessionListReqParam): Flow<SessionPaginationModel> = flow {
        emit(
            SessionPaginationModel(
                count = param.count,
                index = param.index,
                lists = emptyList(),
                remain = 0
            )
        )
    }

    override fun getAllUnreadCount() = flow {
        emit(SessionUnreadHelper.getAllSessionUnread())
    }

    override fun getBannerList(): Flow<FoldNotificationBannersModel> = flow {
        emit(
            SessionApi.getInstance().foldedNotificationBannerList.toModel()
        )
    }

    override fun observeInteract(): Flow<SessionNoticeInfoModel> {
        fun getNoticeInfo(): SessionNoticeInfoModel {
            val text = KV.getUserStr(
                SPKeys.User.NoticeMsg.KEY_INTERACTION_NOTICE_TEXT,
                UIUtils.getString(R.string.session_notice_desc_default)
            )
            val unReadNoticeCount =
                KV.getUserInt(SPKeys.User.NoticeMsg.KEY_INTERACTION_NOTICE_UNREAD_COUNT, 0)

            val gotoTabType =
                KV.getUserInt(SPKeys.User.NoticeMsg.KEY_INTERACTION_NOTICE_TAB, 0)
            return SessionNoticeInfoModel(unReadNoticeCount, text, gotoTabType)
        }


        return callbackFlow {
            offer(getNoticeInfo())
            val subscriber = object : MessageManager.MessageSubscriber {
                override fun onMessageReceive(bundle: Bundle?, action: String?): Boolean {
                    offer(getNoticeInfo())
                    return false
                }
            }
            MessageManager.registerMessageReceiver(
                this@SessionListRepository,
                subscriber,
                400,
                MessageKeys.ACTION_INTERACTION_NOTICE
            )
            val receiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    launch(MMDispatchers.Main) {
                        offer(getNoticeInfo())
                    }
                }
            }
            MomoKit.app.registerReceiver(
                receiver,
                IntentFilter(ReflushSessionUnreadReceiver.ReflushNotice)
            )
            awaitClose {
                MomoKit.app.unregisterReceiver(receiver)
                MessageManager.unregisterMessageReceiver(this@SessionListRepository)
            }
        }
    }

    override fun findAllSessions(
        size: Int,
        filter: (SessionMetadata) -> Boolean
    ): List<SessionModel> {
        return SessionManager.get().findAllSessions(
            size, filter
        ) {
            it.sessionKey
        }.let {
            SessionManager.get().getSessionModelsByKeys(
                it, size, true
            )
        }
    }

    override suspend fun refreshSessionUserStatus(sessionKeyList: Set<String?>): Map<String, UserOnlineStatus> {
        withContext(MMDispatchers.Main) {
            MomoTaskExecutor.cancleAllTasksByTag(this@SessionListRepository)
        }
        withTimeoutOrNull(5000) {
            suspendCoroutine<SessionOnlineBean> {
                val refreshOnlineStatusTask =
                    object : RefreshOnlineStatusTask(sessionKeyList.toMutableSet()) {
                        override fun onTaskSuccess(onlineBean: SessionOnlineBean) {
                            super.onTaskSuccess(onlineBean)
                            it.resume(onlineBean)
                        }

                        override fun onTaskError(e: Exception) {
                            it.resumeWithException(e)
                        }
                    }
                MomoTaskExecutor.executeUserTask(
                    this@SessionListRepository,
                    refreshOnlineStatusTask
                )
            }
        }?.also {
            return it.userOnlineList
        }
        return emptyMap()
    }

    override fun requestSessionTopOperator(map: Map<String, String>): Flow<SessionTopOperatorData> =
        flow {
            emit(
                SessionApi.getInstance().requestSessionTopOperator(map)
            )
        }

    override fun requestLocalSessionTopOperator(): Flow<SessionTopOperatorData> = flow {
        emit(
            SessionApi.getInstance().requestLocalSessionTopOperator()
        )
    }
}