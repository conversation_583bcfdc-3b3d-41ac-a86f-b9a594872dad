package com.immomo.momo.maintab.session2.domain.interactor

import com.immomo.android.mm.kobalt.domain.fx.Option
import com.immomo.android.mm.kobalt.domain.interactor.UseCase
import com.immomo.momo.maintab.session2.domain.model.SessionNoticeInfoModel
import com.immomo.momo.maintab.session2.domain.repository.ISessionListRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow

class ObserveInteractNotificationUseCase(
    dispatcher: CoroutineDispatcher,
    private val repository: ISessionListRepository
) : UseCase<SessionNoticeInfoModel, Unit>(dispatcher) {
    override fun build(param: Option<Unit>): Flow<SessionNoticeInfoModel> {
        return repository.observeInteract()
    }
}