package com.immomo.momo.maintab.session2.data.database;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.immomo.momo.maintab.session2.SessionDefinitionManager;
import com.immomo.momo.service.bean.FolderType;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Index;
import org.greenrobot.greendao.annotation.Unique;
import org.greenrobot.greendao.annotation.Generated;

/**
 * 注意：在SessionEntity里面加的字段，
 * 都必须考虑是否需要加入{@link SessionSnapshotManager#takeQuickSnapshot(SessionEntity)}
 */
@Entity(nameInDb = "session_entity",
        indexes = {
                @Index(value = "sessionKey, orderId"),
                @Index(value = "sessionType, sessionId"),
                @Index(value = "contentIdx1"),
                @Index(value = "contentIdx2"),
                @Index(value = "contentIdx3"),
                @Index(value = "contentIdx4"),
                @Index(value = "contentIdx5"),
        },
        generateConstructors = false)
public class SessionEntity {
    public static final int CACHE_MEMORY = 0b01;
    public static final int CACHE_DATABASE = 0b10;

    /**
     * 由sessionType和sessionId组成
     */
    @Id
    @Unique
    @NonNull
    private String sessionKey;

    private String sessionType;
    private String sessionId;
    @Nullable
    private Integer foldType;
    @Nullable
    private Integer foldTypeV3;

    @Nullable
    private String contentStr;
    @Nullable
    private transient Object contentObj;

    @Nullable
    private String lastMsgId;
    //由于有取消上浮的消息，lastMsgId和lastMsgTime可能对应不上
    private long lastMsgTime;

    private long recommendTime;
    //是否聊天置顶
    private boolean isSticky;
    //排序id
    private long orderId;
    private long lastFetchTime;

    private int unreadMessageCount;
    private int silentMessageCount;

    //最后一条处理过的消息时间
    private long lastProcessedMsgTime;
    private long lastUpdateId;
    private boolean markAsDeleted = false;

    @Nullable
    private String contentIdx1;
    @Nullable
    private String contentIdx2;
    @Nullable
    private String contentIdx3;
    @Nullable
    private String contentIdx4;
    @Nullable
    private String contentIdx5;

    private transient int cacheStatus;

    public boolean canBeShown(Boolean skipFoldCheck) {
        return !markAsDeleted && (skipFoldCheck ||
                (foldType == null || foldType == FolderType.Default));
    }

    public SessionEntity() {
    }

    public SessionEntity(@NonNull SessionContent contentObj) {
        this.contentObj = contentObj;
    }

    @NonNull
    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(@NonNull String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public String getSessionType() {
        return sessionType;
    }

    public void setSessionType(String sessionType) {
        this.sessionType = sessionType;
    }

    @NonNull
    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(@NonNull String sessionId) {
        this.sessionId = sessionId;
    }

    @Nullable
    public Integer getFoldType() {
        return foldType;
    }

    public void setFoldType(@Nullable Integer foldType) {
        this.foldType = foldType;
    }

    @Nullable
    public Integer getFoldTypeV3() {
        return foldTypeV3;
    }

    public void setFoldTypeV3(@Nullable Integer foldTypeV3) {
        this.foldTypeV3 = foldTypeV3;
    }

    @Nullable
    public String getContentStr() {
        return contentStr;
    }

    public void setContentStr(@Nullable String contentStr) {
        this.contentStr = contentStr;
    }

    @Nullable
    public Object getContentObj() {
        return contentObj;
    }

    public void setContentObj(@Nullable Object contentObj) {
        this.contentObj = contentObj;
    }

    @Nullable
    public String getLastMsgId() {
        return lastMsgId;
    }

    public void setLastMsgId(@Nullable String lastMsgId) {
        this.lastMsgId = lastMsgId;
    }

    public long getLastMsgTime() {
        return lastMsgTime;
    }

    public void setLastMsgTime(long lastMsgTime) {
        this.lastMsgTime = lastMsgTime;
    }

    public long getRecommendTime() {
        return recommendTime;
    }

    public void setRecommendTime(long recommendTime) {
        this.recommendTime = recommendTime;
    }

    public boolean getIsSticky() {
        return this.isSticky;
    }

    public void setIsSticky(boolean isSticky) {
        this.isSticky = isSticky;
    }

    public long getOrderId() {
        return orderId;
    }

    public void setOrderId(long orderId) {
        this.orderId = orderId;
    }

    public long getLastFetchTime() {
        return lastFetchTime;
    }

    public void setLastFetchTime(long lastFetchTime) {
        this.lastFetchTime = lastFetchTime;
    }

    public int getUnreadMessageCount() {
        return unreadMessageCount;
    }

    public void setUnreadMessageCount(int unreadMessageCount) {
        this.unreadMessageCount = unreadMessageCount;
    }

    public int getSilentMessageCount() {
        return silentMessageCount;
    }

    public void setSilentMessageCount(int silentMessageCount) {
        this.silentMessageCount = silentMessageCount;
    }

    public long getLastProcessedMsgTime() {
        return lastProcessedMsgTime;
    }

    public void setLastProcessedMsgTime(long lastProcessedMsgTime) {
        this.lastProcessedMsgTime = lastProcessedMsgTime;
    }

    public long getLastUpdateId() {
        return lastUpdateId;
    }

    public void setLastUpdateId(long lastUpdateId) {
        this.lastUpdateId = lastUpdateId;
    }

    public boolean isMarkAsDeleted() {
        return markAsDeleted;
    }

    public boolean getMarkAsDeleted() {
        return this.markAsDeleted;
    }

    public void setMarkAsDeleted(boolean markAsDeleted) {
        this.markAsDeleted = markAsDeleted;
    }

    @Nullable
    public String getContentIdx1() {
        return contentIdx1;
    }

    public void setContentIdx1(@Nullable String contentIdx1) {
        this.contentIdx1 = contentIdx1;
    }

    @Nullable
    public String getContentIdx2() {
        return contentIdx2;
    }

    public void setContentIdx2(@Nullable String contentIdx2) {
        this.contentIdx2 = contentIdx2;
    }

    @Nullable
    public String getContentIdx3() {
        return contentIdx3;
    }

    public void setContentIdx3(@Nullable String contentIdx3) {
        this.contentIdx3 = contentIdx3;
    }

    @Nullable
    public String getContentIdx4() {
        return contentIdx4;
    }

    public void setContentIdx4(@Nullable String contentIdx4) {
        this.contentIdx4 = contentIdx4;
    }

    @Nullable
    public String getContentIdx5() {
        return contentIdx5;
    }

    public void setContentIdx5(@Nullable String contentIdx5) {
        this.contentIdx5 = contentIdx5;
    }

    public int getCacheStatus() {
        return cacheStatus;
    }

    public void setCacheStatus(int cacheStatus) {
        this.cacheStatus = cacheStatus;
    }

    @Override
    public String toString() {
        return "SessionEntity{" +
                "sessionKey='" + sessionKey + '\'' +
                ", unreadMessageCount=" + unreadMessageCount +
                ", sessionType='" + sessionType + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", foldType=" + foldType +
                ", contentStr='" + SessionDefinitionManager.INSTANCE.serialize(this) + '\'' +
                ", lastMsgId='" + lastMsgId + '\'' +
                ", lastMsgTime=" + lastMsgTime +
                ", recommendTime=" + recommendTime +
                ", isSticky=" + isSticky +
                ", orderId=" + orderId +
                ", lastFetchTime=" + lastFetchTime +
                ", silentMessageCount=" + silentMessageCount +
                ", lastProcessedMsgTime=" + lastProcessedMsgTime +
                ", lastUpdateId=" + lastUpdateId +
                ", markAsDeleted=" + markAsDeleted +
                ", cacheStatus=" + cacheStatus +
                '}';
    }
}
