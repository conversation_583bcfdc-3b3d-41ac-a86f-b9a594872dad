package com.immomo.momo.maintab.sessionlist

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.view.View
import android.view.ViewTreeObserver
import android.view.Window
import android.widget.Button
import android.widget.RadioButton
import android.widget.TextView
import com.immomo.framework.utils.UIUtils
import com.immomo.lcapt.evlog.EVLog
import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.momo.R
import com.immomo.momo.maintab.sessionlist.log.IPushGuideLog
import com.immomo.momo.mvp.maintab.broadcast.UpdateBasicUserInfoReceiver
import com.immomo.momo.protocol.http.MiPushApi
import com.immomo.momo.sessionnotice.bean.TipsInfoCardV2
import com.immomo.momo.statistics.EVAction
import com.immomo.momo.util.MomoKit
import com.immomo.svgaplayer.view.MomoSVGAImageView

class PushSwitchGuideDialogV2(context: Context) : Dialog(context, R.style.customDialog),
    View.OnClickListener {
    private var guideContainer: View
    private var ivAlbum: MomoSVGAImageView
    private var tvTitle: TextView
    private var tvDesc: TextView
    private var btnOk: Button
    private var cancelTv: TextView
    private var txTitle1: TextView
    private var txDesc1: TextView
    private var radioButton1: RadioButton
    private var txTitle2: TextView
    private var txDesc2: TextView
    private var radioButton2: RadioButton

    init {
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        setCancelable(false)
        setCanceledOnTouchOutside(false)

        setContentView(R.layout.dialog_push_switch_guide_v2)

        guideContainer = findViewById(R.id.guide_container)
        ivAlbum = findViewById(R.id.iv_album)
        tvTitle = findViewById(R.id.tv_title)
        tvDesc = findViewById(R.id.tv_desc)
        btnOk = findViewById(R.id.btn_ok)
        cancelTv = findViewById(R.id.cancel_tv)
        btnOk.setOnClickListener(this)
        cancelTv.setOnClickListener(this)
        txTitle1 = findViewById(R.id.tx_title1)
        txDesc1 = findViewById(R.id.tx_desc1)
        radioButton1 = findViewById(R.id.checkbox1)
        txTitle2 = findViewById(R.id.tx_title2)
        txDesc2 = findViewById(R.id.tx_desc2)
        radioButton2 = findViewById(R.id.checkbox2)
        guideContainer.viewTreeObserver.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                guideContainer.viewTreeObserver.removeOnGlobalLayoutListener(this)
                val params = guideContainer.layoutParams
                params?.let {
                    it.width = (UIUtils.getScreenWidth() * 0.84f).toInt()
                    guideContainer.layoutParams = it
                }
            }
        })
        radioButton1.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                radioButton2.isChecked = false
            }
        }
        radioButton2.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                radioButton1.isChecked = false
            }
        }
    }

    fun setData(card: TipsInfoCardV2) = with(card) {
        img?.let {
            if (!it.isNullOrEmpty()) {
                ivAlbum.startSVGAAnim(it, Int.MAX_VALUE)
            }
        }
        tvTitle.text = title
        tvDesc.text = desc
        btnOk.text = buttonText
        txTitle1.text = itemTitle1
        txDesc1.text = itemDesc1
        txTitle2.text = itemTitle2
        txDesc2.text = itemDesc2
    }

    override fun show() {
        super.show()
        EVLog.create(IPushGuideLog::class.java).onShowDialog()
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.btn_ok -> {
                if (radioButton1.isChecked) {
                    gotoSettingPage()
                } else {
                    MomoTaskExecutor.executeUserTask(hashCode(), SetOnlyAboutSelfSwitchTask())
                }
            }
            R.id.cancel_tv -> {
                dismiss()
            }
        }

        var logMap: MutableMap<String, String?> = mutableMapOf()
        logMap.put("button", if (v?.id == R.id.btn_ok) "open" else "close")
        logMap.put("type", if (radioButton1.isChecked) "all" else "just_look_at_me")
        EVLog.create(IPushGuideLog::class.java).onItemClick(logMap)
    }

    private fun gotoSettingPage() {
        MomoKit.gotoApplicationNotifySettings()
        dismiss()
    }

    override fun dismiss() {
        super.dismiss()
        MomoTaskExecutor.cancleAllTasksByTag(hashCode())
    }


    inner class SetOnlyAboutSelfSwitchTask : MomoTaskExecutor.Task<Any, Any, Boolean>("") {
        @Throws(Exception::class)
        override fun executeTask(vararg params: Any?): Boolean {
            MiPushApi.getInstance().setOnlyAboutSelf()
            return true
        }

        override fun onTaskError(e: Exception?) {
            super.onTaskError(e)
            gotoSettingPage()
        }

        override fun onTaskSuccess(result: Boolean) {
            val intent = Intent(UpdateBasicUserInfoReceiver.ACTION_UPDAT_BASE_USERINFO)
            com.immomo.momo.MomoKit.getContext().sendBroadcast(intent)
            gotoSettingPage()
        }
    }
}