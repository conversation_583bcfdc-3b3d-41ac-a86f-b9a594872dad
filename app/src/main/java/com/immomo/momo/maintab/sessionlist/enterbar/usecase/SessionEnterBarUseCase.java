package com.immomo.momo.maintab.sessionlist.enterbar.usecase;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.immomo.framework.rxjava.executor.PostExecutionThread;
import com.immomo.framework.rxjava.executor.ThreadExecutor;
import com.immomo.framework.rxjava.interactor.UseCase;
import com.immomo.momo.mvp.common.model.ModelManager;
import com.immomo.momo.service.sessions.ISessionRepository;

import io.reactivex.Flowable;

public class SessionEnterBarUseCase extends UseCase<SessionEnterBarResponse, String> {
    public SessionEnterBarUseCase(@NonNull ThreadExecutor threadExecutor, @NonNull PostExecutionThread postExecutionThread) {
        super(threadExecutor, postExecutionThread);
    }

    @NonNull
    @Override
    protected Flowable<SessionEnterBarResponse> buildUseCaseFlowable(@Nullable String s) {
        return ModelManager.getModel(ISessionRepository.class).fetchEnterBarConfig(s);
    }
}
