package com.immomo.momo.maintab;

import com.immomo.mmutil.task.ThreadUtils;
import com.immomo.momo.android.synctask.Callback;
import com.immomo.momo.android.synctask.MomoThread;
import com.immomo.momo.protocol.http.AppApi;

import java.io.File;

import com.immomo.mmutil.MD5Utils;

/**
 * Created by joel on 2016/10/19.
 * <p>
 * Momo Tech 2011-2016 © All Rights Reserved.
 */

public class DownloadSplashFileThread extends MomoThread<File> {
    private String url;
    private File fileHome;

    public DownloadSplashFileThread(String url, File fileHome,Callback<File> callback) {
        super(callback);
        this.url = url;
        this.fileHome = fileHome;
    }

    @Override
    public void run() {
        String md5FileName = MD5Utils.getMD5(url);
        File resultFile = new File(fileHome, md5FileName);
        File tmpFile = new File(resultFile.getParentFile(), System.currentTimeMillis() + "");
        try {
            AppApi.getInstance().downloadFile(url, tmpFile, null);

            if (resultFile.exists()) {
                resultFile.delete();
            }

            tmpFile.renameTo(resultFile);

            callBack(resultFile);
        } catch (Throwable ex) {
            if (tmpFile != null && tmpFile.exists()) {
                tmpFile.delete();
            }

            if (resultFile != null && resultFile.exists()) {
                resultFile.delete();
            }
            callBack(null);
        } finally {

        }
    }

    @Override
    public void execute() {
        ThreadUtils.execute(ThreadUtils.TYPE_RIGHT_NOW, this);
    }
}
