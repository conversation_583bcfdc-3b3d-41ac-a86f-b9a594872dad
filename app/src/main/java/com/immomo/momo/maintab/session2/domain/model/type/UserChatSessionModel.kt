package com.immomo.momo.maintab.session2.domain.model.type

import android.text.TextUtils
import com.immomo.android.mm.kobalt.domain.fx.Option
import com.immomo.momo.maintab.model.TextIconSessionTag
import com.immomo.momo.maintab.model.UserAvatarFrameWrapper
import com.immomo.momo.maintab.model.UserBadgeWrapper
import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import java.util.*

data class UserOnlineTagModel(
    val name: String,
    val tagColor: String,
    val action: String,
    val showAnim: Boolean,
    val roomPattern: String,
    val needLog: Boolean = true
)

data class UserChatTagModel(
    val text: String
)

data class UserChatSessionModel(
    override val baseInfo: BaseSessionInfo,
    override var chatId: String,
    override var desc: String,
    override val draftString: String,
    override val draftQuoteString: String,
    override val lastMessageType: Int,
    override val showMessageStatus: Boolean,
    override val lastMessageStatus: Int,

    val userAvatar: String,
    val userName: String,
    val userIsVip: Boolean,

    val userOnlineTag: Option<UserOnlineTagModel>,
    val userLocationTimestamp: Long,
    val userChatTag: Option<UserChatTagModel>,
    /**
     * 有没有红包
     */
    val isHongbao: Boolean,
    val isGift: Boolean,
    val isMissedFriendCall: Boolean,
    val missedFriendCallDesc: String,
    val isDianDianCard: Boolean,
    val isQuestionMatch: Boolean,

    val isType28: Boolean,
    val lastType28Prompt: String,
    val type28AppId: String,

    /**
     * [xxxx]特殊前缀,如果最后一条消息是任务礼物，则设置 specialText
     */
    val specialText: String,

    /**
     * 类似  点点匹配 这种红色的前缀
     */
    val pushPrefix: String,

    val sessionBusinessType: Int,
    val onlineMsgTime: String,
//    val noteFeedLock: Int,
    val officialOperation: Int,
    val cellTagUniformLabels: UserBadgeWrapper? = null, // 用户标签
    val textIconTag: TextIconSessionTag? = null, // 图片带文字标签
    val sessionTagLogMap: String? = null, // textIconTag打点
    val fireIcon: String? = null,
    val fireIconDark: String? = null,
    val fireSign: String? = null,
    val avatarFrame: UserAvatarFrameWrapper? = null // 头像框
) : ChatSessionModel {

    // 类型为type28且含有prompt字段（不一定展示出来，实际展示时有优先级判断）
    fun canShowType28Prompt(): Boolean {
        return isType28 && !TextUtils.isEmpty(lastType28Prompt)
    }

    override fun hasSpecialNotice(): Boolean {
        return isHongbao || isGift || isDianDianCard || isQuestionMatch || specialText.isNotEmpty()
                || canShowType28Prompt()
    }

    fun copyWith(unreadMessageCount: Int) = copy(
        baseInfo = baseInfo.copy(unreadMessageCount = unreadMessageCount)
    )
}