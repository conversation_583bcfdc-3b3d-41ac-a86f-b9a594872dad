package com.immomo.momo.maintab.usecase;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.immomo.framework.rxjava.executor.PostExecutionThread;
import com.immomo.framework.rxjava.executor.ThreadExecutor;
import com.immomo.framework.rxjava.interactor.UseCase;
import com.immomo.momo.service.bean.SayhiSession;
import com.immomo.momo.service.sessions.ISessionRepository;

import io.reactivex.Flowable;

/**
 * Created by huang.liang<PERSON><PERSON> on 2017/11/10.
 *
 * Momo Tech 2011-2017 © All Rights Reserved.
 */

public class ClearSessionUseCase extends UseCase<Object, Integer> {
    private ISessionRepository sessionRepository;

    public ClearSessionUseCase(@NonNull ThreadExecutor threadExecutor, ISessionRepository repository, @NonNull PostExecutionThread postExecutionThread) {
        super(threadExecutor, postExecutionThread);

        this.sessionRepository = repository;
    }

    @NonNull
    @Override
    protected Flowable buildUseCaseFlowable(@Nullable Integer integer) {
        return sessionRepository.clearSayhiSessions(SayhiSession.FROM_TYPE_HARASS_GREETING);
    }
}
