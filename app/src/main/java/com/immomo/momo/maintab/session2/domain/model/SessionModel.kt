package com.immomo.momo.maintab.session2.domain.model

import com.immomo.android.mm.kobalt.domain.model.WithUniqueId
import com.immomo.framework.common.UniqueHash
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.manager.SessionKey

interface SessionModel : WithUniqueId<SessionModel> {
    val baseInfo: BaseSessionInfo

    val sessionKey: SessionKey
        get() = SessionKey.fromString(baseInfo.sessionKey)

    val sessionKeyStr: String
        get() = baseInfo.sessionKey

    val sessionId: String get() = baseInfo.sessionId

    override val uniqueId: Long
        get() = UniqueHash.id(baseInfo.sessionKey)

    override val uniqueCategory: Class<out SessionModel>
        get() = SessionModel::class.java

    fun hasUnread() = baseInfo.silentMessageCount > 0 || baseInfo.unreadMessageCount > 0

    fun isInCacheMemory() =
        (baseInfo.cacheStatus and SessionEntity.CACHE_MEMORY) == SessionEntity.CACHE_MEMORY

    fun isInCacheDb() =
        (baseInfo.cacheStatus and SessionEntity.CACHE_DATABASE) == SessionEntity.CACHE_DATABASE

    fun getUnreadCnt() =
        if (baseInfo.silentMessageCount > 0) baseInfo.silentMessageCount else baseInfo.unreadMessageCount
}

data class BaseSessionInfo(
    val sessionKey: String,
    val sessionId: String,
    val foldType: Int,
    val foldTypeV3: Int,
    //最后一条消息id
    val lastMsgId: String,
    val lastMessageTime: Long,
    val recommendTime: Long,
    //排序id
    val orderId: Long,
    var unreadMessageCount: Int,
    val silentMessageCount: Int,
    val lastFetchTime: Long,
    val sticky: Boolean,
    val forceRefreshId: Int,
    val cacheStatus: Int,
    val markedAsDeleted: Boolean
)