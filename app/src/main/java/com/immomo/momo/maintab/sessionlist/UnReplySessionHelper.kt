package com.immomo.momo.maintab.sessionlist

import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.storage.preference.SPKeys
import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.momo.MomoKit
import com.immomo.momo.message.http.HiSessionApi
import com.immomo.momo.messages.service.SingleMsgService
import com.immomo.momo.mvp.maintab.mainimpl.appconfig.apt.SessionFoldTestConfigV2Getter
import com.immomo.momo.service.bean.FolderType
import com.immomo.momo.service.sessions.SessionService
import com.immomo.momo.util.CollectionUtils
import com.immomo.momo.util.GsonUtils
import org.eclipse.jetty.util.ConcurrentHashSet

/**
 * 冷启时，对未回复的招呼进行迁移
 * 用户第一次安装通过扫描session表交给api校验未回复的招呼
 */
class UnReplySessionHelper {
    private val STATUS_NO = 0 //没有开始
    private val STATUS_DOING = 1 //进行中
    private val STATUS_SUC = 2 //迁移完成
    private val STATUS_FAIL = 3 //迁移失败
    private var migrateStatus = STATUS_NO

    private var isDestroy = false

    //本次是否有新迁移的session
    private var isIncrement = false

    companion object {
        //当有新新迁移的session时，入口要展示动画
        @kotlin.jvm.JvmField
        var showAnim = false
    }

    init {
        showAnim = false
    }

    private inner class MigrateSessionTask : MomoTaskExecutor.Task<String?, Int?, Int>("") {
        override fun executeTask(vararg params: String?): Int? {
            doMigrateToUnreplyByApi()
            if (isIncrement) {
                SessionService.getInstance().updateNoReplySession()
            } else {
                //扫描不到未回复的消息，就把允许条件关闭，防止每次启动都做无用的查询
                UnReplySessionEnterHelper.setShowUnreplyEnter(false)
            }
            return migrateStatus
        }

        override fun onTaskSuccess(status: Int) {
            super.onTaskSuccess(status)
            migrateStatus = STATUS_SUC
            if (!isDestroy && isIncrement) {
                showAnim = true
                val it = Intent(SessionListReceiver.ActionReLoadAll)
                LocalBroadcastManager.getInstance(MomoKit.getApp()).sendBroadcast(it)
            }
        }

        override fun onTaskError(e: Exception) {
            migrateStatus = STATUS_FAIL
        }


        /**
         * 首次安装通过api校验迁移
         *
         * @return
         * @throws Exception
         */
        private fun doMigrateToUnreplyByApi() {
            //需要被校验的最大限制
            val maxCount = 100
            val count = 20

            var hasMore = true
            var index = 0
            val remoteIds: MutableList<String> = ArrayList()
            while (!isDestroy && hasMore && index < maxCount) {
                val sessionList =
                    SessionService.getInstance().findUncertainReplySessions(index, count)
                val sessionCount = sessionList?.size ?: 0
                hasMore = sessionCount >= count

                if (sessionCount > 0) {
                    index += sessionCount

                    val params = sessionList.filter {
                        SingleMsgService.getInstance().getReceiveMessageCount(it) <= 0
                    }

                    if (CollectionUtils.isEmpty(params)) {
                        continue
                    }
                    HiSessionApi.getInstance()
                        .checkUnreplyByApi(GsonUtils.g().toJson(params))?.norepliedList?.let {
                            if (it.isNotEmpty()) {
                                remoteIds.addAll(it)
                            }
                        }
                }
            }
            if (remoteIds.size > 0) {
                SessionService.getInstance()
                    .updateSessionFolderType(FolderType.Unreply, remoteIds.toTypedArray())
                isIncrement = true
            }
        }
    }

    private fun hasTag(): Int {
        return hashCode()
    }

    fun migrateToUnreply() {
        if (isDestroy || migrateStatus == STATUS_SUC || migrateStatus == STATUS_DOING) {
            return
        }
        migrateStatus = STATUS_DOING
        MomoTaskExecutor.executeUserTask(hasTag(), MigrateSessionTask())

    }

    fun destroy() {
        isDestroy = true
        MomoTaskExecutor.cancleAllTasksByTag(hasTag())
    }

}

/**
 * 打出的招呼入口相关
 */
class UnReplySessionEnterHelper {
    companion object {
        //是否满足展示"打出的招呼"入口条件
        private const val KV_SHOW_UNREPLY_ENTER = "need_show_unreply_enter"

        @kotlin.jvm.JvmStatic
        fun setShowUnreplyEnter(isShow: Boolean) {
            KV.saveUserValue(KV_SHOW_UNREPLY_ENTER, isShow)
        }

        @kotlin.jvm.JvmStatic
        fun isShowUnreplyEnter() = KV.getUserBool(KV_SHOW_UNREPLY_ENTER, false)

        @kotlin.jvm.JvmField
        var hasUnreplyEnter: Boolean = false
    }
}

/**
 * 未回复的招呼缓存
 */
class UnreplySessionCacheHelper {

    companion object {
        private val unreplyCache = ConcurrentHashSet<String>()

        @kotlin.jvm.JvmStatic
        fun updateUnreplyCache(remoteId: String) {
            unreplyCache.add(remoteId)
        }

        @kotlin.jvm.JvmStatic
        fun resetUnreplyCache() {
            unreplyCache.clear()
        }

        @kotlin.jvm.JvmStatic
        fun removeUnreplyFromCache(remoteId: String?) {
            if (remoteId == null) return
            unreplyCache.remove(remoteId)
        }

        @kotlin.jvm.JvmStatic
        fun removeUnreplyFromCache(remoteIds: Collection<String>) {
            unreplyCache.removeAll(remoteIds)
        }

        @JvmStatic
        fun needUnreply(remoteId: String?): Boolean {
            if (remoteId == null) return false
            return unreplyCache.contains(remoteId)
        }

    }
}

class SayHiSessionListHelper {
    companion object {
        @kotlin.jvm.JvmStatic
        fun addSayHiId(momoId: String) {
            var ids = KV.getUserStr(SPKeys.SessionFold.KEY_SESSION_SAY_HI_LIST, "")
            ids = if (ids.isNullOrEmpty()) momoId else "$ids,$momoId"
            if (ids.split(",").size > SessionFoldTestConfigV2Getter.get().msgFoldCount()) {
                UnReplySessionEnterHelper.setShowUnreplyEnter(true)
            } else {
                KV.saveUserValue(SPKeys.SessionFold.KEY_SESSION_SAY_HI_LIST, ids)
            }
        }
    }
}