package com.immomo.momo.maintab.sessionlist.bean

import androidx.annotation.Keep
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

/**
 * {
 *   "remoteId": "911572890",
 *   "title": "对话已结束",
 *   "avatarUrl": "https://s.momocdn.com/s1/u/jajjjfhid/maskChat/male.png",
 *   "name": "哈哈哈哈哈哈哈哈哈哈哈",
 *   "desc": "气氛值达到5，未能解锁彼此主页",
 *   "punishTitle": "确认要惩罚他吗？",
 *   "punishDesc": "被累计惩罚5次，每天将被限制仅可匹配1次",
 *   "punishInfo": "每天有1次惩罚机会，聊满气氛值当日额外获得1次惩罚机会，凌晨5点刷新",
 *   "unlocked": false,
 *   "finalMood": 5,
 *   "remainMatchCount": 24,
 *   "remainPunishCount": 1
 * }
 */
@Keep
data class MaskChatQuitData(
        @Expose
        @SerializedName("remoteId")
        var remoteId: String = "",
        @Expose
        @SerializedName("roomId")
        var roomId: Int = 0,
        @Expose
        @SerializedName("title")
        var title: String = "",
        @Expose
        @SerializedName("avatarUrl")
        var avatarUrl: String = "",
        @Expose
        @SerializedName("name")
        var name: String = "",
        @Expose
        @SerializedName("desc")
        var desc: String? = null,
        @Expose
        @SerializedName("punishTitle")
        var punishTitle: String? = null,
        @Expose
        @SerializedName("punishDesc")
        var punishDesc: String? = null,
        @Expose
        @SerializedName("punishInfo")
        var punishInfo: String? = null,
        @Expose
        @SerializedName("unlocked")
        var unlocked: Boolean = false,
        @Expose
        @SerializedName("finalMood")
        var finalMood: Int = 0,
        @Expose
        @SerializedName("remainMatchCount")
        var remainMatchCount: Int = 0,
        @Expose
        @SerializedName("remainPunishCount")
        var remainPunishCount: Int = 0,
        @Expose
        @SerializedName("remoteGender")
        var remoteGender: String? = null,

        var extraData: String = "",

        @Expose
        @SerializedName("items")
        var items: Items?,

        @Expose
        @SerializedName("canClaimFreeFingerGuessCard")
        var canClaimFreeFingerGuessCard: Boolean = false
)
