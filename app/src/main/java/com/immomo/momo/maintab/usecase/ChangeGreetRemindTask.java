package com.immomo.momo.maintab.usecase;

import android.app.Activity;
import android.os.Bundle;

import com.immomo.framework.task.BaseDialogTask;
import com.immomo.mmstatistics.event.TaskEvent;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.MomoKit;
import com.immomo.momo.greet.GreetHelper;
import com.immomo.momo.im.GiftSayHiAppConfigV1;
import com.immomo.momo.maintab.sessionlist.SessionListFragment;
import com.immomo.momo.message.NewSayUIConfigV1;
import com.immomo.momo.protocol.http.SayHiApi;
import com.immomo.momo.service.bean.Session;
import com.immomo.momo.statistics.EVAction;
import com.immomo.momo.statistics.EVPage;

public class ChangeGreetRemindTask extends BaseDialogTask<Object, Object, Object> {
    private boolean forbid;
    private String source;

    public ChangeGreetRemindTask(boolean forbid, String source, Activity activity) {
        super(activity);
        this.forbid = forbid;
        this.source = source;
    }

    @Override
    protected Object executeTask(Object... objects) throws Exception {
        SayHiApi.getInstance().setGreetNoRemind(forbid);
        boolean updateDB = true;
        if (NewSayUIConfigV1.isUserNewUI()) {
            updateDB = false;
        }
        GreetHelper.setGreetNotRemindSetting(forbid ? 1 : 0, updateDB);
        return null;
    }

    @Override
    protected void onCancelled() {
        super.onCancelled();
        this.activity = null;
    }

    @Override
    protected void onTaskSuccess(Object o) {
        super.onTaskSuccess(o);
        Toaster.show(forbid ? "操作成功" : "已开启");
        logTaskSuccess(forbid, source);
        Bundle bundle = new Bundle();
        bundle.putString(SessionListFragment.Key_SessionId, Session.ID.SayhiSession);
        bundle.putInt(SessionListFragment.Key_SessionType, Session.TYPE_SAYHI);
        MomoKit.getApp().dispatchMessage(bundle, SessionListFragment.Action_SessionChanged);
        // 发送给礼物招呼
        if (GiftSayHiAppConfigV1.isOpenExp()) { // 需要刷新礼物招呼
            Bundle bundleGift = new Bundle();
            bundleGift.putString(SessionListFragment.Key_SessionId, Session.ID.GiftSayhiSession);
            MomoKit.getApp().dispatchMessage(bundleGift, SessionListFragment.Action_SessionChanged);
        }
    }

    private void logTaskSuccess(boolean forbid, String source) {
        TaskEvent.create().action(EVAction.List.MsgSwitch)
                .page(EVPage.Msg.Chatlist)
                .putExtra("on_off", forbid ? 1 : 0)
                .putExtra("source", source)
                .submit();
    }
}
