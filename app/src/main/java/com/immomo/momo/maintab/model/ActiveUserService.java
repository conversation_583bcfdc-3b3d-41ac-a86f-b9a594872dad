package com.immomo.momo.maintab.model;

import com.cosmos.mdlog.MDLog;
import com.immomo.momo.LogTag;
import com.immomo.momo.greendao.ActiveUserDao;
import com.immomo.momo.greendao.AppDBUtils;

import java.util.List;

/**
 * Created by huang.liangjie on 2017/4/1.
 *
 * Momo Tech 2011-2017 © All Rights Reserved.
 */

public class ActiveUserService {
    private static ActiveUserService instance;

    public ActiveUserService() {
    }

    /**
     * 单例化
     * @return
     */
    public static synchronized ActiveUserService getInstance() {
        if (instance != null) {
            return instance;
        }
        instance = new ActiveUserService();
        return instance;
    }

    /**
     * 释放
     */
    public synchronized static void reset() {
        instance = null;
    }

    public void saveList(List<ActiveUser> userList) {
        AppDBUtils.getInstance().deleteAll(ActiveUser.class);
        AppDBUtils.getInstance().insert(userList, ActiveUser.class);
    }

    public List<ActiveUser> getList() {
        return AppDBUtils.getInstance().list(getValidQueryWhereStr(), null, ActiveUserDao.Properties.Id, true, ActiveUser.class);
    }

    public void delete(ActiveUser user) {
        try {
            AppDBUtils.getInstance().delete(user);
        } catch (Exception e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
    }

    public void delete(String id) {
        try {
            AppDBUtils.getInstance().delete(ActiveUserDao.Properties.Id, id, ActiveUser.class);
        } catch (Throwable e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
    }

    public void deleteAll() {
        try {
            AppDBUtils.getInstance().deleteAll(ActiveUser.class);
        } catch (Throwable e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
    }

    public long getCount() {
        return AppDBUtils.getInstance().count(getValidQueryWhereStr(), null, ActiveUser.class);
    }

    private String getValidQueryWhereStr() {
        long now = System.currentTimeMillis() / 1000;
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(ActiveUserDao.Properties.End_time.columnName).append(" > ").append(now);
        return sqlBuilder.toString();
    }
}
