package com.immomo.momo.maintab.session2

import com.immomo.lcapt.evlog.EVLog
import com.immomo.lcapt.evlog.anno.Param
import com.immomo.lcapt.evlog.anno.TaskPoint
import com.immomo.momo.message.log.apt.MsgLogAppconfigGetter

/**
 * Created by huang.lian<PERSON><PERSON>e on 2024/8/8.
 *
 * Momo Tech 2011-2024 © All Rights Reserved.
 */
interface ISessionListLog {
    @TaskPoint(requireId = "18454", page = "msg.chatlist", action = "list.load")
    fun sessionLog(
        @Param("loadTime") loadTime: Int,
        @Param("sessionCount") sessionCount: Int
    )

    companion object {
        private var startTime = 0L

        private var isEnable = false

        init {
            isEnable = (MsgLogAppconfigGetter.get().sessionLoadLog() == 1)
        }

        fun loadStart() {
            startTime = System.currentTimeMillis()
        }

        fun loadEnd(sessionCount: Int) {
            if (!isEnable) return

            val endTime = System.currentTimeMillis()
            val loadTime = endTime - startTime

            EVLog.create(ISessionListLog::class.java).sessionLog(loadTime.toInt(), sessionCount)
            startTime = 0
        }
    }
}