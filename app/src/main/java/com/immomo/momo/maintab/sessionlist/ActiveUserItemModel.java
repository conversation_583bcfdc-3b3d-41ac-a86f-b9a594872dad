package com.immomo.momo.maintab.sessionlist;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.immomo.framework.cement.CementAdapter;
import com.immomo.framework.cement.CementModel;
import com.immomo.framework.cement.CementViewHolder;
import com.immomo.framework.kotlin.ImageLoader;
import com.immomo.framework.kotlin.ImageType;
import com.immomo.framework.utils.UIUtils;
import com.immomo.momo.R;
import com.immomo.momo.android.view.CircleImageView;
import com.immomo.momo.maintab.model.ActiveUser;
import com.immomo.momo.service.bean.AvatarFrame;
import com.immomo.momo.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by huang.liangjie on 2018/5/14.
 * <p>
 * Momo Tech 2011-2018 © All Rights Reserved.
 */
public class ActiveUserItemModel extends CementModel<ActiveUserItemModel.ViewHolder> {

    private ActiveUser activeUser;
    private boolean topFoldMode;

    public ActiveUserItemModel(boolean topFoldMode, ActiveUser activeUser) {
        this.activeUser = activeUser;
        this.topFoldMode = topFoldMode;
        id(activeUser.getMomoid());
    }

    public ActiveUser getActiveUser() {
        return activeUser;
    }

    public Map<String, String> getLogMap() {
        Map<String, String> map = new HashMap<>();
        if (activeUser == null) {
            return map;
        }
        map.put("momo_id", activeUser.getMomoid());
        if (activeUser.getType() == ActiveUser.TYPE_COMMON) {
            map.put("time_text", activeUser.getTitle());
        } else {
            map.put("time_text", activeUser.getName());
        }
        map.put("reason_text", activeUser.getReason());
        map.put("is_more", "0");
        return map;
    }

    @Override
    public void bindData(@NonNull ActiveUserItemModel.ViewHolder holder) {
        super.bindData(holder);
        if (topFoldMode) {
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) holder.itemView.getLayoutParams();
            layoutParams.leftMargin = UIUtils.getPixels(holder.getAdapterPosition() == 0 ? 3.5f : 0);
        }
        if (activeUser.getType() == ActiveUser.TYPE_COMMON) {
            holder.activeUserName.setText(activeUser.getTitle());
        } else {
            holder.activeUserName.setText(activeUser.getName());
        }
        holder.activeUserDesc.setText(activeUser.getReason());
        if (StringUtils.isEmpty(activeUser.getTip_icon())) {
            holder.activeUserSign.setVisibility(View.GONE);
        } else {
            holder.activeUserSign.setVisibility(View.VISIBLE);
            ImageLoader.load(activeUser.getTip_icon()).canUseNight(true).into(holder.activeUserSign);
        }
        ImageLoader.load(activeUser.getAvatar()).imageType(com.immomo.framework.kotlin.ImageType.ALBUM_250X250).canUseNight(true).into(holder.activeUserAvatar);
        ImageView activeUserAvatarFrame = holder.activeUserAvatarFrame;
        if (activeUserAvatarFrame != null) {
            AvatarFrame avatarFrame = activeUser.getAvatarFrame();
            if (avatarFrame != null && StringUtils.isNotBlank(avatarFrame.getNormalFrame())) {
                String safeAvatarFrame = avatarFrame.getSafeAvatarFrame();
                ImageLoader.load(safeAvatarFrame).imageType(ImageType.URL).into(activeUserAvatarFrame);
                activeUserAvatarFrame.setVisibility(View.VISIBLE);
            } else {
                activeUserAvatarFrame.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public int getLayoutRes() {
        return topFoldMode ? R.layout.listitem_session_active_user_item_fold_mode : R.layout.listitem_session_active_user_item;
    }

    @NonNull
    @Override
    public CementAdapter.IViewHolderCreator<ActiveUserItemModel.ViewHolder> getViewHolderCreator() {
        return ViewHolder::new;
    }

    public static class ViewHolder extends CementViewHolder {
        ViewGroup container;
        CircleImageView activeUserAvatar;
        ImageView activeUserSign;
        TextView activeUserName;
        TextView activeUserDesc;
        ImageView activeUserAvatarFrame;

        public ViewHolder(View itemView) {
            super(itemView);
            container = itemView.findViewById(R.id.session_active_user_container);
            activeUserAvatar = itemView.findViewById(R.id.iv_session_active_user_avatar);
            activeUserSign = itemView.findViewById(R.id.iv_session_active_user_sign);
            activeUserName = itemView.findViewById(R.id.tv_session_active_user_name);
            activeUserDesc = itemView.findViewById(R.id.tv_session_active_user_desc);
            activeUserAvatarFrame = itemView.findViewById(R.id.iv_session_active_user_frame);
        }
    }
}
