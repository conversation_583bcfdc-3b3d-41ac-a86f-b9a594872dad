package com.immomo.momo.maintab.sessionlist.util

import com.immomo.momo.maintab.model.UserAvatarFrameWrapper
import com.immomo.momo.maintab.model.UserBadgeWrapper
import com.immomo.momo.maintab.model.UserOnlineStatus
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.defs.UserChatContent
import com.immomo.momo.maintab.session2.defs.UserChatSessionDefinition.Companion.key
import com.immomo.momo.maintab.sessionlist.util.SessionHelper.hideMsgDistanceStrInSession
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.sessions.SessionService

/**
 * CREATED BY liu.chong
 * AT 2024/7/11
 */
object SessionUserHelper {

    /**
     * 更新session的副标题
     */
    fun updateSessionDesc(momoId: String?, forceDesc: String?) {
        if (forceDesc.isNullOrBlank()) return
        momoId?.takeIf { it.isNotBlank() }?.also {
            SessionManager.get().updateSession(key(it), false) { sessionEntity: SessionEntity ->
                (sessionEntity.content as? UserChatContent?)?.let { sessionContent ->
                    if (sessionEntity.unreadMessageCount <= 0) {
                        sessionContent.forcedDesc = forceDesc
                        sessionContent.showMessageStatus = false // 隐藏未读状态
                        true
                    } else false
                } ?: false
            }
        }
    }

    fun clearSessionFireIcon(momoId: String?) {
        momoId?.takeIf { it.isNotBlank() }?.also {
            SessionManager.get().updateSession(key(it), false) { sessionEntity: SessionEntity ->
                (sessionEntity.content as? UserChatContent?)?.let { sessionContent ->
                    sessionContent.fireIcon = ""
                    sessionContent.fireIconDark = ""
                    sessionContent.fireSign = ""
                    true
                } ?: false
            }
        }
    }

    fun onSessionUserUpdate(userOnlineList: Map<String, UserOnlineStatus>) {
        val sessionManager = SessionManager.get()
        val sessionKeys: MutableList<SessionKey> = ArrayList()
        for ((momoId, status) in userOnlineList.entries) {
            val sessionKey = key(momoId)
            sessionManager.updateSessionForJava(
                sessionKey,
                false
            ) { sessionEntity: SessionEntity ->
                val content =
                    sessionEntity.content as UserChatContent?
                        ?: return@updateSessionForJava false
                content.userOnlineTag = status.userOnlineTag
                content.userLocationTimestamp = status.locTimesec * 1000L
                content.userChatTag = status.chatTag
                content.onlyShowMessageContent = hideMsgDistanceStrInSession(content)
                content.onlineMsgTime = status.onlineMsgTime
                content.sevenDaysIn = status.sevenDaysIn
                content.sevenDaysOut = status.sevenDaysOut
                content.distance = status.distance
                content.officialOperation = status.getOfficialOperation()
                content.textIconTag = status.textIconTag
                content.sessionTagLogMap = status.sessionTagLogMapData
                content.fireIcon = status.sparkBtn?.btnIcon
                content.fireIconDark = status.sparkBtn?.btnIconDark
                content.fireSign = status.sparkBtn?.emotionSign
                val avatarFrame = status.avatarFrame
                if (avatarFrame != null) {
                    content.userAvatarFrame = UserAvatarFrameWrapper(avatarFrame)
                }
                val cellTagUniformLabels = status.getCellTagUniformLabels()
                if (cellTagUniformLabels != null) {
                    content.tagUniformLabels = UserBadgeWrapper(cellTagUniformLabels)
                } else {
                    content.tagUniformLabels = null
                }
                status.mVipPoint?.also {
                    content.userIsVip = it.valid == 1
                }
                true
            }
            sessionKeys.add(sessionKey)
        }
        if (sessionManager.hasNewBoySession(sessionKeys)) {
            SessionService.getInstance().updateFoldSession(Session.ID.NEW_BOY)
        }

        if (sessionManager.hasHePaiSession(sessionKeys)) {
            SessionService.getInstance().updateFoldSession(Session.ID.HEPAI)
        }

        if (sessionManager.hasSpamSession(sessionKeys)) {
            SessionService.getInstance().updateFoldSession(Session.ID.SPAM)
        }
    }
}