package com.immomo.momo.maintab.session2.defs

import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.kotlin.extern.isNotNullOrEmpty
import com.immomo.momo.globalevent.GlobalEventManager
import com.immomo.momo.maintab.session2.SessionContentParser
import com.immomo.momo.maintab.session2.SessionDefinition
import com.immomo.momo.maintab.session2.data.database.SessionContent
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.type.UniverseFoldSessionModel
import com.immomo.momo.maintab.sessionlist.SessionListFragment
import com.immomo.momo.service.bean.Session
import com.immomo.momo.universe.im.UniUnreadManager
import com.immomo.momo.universe.im.cons.SessionCons
import com.immomo.momo.universe.im.data.UniMsgEntity
import com.immomo.momo.universe.im.service.UniMsgService
import com.immomo.momo.universe.im.service.UniSessionService
import com.immomo.momo.universe.user.UniUserService
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
class UniverseSessionContent(
    var lastMsgTime: Long? = 0L,
    var desc: String? = null
) : SessionContent(SessionKey.KEY_UNIVERSE)

/**
 * 小宇宙消息盒子
 */
class UniverseSessionDefinition :
    SessionDefinition<UniverseSessionContent, UniverseFoldSessionModel>(
        SessionKey.KEY_UNIVERSE, SessionContentParser.moshiParser()
    ) {

    private val defaultContent = "暂无最新消息"

    override fun createContent(): UniverseSessionContent {
        return UniverseSessionContent()
    }

    private fun refreshContentCnt(content: UniverseSessionContent, session: SessionEntity) {
        var needRefreshProfile = false
        session.unreadMessageCount = UniUnreadManager.getTotalUnreadCount()
        var queryNewestMsg: UniMsgEntity? = null
        val sessionNewest = UniSessionService.querySessionNewest()?.apply {
            session.lastMsgTime = timeStamp
            if (type == SessionCons.SessionType.CHAT && lastMsgId.isNotNullOrEmpty()) {
                queryNewestMsg = UniMsgService.query(getLastMsgId())
            }
        }
        queryNewestMsg?.timeStamp?.takeIf { it > 0L }?.also {
            session.lastMsgTime = it
        }
        session.lastMsgId = queryNewestMsg?.msgId.safe()
        content.lastMsgTime = session.lastMsgTime
        sessionNewest?.also {
            val messageContent = it.subtitle.safe(defaultContent)
            queryNewestMsg?.also { msg ->
                val uniUserEntity = UniUserService.list(mutableListOf(it.id)).firstOrNull()
                needRefreshProfile = uniUserEntity == null
                val remark = uniUserEntity?.remark
                val userName: String = remark?.takeIf { it.isNotBlank() }?.let { // 优先取昵称
                    remark
                } ?: uniUserEntity?.name.safe(it.title.safe())
                content.desc = if (msg.isReceive && userName.isNotEmpty())
                    "$userName:$messageContent"
                else
                    messageContent
            } ?: run {
                content.desc = messageContent
            }
        } ?: run { content.desc = defaultContent }
        if (content.desc.isNullOrEmpty()) {
            content.desc = defaultContent
        }
        if (needRefreshProfile) {
            val eventMap = hashMapOf<String, Any?>("uid" to sessionNewest?.id)
            GlobalEventManager.getInstance().sendEvent(
                GlobalEventManager.Event(SessionListFragment.EVENT_UNIVERSE_PROFILE_REFRESH)
                    .msg(eventMap).src(GlobalEventManager.EVN_NATIVE).dst(GlobalEventManager.EVN_NATIVE)
            )
        }
    }

    override fun onClearUnread(session: SessionEntity) {
        val content = session.content as? UniverseSessionContent ?: return
        session.unreadMessageCount = 0
    }

    override fun onReloadInfo(id: String, session: SessionEntity, forceReload: Boolean) {
        syncSession(session)
    }

    // 更新招呼Session
    private fun syncSession(session: SessionEntity): Boolean {
        val content = session.content as? UniverseSessionContent ?: return false
        refreshContentCnt(content, session)
        return true
    }

    override fun syncSession(session: SessionEntity, data: Any?): Boolean {
        if (super.syncSession(session, data)) return true
        return syncSession(session)
    }

    override fun UniverseSessionContent.contentToModel(
        baseInfo: BaseSessionInfo
    ): UniverseFoldSessionModel = UniverseFoldSessionModel(
        baseInfo,
        lastMsgTime = lastMsgTime.safe(0L),
        desc = desc.safe()
    )

    companion object {
        @JvmField
        val KEY_UNIVERSE = SessionKey(SessionKey.KEY_UNIVERSE, Session.ID.UniverseSession)

        const val UNIVERSE = SessionKey.KEY_UNIVERSE + "_" + Session.ID.UniverseSession
    }
}