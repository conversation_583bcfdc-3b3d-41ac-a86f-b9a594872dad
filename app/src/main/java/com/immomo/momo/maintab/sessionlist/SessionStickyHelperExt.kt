package com.immomo.momo.maintab.sessionlist

import com.immomo.momo.maintab.session2.SessionUpdateBundle
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.isInCache
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.defs.*

internal fun SessionManager.updateSticky(
    remoteId: String,
    type: SessionStickyHelper.ChatType,
    isSticky: Boolean,
    setTime: Long = -1
) {
    val key = when (type) {
        SessionStickyHelper.ChatType.TYPE_CHAT ->
            UserChatSessionDefinition.key(remoteId)
        SessionStickyHelper.ChatType.TYPE_GROUP ->
            GroupChatSessionDefinition.key(remoteId)
        SessionStickyHelper.ChatType.TYPE_DISCUSS ->
            DiscussChatSessionDefinition.key(remoteId)
        SessionStickyHelper.ChatType.TYPE_VCHAT_SUPER_ROOM ->
            VChatSuperRoomSessionDefinition.key(remoteId)
        SessionStickyHelper.ChatType.TYPE_SAYHI ->
            SayHiSessionDefinition.KEY_SAYHI
    }
    updateSession(key, createWhenAbsent = isSticky) {
        if (it.isSticky != isSticky) {
            it.isSticky = isSticky

            if (isSticky) {
                it.lastFetchTime = setTime
                //如果是从api获取的置顶，且没有在内存中，主动获取一次聊天信息
                //如果是聊天室专属房间，需要主动检查是否已经过期
                if (!it.isInCache(SessionEntity.CACHE_MEMORY)
                    || type == SessionStickyHelper.ChatType.TYPE_VCHAT_SUPER_ROOM
                ) {
                    syncSession(SessionUpdateBundle.ReloadInfo(key))
                }
            }
            true
        } else false
    }
}