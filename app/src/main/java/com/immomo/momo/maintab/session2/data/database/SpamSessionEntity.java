package com.immomo.momo.maintab.session2.data.database;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Generated;

@Entity(nameInDb = "spam_session",generateConstructors = false)
public class SpamSessionEntity {
    @Id
    String sessionId;

    public SpamSessionEntity() {
    }

    public SpamSessionEntity(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getSessionId() {
        return this.sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
}
