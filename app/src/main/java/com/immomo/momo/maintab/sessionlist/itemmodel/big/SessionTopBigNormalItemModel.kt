package com.immomo.momo.maintab.sessionlist.itemmodel.big

import android.view.ViewGroup.MarginLayoutParams
import androidx.core.view.updateLayoutParams
import com.immomo.momo.maintab.sessionlist.bean.CardItemData

/**
 * 展开态入口
 */
class SessionTopBigNormalItemModel(itemModeOriginData: CardItemData) :
    BaseSessionTopBigItemModel(itemModeOriginData) {

    override fun bindData(holder: ViewHolder) {
        super.bindData(holder)
        refreshHolderView(holder)
    }

    override fun refreshHolderView(holder: ViewHolder) {
        super.refreshHolderView(holder)
        // 处理宽度
        holder.itemView.updateLayoutParams<MarginLayoutParams> { // 展示大卡
            width = if (itemModeData.avatarFrame != null) {
                holder.normalTitleContainer.setPadding(AVATAR_CARD_PADDING_SIZE, 0, 0, 0)
                MIDDLE_CARD_SIZE
            } else {
                holder.normalTitleContainer.setPadding(SMALL_CARD_PADDING_SIZE, 0, 0, 0)
                SMALL_CARD_SIZE
            }
        }
        refreshNormalViewData(holder)
        showNormalView(holder)
    }
}