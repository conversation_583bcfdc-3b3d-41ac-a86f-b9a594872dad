package com.immomo.momo.maintab.session2.data.database;

import androidx.annotation.NonNull;

import com.immomo.momo.service.daobase.ITable;

import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Unique;

public class UnreadCountEntity {
    public static final int TYPE_UNREAD = 0;
    public static final int TYPE_SILENT_UNREAD = 1;

    public static String key(@NonNull String sessionKey, int type) {
        return sessionKey + "_" + type;
    }

    @Id
    @Unique
    @NonNull
    private String id;

    @NonNull
    private String sessionKey;
    private int type;

    @NonNull
    private String sessionType;
    @NonNull
    private String sessionId;

    private int count;

    private long maxMessageTimestamp;

    public UnreadCountEntity() {
    }

    public UnreadCountEntity(@NonNull String sessionKey, int type) {
        this.id = key(sessionKey, type);
        this.sessionKey = sessionKey;
        this.type = type;
    }

    @NonNull
    public String getId() {
        return id;
    }

    public void setId(@NonNull String id) {
        this.id = id;
    }

    @NonNull
    public String getSessionKey() {
        return this.sessionKey;
    }

    public void setSessionKey(@NonNull String sessionKey) {
        this.sessionKey = sessionKey;
    }

    @NonNull
    public String getSessionType() {
        return this.sessionType;
    }

    public void setSessionType(@NonNull String sessionType) {
        this.sessionType = sessionType;
    }

    @NonNull
    public String getSessionId() {
        return this.sessionId;
    }

    public void setSessionId(@NonNull String sessionId) {
        this.sessionId = sessionId;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getMaxMessageTimestamp() {
        return this.maxMessageTimestamp;
    }

    public void setMaxMessageTimestamp(long maxMessageTimestamp) {
        this.maxMessageTimestamp = maxMessageTimestamp;
    }

    public interface Table extends ITable {
        String TableName = "unread_count_entity";
        String F_Id = DBFIELD_ID;
        String F_SessionKey = "sessionKey";
        String F_Type = "type";
        String F_SessionType = "sessionType";
        String F_SessionId = "sessionId";
        String F_Count = "count";
        String F_MaxMessageTimestamp = "maxMessageTimestamp";
    }
}
