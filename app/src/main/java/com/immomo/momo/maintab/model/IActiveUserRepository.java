package com.immomo.momo.maintab.model;

import com.immomo.momo.mvp.common.model.ModelManager;

/**
 * Created by huang.lian<PERSON><PERSON><PERSON> on 2017/4/1.
 *
 * Momo Tech 2011-2017 © All Rights Reserved.
 */

public interface IActiveUserRepository extends ModelManager.IModel{
    SessionActiveUser getActiveUserSession(int totalSessionCount,int coreSessionCount) throws Exception;

    void inActiveOneUser(ActiveUser user) throws Exception;

    void removeActiveOneUser(ActiveUser user) throws Exception;

    boolean isCacheEmpty();

    void clear();
}
