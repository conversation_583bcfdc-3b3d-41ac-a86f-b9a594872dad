package com.immomo.momo.maintab.sessionlist.util

import android.app.Dialog
import android.content.Context
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.immomo.framework.kotlin.ImageLoader.load
import com.immomo.framework.kotlin.ImageType
import com.immomo.momo.R
import com.immomo.momo.util.StringUtils

/**
 * 生态治理弹窗
 */
class EcologicalGovernanceDialog(
    context: Context,
    title: String?,
    content: String?,
    pos: String?,
    neg: String?
) : Dialog(context, R.style.customDialog) {
    private var mConfirm: TextView
    private var mCancel: TextView
    private var mTitle: TextView
    private var mContent: TextView
    private var topIcon: ImageView
    private var mOnClickListener: View.OnClickListener? = null
    private var mOnCancelListener: View.OnClickListener? = null

    fun setPosListener(listener: View.OnClickListener?) {
        mOnClickListener = listener
    }

    fun setNegListener(listener: View.OnClickListener?) {
        mOnCancelListener = listener
    }

    fun setListener(confirmListener: View.OnClickListener?, cancelListener: View.OnClickListener?) {
        setPosListener(confirmListener)
        setNegListener(cancelListener)
    }

    init {
        setContentView(R.layout.layout_dialog_ecological_governance_confirm)
        topIcon = findViewById(R.id.top_icon)
        mConfirm = findViewById(R.id.dialog_ecological_chat_confirm)
        mCancel = findViewById(R.id.dialog_ecological_chat_cancel)
        mTitle = findViewById(R.id.dialog_ecological_chat_title)
        mContent = findViewById(R.id.dialog_ecological_chat_content)
        initListener()
        if (StringUtils.isEmpty(pos)) {
            mConfirm.visibility = View.GONE
        } else {
            mConfirm.text = pos
        }
        if (StringUtils.isEmpty(neg)) {
            mCancel.visibility = View.GONE
        } else {
            mCancel.text = neg
        }
        mTitle.text = title
        if (StringUtils.isBlank(content)) {
            mContent.visibility = View.GONE
        } else {
            mContent.visibility = View.VISIBLE
            mContent.text = content
        }
    }

    private fun initListener() {
        mConfirm.setOnClickListener { v: View? ->
            dismiss()
            mOnClickListener?.onClick(v)
        }
        mCancel.setOnClickListener { v: View? ->
            dismiss()
            mOnCancelListener?.onClick(v)
        }
    }

    fun setCoverImg(cover: String) {
        load(cover).imageType(ImageType.URL).into(topIcon)
    }
}
