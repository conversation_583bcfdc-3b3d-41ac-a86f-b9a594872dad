package com.immomo.momo.maintab.session2.presentation.itemmodel

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.view.View
import com.immomo.android.mm.cement2.AsyncCementModel
import com.immomo.android.mm.cement2.CementViewHolder
import com.immomo.android.mm.cement2.IViewHolderCreator
import com.immomo.kliaocore.utils.CheckDoubleClick
import com.immomo.mmutil.toast.Toaster
import com.immomo.momo.R
import com.immomo.momo.fullsearch.activity.FullSearchActivity
import com.immomo.momo.fullsearch.base.FullSearchHelper

class FullSearchItemModel :
    AsyncCementModel<String, CementViewHolder>("full-search") {

    init {
        id("full-search")
    }

    override val layoutRes: Int
        get() = R.layout.include_fullsearch_header

    override val viewHolderCreator: IViewHolderCreator<CementViewHolder>
        get() = object : IViewHolderCreator<CementViewHolder> {
            override fun create(view: View): CementViewHolder = CementViewHolder(view)
        }

    override fun bindData(holder: CementViewHolder) {
        super.bindData(holder)
        holder.itemView.setOnClickListener {
            FullSearchActivity.start(it.context as Activity)
        }
    }
}
