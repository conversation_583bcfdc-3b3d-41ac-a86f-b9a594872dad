package com.immomo.momo.maintab.usecase;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.immomo.framework.rxjava.executor.PostExecutionThread;
import com.immomo.framework.rxjava.executor.ThreadExecutor;
import com.immomo.framework.rxjava.interactor.UseCase;
import com.immomo.momo.maintab.model.SayHiListReqParam;
import com.immomo.momo.service.bean.SayhiSession;
import com.immomo.momo.service.sessions.ISessionRepository;

import java.util.List;

import io.reactivex.Flowable;

/**
 * Created by huang.liangjie on 2017/11/7.
 *
 * Momo Tech 2011-2017 © All Rights Reserved.
 */

public class LoadSayHiSessionUserCase extends UseCase<List<SayhiSession>, SayHiListReqParam> {
    private ISessionRepository sessionRepository;

    public LoadSayHiSessionUserCase(@NonNull ThreadExecutor threadExecutor, ISessionRepository repository, @NonNull PostExecutionThread postExecutionThread) {
        super(threadExecutor, postExecutionThread);

        this.sessionRepository = repository;
    }

    @NonNull
    @Override
    protected Flowable<List<SayhiSession>> buildUseCaseFlowable(@Nullable SayHiListReqParam sayHiListReqParam) {
        return sessionRepository.loadSayHiSession(sayHiListReqParam);
    }
}
