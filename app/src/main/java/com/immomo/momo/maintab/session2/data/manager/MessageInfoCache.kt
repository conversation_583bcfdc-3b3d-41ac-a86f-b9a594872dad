package com.immomo.momo.maintab.session2.data.manager

import android.util.Log
import java.util.concurrent.ConcurrentHashMap

class MessageInfoCache {

    interface Interceptor {
        fun onIntercept(sessionMessage: SessionMessage)

        fun reset()
    }

    val messageInterceptors = ConcurrentHashMap<Class<*>, Interceptor>()

    inline fun <reified T : Interceptor> register(interceptor: T) {
        messageInterceptors[T::class.java] = interceptor
    }

    /**
     * 添加消息的时候，只有存在缓存，并且缓存为false才处理，true不用处理
     */
    fun handleMessage(sessionMessage: SessionMessage) =
        messageInterceptors.forEach { (_, interceptor) ->
            try {
                interceptor.onIntercept(sessionMessage)
            } catch (e: Exception) {
                Log.e("MessageInfoCache", "error at intercept", e)
            }
        }

    fun reset() {
        messageInterceptors.values.forEach {
            try {
                it.reset()
            } catch (e: Exception) {
                Log.e("MessageInfoCache", "error at reset", e)
            }
        }
    }

    inline fun <reified T : Interceptor> interceptor(): T = messageInterceptors[T::class.java] as? T
        ?: throw IllegalArgumentException("can not find ${T::class.java}")
}

