package com.immomo.momo.maintab.usecase

import com.immomo.framework.rxjava.interactor.UseCase
import com.immomo.mmutil.task.MMThreadExecutors
import com.immomo.momo.maintab.sessionlist.bean.EcologicalGovernanceAlertData
import com.immomo.momo.protocol.http.UserApi
import io.reactivex.Flowable

/**
 * 生态治理弹窗
 */
class EcologicalGovernanceAlertUseCase : UseCase<EcologicalGovernanceAlertData, String>(
    MMThreadExecutors.User, MMThreadExecutors.Main
) {
    override fun buildUseCaseFlowable(params: String?): Flowable<EcologicalGovernanceAlertData> {
        return Flowable.fromCallable {
            UserApi.getInstance().ecologicalGovernanceAlert()
        }
    }
}