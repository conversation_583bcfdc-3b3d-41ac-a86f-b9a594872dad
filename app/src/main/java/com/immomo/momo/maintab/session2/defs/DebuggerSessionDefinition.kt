package com.immomo.momo.maintab.session2.defs

import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.momo.maintab.session2.SessionContentParser
import com.immomo.momo.maintab.session2.SessionDefinition
import com.immomo.momo.maintab.session2.data.database.SessionContent
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.manager.SessionMessage
import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.type.DebuggerSessionModel
import com.immomo.momo.message.helper.SessionTextHelper
import com.immomo.momo.protocol.imjson.util.Debugger
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.sessions.SessionService
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
class DebuggerContent(
    var desc: String?
) : SessionContent("debugger")

class DebuggerSessionDefinition :
    SessionDefinition<DebuggerContent, DebuggerSessionModel>(
        "debugger", SessionContentParser.moshiParser()
    ) {
    override fun createContent(): DebuggerContent {
        return DebuggerContent("")
    }

    private fun updateSessionWithMessage(
        session: SessionEntity,
        content: DebuggerContent,
        message: Message
    ) {
        session.lastMsgId = message.msgId ?: session.lastMsgId
        if (message.stopFloat == 0) {
            session.lastMsgTime = message.timestamp?.time ?: session.lastMsgTime
        }

        if (message.isUpdateSession) {
            //SessionItemHolder#fillMessage(TextView contentView, final Session session)
            content.desc = SessionTextHelper.getMessageContent(message)
        }
    }

    override fun syncSession(session: SessionEntity, data: Any?): Boolean {
        if (super.syncSession(session, data)) return true
        val content = session.content as? DebuggerContent ?: return false

        if (data !is SessionMessage) return false

        return when (data) {
            is SessionMessage.Sync,
            is SessionMessage.Receive,
            is SessionMessage.Send -> {
                data.messageList.sortedBy { it.timestampExt }.forEach {
                    updateSessionWithMessage(session, content, it)
                }
                true
            }
            is SessionMessage.Delete,
            is SessionMessage.ResetLast,
            is SessionMessage.Update,
            is SessionMessage.UpdateStatus -> {
                false
            }
        }
    }

    override fun isTransformCompatible(oldSessionType: Int, oldSessionId: String): String? {
        return if (
            oldSessionType == Session.TYPE_LOGGER
        ) oldSessionId else null
    }

    override fun transformOldSession(id: String): SessionEntity? {
        val oldSession = SessionService.getInstance().getSession(id) ?: return null

        val entity = createSession(id)
        val content = entity.content as DebuggerContent

        entity.orderId = oldSession.orderId
        entity.lastMsgId = oldSession.lastmsgId
        if (oldSession.lastMessage?.stopFloat == 0) {
            entity.lastMsgTime = oldSession.lastMessage?.timestamp?.time ?: entity.lastMsgTime
        }

        oldSession.lastMessage?.let { message ->
            if (message.notShowInSession) {
                content.desc = message.recommendReason
            } else if (message.isUpdateSession) {
                //SessionItemHolder#fillMessage(TextView contentView, final Session session)
                content.desc = SessionTextHelper.getMessageContent(message)
            }
        }
        return entity
    }

    override fun DebuggerContent.contentToModel(
        baseInfo: BaseSessionInfo
    ): DebuggerSessionModel = DebuggerSessionModel(
        baseInfo = baseInfo,
        desc = desc.safe()
    )

    companion object {
        const val Type = "debugger"
        const val Key = "debugger_${Debugger.LoggerSessionId}"
    }
}