package com.immomo.momo.maintab.session2.defs

import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.momo.group.audio.GroupAudioHelper
import com.immomo.momo.group.bean.GroupPreference
import com.immomo.momo.maintab.session2.SessionContentParser
import com.immomo.momo.maintab.session2.data.database.ChatContent
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.manager.LastUnreadMessageInterceptor
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.data.manager.UnreadCountMessageInterceptor
import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.type.GroupChatSessionModel
import com.immomo.momo.maintab.sessionlist.SessionStickyHelper
import com.immomo.momo.message.helper.SessionTextHelper
import com.immomo.momo.messages.service.GroupMsgService
import com.immomo.momo.messages.service.GroupMsgServiceV2
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.sessions.LastMsgCache
import com.immomo.momo.service.sessions.MessageServiceHelper
import com.immomo.momo.service.sessions.SessionService
import com.immomo.momo.util.StringUtils
import com.squareup.moshi.JsonClass
import java.util.*

@JsonClass(generateAdapter = true)
class GroupChatContent : ChatContent("g") {
    var groupAvatar: String? = null
    var groupName: String? = null
    var groupIsVip: Boolean = false

    override fun isChatInfoValid(): Boolean {
        return groupName?.isNotEmpty() == true
    }

    var isHongbao = false
    var isGift = false

    //是否正在视频聊天
    var isVideoChatting = false
    var isAtMe = false
    var atText: String? = null
}

class GroupChatSessionDefinition
    : ChatSessionDefinition<GroupChatContent, GroupChatSessionModel>(
    "g",
    SessionContentParser.moshiParser()
) {
    override fun createContent(): GroupChatContent {
        return GroupChatContent()
    }

    override fun getLastMessage(session: SessionEntity): Message? {
        return GroupMsgServiceV2.service.findLastMessage(session.sessionId)
    }

    override fun updateSessionDescWithLastMessage(
        session: SessionEntity,
        content: GroupChatContent,
        lastMessage: Message,
        updateProcessedTime: Boolean
    ) {
        super.updateSessionDescWithLastMessage(session, content, lastMessage, updateProcessedTime)

        if (!lastMessage.isImSpam) {
            session.lastMsgId = lastMessage.msgId ?: session.lastMsgId
            if (lastMessage.stopFloat == 0) {
                session.lastMsgTime = lastMessage.timestamp?.time ?: session.lastMsgTime
            }
            content.lastMessageType = lastMessage.contentType
            LastMsgCache.onSendNewMsg(lastMessage)
        }

        if (lastMessage.notShowInSession) {
            content.forcedDesc = lastMessage.recommendReason ?: ""
        } else {
            content.lastMessageOwnerId = lastMessage.remoteId
            SessionManager.getInfoCache().fetchUser(session, lastMessage.remoteId) { user ->
                //最后一条消息的id不一致
                if (this.content.castOrNull<ChatContent>()?.lastMessageOwnerId
                    != lastMessage.remoteId
                ) return@fetchUser false

                lastMessage.owner = user
                this.content.castOrNull<ChatContent>()?.lastMessageOwnerName =
                    getDisplayName(lastMessage)
                true
            }

            // 关闭了消息提醒，通过最后一条消息内容的位置，提醒未读数量
            if (!GroupPreference.isPushOpened(content.chatId) && session.silentMessageCount > 0) {
                content.forcedDesc = session.silentMessageCount.toString() + "条消息未读"
            } else {
                content.forcedDesc = null
            }
            content.onlyShowMessageContent = false
            content.distanceInfo = -1F
            if (!lastMessage.receive) {
                // 自己发送的消息
                content.onlyShowMessageContent = true
            } else if (lastMessage.contentType == Message.CONTENTTYPE_MESSAGE_NOTICE) {
                // notioce 不组拼消息的username
                content.onlyShowMessageContent = true
            } else if (StringUtils.isEmpty(lastMessage.remoteId)) {
                content.onlyShowMessageContent = true
            } else if (lastMessage.diatance >= 0 && !content.isHongbao && !content.isGift) {
                // 有红包和礼物的情况下不显示距离
                content.distanceInfo = lastMessage.diatance
            }

            content.lastMessageContent = SessionTextHelper.getMessageContent(lastMessage)
        }

        updateLastMessageStatus(
            session,
            lastMessage.msgId,
            lastMessage.receive,
            lastMessage.status
        )
    }

    override fun updateSessionIndicatorWithEveryMessage(
        session: SessionEntity,
        content: GroupChatContent,
        message: Message?
    ) {
        if (message?.status == Message.STATUS_CLOUD) return

        session.unreadMessageCount =
            UnreadCountMessageInterceptor.getCount(key(session.sessionId))
        //理论上有未读消息就不应该有静默消息
        if (session.unreadMessageCount > 0) {
            session.silentMessageCount = 0
        } else {
            session.silentMessageCount =
                UnreadCountMessageInterceptor.getSilentCount(key(session.sessionId))
        }

        content.isGift = LastUnreadMessageInterceptor.getUnread(
            key(session.sessionId),
            Message.CONTENTTYPE_MESSAGE_GIFT
        )?.isValid() == true
        content.isHongbao = LastUnreadMessageInterceptor.getUnread(
            key(session.sessionId),
            Message.CONTENTTYPE_MESSAGE_HONGBAO
        )?.isValid() == true

        LastUnreadMessageInterceptor.getUnreadAtMessage(
            key(session.sessionId)
        ).also {
            content.isAtMe = it?.isValid() == true
            content.atText = it?.data.castOrNull()
        }
    }

    override fun onReloadChatInfo(id: String, session: SessionEntity, forceReload: Boolean) {
        val content = session.content as? GroupChatContent ?: return
        content.pendingReloadChatInfo = true
        val modelBefore = GroupPreference.getNotificationModel(content.chatId)

        SessionManager.getInfoCache().fetchGroup(session, content.chatId, forceReload) { group ->
            this.content.castOrNull<GroupChatContent>()?.also {
                it.pendingReloadChatInfo = false

                it.groupAvatar = group.getLoadImageId()
                it.groupName = group.displayName ?: session.sessionId
                it.groupIsVip = group.isVipGroup == true
                val modeAfter = GroupPreference.getNotificationModel(content.chatId)
                if (modelBefore!=modeAfter && modeAfter == GroupPreference.NOTIFICATION_CLOSE) {
                    kotlin.runCatching {
                        GroupMsgService.getInstance().updateMessagesIgnore(content.chatId)
                    }

                    session.silentMessageCount = session.unreadMessageCount
                    session.unreadMessageCount = 0
                }
            }
            true
        }

        if (!GroupPreference.isPushOpened(content.chatId) && session.silentMessageCount > 0) {
            content.forcedDesc = session.silentMessageCount.toString() + "条消息未读"
        } else {
            content.forcedDesc = null
        }
    }

    override fun syncSession(session: SessionEntity, data: Any?): Boolean {
        val content = session.content as? GroupChatContent ?: return false
        content.chatId = session.sessionId

        session.isSticky =
            SessionStickyHelper.getInstance().getOrderID("g_${session.sessionId}") != -1L

        content.isVideoChatting = GroupAudioHelper.instance.isGroupChating(content.chatId ?: "")

        content.groupName = content.chatId
        onReloadChatInfo(session.sessionId, session, false)

        return super.syncSession(session, data)
    }

    override fun isTransformCompatible(oldSessionType: Int, oldSessionId: String): String? {
        return if (oldSessionId.startsWith("g")) oldSessionId.removePrefix("g_") else null
    }

    override fun transformOldSession(id: String): SessionEntity? {
        val oldSession = SessionService.getInstance().getSession("g_$id") ?: return null

        val session = createSession(id)
        syncSession(session, null)
        val content = session.content.castOrNull<GroupChatContent>() ?: return null

        session.lastMsgId = oldSession.lastmsgId
        session.lastMsgTime = oldSession.lastMessage?.timestampMillis ?: 0
        session.lastFetchTime = oldSession.fetchtime?.time ?: 0
        session.orderId = oldSession.orderId

        content.draftString = oldSession.draftString
        content.draftQuoteString = oldSession.draftQuoteString

        updateSessionIndicatorWithEveryMessage(session, content, oldSession.lastMessage)
        oldSession.lastMessage?.let {
            updateSessionDescWithLastMessage(session, content, it, true)
        }
        return session
    }


    override fun saveOldSession(session: SessionEntity) {
        val content = session.content.castOrNull<GroupChatContent>() ?: return

        SessionService.getInstance().updateSession("g_${session.sessionId}") {
            it.type = Session.TYPE_GROUP
            it.chatId = session.sessionId

            it.lastmsgId = session.lastMsgId
            it.setOrderid(session.orderId)
            it.fetchtime = Date(session.lastFetchTime)

            it.draftString = content.draftString
            it.draftQuoteString = content.draftQuoteString

            true
        }
    }

    override fun removeOldSession(id: String) {
        SessionService.getInstance().deleteSession("g_$id")
    }

    override fun GroupChatContent.contentToModel(
        baseInfo: BaseSessionInfo
    ): GroupChatSessionModel = GroupChatSessionModel(
        baseInfo = baseInfo,
        chatId = chatId.safe(),
        desc = desc.safe(),
        draftString = draftString.safe(),
        draftQuoteString = draftQuoteString.safe(),
        lastMessageType = lastMessageType.safe(0),
        showMessageStatus = showMessageStatus,
        lastMessageStatus = lastMessageStatus ?: 0,

        groupAvatar = groupAvatar.safe(),
        groupName = groupName.safe(),
        groupIsVip = groupIsVip,

        isHongbao = isHongbao,
        isGift = isGift,

        isVideoChatting = isVideoChatting,
        isAtMe = isAtMe,
        atText = atText.safe()
    )

    companion object {
        const val Type = "g"

        fun key(id: String) = SessionKey(Type, id)
    }
}