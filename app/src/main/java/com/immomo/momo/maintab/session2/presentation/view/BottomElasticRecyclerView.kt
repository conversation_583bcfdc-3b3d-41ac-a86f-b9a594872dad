package com.immomo.momo.maintab.session2.presentation.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.core.view.updateLayoutParams
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.cosmos.mdlog.MDLog
import com.immomo.android.module.specific.data.mapper.safe

/**
 * CREATED BY liu.chong
 * AT 2022/3/8
 */
class BottomElasticRecyclerView(context: Context, attrs: AttributeSet?) :
    RecyclerView(context, attrs) {
    private var startPosition :Float?= null
    private var overScrolling = false
    private var canTrigger = false//是否越过阈值
    var elasticity = 0.4F
    var triggerDp = 30 * 2
    var onOverScroll: ((isReachedLimit: Boolean) -> Unit)? = null
    var onOverScrollStateChanged: ((isReachedLimit: Boolean) -> Unit)? = null
    var onTrigger: (() -> Unit)? = null
    var onCancel: (() -> Unit)? = null


    private fun isHorizon(): Boolean {
        return layoutManager?.canScrollHorizontally() == true
    }

    private fun updateTranslation(trans: Float) {
        if (isHorizon()) {
            translationX = trans
        } else {
            translationY = trans
        }
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        parent.requestDisallowInterceptTouchEvent(true)
        when (event?.action) {
            MotionEvent.ACTION_UP -> {
                if (overScrolling) {
                    if (canTrigger) onTrigger?.invoke() else onCancel?.invoke()
                }
                startPosition = null
                updateTranslation(0F)
                overScrolling = false
            }
            MotionEvent.ACTION_CANCEL->{
                if (overScrolling) {
                    if (canTrigger) onTrigger?.invoke() else onCancel?.invoke()
                }
                startPosition = null
                updateTranslation(0F)
                overScrolling = false
            }
            MotionEvent.ACTION_MOVE -> {

                val lm = layoutManager as LinearLayoutManager
                val isBottom =
                    lm.findLastCompletelyVisibleItemPosition() == (adapter?.itemCount ?: 0) - 1
                if (isBottom.not()) {
                    return super.onTouchEvent(event)
                }
                if (startPosition == null) {
                    startPosition = if (isHorizon()) event.x else event.y
                }
                val offset = startPosition.safe() - if (isHorizon()) event.x else event.y
                if (offset <= 0) {
                    updateTranslation(0F)
                    return super.onTouchEvent(event)
                }
                MDLog.d("lclc_","onTouchEvent startPosition:$startPosition offset: $offset")
                overScrolling = true
                updateLayoutParams<MarginLayoutParams> {
                    val pix = offset * elasticity
                    val reachedLimit = pix > triggerDp
                    if (canTrigger != reachedLimit) {
                        onOverScrollStateChanged?.invoke(reachedLimit)
                        canTrigger = reachedLimit
                    }
                    onOverScroll?.invoke(canTrigger)
                    updateTranslation(-pix)
                }
                return true
            }
        }
        return super.onTouchEvent(event)
    }
}