package com.immomo.momo.maintab.session2.data.manager

import android.util.Log
import com.immomo.momo.maintab.session2.HasSessionKey
import com.immomo.momo.maintab.session2.defs.DebuggerSessionDefinition
import com.immomo.momo.maintab.session2.defs.GotoSessionDefinition
import com.immomo.momo.maintab.session2.defs.PaasSessionDefinition
import com.immomo.momo.messages.service.FlashChatService
import com.immomo.momo.protocol.imjson.util.Debugger
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.sessions.MessageServiceHelper

enum class MessageSaveType {
    Skip, Sync, Receive, Send
}

sealed class SessionMessage(
    open val messageList: List<Message> = emptyList()
) : HasSessionKey {
    class Sync(
        override val sessionKey: SessionKey,
        override val messageList: List<Message>
    ) : SessionMessage()

    class Receive(
        override val sessionKey: SessionKey,
        override val messageList: List<Message>
    ) : SessionMessage()

    class Send(
        override val sessionKey: SessionKey,
        override val messageList: List<Message>
    ) : SessionMessage()

    class Update(
        override val sessionKey: SessionKey,
        override val messageList: List<Message>
    ) : SessionMessage() {
        constructor(sessionKey: SessionKey, message: Message?) : this(
            sessionKey,
            if (message == null) emptyList() else arrayListOf(message)
        )
    }

    class UpdateStatus(
        override val sessionKey: SessionKey,
        val messageIds: List<String> = emptyList(),
        val isMessageReceive: Boolean? = null,
        val isUpdateAll: Boolean = false,
        val fromStatus: Array<Int>? = null,
        val toStatus: Int
    ) : SessionMessage()

    class ResetLast(
        override val sessionKey: SessionKey,
        val message: Message
    ) : SessionMessage()

    class Delete(
        override val sessionKey: SessionKey,
        val messageIds: List<String> = emptyList(),
        val isDeleteAll: Boolean = false
    ) : SessionMessage()
}

object SessionMessageProcessor {
    inline fun MutableMap<String, MutableList<Message>>.getMessageList(key: String) =
        this.getOrPut(key) { mutableListOf() }

    private fun groupByMessage(
        messageList: List<Message?>,
        block: (SessionKey, List<Message>) -> Unit
    ) {
        val messageMap = mutableMapOf<String, MutableList<Message>>()

        messageList.forEach { message ->
            if (message == null) return@forEach

            when (message.chatType) {
                Message.CHATTYPE_PAAS -> {
                    val sessionKey = PaasSessionDefinition.sessionKey(
                        message.bid,
                        message.busiActionId,
                        message.remoteId
                    )
                    messageMap.getMessageList(sessionKey.value).add(message)
                }

                Message.CHATTYPE_USER ->
                    if (message.remoteId == Debugger.LoggerSessionId) {
                        messageMap.getMessageList(DebuggerSessionDefinition.Key).add(message)
                    } else if (!(message.receive && message.isSayhi)) {
                        messageMap.getMessageList("u_${message.remoteId}").add(message)
                    }

                Message.CHATTYPE_GROUP ->
                    messageMap.getMessageList("g_${message.groupId}").add(message)

                Message.CHATTYPE_DISCUSS ->
                    messageMap.getMessageList("d_${message.discussId}").add(message)

                Message.CHATTYPE_VCHAT_SUPER_ROOM ->
                    messageMap.getMessageList("v_${message.vchatRoomId}").add(message)

                Message.CHATTYPE_FLASH_CHAT ->
                    if (!FlashChatService.getInstance().isFakeMessageCame(message)) {
                        messageMap.getMessageList(GotoSessionDefinition.FLASH_CHAT).add(message)
                    }

                Message.CHATTYPE_TEXT_CHAT ->
                    messageMap.getMessageList(GotoSessionDefinition.TEXT_CHAT).add(message)
            }
        }
        messageMap.forEach { (key, messages) ->
            block(SessionKey.fromString(key), messages)
        }
    }

    private fun SessionMessage.submit(createWhenAbsent: Boolean) {
        SessionManager.getMessageInfoCache().handleMessage(this)
        SessionManager.get().syncSession(this, createWhenAbsent = createWhenAbsent)
    }

    @JvmStatic
    fun onSaveMessage(message: Message?, messageSaveType: MessageSaveType) {
        if (message == null) return
        onSaveMessage(arrayListOf(message), messageSaveType)
    }

    @JvmStatic
    fun onSaveMessage(messageList: List<Message?>?, messageSaveType: MessageSaveType) {
        messageList?.filterNotNull()?.let {
            if (it.isEmpty()) return

            when (messageSaveType) {
                MessageSaveType.Skip -> {
                }

                MessageSaveType.Sync -> onSyncMessage(it)
                MessageSaveType.Receive -> onReceiveMessage(it)
                MessageSaveType.Send -> onSendMessage(it)
            }
        }
    }

    @JvmStatic
    fun onSyncMessage(messageList: List<Message?>) {
        groupByMessage(messageList) { sessionKey, messages ->
            Log.d(
                "SessionMessageProcessor",
                "sync message($sessionKey): ${messages.map { it.msgId }}"
            )
            SessionMessage.Sync(sessionKey, messages).submit(true)
        }
    }

    @JvmStatic
    fun onReceiveMessage(messageList: List<Message?>) {
        groupByMessage(messageList) { sessionKey, messages ->
            Log.d(
                "SessionMessageProcessor",
                "recv message($sessionKey): ${messages.map { it.msgId }}"
            )
            SessionMessage.Receive(sessionKey, messages).submit(true)
        }
    }

    @JvmStatic
    fun onSendMessage(message: Message) {
        onSendMessage(arrayListOf(message))
    }

    @JvmStatic
    fun onSendMessage(messageList: List<Message?>) {
        groupByMessage(messageList) { sessionKey, messages ->
            Log.d(
                "SessionMessageProcessor",
                "send message($sessionKey): ${messages.map { it.msgId }}"
            )
            SessionMessage.Send(sessionKey, messages).submit(true)
        }
    }

    @JvmStatic
    fun onUpdateMessage(message: Message) {
        groupByMessage(arrayListOf(message)) { sessionKey, messages ->
            Log.d(
                "SessionMessageProcessor",
                "update message($sessionKey): ${messages.map { it.msgId }}"
            )
            SessionMessage.Update(sessionKey, messages).submit(true)
        }
    }

    @JvmStatic
    fun onUpdateMessageStatus(message: Message) {
        groupByMessage(arrayListOf(message)) { sessionKey, messages ->
            Log.d(
                "SessionMessageProcessor",
                "update message($sessionKey): ${messages.map { it.msgId }} to ${message.status}"
            )
            SessionMessage.UpdateStatus(
                sessionKey = sessionKey,
                messageIds = messages.map { it.msgId },
                isMessageReceive = message.receive,
                isUpdateAll = false,
                toStatus = message.status
            ).submit(false)
        }
    }


    @JvmStatic
    fun onUpdateMessageStatus(
        chatType: Int,
        remoteId: String?,
        messageIds: List<String?>,
        isMessageReceive: Boolean,
        status: Int
    ) {
        if (remoteId == null) return

        val sessionKey =
            SessionKey.fromString(MessageServiceHelper.getSessionIdByType(remoteId, chatType))
        val filtered = messageIds.filterNotNull()
        Log.d("SessionMessageProcessor", "update message($sessionKey): to $status on $messageIds")
        SessionMessage.UpdateStatus(
            sessionKey,
            filtered,
            isMessageReceive = isMessageReceive,
            isUpdateAll = false,
            toStatus = status
        ).submit(false)
    }

    @JvmStatic
    fun onUpdateAllMessageStatus(
        chatType: Int,
        remoteId: String,
        isMessageReceive: Boolean?,
        fromStatus: Array<Int>?,
        toStatus: Int
    ) {
        Log.d(
            "SessionMessageProcessor",
            "update all message(${chatType}_$remoteId): $fromStatus -> $toStatus"
        )

        val sessionKey = SessionKey.fromString(
            MessageServiceHelper.getSessionIdByType(remoteId, chatType)
        )
        SessionMessage.UpdateStatus(
            sessionKey = sessionKey,
            isMessageReceive = isMessageReceive,
            isUpdateAll = true,
            fromStatus = fromStatus,
            toStatus = toStatus
        ).also {
            SessionManager.getMessageInfoCache().handleMessage(it)
            if (isMessageReceive != true) {
                SessionManager.get().syncSession(it, false)
            }
        }
    }
    @JvmStatic
    fun onUpdateAllMessageStatus(
        sessionKey: SessionKey,
        isMessageReceive: Boolean?,
        fromStatus: Array<Int>?,
        toStatus: Int
    ) {
        SessionMessage.UpdateStatus(
            sessionKey = sessionKey,
            isMessageReceive = isMessageReceive,
            isUpdateAll = true,
            fromStatus = fromStatus,
            toStatus = toStatus
        ).also {
            SessionManager.getMessageInfoCache().handleMessage(it)
            if (isMessageReceive != true) {
                SessionManager.get().syncSession(it, false)
            }
        }
    }

    @JvmStatic
    @JvmOverloads
    fun onResetLastMessage(
        chatType: Int,
        remoteId: String,
        message: Message,
        createWhenAbsent: Boolean = false
    ) {
        Log.d("SessionMessageProcessor", "rest message: ${message.msgId}")

        val sessionKey = if (chatType == Session.TYPE_FLASH_CHAT) {
            GotoSessionDefinition.KEY_FLASH_CHAT
        } else if (chatType == Session.TYPE_TEXT_CHAT) {
            GotoSessionDefinition.KEY_TEXT_CHAT
        } else SessionKey.fromString(
            MessageServiceHelper.getSessionIdByType(remoteId, chatType)
        )
        SessionMessage.ResetLast(
            sessionKey = sessionKey,
            message = message
        ).submit(createWhenAbsent)
    }
    @JvmStatic
    @JvmOverloads
    fun onResetLastMessage(
        sessionKey: SessionKey,
        message: Message,
        createWhenAbsent: Boolean = false
    ) {
        Log.d("SessionMessageProcessor", "rest message: ${message.msgId}")
        SessionMessage.ResetLast(
            sessionKey = sessionKey,
            message = message
        ).submit(createWhenAbsent)
    }

    @JvmStatic
    fun onDeleteMessage(
        chatType: Int,
        remoteId: String,
        messageId: String?,
        messageStatus: Int?
    ) {
        if (messageId == null) return

        Log.d("SessionMessageProcessor", "delete $chatType-$remoteId")
        val sessionKey = SessionKey.fromString(
            MessageServiceHelper.getSessionIdByType(remoteId, chatType)
        )
        SessionMessage.Delete(
            sessionKey = sessionKey,
            isDeleteAll = false
        ).submit(false)
    }

    @JvmStatic
    fun onDeleteMessage(
        chatType: Int,
        remoteId: String,
        isDeleteAll: Boolean
    ) {
        Log.d("SessionMessageProcessor", "delete $chatType-$remoteId")
        val sessionKey = SessionKey.fromString(
            MessageServiceHelper.getSessionIdByType(remoteId, chatType)
        )
        SessionMessage.Delete(
            sessionKey = sessionKey,
            isDeleteAll = isDeleteAll
        ).submit(false)
    }

    @JvmStatic
    fun onDeleteAllMessage(
        chatType: Int,
        remoteId: String
    ) {
        val sessionKey = SessionKey.fromString(
            MessageServiceHelper.getSessionIdByType(remoteId, chatType)
        )
        SessionMessage.Delete(
            sessionKey = sessionKey,
            isDeleteAll = true
        ).submit(false)
    }

    fun onDeleteMessage(
        sessionKey: SessionKey,
        deleteAll: Boolean = false
    ) {
        SessionMessage.Delete(
            sessionKey = sessionKey,
            isDeleteAll = deleteAll
        ).submit(false)
    }
}