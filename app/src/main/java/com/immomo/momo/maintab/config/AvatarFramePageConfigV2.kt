package com.immomo.momo.maintab.config

import com.immomo.annotations.appconfig.AppConfigField
import com.immomo.annotations.appconfig.appconfigv2.AppConfigV2
import com.immomo.momo.maintab.config.apt.AvatarFramePageConfigV2Getter

/**
 * 生态治理配置
 */
@AppConfigV2
class AvatarFramePageConfigV2 {

    @AppConfigField(mark = "600", key = "tip_show", defValue = "0", isSysValue = true)
    var tipShow: Int = 0

    @AppConfigField(mark = "600", key = "tip_text", defValue = "头像框首发，可前往装扮中心获取", isSysValue = true)
    var tipText: String = ""

    @AppConfigField(mark = "600", key = "tip_duration", defValue = "5", isSysValue = true)
    var tipDuration: Int = 5

    @AppConfigField(mark = "600", key = "tip_goto", defValue = "", isSysValue = true)
    var tipGoto: String = ""

    companion object {

        val tipShowConfig = AvatarFramePageConfigV2Getter.get().tipShow() == 1

        val tipTextConfig = AvatarFramePageConfigV2Getter.get().tipText()

        val tipDurationConfig = AvatarFramePageConfigV2Getter.get().tipDuration() * 1000L

        val tipGotoConfig = AvatarFramePageConfigV2Getter.get().tipGoto()

    }

}