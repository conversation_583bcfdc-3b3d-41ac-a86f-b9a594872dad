package com.immomo.momo.maintab.sessionlist.itemmodel

import android.animation.Animator
import android.animation.ValueAnimator
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.view.updateLayoutParams
import com.cosmos.mdlog.MDLog
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.view.dp
import com.immomo.mmutil.task.MomoMainThreadExecutor
import com.immomo.momo.maintab.sessionlist.bean.CardItemData
import com.immomo.momo.util.MomoKit
import com.immomo.svgaplayer.SVGAAnimListenerAdapter

class SessionTopOperatorScaleChildItemModel(
    itemModeData: CardItemData,
    runHintAnimation: Boolean,
    override val onGuideAnimationFinished: () -> Unit
) :
    SessionTopOperatorChildItemModel(itemModeData, runHintAnimation, onGuideAnimationFinished) {
    private val animators = mutableListOf<Animator>()
    private var hasFinished: Boolean? = null

    override fun startShackAnimation(holder: ViewHolder, onAnimationStart: (() -> Unit)?) {
        setTitle(holder.itemTitle2, itemModeData.activityInfo?.title)
        refreshDescAnimationContent(
            holder.itemSubtitle2,
            itemModeData.activityInfo?.desc?.let { listOf(it) },
            true
        )
        playSVGScaleAnim(holder)
    }

    private fun playSVGScaleAnim(holder: ViewHolder) {
        hasFinished = false
        cancelAnim()
        resumeState(holder)
        holder.itemBgSvg2.stopAnimCompletely()
        MomoMainThreadExecutor.cancelAllRunnables(holder.toString())
        MomoMainThreadExecutor.postDelayed(holder.toString(), {
            holder.itemBgSvg2.stopAnimCompletely()
            val svga =
                if (MomoKit.isDarkMode(holder.itemBgIcon.context))
                    itemModeData.activityInfo?.darkSvga
                else itemModeData.activityInfo?.svga

            holder.itemBgSvg2.startSVGAAnimWithListener(svga, 1,
                object : SVGAAnimListenerAdapter() {
                    override fun onStart() {
                        super.onStart()
                        holder.itemBgIcon.visibility = View.GONE
                        holder.itemBgSvga.visibility = View.GONE

                        holder.itemBgSvg2.visibility = View.VISIBLE
                        startTransScaleOut(holder)
                    }

                    override fun onStep(frame: Int, percentage: Double) {
                        super.onStep(frame, percentage)
                        if (frame == itemModeData.activityInfo?.endFrame.safe(228)) {
                            startTransScaleIn(holder)
                        }
                    }

                    override fun onFinished() {
                        super.onFinished()
                        resumeState(holder)
                    }
                })
        }, itemModeData.activityInfo?.delay.safe(1).times(1000))
    }

    private fun startTransScaleIn(holder: ViewHolder) {
        val titleTranslationOffset = (-28).dp
        val animator = ValueAnimator.ofFloat(1F, 0F)
        animator.duration = 400
        animator.doOnStart {
            holder.itemTitle2.visibility = View.VISIBLE
            holder.itemTitle2.alpha = 1F
            holder.itemSubtitle2.visibility = View.VISIBLE
            holder.itemSubtitle2.alpha = 1F
        }
        animator.addUpdateListener {
            val factor = it.animatedValue as Float
            val w = (180 - 143).dp.toFloat() * factor + 143.dp
            holder.itemView.updateLayoutParams<MarginLayoutParams> {
                width = w.toInt()
            }
            val fadeInFactor = 1 - factor
            holder.itemIcon.alpha = fadeInFactor
            holder.itemTitle.alpha = fadeInFactor
            holder.itemSvga.alpha = fadeInFactor
            holder.itemSubtitle.alpha = fadeInFactor

            holder.itemSvga.scaleX = fadeInFactor
            holder.itemSvga.scaleY = fadeInFactor
            holder.itemIcon.scaleX = fadeInFactor
            holder.itemIcon.scaleY = fadeInFactor

            holder.itemIcon.translationX = titleTranslationOffset * factor
            holder.itemSvga.translationX = titleTranslationOffset * factor
            holder.itemTitle.translationX = titleTranslationOffset * factor
            holder.itemSubtitle.translationX = titleTranslationOffset * factor
            holder.itemTitle2.translationX = titleTranslationOffset * factor
            holder.itemSubtitle2.translationX = titleTranslationOffset * factor
            holder.itemTitle2.alpha = factor
            holder.itemSubtitle2.alpha = factor

        }
        animator.doOnEnd {
            hasFinished = true
        }
        animator.start()
        animators.add(animator)
    }

    override fun refreshHolderView(holder: ViewHolder, isPreloadContent: Boolean) {
        super.refreshHolderView(holder, isPreloadContent)
        if (hasFinished == false) {//null表示还没开始播
            //只做重播，不主动播
            playSVGScaleAnim(holder)
        }
    }

    private fun resumeState(holder: ViewHolder) {
        holder.itemTitle2.visibility = View.GONE
        holder.itemSubtitle2.visibility = View.GONE
        holder.itemBgSvg2.visibility = View.GONE
        holder.itemBgSvga.visibility = View.VISIBLE
        holder.itemBgIcon.visibility = View.VISIBLE
        holder.itemTitle.alpha = 1F
        holder.itemSubtitle.alpha = 1F
        holder.itemIcon.alpha = 1F
        holder.itemSvga.alpha = 1F
        holder.itemIcon.scaleX = 1F
        holder.itemIcon.scaleY = 1F
        holder.itemSvga.scaleX = 1F
        holder.itemSvga.scaleY = 1F
        holder.itemIcon.translationX = 0F
        holder.itemSvga.translationX = 0F
        holder.itemTitle.translationX = 0F
        holder.itemSubtitle.translationX = 0F
        holder.itemView.updateLayoutParams<MarginLayoutParams> {
            width = 143.dp
        }
    }

    private fun startTransScaleOut(holder: ViewHolder) {
        val titleTranslationOffset = (-28).dp
        val animator = ValueAnimator.ofFloat(0F, 1F)
        animator.duration = 400

        animator.doOnStart {
            holder.itemBgSvg2.visibility = View.VISIBLE
            holder.itemTitle2.visibility = View.VISIBLE
            holder.itemTitle2.alpha = 0F
            holder.itemSubtitle2.visibility = View.VISIBLE
            holder.itemSubtitle2.alpha = 0F
        }
        animator.addUpdateListener {
            holder.itemBgSvga.visibility = View.GONE
            holder.itemBgIcon.visibility = View.GONE
            val factor = it.animatedValue as Float
            val w = (180 - 143).dp.toFloat() * factor + 143.dp
            MDLog.d("animValue:", "w:$w,,factor:$factor")
            holder.itemView.updateLayoutParams<MarginLayoutParams> {
                width = w.toInt()
            }
            val fadeOutFactor = 1 - factor
            holder.itemIcon.alpha = fadeOutFactor
            holder.itemTitle.alpha = fadeOutFactor
            holder.itemSubtitle.alpha = fadeOutFactor
            holder.itemSvga.alpha = fadeOutFactor

            holder.itemIcon.scaleX = fadeOutFactor
            holder.itemIcon.scaleY = fadeOutFactor
            holder.itemSvga.scaleX = fadeOutFactor
            holder.itemSvga.scaleY = fadeOutFactor

            holder.itemIcon.translationX = titleTranslationOffset * factor
            holder.itemSvga.translationX = titleTranslationOffset * factor
            holder.itemTitle.translationX = titleTranslationOffset * factor
            holder.itemSubtitle.translationX = titleTranslationOffset * factor
            holder.itemTitle2.translationX = titleTranslationOffset * factor
            holder.itemSubtitle2.translationX = titleTranslationOffset * factor
            holder.itemTitle2.alpha = factor
            holder.itemSubtitle2.alpha = factor

        }
        animator.start()
        animators.add(animator)
    }

    override fun unbind(holder: ViewHolder) {
        kotlin.runCatching {
            super.unbind(holder)
            cancelAnim()
        }
    }

    private fun cancelAnim() {
        animators.forEach {
            it.cancel()
            it.removeAllListeners()
        }
        animators.clear()
    }

    override fun onFragmentVisible(isResume: Boolean) {

    }

}