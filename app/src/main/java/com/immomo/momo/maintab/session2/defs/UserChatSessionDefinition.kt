package com.immomo.momo.maintab.session2.defs

import android.text.TextUtils
import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.android.module.specific.data.mapper.optionMap
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.utils.TimeUtils
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.momo.greendao.AppDBUtils
import com.immomo.momo.greendao.HepaiSessionEntityDao
import com.immomo.momo.greendao.OldFriendEntityDao
import com.immomo.momo.greendao.SpamSessionEntityDao
import com.immomo.momo.impaas.common.ext.isTextChatMsg
import com.immomo.momo.maintab.model.TextIconSessionTag
import com.immomo.momo.maintab.model.UserAvatarFrameWrapper
import com.immomo.momo.maintab.model.UserBadgeWrapper
import com.immomo.momo.maintab.session2.*
import com.immomo.momo.maintab.session2.data.database.*
import com.immomo.momo.maintab.session2.data.manager.*
import com.immomo.momo.maintab.session2.defs.UserChatSessionDefinition.Companion.Type
import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.type.UserChatSessionModel
import com.immomo.momo.maintab.session2.domain.model.type.UserChatTagModel
import com.immomo.momo.maintab.session2.domain.model.type.UserOnlineTagModel
import com.immomo.momo.maintab.session2.utils.SessionFoldHelper
import com.immomo.momo.maintab.sessionlist.SessionStickyHelper
import com.immomo.momo.maintab.sessionlist.UnreplySessionCacheHelper
import com.immomo.momo.maintab.sessionlist.util.SessionHelper
import com.immomo.momo.message.business.textchat.TChatSessionService
import com.immomo.momo.message.business.textchat.TextChatViewHelper
import com.immomo.momo.message.helper.MatchFolder
import com.immomo.momo.message.helper.MsgOfficialHelper
import com.immomo.momo.message.helper.SessionTextHelper
import com.immomo.momo.messages.service.SingleMsgServiceV2
import com.immomo.momo.service.bean.*
import com.immomo.momo.service.bean.message.*
import com.immomo.momo.service.sessions.LastMsgCache
import com.immomo.momo.service.sessions.SessionService
import com.squareup.moshi.JsonClass
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import java.util.*

@JsonClass(generateAdapter = true)
class UserChatContent : ChatContent(Type) {
    var userAvatar: String? = null
    var userName: String? = null
    var userIsVip: Boolean = false
    var officialOperation: Int = 0
    var textIconTag: TextIconSessionTag? = null
    var sessionTagLogMap: String? = null

    override fun isChatInfoValid(): Boolean {
        return userName?.isNotEmpty() == true
    }

    var userOnlineTag: UserOnlineTag? = null
    var userLocationTimestamp: Long? = null

    var isLastMessageReceive: Boolean = false

    /**
     * 用户标签，通用，需要跟资料保持同步
     */
    @Transient
    var userChatTag: ChatTag? = null

    /**
     * 有没有红包
     */
    var isHongbao = false
    var isGift = false
    var isMissedFriendCall = false
    var missedFriendCallDesc: String? = null
    var isDianDianCard = false
    var isQuestionMatch = false

    var isType28 = false
    var lastType28Prompt: String? = null
    var type28AppId: String? = null

    /**
     * [xxxx]特殊前缀,如果最后一条消息是任务礼物，则设置 specialText
     */
    var specialText = ""

    /**
     * 类似  点点匹配 这种红色的前缀
     */
    var pushPrefix: String? = null

    var sessionBusinessType = Session.BUSINESS_NONE

    var onlineMsgTime: String? = null

    var sevenDaysIn: String? = null

    var sevenDaysOut: String? = null

    var distance: Int = -3

    var needExposeOnline = true

    @Transient
    var tagUniformLabels: UserBadgeWrapper? = null // 用户标签

    var fireIcon: String? = ""

    var fireIconDark: String? = ""

    @Transient
    var fireSign: String? = ""

    var userAvatarFrame: UserAvatarFrameWrapper? = null // 用户头像框

    override fun idx1(): String? {
        return sessionBusinessType.toString()
    }

    // 类型为type28且含有prompt字段（不一定展示出来，实际展示时有优先级判断）
    fun canShowType28Prompt(): Boolean {
        return isType28 && !TextUtils.isEmpty(lastType28Prompt)
    }
}


class UserChatSessionDefinition
    : ChatSessionDefinition<UserChatContent, UserChatSessionModel>(
    Type,
    SessionContentParser.moshiParser()
) {

    override fun createContent(): UserChatContent {
        return UserChatContent()
    }

    override fun getLastMessage(session: SessionEntity): Message? {
        return SingleMsgServiceV2.service.findLastMessage(session.sessionId)
    }

    override fun onUpdateMessage(
        session: SessionEntity,
        content: UserChatContent,
        data: SessionMessage.Update
    ): Boolean {
        val onUpdateMessage = super.onUpdateMessage(session, content, data)
        if (session.foldType == FolderType.FOLDED_MSG) {
            SessionFoldHelper.notifyMsgFoldSessionUpdateLastMsg()
        }
        return onUpdateMessage

    }

    override fun updateSessionMetadataWithEveryMessage(
        session: SessionEntity,
        content: UserChatContent,
        message: Message
    ) {
        processFoldTypeByMsg(session, message)
        val updateFoldSessions = mutableSetOf<String>()

        /**
         * 当未回复的招呼收到对方回复后，要把session的状态从在二级页面变成在消息帧展示
         * 同时更新打出的招呼入口信息
         */

        if (session.foldType == FolderType.Unreply) {
            if (message.receive && !message.isMessageNotice) {
                session.foldType = FolderType.Default
                UnreplySessionCacheHelper.removeUnreplyFromCache(message.remoteId)
            }
            updateFoldSessions.add(Session.ID.HI_UNREPLY)
        } else if (session.foldType == FolderType.NewBOY) {
            if (!message.isMessageNotice && message.contentType != Message.CONTENTTYPE_MESSAGE_UPDATE_NOTICE) {
                session.foldType = FolderType.Default
                GlobalScope.async(MMDispatchers.User) {
                    (AppDBUtils.getInstance()
                        .getDao(OldFriendEntity::class.java) as OldFriendEntityDao).insertOrReplace(
                        OldFriendEntity(session.sessionId)
                    )
                }
            }
            updateFoldSessions.add(Session.ID.NEW_BOY)
        } else if (session.foldType == FolderType.HePai) {
            if (!message.receive
                && !message.isMessageNotice
                && message.contentType != Message.CONTENTTYPE_MESSAGE_UPDATE_NOTICE
            ) { //发出的消息
                session.foldType = FolderType.Default

                GlobalScope.async(MMDispatchers.User) {
                    (AppDBUtils.getInstance()
                        .getDao(HepaiSessionEntity::class.java) as HepaiSessionEntityDao).insertOrReplace(
                        HepaiSessionEntity(session.sessionId)
                    )
                }
            }

            updateFoldSessions.add(Session.ID.HEPAI)
        } else if (session.foldType == FolderType.Spam) {
            GlobalScope.async(MMDispatchers.User) {
                (AppDBUtils.getInstance()
                    .getDao(SpamSessionEntity::class.java) as SpamSessionEntityDao).insertOrReplace(
                    SpamSessionEntity(session.sessionId)
                )
            }
            updateFoldSessions.add(Session.ID.SPAM)
        } else if (session.foldType == FolderType.FOLDED_MSG) {
            //如果不是之前的消息，视为新增消息，将挪出session。
            // 就不判断消息是否比当前session新了
            session.foldType = FolderType.Default
            SessionFoldHelper.notifyMsgFoldSessionUpdateLastMsg()
        } else if (session.foldType == FolderType.GAMEBOX) {
            session.foldType = FolderType.GAMEBOX
            updateFoldSessions.add(Session.ID.GAME_BOX)
        } else if (session.foldType == FolderType.TEXT_CHAT) {
            SessionManager.get().syncSession(
                SessionMessage.Sync(
                    GotoSessionDefinition.KEY_TEXT_CHAT,
                    listOf(message)
                ), true
            )
        } else {
//            if (message.fold == FolderType.Spam) {
//                updateFoldSessions.add(Session.ID.SPAM)
//            }
            if (message.fold == FolderType.HePai) {
                updateHePaiFoldMsg(session, message)
            } else {
                session.foldType = message.fold
            }

            if (session.foldType == FolderType.Default
                && UnreplySessionCacheHelper.needUnreply(message.remoteId)
            ) {
                session.foldType = FolderType.Unreply
                UnreplySessionCacheHelper.removeUnreplyFromCache(message.remoteId)

                updateFoldSessions.add(Session.ID.HI_UNREPLY)
            }
            if (MsgOfficialHelper.isOfficialFoldSession(
                    session.sessionType,
                    session.sessionId,
                    message.fold != 0
                )
            ) {
                session.foldType = FolderType.Official
            }
            if (session.foldType == FolderType.Official) {
                updateFoldSessions.add(Session.ID.FolderOfficial)
            }
        }

        // 有红娘标识就添加，添加后无过期
        if (message.isKliaoMatchMsg) {
            content.sessionBusinessType = Session.BUSINESS_KLIAO_MATCH
        }

        session.recommendTime = 0 //收到新消息后自动取消推荐状态

        updateFoldSessions.forEach {
            SessionManager.get().syncSession(
                FoldSessionParam(
                    it,
                    content.userName,
                    message.msgId,
                    message.timestamp?.time,
                    message
                ), createWhenAbsent = true
            )
        }
    }

    private fun processFoldTypeByMsg(
        session: SessionEntity,
        message: Message
    ) {
        session.foldTypeV3 = message.foldV3
        if (message.isTextChatMsg()) {
            if (!TextChatViewHelper.isChatUnlocked(message.remoteId.safe())) {
                session.foldType = FolderType.TEXT_CHAT
            }
        }
    }

    private fun updateHePaiFoldMsg(session: SessionEntity, message: Message) {
        GlobalScope.async(MMDispatchers.User) {
            var foldHepai = false
            if (!message.isImSpam && SessionService.getInstance()
                    .checkExist(FoldSessionDefinition.HEPAI)
            ) {
                try {
                    val hepaiOldDao =
                        AppDBUtils.getInstance()
                            .getDao(HepaiSessionEntity::class.java) as HepaiSessionEntityDao
                    val hepaiOldList =
                        hepaiOldDao.queryBuilder().build().list().map { it.sessionId }.toSet()
                    foldHepai = !hepaiOldList.contains(session.sessionId)
                } catch (e: Exception) {
                    // nothing
                }
            }

            if (foldHepai) {
                session.foldType = FolderType.HePai
                GlobalScope.launch(MMDispatchers.Main) {
                    SessionService.getInstance().updateFoldSession(Session.ID.HEPAI)
                }
            } else {
                session.foldType = FolderType.Default
            }
        }
    }

    override fun updateSessionDescWithLastMessage(
        session: SessionEntity,
        content: UserChatContent,
        lastMessage: Message,
        updateProcessedTime: Boolean
    ) {
        super.updateSessionDescWithLastMessage(session, content, lastMessage, updateProcessedTime)
        var updateType17 = lastMessage.extraData.getOrDefault("update17", "0") == "1"
        // update session order params
        if ((updateType17 || lastMessage.isUpdateSession) && !lastMessage.isImSpam) {
            session.lastMsgId = lastMessage.msgId ?: session.lastMsgId
            if (lastMessage.stopFloat == 0) {
                session.lastMsgTime = lastMessage.timestamp?.time ?: session.lastMsgTime
            }
            content.lastMessageType = lastMessage.contentType
            LastMsgCache.onSendNewMsg(lastMessage)
        }

        if (lastMessage.notShowInSession) {
            content.forcedDesc = lastMessage.recommendReason ?: ""
        } else if ((updateType17 || lastMessage.isUpdateSession) && !lastMessage.isImSpam) {
            content.forcedDesc = null
            content.distanceInfo = lastMessage.diatance
            content.lastMessageContent = SessionTextHelper.getMessageContent(lastMessage)

            content.isLastMessageReceive = lastMessage.receive
            content.onlyShowMessageContent =
                SessionHelper.hideMsgDistanceStrInSession(content)
        }

        updateLastMessageStatus(
            session,
            lastMessage.msgId,
            lastMessage.receive,
            lastMessage.status
        )
    }

    override fun updateSessionIndicatorWithEveryMessage(
        session: SessionEntity,
        content: UserChatContent,
        message: Message?
    ) {
        if (message?.status == Message.STATUS_CLOUD) return

        //设置未读数 unreadMessageCount
        session.unreadMessageCount = UnreadCountMessageInterceptor.getCount(key(session.sessionId))
        if (session.unreadMessageCount > 0) {
            session.silentMessageCount = 0
        } else {
            session.silentMessageCount =
                UnreadCountMessageInterceptor.getSilentCount(session.sessionKey())
        }
        //根据优先级设置session未读消息的前缀 setChatSessionPrefixFromUnreadMsg
        //新增unread的表/缓存
        content.isGift = LastUnreadMessageInterceptor.getUnread(
            key(session.sessionId),
            Message.CONTENTTYPE_MESSAGE_GIFT
        )?.isValid() == true

        content.isHongbao = LastUnreadMessageInterceptor.getUnread(
            key(session.sessionId),
            Message.CONTENTTYPE_MESSAGE_HONGBAO
        )?.isValid() == true

        LastUnreadMessageInterceptor.getUnread(
            key(session.sessionId),
            Message.CONTENTTYPE_MESSAGE_FRIEND_QCHAT
        ).also {
            content.isMissedFriendCall = it?.isValid() == true
            content.missedFriendCallDesc = it?.data.castOrNull()
        }

        LastUnreadMessageInterceptor.getUnread(
            key(session.sessionId),
            Message.CONTENTTYPE_MESSAGE_QUESTION_MATCH
        ).also {
            content.isQuestionMatch = it?.isValid() == true
            content.pushPrefix = it?.data.castOrNull() ?: content.pushPrefix
        }

        LastUnreadMessageInterceptor.getUnread(
            key(session.sessionId),
            Message.CONTENTTYPE_MESSAGE_DIANDIAN
        ).also {
            content.isDianDianCard = it?.isValid() == true
            content.pushPrefix = it?.data.castOrNull() ?: content.pushPrefix
        }

        LastUnreadMessageInterceptor.getUnread(
            key(session.sessionId),
            Message.CONTENTTYPE_MESSAGE_MGS_GROUP
        ).also {
            content.isType28 = it?.isValid() == true
            it?.data.castOrNull<Pair<String?, String?>>()?.let { pair ->
                content.lastType28Prompt = pair.first
                content.type28AppId = pair.second
            }
        }
    }

    override fun onReloadChatInfo(id: String, session: SessionEntity, forceReload: Boolean) {
        //用户头像，昵称，相关数据
        val content = session.content as? UserChatContent ?: return
        content.pendingReloadChatInfo = true

        SessionManager.getInfoCache().fetchUser(session, content.chatId, forceReload) { user ->
            this.content.castOrNull<UserChatContent>()?.also {
                it.pendingReloadChatInfo = false

                it.userAvatar = user.loadImageId
                it.userName = user.displayName ?: session.sessionId
                it.userIsVip = user.isMomoVip
                it.officialOperation = user.officialOperation

                //强制刷新的数据不对，跳过
                if (!forceReload) {
                    it.userOnlineTag = user.onlineTag
                    it.userLocationTimestamp = user.getLocationTimestamp()?.time

                    it.userChatTag = user.chatTag
                    //根据chatTag刷新onlyShowMessageContent
                    it.onlyShowMessageContent =
                        SessionHelper.hideMsgDistanceStrInSession(content)
                }
            } != null
        }
    }

    //SessionListPresenter#processSessionNewMessage
    override fun syncSession(session: SessionEntity, data: Any?): Boolean {
        val content = session.content as? UserChatContent ?: return false
        content.chatId = session.sessionId

        session.isSticky =
            SessionStickyHelper.getInstance().getOrderID("u_${session.sessionId}") != -1L

        content.userName = content.chatId
        onReloadChatInfo(session.sessionId, session, false)

        return super.syncSession(session, data)
    }

    override fun isTransformCompatible(oldSessionType: Int, oldSessionId: String): String? {
        return if (oldSessionId.startsWith("u")) {
            val momoid = oldSessionId.removePrefix("u_")
            //过滤旧的点点号
            if (momoid != MatchFolder.oldMatchHelperChatId) momoid else null
        } else null
    }

    override fun transformOldSession(id: String): SessionEntity? {
        val oldSession = SessionService.getInstance().getSession("u_$id") ?: return null

        val session = createSession(id)
        syncSession(session, null)
        val content = session.content.castOrNull<UserChatContent>() ?: return null

        session.lastMsgId = oldSession.lastmsgId
        session.lastMsgTime = oldSession.lastMessage?.timestampMillis ?: 0
        session.lastFetchTime = oldSession.fetchtime?.time ?: 0
        session.orderId = oldSession.orderId

        content.draftString = oldSession.draftString
        content.draftQuoteString = oldSession.draftQuoteString

        session.foldType = oldSession.foldType
        content.sessionBusinessType = content.sessionBusinessType

        updateSessionIndicatorWithEveryMessage(session, content, oldSession.lastMessage)
        oldSession.lastMessage?.let {
            updateSessionDescWithLastMessage(session, content, it, true)
        }
        return session
    }

    override fun saveOldSession(session: SessionEntity) {
        val content = session.content.castOrNull<UserChatContent>() ?: return
        if (TChatSessionService.saveToOldDb(session)) {
            return
        }
        SessionService.getInstance().updateSession("u_${session.sessionId}") {
            it.type = Session.TYPE_CHAT
            it.chatId = session.sessionId

            it.lastmsgId = session.lastMsgId
            it.setOrderid(session.orderId)
            it.fetchtime = Date(session.lastFetchTime)

            it.draftString = content.draftString
            it.draftQuoteString = content.draftQuoteString
            it.unreadMessageCount = session.unreadMessageCount

            it.foldType = session.foldType ?: FolderType.Default
            it.sessionBusinessType = content.sessionBusinessType
            it.recommendTime = session.recommendTime
            true
        }
    }

    override fun removeOldSession(id: String) {
        SessionService.getInstance().deleteSession("u_$id")
    }

    override fun onClearUnread(session: SessionEntity) {
        super.onClearUnread(session)
        val updateFoldSessions = mutableSetOf<String>()

        if (session.foldType == FolderType.NewBOY) {
            updateFoldSessions.add(Session.ID.NEW_BOY)
        }
        if (session.foldType == FolderType.HePai) {
            updateFoldSessions.add(Session.ID.HEPAI)
        }
        if (session.foldType == FolderType.Official) {
            updateFoldSessions.add(Session.ID.FolderOfficial)
        }
        if (session.foldType == FolderType.GAMEBOX) {
            updateFoldSessions.add(Session.ID.GAME_BOX)
        }
        updateFoldSessions.forEach {
            SessionManager.get().syncSession(
                FoldSessionParam(
                    it,
                    null,
                    null,
                    null,
                    null
                ), createWhenAbsent = true
            )
        }
    }

    override fun UserChatContent.contentToModel(
        baseInfo: BaseSessionInfo
    ): UserChatSessionModel = UserChatSessionModel(
        baseInfo = baseInfo,
        chatId = chatId.safe(),
        desc = desc.safe(),
        draftString = draftString.safe(),
        draftQuoteString = draftQuoteString.safe(),
        lastMessageType = lastMessageType.safe(0),
        showMessageStatus = showMessageStatus,
        lastMessageStatus = lastMessageStatus ?: 0,
        userAvatar = userAvatar.safe(),
        userName = userName.safe(),
        userIsVip = userIsVip,
        userOnlineTag = userOnlineTag.optionMap {
            UserOnlineTagModel(
                it.getName().safe(),
                it.getTagColor().safe(),
                it.getAction().safe(),
                it.canShowAnim(),
                it.getRoomPattern().safe(),
                needExposeOnline
            )
        },
        userLocationTimestamp = userLocationTimestamp.safe(0L),
        userChatTag = userChatTag.optionMap { UserChatTagModel(it.text.safe()) },

        isHongbao = isHongbao,
        isGift = isGift,
        isMissedFriendCall = isMissedFriendCall,
        missedFriendCallDesc = missedFriendCallDesc.safe(),
        isDianDianCard = isDianDianCard,
        isQuestionMatch = isQuestionMatch,
        isType28 = isType28,
        lastType28Prompt = lastType28Prompt.safe(),
        type28AppId = type28AppId.safe(),
        specialText = specialText,
        pushPrefix = pushPrefix.safe(),
        sessionBusinessType = sessionBusinessType.safe(0),
        onlineMsgTime = when {
            (baseInfo.lastMsgId.isNotEmpty() && distance != -2 && TimeUtils.isBeforeYesterday(
                baseInfo.lastMessageTime
            )) -> {
                onlineMsgTime.safe()
            }

            (baseInfo.lastMsgId.isNotEmpty() && distance == -2 && TimeUtils.isBeforeFirstWeek(
                baseInfo.lastMessageTime
            ) == 0) -> {
                sevenDaysIn.safe()
            }

            (baseInfo.lastMsgId.isNotEmpty() && distance == -2 && TimeUtils.isBeforeFirstWeek(
                baseInfo.lastMessageTime
            ) == 1) -> {
                sevenDaysOut.safe()
            }

            else -> {
                ""
            }
        },
        officialOperation = officialOperation,
        cellTagUniformLabels = tagUniformLabels,
        textIconTag = textIconTag,
        sessionTagLogMap = sessionTagLogMap,
        fireIcon = fireIcon,
        fireIconDark = fireIconDark,
        fireSign = fireSign,
        avatarFrame = userAvatarFrame
    )

    companion object {
        const val Type = "u"

        @JvmStatic
        fun key(id: String) = SessionKey(Type, id)
    }
}

