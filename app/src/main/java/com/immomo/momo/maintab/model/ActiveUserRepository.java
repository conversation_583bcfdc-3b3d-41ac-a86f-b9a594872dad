package com.immomo.momo.maintab.model;

import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.momo.flashchat.contract.FlashChatConstants;
import com.immomo.momo.maintab.session2.data.database.SessionContentKt;
import com.immomo.momo.maintab.session2.data.manager.SessionManager;
import com.immomo.momo.maintab.session2.defs.ActiveUserContent;
import com.immomo.momo.maintab.session2.defs.ActiveUserSessionDefinition;
import com.immomo.momo.protocol.http.UserApi;
import com.immomo.momo.util.StringUtils;

/**
 * Created by huang.liang<PERSON>e on 2017/4/1.
 * <p>
 * Momo Tech 2011-2017 © All Rights Reserved.
 */

public class ActiveUserRepository implements IActiveUserRepository {
    @Override
    public SessionActiveUser getActiveUserSession(int totalSessionCount, int coreSessionCount) throws Exception {
        boolean checkFlashChat = FlashChatConstants.ActiveUser.needCheckFlashChat();
        int version = KV.getUserInt(SPKeys.User.ActiveUser.KEY_LAST_REFRESH_VERSION, 0);
        long lastUpdateTime = KV.getUserLong(SPKeys.User.ActiveUser.KEY_LAST_SESSION_TIME, 0L);

        SessionActiveUser sessionActiveUser = UserApi.getInstance().getActiveUserList(version, lastUpdateTime, checkFlashChat, totalSessionCount, coreSessionCount);
        KV.saveUserValue(SPKeys.User.ActiveUser.KEY_LAST_SESSION_TIME, sessionActiveUser.updateTime);
        FlashChatConstants.ActiveUser.saveActiveConfig(sessionActiveUser.getFlashChat());

//        SessionSoulMatch matchBean = sessionActiveUser.match;
//        if (matchBean != null) {
//            if (matchBean.isRed == 1) {
//                PreferenceUtil.saveUserPreference(SPKeys.System.AppMultiConfig.KEY_HISESSION_IS_SHOW_SIMILARITY_RED_POINT, true);
//            }
//        }

        handleList(sessionActiveUser, version);

        KV.saveUserValue(SPKeys.User.ActiveUser.KEY_LAST_REFRESH_ACTIVE_USER, System.currentTimeMillis());
        KV.saveUserValue(SPKeys.User.ActiveUser.KEY_REFRESH_ACTIVE_USER_CONFIG, sessionActiveUser.interval);
        KV.saveUserValue(SPKeys.User.ActiveUser.KEY_LAST_REFRESH_VERSION, sessionActiveUser.version);
        KV.saveUserValue(SPKeys.User.ActiveUser.KEY_ACTIVE_TITLE, sessionActiveUser.title);
        return sessionActiveUser;
    }

    private void handleList(SessionActiveUser sessionActiveUser, int version) {
        if (sessionActiveUser.isDataValid() && sessionActiveUser.userList.size() >= 3) {
            ActiveUserService.getInstance().saveList(sessionActiveUser.userList);

            SessionManager.get().syncSession(sessionActiveUser, true);
        } else if (version != sessionActiveUser.version) {
            clear();
        } else {
            long count = ActiveUserService.getInstance().getCount();
            // 小于三个就清空本地数据
            if (count < 3) {
                clear();
            } else {
                long updateTime = sessionActiveUser.updateTime * 1000;
                SessionManager.get().updateSessionForJava(
                        ActiveUserSessionDefinition.KEY_ACTIVE_USER,
                        true,
                        sessionEntity -> {
                            sessionEntity.setLastFetchTime(updateTime);
                            return true;
                        });
            }
        }
    }

    @Override
    public void inActiveOneUser(ActiveUser user) throws Exception {
        UserApi.getInstance().interactUser(user.getMomoid(), user.getLog_str());

        ActiveUserService.getInstance().delete(user);
        removeActiveUserInSession(user);
    }

    @Override
    public void removeActiveOneUser(ActiveUser user) throws Exception {
        UserApi.getInstance().removeActiveUser(user.getMomoid());

        ActiveUserService.getInstance().delete(user);
        removeActiveUserInSession(user);
    }

    private void removeActiveUserInSession(ActiveUser user) {
        String momoid = user.getMomoid();
        SessionManager.get().updateSessionForJava(
                ActiveUserSessionDefinition.KEY_ACTIVE_USER,
                false,
                sessionEntity -> {
                    ActiveUserContent content = ((ActiveUserContent) SessionContentKt.getContent(sessionEntity));
                    if (content == null || content.getActiveUsers() == null) return false;

                    content.getActiveUsers().removeIf(activeUser ->
                            StringUtils.equalsNonNull(activeUser.getMomoid(), momoid));

                    if (content.getActiveUsers().size() < 3) {
                        sessionEntity.setMarkAsDeleted(true);
                    }
                    return true;
                });
    }

    public void deleteActiveUserSession() {
        SessionManager.get().updateSessionForJava(
                ActiveUserSessionDefinition.KEY_ACTIVE_USER,
                false,
                sessionEntity -> {
                    sessionEntity.setMarkAsDeleted(true);
                    return true;
                });
    }

    @Override
    public boolean isCacheEmpty() {
        long count = ActiveUserService.getInstance().getCount();
        if (count == 0) {
            deleteActiveUserSession();
            return true;
        }
        return false;
    }

    @Override
    public void clear() {
        ActiveUserService.getInstance().deleteAll();
        deleteActiveUserSession();
    }

    @Override
    public void onLogout() {
        // default implementation ignored
    }
}
