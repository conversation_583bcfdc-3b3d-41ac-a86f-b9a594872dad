package com.immomo.momo.maintab.model;

import android.text.TextUtils;

import com.immomo.momo.service.bean.Message;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/5/17.
 */
public class AbsSession {
    /**
     * 统一令Paas的SessionType为-2,除了SessionProcessor有类型判断以匹配新sessionKey
     * 已经不再使用，仅作为区分session类型使用了
     */
    public final static int TYPE_PAAS = -2;
    /**
     * 类型：待删除的session，只用于session列表的刷新逻辑
     */
    public final static int TYPE_DELETE = -1;
    /**
     * 类型：普通聊天对话
     */
    public final static int TYPE_CHAT = 0;
    /**
     * 招呼会话
     */
    public final static int TYPE_SAYHI = 1;
    /**
     * 群会话
     */
    public final static int TYPE_GROUP = 2;
    /** 通讯录动态 4.10已弃用 */
    //	public final static int TYPE_CONTACTNOTICE = 5;
    /**
     * 好友距离提醒
     */
    public final static int TYPE_FRIENDDISTANCE = 8;
    /**
     * 讨论组
     */
    public final static int TYPE_DISCUSS = 6;
    /**
     * 日志类会话
     */
    public final static int TYPE_LOGGER = 7;
    /**
     * 跳转类型会话
     */
    public final static int TYPE_GOTO = 9;
    /**
     * @deprecated 与商户聊天已下线，不再维护
     */
    @Deprecated
    public final static int TYPE_COMMERCE = 10;
    /**
     * @deprecated 汇总所有用户与本商户的会话已下线，不再维护
     */
    @Deprecated
    public final static int TYPE_COMMERCE_GROUP = 11;
    /**
     * 聊天室
     */
    @Deprecated
    public final static int TYPE_CHATROOM = 12;
    /**
     * 通知中心
     */
    @Deprecated
    public final static int TYPE_FEEDNOTICE = 13;
    /**
     * 群动态
     */
    @Deprecated
    public final static int TYPE_GROUPACTION = 14;
    /**
     * 官方折叠消息
     */
    public final static int TYPE_FOLDER_OFFICIAL = 15;
    /**
     * 圈子通知
     */
    @Deprecated
    public final static int TYPE_FORUM_NOTICE = 16;
    /**
     * 点点折叠消息
     */
    public final static int TYPE_FOLDER_MATCH = 17;
    /**
     * 资料互赞
     */
    public final static int TYPE_PROFILE_LIKE = 18;
    /**
     * 好友提醒
     */
    public final static int TYPE_FRIEND_NOTICE = 19;
    /**
     * 活跃好友
     */
    public final static int TYPE_ACTIVE_USER = 20;
    /**
     * 快聊邀请
     */
    @Deprecated
    public static final int TYPE_QCHAT_INVITE = 21;
    /**
     * 聊天室常驻房间
     */
    public static final int TYPE_VCHAT_SUPER_ROOM = 22;
    /**
     * 合拍入口 已下线
     */
    public static final int TYPE_SOUL_MATCH = 23;
    /**
     * 闪聊
     */
    public static final int TYPE_FLASH_CHAT = 24;
    /**
     * 打出的招呼
     */
    public static final int TYPE_UNREPLY = 25;
    /**
     * 蒙面真心话
     */
    public static final int TYPE_MASK_CHAT = 26;

    /**
     * 文字匹配
     */
    public static final int TYPE_TEXT_CHAT = 27;

    /**
     * 保存最后的一条消息
     */
    protected Message lastMessage;

    /**
     * 收到的最后一条消息的id
     */
    protected String lastmsgId;

    public int type = TYPE_CHAT;

    @Nullable
    public Message getLastMessage() {
        return lastMessage;
    }

    public void setLastMessage(@Nullable Message newMsg) {
        if (newMsg == null) {
            this.lastMessage = null;
            this.lastmsgId = "";
        } else if (!newMsg.isImSpam()) {
            this.lastMessage = newMsg;
            this.lastmsgId = newMsg.msgId;
        }
    }

    public String getLastmsgId() {
        return lastmsgId;
    }

    public boolean isLastMsg(String msgId) {
        return TextUtils.equals(msgId, this.lastmsgId);
    }

    public void setLastmsgId(@Nullable String lastmsgId) {
        this.lastmsgId = lastmsgId;
    }

    @NonNull
    public Message onLastMessageEmpty() {
        Message lastMessage = new Message("");
        lastMessage.receive = true;

        lastMessage.contentType = Message.CONTENTTYPE_MESSAGE_TEXT;
        lastMessage.setContent("");
        lastMessage.setTimestamp(null);

        setLastMessage(lastMessage);

        return lastMessage;
    }
}
