package com.immomo.momo.maintab.sessionlist.util

import android.content.Intent
import com.immomo.mls.MLSBundleUtils
import com.immomo.momo.luaview.LuaViewActivity
import com.immomo.momo.setting.apt.SettingConfigV2Getter
import com.immomo.momo.setting.apt.SettingLuaConfigV2Getter
import com.immomo.momo.setting.bean.SettingUrlConfig.URL_SETTING_NOTICE
import android.content.Context
import com.immomo.molive.kotlin.extensions.isNotNullOrEmpty

object  SettingGotoHelper {
    @JvmStatic
    fun gotoNoticeSetting(context: Context) {
        if (SettingLuaConfigV2Getter.get().notice_setting_lua().isNotNullOrEmpty()) {
            val initData = MLSBundleUtils.createInitData(SettingLuaConfigV2Getter.get().notice_setting_lua())
            val intent = Intent(context, LuaViewActivity::class.java)
            intent.putExtras(MLSBundleUtils.createBundle(initData))
            context.startActivity(intent)
        } else{
            val initData = MLSBundleUtils.createInitData(URL_SETTING_NOTICE)
            val intent = Intent(context, LuaViewActivity::class.java)
            intent.putExtras(MLSBundleUtils.createBundle(initData))
            context.startActivity(intent)
        }
    }
}