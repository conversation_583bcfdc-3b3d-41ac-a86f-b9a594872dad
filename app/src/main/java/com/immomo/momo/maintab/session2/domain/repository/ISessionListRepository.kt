package com.immomo.momo.maintab.session2.domain.repository

import com.immomo.android.mm.kobalt.domain.repository.ReqParam
import com.immomo.momo.maintab.model.UserOnlineStatus
import com.immomo.momo.maintab.session2.data.manager.SessionMetadata
import com.immomo.momo.maintab.session2.domain.model.FoldNotificationBannersModel
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.maintab.session2.domain.model.SessionNoticeInfoModel
import com.immomo.momo.maintab.session2.domain.model.SessionPaginationModel
import com.immomo.momo.maintab.sessionlist.bean.SessionTopOperatorData
import kotlinx.coroutines.flow.Flow

class SessionListReqParam(
    override val reqType: ReqParam.Type = ReqParam.Type.UNDEFINED,
    val index: Int,
    val count: Int
) : ReqParam

interface ISessionListRepository {
    fun getSessionList(
        param: SessionListReqParam
    ): Flow<SessionPaginationModel>
    fun getAllUnreadCount(): Flow<Int>

    fun getBannerList(): Flow<FoldNotificationBannersModel>
    fun observeInteract(): Flow<SessionNoticeInfoModel>
    fun findAllSessions(size: Int, filter: (SessionMetadata) -> Boolean): List<SessionModel>
    suspend fun refreshSessionUserStatus(sessionKeyList: Set<String?>):Map<String, UserOnlineStatus>
    fun requestSessionTopOperator(map: Map<String, String>): Flow<SessionTopOperatorData>
    fun requestLocalSessionTopOperator(): Flow<SessionTopOperatorData>
}