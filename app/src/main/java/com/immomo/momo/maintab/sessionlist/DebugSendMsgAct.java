package com.immomo.momo.maintab.sessionlist;

import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.cosmos.mdlog.MDLog;
import com.immomo.LogTag;
import com.immomo.framework.base.BaseActivity;
import com.immomo.mmutil.task.ThreadUtils;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.message.helper.MessageHelper;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.service.sessions.MessageServiceHelper;

/**
 * Created by tangyuchun on 2018/8/24.
 */

public class DebugSendMsgAct extends BaseActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.act_debug_send_msg);
        findViewById(R.id.btn_ok).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                sendMsg();
            }
        });
    }

    private int msgCount = 0;
    private boolean isSending = false;

    private void sendMsg() {
        if (isSending) {
            Toast.makeText(thisActivity(), "正在发送 " + msgCount + " 条", Toast.LENGTH_SHORT).show();
            return;
        }
        isSending = true;
        final int count = Integer.valueOf(((EditText) findViewById(R.id.msg_count)).getText().toString());
        int userCount = Integer.valueOf(((EditText) findViewById(R.id.user_count)).getText().toString());
        final int interval = Integer.valueOf(((EditText) findViewById(R.id.msg_interval)).getText().toString());
        final String userId = ((EditText) findViewById(R.id.target_user_id)).getText().toString().trim();

        ThreadUtils.execute(ThreadUtils.TYPE_RIGHT_NOW, new Runnable() {
            @Override
            public void run() {
                User user = new User(userId);
                for (int i = 0; i < count; i++) {
                    String content = userId + "_" + i;
                    if (i == 0) {
                        content = "start";
                    } else if (i == count - 1) {
                        content = "end";
                    }
                    com.immomo.momo.service.bean.Message message = MessageHelper.getInstance().packetTextMessage(
                            content,
                            user,
                            null,
                            com.immomo.momo.service.bean.Message.CHATTYPE_USER
                    );
                    MessageServiceHelper.getInstance().saveSent(message);
                    MomoKit.getApp().sendMessage(message);
                    msgCount++;

                    try {
                        Thread.sleep(interval);
                    } catch (InterruptedException e) {
                        MDLog.printErrStackTrace(LogTag.COMMON, e);
                    }
                    if (i % 100 == 0) {
                        myHandler.sendEmptyMessage(0);
                    }
                }
                isSending = false;
                myHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        ((TextView) findViewById(R.id.debug_info)).setText("发送完毕 " + msgCount + " 条");
                    }
                });
            }
        });
    }

    private Handler myHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            ((TextView) findViewById(R.id.debug_info)).setText("正在发送 " + msgCount + " 条");
        }
    };
}
