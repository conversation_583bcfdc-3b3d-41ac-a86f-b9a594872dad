package com.immomo.momo.maintab.session2.data.response

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.momo.maintab.session2.domain.model.FoldNotificationBannerModel
import com.immomo.momo.maintab.session2.domain.model.FoldNotificationBannersModel

/**
 * CREATED BY liu.chong
 * AT 2022/3/9
 */
data class FoldNotificationBannerResponse(
    @SerializedName("lists") @Expose val lists: List<FoldNotificationBannerItemResponse>
)

data class FoldNotificationBannerItemResponse(
    @SerializedName("id") @Expose val id: String?,
    @SerializedName("pic") @Expose val url: String?,
    @SerializedName("goto") @Expose val action: String?
)

fun FoldNotificationBannerResponse.toModel(): FoldNotificationBannersModel {
    return FoldNotificationBannersModel(
        lists.filter { it.id.isNullOrBlank().not() && it.url.isNullOrBlank().not() }
            .map { it.toModel() }
    )
}

fun FoldNotificationBannerItemResponse.toModel(): FoldNotificationBannerModel {
    return FoldNotificationBannerModel(
        id = id.safe(),
        url = url.safe(),
        action = action.safe()
    )
}
