package com.immomo.momo.maintab.sessionlist;

import android.app.Activity;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;

import com.immomo.momo.flashchat.contract.ISessionListEnterBar;
import com.immomo.momo.maintab.session2.domain.model.SessionModel;
import com.immomo.momo.maintab.sessionlist.bean.MaskChatData;
import com.immomo.momo.maintab.sessionlist.bean.MaskChatQuitData;
import com.immomo.momo.maintab.sessionlist.bean.SessionSpaceBean;
import com.immomo.momo.message.view.DragBubbleView;
import com.immomo.momo.sessionnotice.bean.PushSwitchTipsInfo;

import java.util.List;

/**
 * Created by tanjie on 5/7/16.
 */
public interface ISessionListView2 extends ISessionListEnterBar {
    /**
     * MomoKit.getApp().dispatchMessage 发送该类型消息，sessionId=单聊：u_+momoid；群聊：g_groupid；多人：d_discussid；其他不变
     */
    String Action_SessionChanged = "action.sessionchanged";
    String Action_VideoChat = "action.session.videochat";
    String Action_SessionGotPresent = "action.sessiongotpresent";
    String Action_SyncFinished = "action.syncfinished";
    String Key_SessionId = "sessionid";
    String Key_ChatId = "chatId";
    String Key_SessionType = "sessiontype";
    String Key_SessionFromType = "from_hi_type";
    String Key_SessionBackFromSayHi = "session_back_from_hi";
    String Action_SessionChangedFromVChat = "action.sessionchanged.vchat";
    String Key_SayHiSessionLikeType = "sayhisessionliketype"; // 打招呼的喜欢类型
    String Key_SayHiSessioncardType = "sayhisessioncardtype"; // 打招呼的划卡类型

    Activity getBaseActivity();

    void removeTips(int tipsID);

    void showBuyVipDialog();

    void hideLoadingTip();

    DragBubbleView getDragView();

    void clearBubble();

    void resetBubble();

    void onDragBubbleOnTouch();

    void disappearBubble(View bubbleView);

    void hideBottomBubble();

    void showBottomBubble(int unreadMessageCount, boolean showTip);

    void showVideoTagView();

    void hideVideoTagView();

    boolean isForegroundNow();

    void updateContactUnreadCount(int count);

    boolean isDestroyed();

    boolean isForeground();

    void onGetPushSwitchTipsInfoSuccess(@NonNull PushSwitchTipsInfo tipsInfo);

    void onGetPushSwitchTipsInfoFail();

    void onGetPushSwitchDialogInfoSuccess(@NonNull PushSwitchTipsInfo tipsInfo);

    void hidePushGuideTips();

    void clearAllUnread();

    void clearDragBubble();

    SessionModel getSessionModelInUi(String sessionKey);

    List<SessionModel> getVisibleChatSessionModel();

    void showSpaceView(SessionSpaceBean bean);

    void showSearchMenu();

    void showOrHideBottomTips(Boolean isShow);

    void checkEcologicalAlert();

    Fragment getViewParentFragment();
}
