package com.immomo.momo.maintab.session2.domain.model.type

import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.SessionModel

data class GotoSessionModel(
    override val baseInfo: BaseSessionInfo,
    val action: String,
    val title: String,
    val text: String,
    val icon: String,
    val source: Int,
    val sourceStr:String,
    val hasRedPacketTxt: Boolean
) : SessionModel {
    val businessId by lazy { baseInfo.sessionId.replace("gotochat", "") }

    fun copyWith(unreadMessageCount: Int) = copy(
        baseInfo = baseInfo.copy(unreadMessageCount = unreadMessageCount)
    )
}