package com.immomo.momo.maintab.session2.presentation.itemmodel

import android.content.Intent
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.immomo.android.mm.cement2.AsyncCementModel
import com.immomo.android.mm.cement2.CementViewHolder
import com.immomo.android.mm.cement2.IViewHolderCreator
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.utils.UIUtils
import com.immomo.framework.view.widget.LinesShimmerTextView
import com.immomo.molive.gui.common.view.DragBubbleView
import com.immomo.momo.R
import com.immomo.momo.homepage.view.FlipTextView
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.maintab.session2.domain.model.type.SpamSessionModel
import com.immomo.momo.maintab.session2.presentation.itemmodel.events.SessionDraggableViewTouchListener
import com.immomo.momo.maintab.sessionlist.util.SessionHelper
import com.immomo.momo.message.activity.SpamSessionListActivity
import com.immomo.momo.util.DateUtil
import java.util.*

interface SpamSessionOnLongClickListener {
    fun onLongClicked(
        view: View,
        viewHolder: SpamSessionItemModel.ViewHolder,
        session: SessionModel,
        adapterPosition: Int
    )
}

class SpamSessionItemModel(
    val info: SpamSessionModel,
    val onDrag: SessionDraggableViewTouchListener?,
    private val onItemLongClicked: SpamSessionOnLongClickListener? = null
    ) :
    AsyncCementModel<SpamSessionModel, SpamSessionItemModel.ViewHolder>(info) {

    init {
        id(info.uniqueId)
    }


    override val layoutRes: Int
        get() = R.layout.item_spm_session

    override val viewHolderCreator: IViewHolderCreator<ViewHolder>
        get() = object : IViewHolderCreator<ViewHolder> {
            override fun create(view: View): ViewHolder =
                ViewHolder(view)
        }

    override fun bindData(holder: ViewHolder) {
        super.bindData(holder)
        var desc = "暂无异常用户"
        if (info.desc.isNotEmpty()) {
            desc = info.desc
        }

        holder.viewDes.setText(
            desc,
            UIUtils.getColor(holder.itemView.context, R.color.color_aaaaaa_to_40fff),
            13f
        )

        holder.statusTextView_new.visibility = View.GONE

        holder.itemView.setOnClickListener { it ->
            SessionHelper.SessionLogParams(
                "spamwithu", info.baseInfo.unreadMessageCount, holder.adapterPosition, "0",
                false, null, 0, 0, "", "", holder.getTimeStr(), false
            ).also {
                SessionHelper.Log.logSessionClick(it)
            }
            it.context.startActivity(Intent(it.context, SpamSessionListActivity::class.java))
        }
        holder.bindLongClickEvents()

        holder.statusTextView_new.setOnTouchListener { v, event ->
            onDrag?.onTouch(
                v,
                event,
                holder.adapterPosition,
                holder.statusTextView_new,
                DragBubbleView.DRAG_FROM_LIST
            ) ?: false
        }

        ImageLoader.load(R.drawable.ic_spam).into(holder.imgAvatar)

        holder.tvTitle.setTextColor(
            UIUtils.getColor(
                if (com.immomo.momo.util.MomoKit.isDarkMode(holder.itemView.context))
                    R.color.color_80fff else R.color.color_text_3b3b3b
            )
        )
    }

    private fun fillTimeStamp(holder: ViewHolder) {
        var timeText: String? = ""
        if (info.baseInfo.lastMsgId.isNotEmpty()) {
            timeText = DateUtil.getTimeLineStringStyle3(Date(info.baseInfo.lastMessageTime))
        }
        holder.tvTimeStamp.text = timeText
    }

    private fun ViewHolder.bindLongClickEvents() {
        if (onItemLongClicked != null) {
            itemView.setOnLongClickListener {
                onItemLongClicked.onLongClicked(it, this, info, adapterPosition)
                true
            }
        } else {
            itemView.setOnLongClickListener(null)
        }
    }


    class ViewHolder(itemView: View) : CementViewHolder(itemView) {
        val tvTitle: LinesShimmerTextView = itemView.findViewById(R.id.tv_title)
        val viewDes: FlipTextView = itemView.findViewById(R.id.view_desc)
        val tvTimeStamp: TextView = itemView.findViewById(R.id.tv_timestamp)
        val statusTextView_new: TextView = itemView.findViewById(R.id.chatlist_item_tv_status_new)
        val imgAvatar: ImageView = itemView.findViewById(R.id.iv_avatar)

        init {
            viewDes.setTxtGravity(Gravity.LEFT)
            viewDes.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13f)
            viewDes.setDelayTime(1000L)
        }


        fun getTimeStr(): String {
            return if (tvTimeStamp.visibility == View.VISIBLE) {
                tvTimeStamp.text.toString()
            } else ""
        }
    }
}