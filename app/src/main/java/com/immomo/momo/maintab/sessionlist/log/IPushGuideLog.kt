package com.immomo.momo.maintab.sessionlist.log

import com.immomo.lcapt.evlog.anno.ClickPoint
import com.immomo.lcapt.evlog.anno.ExposurePoint
import com.immomo.lcapt.evlog.anno.ParamMap

interface IPushGuideLog {
    @ExposurePoint(page = "growth.pushsetting", action = "window.push_all", requireId = "14936")
    fun onShowDialog()

    //button:open去开启,close暂不开启,type:all全部,just_look_at_me只看与我有关的消息
    @ClickPoint(page = "growth.pushsetting", action = "window.push_button", requireId = "14934")
    fun onItemClick(@ParamMap params: Map<String, String?>)
}