package com.immomo.momo.maintab.session2.data.manager

import android.util.Log
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.cosmos.mdlog.MDLog
import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.moarch.account.AccountKit
import com.immomo.momo.maintab.model.AbsSession
import com.immomo.momo.maintab.session2.HasSessionKey
import com.immomo.momo.maintab.session2.ISessionListLog
import com.immomo.momo.maintab.session2.SessionDefinitionManager
import com.immomo.momo.maintab.session2.SessionUpdateBundle
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.SessionService
import com.immomo.momo.maintab.session2.data.database.SessionSnapshotManager
import com.immomo.momo.maintab.session2.data.database.checkInCache
import com.immomo.momo.maintab.session2.data.database.isInCache
import com.immomo.momo.maintab.session2.data.database.removeInCache
import com.immomo.momo.maintab.session2.data.database.setInCache
import com.immomo.momo.maintab.session2.data.mapper.toMetadata
import com.immomo.momo.maintab.session2.data.mapper.toModel
import com.immomo.momo.maintab.session2.data.util.SessionTabTestHelper
import com.immomo.momo.maintab.session2.defs.ActiveUserSessionDefinition
import com.immomo.momo.maintab.session2.defs.PaasSessionDefinition
import com.immomo.momo.maintab.session2.defs.UserChatSessionDefinition
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.maintab.session2.utils.SessionFilterHelper
import com.immomo.momo.maintab.sessionlist.SessionStickyHelper
import com.immomo.momo.maintab.sessionlist.bean.SessionCountBean
import com.immomo.momo.maskchat.paas.MaskCons
import com.immomo.momo.mulog.pair.IMUPair
import com.immomo.momo.mulog.pair.MUPairItem
import com.immomo.momo.protocol.imjson.event.IMEventReporter
import com.immomo.momo.protocol.imjson.event.IMOfflineEvent
import com.immomo.momo.protocol.imjson.log.IMLocalLogType
import com.immomo.momo.protocol.imjson.log.IMLocalLogger
import com.immomo.momo.protocol.imjson.util.Debugger
import com.immomo.momo.service.bean.FolderType
import com.immomo.momo.service.bean.Session
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.BroadcastChannel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.ClosedReceiveChannelException
import kotlinx.coroutines.channels.SendChannel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.sample
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.selects.select
import org.json.JSONObject
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.atomic.AtomicInteger
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine


private fun reportLog(id: String, msg: String) {
    Log.d("SessionManager", "[Global]$id: $msg")
    IMLocalLogger.log(IMLocalLogType.SessionManager, "[Global]$id: $msg")
}

internal fun reportErrors(id: String, e: Exception?, vararg params: IMUPair) {
    Log.e("SessionManager", "$id: ${params.toList().joinToString("\n") { it.value.toString() }}", e)
    IMEventReporter.eventOfflineTime(
        IMOfflineEvent.SESSION_MANAGER,
        MUPairItem.errorMsg(e?.stackTrace?.joinToString("\n") { it.toString() } ?: ""),
        MUPairItem.info(
            "online=${AccountKit.getAccountManager().isOnline}," +
                    " guestOnline=${AccountKit.getAccountManager().isGuestOnline}, " + e?.message
        ),
        MUPairItem.createTime(System.nanoTime()),
        MUPairItem.host(Thread.currentThread().name),
        MUPairItem.id(id),
        *params
    )
}

data class SessionKey(val type: String, val id: String) {
    val value = "${type}_$id"

    companion object {
        val INVALID = SessionKey("EMPTY", "EMPTY")

        const val KEY_GIFT_HI = "giftsayhi"

        const val KEY_UNIVERSE = "universe" // 小宇宙

        @JvmStatic
        fun fromString(from: String): SessionKey {
            val splits = from.split("_")
            if (splits.size != 2) {
                val e = IllegalArgumentException("can not parse session key $from")
                reportErrors(from, e)
                if (Debugger.isDebuggable()) {
                    GlobalScope.launch(MMDispatchers.Main) {
                        throw e
                    }
                }
                return INVALID
            }
            return SessionKey(splits[0], from.removePrefix(splits[0] + "_"))
        }

        @JvmStatic
        fun fromCompatible(from: String): SessionKey {
            return when {
                from == Session.ID.SayhiSession -> SessionKey("sayhi", from)
                from == Session.ID.GiftSayhiSession -> SessionKey(KEY_GIFT_HI, from)
                from == Session.ID.Debugger -> SessionKey("debugger", from)
                from == Session.ID.ActiveUser -> SessionKey("activeUser", from)
                from == Session.ID.FLASH_CHAT -> SessionKey("goto", from)
                from == Session.ID.TEXT_CHAT -> SessionKey("goto", from)
                from == Session.ID.FolderOfficial -> SessionKey("fold", from)
                from == Session.ID.HI_UNREPLY -> SessionKey("fold", from)
                from == Session.ID.HEPAI -> SessionKey("fold", from)
                from == Session.ID.SPAM -> SessionKey("fold", from)
                from == Session.ID.GAME_BOX -> SessionKey("fold", from)
                from == Session.ID.UniverseSession -> SessionKey(KEY_UNIVERSE, from)
                from.startsWith("u_") -> fromString(from)
                from.startsWith("g_") -> fromString(from)
                from.startsWith("d_") -> fromString(from)
                from.startsWith("v_") -> fromString(from)
                from.startsWith("l_") -> fromString(from)
                else -> fromString(from)
            }
        }
    }

    fun isMaskChat(): Boolean {
        return value.startsWith("mask_") || PaasSessionDefinition.businessFromSessionId(id) == MaskCons.Paas.BID
    }


    override fun toString(): String {
        return "SessionKey=$value"
    }

    override fun hashCode(): Int {
        return value.hashCode()
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as SessionKey

        if (value != other.value) return false

        return true
    }
}

/**
 * 二级缓存
 * 内存：
 * 数据库：
 */
data class SessionUpdater(
    val sessionKey: SessionKey,
    val block: (SessionEntity) -> Unit
)

sealed class SessionOp {
    private val idx = counter++

    class Insert(val pos: Int, val models: List<SessionModel>) : SessionOp()
    class Remove(val pos: Int, val count: Int) : SessionOp()
    class Move(val from: Int, val to: Int) : SessionOp()
    class Update(val pos: Int, val models: List<SessionModel>) : SessionOp()

    class All(val models: List<SessionModel>, val total: Int) : SessionOp()

    override fun toString(): String {
        return "[$idx]SessionOp.${this::class.simpleName}"
    }

    companion object {
        private var counter = 0
    }
}

data class SessionMetadata(
    val sessionKey: String,
    val sessionType: String,
    val sessionId: String,
    var foldType: Int?,
    var foldV3: Int,
    @Deprecated("已下线")
    var recommendTime: Long,
    var orderId: Long,
    var markedAsDeleted: Boolean,
    var lastMsgUpdateTime: Long,
    var unreadCount: Int,
    var silenceCount:Int
) {
    //判断是否可以仅通知model更新，因为涉及可见性变化的情况，比如foldType，需要重新加载
    fun canOnlyUpdate(model: SessionModel): Boolean {
        return foldType == model.baseInfo.foldType
                && model.baseInfo.foldType == FolderType.Default
                && markedAsDeleted == model.baseInfo.markedAsDeleted
                && !model.baseInfo.markedAsDeleted
    }

    fun with(model: SessionModel) {
        foldType = model.baseInfo.foldType
        foldV3 = model.baseInfo.foldTypeV3
        recommendTime = model.baseInfo.recommendTime
        orderId = model.baseInfo.orderId
        markedAsDeleted = model.baseInfo.markedAsDeleted
        lastMsgUpdateTime = model.baseInfo.lastMessageTime
        unreadCount = model.baseInfo.unreadMessageCount
        silenceCount = model.baseInfo.silentMessageCount
    }
}

interface SessionManagerLifecycle {
    fun onEvent(sessionManager: SessionManager, event: Event)

    enum class Event {
        Prepared, Inited, Reset
    }
}

class SessionManangerResetException(message: String?) : CancellationException(message)

@OptIn(ExperimentalCoroutinesApi::class, FlowPreview::class)
class SessionManager(val uid: String) {
    companion object {
        private const val threadName = "__SessionManager__"
        private val flushDispatcher by lazy {
            val counter = AtomicInteger(0)
            Executors.newFixedThreadPool(2) {
                Thread(it, threadName + counter.incrementAndGet()).also { thread ->
                    thread.priority = Thread.NORM_PRIORITY
                }
            }.asCoroutineDispatcher()
        }

        private const val OPENING = 1
        private const val RUNNING = 2
        private const val TERMINATED = 3

        private const val UNKNOWN_USER = "__UNKNOWN_USER__"

        internal var currentUid = UNKNOWN_USER
            private set
        private val managerCache = mutableMapOf<String, SessionManager>()

        private fun getDelegate(uid: String): SessionManager =
            managerCache.getOrPut(uid) { SessionManager(uid) }

        private fun getDelegateOrNull(uid: String): SessionManager? =
            managerCache[uid]

        @Synchronized
        @JvmStatic
        fun onLogin(uid: String?) {
            if (uid == null) return

            reportLog("onLogin", "$uid")
            currentUid = uid
            getDelegate(uid).prepare()
        }

        @Synchronized
        @JvmStatic
        fun onOnline(uid: String?) {
            if (uid == null) return

            reportLog("onOnline", "$uid")
            currentUid = uid
            getDelegate(uid).prepare()
        }

        @Synchronized
        @JvmStatic
        fun onOffline(uid: String?) {
            if (uid == null) return

            reportLog("onOffline", "$uid")
            getDelegateOrNull(uid)?.reset()
        }

        @Synchronized
        @JvmStatic
        fun onLogout(uid: String?) {
            if (uid == null) return

            reportLog("onLogout", "$uid")
            getDelegateOrNull(uid)?.reset()
            managerCache.remove(uid)
        }


        @Synchronized
        @JvmStatic
        fun get(): SessionManager = getDelegate(currentUid)

        fun getInfoCache(): SessionInfoCache = get().infoCache

        @JvmStatic
        fun getService(): SessionService = get().sessionService

        fun getMessageInfoCache() = get().messageInfoCache
    }

    private fun reportLog(id: String, msg: String) {
        Log.d("SessionManager", "[@${this.hashCode()}]$id: $msg")
        IMLocalLogger.log(IMLocalLogType.SessionManager, "[@${this.hashCode()}]$id: $msg")
    }

    private fun reportErrors(id: String, e: Exception) {
        if (e is SessionManangerResetException || e is ClosedReceiveChannelException) return
        reportErrors(
            id, e,
            MUPairItem.content(
                "user=${uid}, state=$state, metadataMap=" +
                        metadataMap.toString()
            )
        )
    }

    val sessionScope = CoroutineScope(SupervisorJob() + flushDispatcher)
    private val sessionDbSupervisorJob = SupervisorJob()

    val topFoldHelper = SessionTabTestHelper()

    val sessionService = SessionService()
    val infoCache = SessionInfoCache(this)
    val messageInfoCache = MessageInfoCache().also {
        it.register(UnreadCountMessageInterceptor(sessionService))
        it.register(LastUnreadMessageInterceptor())
    }

    private val sessionEntityCache = ConcurrentHashMap<String, SessionEntity>()

    private val metadataMap = ConcurrentHashMap<String, SessionMetadata>()
    private val descendingMetadataList = mutableListOf<SessionMetadata>()
    private var minOrderId = Long.MAX_VALUE

    private fun updateMetadata(
        key: SessionKey,
        reorder: Boolean = true,
        block: SessionMetadata.() -> Unit
    ) {
        metadataMap.getOrPut(key.value) {
            SessionMetadata(
                key.value, key.type, key.id, 0, 0, 0, 0,
                false, 0L, 0,0
            )
        }.block()
        if (reorder) {
            descendingMetadataList.clear()
            descendingMetadataList.addAll(metadataMap.values.sortedByDescending { it.orderId })
        }
        minOrderId = descendingMetadataList.lastOrNull()?.orderId ?: minOrderId
    }

    private var sessionModelRequiredCount = 30
    private val sessionModelsInUI = mutableListOf<SessionModel>()

    private val sessionDbChannel = Channel<SessionDbAction>(capacity = Channel.UNLIMITED)

    private val sessionEntityReadChannel =
        Channel<SessionEntityAction>(capacity = Channel.UNLIMITED)
    private val sessionEntityWriteChannel =
        Channel<SessionEntityAction>(capacity = Channel.UNLIMITED)

    private val sessionModelQuerySamplingChannel =
        BroadcastChannel<SessionModelAction>(capacity = Channel.CONFLATED)
    private val sessionModelReadChannel =
        Channel<SessionModelAction>(capacity = Channel.UNLIMITED)
    private val sessionModelWriteChannel =
        Channel<SessionModelAction>(capacity = Channel.UNLIMITED)

    private val sessionOpFlow = SessionOpFlow()

    @Deprecated("upgrade to sharedFlow when upgrading coroutine >= 1.4")
    private val sessionChannel = BroadcastChannel<SessionOp>(64)

    @Deprecated("upgrade to sharedFlow when upgrading coroutine >= 1.4")
    private val sharedSessionChannel = BroadcastChannel<SessionModel>(64)

    @Deprecated("upgrade to sharedFlow when upgrading coroutine >= 1.4")
    val sessionFlow: Flow<SessionOp>
        get() = sessionChannel.asFlow()

    @Deprecated("upgrade to sharedFlow when upgrading coroutine >= 1.4")
    private val sharedSessionFlow: Flow<SessionModel>
        get() = sharedSessionChannel.asFlow().distinctUntilChanged()

    private var state = AtomicInteger(OPENING)
    private var initJob: Job? = null
    private val isInitJobFinished get() = initJob?.isCompleted == true

    //<editor-fold desc="State and Init">
    private val lifecycleListeners = mutableListOf<SessionManagerLifecycle>()

    private fun notifyLifecycle(event: SessionManagerLifecycle.Event) {
        lifecycleListeners.onEach { it.onEvent(this, event) }
    }

    fun addLifecycleListener(listener: SessionManagerLifecycle) {
        if (listener in lifecycleListeners) return
        lifecycleListeners.add(listener)
    }

    fun removeLifecycleListener(listener: SessionManagerLifecycle) {
        lifecycleListeners.remove(listener)
    }

    fun isRunning() = state.get() == RUNNING

    @Synchronized
    fun prepare() {
        state.set(OPENING)

        notifyLifecycle(SessionManagerLifecycle.Event.Prepared)
        reportLog("prepare", "$uid-$state")
    }

    @Synchronized
    fun init() {
        if (state.get() == TERMINATED || initJob != null) return

        reportLog("init", "$uid-$state")
        ISessionListLog.loadStart()
        initJob = sessionScope.launch(MMDispatchers.Message) {
            try {
                tryMarkFoldSession()
                loadAllSessionKeys()
                initTopFoldInfo()
                setupSessionQueue()

                state.set(RUNNING)
                notifyLifecycle(SessionManagerLifecycle.Event.Inited)
                reportLog("init-success", "$uid-$state")

                ISessionListLog.loadEnd(metadataMap.values.size)
            } catch (e: Exception) {
                reportErrors("launch", e)
            }
        }
    }

    private fun initTopFoldInfo() {
        metadataMap.count { entry ->
            topFoldHelper.isTopFoldSession(
                entry.value.foldV3,
                entry.value.sessionType,
                entry.value.sessionId
            )
        }
    }

    private fun tryMarkFoldSession() {
        topFoldHelper.tryMarkTopFoldSession(sessionService)
    }

    @Synchronized
    fun reset() {
        if (state.getAndSet(TERMINATED) == TERMINATED) return

        notifyLifecycle(SessionManagerLifecycle.Event.Reset)
        lifecycleListeners.clear()
        reportLog("reset", "$uid-$state")

        infoCache.reset()
        messageInfoCache.reset()

        sessionDbSupervisorJob.cancel(SessionManangerResetException("cancel by reset"))
        sessionScope.cancel(SessionManangerResetException("cancel by reset"))
    }

    @Synchronized
    fun waitForInit() = runBlocking {
        val job = initJob
        requireNotNull(job) {
            reportErrors("launch", Exception("wait for init failed"))
            "wait for init failed"
        }
        job.join()
    }

    fun dumpState(extra: String) {
        val stackTraces = Thread.getAllStackTraces().mapNotNull { entry ->
            if (entry.key.name.startsWith("MMT")) {
                "${entry.key.name} -> ${entry.value.joinToString("\n") { it.toString() }}"
            } else null
        }.filter { it.contains("MMT4") || it.contains("com.immomo") }.joinToString("\n")

        reportErrors(
            "emptyList", null, MUPairItem.content(
                """
Uid: $uid
State: $state
InitJob: $initJob
ChannelState: 
sessionDbChannel=$sessionDbChannel
sessionEntityReadChannel=$sessionEntityReadChannel
sessionEntityWriteChannel=$sessionEntityWriteChannel
sessionModelQuerySamplingChannel=$sessionModelQuerySamplingChannel
sessionModelReadChannel=$sessionModelReadChannel
sessionModelWriteChannel=$sessionModelWriteChannel
sessionOpFlow=$sessionOpFlow
sessionChannel=$sessionChannel
sharedSessionChannel=$sharedSessionChannel
------------------------------------------
metadataMap=$metadataMap
------------------------------------------
$extra
------------------------------------------
$stackTraces
        """.trimIndent()
            )
        )
    }
    //</editor-fold>

    private fun loadAllSessionKeys() {
        sessionService.loadAllKeys().forEach { (sessionKey, metadata) ->
            metadataMap[sessionKey] = metadata
            minOrderId = minOrderId.coerceAtMost(metadata.orderId)
        }
        descendingMetadataList.clear()
        descendingMetadataList.addAll(metadataMap.values.sortedByDescending { it.orderId })

    }

    private fun <E> SendChannel<E>.safeOffer(element: E) =
        try {
            if (offer(element)) null else IllegalStateException("offering $element failed")
        } catch (e: Exception) {
            e
        }.also { e ->
            if (e != null) {
                reportErrors(
                    "offerError", e,
                    MUPairItem.content(
                        """
Uid: $uid
State: $state
InitJob: $initJob
ChannelState: 
sessionDbChannel=$sessionDbChannel
sessionEntityReadChannel=$sessionEntityReadChannel
sessionEntityWriteChannel=$sessionEntityWriteChannel
sessionModelQuerySamplingChannel=$sessionModelQuerySamplingChannel
sessionModelReadChannel=$sessionModelReadChannel
sessionModelWriteChannel=$sessionModelWriteChannel
sessionChannel=$sessionChannel
sharedSessionChannel=$sharedSessionChannel
------------------------------------------
metadataMap=${metadataMap.size}
"""
                    )
                )
            }
        } == null

    private fun setupSessionQueue() {
        setUpEntityDbQueue()
        setUpEntityActionQueue()
        setupModelActionQueue()
    }

    private fun closeChannels() {
        sessionDbChannel.close()

        sessionEntityReadChannel.close()
        sessionEntityWriteChannel.close()

        sessionModelQuerySamplingChannel.close()
        sessionModelReadChannel.close()
        sessionModelWriteChannel.close()
    }

    //<editor-fold desc="SessionDbActions">
    sealed class SessionDbAction(val type: String, val entity: SessionEntity) {
        private val idx = counter++

        class Delete(entity: SessionEntity) : SessionDbAction("delete", entity)

        class Update(entity: SessionEntity) : SessionDbAction("update", entity)

        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (other !is SessionDbAction) return false

            if (type != other.type) return false
            if (entity != other.entity) return false

            return true
        }

        override fun hashCode(): Int {
            var result = type.hashCode()
            result = 31 * result + entity.hashCode()
            return result
        }

        override fun toString(): String {
            return "[$idx]SessionDbAction(type='$type', entity=${entity.sessionKey})"
        }

        companion object {
            private var counter = 0
        }
    }

    private fun setUpEntityDbQueue() {
        val bufferedDbAction = mutableListOf<SessionDbAction>()
        var bufferedDbJob: Job? = null

        sessionScope.launch {
            for (action in sessionDbChannel) {
                if (bufferedDbAction.contains(action)) {
                    Log.d("SessionManager", "skipping $action")
                    continue
                }
                bufferedDbAction.add(action)
                bufferedDbJob?.cancel()

                bufferedDbJob = async {
                    delay(200)
                    val copied = ArrayList(bufferedDbAction)
                    bufferedDbAction.clear()

                    handleEntityDbActionAsync(copied)
                }
            }
        }
    }

    private fun CoroutineScope.handleEntityDbActionAsync(actions: List<SessionDbAction>) =
        async(coroutineContext + sessionDbSupervisorJob + MMDispatchers.User) {
            if (state.get() == TERMINATED) return@async

            actions.forEach { action ->
                try {
                    when (action) {
                        is SessionDbAction.Delete -> {
                            sessionService.deleteSession(action.entity)
                        }

                        is SessionDbAction.Update -> {
                            sessionService.saveSession(action.entity)
                        }
                    }
                } catch (e: Exception) {
                    reportErrors("sessionDb", e)
                }
            }
        }

    private fun offerToDbQueue(session: SessionEntity) {
        if (session.isMarkAsDeleted && session.isInCache(SessionEntity.CACHE_DATABASE)) {
            sessionDbChannel.safeOffer(SessionDbAction.Delete(session))
            session.removeInCache(SessionEntity.CACHE_DATABASE)
        } else if (!session.isMarkAsDeleted) {
            sessionDbChannel.safeOffer(SessionDbAction.Update(session))
            session.setInCache(SessionEntity.CACHE_DATABASE)
        }
    }
    //</editor-fold>

    //<editor-fold desc="SessionEntityAction">
    sealed class SessionEntityAction {
        protected val idx = counter++

        protected val __source =
            if (Debugger.isDebuggable()) Exception()
                .stackTrace.map { it.toString() }
                .filter { !it.contains("<init>") && !it.contains("app_debug") }
                .joinToString("\n ")
            else null

        class Query(
            val sessionKeys: List<String>,
            val size: Int,
            val skipFoldCheck: Boolean = false,
            //TODO: handle on error
            val onFetched: ((List<SessionModel>) -> Boolean)? = null
        ) : SessionEntityAction() {
            override fun toString(): String {
                return "[$idx]Entity:Query: keys=$sessionKeys, size=$size, skipFoldCheck=$skipFoldCheck, onFetched=$onFetched"
            }
        }

        class Update(
            val sessionKey: SessionKey,
            val createWhenAbsent: Boolean,
            val block: suspend CoroutineScope.(SessionEntity) -> Boolean
        ) : SessionEntityAction() {
            override fun toString(): String {
                return "[$idx]Entity:Update: key=$sessionKey, createWhen=$createWhenAbsent, source=$__source"
            }
        }

        companion object {
            private var counter = 0
        }
    }

    private fun setUpEntityActionQueue() {
        sessionScope.launch {
            try {
                while (isActive) {
                    select<Unit> {
                        MDLog.d(
                            "yeyc---",
                            "sessionEntityReadChannel-----onReceive---111111---" + sessionEntityWriteChannel.toString()
                        )
                        sessionEntityReadChannel.onReceive {
                            try {
                                handleSessionEntityAction(it)
                            } catch (e: Exception) {
                                reportErrors("sessionEntityQueue-R", e)
                            }
                        }
                        sessionEntityWriteChannel.onReceive {
                            try {
                                MDLog.d(
                                    "yeyc---",
                                    "sessionEntityWriteChannel-----onReceive----22222----" + it.toString()
                                )
                                handleSessionEntityAction(it)
                            } catch (e: Exception) {
                                reportErrors("sessionEntityQueue-W", e)
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                reportErrors("sessionEntityQueue", e)
            } finally {
                closeChannels()
            }
        }
    }

    private inline fun doSessionEntityUpdate(
        session: SessionEntity,
        runUpdateBlock: Boolean = true,
        updateBlock: () -> Boolean = { false }
    ): Boolean {
        val changed = if (runUpdateBlock) {
            val quickSnapshot = SessionSnapshotManager.takeQuickSnapshot(session)
            val snapshot = SessionSnapshotManager.takeSnapshot(session)

            val isChanged = updateBlock()
                    && quickSnapshot != SessionSnapshotManager.takeQuickSnapshot(session)
            val newSnapshot = SessionSnapshotManager.takeSnapshot(session)
            val compareResult = SessionSnapshotManager.compare(snapshot, newSnapshot)
            if (compareResult != null) {
                Log.d("SessionManager", "${session.sessionKey}: $compareResult")
                if (isChanged != compareResult.changed) {
                    //与预期不符，直接crash
                    sessionScope.launch(MMDispatchers.Main) {
                        throw IllegalStateException(
                            "${session.sessionKey}: snapshot detected data changed=$compareResult," +
                                    " while program declared changed=$isChanged"
                        )
                    }
                }
            }
            isChanged
        } else false
        session.checkInCache(SessionEntity.CACHE_MEMORY) {
            sessionEntityCache.put(session.sessionKey, session)
            true
        }
        session.checkInCache(
            SessionEntity.CACHE_DATABASE,
            skipCheck = changed || session.isMarkAsDeleted
        ) {
            //send to a new queue
            offerToDbQueue(session)
            false
        }

        return changed
    }

    private suspend fun handleSessionEntityAction(
        action: SessionEntityAction
    ) {
        when (action) {
            is SessionEntityAction.Query -> {
                var filtered = 0
                val models = action.sessionKeys.asSequence()
                    .mapNotNull { sessionKey ->
                        val (session, fromDb) = try {
                            getSession(sessionKey, createWhenAbsent = false)
                        } catch (e: Exception) {
                            reportErrors("getSession", e)
                            null to false
                        }
                        val matched = SessionFilterHelper
                            .filterDisplaySessions(
                                session?.toMetadata(), action.skipFoldCheck
                            )
                        if (!matched) {
                            filtered++
                            return@mapNotNull null
                        }

                        doSessionEntityUpdate(session!!, fromDb) {
                            SessionDefinitionManager.validateSession(session, true) ?: false
                        }

                        session.toModel()
                    }
                    .take(action.size)
                    .toList()
                action.onFetched?.invoke(models) ?: sessionModelWriteChannel.safeOffer(
                    SessionModelAction.Provide(models, filtered)
                )
            }

            is SessionEntityAction.Update -> {
                val (session, fromDb) = getSession(action.sessionKey.value, action.createWhenAbsent)
                if (session == null) return

                val isChanged = doSessionEntityUpdate(session) {
                    //如果是从数据库读取的，需要尝试validate一次
                    val result = if (fromDb) {
                        SessionDefinitionManager.validateSession(session, false) ?: false
                    } else false
                    //是必须要创建的，那么重置当前的markAsDeleted状态
                    if (action.createWhenAbsent) {
                        session.markAsDeleted = false
                    }

                    action.block(sessionScope, session) || result
                }
                if (isChanged) {
                    session.toModel()?.let { model ->
                        sharedSessionChannel.safeOffer(model)
                        sessionModelWriteChannel.safeOffer(
                            SessionModelAction.Update(session.orderId, action.sessionKey, model)
                        )
                    }
                }
            }
        }
    }
    //</editor-fold>

    //<editor-fold desc="SessionModelAction">
    sealed class SessionModelAction {
        protected val idx = counter++

        class Query(
            val size: Int? = null,
            val offset: Int = 0
        ) : SessionModelAction() {
            override fun toString(): String {
                return "[$idx]Model:Query: size=$size, ${hashCode()}"
            }
        }

        class Update(
            val orderId: Long,
            val key: SessionKey,
            val model: SessionModel
        ) : SessionModelAction() {
            override fun toString(): String {
                return "[$idx]Model:Update: orderId=$orderId, key=$key, model=$model"
            }
        }

        class Provide(
            val models: List<SessionModel>,
            val filtered: Int
        ) : SessionModelAction() {
            override fun toString(): String {
                return "[$idx]Model:Provide: filtered=${filtered}, models=${models.map { it.sessionKeyStr }}"
            }
        }

        companion object {
            private var counter = 0
        }
    }

    private fun setupModelActionQueue() {
        sessionScope.launch {
            try {
                sessionOpFlow.collect {
                    sessionChannel.safeOffer(it)
                }
            } catch (e: Exception) {
                reportErrors("sessionOpFlow", e)
            }
        }
        sessionScope.launch {
            sessionModelQuerySamplingChannel.asFlow().sample(500).collect {
                sessionModelReadChannel.safeOffer(it)
            }
        }
        sessionScope.launch {
            try {
                while (isActive) {
                    select<Unit> {
                        sessionModelReadChannel.onReceive {
                            try {
                                handleSessionModelAction(it)
                            } catch (e: Exception) {
                                reportErrors("sessionModelQueue-R", e)
                            }
                        }
                        sessionModelWriteChannel.onReceive {
                            try {
                                handleSessionModelAction(it)
                            } catch (e: Exception) {
                                reportErrors("sessionModelQueue-W", e)
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                reportErrors("sessionModelQueue", e)
            } finally {
                closeChannels()
            }
        }
    }

    private fun handleSessionModelAction(action: SessionModelAction) {
        when (action) {
            is SessionModelAction.Query -> {
                if (action.size != null) {
                    sessionModelRequiredCount = action.size
                } else {
                    sessionModelRequiredCount += action.offset
                }
                sessionEntityReadChannel.safeOffer(
                    SessionEntityAction.Query(
                        descendingMetadataList.map { it.sessionKey },
                        sessionModelRequiredCount
                    )
                )
            }

            is SessionModelAction.Provide -> {
                val newModels = action.models
                sessionOpFlow.tryEmit(
                    SessionOp.All(newModels, metadataMap.keys.size - action.filtered)
                )
                sessionModelsInUI.clear()
                sessionModelsInUI.addAll(newModels)
                newModels.forEach {
                    updateMetadata(it.sessionKey,false) {
                        with(it)
                    }
                }
            }

            is SessionModelAction.Update -> {
                val newModel = action.model
                val newOrderId = action.orderId
                val oldMetadata = metadataMap[action.key.value]
                val oldIndex = descendingMetadataList.indexOf(oldMetadata)
                if (oldMetadata != null && oldIndex >= 0 && oldMetadata.canOnlyUpdate(action.model)) {
                    val upperOrderId =
                        descendingMetadataList.getOrNull(oldIndex - 1)?.orderId ?: Long.MAX_VALUE
                    val downOrderId =
                        descendingMetadataList.getOrNull(oldIndex + 1)?.orderId ?: Long.MIN_VALUE
                    if (newOrderId in downOrderId..upperOrderId) {
                        val index =
                            sessionModelsInUI.indexOfFirst { it.baseInfo.sessionKey == action.key.value }
                        if (index >= 0) {
                            Log.d(
                                "SessionManager",
                                "${action.model.sessionKey.value}: ui cache hit "
                            )
                            updateMetadata(action.key, reorder = false) {
                                with(action.model)
                            }

                            val oldModel = sessionModelsInUI[index]
                            if (newModel != oldModel) {
                                sessionOpFlow.tryEmit(
                                    SessionOp.Update(index, arrayListOf(newModel))
                                )
                                sessionModelsInUI[index] = newModel
                            }
                            return
                        }
                    }
                }

                Log.d("SessionManager", "${action.model.sessionKey.value}: ui cache missed ")

                val queryOffset = if (sessionModelsInUI.isNotEmpty()) {
                    if (oldMetadata == null && newOrderId >= minOrderId) 1 else 0
                } else 0

                updateMetadata(action.key) {
                    with(action.model)
                }

                sessionModelQuerySamplingChannel.safeOffer(
                    SessionModelAction.Query(offset = queryOffset)
                )
            }
        }
    }
    //</editor-fold>

    internal fun getSessionInCache(sessionKey: String): SessionEntity? =
        getSession(sessionKey, createWhenAbsent = false).first

    internal fun getSession(
        sessionKey: String,
        createWhenAbsent: Boolean
    ): Pair<SessionEntity?, Boolean> {
        var fromDb = false
        val session = sessionEntityCache[sessionKey]
            ?: sessionService.getSession(SessionKey.fromString(sessionKey))?.also {
                if (it.orderId == 0L) {
                    it.generateNewOrderId()
                }
                fromDb = true
            }
            ?: (if (createWhenAbsent)
                SessionDefinitionManager.createSession(SessionKey.fromString(sessionKey)).also {
                    it.generateNewOrderId()
                }
            else null)
        return session to fromDb
    }

    internal fun <T : SessionEntity> withSession(
        sessionKey: SessionKey,
        createWhenAbsent: Boolean = false,
        block: suspend CoroutineScope.(T) -> Boolean
    ) {
        sessionEntityWriteChannel.safeOffer(
            SessionEntityAction.Update(
                sessionKey, createWhenAbsent,
                block as suspend CoroutineScope.(SessionEntity) -> Boolean
            )
        ).let { offerSuccess ->
            if (offerSuccess) {
                sessionService.addUnreliable(sessionKey)
            }
        }
    }

    /**
     * @param skipSampling 是否跳过500ms仅请求一次的采样
     */
    internal fun getSessionListAsync(size: Int, skipSampling: Boolean = false) =
        SessionModelAction.Query(size).let {
            if (skipSampling) sessionModelReadChannel.safeOffer(it)
            else sessionModelQuerySamplingChannel.safeOffer(it)
        }

    fun getSessionModelInCache(sessionKeyStr: String) = getSessionInCache(sessionKeyStr)?.let {
        SessionDefinitionManager.entityToModel(it)
    }

    fun getSessionModelsByKeys(
        sessionKeys: List<String>,
        count: Int = 100,
        skipFoldCheck: Boolean = false
    ) = runBlocking {
        require(!Thread.currentThread().name.startsWith(threadName)) {
            "cannot block flush thread"
        }
        suspendCoroutine<List<SessionModel>> { cont ->
            val result =
                sessionEntityReadChannel.safeOffer(
                    SessionEntityAction.Query(sessionKeys, count, skipFoldCheck) {
                        cont.resume(it)
                        true
                    })
            if (!result) {
                cont.resume(emptyList())
            }
        }
    }

    //<editor-fold desc="Update Session">
    private fun SessionEntity.generateNewOrderId() {
        val base = if (recommendTime != 0L) recommendTime else {
            if (lastMsgId != null) lastMsgTime else lastFetchTime
        }
        orderId = when {
            isSticky -> {
                SessionStickyHelper.makeOrderId(base)
            }

            else -> base
        }
    }

    fun updateSession(
        sessionKey: SessionKey,
        createWhenAbsent: Boolean = false,
        updater: suspend CoroutineScope.(SessionEntity) -> Boolean
    ) = withSession<SessionEntity>(sessionKey, createWhenAbsent) {
        if (updater(it)) {
            it.generateNewOrderId()
            true
        } else false
    }

    fun updateSession(
        sessionModel: SessionModel,
        createWhenAbsent: Boolean = false,
        block: suspend CoroutineScope.(SessionEntity) -> Boolean
    ) = updateSession(sessionModel.sessionKey, createWhenAbsent, block)

    fun updateSession(
        bundle: SessionUpdateBundle,
        createWhenAbsent: Boolean = false,
        block: suspend CoroutineScope.(SessionEntity) -> Boolean
    ) {
        val sessionKey = bundle.castOrNull<HasSessionKey>()?.sessionKey
        if (sessionKey != null) {
            updateSession(sessionKey, createWhenAbsent, block)
        } else {
            SessionDefinitionManager.findSessionKey(bundle) { key, _ ->
                updateSession(key, createWhenAbsent, block)
            }
        }
    }

    fun updateSessionForJava(
        key: SessionKey,
        createWhenAbsent: Boolean = false,
        block: (SessionEntity) -> Boolean
    ) = updateSession(key, createWhenAbsent) { block(it) }

    //</editor-fold>


    //<editor-fold desc="Sync Session">
    fun syncSession(sessionKey: SessionKey, createWhenAbsent: Boolean = false, data: Any?) =
        updateSession(sessionKey, createWhenAbsent) {
            SessionDefinitionManager.syncSession(it, data)
        }

    @JvmOverloads
    fun syncSession(raw: Any, createWhenAbsent: Boolean = false) {
        val sessionKey = raw.castOrNull<HasSessionKey>()?.sessionKey
        if (sessionKey != null) {
            syncSession(sessionKey, createWhenAbsent, raw)
        } else {
            SessionDefinitionManager.findSessionKey(raw) { key, data ->
                syncSession(key, createWhenAbsent, raw)
            }
        }
    }
    //</editor-fold>

    @JvmOverloads
    fun deleteSession(
        sessionKey: SessionKey,
        onFetchedDispatcher: CoroutineDispatcher = MMDispatchers.User,
        onFetched: suspend CoroutineScope.(SessionModel) -> Unit = {}
    ) {
        sessionEntityCache.get(sessionKey.value)?.let {
            it.unreadMessageCount = 0
            it.silentMessageCount = 0
        }
        updateSession(sessionKey, createWhenAbsent = false) {
            @Suppress("DeferredResultUnused")
            async(onFetchedDispatcher) {
                SessionDefinitionManager.entityToModel(it)?.let { model ->
                    onFetched(model)
                    updateSession(sessionKey, createWhenAbsent = false) {
                        it.markAsDeleted = true
                        true
                    }
                }
            }
            false
        }
    }

    //<editor-fold desc="Business Part">
    fun checkSessionExist(sessionKey: String) =
        if (isInitJobFinished) {
            metadataMap[sessionKey]?.markedAsDeleted == false
        } else {
            sessionService.getSession(sessionKey)?.isMarkAsDeleted == false
        }

    fun checkSessionExist(sessionKey: String, folderType: Int) =
        if (isInitJobFinished) {
            metadataMap[sessionKey]?.markedAsDeleted == false &&
                    metadataMap[sessionKey]?.foldType == folderType
        } else {
            sessionService.getSession(sessionKey)?.let {
                !it.markAsDeleted && it.foldType == folderType
            } == true
        }

    fun findContactedMomoIds() = metadataMap.values.asSequence()
        .filter {
            it.sessionType == "u"
                    && it.foldType == FolderType.Default
                    && !it.markedAsDeleted
        }
        .sortedByDescending { it.orderId }
        .map { it.sessionId }
        .toList().toTypedArray()

    fun <T : Any> findAllSessions(
        limit: Int = Int.MAX_VALUE,
        filter: (SessionMetadata) -> Boolean,
        mapper: (SessionMetadata) -> T
    ): List<T> {
        return (if (metadataMap.isEmpty()) {
            sessionService.loadAllKeys().map { it.value }
        } else {
            metadataMap.values
        }).asSequence()
            .filter(filter)
            .sortedByDescending { it.orderId }
            .take(limit)
            .map(mapper)
            .toList()
    }

    @JvmOverloads
    fun findAllSessionIds(
        sessionType: String,
        fallbackOnDb: Boolean = false,
        withoutFold: Boolean = false,
        limit: Int = Int.MAX_VALUE
    ) =
        (if (metadataMap.isEmpty() && fallbackOnDb) {
            sessionService.loadAllKeys().map { it.value }
        } else {
            metadataMap.values
        }).asSequence()
            .filter {
                it.sessionType == sessionType
                        && !it.markedAsDeleted
                        && (if (withoutFold) it.foldType == FolderType.Default else true)
            }
            .sortedByDescending { it.orderId }
            .take(limit)
            .map { it.sessionId }
            .toList().toTypedArray()

    @Deprecated("only for web bridge")
    fun findAllChatSessionIds(withoutFold: Boolean, limit: Int) = metadataMap.values.asSequence()
        .filter {
            !it.markedAsDeleted && when (it.sessionType) {
                "u" -> if (withoutFold) it.foldType == FolderType.Default else true
                "d", "g" -> true
                else -> false
            }
        }
        .sortedByDescending { it.orderId }
        .take(limit)
        .map {
            when (it.sessionType) {
                "u" -> AbsSession.TYPE_CHAT
                "g" -> AbsSession.TYPE_GROUP
                "d" -> AbsSession.TYPE_DISCUSS
                else -> AbsSession.TYPE_DELETE
            } to it.sessionId
        }
        .toList()

    /**
     * @param upToDate: 是否从缓存里面直接取数据
     */
    fun findAllFoldSessionIds(
        folderType: Int,
        upToDate: Boolean = false,
        lastMsgTimeDesc: Boolean = false
    ): Array<String> {
        val sequence = metadataMap.values.asSequence()
            .filter { it.sessionType == UserChatSessionDefinition.Type }
            .map {
                val newMetadata =
                    if (upToDate) sessionEntityCache[it.sessionKey]?.toMetadata() else null
                newMetadata ?: it
            }
            .filter {
                it.foldType == folderType && !it.markedAsDeleted
            }

        return if (lastMsgTimeDesc) {
            sequence.map { it.sessionId to it.lastMsgUpdateTime }
                .sortedByDescending { it.second }
                .map { it.first }
                .toList().toTypedArray()
        } else {
            //尝试解决Comparison method violates its general contract!问题
            sequence.map { it.sessionId to it.orderId }
                .sortedByDescending { it.second }
                .map { it.first }
                .toList().toTypedArray()
        }
    }

    fun findAllOfficialFoldSessionIds(
        folderType: Int,
        upToDate: Boolean = false,
        lastMsgTimeDesc: Boolean = false
    ): Array<String> {
        val sequence = metadataMap.values.asSequence()
            .filter { it.sessionType == "goto" || it.sessionType == UserChatSessionDefinition.Type }
            .map {
                val newMetadata =
                    if (upToDate) sessionEntityCache.get(it.sessionKey)?.toMetadata() else null
                newMetadata ?: it
            }
            .filter {
                it.foldType == folderType && !it.markedAsDeleted
            }

        return if (lastMsgTimeDesc) {
            sequence.map { it.sessionId to it.lastMsgUpdateTime }
                .sortedByDescending { it.second }
                .map { it.first }
                .toList().toTypedArray()
        } else {
            //尝试解决Comparison method violates its general contract!问题
            sequence.map { it.sessionId to it.orderId }
                .sortedByDescending { it.second }
                .map { it.first }
                .toList().toTypedArray()
        }
    }

    fun findAllFoldSessionIdsExcept(folderType: Int, index: Int = 0, count: Int = Int.MAX_VALUE) =
        metadataMap.values.asSequence()
            .filter {
                it.sessionType == "u" && it.foldType != folderType && !it.markedAsDeleted
            }
            .sortedByDescending { it.orderId }
            .map { it.sessionId }
            .drop(index)
            .take(count)
            .toList().toTypedArray()
    //</editor-fold>

    fun observeSessionModelChange(owner: LifecycleOwner, callback: (SessionModel) -> Unit) {
        owner.lifecycleScope.launch(MMDispatchers.Main) {
            sharedSessionFlow.collect {
                callback(it)
            }
        }
    }

    fun getNewBoyPossible(oldFriendList: Set<String>, maxCount: Int) =
        metadataMap.values.asSequence()
            .filter {
                it.sessionType == "u" && it.foldType == FolderType.Default && !it.markedAsDeleted && !oldFriendList.contains(
                    it.sessionId
                )
            }
            .sortedByDescending { it.orderId }
            .map { it.sessionId }
            .take(maxCount)
            .toList()


    fun getHePaiPossible(oldSessionList: Set<String>, maxCount: Int) =
        metadataMap.values.asSequence()
            .filter {
                it.sessionType == "u" && it.foldType == FolderType.Default && !it.markedAsDeleted && !oldSessionList.contains(
                    it.sessionId
                )
            }
            .sortedByDescending { it.orderId }
            .map { it.sessionId }
            .take(maxCount)
            .toList()

    fun getSpamPossible(oldSessionList: Set<String>, maxCount: Int) =
        metadataMap.values.asSequence()
            .filter {
                it.sessionType == "u" && !it.markedAsDeleted && !oldSessionList.contains(
                    it.sessionId
                )
            }
            .filter {
                (System.currentTimeMillis() - it.lastMsgUpdateTime) / (1000 * 60 * 60) >= 24
            }
            .sortedByDescending { it.lastMsgUpdateTime }
            .map { it.sessionId }
            .take(maxCount)
            .toList()

    fun getSpamFoldPossible(spamSessionList: Set<String>, maxCount: Int) =
        metadataMap.values.asSequence()
            .sortedByDescending { it.lastMsgUpdateTime }
            .filter {
                it.sessionType == "u" && !it.markedAsDeleted && spamSessionList.contains(it.sessionId)
            }
            .map { it.sessionId }
            .take(maxCount)
            .toList()

    /**
     * 获取展示在消息主帧的session数量
     */
    fun getSessionListSize() = metadataMap.values.filter { it.foldType == FolderType.Default }.size

    fun getSessionMetadata(sessionKey: SessionKey): SessionMetadata? {
        return metadataMap.get(sessionKey.value)
    }

    fun hasNewBoySession(sessionKeys: MutableList<SessionKey>): Boolean {
        sessionKeys.forEach {
            if (metadataMap.get(it.value)?.foldType == FolderType.NewBOY) {
                return true
            }
        }
        return false
    }

    fun hasSpamSession(sessionKeys: MutableList<SessionKey>): Boolean {
        sessionKeys.forEach {
            if (metadataMap.get(it.value)?.foldType == FolderType.Spam) {
                return true
            }
        }
        return false
    }

    fun hasHePaiSession(sessionKeys: MutableList<SessionKey>): Boolean {
        sessionKeys.forEach {
            if (metadataMap[it.value]?.foldType == FolderType.HePai) {
                return true
            }
        }
        return false
    }

    fun getSpecialCount(): SessionCountBean {
        var chatCount = 0
        var recentOnLineCount = 0
        metadataMap.values.forEach {
            if (it.sessionType == "g" || it.sessionType == "u") {
                chatCount++
            }
            if (it.sessionType == ActiveUserSessionDefinition.Type) {
                recentOnLineCount++
            }
        }
        return SessionCountBean(metadataMap.values.size - recentOnLineCount, chatCount)
    }

    fun getAllOfficialUnread(): Int {
        var unreadCount = 0
        sessionEntityCache.forEach {
            if (it.value.foldType == FolderType.Official) {
                unreadCount += it.value.unreadMessageCount
            }
        }
        return unreadCount
    }

    fun getAllGameBoxUnread(): Int {
        var unreadCount = 0
        sessionEntityCache.forEach {
            if (it.value.foldType == FolderType.GAMEBOX) {
                unreadCount += it.value.unreadMessageCount
            }
        }
        return unreadCount
    }

    fun getAllOfficialContent(): Pair<String, Long> {
        val list = metadataMap.values.asSequence()
            .sortedByDescending { it.lastMsgUpdateTime }
            .filter {
                it.foldType == FolderType.Official && !it.markedAsDeleted
            }
            .map { it.sessionKey }
            .take(100)
            .toList()
        var officialContent = ""
        var time = 0L
        if (list.isNullOrEmpty()) {
            return Pair(officialContent, time)
        } else {
            val sessionInCache = getSessionInCache(list[0])
            try {
                val jsonObject = JSONObject(sessionInCache?.contentStr)
                if (sessionInCache?.sessionType == "u") {
                    officialContent = jsonObject.optString(
                        "userName",
                        ""
                    ) + ":" + jsonObject.optString("lastMessageContent", "")
                } else if (sessionInCache?.sessionType == "goto") {
                    officialContent =
                        jsonObject.optString("title", "") + ":" + jsonObject.optString("text", "")
                }
                time =
                    if (sessionInCache?.lastMsgTime == 0L) sessionInCache?.lastFetchTime else sessionInCache?.lastMsgTime
                        ?: 0L
            } catch (e: Exception) {
            }
            return Pair(officialContent, time)
        }
    }

}