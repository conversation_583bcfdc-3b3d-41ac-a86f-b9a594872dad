package com.immomo.momo.maintab.view

import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.animation.LinearInterpolator
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.addListener
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.momo.R

/**
 * CREATED BY liu.chong
 * AT 2021/8/17
 */
class ShimmerContainer(context: Context?, attrs: AttributeSet?) : ConstraintLayout(context, attrs) {
    data class AnimInfo(val delay: Long, val duration: Long, val repeatCount: Int)

    var shimmerImg: ImageView? = null
    var text: View? = null
    var anim: ValueAnimator? = null
    var mAnimating = false
    var imgWidth = 0
    var mViewWidth = 0
    var isAnimValid = false
    var onRealEnd: (() -> Unit)? = null

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        if (shimmerImg == null) {
            shimmerImg = findViewById(R.id.image_shimmer)
        }
        if (text == null) {
            text = findViewById(R.id.session_pre_content_shimmer_text)
        }
        imgWidth = shimmerImg?.width ?: 40
        mViewWidth = width
    }


    private fun start(duration: Long = 1500, delay: Long = 500, repeatCount: Int = 0) {
        if (mAnimating) {
            return
        }
        anim?.cancel()
        anim = ValueAnimator.ofFloat(0f, 1f).apply {
            this.duration = duration
            this.repeatCount = repeatCount
            this.startDelay = delay
            interpolator = LinearInterpolator()
            addUpdateListener {
                val v = it.animatedValue as Float
                if (v > 0.2) {
                    isAnimValid = true
                }
                shimmerImg?.translationX = (mViewWidth + imgWidth) * v
            }
            addListener(onStart = {
                mAnimating = true
                isAnimValid = false
                if (imgWidth == 0) {
                    imgWidth = shimmerImg?.width.safe()
                }
                shimmerImg?.translationX = 0F
                shimmerImg?.visibility = View.VISIBLE
            },
                onEnd = {
                    mAnimating = false
                    shimmerImg?.visibility = View.INVISIBLE
                    if (isAnimValid) {
                        onRealEnd?.invoke()
                    }
                },
                onCancel = {
                    mAnimating = false
                    shimmerImg?.visibility = View.INVISIBLE
                })
        }
        mAnimating = true
        anim?.start()
    }

    fun startAnim(duration: Long = 1500, delay: Long = 500, repeatCount: Int = 0) {
        if (mAnimating) {
            return
        }
        post {
            start(duration, delay, repeatCount)
        }
    }

    fun endAnim() {
        anim?.cancel()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        endAnim()
    }
}