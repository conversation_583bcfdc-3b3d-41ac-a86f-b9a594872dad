package com.immomo.momo.maintab.session2.event;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.util.MomoKit;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

/**
 * -----------------------------------------------------------------
 * Copyright (C)  sun
 * Create: 2022/6/7 5:31 PM
 * -----------------------------------------------------------------
 */
public class SessionInnerReceiver extends BaseReceiver {
    public static final String ACTION_NOTE_STATUS_CHANGE = MomoKit.INSTANCE.getPackageName() + ".action.session.inner.note_status_change";
    public static final String ACTION_NOTE_READ_CHANGE = MomoKit.INSTANCE.getPackageName() + ".action.session.inner.note_read_change";
    public static final String KEY_NOTE_STATUS = "key_note_status";
    public SessionInnerReceiver(Context context) {
        super(context);
        register(ACTION_NOTE_STATUS_CHANGE);
        register(ACTION_NOTE_READ_CHANGE);
    }

    @Override
    public void register(IntentFilter filter) {
        LocalBroadcastManager.getInstance(MomoKit.INSTANCE.getApp()).registerReceiver(this, filter);
    }

    public void unregister() {
        LocalBroadcastManager.getInstance(MomoKit.INSTANCE.getApp()).unregisterReceiver(this);
    }

    public static void sendNoteStatusBroadCast(int state) {
        Intent intent = new Intent(SessionInnerReceiver.ACTION_NOTE_STATUS_CHANGE);
        intent.putExtra(KEY_NOTE_STATUS, state);
        LocalBroadcastManager.getInstance(MomoKit.INSTANCE.getApp()).sendBroadcast(intent);
    }

    public static void sendNoteReadBroadCast() {
        Intent intent = new Intent(SessionInnerReceiver.ACTION_NOTE_READ_CHANGE);
        LocalBroadcastManager.getInstance(MomoKit.INSTANCE.getApp()).sendBroadcast(intent);
    }
}
