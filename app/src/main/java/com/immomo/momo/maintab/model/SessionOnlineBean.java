package com.immomo.momo.maintab.model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.HashMap;

import kotlin.jvm.Transient;

public class SessionOnlineBean implements Serializable {
    @Expose
    @SerializedName("users")
    private HashMap<String, UserOnlineStatus> userOnlineList;

    @Transient
    private boolean isAvatarDataChanged = false;

    public HashMap<String, UserOnlineStatus> getUserOnlineList() {
        return userOnlineList;
    }

    public void setUserOnlineList(HashMap<String, UserOnlineStatus> userOnlineList) {
        this.userOnlineList = userOnlineList;
    }

    public boolean isAvatarDataChanged() {
        return isAvatarDataChanged;
    }

    public void setAvatarDataChanged(boolean avatarDataChanged) {
        isAvatarDataChanged = avatarDataChanged;
    }
}
