package com.immomo.momo.maintab.sessionlist.sort

import com.immomo.lcapt.evlog.anno.*

interface ISessionSortLog {
    @ExposurePoint(page = "msg.chatlist", action = "list.entrance", requireId = "19588")
    fun logSessionExposure(@ParamMap param: Map<String, String>)

    @ClickPoint(page = "msg.chatlist", action = "list.entrance", requireId = "19591")
    fun logSessionClick(@ParamMap param: Map<String, String>)

    @TaskPoint(page = "msg.chatlist", action = "left.slip", requireId = "12674")
    fun logSessionLongClick(@ParamMap param: Map<String, String>)

    @ClickPoint(page = "msg.chatlist", action = "window.delete", requireId = "12676")
    fun sessionOptionWindowDeleteClick(
        @Param("which_item") whichItem: String,
        @Param("remoteid") remoteId: String,
        @Param("click_pos") clickPos: String
    )

    @ExposurePoint(page = "msg.sayhi_to", action = "list.card", requireId = "12921")
    fun secondSessionExposureUnreply(@Param("to_momo_id") remoteId: String, @LogId logID: String)

    @ExposurePoint(page = "msg.newfriends", action = "list.cell", requireId = "13692")
    fun secondSessionExposureNewBoy(@Param("momo_id") remoteId: String, @LogId logID: String)

    @ExposurePoint(page = "cooperate.shotwithu", action = "list.entrance", requireId = "14407")
    fun secondSessionExposureHePai(@Param("momo_id") remoteId: String, @LogId logID: String)

    @ExposurePoint(page = "vchat.breathing_light", action = "content.show", requireId = "17712")
    fun breathingLightShow(
            @Param("momo_id") remoteId: String,
            @Param("show_page") showPage: String,
            @Param("room_business") roomBusiness: String,
            @Param("biz") biz: String,
            @LogId logId: String
    )

    @ClickPoint(page = "vchat.breathing_light", action = "content.click", requireId = "17713")
    fun breathingLightClick(
            @Param("momo_id") remoteId: String,
            @Param("show_page") showPage: String,
            @Param("room_business") roomBusiness: String,
            @Param("biz") biz: String
    )
}