package com.immomo.momo.maintab.session2.presentation.itemmodel

import android.content.Intent
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.immomo.android.mm.cement2.AsyncCementModel
import com.immomo.android.mm.cement2.CementViewHolder
import com.immomo.android.mm.cement2.IViewHolderCreator
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.utils.UIUtils
import com.immomo.framework.view.widget.LinesShimmerTextView
import com.immomo.momo.R
import com.immomo.momo.homepage.view.FlipTextView
import com.immomo.momo.maintab.session2.domain.model.type.UnReplySessionModel
import com.immomo.momo.maintab.sessionlist.UnReplySessionHelper
import com.immomo.momo.maintab.sessionlist.util.SessionHelper
import com.immomo.momo.message.activity.HiSessionSendListActivity


class UnReplySessionItemModel(val info: UnReplySessionModel) :
    AsyncCementModel<UnReplySessionModel, UnReplySessionItemModel.ViewHolder>(info) {

    init {
        id(info.uniqueId)
    }

    override val layoutRes: Int
        get() = R.layout.item_unreply_session

    override val viewHolderCreator: IViewHolderCreator<ViewHolder>
        get() = object : IViewHolderCreator<ViewHolder> {
            override fun create(view: View): ViewHolder = ViewHolder(view)
        }

    override fun bindData(holder: ViewHolder) {
        super.bindData(holder)
        ImageLoader.load(R.drawable.icon_session_list_unreply).into(holder.imgAvatar)
        holder.tvTimeStamp.setText("")
        holder.viewDes.setText(
            "你打出的招呼都在这",
            UIUtils.getColor(holder.itemView.context, R.color.color_aaaaaa_to_40fff),
            13f
        )
        if (UnReplySessionHelper.showAnim) {
            holder.tvTitle.startAnimation()
            UnReplySessionHelper.showAnim = false
        }

        holder.tvTitle.setTextColor(
            UIUtils.getColor(
                if (com.immomo.momo.util.MomoKit.isDarkMode(holder.itemView.context))
                    R.color.color_80fff else R.color.color_text_3b3b3b
            )
        )
        holder.itemView.setOnClickListener {
            SessionHelper.SessionLogParams(
                "sayhi_to", 0, holder.adapterPosition, "0",
                false, null, 0, 0, "", "", "", false
            ).also {
                SessionHelper.Log.logSessionClick(it)
            }
            it.context.startActivity(Intent(it.context, HiSessionSendListActivity::class.java))
        }
    }

    override fun unbind(holder: ViewHolder) {
        super.unbind(holder)
        holder.tvTitle.stopAnimation()
    }

    class ViewHolder(itemView: View) : CementViewHolder(itemView) {
        val tvTitle: LinesShimmerTextView = itemView.findViewById(R.id.tv_title)
        val viewDes: FlipTextView = itemView.findViewById(R.id.view_desc)
        val tvTimeStamp: TextView = itemView.findViewById(R.id.tv_timestamp)
        val imgAvatar: ImageView = itemView.findViewById(R.id.iv_avatar)

        init {
            viewDes.setTxtGravity(Gravity.LEFT)
            viewDes.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13f)
            viewDes.setDelayTime(1000L)
        }
    }
}