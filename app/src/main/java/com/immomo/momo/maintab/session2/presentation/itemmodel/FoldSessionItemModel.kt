package com.immomo.momo.maintab.session2.presentation.itemmodel

import android.content.Context
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.widget.TextView
import com.immomo.android.mm.cement2.AsyncCementModel
import com.immomo.android.mm.cement2.CementViewHolder
import com.immomo.android.mm.cement2.IViewHolderCreator
import com.immomo.framework.view.widget.LinesShimmerTextView
import com.immomo.momo.R
import com.immomo.momo.homepage.view.FlipTextView
import com.immomo.momo.maintab.session2.SessionAppConfigV1
import com.immomo.momo.maintab.session2.domain.model.type.MsgFoldSessionModel
import com.immomo.momo.maintab.session2.utils.SessionFoldHelper
import com.immomo.momo.maintab.sessionlist.expose.IItemSessionLogParamsProvider
import com.immomo.momo.maintab.sessionlist.util.SessionHelper
import com.immomo.momo.util.DateUtil
import java.util.Date


class FoldSessionItemModel(
    val info: MsgFoldSessionModel,
    val onHelpClick: (ViewHolder) -> Unit,
    val onItemClickListener: (MsgFoldSessionModel) -> Unit
) :
    AsyncCementModel<MsgFoldSessionModel, FoldSessionItemModel.ViewHolder>(info),
    IItemSessionLogParamsProvider {

    init {
        id(info.uniqueId)
    }

    override val layoutRes: Int
        get() = R.layout.item_fold_box

    override val viewHolderCreator: IViewHolderCreator<ViewHolder>
        get() = object : IViewHolderCreator<ViewHolder> {
            override fun create(view: View): ViewHolder = ViewHolder(view)
        }

    override fun bindData(holder: ViewHolder) {
        super.bindData(holder)
        val timeStr =
            DateUtil.getTimeLineStringStyle3(Date(info.baseInfo.lastMessageTime))
        if (SessionFoldHelper.isBubbleTest) {
            holder.unread.visibility = View.GONE
        } else {
            val unreadCount = info.baseInfo.unreadMessageCount
            holder.unread.visibility = if (unreadCount > 0) View.VISIBLE else View.GONE
            holder.unread.text = "$unreadCount"
        }
        holder.tvTimeStamp.text = timeStr
        holder.viewDes.setText(info.desc)
        holder.tvTitle.text = "已折叠消息"
        holder.iconAtTitleRight.visibility = View.VISIBLE
        holder.itemView.setOnClickListener {
            SessionHelper.SessionLogParams(
                "msg_fold_box", 0, holder.adapterPosition, "0",
                false, null, 0, 0, "", "",
                timeStr, false
            ).also {
                SessionHelper.Log.logSessionClick(it)
            }
            onItemClickListener(info)
        }
        holder.iconAtTitleRight.setOnClickListener {
            onHelpClick(holder)
        }
    }

    override fun unbind(holder: ViewHolder) {
        super.unbind(holder)
        holder.tvTitle.stopAnimation()
    }

    class ViewHolder(itemView: View) : CementViewHolder(itemView) {
        val tvTitle: LinesShimmerTextView = itemView.findViewById(R.id.box_title)
        val viewDes: FlipTextView = itemView.findViewById(R.id.box_desc)
        val tvTimeStamp: TextView = itemView.findViewById(R.id.box_timestamp)
        val iconAtTitleRight: View = itemView.findViewById(R.id.box_icon_title_right)
        val unread: TextView = itemView.findViewById(R.id.unread_text)

        init {
            viewDes.setTxtGravity(Gravity.LEFT)
            viewDes.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13f)
            viewDes.setDelayTime(1000L)
        }
    }

    override fun sessionLogParams(
        context: Context,
        position: Int,
        holder: CementViewHolder
    ) = SessionHelper.SessionLogParams(
        "msg_fold_box",
        info.baseInfo.unreadMessageCount,
        position,
        "0",
        false,
        null,
        0,
        0,
        "",
        "",
        (holder as? ViewHolder)?.tvTimeStamp?.text.toString(),
        false
    )

}