package com.immomo.momo.maintab.session2.defs

import android.text.TextUtils
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.storage.preference.SPKeys.User.SayHi.KEY_SAYHI_SESSION_RED_TEXT
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.R
import com.immomo.momo.globalevent.GlobalEventManager
import com.immomo.momo.greet.GreetHelper
import com.immomo.momo.im.GiftSayHiAppConfigV1.isOpenExp
import com.immomo.momo.maintab.session2.SessionContentParser
import com.immomo.momo.maintab.session2.SessionDefinition
import com.immomo.momo.maintab.session2.SessionUpdateBundle
import com.immomo.momo.maintab.session2.data.database.SessionContent
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.database.isInCache
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.data.manager.SessionKey.Companion.fromString
import com.immomo.momo.maintab.session2.data.manager.SessionManager.Companion.get
import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.type.SayHiSessionModel
import com.immomo.momo.message.NewSayUIConfigV1
import com.immomo.momo.message.sayhi.NewSayHiStackCache
import com.immomo.momo.message.sayhi.SayHiArgs
import com.immomo.momo.message.sayhi.SayHiStackCache
import com.immomo.momo.message.sayhi.SayHiUiTest
import com.immomo.momo.message.sayhi.utils.NewSayHiSessionFlowUtil
import com.immomo.momo.messages.service.SingleMsgService
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.SayhiSession
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.bean.message.Type15Content
import com.immomo.momo.service.sessions.LastMsgCache
import com.immomo.momo.service.sessions.SessionService
import com.immomo.momo.util.StringUtils
import com.squareup.moshi.JsonClass
import java.util.Date

@JsonClass(generateAdapter = true)
class SayHiContent(
    var hiUserTotalCount: Int = 0,
    var hiUserTotalCountUsable: Int = 0,
    var hiUnreadCount: Int = 0,
    var lastMsgRemoteId: String? = null,
    var desc: String? = null,
    var hiUserCount: Int = 0, //所有招呼人数
    var hasRedPacketTxt: Boolean = false,
    var heartbeatCount: Int = 0,
    var showGiftTag: Boolean = false // 是否展示礼物标签
) : SessionContent("sayhi")

//""
class SayHiSessionDefinition :
    SessionDefinition<SayHiContent, SayHiSessionModel>(
        "sayhi",
        SessionContentParser.moshiParser()
    ) {
    override fun createContent(): SayHiContent {
        return SayHiContent()
    }

    override fun generateId(data: Any?): String? {
        return when {
            data is SayhiSession ->
                Session.ID.SayhiSession

            else -> super.generateId(data)
        }
    }

    override fun onClearUnread(session: SessionEntity) {
        val content = session.content as? SayHiContent ?: return
        val userNewUI = NewSayUIConfigV1.isUserNewUI()  // 是否开启了新招呼实验
        content.hiUserTotalCount = if (SayHiArgs.isOpenBlock())
            SingleMsgService.getInstance().getAllSayHiUnreadWithPartSpamUserCount(isOpenExp()) else
            SingleMsgService.getInstance().getAllSayhiUnreadedUserCount(isOpenExp())
        refreshAllUnreadUserCnt(userNewUI, content)
        content.hiUserCount =
            SessionService.getInstance().getAllSayHiUserNormalPartSpamCount(isOpenExp())
        if (!NewSayUIConfigV1.isUserNewUI()) {
            content.heartbeatCount = SingleMsgService.getInstance().getAllSayHiWithHeartbeat(isOpenExp())
        } else {
            content.heartbeatCount = 0
        }
        content.desc = when {
            GreetHelper.isGreetNotRemindSettingMode() -> {
                "今日不再提醒新的招呼"
            }

            content.hiUserTotalCount > 0 -> {
                getNewUserHiContent(content)
            }

            content.hiUserTotalCountUsable > 0 -> {
                refreshAllUnreadUserCnt(content, userNewUI)
            }

            else -> {
                UIUtils.getString(R.string.no_more_unread_sayhi_user_now)
            }
        }
        KV.saveUserValue(KEY_SAYHI_SESSION_RED_TEXT, "")
        content.hasRedPacketTxt = false
        content.showGiftTag = false
        NewSayHiSessionFlowUtil.checkNeedFlowHiSession(content)
    }

    private fun refreshAllUnreadUserCnt(userNewUI: Boolean, content: SayHiContent) {
        if (userNewUI) {
            content.hiUserTotalCountUsable = if (SayHiArgs.isOpenBlock())
                SingleMsgService.getInstance().getAllSayHiWithPartSpamUserCountNew(isOpenExp()) else
                SingleMsgService.getInstance().getAllSayhiUsableUserCountNew(isOpenExp())
        } else {
            content.hiUserTotalCountUsable = if (SayHiArgs.isOpenBlock())
                SingleMsgService.getInstance().getAllSayHiWithPartSpamUserCount(isOpenExp()) else
                SingleMsgService.getInstance().getAllSayhiUsableUserCount(isOpenExp())
        }
    }

    override fun onReloadInfo(id: String, session: SessionEntity, forceReload: Boolean) {
        syncSession(session, null, false)
    }

    // 更新招呼Session
    /** 【 PS 】
     * 收到招呼如果第一次是普通消息或者live消息,两个不互通,所以第一次进来是什么以后收
     * 到另一个类型的都不相互切货,因此只在insert的时候保存。
     *
     * 但是如果之前是live招呼或者普通招呼,收到一条礼物消息,都需要把这个对应的
     * sayhisession收到礼物类型的招呼里面去,因此在任何时候收到礼物都需要更新这个session的类型。
     */
    private fun syncSession(
        session: SessionEntity,
        lastMessage: Message?,
        isHarass: Boolean
    ): Boolean {
        val content = session.content as? SayHiContent ?: return false
        val openGiftSessionExp = isOpenExp() // 是否开启了礼物招呼Session抽离功能
        val userNewUI = NewSayUIConfigV1.isUserNewUI()  // 是否开启了新招呼实验
        var unreadGiftHiMsgCount = 0
        var sayhiUsersIds: Array<String>? = null
        if (isOpenExp()) {
            val allGiftSayhiSessionData =
                SingleMsgService.getInstance().getAllSayhiSessionData(SayhiSession.FROM_TYPE_GIFT)
            unreadGiftHiMsgCount = allGiftSayhiSessionData.unreadCnt
            sayhiUsersIds = allGiftSayhiSessionData.sayhiUsers
        }
        var unreadMsgCount =
            SessionService.getInstance().allSayhiUnreadedMessageCount - unreadGiftHiMsgCount
        if (unreadMsgCount < 0) {
            unreadMsgCount = 0
        }
        session.unreadMessageCount = unreadMsgCount
        //理论上有未读消息就不应该有静默消息
        if (session.unreadMessageCount > 0) {
            session.silentMessageCount = 0
        } else {
            if (SayHiUiTest.isTest() && GreetHelper.isGreetNotRemindSettingMode()) {//实验中并打开消息免打扰需要展示静默红点
                session.silentMessageCount =
                    SessionService.getInstance().allSayhiUnreadedMessageCount
            } else {
                var unreadSilentCnt =
                    SessionService.getInstance().allSayhiSlientMessageCount - unreadGiftHiMsgCount
                if (unreadSilentCnt < 0) {
                    unreadSilentCnt = 0
                }
                session.silentMessageCount = unreadSilentCnt
            }
        }
        content.hiUserTotalCount =
            if (SayHiArgs.isOpenBlock()) SingleMsgService.getInstance()
                .getAllSayHiUnreadWithPartSpamUserCount(isOpenExp())
            else SingleMsgService.getInstance().getAllSayhiUnreadedUserCount(isOpenExp())
        refreshAllUnreadUserCnt(userNewUI, content) // 刷新所有用户未读数
        content.hiUserCount = SessionService.getInstance().getAllSayHiUserNormalPartSpamCount(isOpenExp())
        if (!NewSayUIConfigV1.isUserNewUI()) {
            content.heartbeatCount =
                SingleMsgService.getInstance().getAllSayHiWithHeartbeat(isOpenExp())
        } else {
            content.heartbeatCount = 0
        }

        content.desc = if (GreetHelper.isGreetNotRemindSettingMode()) {
            "今日不再提醒新的招呼"

        } else if (content.hiUserTotalCount > 0) {

            getNewUserHiContent(content)

        } else if (content.hiUserTotalCountUsable > 0) {
            refreshAllUnreadUserCnt(content, userNewUI)

        } else {
            UIUtils.getString(R.string.no_more_unread_sayhi_user_now)
        }

        if (userNewUI) { // 招呼新实验
            if (GreetHelper.isGreetNotRemindSettingMode()) {//打开消息免打扰需要展示静默红点
                session.unreadMessageCount = 0
                session.silentMessageCount = content.hiUserTotalCount
            } else {
                session.silentMessageCount = 0
                session.unreadMessageCount = content.hiUserTotalCount
            }
        }

        //处理收招呼cell是否置顶
        SayHiStackCache.setHasNewHi(content.hiUserTotalCount > 0)
        NewSayHiStackCache.setHasNewHi(content.hiUserTotalCount > 0) // 是否有未读数

        //仅更新最新一条招呼的消息
        if (!openGiftSessionExp ||
            !(lastMessage != null && sayhiUsersIds != null && sayhiUsersIds.contains(lastMessage.remoteId))
        ) {
            if (lastMessage != null && lastMessage.isUpdateSession && !lastMessage.isImSpam
                && session.lastMsgTime <= lastMessage.timestampMillis
            ) {
                session.lastMsgId = lastMessage.msgId ?: session.lastMsgId
                if (lastMessage.stopFloat == 0) {
                    session.lastMsgTime = lastMessage.timestamp?.time ?: session.lastMsgTime
                    session.recommendTime = 0
                }
            }
        }
        // NOTICE类型消息不显示在招呼界面
        if (lastMessage?.contentType != Message.CONTENTTYPE_MESSAGE_NOTICE
            && lastMessage?.contentType != Message.CONTENTTYPE_MESSAGE_HARASS
            && !isHarass
        ) {
            LastMsgCache.onSendNewMsg(lastMessage)
            if (userNewUI && !openGiftSessionExp) { // 礼物招呼新招呼实验展示外露
                (lastMessage?.messageContent as? Type15Content)?.also {
                    content.showGiftTag = true
                    content.desc = if (GreetHelper.isGreetNotRemindSettingMode()) {
                        "今日不再提醒新的招呼"
                    } else {
                        "有人送你礼物了！快来看看"
                    }
                }
            }
        }
        if (openGiftSessionExp) {
            content.showGiftTag = false
        }
        content.hasRedPacketTxt =
            StringUtils.notEmpty(KV.getUserStr(KEY_SAYHI_SESSION_RED_TEXT, ""))
        NewSayHiSessionFlowUtil.checkNeedFlowHiSession(content)
        return true
    }

    private fun getNewUserHiContent(content: SayHiContent): String {
        return if (NewSayUIConfigV1.isUserNewUI()) {
            "有${content.hiUserTotalCount}个新招呼"
        } else {
            if (content.heartbeatCount > 0) {
                "又有${content.hiUserTotalCount}个人打招呼，收到${content.heartbeatCount}个心动招呼"
            } else {
                "又有${content.hiUserTotalCount}个人给你打招呼"
            }
        }
    }

    private fun refreshAllUnreadUserCnt(content: SayHiContent, useNewUI: Boolean): String {
        return if (useNewUI) {
            "有${content.hiUserTotalCountUsable}个未读招呼，等你处理"
        } else {
            if (content.heartbeatCount > 0) {
                "有${content.hiUserTotalCountUsable}个未处理招呼，含${content.heartbeatCount}个心动招呼"
            } else {
                "有${content.hiUserTotalCountUsable}个未处理招呼"
            }
        }
    }

    override fun syncSession(session: SessionEntity, data: Any?): Boolean {
        if (super.syncSession(session, data)) return true
        val content = session.content as? SayHiContent ?: return false

        val sayhiSession =
            data as? SayhiSession ?: return syncSession(session, null, false)
        val isHarass = if (SayHiArgs.isOpenBlock())
            sayhiSession.isSessionSpamAll else
            sayhiSession.isHarassGreeting
        // 收到骚扰折叠招呼时消息的招呼session未创建的情况不需要创建招呼session.
        if (!session.isInCache(SessionEntity.CACHE_DATABASE) && isHarass) {
            session.markAsDeleted = true
            return true
        }

        var lastMessage: Message? = sayhiSession.lastMessage
        val momoId = if (SayHiArgs.isOpenBlock())
            SessionService.getInstance().findLastSayHiWithPartSpamSessionId() else
            SessionService.getInstance().findLastSayHiSessionId()
        if (!TextUtils.isEmpty(momoId)) {
            //不是同一个人，则强制更新
            if (content.lastMsgRemoteId == null || content.lastMsgRemoteId != momoId) {
                lastMessage = SingleMsgService.getInstance().getHiLastCommonMsgWithoutNotice(momoId)
            }
        }
        if (isOpenExp() && lastMessage != null && lastMessage.contentType == Message.CONTENTTYPE_MESSAGE_GIFT) { // 在实验组收到了礼物消息，低概率会出现在这，做个保护
            return true
        }
        if (isOpenExp() && (sayhiSession.isSessionSpamAll || sayhiSession.isHarassGreeting) && lastMessage != null) { // 如果当前是骚扰招呼
            val param = HashMap<String, Any>(1)
            param[PARAM_HARASS_USER_ID] = lastMessage.remoteId
            GlobalEventManager.getInstance().sendEvent(
                GlobalEventManager.Event(HARASS_MSG_IN_TO_DEAL_GIFT).msg(param)
                    .dst(GlobalEventManager.EVN_NATIVE)
                    .src(GlobalEventManager.EVN_NATIVE)
            )
            get().syncSession(
                SessionUpdateBundle.ReloadInfo(fromString(GiftSayHiSessionDefinition.SAYHI))
            )
        }
        return syncSession(session, lastMessage, isHarass)
    }

    override fun isTransformCompatible(oldSessionType: Int, oldSessionId: String): String? {
        return if (oldSessionType == Session.TYPE_SAYHI)
            Session.ID.SayhiSession else null
    }

    override fun transformOldSession(id: String): SessionEntity? {
        val oldSession = SessionService.getInstance().getSession(id) ?: return null

        val session = createSession(id)
        syncSession(session, oldSession.lastMessage, false)
        session.lastFetchTime = oldSession.fetchtime?.time ?: System.currentTimeMillis()
        return session
    }

    override fun saveOldSession(session: SessionEntity) {
        SessionService.getInstance().updateSession(session.sessionId) {
            it.type = Session.TYPE_SAYHI

            it.lastmsgId = session.lastMsgId
            it.setOrderid(session.orderId)
            it.fetchtime = Date(session.lastFetchTime)

            true
        }
    }

    override fun SayHiContent.contentToModel(
        baseInfo: BaseSessionInfo
    ): SayHiSessionModel = SayHiSessionModel(
        baseInfo,
        hiUserTotalCount = hiUserTotalCount,
        hiUserTotalCountUsable = hiUserTotalCountUsable,
        desc = desc.safe(),
        hasRedPacketTxt = hasRedPacketTxt,
        hasGiftTag = showGiftTag
    )

    companion object {

        const val HARASS_MSG_IN_TO_DEAL_GIFT = "harass_msg_in_to_deal_gift" // 命中骚扰招呼需要处理礼物招呼

        const val PARAM_HARASS_USER_ID = "harassUserId"

        @JvmField
        val KEY_SAYHI = SessionKey("sayhi", Session.ID.SayhiSession)

        @JvmField
        val SAYHI = "sayhi_" + Session.ID.SayhiSession
    }
}