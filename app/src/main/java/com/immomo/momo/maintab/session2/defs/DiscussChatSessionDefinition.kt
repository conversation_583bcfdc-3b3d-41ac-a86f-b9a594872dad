package com.immomo.momo.maintab.session2.defs

import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.momo.MomoKit
import com.immomo.momo.group.bean.GroupPreference
import com.immomo.momo.maintab.session2.SessionContentParser
import com.immomo.momo.maintab.session2.data.database.ChatContent
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.manager.LastUnreadMessageInterceptor
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.data.manager.UnreadCountMessageInterceptor
import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.type.DiscussChatSessionModel
import com.immomo.momo.maintab.sessionlist.SessionStickyHelper
import com.immomo.momo.message.helper.SessionTextHelper
import com.immomo.momo.messages.service.DiscussMsgServiceV2
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.sessions.LastMsgCache
import com.immomo.momo.service.sessions.MessageServiceHelper
import com.immomo.momo.service.sessions.SessionService
import com.immomo.momo.util.StringUtils
import com.squareup.moshi.JsonClass
import java.util.*

@JsonClass(generateAdapter = true)
class DiscussChatContent : ChatContent("d") {
    var discussAvatar: String? = null
    var discussName: String? = null

    var isHongbao = false
    var isGift = false

    override fun isChatInfoValid(): Boolean {
        return discussName?.isNotEmpty() == true
    }
}

class DiscussChatSessionDefinition
    : ChatSessionDefinition<DiscussChatContent, DiscussChatSessionModel>(
    "d", SessionContentParser.moshiParser()
) {
    override fun createContent(): DiscussChatContent {
        return DiscussChatContent()
    }

    override fun getLastMessage(session: SessionEntity): Message? {
        return DiscussMsgServiceV2.service.findLastMessage(session.sessionId)
    }

    override fun updateSessionDescWithLastMessage(
        session: SessionEntity,
        content: DiscussChatContent,
        lastMessage: Message,
        updateProcessedTime: Boolean
    ) {
        super.updateSessionDescWithLastMessage(session, content, lastMessage, updateProcessedTime)

        if (!lastMessage.isImSpam) {
            session.lastMsgId = lastMessage.msgId ?: session.lastMsgId
            if (lastMessage.stopFloat == 0) {
                session.lastMsgTime = lastMessage.timestamp?.time ?: session.lastMsgTime
            }
            content.lastMessageType = lastMessage.contentType
            LastMsgCache.onSendNewMsg(lastMessage)
        }

        if (lastMessage.notShowInSession) {
            content.forcedDesc = lastMessage.recommendReason ?: ""
        } else {
            content.lastMessageOwnerId = lastMessage.remoteId
            SessionManager.getInfoCache().fetchUser(session, lastMessage.remoteId) { user ->
                //最后一条消息的id不一致
                if (this.content.castOrNull<ChatContent>()?.lastMessageOwnerId
                    != lastMessage.remoteId
                ) return@fetchUser false

                lastMessage.owner = user
                this.content.castOrNull<ChatContent>()?.lastMessageOwnerName =
                    getDisplayName(lastMessage)
                true
            }

            // 关闭了消息提醒，通过最后一条消息内容的位置，提醒未读数量
            val preference = MomoKit.getCurrentPreference()
            val dp = preference?.getDiscussPreference(content.chatId)

            if (dp != null && !dp.isPushOpened && session.silentMessageCount > 0) {
                content.forcedDesc = session.silentMessageCount.toString() + "条消息未读"
            } else {
                content.forcedDesc = null
            }
            content.onlyShowMessageContent = false
            content.distanceInfo = -1F
            if (!lastMessage.receive) {
                // 自己发送的消息
                content.onlyShowMessageContent = true
            } else if (lastMessage.contentType == Message.CONTENTTYPE_MESSAGE_NOTICE) {
                // notioce 不组拼消息的username
                content.onlyShowMessageContent = true
            } else if (StringUtils.isEmpty(lastMessage.remoteId)) {
                content.onlyShowMessageContent = true
            } else if (lastMessage.diatance >= 0 && !content.isHongbao && !content.isGift) {
                // 有红包和礼物的情况下不显示距离
                content.distanceInfo = lastMessage.diatance
            }

            content.lastMessageContent = SessionTextHelper.getMessageContent(lastMessage)
        }

        updateLastMessageStatus(
            session,
            lastMessage.msgId,
            lastMessage.receive,
            lastMessage.status
        )
    }

    override fun updateSessionIndicatorWithEveryMessage(
        session: SessionEntity,
        content: DiscussChatContent,
        message: Message?
    ) {
        if (message?.status == Message.STATUS_CLOUD) return

        session.unreadMessageCount =
            UnreadCountMessageInterceptor.getCount(key(session.sessionId))
        //理论上有未读消息就不应该有静默消息
        if (session.unreadMessageCount > 0) {
            session.silentMessageCount = 0
        } else {
            session.silentMessageCount =
                UnreadCountMessageInterceptor.getSilentCount(key(session.sessionId))
        }
        // FIX：初次登录时，还没有获取到群组的消息屏蔽开关，屏蔽的群，会有未读消息数量，当获得开关配置后，不再显示未读数，导致气泡数对不上
        // 解决方案：如果发现Session是屏蔽状态，则清空Session的未读数
        if (MessageServiceHelper.getSessionNotificationMode(
                SessionKey.fromString(session.sessionKey)
            ) == GroupPreference.NOTIFICATION_CLOSE
        ) {
            session.unreadMessageCount = 0
        }

        content.isGift = LastUnreadMessageInterceptor.getUnread(
            key(session.sessionId),
            Message.CONTENTTYPE_MESSAGE_GIFT
        )?.isValid() == true

        content.isHongbao = LastUnreadMessageInterceptor.getUnread(
            key(session.sessionId),
            Message.CONTENTTYPE_MESSAGE_HONGBAO
        )?.isValid() == true
    }

    override fun onReloadChatInfo(id: String, session: SessionEntity, forceReload: Boolean) {
        val content = session.content as? DiscussChatContent ?: return
        content.pendingReloadChatInfo = true

        SessionManager.getInfoCache()
            .fetchDiscuss(session, session.sessionId, forceReload) { discuss ->
                this.content.castOrNull<DiscussChatContent>()?.also {
                    it.pendingReloadChatInfo = false

                    it.discussAvatar = discuss.getLoadImageId()
                    it.discussName = discuss.displayName ?: session.sessionId
                }
                true
            }

        val preference = MomoKit.getCurrentPreference()
        val dp = preference?.getDiscussPreference(content.chatId)

        if (dp != null && !dp.isPushOpened && session.silentMessageCount > 0) {
            content.forcedDesc = session.silentMessageCount.toString() + "条消息未读"
        } else {
            content.forcedDesc = null
        }
    }

    override fun syncSession(session: SessionEntity, data: Any?): Boolean {
        val content = session.content as? DiscussChatContent ?: return false
        content.chatId = session.sessionId

        session.isSticky =
            SessionStickyHelper.getInstance().getOrderID("d_${session.sessionId}") != -1L

        content.discussName = content.chatId
        onReloadChatInfo(session.sessionId, session, false)

        return super.syncSession(session, data)
    }


    override fun isTransformCompatible(oldSessionType: Int, oldSessionId: String): String? {
        return if (oldSessionId.startsWith("d")) oldSessionId.removePrefix("d_") else null
    }

    override fun transformOldSession(id: String): SessionEntity? {
        val oldSession = SessionService.getInstance().getSession("d_$id") ?: return null

        val session = createSession(id)
        syncSession(session, null)
        val content = session.content.castOrNull<DiscussChatContent>() ?: return null

        session.lastMsgId = oldSession.lastmsgId
        session.lastMsgTime = oldSession.lastMessage?.timestampMillis ?: 0
        session.lastFetchTime = oldSession.fetchtime?.time ?: 0
        session.orderId = oldSession.orderId

        content.draftString = oldSession.draftString
        content.draftQuoteString = oldSession.draftQuoteString

        updateSessionIndicatorWithEveryMessage(session, content, oldSession.lastMessage)
        oldSession.lastMessage?.let {
            updateSessionDescWithLastMessage(session, content, it, true)
        }
        return session
    }

    override fun saveOldSession(session: SessionEntity) {
        val content = session.content.castOrNull<DiscussChatSessionModel>() ?: return

        SessionService.getInstance().updateSession("d_${session.sessionId}") {
            it.type = Session.TYPE_DISCUSS
            it.chatId = session.sessionId

            it.lastmsgId = session.lastMsgId
            it.setOrderid(session.orderId)
            it.fetchtime = Date(session.lastFetchTime)

            it.draftString = content.draftString
            it.draftQuoteString = content.draftQuoteString

            true
        }
    }

    override fun removeOldSession(id: String) {
        SessionService.getInstance().deleteSession("d_$id")
    }

    override fun DiscussChatContent.contentToModel(
        baseInfo: BaseSessionInfo
    ): DiscussChatSessionModel = DiscussChatSessionModel(
        baseInfo = baseInfo,
        chatId = chatId.safe(),
        desc = desc.safe(),
        draftString = draftString.safe(),
        draftQuoteString = draftQuoteString.safe(),
        lastMessageType = lastMessageType.safe(0),
        showMessageStatus = showMessageStatus,
        lastMessageStatus = lastMessageStatus ?: 0,

        discussAvatar = discussAvatar.safe(),
        discussName = discussName.safe(),

        isHongbao = isHongbao,
        isGift = isGift
    )

    companion object {
        const val Type = "d"

        fun key(id: String) = SessionKey(Type, id)
    }
}

