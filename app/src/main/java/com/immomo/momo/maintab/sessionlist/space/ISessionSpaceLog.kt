package com.immomo.momo.maintab.sessionlist.space

import com.immomo.lcapt.evlog.anno.ClickPoint
import com.immomo.lcapt.evlog.anno.ExposurePoint
import com.immomo.lcapt.evlog.anno.ParamMap

/**
 * 单位空间实验埋点
 */
interface ISessionSpaceLog {


    @ClickPoint(page = "msg.chatlist", action = "window.close", requireId = "13829")
    fun onCloseClick(@ParamMap params: Map<String, String>)

    @ClickPoint(page = "msg.chatlist", action = "window.join", requireId = "13828")
    fun onJoinClick(@ParamMap params: Map<String, String>)

    @ClickPoint(page = "msg.chatlist", action = "window.content", requireId = "13827")
    fun onContentClick(@ParamMap params: Map<String, String>)

    @ExposurePoint(page = "msg.chatlist", action = "window.card", requireId = "13826")
    fun onCardShow(@ParamMap params: Map<String, String>)


}