package com.immomo.momo.maintab.session2.data.service

import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.data.manager.SessionOp
import com.immomo.momo.maintab.session2.domain.service.ISessionChangeService
import kotlinx.coroutines.flow.Flow

class SessionChangeService : ISessionChangeService {
    override fun observe(): Flow<SessionOp> {
        return SessionManager.get().sessionFlow
    }
}