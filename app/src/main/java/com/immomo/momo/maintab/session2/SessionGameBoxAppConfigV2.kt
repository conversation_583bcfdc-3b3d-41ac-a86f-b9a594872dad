package com.immomo.momo.maintab.session2

import com.immomo.annotations.appconfig.AppConfigField
import com.immomo.annotations.appconfig.appconfigv2.AppConfigV2
import com.immomo.momo.maintab.session2.apt.SessionGameBoxAppConfigV2Getter

/**
 * 游戏盒子
 */
@AppConfigV2
object SessionGameBoxAppConfigV2 {

    @AppConfigField(
        mark = "569",
        key = "gameFoldIcon",
        defValue = "https://s.momocdn.com/s1/u/fgjdgciai/gameFold/icon.png",
        isSysValue = true
    )
    var gameFoldIcon: String = "https://s.momocdn.com/s1/u/fgjdgciai/gameFold/icon.png"

    @AppConfigField(
        mark = "569",
        key = "gameFoldTitle",
        defValue = "游戏互动消息",
        isSysValue = true
    )
    var gameFoldTitle: String = "游戏互动消息"

    /**
     * 配置的icon
     */
    @JvmStatic
    fun getConfigIcon() = SessionGameBoxAppConfigV2Getter.get().gameFoldIcon()

    /**
     * 配置的文本
     */
    @JvmStatic
    fun getConfigText() = SessionGameBoxAppConfigV2Getter.get().gameFoldTitle()

}

