package com.immomo.momo.maintab.sessionlist.space;

import com.immomo.annotations.appconfig.AppConfigField;
import com.immomo.annotations.appconfig.appconfigv2.AppConfigV2;

/**
 * <AUTHOR>
 */
@AppConfigV2
public class SessionSpaceConfigV2 {
    /**
     * 用户二次看见卡片，卡片是否消失时间间隔
     */
    @AppConfigField(mark = "339", key = "session_leave_limit", defValue = "0", isSysValue = false)
    int leaveInterval;
    /**
     * 点击卡片是否隐藏标示
     */
    @AppConfigField(mark = "339", key = "session_leave_close", defValue = "0", isSysValue = false)
    int leaveClose;
    /**
     * 卡片展示时间间隔
     * 二次返回
     */
    @AppConfigField(mark = "339", key = "session_leave_limit_open_timer", defValue = "0", isSysValue = false)
    int showInterval;
    /**
     * 曝光时间间隔
     * 点击关闭
     */
    @AppConfigField(mark = "339", key = "session_leave_cancel_open_timer", defValue = "0", isSysValue = false)
    int exposureInterval;

    @AppConfigField(mark = "496", key = "chat_session_from_qiaoqiao_str", defValue = "来自敲敲", isSysValue = false)
    String sessionChatFromQi;


}
