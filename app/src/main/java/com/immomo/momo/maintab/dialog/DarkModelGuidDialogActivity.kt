package com.immomo.momo.maintab.dialog

import android.animation.Animator
import android.animation.Animator.AnimatorListener
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.Looper
import android.view.View
import android.view.WindowManager
import android.view.animation.DecelerateInterpolator
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.lifecycleScope
import com.google.android.material.internal.BaselineLayout
import com.immomo.framework.base.BaseActivity
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.utils.UIUtils
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.mmutil.task.MomoMainThreadExecutor
import com.immomo.molive.common.ktext.px
import com.immomo.momo.MomoApplicationEvent
import com.immomo.momo.R
import com.immomo.momo.maintab.MaintabActivity
import com.immomo.momo.maintab.config.DarkModeConfig
import com.immomo.momo.setting.activity.NightModelSettingActivity
import com.immomo.momo.setting.tools.NightModelHelper
import com.immomo.momo.util.MomoKit
import com.immomo.momo.util.device.DeviceInfoHelper
import com.immomo.momo.voicechat.util.dp2px
import com.immomo.svgaplayer.SVGAAnimListenerAdapter
import com.immomo.svgaplayer.view.MomoSVGAImageView
import kotlinx.coroutines.withContext


/**
 * 夜间模式引导弹窗
 */
class DarkModelGuidDialogActivity : BaseActivity() {

    companion object {

        /**
         * 是否已经展示了夜间模式引导
         */
        private const val KAY_IS_SHOWN_DARK_MODEL_GUID = "kay_is_shown_dark_model_guid"

        @JvmStatic
        fun checkShow(context: MaintabActivity) {
            context.lifecycleScope.launchWhenResumed {
                val isShown = KV.getSysBool(KAY_IS_SHOWN_DARK_MODEL_GUID, false)
                if (!isShown
                    && NightModelHelper.supportNightModel()
                    && DarkModeConfig.darkModeGuideOpen()
                ) {
                    withContext(MMDispatchers.User) {
                        if (!NightModelHelper.supportNightModel()) { // 暗黑功能没开就不展示
                            return@withContext
                        }
                        val currentTimeBetween = DeviceInfoHelper.isCurrentTimeBetween(18, 5)
                        if (!currentTimeBetween) {
                            return@withContext
                        }
                        MomoMainThreadExecutor.postDelayed(
                            context.hashCode(), { start(context) }, 1500
                        )
                    }
                }
            }
        }

        private fun start(context: MaintabActivity) {
            Looper.myQueue().addIdleHandler {
                if (MomoApplicationEvent.isForeground()) { // 判断在前台时启动页面
                    KV.saveSysValue(KAY_IS_SHOWN_DARK_MODEL_GUID, true)
                    context.startActivity(Intent(context, DarkModelGuidDialogActivity::class.java))
                    context.overridePendingTransition(
                        R.anim.anim_alpha_in_700,
                        R.anim.anim_alpha_out
                    )
                }
                false
            }
        }
    }

    private var mExitAnimatorSet: AnimatorSet? = null
    private var topAnimator: ValueAnimator? = null
    private lateinit var baseLine: BaselineLayout
    private lateinit var switchIcon: ImageView
    private lateinit var svgaIcon: MomoSVGAImageView
    private lateinit var topContainer: ConstraintLayout
    private lateinit var containerView: ConstraintLayout
    private lateinit var bgBlack: ImageView
    private lateinit var tipContentIv: ImageView
    private lateinit var btnOpen: TextView
    private lateinit var btnClose: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.layout_dialog_dark_model_main_guid)
        window?.also {
            it.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            it.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            it.decorView.systemUiVisibility =
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            it.statusBarColor = Color.TRANSPARENT
        }
        containerView = findViewById(R.id.container) as ConstraintLayout
        baseLine = findViewById(R.id.base_line) as BaselineLayout
        switchIcon = findViewById(R.id.switch_icon) as ImageView
        svgaIcon = findViewById(R.id.svga_icon) as MomoSVGAImageView
        topContainer = findViewById(R.id.top_container) as ConstraintLayout
        tipContentIv = findViewById(R.id.tip_content) as ImageView
        bgBlack = findViewById(R.id.bg_black) as ImageView
        btnOpen = findViewById(R.id.btn_open) as TextView
        btnClose = findViewById(R.id.btn_close) as TextView
        btnClose.setOnClickListener {
            finish()
        }
        btnOpen.setOnClickListener {
            startActivity(
                Intent(
                    this@DarkModelGuidDialogActivity,
                    NightModelSettingActivity::class.java
                )
            )
            finish()
        }
        checkDeviceUI()
        startShowAnimation()
    }

    override fun isLightTheme(): Boolean {
        return !MomoKit.isDarkMode(this)
    }

    private fun checkDeviceUI() {
        var screenWidth = UIUtils.getScreenWidth()
        val screenHeight = UIUtils.getScreenHeight()
        screenWidth = if (screenWidth <= 0) 1 else screenWidth
        val aspectRatio = screenHeight.toFloat() / screenWidth
        if (aspectRatio <= 1.78f) {
            (btnClose.layoutParams as ConstraintLayout.LayoutParams?)?.also {
                it.bottomMargin = UIUtils.getPixels(5f)
            }
            (tipContentIv.layoutParams as ConstraintLayout.LayoutParams?)?.also {
                it.topMargin = UIUtils.getPixels(10f)
                it.width = UIUtils.getPixels(70f)
            }
            (svgaIcon.layoutParams as ConstraintLayout.LayoutParams?)?.also {
                it.topMargin = UIUtils.getPixels(0f)
                it.width = UIUtils.getPixels(50f)
                it.width = UIUtils.getPixels(50f)
            }
            (tipContentIv.layoutParams as ConstraintLayout.LayoutParams?)?.also {
                it.topMargin = UIUtils.getPixels(10f)
                it.width = UIUtils.getPixels(110f)
            }
            topContainer.setPadding(0, UIUtils.getPixels(20f), 0, 0)
        } else if (aspectRatio <= 2.05f) { // 2比1的屏幕
            topContainer.setPadding(0, UIUtils.getPixels(30f), 0, 0)
            (btnClose.layoutParams as ConstraintLayout.LayoutParams?)?.also {
                it.bottomMargin = UIUtils.getPixels(30f)
            }
        }
    }

    private fun startShowAnimation() {
        mExitAnimatorSet = AnimatorSet()
        mExitAnimatorSet?.duration = 500
        topContainer.post {
            svgaIcon.startSVGAAnimWithListener("dark_model_guid_center_load.svga", 1, object :
                SVGAAnimListenerAdapter() {
                override fun onFinished() {
                    svgaIcon.stepToPercentage(1.0, false)
                }
            })
            val y = topContainer.y
            val animatorTrans =
                ObjectAnimator.ofFloat(
                    topContainer,
                    View.TRANSLATION_Y,
                    y + 50f.dp2px(),
                    y
                )
            val scaleXAnimator = ObjectAnimator.ofFloat(svgaIcon, "scaleX", 0.5f, 1f)
            val scaleYAnimator = ObjectAnimator.ofFloat(svgaIcon, "scaleY", 0.5f, 1f)
            val animatorAlpha = ObjectAnimator.ofFloat(containerView, View.ALPHA, 0f, 1f)
            mExitAnimatorSet?.playTogether(
                animatorAlpha,
                animatorTrans,
                scaleXAnimator,
                scaleYAnimator
            )
            mExitAnimatorSet?.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    MomoMainThreadExecutor.postDelayed(hashCode(), {
                        (baseLine.layoutParams as ConstraintLayout.LayoutParams?)?.also {
                            playTopCardAnimation(it, it.rightMargin, 240f.px()) {
                            }
                        }
                    }, 500)
                }
            })
            containerView.visibility = View.VISIBLE
            mExitAnimatorSet?.start()
        }
    }

    private fun playTopCardAnimation(
        layoutParams: ConstraintLayout.LayoutParams,
        startMargin: Int,
        endMargin: Int,
        onAnimationFinish: (() -> Unit)? = null
    ) {
        topAnimator = ValueAnimator.ofInt(startMargin, endMargin).apply {
            duration = 1000 // 动画持续时间，单位：毫秒
            interpolator = DecelerateInterpolator()
            addUpdateListener { animation ->
                layoutParams.rightMargin = animation.animatedValue as Int
                baseLine.layoutParams = layoutParams
            }
            addListener(object : AnimatorListener {
                override fun onAnimationStart(p0: Animator) {
                    switchIcon.visibility = View.VISIBLE
                }

                override fun onAnimationEnd(p0: Animator) {
                    switchIcon.visibility = View.GONE
                    onAnimationFinish?.invoke()
                }

                override fun onAnimationCancel(p0: Animator) {
                }

                override fun onAnimationRepeat(p0: Animator) {
                }
            })
            start()
        }
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(R.anim.anim_alpha_in, R.anim.anim_alpha_out)
    }

    override fun onDestroy() {
        super.onDestroy()
        mExitAnimatorSet?.cancel()
        topAnimator?.cancel()
        MomoMainThreadExecutor.cancelAllRunnables(hashCode())
    }

}