package com.immomo.momo.maintab.view

import com.immomo.svgaplayer.SVGAAnimListenerAdapter

/**
 * CREATED BY liu.chong
 * AT 2021/6/30
 */

open class OptSVGAListener(val repeatCount: Int = 1) : SVGAAnimListenerAdapter() {
    private var currentCount = 0
    private var canComplete = false
    override fun onStart() {
        currentCount = 0
    }

    override fun onRepeat() {
        currentCount++
    }

    override fun onStep(frame: Int, percentage: Double) {
        if (currentCount == repeatCount - 1 && percentage > 0.9) {
            canComplete = true
        }
    }

    override fun onFinished() {
        if (canComplete) {
            onCompleted()
        }
    }

    open fun onCompleted() {}
}