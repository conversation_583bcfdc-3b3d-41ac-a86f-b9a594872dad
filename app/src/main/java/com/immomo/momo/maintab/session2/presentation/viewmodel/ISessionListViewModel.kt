package com.immomo.momo.maintab.session2.presentation.viewmodel

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.coroutineScope
import com.immomo.android.mm.kobalt.domain.fx.Trigger
import com.immomo.android.mm.kobalt.domain.fx.toOption
import com.immomo.android.mm.kobalt.presentation.itemmodel.UniqueIdList
import com.immomo.android.mm.kobalt.presentation.itemmodel.emptyUniqueIdList
import com.immomo.android.mm.kobalt.presentation.viewmodel.Async
import com.immomo.android.mm.kobalt.presentation.viewmodel.KobaltState
import com.immomo.android.mm.kobalt.presentation.viewmodel.KobaltViewModel
import com.immomo.android.mm.kobalt.presentation.viewmodel.Success
import com.immomo.android.mm.kobalt.presentation.viewmodel.Uninitialized
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.domain.interactor.FetchUserInfoUseCase
import com.immomo.momo.maintab.session2.domain.interactor.GetSessionModelListUseCase
import com.immomo.momo.maintab.session2.domain.interactor.SessionFilter
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.maintab.sessionlist.util.SessionUserHelper
import com.immomo.momo.protocol.imjson.saas.common.util.batchCollect
import com.immomo.momo.service.sessions.SessionService
import com.immomo.momo.service.sessions.SessionUserCache
import com.immomo.momo.service.sessions.delete
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.consumeAsFlow
import kotlinx.coroutines.launch

/**
 * CREATED BY liu.chong
 * AT 2024/5/23
 */
data class SessionListState(
    val firstAsync: Async<GetSessionModelListUseCase.SessionResult> = Uninitialized,
    val loadMoreAsync: Async<GetSessionModelListUseCase.SessionResult> = Uninitialized,//有几个loading，就有几个Async ~
    val sessionList: UniqueIdList<SessionModel> = emptyUniqueIdList(),
    val scrollToTop: Trigger = Trigger.create(),
    val onSessionRemoved: Trigger = Trigger.create(),
    val hasMore: Boolean = false
) : KobaltState

class SessionListViewModel(
    private val initialState: SessionListState,
    private val sessionFilter: SessionFilter,
    private val getSessionModelUseCase: GetSessionModelListUseCase,
    private val refreshUserUseCase: FetchUserInfoUseCase

) : KobaltViewModel<SessionListState>(initialState) {
    private var pageSize = 20
    private val userIdToRefreshChannel = Channel<String>(Channel.UNLIMITED)

    init {
        viewModelScope.launch(MMDispatchers.User) {
            userIdToRefreshChannel.consumeAsFlow()
                .batchCollect(30, 1 * 1000)
                .collect {
                    fetchBatchUserInfo(it)
                }
        }

    }

    fun startLoop(lifecycleOwner: LifecycleOwner) {
        lifecycleOwner.lifecycle.coroutineScope.launchWhenResumed {
            while (true) {
                delay(5000)
                withState {
                    addUserIdToRefresh(
                        it.sessionList.map { it.sessionKeyStr }
                    )
                }
            }
        }
    }

    private fun fetchBatchUserInfo(sessionKeyList: List<String>) {
        sessionKeyList.toSet().filter {
            val user = SessionUserCache.getUser(it.substringAfter("_"))
            val delta = System.currentTimeMillis() - user?.lastStatusRefreshTime.safe()
            delta > 180 * 1000
        }.takeIf { it.isNotEmpty() }?.also {
            refreshUserUseCase.execute(it.toSet().toOption()) {
                if (it is Success) {
                    SessionUserHelper.onSessionUserUpdate(it())
                }
                this
            }
        }
    }

    fun addUserIdToRefresh(sessionKeyList: List<String>) {
        if (sessionKeyList.isEmpty()) {
            return
        }
        sessionKeyList.toSet().forEach {
            userIdToRefreshChannel.offer(it)
        }
    }

    /**
     * 首次进入请求列表
     */
    fun getSessionList() {
        getSessionModelUseCase.execute(
            GetSessionModelListUseCase.Param(pageSize).toOption()
        ) { async ->
            copy(
                firstAsync = async,
                sessionList = async()?.sessionList?.sortedByDescending { it.baseInfo.orderId }
                    ?.let { UniqueIdList(it) } ?: sessionList,
                hasMore = async()?.hasMore ?: hasMore,
                scrollToTop = if (async()?.sessionList.isNullOrEmpty().not())
                    scrollToTop.activate()
                else
                    scrollToTop
            )
        }
    }

    /**
     * 下滑，加载更多
     */
    fun loadMore() {
        withState { state ->
            getSessionModelUseCase.execute(
                GetSessionModelListUseCase.Param(state.sessionList.size + pageSize)
                    .toOption()
            ) { async ->
                val finalList = UniqueIdList(
                    (async()?.sessionList ?: emptyList()) + sessionList
                ).sortedByDescending { it.baseInfo.orderId }
                copy(
                    loadMoreAsync = async,
                    sessionList = UniqueIdList(finalList),
                    hasMore = async()?.hasMore ?: hasMore
                )
            }
        }
    }

    fun updateModel(model: SessionModel) {
        setState {
            var isDelete = false
            val finalList =
                if (model.baseInfo.markedAsDeleted || !sessionFilter.filterSessionModel(model)) {
                    isDelete = true
                    sessionList.filter { it.sessionId != model.sessionId }
                } else {
                    UniqueIdList(listOf(model) + sessionList)
                }
            copy(
                sessionList = UniqueIdList(finalList.sortedByDescending { it.baseInfo.orderId }),
                scrollToTop = if (finalList.size > sessionList.size) scrollToTop.activate() else scrollToTop,
                onSessionRemoved = if (isDelete) onSessionRemoved.activate() else onSessionRemoved
            )
        }
    }

    open fun onSessionDeleted() {

    }

    fun deleteSession(sessionKey: SessionKey) {
        SessionService.getInstance().delete(sessionKey, true)
    }

}