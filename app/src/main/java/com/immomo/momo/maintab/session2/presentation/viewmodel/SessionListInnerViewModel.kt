package com.immomo.momo.maintab.session2.presentation.viewmodel

import android.util.Log
import com.cosmos.mdlog.MDLog
import com.immomo.android.mm.kobalt.domain.fx.None
import com.immomo.android.mm.kobalt.domain.fx.Option
import com.immomo.android.mm.kobalt.domain.fx.Trigger
import com.immomo.android.mm.kobalt.domain.fx.toOption
import com.immomo.android.mm.kobalt.presentation.itemmodel.UniqueIdList
import com.immomo.android.mm.kobalt.presentation.itemmodel.emptyUniqueIdList
import com.immomo.android.mm.kobalt.presentation.viewmodel.Async
import com.immomo.android.mm.kobalt.presentation.viewmodel.Fail
import com.immomo.android.mm.kobalt.presentation.viewmodel.KobaltState
import com.immomo.android.mm.kobalt.presentation.viewmodel.KobaltViewModel
import com.immomo.android.mm.kobalt.presentation.viewmodel.Loading
import com.immomo.android.mm.kobalt.presentation.viewmodel.Success
import com.immomo.android.mm.kobalt.presentation.viewmodel.Uninitialized
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.data.manager.SessionOp
import com.immomo.momo.maintab.session2.domain.interactor.ObserveSessionChangeUseCase
import com.immomo.momo.maintab.session2.domain.interactor.SessionLocalTopOperatorUseCase
import com.immomo.momo.maintab.session2.domain.interactor.SessionTopOperatorUseCase
import com.immomo.momo.maintab.session2.domain.interactor.SessionUnreadCountUseCase
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.maintab.session2.domain.model.SessionNoticeInfoModel
import com.immomo.momo.maintab.session2.domain.model.type.ActiveUserSessionModel
import com.immomo.momo.maintab.sessionlist.bean.SessionTopOperatorData
import com.immomo.momo.mk.util.BusinessNotifySwitchUtils
import com.immomo.momo.service.sessions.SessionService
import com.immomo.momo.service.sessions.delete

data class SessionPaginationState(
    val models: UniqueIdList<SessionModel> = emptyUniqueIdList(),
    val loadingRequest: Async<Boolean> = Uninitialized,
    val hasMore: Boolean = false,
    val noticeInfo: SessionNoticeInfoModel = SessionNoticeInfoModel.NoInfo,
    val hasActiveUserItem: Boolean = false,
    val totalUnread: Int = 0,
    val reloadTotalUnread: Trigger = Trigger.create(),
    val showTip: Boolean = false
) : KobaltState

class SessionListInnerViewModel(
    state: SessionPaginationState,
    private val observeSessionChangeUseCase: ObserveSessionChangeUseCase,
    private val sessionTopOperatorUseCase: SessionTopOperatorUseCase,
    private val sessionUnreadUseCase: SessionUnreadCountUseCase,
    private val sessionLocalTopOperatorUseCase: SessionLocalTopOperatorUseCase
) : KobaltViewModel<SessionPaginationState>(state) {
    var unreadCount = 0
        private set

    fun SessionPaginationState.reloadModels(
        models: List<SessionModel>,
        hasMore: Boolean
    ): SessionPaginationState {
        var hasActiveUserItem = false


        models.forEach { model ->
            if (model is ActiveUserSessionModel) {
                hasActiveUserItem = true
            }
        }
        return copy(
            reloadTotalUnread = reloadTotalUnread.activate(),
            models = UniqueIdList(models),
            hasMore = hasMore,
            hasActiveUserItem = hasActiveUserItem,
            loadingRequest = Success(true)
        )
    }

    private fun SessionPaginationState.updateModel(
        newModels: List<SessionModel>
    ): SessionPaginationState {
        val newModelMap = newModels.associateBy { it.sessionKeyStr }

        var hasActiveUserItem = false
        val finalModels = models.map { model ->
            val ret = newModelMap[model.sessionKeyStr] ?: model
            if (ret is ActiveUserSessionModel) {
                hasActiveUserItem = true
            }
            ret
        }

        return copy(
            models = UniqueIdList(finalModels),
            reloadTotalUnread = reloadTotalUnread.activate(),
            hasActiveUserItem = hasActiveUserItem,
            loadingRequest = Success(true)
        )
    }

    fun init() {
        observeSessionChangeUseCase.execute(None) {
            when (val op = it()) {
                is SessionOp.Insert -> {
                    Log.d("FADJALDJLASKD", "insert ${op.pos}, ${op.models}")
                }

                is SessionOp.Move -> {
                    Log.d("FADJALDJLASKD", "move ${op.from} -> ${op.to}")
                }

                is SessionOp.Remove -> {
                    Log.d("FADJALDJLASKD", "remove ${op.pos}, count=${op.count}")
                }

                is SessionOp.Update -> {
                    return@execute updateModel(op.models)
                }

                is SessionOp.All -> {
                    return@execute reloadModels(op.models, op.models.size < op.total)
                }
            }
            this
        }
    }


    fun checkUnread() {
        setState {
            val filterIndexed = models.filterIndexed { index, model ->
                index > 8 && model.baseInfo.unreadMessageCount > 0
            }
            copy(showTip = !filterIndexed.isNullOrEmpty())
        }
    }

    fun updateUnreadCount() {
        sessionUnreadUseCase.execute("".toOption()) { unread ->
            if (unread !is Success) {
                return@execute this
            }
            val all = noticeInfo.count + unread()
            unreadCount = all
            copy(totalUnread = all)
        }
    }

    private var isFirstRequest = true

    fun requestRefresh() {
        SessionManager.get().getSessionListAsync(PAGE_SIZE, isFirstRequest)
        isFirstRequest = false
    }

    fun loadMoreSessions(clearAll: Boolean) {
        withState { state ->
            if (state.loadingRequest is Loading) return@withState
            if (!state.hasMore) return@withState

            setState {
                copy(loadingRequest = Loading())
            }
            SessionManager.get().getSessionListAsync(
                if (clearAll) {
                    PAGE_SIZE
                } else {
                    state.models.size + PAGE_SIZE
                }, skipSampling = true
            )
        }
    }

    val businessNotifySwitchTag = "SessionList" + hashCode()

    fun removeSession(sessionKey: SessionKey) {
        BusinessNotifySwitchUtils.getIntance()
            .removeCallbackListenerBySessionId(businessNotifySwitchTag, sessionKey.value)
        SessionService.getInstance().delete(sessionKey, true)
    }

    fun updateNoticeHeader(count: Int, desc: String?, gotoTabType: Int = 1) {
        setState {
            copy(noticeInfo = SessionNoticeInfoModel(count, desc ?: "", gotoTabType))
        }
    }

    fun getTopOperatorData(
        reqData: Map<String, String>,
        onDataGet: (SessionTopOperatorData) -> Unit,
        onFailed: (Throwable) -> Unit
    ) {
        sessionTopOperatorUseCase.execute(Option.just(reqData)) {
            if (it is Fail) {
                MDLog.e("getTopOperatorData:", it.error.message)
                onFailed(it.error)
            } else if (it is Success) {
                val sessionTopOperatorData = it()
                onDataGet(sessionTopOperatorData)
            }
            return@execute this
        }
    }

    fun getTopOperatorLocalData(
        onDataGet: (SessionTopOperatorData) -> Unit, onFailed: (Throwable) -> Unit
    ) {
        sessionLocalTopOperatorUseCase.execute(Option.just("")) {
            if (it is Fail) {
                MDLog.e("getTopOperatorLocalData:", it.error.message)
                onFailed(it.error)
            } else if (it is Success) {
                val sessionTopOperatorData = it()
                onDataGet(sessionTopOperatorData)
            }
            return@execute this
        }
    }

    companion object {
        private const val PAGE_SIZE = 30
    }
}
