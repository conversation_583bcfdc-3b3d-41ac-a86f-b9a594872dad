package com.immomo.momo.maintab.sessionlist.enterbar.usecase

import com.immomo.framework.rxjava.executor.PostExecutionThread
import com.immomo.framework.rxjava.executor.ThreadExecutor
import com.immomo.framework.rxjava.interactor.UseCase
import com.immomo.momo.mvp.common.model.ModelManager.getModel
import com.immomo.momo.service.sessions.ISessionRepository
import io.reactivex.Flowable

/**
 * 聊天室关闭的usecase
 */
class SessionEnterBarCloseUseCase(
    threadExecutor: ThreadExecutor,
    postExecutionThread: PostExecutionThread
) : UseCase<String, String?>(threadExecutor, postExecutionThread) {
    override fun buildUseCaseFlowable(type: String?): Flowable<String> {
        return getModel(ISessionRepository::class.java).fetchSessionEnterBarCloseCapsule(type)
    }
}