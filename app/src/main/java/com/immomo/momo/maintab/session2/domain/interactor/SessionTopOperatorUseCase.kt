package com.immomo.momo.maintab.session2.domain.interactor

import com.immomo.android.mm.kobalt.domain.exception.NoParamProvided
import com.immomo.android.mm.kobalt.domain.fx.Option
import com.immomo.android.mm.kobalt.domain.interactor.UseCase
import com.immomo.momo.maintab.session2.domain.repository.ISessionListRepository
import com.immomo.momo.maintab.sessionlist.bean.SessionTopOperatorData
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow

/**
 * 消息帧顶部匹配运营位
 */
class SessionTopOperatorUseCase(
    dispatcher: CoroutineDispatcher,
    private val repository: ISessionListRepository
) : UseCase<SessionTopOperatorData, Map<String, String>>(dispatcher) {
    override fun build(param: Option<Map<String, String>>): Flow<SessionTopOperatorData> {
        return param.fold({ throw NoParamProvided() }) {
            repository.requestSessionTopOperator(it)
        }
    }
}

/**
 * 消息帧顶部请求本地数据
 */
class SessionLocalTopOperatorUseCase(
    dispatcher: CoroutineDispatcher,
    private val repository: ISessionListRepository
) : UseCase<SessionTopOperatorData, String>(dispatcher) {
    override fun build(param: Option<String>): Flow<SessionTopOperatorData> {
        return param.fold({ throw NoParamProvided() }) {
            repository.requestLocalSessionTopOperator()
        }
    }
}