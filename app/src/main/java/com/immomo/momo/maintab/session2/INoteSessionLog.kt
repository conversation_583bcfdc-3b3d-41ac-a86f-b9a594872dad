package com.immomo.momo.maintab.session2

import com.immomo.lcapt.evlog.anno.ClickPoint
import com.immomo.lcapt.evlog.anno.ExposurePoint
import com.immomo.lcapt.evlog.anno.Param

interface INoteSessionLog {
    @ExposurePoint(requireId = "15707", page = "msg.chatlist", action = "list.notefeed")
    fun noteSessionShow(@Param("type") type: String, @Param("status") status: String)

    @ClickPoint(requireId = "15708", page = "msg.chatlist", action = "list.notefeed")
    fun noteSessionClick(@Param("type") type: String, @Param("status") status: String)
}