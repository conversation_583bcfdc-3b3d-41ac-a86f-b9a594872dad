package com.immomo.momo.maintab.usecase;

import android.os.Bundle;
import android.text.TextUtils;

import com.immomo.android.login.router.LoginRegisterRouter;
import com.immomo.framework.base.BaseActivity;
import com.immomo.momo.android.view.dialog.MAlertDialog;

import info.xudshen.android.appasm.AppAsm;

public class AvatarCheckActivity extends BaseActivity {

    public static final String SHOW_CONTENT = "show_content";

    private String showContent;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        initData();
        initDialog();
    }

    private void initData() {
        if (getIntent() == null) {
            finish();
        }

        showContent = getIntent().getStringExtra(SHOW_CONTENT);
        if (TextUtils.isEmpty(showContent)) {
            finish();
        }
    }

    private void initDialog() {
        MAlertDialog dialog = MAlertDialog.makeSingleButtonDialog(this, showContent, (dialog1, which) -> finish());
        dialog.setCancelable(false);
        dialog.setSupportDark(true);
        showDialog(dialog);
    }

    @Override
    public void finish() {
        AppAsm.getRouter(LoginRegisterRouter.class).clearAvatarCheckContent();
        super.finish();
    }
}
