package com.immomo.momo.maintab.model;


import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.immomo.android.module.fundamental.Badge.UniformLabelsBean;
import com.immomo.android.module.fundamental.Badge.model.BaseBadgeModel;
import com.immomo.momo.maintab.sessionlist.bean.FireOutData;
import com.immomo.momo.router.ProfileRealAuth;
import com.immomo.momo.service.bean.AvatarFrame;
import com.immomo.momo.service.bean.ChatTag;
import com.immomo.momo.service.bean.UserOnlineTag;

import java.io.Serializable;
import java.util.List;

public class UserOnlineStatus implements Serializable {
    public static class VipPoint implements Serializable {
        @Expose
        @SerializedName("level")
        public int level;
        @Expose
        @SerializedName("year")
        public int year;
        @Expose
        @SerializedName("valid")
        public int valid;
        @Expose
        @SerializedName("active_level")
        public int active_level;
        @Expose
        @SerializedName("pretty_id_type")
        public int pretty_id_type = -1;
    }

    @Expose
    @SerializedName("online_status")
    private int onlineStatus;

    @Expose
    @SerializedName("onlineMsgTime")
    private String onlineMsgTime;

    @Expose
    @SerializedName("distance")
    private int distance;

    @Expose
    @SerializedName("loc_timesec")
    private long locTimesec;


    @Expose
    @SerializedName("online_tag")
    private UserOnlineTag userOnlineTag;

    @Expose
    @SerializedName("realAuth")
    private ProfileRealAuth realAuth;


    @Expose
    @SerializedName("is_mark_spray")
    private int isMarkSpray;

    @Expose
    private IntimacyTag intimacy;
    @Expose
    @SerializedName("chatTag")
    private ChatTag chatTag;
    @Expose
    @SerializedName("relationBuildTime")
    private long relationBuildTime;
    @Expose
    @SerializedName("relationLastChatTime")
    private long relationLastChatTime;
    @Expose
    @SerializedName("p2pUniformLabels")
    public UniformLabelsBean p2pUniformLabels;
    public List<BaseBadgeModel> cellTagUniformLabels;
    @Expose
    @SerializedName("officialOperation")
    public int officialOperation;
    @Expose
    @SerializedName("vip")
    public VipPoint mVipPoint;
    @Expose
    @SerializedName("svip")
    public VipPoint sVipPoint;
    private String sevenDaysIn;
    private String sevenDaysOut;
    @Expose
    @SerializedName("focusFireTag")
    private TextIconSessionTag textIconTag;
    @Expose
    @SerializedName("sessionTagLogMap")
    private String sessionTagLogMapData;
    @Expose
    @SerializedName("sparkBtn")
    private FireOutData sparkBtn;
    @Expose
    @SerializedName("avatarFrameInfos")
    private AvatarFrame avatarFrame;

    public UniformLabelsBean getP2PUniformLabels() {
        return p2pUniformLabels;
    }

    public int getOnlineStatus() {
        return onlineStatus;
    }

    public String getSevenDaysOut() {
        return sevenDaysOut;
    }

    public String getSevenDaysIn() {
        return sevenDaysIn;
    }

    public int getDistance() {
        return distance;
    }

    public void setDistance(int distance) {
        this.distance = distance;
    }

    public void setSevenDaysOut(String sevenDaysOut) {
        this.sevenDaysOut = sevenDaysOut;
    }

    public void setSevenDaysIn(String sevenDaysIn) {
        this.sevenDaysIn = sevenDaysIn;
    }

    public String getOnlineMsgTime() {
        return onlineMsgTime;
    }

    public void setOnlineMsgTime(String onlineMsgTime) {
        this.onlineMsgTime = onlineMsgTime;
    }

    public int getIsMarkSpray() {
        return isMarkSpray;
    }

    public void setIsMarkSpray(int isMarkSpray) {
        this.isMarkSpray = isMarkSpray;
    }

    public void setOnlineStatus(int onlineStatus) {
        this.onlineStatus = onlineStatus;
    }

    public long getLocTimesec() {
        return locTimesec;
    }

    public void setLocTimesec(long locTimesec) {
        this.locTimesec = locTimesec;
    }

    public UserOnlineTag getUserOnlineTag() {
        return userOnlineTag;
    }

    public void setUserOnlineTag(UserOnlineTag userOnlineTag) {
        this.userOnlineTag = userOnlineTag;
    }

    public ProfileRealAuth getRealAuth() {
        return realAuth;
    }

    public void setRealAuth(ProfileRealAuth realAuth) {
        this.realAuth = realAuth;
    }

    public IntimacyTag getIntimacy() {
        return intimacy;
    }

    public void setIntimacy(IntimacyTag intimacy) {
        this.intimacy = intimacy;
    }

    public int getIntimacyLevel() {
        return intimacy != null ? intimacy.level : 0;
    }

    public String getIntimacyIcon() {
        return intimacy != null ? intimacy.icon : "";
    }

    public long getRelationBuildTime() {
        return relationBuildTime;
    }

    public void setRelationBuildTime(long relationBuildTime) {
        this.relationBuildTime = relationBuildTime;
    }

    public long getRelationLastChatTime() {
        return relationLastChatTime;
    }

    public void setRelationLastChatTime(long relationLastChatTime) {
        this.relationLastChatTime = relationLastChatTime;
    }

    public ChatTag getChatTag() {
        return chatTag;
    }

    public void setChatTag(ChatTag chatTag) {
        this.chatTag = chatTag;
    }

    public int getOfficialOperation() {
        return officialOperation;
    }

    public void setOfficialOperation(int officialOperation) {
        this.officialOperation = officialOperation;
    }

    public VipPoint getVipPoint() {
        return mVipPoint;
    }

    public void setVipPoint(VipPoint vipPoint) {
        mVipPoint = vipPoint;
    }

    public VipPoint getsVipPoint() {
        return sVipPoint;
    }

    public void setsVipPoint(VipPoint sVipPoint) {
        this.sVipPoint = sVipPoint;
    }

    public List<BaseBadgeModel> getCellTagUniformLabels() {
        return cellTagUniformLabels;
    }

    public void setCellTagUniformLabels(List<BaseBadgeModel> cellTagUniformLabels) {
        this.cellTagUniformLabels = cellTagUniformLabels;
    }

    public TextIconSessionTag getTextIconTag() {
        return textIconTag;
    }

    public void setTextIconTag(TextIconSessionTag textIconTag) {
        this.textIconTag = textIconTag;
    }

    public String getSessionTagLogMapData() {
        return sessionTagLogMapData;
    }

    public void setSessionTagLogMapData(String sessionTagLogMapData) {
        this.sessionTagLogMapData = sessionTagLogMapData;
    }

    public AvatarFrame getAvatarFrame() {
        return avatarFrame;
    }

    public void setAvatarFrame(AvatarFrame avatarFrame) {
        this.avatarFrame = avatarFrame;
    }

    public static class IntimacyTag implements Serializable {

        @Expose
        int level;

        @Expose
        String icon;

        public int getLevel() {
            return level;
        }

        public void setLevel(int level) {
            this.level = level;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }
    }

    public FireOutData getSparkBtn() {
        return sparkBtn;
    }
}
