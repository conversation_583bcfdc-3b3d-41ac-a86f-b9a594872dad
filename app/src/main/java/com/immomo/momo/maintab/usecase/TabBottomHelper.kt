package com.immomo.momo.maintab.usecase

import android.view.View
import androidx.annotation.LayoutRes
import androidx.annotation.UiThread
import com.immomo.momo.home.manager.FrameConfigManager
import com.immomo.momo.maingroup.manager.FrameConfigConst
import com.immomo.momo.maingroup.manager.FrameConfigConst.BottomSvg.ITEM_CHAT_DARK
import com.immomo.momo.maingroup.manager.FrameConfigConst.BottomSvg.ITEM_CHAT_LIGHT
import com.immomo.momo.maingroup.manager.FrameConfigConst.BottomSvg.ITEM_FOLLOW_DARK
import com.immomo.momo.maingroup.manager.FrameConfigConst.BottomSvg.ITEM_FOLLOW_LIGHT
import com.immomo.momo.maingroup.manager.FrameConfigConst.BottomSvg.ITEM_LIVE_DARK
import com.immomo.momo.maingroup.manager.FrameConfigConst.BottomSvg.ITEM_LIVE_LIGHT
import com.immomo.momo.maingroup.manager.FrameConfigConst.BottomSvg.ITEM_LIVE_SHENGDIAN
import com.immomo.momo.maingroup.manager.FrameConfigConst.BottomSvg.ITEM_MAIN_DARK
import com.immomo.momo.maingroup.manager.FrameConfigConst.BottomSvg.ITEM_MAIN_LIGHT
import com.immomo.momo.maingroup.manager.FrameConfigConst.BottomSvg.ITEM_MAIN_WORLDCUP
import com.immomo.momo.maingroup.manager.FrameConfigConst.BottomSvg.ITEM_MORE_DARK
import com.immomo.momo.maingroup.manager.FrameConfigConst.BottomSvg.ITEM_MORE_LIGHT
import com.immomo.momo.maingroup.manager.FrameConfigConst.BottomSvg.ITEM_UNIVERSE_DARK
import com.immomo.momo.maingroup.manager.FrameConfigConst.BottomSvg.ITEM_UNIVERSE_LIGHT
import com.immomo.momo.maingroup.manager.FrameConfigConst.BottomSvg.ITEM_UNIVERSE_NOR_DARK
import com.immomo.momo.maingroup.manager.FrameConfigConst.BottomSvg.ITEM_UNIVERSE_NOR_LIGHT
import com.immomo.momo.maingroup.manager.TabConfigModel
import com.immomo.momo.maingroup.manager.TabFrameConfig
import com.immomo.momo.maintab.view.TabItemView
import com.immomo.momo.util.StringUtils

class TabBottomHelper {
    companion object {
        @JvmField
        val svgaPathMap = HashMap<String, String>().apply {
            put(ITEM_MAIN_DARK, "item_home_dark.svga")
            put(ITEM_CHAT_DARK, "item_chat_dark.svga")
            put(ITEM_LIVE_DARK, "item_live_dark.svga")
            put(ITEM_FOLLOW_DARK, "item_follow_dark.svga")
            put(ITEM_MORE_DARK, "item_more_dark.svga")
            put(ITEM_UNIVERSE_DARK, "item_universe_dark.svga")
            put(ITEM_UNIVERSE_NOR_DARK, "item_universe_nor_dark.svga")
            put(ITEM_MAIN_WORLDCUP, "item_home_worldcup.svga")
            put(ITEM_LIVE_SHENGDIAN, "item_live_shengDian.svga")

            put(ITEM_MAIN_LIGHT, "home_tab_nearby.svga")
            put(ITEM_CHAT_LIGHT, "item_chat_light.svga")
            put(ITEM_LIVE_LIGHT, "item_live_light.svga")
            put(ITEM_FOLLOW_LIGHT, "item_follow_light.svga")
            put(ITEM_MORE_LIGHT, "item_more_light.svga")
            put(ITEM_UNIVERSE_LIGHT, "item_universe_light.svga")
            put(ITEM_UNIVERSE_NOR_LIGHT, "item_universe_nor_light.svga")
        }

    }

    var itemMap = mutableMapOf<String, TabItemView>()

    @UiThread
    fun initViews(homeKey: String, itemView: TabItemView) {
        itemMap[homeKey] = itemView
    }

    fun updateTab(homeKey: String, tabFrameConfig: TabFrameConfig, defaultConfig: TabConfigModel) {
        itemMap.forEach {
            if (homeKey == it.key) {
                if (StringUtils.equalsNonNull(it.value.currentUrl, tabFrameConfig.tabSvga)) {
                    it.value.updateSvgaImage(tabFrameConfig)
                } else {
                    it.value.updateView(tabFrameConfig, playSvg = true)
                }
                return@forEach
            }
            when (it.key) {
                FrameConfigConst.KEY_HOME_MAIN -> it.value.updateView(defaultConfig.mainFrame)
                FrameConfigConst.KEY_HOME_LIVE -> it.value.updateView(defaultConfig.liveFrame)
                FrameConfigConst.KEY_HOME_CHAT -> it.value.updateView(defaultConfig.chatFrame)
                FrameConfigConst.KEY_HOME_TEMP -> it.value.updateView(defaultConfig.tempFrame)
                FrameConfigConst.KEY_HOME_MORE -> it.value.updateView(defaultConfig.moreFrame)
            }
        }
    }

    //更新当前选中的和其它tab的文案
    fun updateTabText() {
        itemMap.forEach {
            it.value.updateTabText(FrameConfigManager.getHomeConfigByKey(it.key)?.tabName ?: "")
        }
    }


    fun getTabItemView(homeKey: String): TabItemView? {
        itemMap.forEach {
            if (homeKey == it.key) {
                return it.value
            }
        }
        return null
    }

    fun setItemTempView(homeKey: String, @LayoutRes res: Int) {
        itemMap[homeKey]?.setTmpLayout(res)
    }

    fun setItemTempView(homeKey: String, view: View) {
        itemMap[homeKey]?.setTmpLayout(view)
    }

    fun onDestroy() {
        itemMap.forEach { (_, view) ->
            view.release()
        }
        itemMap.clear()
    }
}