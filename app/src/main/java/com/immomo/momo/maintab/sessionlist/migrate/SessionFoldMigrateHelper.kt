package com.immomo.momo.maintab.sessionlist.migrate

import com.cosmos.mdlog.MDLog
import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.momo.maintab.session2.SessionAppConfigV1
import com.immomo.momo.maintab.session2.apt.SessionAppConfigV2Getter
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.defs.FoldSessionDefinition
import com.immomo.momo.maintab.session2.defs.FoldSessionParam
import com.immomo.momo.maintab.session2.defs.UserChatContent
import com.immomo.momo.maintab.session2.utils.SessionFoldHelper
import com.immomo.momo.messages.service.SingleMsgServiceV2
import com.immomo.momo.mulog.MULogConstants
import com.immomo.momo.mulog.MURealtimeLog
import com.immomo.momo.mulog.pair.MUPairItem
import com.immomo.momo.service.bean.FolderType
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.Session
import com.immomo.momo.util.DateUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import java.util.Date

/**
 * Created by huang.liangjie on 2024/8/4.
 *
 * Momo Tech 2011-2024 © All Rights Reserved.
 */
class SessionFoldMigrateHelper {
    private var migrateJob: Job? = null
    fun destroy() {
        migrateJob?.cancel()
    }

    fun migrate() {
        if (migrateJob?.isActive == true || migrateJob?.isCompleted == true) return
        migrateJob = CoroutineScope(SupervisorJob() + MMDispatchers.User).launch {
            try {
                if (SessionFoldHelper.isBubbleTest) {
                    SessionManager.get().updateSession(FoldSessionDefinition.KEY_FOLDED_MSG) {
                        it.unreadMessageCount = 0
                        true
                    }
                }
                if (SessionAppConfigV2Getter.get().msgFoldScanSwitch() == 0) {
                    return@launch
                }
                if (SessionFoldHelper.hasScanedToday()) {
                    return@launch
                }
                val sessionManager = SessionManager.get()
                val id = System.currentTimeMillis().toString()
                logStart("hitTest", id)
                val limitConfig = SessionAppConfigV2Getter.get().msgFoldScanCount()
                var inTimes = 0
                var outTimes = 0
                val allSessionMetas = sessionManager.findAllSessions(
                    filter = {
                        it.sessionKey.startsWith("u") && !it.markedAsDeleted
                    }
                ) { it }
                val allSessions = allSessionMetas.asSequence()

                //执行折叠逻辑
                allSessions
                    .filter { it.foldType != FolderType.FOLDED_MSG } //已经是折叠的，忽略
//                        .filter { it.foldType == FolderType.Default } //只针对普通的进行扫描，暂时不改
                    .mapNotNull {
                        sessionManager.getSession(
                            it.sessionKey,
                            false
                        ).first
                    }
                    .filter { it.isSticky.not() } //忽略置顶的
                    .filter { it.unreadMessageCount <= 1 } //未读是0的
                    .take(limitConfig)
                    .filter { entity ->
                        inTimes++
                        onlyMessageShouldFold(entity)?.also { msg ->
                            FoldSessionParam(
                                type = Session.ID.FOLDED_MSG,
                                userName = sessionManager.getSession(
                                    SessionKey("u", msg.remoteId).value,
                                    false
                                ).first?.content?.castOrNull<UserChatContent>()?.userName.safe(),
                                lastMessageId = msg.msgId,
                                lastMsgTime = msg.timestampMillis,
                                message = msg,
                                unreadCount = entity.unreadMessageCount
                            ).also { sessionManager.syncSession(it, true) }
                        } != null
                    }.forEach { entity ->
                        sessionManager.updateSession(
                            SessionKey.fromString(entity.sessionKey), true
                        ) {
                            it.foldType = FolderType.FOLDED_MSG
                            true
                        }
                    }
                //执行拿出逻辑
                allSessions.filter {
                    it.foldType == FolderType.FOLDED_MSG
                }.mapNotNull {
                    sessionManager.getSession(
                        it.sessionKey,
                        false
                    ).first
                }.filter {
                    outTimes++
                    it.isSticky || onlyMessageShouldFold(it) == null
                }.forEach {
                    sessionManager.updateSession(
                        SessionKey.fromString(it.sessionKey), true
                    ) {
                        MDLog.d(SessionFoldHelper.TAG, "移出折叠 ${it.sessionId}")
                        it.foldType = FolderType.Default
                        true
                    }
                }
                sessionManager.syncSession(
                    FoldSessionParam(
                        FoldSessionDefinition.KEY_FOLDED_MSG.id,
                        null, null, null, null, null
                    ), false
                )
                logEnd(
                    "hitTest", id,
                    "allSession:${allSessionMetas.size},inTimes:$inTimes,outTimes:$outTimes"
                )
            } catch (_: Exception) {

            }
        }
    }

    //如果满足折叠条件，返回唯一的message，否则返回null
    private fun onlyMessageShouldFold(entity: SessionEntity): Message? {
        return kotlin.runCatching {
            //查两条
            SingleMsgServiceV2.service.findNormalMessageBy(
                remoteId = entity.sessionId,
                largerThan = false,
                size = 2,
                newestMsg = null,
                isInit = false
            )
        }.fold({ it }) {
            MDLog.e(SessionFoldHelper.TAG, it.toString())
            null
        }?.takeIf {
            //如果查出来为0条，忽略，>1说明回复过。
            it.size == 1
        }?.first()?.takeIf {
            SessionFoldHelper.shouldFold(it)
        }
    }

    private fun logStart(type: String, id: String) {
        log("start", type, id)
    }

    private fun logEnd(type: String, id: String, vararg info: String) {
        log("end", type, id, *info)
    }

    private fun log(action: String, type: String, id: String, vararg args: String) {
        MURealtimeLog.business(MULogConstants.BUSINESS_MOMO_BASIC)
            .secondLBusiness("Message")
            .thirdLBusiness("sessionTrace")
            .addBodyItem(MUPairItem.action(action))
            .addBodyItem(MUPairItem.typeStr(type))
            .addBodyItem(MUPairItem.id(id))
            .addBodyItem(MUPairItem.info(args.joinToString()))
            .addBodyItem(MUPairItem.msg(DateUtil.formateDateTime(Date())))
            .commit()
    }
}
