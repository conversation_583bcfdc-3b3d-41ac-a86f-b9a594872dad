package com.immomo.momo.maintab.sessionlist.util;

import android.os.Looper;

/**
 * Created by chenxin on 2019/5/24.
 */
public abstract class LiveTimerHelper<T> extends MessageDispatchTimerHelper {

    T pushItem;

    public LiveTimerHelper(long updateInterval) {
        super(updateInterval);
    }

    public LiveTimerHelper(long updateInterval, Looper looper) {
        super(updateInterval, looper);
    }

    public final void handleUpdate() {
        if (this.pushItem != null) {
            this.pushData(this.pushItem);
            this.pushItem = null;
        }
    }

    public void addData(T item) {
        if (null != item) {
            if (this.pushItem == null) {
                this.notifyTimer();
            }

            this.pushItem = item;
        }
    }

    public T getData() {
        return this.pushItem;
    }

    public void reset() {
        super.reset();
        this.pushItem = null;
    }

    public abstract void pushData(T data);

}
