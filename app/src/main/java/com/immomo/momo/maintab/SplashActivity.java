package com.immomo.momo.maintab;

import static com.immomo.momo.maintab.MaintabActivity.KEY_SON_TABINDEX;
import static com.immomo.momo.maintab.MaintabActivity.KEY_SON_BUSINESS;
import static com.immomo.momo.maintab.MaintabActivity.KEY_SOURCE;
import static com.immomo.momo.maintab.MaintabActivity.KEY_TABINDEX;
import static com.immomo.momo.newaccount.push.PushGotoProcessor.hideen_hometop;

import android.content.Intent;
import android.os.Bundle;

import com.immomo.android.AppUseConstants;
import com.immomo.framework.base.BaseActivity;
import com.immomo.framework.statistics.pagespeed.AutoSpeed;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.businessmodel.usermodel.IUserModel;
import com.immomo.momo.homepage.fragment.HomePageFragment;
import com.immomo.momo.maingroup.manager.FrameConfigConst;
import com.immomo.momo.mvp.common.model.ModelManager;
import com.immomo.momo.service.bean.UserProfileConfigs;

import java.util.List;

public class SplashActivity extends BaseActivity implements SplashPresenter.OnSkipClickInterface {
    public static final String KEY_FROM_MAINTAB = "key_from_maintab";
    public static final String KEY_NEED_SHOW_CONTACT = "key_need_show_contact";

    private boolean isFromMainTab = false;
    private boolean gotoNearbyPeople = false;
    private boolean isNeedShowContactGuide = false;
    private boolean isFromThirdRegister = false;

    private SplashPresenter splashPresenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (thisActivity() != null) {
            try {
                NotchUtils.INSTANCE.openFullScreen(thisActivity());
            } catch (Throwable e) {
                NotchUtils.INSTANCE.uploadSplashLog(e.getMessage());
            }
        }
        setContentView(R.layout.activity_splash);

        isFromMainTab = getIntent().getBooleanExtra(KEY_FROM_MAINTAB, false);
        gotoNearbyPeople = getIntent().getBooleanExtra(AppUseConstants.GOTO_NEARBY_PEOPLE, false);
        isFromThirdRegister = getIntent().getBooleanExtra(AppUseConstants.IS_FROM_THIRD_REGISTER, false);
        isNeedShowContactGuide = getIntent().getBooleanExtra(KEY_NEED_SHOW_CONTACT, false);
        showSplashAndChangeActivity();
    }

    @Override
    protected void onResume() {
        super.onResume();
        AutoSpeed.getInstance().setNormalLaunch(false);
    }

    private void showSplashAndChangeActivity() {
        List<String> allTypes = null;
        UserProfileConfigs profileConfigs = ModelManager.getModel(IUserModel.class).getShopSetting();
        if (profileConfigs != null) {
            allTypes = profileConfigs.allowsAdTypes;
        }
        if (SplashPresenter.isShowSplash(allTypes)) {
            splashPresenter = new SplashPresenter(this, allTypes);
            splashPresenter.setSkipCallback(SplashActivity.this);
            splashPresenter.show();
            MomoMainThreadExecutor.post(new Runnable() {
                @Override
                public void run() {
                    if (isDestroyed()) {
                        return;
                    }
                    if (!isFromMainTab) {
                        AppInitializer.initAppRelated(MomoKit.getApp());
                    }
                }
            });
        } else {
            if (!isFromMainTab) {
                AppInitializer.initAppRelated(MomoKit.getApp());
            }

            gotoMainTab(true);
        }
    }

    private boolean isTranslateStart = false;

    private void gotoMainTab(boolean fromSdk) {
        if (isTranslateStart) return;
        isTranslateStart = true;

        if (isFromMainTab) {
            Intent intent = new Intent();
            intent.putExtra(KEY_NEED_SHOW_CONTACT, isNeedShowContactGuide);
            setResult(RESULT_OK, intent);
            finish();
        } else {
            Intent inIntent = getIntent();
            Intent intent = new Intent(getApplicationContext(), MaintabActivity.class);
            intent.putExtra(AppUseConstants.IS_FROM_THIRD_REGISTER, isFromThirdRegister);
            if (gotoNearbyPeople) {
                intent.putExtra(KEY_TABINDEX, MaintabActivity.TAB_FEED);
                intent.putExtra(KEY_SOURCE, HomePageFragment.HOMEPAGE_FRAGMENT);
                intent.putExtra(hideen_hometop, 0);
                intent.putExtra(KEY_SON_TABINDEX, HomePageFragment.TAB_NEARBY_PEOPLE);
                intent.putExtra(KEY_SON_BUSINESS, FrameConfigConst.FRAME_NEARBY_PEOPLE);
            }
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK|Intent.FLAG_ACTIVITY_CLEAR_TOP);
            intent.putExtra(MaintabActivity.KEY_CALL_FROM_SDK, fromSdk);
            if (inIntent != null) {
                if (inIntent.getBooleanExtra(MaintabActivity.KEY_NEED_RECREATE, false)) {
                    intent.putExtra(MaintabActivity.KEY_NEED_RECREATE, true);
                }
                intent.putExtra(MaintabActivity.KEY_NEED_GET_PROFILE, inIntent.getBooleanExtra(MaintabActivity.KEY_NEED_GET_PROFILE, true));
            }

            getApplicationContext().startActivity(intent);
            finish();
        }
    }

    public boolean isShowingAd() {
        if (splashPresenter != null) {
            return splashPresenter.isPlayingAd();
        } else {
            return false;
        }
    }

    @Override
    public void onSkipClicked() {
        gotoMainTab(true);
    }

    @Override
    public void onShowCompleted() {
        gotoMainTab(true);
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (splashPresenter != null) {
            splashPresenter.disappear();
        }
    }

    @Override
    protected boolean isSupportSwipeBack() {
        return false;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        if (splashPresenter != null) {
            splashPresenter.destroy();
        }
    }

    @Override
    public void onBackPressed() {
        // 禁用开屏的问题回退按键
        /*  super.onBackPressed();*/
    }
}
