package com.immomo.momo.maintab.session2

import android.util.Log
import androidx.annotation.CallSuper
import com.immomo.android.module.specific.data.api.response.kobaltMoshi
import com.immomo.momo.maintab.session2.data.database.SessionContent
import com.immomo.momo.maintab.session2.data.database.SessionEntity
import com.immomo.momo.maintab.session2.data.database.content
import com.immomo.momo.maintab.session2.data.manager.SessionKey
import com.immomo.momo.maintab.session2.data.mapper.toBaseInfo
import com.immomo.momo.maintab.session2.domain.model.BaseSessionInfo
import com.immomo.momo.maintab.session2.domain.model.SessionModel
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.sessions.SessionService
import com.immomo.momo.util.StringUtils

interface HasSessionKey {
    val sessionKey: SessionKey?
}

sealed class SessionUpdateBundle : HasSessionKey {
    class ClearUnread(override val sessionKey: SessionKey) : SessionUpdateBundle()
    class ReloadInfo(override val sessionKey: SessionKey) : SessionUpdateBundle()
}

fun SessionEntity.sessionKey(): SessionKey {
    return SessionKey(sessionType, sessionId)
}
interface SessionContentParser<Data : SessionContent> {
    fun serialize(data: Data): String?

    fun deserialize(raw: String?): Data?

    companion object {
        inline fun <reified Data : SessionContent> moshiParser() =
            object : SessionContentParser<Data> {
                override fun serialize(data: Data): String? {
                    return kobaltMoshi.adapter(Data::class.java).toJson(data)
                }

                override fun deserialize(raw: String?): Data? {
                    return raw?.let { kobaltMoshi.adapter(Data::class.java).fromJson(raw) }
                        ?: Data::class.java.newInstance()
                }
            }
    }
}

abstract class SessionDefinition<Data : SessionContent, Model : SessionModel>(
    val type: String,
    val contentParser: SessionContentParser<Data>
) {
    open fun createSession(id: String): SessionEntity = SessionEntity(createContent()).also {
        it.sessionKey = SessionKey(type, id).value
        it.sessionId = id
        it.sessionType = type
    }

    abstract fun createContent(): Data

    fun determineKey(data: Any?) = generateId(data)?.let { SessionKey(type, it) }

    open fun generateId(data: Any?): String? = null

    /**
     * return true if changed
     */
    open fun validateSession(session: SessionEntity, canRemoveUnreliable: Boolean): Boolean {
        return false
    }

    @CallSuper
    open fun syncSession(session: SessionEntity, data: Any?): Boolean {
        return data is SessionUpdateBundle && syncSessionWithBundle(session, data)
    }

    fun getDisplayName(lastMessage: Message): String = when {
        StringUtils.notEmpty(lastMessage.nickName) -> lastMessage.nickName + ": "
        lastMessage.owner != null -> lastMessage.owner.displayName + ": "
        StringUtils.notEmpty(lastMessage.username) -> lastMessage.username + ": "
        else -> ""
    }

    private fun syncSessionWithBundle(session: SessionEntity, data: SessionUpdateBundle): Boolean =
        when (data) {
            is SessionUpdateBundle.ClearUnread -> {
                session.unreadMessageCount = 0
                session.silentMessageCount = 0
                onClearUnread(session)
                true
            }
            is SessionUpdateBundle.ReloadInfo -> {
                onReloadInfo(data.sessionKey.id, session, true)
                true
            }
        }

    open fun onClearUnread(session: SessionEntity) {}

    open fun onReloadInfo(id: String, session: SessionEntity, forceReload: Boolean) {}

    open fun isTransformCompatible(oldSessionType: Int, oldSessionId: String): String? = null

    open fun transformOldSession(id: String): SessionEntity? = null

    @Deprecated("仅用于公测包，兼容覆盖安装的情况")
    open fun saveOldSession(session: SessionEntity) {}

    open fun removeOldSession(id: String) {
        SessionService.getInstance().deleteSession(id)
    }

    fun entityToModel(session: SessionEntity): Model? {
        val content = session.content as? Data ?: return null
        return content.contentToModel(session.toBaseInfo())
    }

    abstract fun Data.contentToModel(baseInfo: BaseSessionInfo): Model
}
