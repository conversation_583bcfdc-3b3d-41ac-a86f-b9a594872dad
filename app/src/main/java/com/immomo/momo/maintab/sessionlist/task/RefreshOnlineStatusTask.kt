package com.immomo.momo.maintab.sessionlist.task

import android.text.TextUtils
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.momo.globalevent.GlobalEventManager
import com.immomo.momo.maintab.config.AvatarFramePageConfigV2
import com.immomo.momo.maintab.model.SessionOnlineBean
import com.immomo.momo.maintab.session2.data.manager.SessionKey.Companion.fromString
import com.immomo.momo.maintab.session2.data.util.SessionTabTestHelper.Companion.getSessionTime
import com.immomo.momo.maintab.session2.defs.MaskChatSessionDefinition
import com.immomo.momo.maintab.session2.defs.UserChatSessionDefinition
import com.immomo.momo.protocol.http.UserApi
import com.immomo.momo.service.bean.User
import com.immomo.momo.service.sessions.SessionUserCache
import com.immomo.momo.service.user.UserService
import com.immomo.momo.util.StringUtils
import java.util.Date
import java.util.concurrent.CopyOnWriteArraySet

/**
 * CREATED BY liu.chong
 * AT 2024/7/11
 */

//</editor-fold>
//去刷新用户状态。
open class RefreshOnlineStatusTask(needRefreshSet: MutableSet<String?>) :
    MomoTaskExecutor.Task<Any?, Any?, SessionOnlineBean>("") {

        companion object {
            const val ON_USER_LOCATION_DATA_CHANGED = "on_user_location_data_changed"
            const val KEY_MMID_USER_LOCATION_DATA_CHANGED = "key_mmid_user_location_data_changed"
        }

    private val refreshSet = CopyOnWriteArraySet<String>()
    private val userList = ArrayList<String?>()

    init {
        refreshSet.addAll(needRefreshSet)
        needRefreshSet.clear()
    }

    override fun executeTask(vararg objects: Any?): SessionOnlineBean {
        userList.clear()
        for (sessionId in refreshSet) {
            val sessionKey = fromString(sessionId!!)
            if (!TextUtils.isEmpty(sessionKey.id)) {
                if (UserChatSessionDefinition.Type == sessionKey.type || MaskChatSessionDefinition.Type == sessionKey.type) {
                    userList.add(sessionKey.id)
                }
            }
        }

        var uids: String? = null
        val onlineBean: SessionOnlineBean
        if (userList.size > 0) {
            uids = StringUtils.join(userList, ",")
            onlineBean = UserApi.getInstance().refreshUsersOnlineStatus(uids, getSessionTime())
            extractUserAndSave(onlineBean)
        } else {
            onlineBean = SessionOnlineBean()
            onlineBean.userOnlineList = hashMapOf()
        }
        return onlineBean
    }

    private fun extractUserAndSave(onlineBean: SessionOnlineBean?) {
        val map = onlineBean?.userOnlineList ?: return
        val toInsert: MutableList<User> = ArrayList()
        for ((momoid, model) in map) {
            if (TextUtils.isEmpty(momoid) || model == null) {
                continue
            }
            val remoteUser = SessionUserCache.getUser(momoid)
            if (remoteUser != null) {
                remoteUser.userOnlineStatus = model.onlineStatus
                remoteUser.isMarkSpray = model.isMarkSpray
                remoteUser.setLocationTimestamp(Date(model.locTimesec * 1000L))
                remoteUser.setOnlineTag(model.userOnlineTag)
                remoteUser.realAuth = model.realAuth
                remoteUser.setIntimacyLevel(model.intimacyLevel)
                remoteUser.setIntimacyIcon(model.intimacyIcon)
                remoteUser.lastStatusRefreshTime = System.currentTimeMillis()
                remoteUser.relationBuildTime = model.relationBuildTime * 1000L
                remoteUser.relationLastChatTime = model.relationLastChatTime * 1000L
                remoteUser.chatTag = model.chatTag
                remoteUser.p2PUniformLabel = model.p2PUniformLabels
                remoteUser.officialOperation = model.getOfficialOperation()
                remoteUser.fireIcon =  model.sparkBtn?.btnIcon
                remoteUser.fireSign =  model.sparkBtn?.emotionSign
                remoteUser.fireIconDark =  model.sparkBtn?.btnIconDark
                if (!onlineBean.isAvatarDataChanged) {
                    onlineBean.isAvatarDataChanged = remoteUser.safeAvatarFrame.safe() != model.avatarFrame?.safeAvatarFrame.safe() // 判断头像数据是否有变化
                }
                remoteUser.avatarFrame =  model.avatarFrame
                model.vipPoint?.also {//这里不用关心svip，所以不解析了
                    remoteUser.setVipLevel(it.active_level)
                    remoteUser.setVipYear(it.year)
                    remoteUser.setVipActivityLevel(it.active_level)
                    remoteUser.isVip = it.valid > 0
                }
                toInsert.add(remoteUser)
                SessionUserCache.insert(momoid,remoteUser)
            }
        }
        UserService.getInstance().saveUserSimple(toInsert)
    }

    override fun onTaskSuccess(onlineBean: SessionOnlineBean) {
        super.onTaskSuccess(onlineBean)
        //删除刷新列表的多余的数据
        refreshSet.clear()
        checkAvatarTip(onlineBean)
    }

    private fun checkAvatarTip(onlineBean: SessionOnlineBean) {
        onlineBean.userOnlineList.takeIf { it.size > 0 && onlineBean.isAvatarDataChanged }?.also {
            val mmids = it.keys.toList()
            val data = hashMapOf<String, Any>(KEY_MMID_USER_LOCATION_DATA_CHANGED to mmids)
            GlobalEventManager.getInstance().sendEvent(
                GlobalEventManager.Event(ON_USER_LOCATION_DATA_CHANGED).msg(data)
                    .dst(GlobalEventManager.EVN_NATIVE).src(GlobalEventManager.EVN_NATIVE)
            )
        }
    }

    override fun onTaskError(e: Exception) {
        // super.onTaskError(e)
    }
}
