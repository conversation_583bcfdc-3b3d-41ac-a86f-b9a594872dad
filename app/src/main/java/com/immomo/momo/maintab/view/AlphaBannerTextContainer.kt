package com.immomo.momo.maintab.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.widget.FrameLayout
import android.widget.TextView
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.mmutil.ColorUtils
import com.immomo.momo.R
import com.immomo.momo.maintab.sessionlist.bean.CardDescData
import com.immomo.momo.util.MomoKit

class AlphaBannerTextContainer @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val textView1 by lazy { findViewById<TextView>(R.id.text_back) }
    private val textView2 by lazy { findViewById<TextView>(R.id.text_front) }

    private var items: List<CardDescData> = arrayListOf()

    private var currentIndex = 0

    init {
        LayoutInflater.from(context)
            .inflate(R.layout.layout_session_operator_top_card_desc_banner, this)
    }

    fun setCardDescDatas(items: List<CardDescData>) {
        kotlin.runCatching {
            this.items = items
            currentIndex = 0
            clearTextAnimation()
            if (this.items.size >= 2) {
                textView1.visibility = View.VISIBLE
                textView2.visibility = View.VISIBLE
                startAnimation()
            } else if (this.items.size == 1) {
                textView1.visibility = View.VISIBLE
                textView2.visibility = View.GONE
                setTextViewProperties(textView1, this.items[0])
            } else {
                visibility = GONE
            }
        }
    }

    private fun startAnimation() {
        kotlin.runCatching {
            if (this.items.isEmpty()) return
            val currentItem = this.items[currentIndex]
            val nextIndex = (currentIndex + 1) % this.items.size
            val nextItem = this.items[nextIndex]

            // 设置当前和下一个 TextView 的内容
            setTextViewProperties(textView1, currentItem)
            setTextViewProperties(textView2, nextItem)

            // 设置动画
            val fadeOut = AlphaAnimation(1.0f, 0.0f).apply {
                startOffset = 2000L
                duration = 1000
                fillAfter = true
            }

            val fadeIn = AlphaAnimation(0.0f, 1.0f).apply {
                startOffset = 2000L
                duration = 1000
                fillAfter = true
            }

            fadeOut.setAnimationListener(object : Animation.AnimationListener {
                override fun onAnimationStart(animation: Animation?) {}
                override fun onAnimationEnd(animation: Animation?) {
                    currentIndex = nextIndex
                    startAnimation()
                }

                override fun onAnimationRepeat(animation: Animation?) {}
            })
            clearTextAnimation()
            textView1.startAnimation(fadeOut)
            textView2.startAnimation(fadeIn)
        }
    }

    fun clearTextAnimation() {
        textView1.clearAnimation()
        textView2.clearAnimation()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        clearTextAnimation()
    }

    private fun setTextViewProperties(textView: TextView, item: CardDescData) {
        textView.text = item.text.safe()
        textView.setTextColor(ColorUtils.parseColor(
            if (MomoKit.isDarkMode(textView.context)) item.darkColor.safe() else item.color.safe(),
            R.color.color_323333_to_80f))
        textView.setTypeface(
            null,
            if (item.bold == 1) android.graphics.Typeface.BOLD else android.graphics.Typeface.NORMAL
        )
    }
}
