package com.immomo.momo.maintab.sessionlist;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Pair;

import com.cosmos.mdlog.MDLog;
import com.immomo.mmutil.IOUtils;
import com.immomo.mmutil.log.Log4Android;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoKit;
import com.immomo.momo.common.AppKit;
import com.immomo.momo.maintab.session2.data.manager.SessionManager;
import com.immomo.momo.maintab.session2.data.manager.SessionManagerKt;
import com.immomo.momo.service.bean.Session;
import com.immomo.momo.service.sessions.MessageServiceHelper;
import com.immomo.momo.service.sessions.SessionService;
import com.tencent.wcdb.database.SQLiteDatabase;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;

/**
 * Created by tanjie on 6/27/16.
 */
public class SessionStickyHelper {
    public static final String ChatTopStr = "chat_tops";
    public static final String ChatIDStr = "id";
    public static final String ChatTypeStr = "type";
    public static final String SetTimeStr = "set_time";
    private static SessionStickyHelper mInstance;
    //todo tangyuchun 2030
    private static final long date2100 = new Date(2100, 1, 1).getTime();
    private HashMap<String, Long> sessionOrders = new HashMap<>(5);

    public static SessionStickyHelper getInstance() {
        if (mInstance == null) {
            mInstance = new SessionStickyHelper();
            mInstance.initFromStorage();
        }
        return mInstance;
    }

    public static void release() {
        mInstance = null;
    }

    //todo 优化，从KV读取，比较小
    private void initFromStorage() {
        ObjectInputStream inputStream = null;
        try {
            File file = getStickySettingFile();
            //文件存在才去读取
            if (file.exists()) {
                inputStream = new ObjectInputStream(new FileInputStream(file));
                sessionOrders = (HashMap<String, Long>) inputStream.readObject();
                inputStream.close();
            }
        } catch (Throwable e) {
            Log4Android.getInstance().e(e);
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
//        Log4Android.getInstance().i("jarek sessionOrders:" + sessionOrders);
    }

    //位置在置顶上边
    public static long makeMaxOrderId(long setTime) {
        return setTime + date2100 + date2100;
    }

    public static long makeOrderId(long setTime) {
        return setTime + date2100;
    }

    public void makeSessionSticky(String momoId, ChatType chatType, long setTime) {
        long orderID;
        if (chatType == ChatType.TYPE_SAYHI) {
            orderID = makeMaxOrderId(setTime);
        } else {
            orderID = makeOrderId(setTime);
        }
        String sessionId = MessageServiceHelper.makeSessionID(momoId, chatType);
        sessionOrders.put(sessionId, orderID);
        SessionStickyHelperExtKt.updateSticky(SessionManager.get(), momoId, chatType, true, setTime);
        saveToStorage();
    }

    private File getStickySettingFile() {
        return new File(MomoKit.getContext().getDir("data", Context.MODE_PRIVATE), "stickymap" + AppKit.getAccountManager().getCurrentAccountUserId());
    }

    //todo tangyuchun 应该改写为 KV存储
    private void saveToStorage() {
//        Log4Android.getInstance().i("jarek current sessionOrders:" + sessionOrders);
        ObjectOutputStream outputStream = null;
        try {
            File file = getStickySettingFile();
            outputStream = new ObjectOutputStream(new FileOutputStream(file));
            outputStream.writeObject(sessionOrders);
            outputStream.flush();
        } catch (Throwable e) {
            Log4Android.getInstance().e(e);
        } finally {
            IOUtils.closeQuietly(outputStream);
        }
    }

    public void removeSessionSticky(String originId, SessionStickyHelper.ChatType type) {
        try {
            String sessionID = MessageServiceHelper.makeSessionID(originId, type);
            sessionOrders.remove(sessionID);

            SessionStickyHelperExtKt.updateSticky(SessionManager.get(), originId, type, false, -1);
            saveToStorage();
        } catch (Throwable e) {
            Log4Android.getInstance().e(e);
        }
    }

    public long getOrderID(String sessionID) {
        if (sessionOrders.containsKey(sessionID)) {
            return sessionOrders.get(sessionID);
        } else {
            return -1L;
        }
    }

    public boolean isSessionSticky(String momoId, ChatType type) {
        return sessionOrders.containsKey(MessageServiceHelper.makeSessionID(momoId, type));
    }

    /**
     * 在锤子1机型上实测，5条置顶设置的解析时间共耗费65ms，性能OK
     *
     * @param stickyArray
     */
    public void parseStickySetting(String momoID, JSONArray stickyArray) {
//        long startTime = System.currentTimeMillis();
//        Log4Android.getInstance().i("jarek PARSE sessionOrders:"  + sessionOrders);
        MDLog.d(LogTag.COMMON, "获取置顶聊天 " + stickyArray);
        try {
            if (!TextUtils.equals(AppKit.getAccountManager().getCurrentAccountUserId(), momoID)) {
                return;
            }
        } catch (Exception e) {
            return;
        }
//        Log4Android.getInstance().i("jarek sticky data form Server:" + stickyArray);
        HashSet<String> sessionIds = new HashSet<>(5);
        ArrayList<ServerSticky> serverStickies = new ArrayList<>(5);
        for (int i = 0; i < stickyArray.length(); i++) {
            try {
                JSONObject setObj = stickyArray.getJSONObject(i);
                String momoid = setObj.getString(ChatIDStr);
                int type = setObj.getInt(ChatTypeStr);
                long setTime = setObj.getLong(SetTimeStr);
                ChatType chatType = ChatType.TYPE_CHAT;
                switch (type) {
                    case 0:
                        chatType = ChatType.TYPE_CHAT;
                        break;
                    case 1:
                        chatType = ChatType.TYPE_DISCUSS;
                        break;
                    case 2:
                        chatType = ChatType.TYPE_GROUP;
                        break;
                    case 3:
                        chatType = ChatType.TYPE_VCHAT_SUPER_ROOM;
                        break;
                    case 4:
                        // TODO  目前仅支持SayhiSession，待重构AbsSession的各种type才能支持通用的置顶逻辑。
                        if (!Session.ID.SayhiSession.equals(momoid)) {
                            continue;
                        }
                        chatType = ChatType.TYPE_SAYHI;
                        break;
                    default:
                        continue;
                }
                serverStickies.add(new ServerSticky(momoid, chatType, setTime));
                String sessionId = MessageServiceHelper.makeSessionID(momoid, chatType);
                sessionIds.add(sessionId);
            } catch (Throwable e) {
                Log4Android.getInstance().e(e);
            }
        }
        Set<String> curKeys = new HashSet<>(5);
        curKeys.addAll(sessionOrders.keySet());
        Set<String> inBothSets = new HashSet<>(5);
        for (Iterator<String> iterator = sessionIds.iterator(); iterator.hasNext(); ) {
            String value = iterator.next();
            if (curKeys.contains(value)) {
                inBothSets.add(value);
            }
        }
        sessionIds.removeAll(inBothSets);
        curKeys.removeAll(inBothSets);
        ArrayList<String> curArray = new ArrayList<>(5);
        for (Iterator<String> iterator = curKeys.iterator(); iterator.hasNext(); ) {
            curArray.add(iterator.next());
        }
        if (curArray.size() > 0) {
            for (int i = 0; i < curArray.size(); i++) {
                Pair<String, ChatType> pair = MessageServiceHelper.reverseSessionID(curArray.get(i));
                if (pair != null) {
                    removeSessionSticky(pair.first, pair.second);
                }
            }
        }
        if (sessionIds.size() > 0) {
            for (int i = 0; i < serverStickies.size(); i++) {
                ServerSticky serverSticky = serverStickies.get(i);
                if (sessionIds.contains(MessageServiceHelper.makeSessionID(serverSticky.momoId, serverSticky.chatType))) {
                    makeSessionSticky(serverSticky.momoId, serverSticky.chatType, serverSticky.setTime * 1000L);
                }
            }
        }
    }

    public enum ChatType {
        TYPE_CHAT,
        TYPE_GROUP,
        TYPE_DISCUSS,
        TYPE_VCHAT_SUPER_ROOM,
        TYPE_SAYHI
    }

    private class ServerSticky {
        ServerSticky(String momoId, ChatType chatType, long setTime) {
            this.momoId = momoId;
            this.chatType = chatType;
            this.setTime = setTime;
        }

        String momoId;
        ChatType chatType;
        long setTime;
    }
}

