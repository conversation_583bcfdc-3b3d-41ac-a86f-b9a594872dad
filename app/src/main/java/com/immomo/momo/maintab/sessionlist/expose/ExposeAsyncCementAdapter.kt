package com.immomo.momo.maintab.sessionlist.expose

import android.content.Context
import android.os.Handler
import com.immomo.android.mm.cement2.AsyncCementAdapter
import com.immomo.android.mm.cement2.CementViewHolder
import com.immomo.android.mm.kobalt.domain.extention.castOrNull

/**
 * CREATED BY liu.chong
 * AT 2022/3/14
 */
open class ExposeAsyncCementAdapter(diffHandler: Handler) : Async<PERSON>ement<PERSON>dapter(diffHandler),IExposureAdapter {
    override fun onExposure(context: Context, position: Int, holder: CementViewHolder) {
        getModel(position).castOrNull<IItemModelExposure>()?.onExposure(context, position, holder)
    }
}