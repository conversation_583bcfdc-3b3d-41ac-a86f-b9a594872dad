package com.immomo.momo.maintab.session2.utils

import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.momo.im.GiftSayHiAppConfigV1
import com.immomo.momo.universe.config.UniverseSessionABTest
import com.immomo.momo.maintab.session2.data.manager.SessionMetadata
import com.immomo.momo.maintab.session2.defs.FoldSessionDefinition
import com.immomo.momo.maintab.session2.defs.GiftSayHiSessionDefinition
import com.immomo.momo.maintab.session2.defs.UniverseSessionDefinition
import com.immomo.momo.service.bean.FolderType

/**
 * CREATED BY liu.chong
 * AT 2025/1/16
 */
object SessionFilterHelper {
    fun filterDisplaySessions(metadata: SessionMetadata?, skipFoldCheck: Boolean = false): Boolean {
        if (metadata == null) {
            return false
        }
        if (metadata.markedAsDeleted) {
            return false
        }
        if (!GiftSayHiAppConfigV1.isOpenExp() && GiftSayHiSessionDefinition.SAYHI == metadata.sessionKey) {
            return false
        }
        if (!UniverseSessionABTest.isTest() && UniverseSessionDefinition.UNIVERSE == metadata.sessionKey) {
            return false
        }
        if (!skipFoldCheck && metadata.foldType.safe(FolderType.Default) != FolderType.Default) {
            return false
        }

        return true
    }

    fun filterCountUnreadSessions(sessionKey: String): Boolean {
        if (sessionKey == FoldSessionDefinition.OFFICIAL) {
            return false
        }
        if (sessionKey == FoldSessionDefinition.KEY_GAME_BOX.value) {
            return false
        }
        return true
    }
}