package com.immomo.momo.maintab.session2.presentation.itemmodel.fold

import android.content.Context
import android.view.View
import androidx.viewpager2.widget.ViewPager2
import com.immomo.android.mm.cement2.AsyncCementModel
import com.immomo.android.mm.cement2.CementViewHolder
import com.immomo.android.mm.cement2.IViewHolderCreator
import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.framework.view.indicator.IndicatorView
import com.immomo.momo.R
import com.immomo.momo.gotologic.GotoDispatcher
import com.immomo.momo.maintab.session2.domain.model.FoldNotificationBannersModel
import com.immomo.momo.maintab.session2.presentation.adapter.BannerPagerAdapter
import com.immomo.momo.maintab.sessionlist.expose.IItemModelExposure
import com.immomo.momo.maintab.sessionlist.expose.TopFoldSessionLogHelper
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

/**
 * CREATED BY liu.chong
 * AT 2022/3/10
 */
class NotificationBannerItemModel(state: FoldNotificationBannersModel) :
    AsyncCementModel<FoldNotificationBannersModel, NotificationBannerItemModel.VH>(state),
    IItemModelExposure {
    init {
        id("NotificationBannerItemModel")
    }

    class VH(itemView: View) : CementViewHolder(itemView) {
        val adapter by lazy {
            BannerPagerAdapter<NotificationBannerItemItemModel.VH> { model, holder, position ->
                model.castOrNull<NotificationBannerItemItemModel>()?.also {
                    TopFoldSessionLogHelper.logBannerClick(position, it.model.id, it.model.url)
                    GotoDispatcher.action(it.model.action, holder.itemView.context).execute()
                }
            }
        }

        val pager: ViewPager2 = itemView.findViewById(R.id.notification_banner_pager)
        val indicator: IndicatorView =
            itemView.findViewById(R.id.notification_banner_indicator)

    }

    override fun bindData(holder: VH) {
        super.bindData(holder)

        holder.apply {
            if (pager.adapter == null) {
                pager.adapter = holder.adapter
            }
            pager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                override fun onPageScrollStateChanged(state: Int) {
                    super.onPageScrollStateChanged(state)
                    if (state == ViewPager2.SCROLL_STATE_IDLE) {
                        holder.triggerBannerExposure()
                    }
                }
            })

            adapter.setModels(state.list.map {
                NotificationBannerItemItemModel(it)
            })
            indicator.setupWithViewPager(pager)
            indicator.setIndicatorCount(state.list.size)

        }

    }

    override fun attachedToWindow(holder: VH) {
        super.attachedToWindow(holder)
        if (state.list.size > 1) {//一条不轮播
            coroutineScope.launch {
                while (isActive) {
                    delay(2500)
                    holder.pager.currentItem = holder.pager.currentItem + 1
                }
            }
        }
    }

    override val layoutRes = R.layout.item_fold_notification_banner
    override val viewHolderCreator: IViewHolderCreator<VH> = object : IViewHolderCreator<VH> {
        override fun create(view: View): VH {
            return VH(view)
        }
    }

    override fun onExposure(context: Context, position: Int, holder: CementViewHolder) {
        holder.castOrNull<VH>()?.triggerBannerExposure()
    }

    private fun VH.triggerBannerExposure() {
        adapter.getModel(pager.currentItem)
            .castOrNull<IItemModelExposure>()
            ?.onExposure(pager.context, pager.currentItem % state.list.size, this)
    }
}

