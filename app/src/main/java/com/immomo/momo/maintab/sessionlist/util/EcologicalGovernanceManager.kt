package com.immomo.momo.maintab.sessionlist.util

import android.content.Context
import android.view.View
import com.cosmos.mdlog.MDLog
import com.immomo.framework.base.BaseActivity
import com.immomo.framework.rxjava.interactor.CommonSubscriber
import com.immomo.framework.storage.kv.KV
import com.immomo.kotlin.extern.isNotNullOrEmpty
import com.immomo.mmutil.StringUtils
import com.immomo.mmutil.task.MomoMainThreadExecutor
import com.immomo.momo.LogTag
import com.immomo.momo.common.AppKit
import com.immomo.momo.gotologic.GotoDispatcher
import com.immomo.momo.maintab.sessionlist.bean.EcologicalGovernanceAlertData
import com.immomo.momo.maintab.usecase.EcologicalGovernanceAlertUseCase
import com.immomo.momo.protocol.imjson.IMJMOToken

/**
 * 生态治理管理器
 */
object EcologicalGovernanceManager {

    private var governanceAlertUseCase: EcologicalGovernanceAlertUseCase? = null

    private var ecologicalGovernanceDialog: EcologicalGovernanceDialog? = null

    /**
     * 检查显示弹窗
     */
    fun checkAlertShow(context: Context?) {
        val userStr = KV.getUserStr(IMJMOToken.EcologicalGovernance.KEY_SP_MOMOID, null)
        if (StringUtils.isNotBlank(userStr)) {
            MomoMainThreadExecutor.cancelAllRunnables(hashCode())
            MomoMainThreadExecutor.postDelayed(hashCode(), {
                if (AppKit.getAccountManager().isOnline()) {
                    if (userStr.equals(com.immomo.momo.MomoKit.getCurrentOrGuestMomoId())) {
                        governanceAlertUseCase?.dispose()
                        governanceAlertUseCase = EcologicalGovernanceAlertUseCase()
                        governanceAlertUseCase?.execute(object :
                            CommonSubscriber<EcologicalGovernanceAlertData>() {

                            override fun onNext(data: EcologicalGovernanceAlertData) {
                                showDialog(context, data)
                            }

                            override fun onError(exception: Throwable?) {}
                        })
                    } else {
                        KV.saveUserValue(IMJMOToken.EcologicalGovernance.KEY_SP_MOMOID, null)
                    }
                }
            }, 100)
        }
    }

    private fun showDialog(context: Context?, data: EcologicalGovernanceAlertData) {
        try {
            val baseActivity = context as? BaseActivity ?: return
            KV.saveUserValue(IMJMOToken.EcologicalGovernance.KEY_SP_MOMOID, null)
            if (!baseActivity.isFinishing) {
                ecologicalGovernanceDialog?.cancel()
                ecologicalGovernanceDialog = null
                ecologicalGovernanceDialog = EcologicalGovernanceDialog(
                    context,
                    data.title,
                    data.desc,
                    data.button?.text ?: "",
                    "确定"
                )
                ecologicalGovernanceDialog?.setPosListener(View.OnClickListener {
                    data.button?.action?.isNotNullOrEmpty {
                        GotoDispatcher.action(it, context).execute()
                    }
                })
                data.cover?.isNotNullOrEmpty {
                    ecologicalGovernanceDialog?.setCoverImg(it)
                }
                ecologicalGovernanceDialog?.setOnDismissListener {
                    ecologicalGovernanceDialog = null
                }
                baseActivity.closeDialog()
                ecologicalGovernanceDialog?.show()
            }
        } catch (t: Throwable) {
            MDLog.printErrStackTrace(LogTag.COMMON, t)
        }
    }

    /**
     * 取消弹窗显示
     */
    fun cancelAlert() {
        governanceAlertUseCase?.dispose()
        MomoMainThreadExecutor.cancelAllRunnables(hashCode())
    }

    /**
     * 弹窗是否正在展示
     */
    fun isAlertShowing(): Boolean {
        return ecologicalGovernanceDialog?.isShowing == true || StringUtils.isNotBlank(
            KV.getUserStr(
                IMJMOToken.EcologicalGovernance.KEY_SP_MOMOID,
                null
            )
        )
    }

    fun release() {
        try {
            cancelAlert()
            ecologicalGovernanceDialog?.also {
                if (it.isShowing) {
                    it.dismiss()
                }
            }
            ecologicalGovernanceDialog = null
        } catch (t: Throwable) {
            MDLog.printErrStackTrace(LogTag.COMMON, t)
        }
    }

}