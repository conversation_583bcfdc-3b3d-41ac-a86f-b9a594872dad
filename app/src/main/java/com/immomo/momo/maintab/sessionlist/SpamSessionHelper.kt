package com.immomo.momo.maintab.sessionlist

import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.molive.kotlin.extensions.isNotNullOrEmpty
import com.immomo.momo.MomoKit
import com.immomo.momo.eventbus.DataEvent
import com.immomo.momo.eventbus.EventKeys
import com.immomo.momo.globalevent.GlobalEventManager
import com.immomo.momo.greendao.AppDBUtils
import com.immomo.momo.greendao.SpamSessionEntityDao
import com.immomo.momo.maintab.session2.data.database.SpamSessionEntity
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.message.http.SpamSessionApi
import com.immomo.momo.service.bean.FolderType
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.sessions.SessionService
import de.greenrobot.event.EventBus
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

/**
 * 冷启时，对距离上次会话消息超过24小时的spam用户进行折叠
 * 折叠列表限制20条
 */
class SpamSessionHelper {
    private val STATUS_NO = 0 //没有开始
    private val STATUS_DOING = 1 //进行中
    private val STATUS_SUC = 2 //迁移完成
    private val STATUS_FAIL = 3 //迁移失败
    private var migrateStatus = STATUS_NO

    private var isDestroy = false

    //本次是否有新迁移的session
    private var isIncrement = false

    // 本次是否有新迁移进spam的session
    private var isIncrementAdd = false

    var addParamRemoteIds = mutableListOf<String>()

    companion object {
        //当有新新迁移的session时，入口要展示动画
        @kotlin.jvm.JvmField
        var showAnim = false
        const val SPAM_TO_UPDATE_UNREAD = "spam_to_update_unread"
    }

    init {
        showAnim = false
    }

    private inner class MigrateSessionTask : MomoTaskExecutor.Task<String?, Int?, Int>("") {
        override fun executeTask(vararg params: String?): Int? {
            //需要被校验的最大限制，取session列表的前40个
            val maxCount = 40

            val spamSessionDao =
                AppDBUtils.getInstance()
                    .getDao(SpamSessionEntity::class.java) as SpamSessionEntityDao

            val oldSpamList =
                spamSessionDao.queryBuilder().build().list().map { it.sessionId }.toSet()

            //消息列表中前40个（不包含已经移入二级列表的）
            val sessionList =
                SessionManager.get().getSpamPossible(oldSpamList, maxCount).toMutableList()

            sessionList += oldSpamList
            sessionList += addParamRemoteIds.subtract(sessionList.toSet())
            val sessionStr = sessionList.joinToString(",")

            val spamList = SpamSessionApi.getInstance().getHiSessionIdsFromApi(sessionStr)

            //解除spam的，从二级列表移出到消息列表
            val removeList = oldSpamList.subtract(spamList.list.toSet())
            removeList.forEach {
                spamSessionDao.delete(SpamSessionEntity(it))
            }

            SessionService.getInstance()
                .updateSessionFolderType(FolderType.Default, removeList.toTypedArray())

            spamList.list.isNotNullOrEmpty { it ->
                val limitList = SessionManager.get().getSpamFoldPossible(it.toSet(), 3)

                //删除超过20个限制的session
                val unUseList = spamList.list - limitList.toSet()
                unUseList.forEach {
                    SessionService.getInstance().delete("u_$it", true)
                    if (spamSessionDao.hasKey(SpamSessionEntity(it))) {
                        spamSessionDao.delete(SpamSessionEntity(it))
                    }
                }
                limitList.forEach { id ->
                    spamSessionDao.insertOrReplace(SpamSessionEntity(id))
                }
                SessionService.getInstance()
                    .updateSessionFolderType(FolderType.Spam, limitList.toTypedArray())

                if (removeList.isNotEmpty() || (spamList.list.toSet() - oldSpamList).isNotEmpty() || addParamRemoteIds.isNotEmpty()) {
                    isIncrement = true
                }

                SessionService.getInstance().updateFoldSession(Session.ID.SPAM)

            }

            if (spamList.list.isNullOrEmpty()) {
                isIncrement = removeList.isNotEmpty()
            }

            if (spamList.list.size + removeList.size > oldSpamList.size) {
                isIncrementAdd = true
            }

            //更新未读数
            GlobalEventManager.getInstance().sendEvent(
                GlobalEventManager.Event(SPAM_TO_UPDATE_UNREAD)
                    .dst("native").src("native")
            )

            return migrateStatus
        }

        override fun onTaskSuccess(status: Int) {
            super.onTaskSuccess(status)
            migrateStatus = STATUS_SUC
            addParamRemoteIds.clear()

            if (!isDestroy && isIncrement) {
                showAnim = true
                val it = Intent(SessionListReceiver.ActionReLoadAll)
                LocalBroadcastManager.getInstance(MomoKit.getApp()).sendBroadcast(it)
            }
        }

        override fun onTaskError(e: Exception) {
            migrateStatus = STATUS_FAIL
            addParamRemoteIds.clear()
        }
    }

    fun clearAllSpamMsg() {
        CoroutineScope(SupervisorJob() + MMDispatchers.User).launch {
            val spamSessionDao =
                AppDBUtils.getInstance()
                    .getDao(SpamSessionEntity::class.java) as SpamSessionEntityDao

            val oldSpamList =
                spamSessionDao.queryBuilder().build().list().map { it.sessionId }.toSet()
            oldSpamList.forEach {
                SessionService.getInstance().delete("u_$it", true)
                spamSessionDao.delete(SpamSessionEntity(it))
                SessionService.getInstance()
                    .updateSessionFolderType(FolderType.Default, arrayOf(it))

            }
        }
    }

    private fun hasTag(): Int {
        return hashCode()
    }

    fun migrateToSpam() {
        if (isDestroy || migrateStatus == STATUS_SUC || migrateStatus == STATUS_DOING) {
            return
        }
        migrateStatus = STATUS_DOING
        isIncrementAdd = false
        MomoTaskExecutor.executeUserTask(hasTag(), MigrateSessionTask())
    }

    fun resetMigrateStatus() {
        migrateStatus = STATUS_NO
        isIncrement = false
    }

    fun destroy() {
        isDestroy = true
        MomoTaskExecutor.cancleAllTasksByTag(hasTag())
    }

}