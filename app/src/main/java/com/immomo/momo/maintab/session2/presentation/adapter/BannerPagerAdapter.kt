package com.immomo.momo.maintab.session2.presentation.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.immomo.android.mm.cement2.CementModel
import com.immomo.android.mm.cement2.CementViewHolder
import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.momo.maintab.sessionlist.expose.IExposureAdapter
import com.immomo.momo.maintab.sessionlist.expose.IItemModelExposure

/**
 * CREATED BY liu.chong
 * AT 2022/3/10
 */
class BannerPagerAdapter<HOLDER : CementViewHolder>(val onItemClick: ((cement:CementModel<HOLDER>,holder: HOLDER, position: Int) -> Unit)?) :
    RecyclerView.Adapter<HOLDER>() {
    private var bannerModels = arrayListOf<CementModel<HOLDER>>()

    fun setModels(models: List<CementModel<HOLDER>>) {
        bannerModels.clear()
        bannerModels.addAll(models)
    }

    fun getModel(position: Int): CementModel<HOLDER> {
        return bannerModels[position % bannerModels.size]
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): HOLDER {
        return bannerModels.find { it.layoutRes == viewType }
            ?.let {
                it.viewHolderCreator.create(
                    LayoutInflater.from(parent.context).inflate(it.layoutRes, parent, false)
                )
            } ?: throw IllegalStateException("未找到 $viewType 对应model")
    }

    override fun onBindViewHolder(holder: HOLDER, position: Int) {
        val cementModel = bannerModels[position % (bannerModels.size)]
        cementModel.bindData(holder)
        if (onItemClick != null) {
            holder.itemView.setOnClickListener {
                onItemClick.invoke(cementModel, holder, position % bannerModels.size)
            }
        }
    }

    override fun getItemCount(): Int {
        return Int.MAX_VALUE
    }

    override fun getItemViewType(position: Int): Int {
        return bannerModels[position % (bannerModels.size)].layoutRes
    }

}