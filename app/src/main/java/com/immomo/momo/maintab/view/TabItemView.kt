package com.immomo.momo.maintab.view

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.annotation.LayoutRes
import com.immomo.framework.kotlin.*
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.R
import com.immomo.momo.android.view.HandyTextView
import com.immomo.momo.maingroup.manager.TabFrameConfig
import com.immomo.momo.maintab.usecase.TabBottomHelper
import com.immomo.momo.mvp.maintab.util.DownloadSvgaTabUtil
import com.immomo.momo.util.ColorUtils
import com.immomo.momo.util.MomoKit
import com.immomo.momo.util.StringUtils
import com.immomo.svgaplayer.SVGAAnimListenerAdapter
import com.immomo.svgaplayer.view.MomoSVGAImageView

class TabItemView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    lateinit var itemSvga: MomoSVGAImageView
    lateinit var itemText: HandyTextView
    lateinit var viewRedText: TextView
    lateinit var viewRedImg: ImageView
    lateinit var itemImage: ImageView
    lateinit var labelGroupView: View
    lateinit var tempView: ViewGroup
    lateinit var bubbleLayout: View
    lateinit var redTextLayout: View
    lateinit var bubbleTempImg: ImageView

    var tabText: String = ""
    var currentUrl = ""

    init {
        if (attrs != null) {
            val typedArray =
                context.theme.obtainStyledAttributes(attrs, R.styleable.MainTabItem, 0, 0)
            tabText = typedArray.getString(R.styleable.MainTabItem_tabText).toString()
            typedArray.recycle()
        }
        initView()
    }

    fun initView() {
        val view = LayoutInflater.from(context).inflate(R.layout.common_bottombar_tab, this)
        itemSvga =
            view.findViewById<MomoSVGAImageView>(R.id.iv_tab).apply { clearsAfterStop = false }
        itemText = view.findViewById(R.id.tab_item_tv_label)
        viewRedText = view.findViewById(R.id.tab_item_tv_badge)
        viewRedImg = view.findViewById(R.id.tabitem_iv_badge)
        itemImage = view.findViewById(R.id.civ_avatar)
        tempView = view.findViewById(R.id.temp_view)
        bubbleLayout = view.findViewById(R.id.fl_bubble_container)
        labelGroupView = view.findViewById(R.id.ll_tab_item_tv_label)
        redTextLayout = view.findViewById(R.id.tab_item_notice)
        bubbleTempImg = view.findViewById(R.id.bubble_temp_img)

        itemText.text = tabText
    }

    fun release() {
        itemSvga.stopAnimCompletely()
    }

    fun updateView(
        tabFrameConfig: TabFrameConfig?,
        playSvg: Boolean = false
    ) {
        itemSvga.visibility = VISIBLE
        itemSvga.addCache = false

        tabFrameConfig?.let {
            currentUrl = it.tabSvga
            itemSvga.stopAnimCompletely()
            itemSvga.loadSVGAAnimWithListener(
                getRealPath(currentUrl),
                0,
                object : SVGAAnimListenerAdapter() {
                    override fun onLoadSuccess() {
                        if (playSvg) {
                            itemSvga.startSVGAAnim(getRealPath(currentUrl), 1)
                        } else {
                            itemSvga.stepToFrame(0, false)
                        }

                    }
                },
                false
            )

            if (MomoKit.isAppDarkMode()) {
                itemText.setTextColor(UIUtils.getColor(R.color.color_80fff))
            } else {
                itemText.setTextColor(ColorUtils.parseColor(it.tabTextColor, Color.BLACK))
            }
        }
    }

    fun updateTabText(string: String) {
        if (StringUtils.notEmpty(string)) {
            itemText.text = string
        }
    }

    private fun getRealPath(path: String): String {
        var url = path
        url = if (!path.startsWith("http")) {
            TabBottomHelper.svgaPathMap[path] ?: path
        } else {
            DownloadSvgaTabUtil.getSvgaPath(url)
        }
        return url
    }

    fun updateSvgaImage(tabFrameConfig: TabFrameConfig?) {
        itemSvga.visibility = VISIBLE

        tabFrameConfig?.let {
            currentUrl = it.tabSvga
            if (MomoKit.isAppDarkMode()) {
                itemText.setTextColor(Color.WHITE)
            } else {
                itemText.setTextColor(ColorUtils.parseColor(it.tabTextColor, Color.BLACK))
            }

            itemSvga.loadSVGAAnimWithListener(
                getRealPath(currentUrl),
                0,
                object : SVGAAnimListenerAdapter() {
                    override fun onLoadSuccess() {
                        itemSvga.stepToPercentage(1.0, false)
                    }
                },
                false
            )

        }

    }

    fun updateImage(url: String?, imageType: ImageType) {
        if (StringUtils.isEmpty(url)) {
            return
        }
        ImageLoader.load(url)
            .imageType(imageType)
            .imageTransform(ImageTransform.CircleCrop)
            .listener(object : ImageLoadingListener<Drawable> {
                override fun onLoadCompleted(model: ImageLoaderOptions.Model, resource: Drawable) {
                    itemImage.visibility = VISIBLE
                    itemImage.setImageDrawable(resource)
                    itemSvga.visibility = GONE
                }

                override fun onLoadFailed(
                    model: ImageLoaderOptions.Model,
                    errorDrawable: Drawable?
                ) {
                    hideImage()
                }
            })
            .into()
    }

    fun updateImage(@DrawableRes drawableRes: Int) {
        //图片盖住svga view
        itemImage.visibility = VISIBLE
        itemSvga.visibility = GONE
        itemImage.setImageResource(drawableRes)
    }

    fun hideImage() {
        //itemImage的隐藏只有这里控制，itemSvga展示也不隐藏
        itemImage.visibility = GONE
        itemSvga.visibility = VISIBLE
    }


    fun setTmpLayout(@LayoutRes layoutRes: Int) {
        tempView.removeAllViews()
        var view = LayoutInflater.from(context).inflate(layoutRes, null)
        tempView.addView(view)
    }

    fun setTmpLayout(view: View) {
        tempView.removeAllViews()
        tempView.addView(view)
    }

    fun getTmpLayout(): View {
        return tempView
    }

    //展示红点
    fun showBubbleImg() {
        bubbleLayout.visibility = VISIBLE
        redTextLayout.visibility = View.GONE
        viewRedImg.visibility = View.VISIBLE
        viewRedText.text = ""
    }

    //展示数字 红点和数字互斥
    fun showBubbleText(text: String) {
        bubbleLayout.visibility = VISIBLE
        redTextLayout.visibility = View.VISIBLE
        viewRedText.visibility = VISIBLE
        viewRedImg.visibility = View.GONE

        viewRedText.text = text
    }

    fun hideBubble() {
        bubbleLayout.visibility = GONE
    }

    fun showBubbleTempImg(@DrawableRes res: Int) {
        bubbleTempImg.visibility = View.VISIBLE
        bubbleTempImg.setImageResource(res)
    }

    fun hideBubbleTempImg() {
        bubbleTempImg.visibility = GONE
    }

    fun isShowPoint(): Boolean {
        return bubbleLayout.visibility == VISIBLE && viewRedImg.visibility == VISIBLE
    }

}