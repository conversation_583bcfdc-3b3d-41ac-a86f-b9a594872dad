package com.immomo.momo.maintab.session2.data.manager

import android.util.Log
import androidx.lifecycle.LifecycleOwner
import com.cosmos.mdlog.MDLog
import com.immomo.momo.brainmatch.BrainMatchCons
import com.immomo.momo.impaas.common.ext.unreadCountFromMsgTable
import com.immomo.momo.maintab.session2.data.database.SessionService
import com.immomo.momo.maintab.session2.defs.DebuggerSessionDefinition
import com.immomo.momo.maintab.session2.defs.DiscussChatSessionDefinition
import com.immomo.momo.maintab.session2.defs.GroupChatSessionDefinition
import com.immomo.momo.maintab.session2.defs.MaskChatSessionDefinition
import com.immomo.momo.maintab.session2.defs.PaasSessionDefinition
import com.immomo.momo.maintab.session2.defs.TextChatSessionDefinition
import com.immomo.momo.maintab.session2.defs.UserChatSessionDefinition
import com.immomo.momo.maintab.session2.defs.VChatSuperRoomSessionDefinition
import com.immomo.momo.messages.service.DiscussMsgServiceV2
import com.immomo.momo.messages.service.GroupMsgServiceV2
import com.immomo.momo.messages.service.MaskMsgServiceV2
import com.immomo.momo.messages.service.SingleMsgServiceV2
import com.immomo.momo.messages.service.TextMatchMsgServiceV2
import com.immomo.momo.messages.service.VChatSuperRoomMsgServiceV2
import com.immomo.momo.mulog.pair.MUPairItem
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.sessions.MessageServiceHelper
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 维护一个UnreadCount的数据库；每次首先尝试从数据库里面读取数据；之后新消息来时，使用缓存，并将该Session置为不可信；
 * 每次启动时，读取上次变更过的不可信session列表；命中重新加载，否则直接从缓存数据库里面读取；
 */
class UnreadCountMessageInterceptor(
    private val sessionService: SessionService
) : MessageInfoCache.Interceptor {
    private val isRunning = AtomicBoolean(true)

    class UnreadInfo(
        val key: String,
        var count: Int = 0,
        var maxMessageTimestamp: Long = 0
    ) {
        @Synchronized
        fun inc(messageTimestamp: Long) {
            if (messageTimestamp > maxMessageTimestamp) {
                maxMessageTimestamp = messageTimestamp
                count++
                Log.d("UnreadCount", "$key: inc to $count")
            } else {
                Log.d("UnreadCount---error", "$key: inc to $count")
            }
        }

        @Synchronized
        fun offset(offset: Int) {
            count = 0.coerceAtLeast(count + offset)
            Log.d("UnreadCount", "$key: offset to $count")
        }

        @Synchronized
        fun dec() {
            count--
            Log.d("UnreadCount", "$key: dec to $count")
        }

        @Synchronized
        fun clear() {
            count = 0
            Log.d("UnreadCount", "$key: clear to $count")
        }
    }

    private val unreadMap = ConcurrentHashMap<SessionKey, UnreadInfo>()
    private val silentUnreadMap = ConcurrentHashMap<SessionKey, UnreadInfo>()

    private fun initUnreadFromCache(sessionKey: SessionKey, isSilent: Boolean): Pair<Int, Long?>? {
        if (sessionService.isUnreliableCount(sessionKey, isSilent)) return null
        return sessionService.findUnread(sessionKey, isSilent)?.let {
            it.count to it.maxMessageTimestamp
        }
    }

    private fun initUnreadFromMessage(sessionKey: SessionKey, isSilent: Boolean) =
        try {
            when (sessionKey.type) {
                UserChatSessionDefinition.Type -> {
                    SingleMsgServiceV2.service.findUnread(
                        sessionKey.id,
                        isSilent,
                        Message.MessageType.IM_MSG_TYPE_NORMAL
                    )
                }

                MaskChatSessionDefinition.Type -> {
                    MaskMsgServiceV2.service.findUnread(
                        sessionKey.id,
                        isSilent,
                        Message.MessageType.IM_MSG_TYPE_NORMAL
                    )
                }

                TextChatSessionDefinition.Type -> {
                    TextMatchMsgServiceV2.service.findUnread(
                        sessionKey.id,
                        isSilent,
                        Message.MessageType.IM_MSG_TYPE_NORMAL
                    )
                }

                GroupChatSessionDefinition.Type -> {
                    GroupMsgServiceV2.service.findUnread(sessionKey.id, isSilent)
                }

                DiscussChatSessionDefinition.Type -> {
                    DiscussMsgServiceV2.service.findUnread(sessionKey.id, isSilent)
                }

                VChatSuperRoomSessionDefinition.Type -> {
                    VChatSuperRoomMsgServiceV2.service.findUnread(sessionKey.id, isSilent)
                }

                PaasSessionDefinition.Type -> {
                    PaasSessionDefinition.findBidHandlerFromSessionId(sessionKey.id)
                        .unreadCountFromMsgTable(
                            PaasSessionDefinition.userIdFromSessionId(sessionKey.id),
                            isSilent,
                            Message.MessageType.IM_MSG_TYPE_NORMAL
                        ).also {
                            MDLog.d(BrainMatchCons.TAG, "初始化${sessionKey.id}未读:${it.first}")
                        }
                }

                else -> 0 to null
            }
        } catch (e: Exception) {
            //没有数据表时，也存入缓存
            if (e.message?.startsWith("no such table: Chat_") == true) {
                0 to 0L
            } else throw e
        }.also { (count, maxMessageTimestamp) ->
            //maxMessageTimestamp不为null，代表找到了数据，或者消息表不存在
            if (maxMessageTimestamp != null) {
                sessionService.saveUnread(
                    sessionKey,
                    count,
                    isSilent,
                    maxMessageTimestamp
                )
            }
        }

    fun initUnread(sessionKey: SessionKey, isSilent: Boolean): UnreadInfo {
        return when (sessionKey.type) {
            DebuggerSessionDefinition.Type -> UnreadInfo(sessionKey.value, 0, 0)
            else -> {
                val (count, maxMessageTimestamp) = initUnreadFromCache(sessionKey, isSilent)
                    ?: initUnreadFromMessage(sessionKey, isSilent)
                if (maxMessageTimestamp == null)
                    UnreadInfo(sessionKey.value, 0, 0)
                else UnreadInfo(sessionKey.value, count, maxMessageTimestamp)
            }
        }
    }

    private fun getUnreadInfo(sessionKey: SessionKey): UnreadInfo =
        unreadMap.getOrPut(sessionKey) {
            try {
                initUnread(sessionKey, false)
            } catch (e: Exception) {
                reportErrors("getUnreadInfo", e, MUPairItem.category(sessionKey.value))
                UnreadInfo(sessionKey.value, 0, 0)
            }
        }

    private fun getSilentUnreadInfo(sessionKey: SessionKey): UnreadInfo =
        silentUnreadMap.getOrPut(sessionKey) {
            try {
                initUnread(sessionKey, true)
            } catch (e: Exception) {
                reportErrors("getSilentUnreadInfo", e, MUPairItem.category(sessionKey.value))
                UnreadInfo(sessionKey.value, 0, 0)
            }
        }

    fun getCount(sessionKey: SessionKey) =
        if (isRunning.get()) getUnreadInfo(sessionKey).count else 0

    fun getSilentCount(sessionKey: SessionKey) =
        if (isRunning.get()) getSilentUnreadInfo(sessionKey).count else 0

    private fun invalidate(sessionKey: SessionKey) {
        sessionService.addUnreliableCount(sessionKey, isSilent = false)
        sessionService.addUnreliableCount(sessionKey, isSilent = true)

        unreadMap.remove(sessionKey)
        silentUnreadMap.remove(sessionKey)
    }

    private fun invalidate() {
        unreadMap.clear()
        silentUnreadMap.clear()
    }

    override fun reset() {
        isRunning.set(false)
        invalidate()
    }

    override fun onIntercept(sessionMessage: SessionMessage) {
        val sessionKey = sessionMessage.sessionKey ?: return

        when (sessionMessage) {
            is SessionMessage.Sync,
            is SessionMessage.Receive
            -> {
                sessionMessage.messageList.forEach { message ->
                    if (message.isImSpam) return@forEach
                    when (message.status) {
                        Message.STATUS_RECE_UNREADED -> {
                            getUnreadInfo(sessionKey).inc(message.timestampExt)
                            sessionService.addUnreliableCount(sessionKey, isSilent = false)
                        }

                        Message.STATUS_RECE_SILENT -> {
                            getSilentUnreadInfo(sessionKey).inc(message.timestampExt)
                            sessionService.addUnreliableCount(sessionKey, isSilent = true)
                        }
                    }
//Message.CHATTYPE_USER -> if (message.isSayhi || message.sayHiFrom != Message.SAY_HI_TYPE_NORMAL) {
//getUnreadInfo(SayHiSessionDefinition.Key).inc(message.timestampExt)
                }
            }

            is SessionMessage.Send -> {
                //发送消息不计入已读未读
            }

            is SessionMessage.Update -> {
                //现有情况不存在Update时更新未读状态的
            }

            is SessionMessage.UpdateStatus -> {
                if (sessionMessage.isUpdateAll) {
                    if (sessionMessage.toStatus == Message.STATUS_RECE_UNREADED
                        || sessionMessage.toStatus == Message.STATUS_RECE_SILENT
                    ) {
                        //无法计算，清除缓存
                        invalidate(sessionKey)
                    } else {
                        if (sessionMessage.fromStatus?.contains(Message.STATUS_RECE_UNREADED) != false) {
                            //将所有未读至为其他状态
                            getUnreadInfo(sessionKey).clear()
                            sessionService.addUnreliableCount(sessionKey, isSilent = false)
                        }
                        if (sessionMessage.fromStatus?.contains(Message.STATUS_RECE_SILENT) != false) {
                            //将所有未读至为其他状态
                            getSilentUnreadInfo(sessionKey).clear()
                            sessionService.addUnreliableCount(sessionKey, isSilent = true)
                        }
                    }
                } else if (sessionMessage.isMessageReceive != false) {
                    //批量将收到的消息至为其他状态
                    if (sessionMessage.toStatus != Message.STATUS_RECE_UNREADED &&
                        sessionMessage.toStatus != Message.STATUS_RECE_SILENT &&
                        sessionMessage.messageIds.isNotEmpty()
                    ) {
                        getUnreadInfo(sessionKey).offset(-sessionMessage.messageIds.size)
                        getSilentUnreadInfo(sessionKey).offset(-sessionMessage.messageIds.size)
                        sessionService.addUnreliableCount(sessionKey, isSilent = false)
                        sessionService.addUnreliableCount(sessionKey, isSilent = true)
                    }
                }
            }

            is SessionMessage.ResetLast -> {
                //重置最新消息直接invalidate
                invalidate(sessionKey)
            }

            is SessionMessage.Delete -> {
                //删除消息直接invalidate
                invalidate(sessionKey)
            }
        }
    }

    companion object {
        private fun self() =
            SessionManager.get().messageInfoCache.interceptor<UnreadCountMessageInterceptor>()

        fun getCount(sessionKey: SessionKey) =
            self().getCount(
                sessionKey
            )


        @JvmStatic
        fun getCount(chatType: Int, remoteId: String) =
            self().getCount(
                SessionKey.fromString(
                    MessageServiceHelper.getSessionIdByType(remoteId, chatType)
                )
            )

        fun getSilentCount(sessionKey: SessionKey) =
            self().getSilentCount(sessionKey)

        @JvmStatic
        fun getSilentCount(chatType: Int, remoteId: String) =
            self().getSilentCount(
                SessionKey.fromString(
                    MessageServiceHelper.getSessionIdByType(remoteId, chatType)
                )
            )

        fun preloadUnreadCount(manager: SessionManager, owner: LifecycleOwner) {
            //do nothing for now
        }
    }
}