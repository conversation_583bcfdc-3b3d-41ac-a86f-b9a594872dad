package com.immomo.momo.message.view;

import android.annotation.TargetApi;
import android.content.Context;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.LinearLayout;

/**
 * 8.0优化新增
 * 自定义消息气泡使用
 * Project momodev
 Package com.immomo.momo.message.view
 * Created by tan<PERSON><PERSON><PERSON> on 2/27/17.
 */

public class MsgBgLinearLayout extends LinearLayout {
    private static final int BG_COLOR = 0xFFEEEEEE;

    public MsgBgLinearLayout(Context context) {
        super(context);
    }

    public MsgBgLinearLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public MsgBgLinearLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public MsgBgLinearLayout(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    /**
     * 触摸事件在 dispatchTouchEvent中处理，是为了避免子view处理touch事件导致无法触摸
     * @param ev
     * @return
     */
    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (ev == null) {
            return false;
        }
        onTouchOverlay(ev);
        return super.dispatchTouchEvent(ev);
    }

    private void onTouchOverlay(MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                if (isClickable()) {
                    Drawable drawable = getBackground();
                    if (drawable != null) {
                        drawable.mutate().setColorFilter(BG_COLOR, PorterDuff.Mode.MULTIPLY);
                    }
                }
                break;
            case MotionEvent.ACTION_MOVE:
                break;
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
                Drawable drawableUp = getBackground();
                if (drawableUp != null) {
                    drawableUp.mutate().clearColorFilter();
                }
                break;
        }
    }
}
