package com.immomo.momo.message.paper.common

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.text.TextUtils
import com.immomo.framework.imageloader.ImageLoaderUtil
import com.immomo.framework.imageloader.ImageType
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.storage.preference.SPKeys
import com.immomo.mmutil.log.Log4Android
import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.momo.Configs
import com.immomo.momo.MomoKit
import com.immomo.momo.android.synctask.Callback
import com.immomo.momo.eventbus.DataEvent
import com.immomo.momo.message.paper.event.PaperEvent
import com.immomo.momo.service.bean.Wallpaper
import com.immomo.momo.util.DataUtil
import com.immomo.momo.util.ImageUtil
import com.immomo.momo.util.StringUtils
import com.immomo.momo.util.WallpaperHelper
import de.greenrobot.event.EventBus
import java.io.File

class BackgroundTask(internal var chatType: Int, internal var chatId: String?, wallpaper: Wallpaper) : MomoTaskExecutor.Task<Wallpaper, Any, Bitmap>(wallpaper) {

    @Throws(Exception::class)
    override fun executeTask(vararg params: Wallpaper): Bitmap? {
        val currentMomoId = MomoKit.getCurrentUser()!!.getMomoid()
        if (StringUtils.isEmpty(currentMomoId)) {
            return null
        }
        var backgroundUrl = KV.getUserStr(SPKeys.Public.PUBLIC_PREFERENCE_KEY_CHATBG_SINGLE + "_" + currentMomoId + "_" + chatType + "_" + chatId, "")
        var backgroundId = KV.getUserStr(SPKeys.Public.PUBLIC_PREFERENCE_KEY_CHATBG_SINGLE_ID + "_" + currentMomoId + "_" + chatType + "_" + chatId, "")
        if (StringUtils.isEmpty(backgroundUrl)) {
            backgroundUrl = KV.getUserStr(SPKeys.Public.PUBLIC_PREFERENCE_KEY_CHATBG_GLOBAL + "_" + currentMomoId, "")
            backgroundId = KV.getUserStr(SPKeys.Public.PUBLIC_PREFERENCE_KEY_CHATBG_GLOBAL_ID + "_" + currentMomoId, "")
        }
        if (backgroundUrl == "default") {
            return null
        }

        //t 如果没有取到的话就再走一遍老的设置逻辑，防止升级上来的用户之前设置的聊天背景失效
        if (StringUtils.isEmpty(backgroundUrl)) {
            //todo 兼容老版本，几个月后可以删掉
            return doOldLogic(*params)
        }

        var bitmap: Bitmap? = null
        if (backgroundUrl.startsWith("http")) {
            val path = MomoKit.getContext().getDir("wallpaper", Context.MODE_PRIVATE).absolutePath
            val imageFile = File(path, "/" + backgroundId + Configs.PICTURE_SUFFIX)
            if (imageFile.exists()) {
                bitmap = BitmapFactory.decodeFile(imageFile.absolutePath)
            }
        } else {
            bitmap = ImageUtil.decodeFile(backgroundUrl)
        }
        return bitmap
    }

    // 老的聊天背景设置逻辑
    private fun doOldLogic(vararg params: Wallpaper): Bitmap? {
        val wallpaper = params[0]
        // ImageView bgView = (ImageView)
        // findViewById(R.id.chat_iv_background);
        var prefix = ""
        if (chatType == com.immomo.momo.service.bean.Message.CHATTYPE_DISCUSS) {
            prefix = "d"
        } else if (chatType == com.immomo.momo.service.bean.Message.CHATTYPE_GROUP || chatType == com.immomo.momo.service.bean.Message.CHATTYPE_GROUP_COMMERCE) {
            prefix = "g"
        } else if (chatType == com.immomo.momo.service.bean.Message.CHATTYPE_USER) {
            prefix = "u"
        }
        val largePicUrl = KV.getUserStr(SPKeys.Public.PUBLIC_PREFERENCE_KEY_CHATBG_RESOURSEID + prefix + chatId, "")
        if (!TextUtils.isEmpty(largePicUrl)) {
            //改用sp里面的，聊天的背景图不应该属于remoteUser内的属性，而应该属于当前用户的设置，更符合逻辑场景
            wallpaper.largePicUrl = largePicUrl
        }
        val defaultImageId = "bg_chat_preview_001"
        // 设置默认
        if (!DataUtil.hasValue(wallpaper.largePicUrl)) {
            wallpaper.largePicUrl = KV.getUserStr(SPKeys.Public.PUBLIC_PREFERENCE_KEY_CHATBG_RESOURSEID, defaultImageId)
        }
        /*
         * 如果是默认背景 则设置背景为空 直接显示资源设置的background 避免小内存手机使用默认背景也发生异常
         */
        if (TextUtils.equals(defaultImageId, wallpaper.largePicUrl)) {
            return null
        }

        if (WallpaperHelper.hasLargeWallpaper(wallpaper)) {
            var bitmap: Bitmap? = null
            if (wallpaper.largeImageId!!.startsWith("http")) {
                bitmap = ImageUtil.decodeFile(ImageLoaderUtil.getImageFile(wallpaper.largeImageId, ImageType.IMAGE_TYPE_URL).toString())
            } else {
                bitmap = ImageUtil.decodeFile(wallpaper.largeImageId)
            }
            return bitmap
        } else {
            // 没有缓存的话就重新下载
            val callback = DownloadWallpaperCallback()
            WallpaperHelper.getInstance().downloadLargeWallpaper(wallpaper, null, callback)
        }
        return null
    }

    override fun onTaskSuccess(bitmap: Bitmap?) {
        EventBus.getDefault().post(DataEvent(PaperEvent.PAPER_EVENT_BACKGROUND_TASK_SUCCESS, bitmap))
    }

    override fun onTaskError(e: Exception) {
        Log4Android.getInstance().e(e)
    }

    private class DownloadWallpaperCallback : Callback<Bitmap> {

        override fun callback(t: Bitmap) {
            EventBus.getDefault().post(DataEvent(PaperEvent.PAPER_EVENT_BACKGROUND_TASK_SUCCESS, t))
        }
    }
}