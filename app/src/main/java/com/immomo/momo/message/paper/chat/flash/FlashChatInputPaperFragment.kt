package com.immomo.momo.message.paper.chat.flash

import android.view.View
import com.immomo.momo.R
import com.immomo.momo.message.paper.BasePaperContainerFragment
import com.immomo.momo.message.paper.PaperConfig
import com.immomo.momo.message.paper.common.*

/**
 * <AUTHOR>
 * @data 2020-12-18.
 */

class FlashChatInputPaperFragment : BasePaperContainerFragment() {

    companion object {
        @JvmStatic
        fun newInstance(): FlashChatInputPaperFragment {
            return FlashChatInputPaperFragment()
        }
    }

    override fun getPagers(): MutableList<PaperConfig>? =
            mutableListOf(PaperConfig(ChatMessageNewMaskPaperFragment.newInstance()))

    override fun getPageLayout(): Int = R.layout.paper_flash_chat_input

    override fun onPageLoad() {
    }

    override fun initPageViews(contentView: View?) {
    }

}