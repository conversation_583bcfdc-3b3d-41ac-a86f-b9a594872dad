package com.immomo.momo.message.sayhi.itemmodel.bean;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.immomo.momo.service.bean.CommonRequestParams;
import com.immomo.momo.service.bean.QandA;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by lei.jialin on 2020-07-02.
 */
public class DetailOneSayhi {
    public static class Request extends CommonRequestParams<Request> {
        private String remoteid;

        public Request(String remoteid) {
            setRemoteid(remoteid);
        }

        public void setRemoteid(String remoteid) {
            this.remoteid = remoteid;
        }

        @Override
        public Map<String, String> toMap() {
            Map<String, String> m = super.toMap();
            if (m == null) m = new HashMap<>();
            m.put("remoteid", remoteid);
            return m;
        }
    }

    public static class Response {
        @Expose
        private List<QandA> questions;

        @Expose
        @SerializedName("feed_photos")
        private List<String> feedPhotos;

        @Expose
        @SerializedName("social_capital_list")
        private List<SocialCapitalBean> socialCapitalBeanList;

        @Expose
        @SerializedName("marks")
        private List<String> socialCapital;

        @Expose
        @SerializedName("isRedEnvelopeHello")
        private int isRedEnvelopeHello;

        @Expose
        @SerializedName("redPacket")
        private SayHiRedPacket redPacket;

        public int getIsRedEnvelopeHello() {
            return isRedEnvelopeHello;
        }

        public void setIsRedEnvelopeHello(int isRedEnvelopeHello) {
            this.isRedEnvelopeHello = isRedEnvelopeHello;
        }

        public List<String> getSocialCapital() {return socialCapital;}

        public List<QandA> getQuestions() {
            return questions;
        }

        public List<String> getFeedPhotos() {
            return feedPhotos;
        }

        public void setFeedPhotos(List<String> feedPhotos) {
            this.feedPhotos = feedPhotos;
        }

        public void setQuestions(List<QandA> questions) {
            this.questions = questions;
        }

        public void setSocialCapitalBeanList(List<SocialCapitalBean> socialCapitalBeanList) {
            this.socialCapitalBeanList = socialCapitalBeanList;
        }

        public List<SocialCapitalBean> getSocialCapitalBeanList() {
            return socialCapitalBeanList;
        }

        public SayHiRedPacket getRedPacket() {
            return redPacket;
        }
    }
}
