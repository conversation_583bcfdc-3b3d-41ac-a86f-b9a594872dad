package com.immomo.momo.message.paper.common.timely.statemachine

import com.immomo.momo.dynamicresources.ResourceChecker
import com.mm.mediasdk.MoMediaManager

class TimelyIdleState(stateManager: TimelyStateManager) :
    AbsBackToCloseState(stateManager) {
    init {
        initRecord()
    }

    fun initRecord() {
        if (ResourceChecker.isRecordReady()) {
            stateMachine.multiRecorder = MoMediaManager.createRecorder().apply {
                this.initRecorder()
            }
        } else {
            backToCloseState()
        }
    }

    override fun hashTag() = hashCode()


}

