package com.immomo.momo.message.paper.common

import android.text.TextUtils
import android.view.View
import android.view.ViewStub
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import com.cosmos.mdlog.MDLog
import com.immomo.framework.utils.UIUtils
import com.immomo.game.util.DpPxUtil
import com.immomo.mmutil.task.MMThreadExecutors
import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.momo.MomoKit
import com.immomo.momo.R
import com.immomo.momo.android.view.tips.TipManager
import com.immomo.momo.android.view.tips.tip.ITip
import com.immomo.momo.android.view.tips.triangle.BottomTriangleDrawable
import com.immomo.momo.eventbus.DataEvent
import com.immomo.momo.greet.GreetHelper
import com.immomo.momo.greet.result.GreetRecommendChatResult
import com.immomo.momo.message.activity.ChatActivity
import com.immomo.momo.message.paper.BasePaperFragment
import com.immomo.momo.message.paper.PaperCommonViewModel
import com.immomo.momo.message.paper.event.PaperEvent
import com.immomo.momo.protocol.http.GreetApi
import io.reactivex.Flowable
import io.reactivex.schedulers.Schedulers
import io.reactivex.subscribers.DisposableSubscriber
import java.util.concurrent.TimeUnit

/**
 * 招呼礼物按钮
 * <AUTHOR>
 * @data 2020-12-18.
 */

class ChatGreetGiftBtnPaperFragment : BasePaperFragment() {

    private var mGreetPanelLayout: RelativeLayout? = null
    private var mContentView: View? = null
    /**
     * 送礼搭讪的整个布局
     */
    private var mChatGreetGiftView: LinearLayout? = null
    private var mChatGreetGiftTextView: TextView? = null
    /**
     * 送礼搭讪上面的tip提示
     */
    private var mGreetChatTip: ITip? = null

    /**
     * 送礼搭讪tip提示的倒计时
     */
    private var mTipDisposable: DisposableSubscriber<*>? = null
    private var mPaperCommonViewModel: PaperCommonViewModel? = null


    companion object {
        fun newInstance(): ChatGreetGiftBtnPaperFragment {
            return ChatGreetGiftBtnPaperFragment()
        }
    }

    override fun getContainerId(): Int = R.id.chat_greet_gift_btn_paper_container

    override fun getPageLayout(): Int = R.layout.paper_chat_greet_gift_btn


    override fun initPageViews(contentView: View?) {
        mContentView = contentView

        getChatActivity()?.let {
            mPaperCommonViewModel = ViewModelProvider(it).get(PaperCommonViewModel::class.java)
            if (it.isGreetHalfMode) {
                initGreetGiftBtnView(it)
                showGreetChat(it)
            }
        }
    }

    override fun onPageLoad() {

    }

    private fun initGreetGiftBtnView(chatActivity: ChatActivity) {
        if (mGreetPanelLayout != null) {
            return
        }
        val viewStub = mContentView?.findViewById(R.id.greet_half_panel_stub) as? ViewStub
        mGreetPanelLayout = viewStub?.inflate() as? RelativeLayout
        mChatGreetGiftView = mContentView?.findViewById(R.id.greet_gift_container) as? LinearLayout
        mChatGreetGiftTextView =  mContentView?.findViewById(R.id.greet_gift_text) as? TextView
        mChatGreetGiftView?.setOnClickListener { v ->
            if (!chatActivity.isForbiddenMsg(true)) {
                chatActivity.showGreetGiftPanelView(true)
            }
        }
        mChatGreetGiftTextView?.text = GreetHelper.getGreetGiftMsg(chatActivity.remoteUser)
    }


    private fun showGreetGiftBtn(show: Boolean) {
        mChatGreetGiftView?.visibility = if (show) View.VISIBLE else View.INVISIBLE
    }

    /**
     * 显示商业招呼(获取招呼面板引导送礼文案)
     */
    private fun showGreetChat(chatActivity: ChatActivity) {
        //礼物面板展示后隐藏打招呼
        if (chatActivity.isGreetHalfMode && mPaperCommonViewModel?.isGreetGiftPanelShow() != true) {
            chatActivity.remoteUser?.let {
                MomoTaskExecutor.executeUserTask(getTaskTag(), GreetChatTask(it.momoid))
            }
        }
    }

    override fun onEvent(event: DataEvent<Any>) {
        super.onEvent(event)
        when (event.action) {
            PaperEvent.PAPER_EVENT_SHOW_GREET_GIFT_BTN -> {
                showGreetGiftBtn(true)
            }
            PaperEvent.PAPER_EVENT_HIDE_GREET_GIFT_BTN -> {
                showGreetGiftBtn(false)
            }
            PaperEvent.PAPER_EVENT_HIDE_GREET_GIFT_BTN_TIP -> {
                hideGreetChatTip()
            }

        }
    }


    override fun onDestroy() {
        MomoTaskExecutor.cancleAllTasksByTag(getTaskTag())
        disposeTip()
        super.onDestroy()
    }

    fun getChatActivity(): ChatActivity? {
        (activity as? ChatActivity)?.let {
            return it
        }
        return null
    }

    /**
     * 隐藏送礼搭讪上面的tip
     */
    private fun hideGreetChatTip() {
        if (mGreetChatTip?.isShowing == true) {
            mGreetChatTip?.hide()
        }
    }


    private fun disposeTip() {
        if (mTipDisposable?.isDisposed != true) {
            mTipDisposable?.dispose()
        }
    }

    fun getTaskTag(): Any {
        return this.javaClass.name + '@'.toString() + Integer.toHexString(this.hashCode())
    }


    /**
     * 获取招呼面板引导送礼文案的异步任务
     */
    private inner class GreetChatTask(private val mRemoteId: String) : MomoTaskExecutor.Task<Any?, Any?, GreetRecommendChatResult?>("") {
        override fun executeTask(vararg params: Any?): GreetRecommendChatResult {
           return GreetApi.getInstance().getGreetRecommendChat(mRemoteId)
        }


        override fun onTaskSuccess(result: GreetRecommendChatResult?) {
            if (result != null && !TextUtils.isEmpty(result.mMsg)) {   //只有接口返回成功，且msg有值才显示
                getChatActivity()?.let { activity ->
                    val tipManager = TipManager.bindActivity(activity).setTouchToHideAll(true).setTouchHideNeedNotfiy(false)

                    tipManager.checkViewCanShowTip(mChatGreetGiftTextView) {
                        val drawable = BottomTriangleDrawable().setColor(UIUtils.getColor(R.color.default_tip_color))
                        val preTx = -(mChatGreetGiftTextView?.width ?: 0) / 4
                        val preTy = -60
                        mGreetChatTip = tipManager.setBackground(UIUtils.getDrawable(R.drawable.bg_corner_8dp_4e7fff)).setTriangles(null, null, null, drawable).setTouchToHideAll(true).setTextPadding(DpPxUtil.dp2px(MomoKit.getContext(), 20f), DpPxUtil.dp2px(MomoKit.getContext(), 13f),
                                DpPxUtil.dp2px(MomoKit.getContext(), 20f), DpPxUtil.dp2px(MomoKit.getContext(), 13f)).setTextSize(DpPxUtil.dp2px(MomoKit.getContext(), 15f).toFloat()).   //px textsize 15sp  距离10dp
                                showTipView(mChatGreetGiftTextView, result.mMsg, preTx, preTy, ITip.Triangle.BOTTOM)

                        if (mChatGreetGiftTextView?.visibility == View.VISIBLE
                                && !activity.mMessageEditTextHasShow && !activity.isEmotionSearching) {   //避免展示的重叠 表情弹窗、输入键盘
                            mGreetChatTip?.show()
                        }

                        //该气泡在3s后会自动消失
                        val millisInFuture: Long = 3
                        mTipDisposable = Flowable.interval(millisInFuture, TimeUnit.SECONDS).onBackpressureDrop().subscribeOn(Schedulers.from(MMThreadExecutors.User)).observeOn(MMThreadExecutors.Main.scheduler).subscribeWith(object : DisposableSubscriber<Long>() {
                            override fun onNext(aLong: Long?) {
                                hideGreetChatTip()
                                disposeTip()
                            }

                            override fun onError(t: Throwable) {
                                //do nothing
                            }

                            override fun onComplete() {
                                //do nothing
                            }
                        })
                    }
                }

            }
        }

        override fun onTaskError(e: Exception) {
            MDLog.printErrStackTrace(TAG, e)
        }
    }

}