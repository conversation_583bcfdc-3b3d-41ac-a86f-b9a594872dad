package com.immomo.momo.message.paper.common

import android.os.Bundle
import android.view.View
import com.cosmos.mdlog.MDLog
import com.immomo.framework.account.MessageManager
import com.immomo.lcapt.evlog.EVLog
import com.immomo.momo.R
import com.immomo.momo.common.ClickUtils
import com.immomo.momo.eventbus.DataEvent
import com.immomo.momo.gotologic.GotoDispatcher
import com.immomo.momo.message.bean.GiftBroadcastBean
import com.immomo.momo.message.log.IGroupGiftBroadcastLog
import com.immomo.momo.message.paper.BasePaperFragment
import com.immomo.momo.message.paper.event.PaperEvent
import com.immomo.momo.mvp.message.view.BaseMessageActivity
import com.immomo.momo.util.GsonUtils
import com.immomo.momo.util.hide
import com.immomo.momo.util.show
import com.immomo.svgaplayer.SVGAAnimListenerAdapter
import com.immomo.svgaplayer.SVGAVideoEntity
import com.immomo.svgaplayer.bean.InsertImgBean
import com.immomo.svgaplayer.bean.InsertTextBean
import com.immomo.svgaplayer.listener.SVGAClickAreaListener
import com.immomo.svgaplayer.utils.SafeKit
import com.immomo.svgaplayer.view.MomoSVGAImageView
import java.util.LinkedList

/**
 * 群礼物广播
 */
open class GiftBroadcastPaperFragment : BasePaperFragment(), MessageManager.MessageSubscriber {

    private var mContentView: View? = null
    private var mSvgaView: MomoSVGAImageView? = null
    private val queue = LinkedList<GiftBroadcastBean>()
    private var isPlaying = false

    companion object {
        const val TAG_ = "GiftBroadcastPaperFragment"
        const val KEY_USER_AVATAR_01 = "user_avatar_01"
        const val KEY_USER_AVATAR_02 = "user_avatar_02"
        const val KEY_USER_NAME_01 = "user_name_01"
        const val KEY_USER_NAME_02 = "user_name_02"
        const val KEY_GIFT = "gift"
        const val KEY_BUTTON_TEXT = "button-text"

        val broadcastIdSet = mutableSetOf<String>()

        fun newInstance(): GiftBroadcastPaperFragment {
            return GiftBroadcastPaperFragment()
        }

        fun getLogParam(
            gift: GiftBroadcastBean,
            curGroupId: String,
            avatarId: String = "",
            onLookerType: Int = -1
        ): Map<String, String?> {
            /**
             * level 广播等级
             * group_id 当前群组id
             * sender_id 送礼人id
             * receiver_id 收礼人id
             * gift_group 送礼行为发生的群组id
             * onlooker_type 围观类型 1 ：直接进群（已是群成员）；2：进群围观（非群成员）；3：进入群资料页
             * avatar_id 点击的头像对应的用户id
             */
            return mapOf(
                "level" to gift.cardLevel.toString(),
                "group_id" to curGroupId,
                "sender_id" to gift.giveUser.momoId,
                "receiver_id" to gift.receiveUser.momoId,
                "gift_group" to gift.sendGiftGroupId,
                "onlooker_type" to onLookerType.toString(),
                "avatar_id" to avatarId
            )
        }
    }

    override fun getContainerId(): Int = R.id.chat_gift_broadcast_paper_container

    override fun getPageLayout(): Int = R.layout.paper_chat_gift_broadcast


    override fun initPageViews(contentView: View?) {
        mContentView = contentView
        mSvgaView = findViewById(R.id.gift_svga)
    }

    override fun onPageLoad() {

    }

    override fun onResume() {
        super.onResume()
        playNextIfIdle()
    }

    override fun onMessageReceive(bundle: Bundle?, action: String?): Boolean {
        return false
    }

    override fun onEvent(event: DataEvent<Any>) {
        super.onEvent(event)
        MDLog.i(TAG_, "event.action:${event.action}")
        when (event.action) {
            PaperEvent.PAPER_EVENT_PLAY_GIFT_BROADCAST -> {
                (event.data as? GiftBroadcastBean)?.let {
                    addGift(it)
                }
            }
        }
    }

    private fun addGift(gift: GiftBroadcastBean) {
        // 是否为当前群的 set
        if (getCurGroupId() == gift.groupId) {
            if (broadcastIdSet.add(gift.broadcastId)) {
                queue.add(gift)
                playNextIfIdle()
            }
        }
    }

    private fun playNextIfIdle() {
        if (isPlaying || queue.isEmpty()) return
        MDLog.i(TAG_, "playNextIfIdle")

        val gift = queue.remove()
        val dressup = gift.cardDressup
        val isSameGroup = getCurGroupId() == gift.sendGiftGroupId

        val listener = object : SVGAAnimListenerAdapter() {
            override fun onLoadSuccess(videoItem: SVGAVideoEntity) {
                super.onLoadSuccess(videoItem)
                MDLog.i(TAG_, "onLoadSuccess")

                val textColor1 = SafeKit.parseColor(dressup.cardTitleColor)
                val textBean1 = InsertTextBean().apply {
                    key = KEY_USER_NAME_01
                    text = dressup.cardTitle
                    textSize = 30f
                    textColor = textColor1
                    singleLine = true
                    ellipsize = 0
                    textAlignType = 1
                }
                val textColor2 = SafeKit.parseColor(dressup.cardDescColor)
                val textBean2 = InsertTextBean().apply {
                    key = KEY_USER_NAME_02
                    text = dressup.cardDesc
                    textSize = 24f
                    textColor = textColor2
                    singleLine = true
                    ellipsize = 2
                    textAlignType = 1
                }
                val imgBean1 = InsertImgBean(KEY_USER_AVATAR_01, gift.giveUser.avatar)
                val imgBean2 = InsertImgBean(KEY_USER_AVATAR_02, gift.receiveUser.avatar)
                mSvgaView?.insertDrawerText(mutableListOf(textBean2, textBean1))
                mSvgaView?.insertDrawerImg(mutableListOf(imgBean1, imgBean2))
                //  礼物 or 围观文字
                val giftBean = InsertImgBean(KEY_GIFT, gift.giftIcon)
                mSvgaView?.insertDrawerImg(mutableListOf(giftBean))

                val textColor3 = SafeKit.parseColor(dressup.btnTitleColor)
                val textBean3 = InsertTextBean().apply {
                    key = KEY_BUTTON_TEXT
                    text = dressup.btnTitle
                    textSize = 28f
                    textColor = textColor3
                    singleLine = true
                    ellipsize = 0
                    textAlignType = 0
                }
                mSvgaView?.insertDrawerText(mutableListOf(textBean3))

                // 动态设置高度
                val factor = videoItem.videoSize.height / videoItem.videoSize.width
                val height = (mSvgaView?.width ?: 0) * factor
                mSvgaView?.layoutParams?.height = height.toInt()
                mSvgaView?.requestLayout()
                mSvgaView?.show()

                mSvgaView?.post {
                    mSvgaView?.startAnimation()
                    markPlayState(true)
                    EVLog.create(IGroupGiftBroadcastLog::class.java)
                        .logShowBroadcast(getLogParam(gift, getCurGroupId()))
                }
            }

            override fun onFinished() {
                super.onFinished()
                MDLog.i(TAG_, "onFinished")
                markPlayState(false)
                mSvgaView?.hide()
                playNextIfIdle()
            }

            override fun loadResError(msg: String) {
                super.loadResError(msg)
                MDLog.i(TAG_, "loadResError")
                mSvgaView?.hide()
                markPlayState(false)
                playNextIfIdle()
            }
        }

        // 资料页跳转
        mSvgaView?.clearInsertData()
        val itemClickAreaListener = object : SVGAClickAreaListener {
            override fun onClick(clickKey: String) {
                if (ClickUtils.isFastClick()) {
                    return
                }
                MDLog.i(TAG_, "$clickKey click")
                when (clickKey) {
                    KEY_USER_AVATAR_01 -> {
                        GotoDispatcher
                            .action(gift.giveUser.action, context)
                            .execute()
                        val logMap = getLogParam(gift, getCurGroupId(), avatarId = gift.giveUser.momoId)
                        EVLog.create(IGroupGiftBroadcastLog::class.java).logAvatarClick(logMap)
                    }

                    KEY_USER_AVATAR_02 -> {
                        GotoDispatcher
                            .action(gift.receiveUser.action, context)
                            .execute()
                        val logMap = getLogParam(gift, getCurGroupId(), avatarId = gift.receiveUser.momoId)
                        EVLog.create(IGroupGiftBroadcastLog::class.java).logAvatarClick(logMap)
                    }

                    KEY_BUTTON_TEXT -> {
                        // 给 goto 附加参数、执行 goto
                        val otherMap = appendGotoInfo(gift)
                        GotoDispatcher
                            .action(dressup.buttonAction, context)
                            .otherParams(otherMap)
                            .execute()
                    }
                }
            }
        }
        mSvgaView?.insertClickArea(KEY_USER_AVATAR_01, itemClickAreaListener)
        mSvgaView?.insertClickArea(KEY_USER_AVATAR_02, itemClickAreaListener)
        if (!isSameGroup) {
            mSvgaView?.insertClickArea(KEY_BUTTON_TEXT, itemClickAreaListener)
        }

        val svgaUrl = if (isSameGroup) dressup.currentResourceUrl else dressup.otherResourceUrl
        mSvgaView?.loadSVGAAnimWithListener(svgaUrl, 1, listener, false)

    }

    override fun onDestroy() {
        super.onDestroy()
        queue.clear()
        mSvgaView?.stopAnimCompletely()
    }

    fun getBaseActivity(): BaseMessageActivity? {
        (activity as? BaseMessageActivity)?.let {
            return it
        }
        return null
    }

    fun appendGotoInfo(gift: GiftBroadcastBean): MutableMap<String, String?> {
        val result = mutableMapOf<String, String?>()
        result["gift"] = GsonUtils.g().toJson(gift)
        result["type15Content"] = gift.giftMessage.toJson().toString()
        result["curGroupId"] = getCurGroupId()

        return result
    }

    private fun getCurGroupId(): String {
        return getBaseActivity()?.chatId ?: ""
    }

    private fun markPlayState(playing: Boolean) {
        isPlaying = playing
    }
}