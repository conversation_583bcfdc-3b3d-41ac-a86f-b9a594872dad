package com.immomo.momo.message.sayhi.widget

import android.content.Context
import android.util.AttributeSet
import com.google.android.flexbox.FlexLine
import com.google.android.flexbox.FlexboxLayout


class FlexBoxLayoutMaxLines @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FlexboxLayout(
    context, attrs, defStyleAttr
) {
    private var maxLines = NOT_SET

    init {
        maxLine = super.getMaxLine()
        super.setMaxLine(NOT_SET)
    }

    override fun setMaxLine(maxLine: Int) {
        maxLines = maxLine
    }

    fun getMaxLines(): Int {
        return maxLines
    }

    override fun getFlexLinesInternal(): List<FlexLine>? {
        val flexLines = super.getFlexLinesInternal()
        val size = flexLines.size

        if (size > 0 && maxLines in 1 until size) {
            flexLines.subList(maxLines, size).clear()
        }
        return flexLines
    }

}