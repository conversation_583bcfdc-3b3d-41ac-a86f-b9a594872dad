package com.immomo.momo.message.paper.common.timely.statemachine

import android.hardware.Camera
import android.view.SurfaceHolder
import com.immomo.framework.base.BaseActivity
import com.immomo.framework.storage.kv.KV
import com.immomo.moment.camera.config.MRConfig
import com.immomo.moment.camera.config.Size
import com.immomo.momo.moment.utils.MomentParamsKeeper
import com.immomo.momo.moment.utils.PermissionCheckerHelper
import com.mm.mediasdk.RecorderConstants
import com.mm.mediasdk.bean.MRSDKConfig

class TimelyReadyState(stateManager: TimelyStateManager) : AbsBackToCloseState(stateManager) {
    var previewSuc = false

    init {
        stateMachine.mView.updateViewInit()
        startPreView()
    }

    private fun startPreView() {
        val surfaceHolder: SurfaceHolder? = stateMachine.currentHolder
        if (surfaceHolder == null) {
            backToCloseState()
            return
        }
        val width = stateMachine.surfaceWidth
        previewSuc = false
        stateMachine.mView.getFragment().activity?.let { activity ->
            val mrConfig = MRSDKConfig
                .Builder(MRConfig.obtain())
                .setRenderModelType(RecorderConstants.RenderModelType.JNI_FILTER_BEAUTY_WITH_JNI_3DRENDING)
                .build()
            if (KV.getUserBool(TimelyStateManager.KEY_TIMELY_CAMERA_FACE_FRONT, false)) {
                mrConfig.mrConfig.defaultCamera = Camera.CameraInfo.CAMERA_FACING_FRONT
            } else {
                mrConfig.mrConfig.defaultCamera = Camera.CameraInfo.CAMERA_FACING_BACK
            }
            mrConfig.mrConfig.encodeSize = Size(width, width)
            // 没有麦克风权限就不使用音频
            if (activity is BaseActivity) {
                val checkerHelper = PermissionCheckerHelper(activity) {}
                if (!checkerHelper.hasRecordPermissionReal()) {
                    mrConfig.mrConfig.forbiddenAudioRecord = true
                }
            }
            stateMachine.multiRecorder?.let {
                it.stopPreview()
                it.prepare(activity, mrConfig)
                it.setSkinAndLightingLevel(
                    MomentParamsKeeper.skin,
                    MomentParamsKeeper.skinLight
                )
                it.setFaceEyeScale(MomentParamsKeeper.bigEyes)
                it.setFaceThinScale(MomentParamsKeeper.thinFace)
                it.setPreviewDisplay(surfaceHolder)
                it.setVisualSize(width, width)
                it.switchCameraResolution(Size(width, width))
                it.setOnFirstFrameRenderedListener {
                    runInUI {
                        previewSuc = true
                        stateMachine.mView.updateViewReady()
                    }
                }
                it.startPreview()

            }
        }
    }

    fun transition2Video() {
        if (previewSuc) {
            transition { TimelyVideoState(stateMachine).apply { startRecord() } }
        }
    }
}