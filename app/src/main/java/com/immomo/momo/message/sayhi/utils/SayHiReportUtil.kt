package com.immomo.momo.message.sayhi.utils

import com.cosmos.mdlog.MDLog
import com.immomo.mmstatistics.event.ClickEvent
import com.immomo.mmstatistics.event.Event
import com.immomo.mmstatistics.event.ExposureEvent
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo
import com.immomo.momo.service.bean.SayhiSession

/**
 * 打招呼打点曝光
 */
object SayHiReportUtil {

    fun listConfirm(sayHiInfo: SayhiSession?) {
        sayHiInfo ?: return
        ClickEvent.create()
                .requireId("17659")
                .page(Event.Page("msg.sayhi_card"))
                .action(Event.Action("list.allow_button"))
                .putExtra("remote_id", sayHiInfo.momoid)
                .putExtra("is_gift", if (sayHiInfo.isFromGift) 1 else 0)
                .putExtra("source", 0)
                .submit()
    }

    fun listChat(sayHiInfo: SayhiSession?) {
        sayHiInfo ?: return
        ClickEvent.create()
                .requireId("17658")
                .page(Event.Page("msg.sayhi_card"))
                .action(Event.Action("list.hi_button"))
                .putExtra("remote_id", sayHiInfo.momoid)
                .putExtra("is_gift", if (sayHiInfo.isFromGift) 1 else 0)
                .putExtra("source", 0)
                .submit()
    }

    fun cardClose(sayHiInfo: SayhiSession?) {
        sayHiInfo ?: return
        ClickEvent.create()
                .requireId("17655")
                .page(Event.Page("msg.sayhi_card"))
                .action(Event.Action("float.gift_close"))
                .putExtra("remote_id", sayHiInfo.momoid)
                .putExtra("is_gift", if (sayHiInfo.isFromGift) 1 else 0)
                .putExtra("source", 0)
                .submit()
    }


    fun cardIgnore(sayHiInfo: SayhiSession?) {
        sayHiInfo ?: return
        ClickEvent.create()
                .requireId("17657")
                .page(Event.Page("msg.sayhi_card"))
                .action(Event.Action("float.gift_ignore"))
                .putExtra("remote_id", sayHiInfo.momoid)
                .putExtra("is_gift", if (sayHiInfo.isFromGift) 1 else 0)
                .putExtra("source", 0)
                .submit()
    }

    fun cardChat(sayHiInfo: SayhiSession?) {
        sayHiInfo ?: return
        ClickEvent.create()
                .requireId("17654")
                .page(Event.Page("msg.sayhi_card"))
                .action(Event.Action("float.gift_reply"))
                .putExtra("remote_id", sayHiInfo.momoid)
                .putExtra("is_gift", if (sayHiInfo.isFromGift) 1 else 0)
                .putExtra("source", 0)
                .submit()
    }

    fun cardConfirm(sayHiInfo: SayhiSession?) {
        sayHiInfo ?: return
        ClickEvent.create()
                .requireId("17653")
                .page(Event.Page("msg.sayhi_card"))
                .action(Event.Action("float.gift_allow"))
                .putExtra("remote_id", sayHiInfo.momoid)
                .putExtra("is_gift", if (sayHiInfo.isFromGift) 1 else 0)
                .putExtra("source", 0)
                .submit()
    }

    /**
     * 上报session曝光
     */
    fun reportSessionShown(sayHiSession: SayhiSession?, sayHiInfo: SayHiInfo?, isFromCard: Boolean = false) {
        sayHiSession ?: return
        val momoid = sayHiSession.momoid
        MDLog.i(SayHiConst.TAG, "onSessionExposure=${sayHiInfo?.name}   isFromCard=${isFromCard}")
        if (isFromCard) {
            ExposureEvent.create(ExposureEvent.Type.Normal)
                    .page(Event.Page("msg.sayhi_card"))
                    .action(Event.Action("float.sayhi_gift"))
                    .requireId("17656")
                    .putExtra("remote_id", sayHiSession.momoid)
                    .putExtra("is_gift", if (sayHiSession.isFromGift) 1 else 0)
                    .putExtra("source", 0)
                    .submit()
        } else {
            ExposureEvent.create(ExposureEvent.Type.Normal)
                    .logId(SayHiConst.pageTag.toString() + "_" + momoid)
                    .page(Event.Page("msg.sayhi_card"))
                    .action(Event.Action("list.feature"))
                    .requireId("17378")
                    .putExtra("remote_id", sayHiSession.momoid)
                    .putExtra("is_gift", if (sayHiSession.isFromGift) 1 else 0)
                    .putExtra("trends", if (sayHiInfo?.feedImageList?.isNotEmpty() == true) 1 else 0)
                    .putExtra("mark", if (sayHiInfo?.hasMarks() == true) 1 else 0)
                    .submit()
        }
    }

}