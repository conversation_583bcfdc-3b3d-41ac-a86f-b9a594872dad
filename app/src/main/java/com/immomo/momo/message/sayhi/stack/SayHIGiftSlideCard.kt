package com.immomo.momo.message.sayhi.stack

import android.content.Context
import android.graphics.Color
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.flexbox.*
import com.immomo.android.module.fundamental.Badge.parseUniformLabelTheme2Model
import com.immomo.android.module.fundamental.Badge.toDisplay
import com.immomo.framework.base.BaseActivity
import com.immomo.framework.cement.CementModel
import com.immomo.framework.cement.CementViewHolder
import com.immomo.framework.cement.SimpleCementAdapter
import com.immomo.framework.cement.eventhook.OnClickEventHook
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.kotlin.ImageType
import com.immomo.framework.utils.UIUtils
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.momo.MomoKit
import com.immomo.momo.R
import com.immomo.momo.android.view.RoundCornerFrameLayout
import com.immomo.momo.gotologic.GotoDispatcher
import com.immomo.momo.likematch.slidestack.BaseSlideCard
import com.immomo.momo.likematch.slidestack.BaseSlideStackView
import com.immomo.momo.message.adapter.items.HiListViewAdapter
import com.immomo.momo.message.helper.FrequentPreferenceHelper
import com.immomo.momo.message.sayhi.itemmodel.SmallImageSelectedItemModel
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiStackCardInfo
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiUserMark
import com.immomo.momo.message.sayhi.utils.SayHiReportHelper
import com.immomo.momo.message.sayhi.widget.FlexBoxLayoutMaxLines
import com.immomo.momo.message.sayhi.widget.UserMarkView
import com.immomo.momo.messages.service.SingleMsgService
import com.immomo.momo.personalprofile.widget.gravitysnaphelper.GravitySnapRecyclerView
import com.immomo.momo.router.ProfileRealAuth
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.message.Type15Content
import com.immomo.momo.util.ColorUtils
import com.immomo.momo.util.view.BadgeView
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class SayHIGiftSlideCard @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : BaseSlideCard<SayHiStackCardInfo>(context, attrs, defStyleAttr) {
    private var imgAvatar: ImageView? = null
    private var tvName: TextView? = null
    private var viewUniform: BadgeView? = null
    private var tvMsg: TextView? = null // 第一条消息
    private var tvMsgSecond: TextView? = null // 第二条消息
    private var tvMsgThird: TextView? = null // 第三条消息
    private var tvIgnore: View? = null
    private var tvPass: View? = null
    private var tvChat: View? = null
    private var imgGift: ImageView? = null
    private var tvGiftTitle: TextView? = null
    private var tvGiftSub: TextView? = null
    private var imgAuth: ImageView? = null
    public var clickIgnore: (() -> Unit)? = null
    public var clickPass: (() -> Unit)? = null
    var clickChat: ((cardInfo: SayHiStackCardInfo?) -> Unit)? = null

    private var photoData = mutableListOf<String>()

    private var smallAdapter = SimpleCementAdapter()
    private var smallLayoutManager: LinearLayoutManager? = null
    private var smallRV: GravitySnapRecyclerView? = null

    private var onlineContainer: RoundCornerFrameLayout? = null
    private var ivLocation: ImageView? = null
    private var locationDistance: TextView? = null
    private var ivReport: ImageView? = null
    private var flexLabelView: FlexBoxLayoutMaxLines? = null
    private var giftCardContainer: LinearLayout? = null

    private var selectModel: SmallImageSelectedItemModel? = null
    private var tvFlashAnim: Animation? = null
    private var ivFlash: ImageView? = null

    override fun onClick(v: View?) {

    }

    override fun getLayoutId(): Int = R.layout.say_hi_gift_slide_card

    override fun init() {
        initView()
    }

    private fun initView() {
        imgAvatar = findViewById(R.id.img_avatar)
        tvName = findViewById(R.id.tv_name)
        viewUniform = findViewById(R.id.view_uniform)
        tvMsg = findViewById(R.id.tv_msg)
        tvMsgSecond = findViewById(R.id.tv_msg_second)
        tvMsgThird = findViewById(R.id.tv_msg_third)
        tvIgnore = findViewById(R.id.tv_ignore)
        tvPass = findViewById(R.id.tv_pass)
        imgGift = findViewById(R.id.img_gift)
        tvChat = findViewById(R.id.tv_chat)
        tvGiftTitle = findViewById(R.id.tv_gift_title)
        tvGiftSub = findViewById(R.id.tv_gift_sub)
        smallRV = findViewById(R.id.list_photos)
        onlineContainer = findViewById(R.id.user_online_status_container)
        ivLocation = findViewById(R.id.iv_location)
        locationDistance = findViewById(R.id.location_distance)
        ivReport = findViewById(R.id.iv_report)
        imgAuth = findViewById(R.id.img_auth)
        flexLabelView = findViewById(R.id.view_tag)
        giftCardContainer = findViewById(R.id.gift_card_container)
        ivFlash = findViewById(R.id.iv_flash)
        flexLabelView?.flexDirection = FlexDirection.ROW
        flexLabelView?.flexWrap = FlexWrap.WRAP
        flexLabelView?.justifyContent = JustifyContent.CENTER
        flexLabelView?.alignItems = AlignItems.CENTER
        flexLabelView?.alignContent = AlignContent.FLEX_START
        tvFlashAnim = AnimationUtils.loadAnimation(MomoKit.getContext(), R.anim.anim_female_tv_flash)
        tvFlashAnim?.let {
            ivFlash?.startAnimation(it)
        }

    }

    override fun endAnim() {

    }

    override fun scaleLikeIconWhileSlide(percentage: Float) {

    }

    override fun onDestroy() {
        ivFlash?.clearAnimation()
    }

    override fun resetViewsOnCard(changedView: BaseSlideCard<SayHiStackCardInfo>?) {

    }

    override fun fillData(
        info: SayHiStackCardInfo?,
        index: Int,
        listener: BaseSlideStackView.CardSwitchListener?
    ) {
        if (info == null) {
            return
        }
        val sayHiNetInfo = info.sayHiNetInfo
        val photoUrlAvatars = sayHiNetInfo?.photoUrl
        dealFoldScreen()
        if (photoUrlAvatars != null && photoUrlAvatars.origin?.isNotBlank() == true) {
            imgAvatar?.let {
                ImageLoader.load(photoUrlAvatars.origin).showDefault().into(it)
            }
        } else {
            imgAvatar?.let {
                ImageLoader.load(info.sayHiInfo.user?.avatar).imageType(ImageType.ALBUM_250X250)
                    .showDefault()
                    .into(it)
            }
        }
        info.sayHiInfo.user?.let {
            if (it.isVip || it.isSvip()) {
                tvName?.setTextColor(UIUtils.getColor(R.color.font_vip_name))
            } else {
                tvName?.setTextColor(UIUtils.getColor(R.color.color_323333_to_80f))
            }
        }
        val realAuth = info.sayHiNetInfo?.realAuth
        if (realAuth?.status == ProfileRealAuth.SUCCESS) {
            imgAuth?.visibility = View.VISIBLE
            imgAuth?.let {
                ImageLoader.load(realAuth.icon).into(it)
            }
            imgAuth?.setOnClickListener {
                GotoDispatcher.action(realAuth.gotoStr, context)
                    .execute()
            }
        } else {
            imgAuth?.visibility = View.GONE
        }
        tvName?.text = info.sayHiInfo.user?.name

        if (info.sayHiInfo.user.uniformLabel != null) {
            val uniformLabel = info.sayHiInfo.user.uniformLabel
            val oldSize = uniformLabel.size
            uniformLabel.size = 2
            viewUniform?.toDisplay(
                info.sayHiInfo.user.uniformLabel.parseUniformLabelTheme2Model(),
                viewUniform
            )
            uniformLabel.size = oldSize
        }
        tvIgnore?.setOnClickListener {
            clickIgnore?.invoke()
        }

        tvPass?.setOnClickListener {
            clickPass?.invoke()
        }
        tvChat?.setOnClickListener {
            clickChat?.invoke(info)
        }
        val avatarTag = info.sayHiNetInfo?.avatarTag
        if (avatarTag != null) {
            if (avatarTag.text?.isBlank() == false) {
                locationDistance?.text = avatarTag.text
                locationDistance?.setTextColor(ColorUtils.parseColor(avatarTag.textColor, Color.BLACK))
            }
            onlineContainer?.setBackgroundColor(ColorUtils.parseColor(avatarTag.bgColor, Color.WHITE))
            if (!avatarTag.icon.isNullOrBlank()) {
                ivLocation?.let { iv ->
                    ImageLoader.load(avatarTag.icon).into(iv)
                }
            }
            onlineContainer?.visibility = View.VISIBLE
        } else {
            onlineContainer?.visibility = View.GONE
        }
        if (smallLayoutManager == null) {
            initSmallPhotosView()
        }
        ivReport?.setOnClickListener {
            SayHiReportHelper.showReportDialog(context as BaseActivity?, info.sayHiInfo, info.sayHiNetInfo)
        }
        giftCardContainer?.visibility = View.GONE
        refreshContentView(info)
    }

    private fun dealFoldScreen() {
        val screenWidth = UIUtils.getScreenWidth()
        val screenHeight = UIUtils.getScreenHeight()
        val sizeAspect = screenHeight.toFloat() / screenWidth // 折叠屏，越来越小
        val smallMinAspect = 16f / 14   // 1.142857142857143
        val middleMinAspect = 2000f / 1080 // 1.777777777777778
        val longAspect = 2150f / 1080    // 1.777777777777778
        // MDLog.i("dealFoldScreen", "$sizeAspect   smallMinAspect=$smallMinAspect   smallBigAspect=$longAspect")
        if (longAspect > sizeAspect) {
            (imgAvatar?.layoutParams as? ConstraintLayout.LayoutParams)?.also {
                it.dimensionRatio = if (sizeAspect >= middleMinAspect && sizeAspect < longAspect) {
                    "1.2"
                } else if (sizeAspect > smallMinAspect && sizeAspect <= middleMinAspect) {
                    "1.5"
                } else {
                    imgAvatar?.scaleType = ImageView.ScaleType.CENTER
                    "2.5"
                }
                imgAvatar?.layoutParams = it
            }
        }
    }

    private fun refreshContentView(info: SayHiStackCardInfo) {
        (context as BaseActivity?)?.also {
            it.lifecycleScope.launch(MMDispatchers.User) {
                val rawMessages: MutableList<Message>? =
                    SingleMsgService.getInstance().findSayhiMessageByRemoteId(info.sayHiInfo.momoid, 0, 10)
                rawMessages?.reverse()
                if (rawMessages != null) {
                    val iterator = rawMessages.iterator()
                    while (iterator.hasNext()) {
                        val message = iterator.next()
                        if (FrequentPreferenceHelper.i().isHarassGreetingOpen
                            && message.sayHiFrom == Message.SAY_HI_TYPE_HARASS) { // 移除spam消息
                            iterator.remove()
                        } else if (message.contentType == Message.CONTENTTYPE_MESSAGE_NOTICE) { // 移除notice消息
                            iterator.remove()
                        }
                    }
                }
                withContext(MMDispatchers.Main) {
                    refreshMsgContent(tvMsgThird, rawMessages, 3)
                    refreshMsgContent(tvMsgSecond, rawMessages, 2)
                    refreshMsgContent(tvMsg, rawMessages, 1)
                    val msgGift = info.msgGift
                    if (msgGift != null) {
                        if (msgGift.messageContent is Type15Content) {
                            tvGiftSub?.text = (msgGift.messageContent as? Type15Content)?.giftText1
                            imgGift?.let {
                                ImageLoader.load((msgGift.messageContent as? Type15Content)?.pic).into(it)
                            }
                        }
                        tvGiftTitle?.text =
                            (if (info.sayHiInfo?.user?.isFemale == true) "她" else "他") + "送了你招呼礼物"
                        giftCardContainer?.visibility = View.VISIBLE
                        smallRV?.visibility = View.GONE // 图片选择和礼物互斥展示
                    } else {
                        refreshSmallPreview(info.sayHiNetInfo)
                    }
                    initFlexItems(info.sayHiNetInfo, rawMessages?.size ?: 0)
                }
            }
        }
    }

    private fun refreshMsgContent(msgView: TextView?, rawMessages: MutableList<Message>?, index: Int) {
        msgView ?: return
        if (rawMessages != null && rawMessages.size >= index) {
            msgView.text = HiListViewAdapter.getMessageContent(rawMessages[index - 1])
            msgView.visibility = VISIBLE
        } else {
            msgView.visibility = GONE
        }
    }

    private fun initFlexItems(sayHiInfo: SayHiInfo?, msgCount: Int) {
        sayHiInfo ?: return
        if (!sayHiInfo.markList.isNullOrEmpty()) {
            flexLabelView?.visibility = View.VISIBLE
            if ((flexLabelView?.flexItemCount ?: 0) > 0) {
                flexLabelView?.removeAllViews()
            }
            if (msgCount <= 1) {
                flexLabelView?.maxLine = 2
            } else {
                flexLabelView?.maxLine = 1
            }
            for (mark in sayHiInfo.markList) {
                val view = createUserMark(mark, false)
                flexLabelView?.addView(view, view.layoutParams)
            }
        } else {
            flexLabelView?.visibility = View.GONE
        }
    }

    private fun createUserMark(mark: SayHiUserMark, isGift: Boolean): View {
        val itemView = UserMarkView(context)
        itemView.setInfo(mark, isGift)
        val layoutParams =
            LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        layoutParams.rightMargin = UIUtils.getPixels(5f)
        layoutParams.topMargin = UIUtils.getPixels(5f)
        itemView.layoutParams = layoutParams
        return itemView
    }

    private fun refreshSmallPreview(sayHiNetInfo: SayHiInfo?) {
        photoData.clear()
        val feedImageList = sayHiNetInfo?.feedImageList
        val photos = mutableListOf<String>()
        sayHiNetInfo?.photoUrl?.also { // 头像图片
            val smallImg = it.small
            val originImg = it.origin
            if (smallImg?.isNotBlank() == true) {
                photos.add(smallImg)
            }
            if (originImg?.isNotBlank() == true) {
                photoData.add(originImg)
            }
        }
        feedImageList?.forEach { // 动态图片
            val smallImg = it.small
            val originImg = it.origin
            if (smallImg?.isNotBlank() == true) {
                photos.add(smallImg)
            }
            if (originImg?.isNotBlank() == true) {
                photoData.add(originImg)
            }
        }
        smallAdapter.clearData()
        val count = photos.size
        if (count > 1) {
            smallAdapter.addDataList(transSmallPreview(photos, count))
            smallRV?.visibility = View.VISIBLE
        } else {
            smallRV?.visibility = View.GONE
        }
    }

    private fun transSmallPreview(photos: List<String?>?, count: Int): List<CementModel<*>> {
        smallAdapter.clearFooters()
        val models: MutableList<SmallImageSelectedItemModel> = ArrayList()
        smallRV?.also {
            if (photos == null) return models
            for (i in 0 until count) {
                val url = photos[i]
                if (TextUtils.isEmpty(url)) continue
                val smallModel = SmallImageSelectedItemModel(url, false, i == 0, it)
                if (i == 0) {
                    smallModel.selected = true
                }
                models.add(smallModel)
            }
        }
        return models
    }

    private fun initSmallPhotosView() {
        smallAdapter = SimpleCementAdapter()
        smallLayoutManager = LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
        smallRV?.layoutManager = smallLayoutManager
        smallRV?.adapter = smallAdapter
        smallRV?.itemAnimator = null
        smallAdapter.addEventHook(object : OnClickEventHook<CementViewHolder>(
            CementViewHolder::class.java
        ) {
            override fun onClick(
                view: View,
                viewHolder: CementViewHolder,
                position: Int,
                rawModel: CementModel<*>
            ) {
                scrollToAndSelect(position, rawModel)
            }

            override fun onBindMany(viewHolder: CementViewHolder): List<View> {
                return listOf(viewHolder.itemView)
            }
        })
    }

    /**
     * 选中了头像
     */
    private fun scrollToAndSelect(position: Int, model: CementModel<*>) {
        if (selectModel == model) return
        imgAvatar?.let {
            if (position < photoData.size) {
                ImageLoader.load(photoData[position]).showDefault().into(it)
            }
        }
        if (model is SmallImageSelectedItemModel && selectModel != model) { // 刷新选中状态
            selectModel?.selected = false
            selectModel?.let {
                smallAdapter.notifyDataChanged(it)
            }
            model.selected = true
            selectModel = model
            smallAdapter.notifyDataChanged(model)
        }
    }
}