package com.immomo.momo.message.sayhi.widget

import android.content.Context
import android.content.DialogInterface
import android.view.View
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.immomo.momo.R

class SayHiActionDialog(
    context: Context,
    var qiaoClick: () -> Unit?, var delClick: () -> Unit?,
    var reportClick: () -> Unit?, var blockClick: () -> Unit?
) :
    BottomSheetDialog(context, R.style.BottomSheetDialog), DialogInterface.OnDismissListener {

    private var viewQiao: View?
    private var viewDel: View?
    private var viewReport: View?
    private var viewBlock: View?

    init {
        setContentView(R.layout.layout_sayhi_more_dialog)
        viewQiao = findViewById(R.id.view_qiaoqiao)
        viewDel = findViewById(R.id.view_del)
        viewReport = findViewById(R.id.view_report)
        viewBlock = findViewById(R.id.view_block)

        window?.setWindowAnimations(0)

        initEvents()
    }

    fun needShowReport(isShowReport: Boolean) {
        viewBlock?.visibility = if (isShowReport) View.VISIBLE else View.GONE
    }

    private fun initEvents() {
        setOnDismissListener(this)
        viewQiao?.setOnClickListener {
            qiaoClick.invoke()
            dismiss()
        }
        viewDel?.setOnClickListener {
            delClick.invoke()
            dismiss()
        }
        viewReport?.setOnClickListener {
            reportClick.invoke()
            dismiss()
        }
        viewBlock?.setOnClickListener {
            blockClick.invoke()
            dismiss()
        }
    }

    override fun onDismiss(dialog: DialogInterface?) {

    }
}