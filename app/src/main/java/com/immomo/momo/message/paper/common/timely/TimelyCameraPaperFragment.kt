package com.immomo.momo.message.paper.common.timely

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Service
import android.content.DialogInterface
import android.os.Vibrator
import android.text.Editable
import android.text.Selection
import android.text.TextUtils
import android.text.TextWatcher
import android.view.MotionEvent
import android.view.SurfaceView
import android.view.View
import android.view.ViewGroup
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.view.animation.AnimationSet
import android.view.animation.LinearInterpolator
import android.view.animation.PathInterpolator
import android.view.animation.ScaleAnimation
import android.view.animation.TranslateAnimation
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.immomo.framework.utils.UIUtils
import com.immomo.lcapt.evlog.EVLog
import com.immomo.mmdns.MomoMainThreadExecutor
import com.immomo.momo.R
import com.immomo.momo.android.videoview.ScalableType
import com.immomo.momo.android.videoview.VideoView
import com.immomo.momo.android.view.textview.gif.GifEmoteEditText
import com.immomo.momo.android.view.dialog.MAlertDialog
import com.immomo.momo.android.view.tips.TipManager
import com.immomo.momo.android.view.tips.anim.DefaultTipAnimation
import com.immomo.momo.android.view.tips.tip.ITip
import com.immomo.momo.message.paper.BasePaperFragment
import com.immomo.momo.message.paper.common.timely.statemachine.TimelyStateManager
import com.immomo.momo.message.view.TimelyTouchView
import com.immomo.momo.moment.model.MicroVideoModel
import com.immomo.momo.moment.utils.KeyBoardUtil
import com.immomo.momo.multpic.entity.Photo
import com.immomo.momo.mvp.message.contract.IMessageLog
import com.immomo.momo.mvp.message.view.BaseMessageActivity
import com.immomo.momo.service.bean.Message
import com.immomo.momo.util.SimpleAnimationListener
import com.immomo.momo.util.SoftKeyBoardListener

class TimelyCameraPaperFragment : BasePaperFragment(), ITimelyCameraFragmentView {

    private lateinit var surfaceView: SurfaceView
    private lateinit var ivSwitchCamera: ImageView
    private lateinit var flayoutState: FrameLayout
    private lateinit var tvState: TextView
    private lateinit var viewProgress: View
    private lateinit var cameraRoot: View
    private lateinit var ivPreviewMask: View
    private lateinit var contentRoot: View
    private lateinit var viewContentBg: View
    private lateinit var llayoutContentRoot: View
    private lateinit var btnSend: View
    private lateinit var editTimely: GifEmoteEditText
    private lateinit var tvAgain: View
    private lateinit var ivClose: View
    private lateinit var timelyTouchView: TimelyTouchView
    private lateinit var videoView: VideoView
    private lateinit var timelyFocusView: View

    private var outAnimating = false
    private var isFistTip = false
    private var editCurrentLength = -1


    private lateinit var stateManager: TimelyStateManager
    private val NORMAL_H = UIUtils.getScreenWidth() - UIUtils.getPixels(30f)
    private val MIN_H = UIUtils.getPixels(200f)
    private var progressAnim: ValueAnimator? = null
    private var isProgressAnim = false

    companion object {
        @JvmStatic
        fun newInstance(): TimelyCameraPaperFragment {
            return TimelyCameraPaperFragment()
        }

        const val TIMELY = "timely"
    }


    override fun getContainerId(): Int = R.id.chat_timely_camera_paper_container
    override fun getPageLayout(): Int = R.layout.paper_chat_timely_camera

    override fun initPageViews(contentView: View?) {
        contentView?.let {
            stateManager = TimelyStateManager(this)
            contentRoot = it
            it.setOnClickListener {}
            (it.findViewById(R.id.view_top) as View).setOnClickListener {
                closeFragment()
            }
            cameraRoot = it.findViewById(R.id.flayout_camera_root) as View

            surfaceView = it.findViewById(R.id.surfaceview) as SurfaceView
            surfaceView.holder.addCallback(stateManager)


            ivSwitchCamera = it.findViewById(R.id.iv_switch_camera) as ImageView

            flayoutState = it.findViewById(R.id.flayout_state) as FrameLayout
            tvState = it.findViewById(R.id.tv_state) as TextView
            viewProgress = it.findViewById(R.id.progress_view) as View
            viewProgress.pivotX = 0f
            viewProgress.scaleX = 0f

            videoView = it.findViewById(R.id.videoview) as VideoView
            videoView.setScalableType(ScalableType.FIT_WIDTH)

            ivClose = it.findViewById(R.id.iv_close) as View
            ivClose.setOnClickListener {
                closeFragment()
            }

            tvAgain = it.findViewById(R.id.tv_again) as View
            tvAgain.setOnClickListener {
                var dialog = MAlertDialog.makeConfirm(
                    activity, "是否重新拍摄", "取消", "重拍",
                    null
                ) { dialog12: DialogInterface?, which: Int ->
                    remakeLog()
                    rollback2Ready()
                }
                dialog.setSupportDark(true)
                showDialog(dialog)
            }
            viewContentBg = it.findViewById(R.id.view_content_bg) as View
            llayoutContentRoot = it.findViewById(R.id.llayout_content_root) as View
            editTimely = it.findViewById(R.id.edit_timely) as GifEmoteEditText
            btnSend = it.findViewById(R.id.btn_send) as View
            btnSend.setOnClickListener {
                stateManager.sendRealMessage(editTimely.getText().toString().trim())
                closeSelf()
            }
            timelyTouchView = it.findViewById(R.id.timely_touch) as TimelyTouchView
            timelyTouchView.onLongPressListen = object : TimelyTouchView.OnLongPressListen {
                override fun onLongPress() {
                    stateManager.change2VideoState()
                }

                override fun onCancelPress(isLongPress: Boolean) {
                    stateManager.recordFinish(isLongPress)
                    enterClickLog(isLongPress)
                }

                override fun onMove(percent: Float) {
                    stateManager.setZoomLevel(percent)
                }

            }

            ivPreviewMask = it.findViewById(R.id.iv_preview_mask) as View
            ivPreviewMask.setOnClickListener { }

            ivSwitchCamera.setOnClickListener {
                stateManager.switchCamera()
            }

            timelyFocusView = it.findViewById(R.id.timely_focus_view) as View

            it.post {
                setSurfaceSize(it.height)
                doInOutAnimation(true)
                editTimely.post {
                    activity?.let { it ->
                        KeyBoardUtil.showSoftKeyboard(it, editTimely)
                    }
                }

            }
        }

        initEvent()
        SoftKeyBoardListener.register(activity,
            object : SoftKeyBoardListener.OnSoftKeyBoardChangeListener {
                override fun keyBoardShow(rootViewVisibleHeight: Int, keyboardHeight: Int) {
                    allowInput(true)
                    setSurfaceSize(rootViewVisibleHeight)
                }

                override fun keyBoardHide(rootViewVisibleHeight: Int, keyboardHeight: Int) {
                }

            })
    }

    private fun setSurfaceSize(rootViewVisibleHeight: Int) {
        val availableHeight =
            rootViewVisibleHeight - UIUtils.getPixels(160f)

        val realH = Math.max(Math.min(availableHeight, NORMAL_H), MIN_H)
        if (cameraRoot.layoutParams.height != realH) {
            cameraRoot.let {
                val rootLayoutParams = it.layoutParams
                rootLayoutParams.height = realH
                it.layoutParams = rootLayoutParams
            }
        }
    }

    override fun onPageLoad() {

    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initEvent() {
        surfaceView.setOnTouchListener(object : View.OnTouchListener {
            val halfSize = UIUtils.getPixels(30f)
            override fun onTouch(v: View?, event: MotionEvent?): Boolean {
                if (!stateManager.isCanFocus()) {
                    return false
                }
                event?.let {
                    if (it.action == MotionEvent.ACTION_UP) {
                        val x = it.getX()
                        val y = it.getY()
                        val width = surfaceView.width
                        val height = surfaceView.height
                        (timelyFocusView.layoutParams as? ViewGroup.MarginLayoutParams)?.let {
                            var left = (x - halfSize).toInt()
                            if (left < 0) {
                                left = 0
                            } else if (left > width - halfSize * 2) {
                                left = width - halfSize * 2
                            }

                            var top = (y - halfSize).toInt()
                            if (top < 0) {
                                top = 0
                            } else if (top > height - halfSize * 2) {
                                top = height - halfSize * 2
                            }
                            it.topMargin = top
                            it.leftMargin = left
                            timelyFocusView.layoutParams = it
                            timelyFocusView.visibility = View.VISIBLE
                            MomoMainThreadExecutor.postDelayed(getTaskTag(), object : Runnable {
                                override fun run() {
                                    timelyFocusView.visibility = View.GONE
                                }
                            }, 1000)
                        }

                        stateManager.focusOnTouch(
                            x, y, width, height
                        )
                    } else if (it.action == MotionEvent.ACTION_DOWN) {
                        MomoMainThreadExecutor.cancelAllRunnables(getTaskTag())
                        timelyFocusView.visibility = View.GONE
                    }
                }
                return true
            }
        })

        editTimely.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

            }

            override fun afterTextChanged(s: Editable) {
                if (editCurrentLength >= 0 && s.length > editCurrentLength) {
                    editTimely.setText(s.subSequence(0, editCurrentLength))
                    Selection.setSelection(editTimely.getText(), editCurrentLength);
                }
            }

        })
    }

    fun closeFragment() {
        if (!stateManager.isCanClose()) {
            return
        }
        if (stateManager.isReadySendState()) {
            val msg = "即将离开相机，要放弃该${if (stateManager.isVideoReady()) "视频" else "照片"}吗？"
            var dialog = MAlertDialog.makeConfirm(
                activity, msg, "取消", "放弃",
                null
            ) { dialog12: DialogInterface?, which: Int ->
                closeSelf()
            }
            dialog.setSupportDark(true)
            showDialog(dialog)
        } else {
            closeSelf()
        }
    }

    private fun doInOutAnimation(isIn: Boolean) {
        if (isIn) {
            cameraRoot.startAnimation(AnimationSet(true).apply {
                addAnimation(
                    ScaleAnimation(
                        0f,
                        1f,
                        0f,
                        1f,
                        ScaleAnimation.RELATIVE_TO_SELF,
                        0f,
                        ScaleAnimation.RELATIVE_TO_SELF,
                        1f
                    )
                )
                addAnimation(AlphaAnimation(0f, 1f))
                duration = 200
                interpolator = PathInterpolator(0.3f, 0.6f, 0f, 1f)
                setAnimationListener(object : SimpleAnimationListener() {
                    override fun onAnimationEnd(animation: Animation?) {
                        rollback2Ready()
                    }
                })
            })
        } else {
            if (outAnimating) return
            cameraRoot.startAnimation(AnimationSet(true).apply {
                addAnimation(
                    TranslateAnimation(
                        0,
                        0f,
                        0,
                        0f,
                        Animation.RELATIVE_TO_SELF,
                        0f,
                        Animation.RELATIVE_TO_SELF,
                        1f
                    )
                )
                addAnimation(AlphaAnimation(1f, 0f))

                fillAfter = true
                duration = 200
                interpolator = LinearInterpolator()
                setAnimationListener(object : SimpleAnimationListener() {
                    override fun onAnimationEnd(animation: Animation?) {
                        outAnimating = false
                        stateManager.mPaperCommonViewModel?.timelyCameraListener?.closeTimelyFragment()
                    }

                    override fun onAnimationStart(animation: Animation?) {
                        outAnimating = true
                        ivPreviewMask.visibility = View.VISIBLE
                    }
                })
            })
        }
    }

    override fun getFragment(): Fragment = this
    private fun closeSelf() {
        if (!isVisible) {
            return
        }
        KeyBoardUtil.hideSoftKeyboardNotAlways(activity)
        doInOutAnimation(false)
    }

    override fun updateViewInit() {
        ivSwitchCamera.visibility = View.GONE
        llayoutContentRoot.visibility = View.GONE
        viewContentBg.visibility = View.GONE
        flayoutState.visibility = View.GONE
        progressAnim?.cancel()
        isProgressAnim = false
        timelyTouchView.visibility = View.GONE
        tvAgain.visibility = View.GONE
        ivPreviewMask.visibility = View.VISIBLE
        ivClose.visibility = View.VISIBLE
        releaseVideoView()
        editTimely.setText("")
    }

    override fun updateViewReady() {
        ivSwitchCamera.visibility = View.VISIBLE
        timelyTouchView.let {
            it.visibility = View.VISIBLE
            it.setDefaultView()
        }
        tvAgain.visibility = View.GONE
        editTimely.visibility = View.VISIBLE
        llayoutContentRoot.visibility = View.VISIBLE
        viewContentBg.visibility = View.VISIBLE
        updateSendBtn(false)
        ivClose.visibility = View.VISIBLE
        releaseVideoView()
        allowInput(true)
        showTimelyTip("轻触拍照，长按录像")
        AlphaAnimation(1f, 0f).apply {
            duration = 150
            interpolator = LinearInterpolator()
            setAnimationListener(object : SimpleAnimationListener() {
                override fun onAnimationEnd(animation: Animation?) {
                    ivPreviewMask.visibility = View.GONE
                }
            })
            ivPreviewMask.startAnimation(this)
        }
        editTimely.post {
            activity?.let { it ->
                KeyBoardUtil.showSoftKeyboard(it, editTimely)
            }
        }
    }

    override fun updateRecording() {
        tvAgain.visibility = View.GONE
        viewContentBg.visibility = View.GONE
        ivSwitchCamera.visibility = View.GONE
        ivClose.visibility = View.GONE


        tvState.text = "00:00"
        viewProgress.scaleX = 0f
        releaseVideoView()
        allowInput(false)
        doContentViewAnim(false)
    }

    override fun updateRecordFinish(
        microVideoModel: MicroVideoModel?,
        photos: MutableList<Photo>?
    ) {
        tvAgain.visibility = View.VISIBLE
        ivClose.visibility = View.VISIBLE
        editTimely.visibility = View.VISIBLE
        llayoutContentRoot.visibility = View.VISIBLE
        viewContentBg.visibility = View.VISIBLE
        updateSendBtn(true)
        timelyTouchView.doDismissAnim()
        flayoutState.visibility = View.GONE
        progressAnim?.cancel()
        isProgressAnim = false
        ivSwitchCamera.visibility = View.GONE

        microVideoModel?.let {
            videoView.visibility = View.VISIBLE
            videoView.setDataSource(it.video?.path)
            videoView.setLooping(true)
            videoView.prepareAsync {
                videoView.start()
            }
        }

        doContentViewAnim(true)
        allowInput(true)
    }

    private fun doContentViewAnim(isShow: Boolean) {
        if (isShow) {
            AnimationSet(true).apply {
                addAnimation(
                    TranslateAnimation(
                        0,
                        0f,
                        0,
                        0f,
                        Animation.RELATIVE_TO_SELF,
                        0.2f,
                        Animation.RELATIVE_TO_SELF,
                        0f
                    )
                )
                addAnimation(AlphaAnimation(0f, 1f))
                fillAfter = true
                duration = 200
                interpolator = PathInterpolator(0.3f, 0.6f, 0f, 1f)
                llayoutContentRoot.startAnimation(this)
            }
        } else {
            AnimationSet(true).apply {
                addAnimation(
                    TranslateAnimation(
                        0,
                        0f,
                        0,
                        0f,
                        Animation.RELATIVE_TO_SELF,
                        0f,
                        Animation.RELATIVE_TO_SELF,
                        0.2f
                    )
                )
                addAnimation(AlphaAnimation(1f, 0f))
                fillAfter = true
                duration = 200
                interpolator = PathInterpolator(0.3f, 0.6f, 0f, 1f)
                setAnimationListener(object : SimpleAnimationListener() {
                    override fun onAnimationEnd(animation: Animation?) {
                        llayoutContentRoot.visibility = View.GONE
                        editTimely.visibility = View.GONE
                    }
                })
                llayoutContentRoot.startAnimation(this)
            }

        }
    }

    override fun rollback2Ready() {
        surfaceView.visibility = View.GONE
        surfaceView.visibility = View.VISIBLE
        updateViewInit()
    }

    private fun updateSendBtn(isVisible: Boolean) {
        if (isVisible) {
            btnSend.visibility = View.VISIBLE
            btnSend.isClickable = true
            btnSend.isEnabled = true
        } else {
            btnSend.visibility = View.INVISIBLE
            btnSend.isClickable = false
            btnSend.isEnabled = false
        }
    }

    override fun updateViewVideoProgress(progress: Int, total: Int) {
        if (total > 0 && progress <= total) {
            if (flayoutState.visibility != View.VISIBLE) {
                flayoutState.visibility = View.VISIBLE
            }
            tvState.text = "00:" + if (progress < 10) {
                "0" + progress
            } else {
                progress
            }
            if (progress > total - 3) {
                (context?.getSystemService(Service.VIBRATOR_SERVICE) as? Vibrator)?.vibrate(50)
            }

            doProgressAnim(total)


        }

    }

    private fun doProgressAnim(total: Int) {
        if (!isProgressAnim) {
            isProgressAnim = true
            progressAnim?.cancel()
            progressAnim = ValueAnimator.ofFloat(0f, 1f).apply {
                duration = total * 1000L
                interpolator = LinearInterpolator()
                addUpdateListener {
                    val progress = it.animatedValue as Float
                    viewProgress.scaleX = progress
                }
                start()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        stateManager.releaseRecord()
        releaseVideoView()
        cameraRoot.clearAnimation()
        SoftKeyBoardListener.unRegister(activity)
        MomoMainThreadExecutor.cancelAllRunnables(getTaskTag())
        KeyBoardUtil.hideSoftKeyboardNotAlways(activity)
        TipManager.unbindActivity(activity)
        progressAnim?.cancel()
    }

    override fun onPause() {
        super.onPause()
        stateManager.interruptTimely()
    }

    private fun releaseVideoView() {
        videoView.takeIf { it.visibility == View.VISIBLE }?.let {
            it.reset()
            it.release()
            it.visibility = View.GONE
        }
    }

    fun getTaskTag(): Any {
        return this.javaClass.name + '@' + Integer.toHexString(this.hashCode())
    }

    private fun showTimelyTip(content: String) {
        if (isFistTip) {
            return
        }
        isFistTip = true
        timelyTouchView.postDelayed({
            if (stateManager.isCanClose()) {
                activity?.let { act ->
                    TipManager.bindActivity(act).let { tipManager ->
                        tipManager.setTouchToHideAll(true)
                        tipManager.setMarginEdge(UIUtils.getPixels(30f))
                        tipManager.checkViewCanShowTip(timelyTouchView) {
                            tipManager.showTipView(
                                it,
                                content,
                                0,
                                UIUtils.getPixels(20f),
                                ITip.Triangle.BOTTOM
                            )
                                ?.setNeedAnimation(true)
                                ?.setTipAnimation(DefaultTipAnimation())
                                ?.autoHide(3000L)
                        }
                    }
                }
            }
        }, 300)
    }

    private fun allowInput(isAllow: Boolean) {
        if (isAllow) {
            editCurrentLength = -1
            editTimely.requestFocus()
        } else {
            editTimely.clearFocus()
            editCurrentLength = editTimely.length()
        }
    }

    private fun enterClickLog(isLongPress: Boolean) {
        (activity as? BaseMessageActivity)?.let {
            val clickType = if (isLongPress) "end_recording" else "photo"
            val duration = if (isLongPress) stateManager.getRecordDuration().toString() else null
            when (it.chatType) {
                Message.CHATTYPE_USER -> {
                    EVLog.create(IMessageLog::class.java)
                        .timelyPressClick(
                            it.chatID,
                            null,
                            clickType,
                            duration
                        )
                }
                Message.CHATTYPE_GROUP -> {
                    EVLog.create(IMessageLog::class.java)
                        .timelyPressClick(
                            null,
                            it.chatID,
                            clickType,
                            duration
                        )
                }
            }
        }
    }

    private fun remakeLog() {
        (activity as? BaseMessageActivity)?.let {
            when (it.chatType) {
                Message.CHATTYPE_USER -> {
                    EVLog.create(IMessageLog::class.java)
                        .timelyRemakeClick(it.chatID, null, getResultType(), getResultContentType())
                }
                Message.CHATTYPE_GROUP -> {
                    EVLog.create(IMessageLog::class.java)
                        .timelyRemakeClick(null, it.chatID, getResultType(), getResultContentType())
                }
            }
        }
    }

    private fun getResultType(): String {
        return if (stateManager.isVideoReady()) "video" else "image"
    }

    private fun getResultContentType(): String {
        return if (!TextUtils.isEmpty(editTimely.text)) "1" else "0"
    }
}