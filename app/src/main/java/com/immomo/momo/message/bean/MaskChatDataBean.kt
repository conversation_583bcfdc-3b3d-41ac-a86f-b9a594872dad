package com.immomo.momo.message.bean

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import com.immomo.momo.maintab.sessionlist.bean.MaskChatQuitData
import com.immomo.momo.util.MomoKit

/**
 * 房间整天数据
 */
@Keep
data class MaskChatRoomStateDataBean(
    @SerializedName("roomId") var roomId: Int,
    @SerializedName("mood") var mood: Int,
    @SerializedName("members") var members: List<MaskChatRoomMember>?, // 房间双方成员
    @SerializedName("roomStatus") var roomStatus: Int, // 当前游戏轮次详情，房间未开始过任何轮次时为空
    @SerializedName("startedRoundCount") var startedRoundCount: Int,    // 当前游戏轮次详情，房间未开始过任何轮次时为空
    @SerializedName("currentRoundDetail") var currentRoundDetail: MaskChatCurrentRoundDetail?,
    @SerializedName("lockInputFirstRound") var lockInputFirstRound: Boolean, // 是否应在首轮锁定输入框
    @SerializedName("quitDetailResp") var quitDetailResp: MaskChatQuitDetailResp?, // 退出聊天响应数据
    @SerializedName("remainSecond") var remainSecond: Long? ,// 倒计时
    @SerializedName("roomItemData") var roomItemData: MaskRoomItemdata?,
    @SerializedName("autoGame") var autoGame: MaskChatAutoGame?,
    @SerializedName("noGameBackPopup") var backPopupData: PopupData?,
    @SerializedName("noGameEndPopup") var endPopupData: PopupData?,
    @SerializedName("gameBackToast") var gameBackToast: String?,
    @SerializedName("activityInfo") var activityInfo: MaskChatActivityInfo?,
    var extraData: String = ""
)

@Keep
data class PopupData(
    @SerializedName("title") var title: String?,
    @SerializedName("desc") var desc: String?
)

/**
 * 退出房间数据
 */
@Keep
data class MaskChatQuitDetailResp(
    @SerializedName("ec") var ec: Int,
    @SerializedName("em") var em: String?,
    @SerializedName("errcode") var errcode: Int,
    @SerializedName("errmsg") var errmsg: String?,
    @SerializedName("timesec") var timesec: Long,
    @SerializedName("data") var data: MaskChatQuitData?
)

@Keep
data class MaskRoomItemdata(
    @SerializedName("items") var items: RoomItem?,
    @SerializedName("fingerGuessCardTitle") var fingerGuessCardTitle: String?,
    @SerializedName("fingerGuessCardDesc") var fingerGuessCardDesc: String
)

@Keep
data class RoomItem(
    @SerializedName("remainAccelerateCount") var remainAccelerateCount: Int,
    @SerializedName("freeAccelerateRemainSecond") var freeAccelerateRemainSecond: Int,
    @SerializedName("fingerGuessCardRemainSec") var fingerGuessCardRemainSec: Int,
    @SerializedName("matchCardPrice") var matchCardPrice: Int,
    @SerializedName("accelerateCardPrice") var accelerateCardPrice: Int,
    @SerializedName("cardPackagePrice") var cardPackagePrice: Int
)

/**
 * 用户信息
 */
@Keep
data class MaskChatRoomMember(
    @SerializedName("momoId") var momoId: String?,
    @SerializedName("avatar") var avatar: String?,
    @SerializedName("name") var name: String?
)

/**
 * 当前游戏轮次详情，房间未开始过任何轮次时为空
 */
@Keep
data class MaskChatCurrentRoundDetail(
    @SerializedName("status") var status: Int,         // 轮次状态，1-已开始，2-已结束
    @SerializedName("creator") var creator: String?,    // 轮次发起人
    @SerializedName("winner") var winner: String?,      // 	轮次获胜方，轮次结束前都为空
    @SerializedName("startTime") var startTime: Long,   // 	本轮开始时间
    @SerializedName("endTime") var endTime: Long      // 	本轮结束时间，轮次结束前都为 0
)

@Keep
data class MaskChatAutoGame(
    @SerializedName("supportAutoGame") var supportAutoGame: Int,
    @SerializedName("triggerLeftTime") var triggerLeftTime: Int
)

/** 蒙面活动数据 */
@Keep
data class MaskChatActivityInfo(
    @Expose @SerializedName("icon") var icon: String?,
    @Expose @SerializedName("darkIcon") var darkIcon: String?,
    @Expose @SerializedName("title") var title: String?,
    @Expose @SerializedName("action") var action: String?
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString()
    ) {
    }

    fun getIconWithDark(): String? {
        return if (MomoKit.isDarkMode()) darkIcon else icon
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(icon)
        parcel.writeString(darkIcon)
        parcel.writeString(title)
        parcel.writeString(action)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<MaskChatActivityInfo> {
        override fun createFromParcel(parcel: Parcel): MaskChatActivityInfo {
            return MaskChatActivityInfo(parcel)
        }

        override fun newArray(size: Int): Array<MaskChatActivityInfo?> {
            return arrayOfNulls(size)
        }
    }
}

/** set发过来活动数据 */
@Keep
data class SetMaskChatActivityData(
    @Expose @SerializedName("dynamic") var dynamic: MaskChatActivityInfo?,
    @Expose @SerializedName("persist") var persist: MaskChatActivityInfo?
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readParcelable(MaskChatActivityInfo::class.java.classLoader),
        parcel.readParcelable(MaskChatActivityInfo::class.java.classLoader)
    ) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeParcelable(dynamic, flags)
        parcel.writeParcelable(persist, flags)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<SetMaskChatActivityData> {
        override fun createFromParcel(parcel: Parcel): SetMaskChatActivityData {
            return SetMaskChatActivityData(parcel)
        }

        override fun newArray(size: Int): Array<SetMaskChatActivityData?> {
            return arrayOfNulls(size)
        }
    }
}