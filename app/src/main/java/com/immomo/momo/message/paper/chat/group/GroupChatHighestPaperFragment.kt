package com.immomo.momo.message.paper.chat.group

import android.view.View
import com.immomo.momo.R
import com.immomo.momo.message.paper.PaperConfig
import com.immomo.momo.message.paper.common.BackgroundPaperFragment
import com.immomo.momo.message.paper.BasePaperContainerFragment
import com.immomo.momo.message.paper.common.GiftBroadcastPaperFragment

/**
 * 群聊最高层paper, 可以延伸到在状态栏里面
 */
class GroupChatHighestPaperFragment : BasePaperContainerFragment() {

    companion object {
        @JvmStatic
        fun newInstance(): GroupChatHighestPaperFragment {
            return GroupChatHighestPaperFragment()
        }
    }

    override fun getPagers(): MutableList<PaperConfig> = mutableListOf(PaperConfig(GiftBroadcastPaperFragment.newInstance()))

    override fun getPageLayout(): Int = R.layout.paper_group_chat_highest

    override fun onPageLoad() {
    }

    override fun initPageViews(contentView: View?) {
    }

}