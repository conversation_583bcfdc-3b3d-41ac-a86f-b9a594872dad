package com.immomo.momo.message.paper.common

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.PorterDuff
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.ViewStub
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.cosmos.mdlog.MDLog
import com.immomo.framework.account.MessageManager
import com.immomo.framework.base.BaseReceiver
import com.immomo.framework.imageloader.ImageLoaderUtil
import com.immomo.framework.imageloader.ImageType
import com.immomo.mmutil.NetUtils
import com.immomo.momo.IMConfigs
import com.immomo.momo.MomoKit
import com.immomo.momo.R
import com.immomo.momo.android.broadcast.LiveStatusReceiver
import com.immomo.momo.eventbus.DataEvent
import com.immomo.momo.expand.allNotNull
import com.immomo.momo.innergoto.helper.ActivityHandler
import com.immomo.momo.message.paper.BasePaperFragment
import com.immomo.momo.message.paper.PaperCommonViewModel
import com.immomo.momo.message.paper.event.PaperEvent
import com.immomo.momo.mvp.message.view.BaseMessageActivity
import com.immomo.momo.service.bean.TopBarNotice
import com.immomo.momo.util.TopBarNoticeHelper

/**
 * 通知基类
 * <AUTHOR>
 * @data 2020-12-18.
 */

open class BaseChatTopBarPaperFragment : BasePaperFragment(), TopBarNoticeHelper.NoticeCheckCallback, MessageManager.MessageSubscriber {

    var mContentView: View? = null
    var mTopBarNotice: TopBarNotice? = null
    /**
     * 顶部Notice相关View
     */
    var topBarNoticeLayout: View? = null
    private var liveReceiver: LiveStatusReceiver? = null
    var mPaperCommonViewModel: PaperCommonViewModel? = null
    private val TAG_TOPBAR_VEDIO = "video"

    private var topbarReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val eventIds = intent.getStringArrayExtra("eventIds")
            allNotNull(eventIds, mTopBarNotice) { ids, nocie ->
                if (nocie.isShow && listOf(*ids).contains(nocie.event_id)) {
                    hideTopBar()
                }
            }
        }
    }
    private val broadcastListener = BaseReceiver.IBroadcastReceiveListener { intent ->
        val action = intent.action
        if (!TextUtils.isEmpty(action) && action == LiveStatusReceiver.ACTION_LIVE_STOP) {
            if (null != mTopBarNotice && TAG_TOPBAR_VEDIO == mTopBarNotice?.tag) {
                hideTopBar()
                mTopBarNotice?.closeTime = System.currentTimeMillis()
                TopBarNoticeHelper.getInstance().saveTopBarNotice(mTopBarNotice)
            }
        }
    }

    companion object {
        fun newInstance(): BaseChatTopBarPaperFragment {
            return BaseChatTopBarPaperFragment()
        }
    }

    override fun getContainerId(): Int = R.id.chat_top_bar_paper_container

    override fun getPageLayout(): Int = R.layout.paper_chat_top_bar

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        getBaseActivity()?.let {
            mPaperCommonViewModel = ViewModelProvider(it).get(PaperCommonViewModel::class.java)
        }
    }

    override fun initPageViews(contentView: View?) {
        mContentView = contentView
        checkTopBarNotice(false)
        val filter = IntentFilter(TopBarNotice.ACTION_TOPBAR_CLOSE)
        LocalBroadcastManager.getInstance(MomoKit.getContext()).registerReceiver(topbarReceiver, filter)
        liveReceiver = LiveStatusReceiver(MomoKit.getContext())
        liveReceiver?.setReceiveListener(broadcastListener)
        MessageManager.registerMessageReceiver(
                this.hashCode(),
                this,
                800,
                IMConfigs.Action_IMJWarning)
    }

    override fun onPageLoad() {

    }


    /**
     * 检查topbar数据
     *
     * @param isForceRefresh 是否强制刷新
     */
    open fun checkTopBarNotice(isForceRefresh: Boolean) {
        //子类实现
    }

    override fun callback(topBarNotice: TopBarNotice?) {
        topBarNotice?.let {
            processTopBarNotice(topBarNotice)
        }
    }

    private fun processTopBarNotice(topBarNotice: TopBarNotice) {
        MDLog.i("KliaoTalent", "yichao ===== processTopBarNotice:%s", topBarNotice.toString())
        if (!topBarNotice.isShow) {
            hideTopBar()
            return
        }
        if (System.currentTimeMillis() > topBarNotice.closeTime + topBarNotice.showStep || TextUtils.equals(topBarNotice.business, TopBarNotice.BUSI_KLIAO_TALENT)) {
            mTopBarNotice = topBarNotice
            if (this is GroupChatTopBarPaperFragment) {
                mPaperCommonViewModel?.updateTopBar(Bundle().apply {
                    putBoolean(GroupChatTopBarPaperFragment.KEY_TOP_BAR_NOTICE_SHOW, true)
                    putSerializable(GroupChatTopBarPaperFragment.KEY_TOP_BAR_NOTICE, topBarNotice)
                })
            } else {
                showTopBar(topBarNotice)
            }
        } else {
            hideTopBar()
        }
    }


    open fun showTopBar(topBarNotice: TopBarNotice): Boolean {
        if (!topBarNotice.isValidNotice || !NetUtils.isNetworkAvailable()) {
            hideTopBar()
            return false
        }
        if (topBarNoticeLayout == null) {
            val viewStub = mContentView?.findViewById(R.id.viewstub_chat_topbar_layout) as? ViewStub
            if (null != viewStub) {
                topBarNoticeLayout = viewStub.inflate()
            }
        }
        topBarNoticeLayout?.let {
            val topBarTitleView = it.findViewById<TextView>(R.id.chat_tv_notice_hongbao_note)
            topBarTitleView.text = topBarNotice.title
            val topBarContentView = it.findViewById<TextView>(R.id.chat_tv_notice_hongbao_content)
            topBarContentView.text = topBarNotice.desc
            val topBarNoticeButton = it.findViewById<TextView>(R.id.chat_btn_notice_hongbao_send)
            val topBarNoticeButton2 = it.findViewById<TextView>(R.id.chat_btn_notice_hongbao_send2)
            //控制右箭头
            if (topBarNotice.btnstyle == 1) {
                it.findViewById<View>(R.id.chat_btn_notice_arrow).visibility = View.VISIBLE
            } else {
                it.findViewById<View>(R.id.chat_btn_notice_arrow).visibility = View.INVISIBLE
            }
            //头像显示
            val iconImgView = it.findViewById<ImageView>(R.id.chat_tv_notice_hongbao_icon)
            if (!TextUtils.isEmpty(topBarNotice.avatar)) {
                iconImgView.visibility = View.VISIBLE
                ImageLoaderUtil.loadRoundImage(topBarNotice.avatar, ImageType.IMAGE_TYPE_URL,
                        iconImgView, null, 4, true, 0)
            } else {
                iconImgView.visibility = View.GONE
            }
            //按钮有无边框
            if (topBarNotice.is_btn == TopBarNotice.BTN_BORDER_NONE) {
                topBarNoticeButton.setVisibility(View.INVISIBLE)
                topBarNoticeButton2.setText(topBarNotice.gotoTitle)
                topBarNoticeButton2.setVisibility(View.VISIBLE)
                topBarNoticeButton2.setTextColor(topBarNotice.btnTextColorUI)
            } else if (topBarNotice.is_btn == TopBarNotice.BTN_BORDER_SERVER) {
                topBarNoticeButton2.setVisibility(View.GONE)
                topBarNoticeButton.setVisibility(View.VISIBLE)
                topBarNoticeButton.setText(topBarNotice.gotoTitle)
                topBarNoticeButton.setTextColor(topBarNotice.btnTextColorUI)
                val myGrid = topBarNoticeButton.getBackground() as GradientDrawable
                myGrid.setColorFilter(topBarNotice.btnColor, PorterDuff.Mode.SRC_IN)
            }
            it.setOnClickListener { v ->
                getBaseActivity()?.let { baseActivity ->
                    ActivityHandler.executeAction(topBarNotice.gotoString, baseActivity)

                }
                hideTopBar()
                topBarNotice.closeTime = System.currentTimeMillis()
                TopBarNoticeHelper.getInstance().saveTopBarNotice(topBarNotice)
            }
            it.visibility = View.VISIBLE
            return true
        }
        return false

    }


    open fun hideTopBar() {
        topBarNoticeLayout?.visibility = View.GONE
    }

    override fun onMessageReceive(bundle: Bundle?, action: String?): Boolean {
        when (action) {
            IMConfigs.Action_IMJWarning -> {
                hideTopBar()
            }
        }
        return false
    }


    override fun onEvent(event: DataEvent<Any>) {
        super.onEvent(event)
        when (event.action) {
            PaperEvent.PAPER_EVENT_CHECK_TOP_BRA_NOTICE -> {
                (event.data as? Boolean)?.let {
                    checkTopBarNotice(it)
                }
            }
            PaperEvent.PAPER_EVENT_HIDE_TOP_BRA_NOTICE -> {
                hideTopBar()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        LocalBroadcastManager.getInstance(MomoKit.getContext()).unregisterReceiver(topbarReceiver)
        liveReceiver?.unRegister()
        MessageManager.unregisterMessageReceiver(this.hashCode())
    }

    fun getBaseActivity(): BaseMessageActivity? {
        (activity as? BaseMessageActivity)?.let {
            return it
        }
        return null
    }

}