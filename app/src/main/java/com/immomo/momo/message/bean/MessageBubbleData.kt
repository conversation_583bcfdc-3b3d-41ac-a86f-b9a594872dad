package com.immomo.momo.message.bean

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

/**
 * <AUTHOR>
 * @data 7/8/21.
 */
data class MessageBubbleData(
    @Expose
    @SerializedName("text_color")
    var textColor: String = "#FFFFFF",
    @Expose
    @SerializedName("link_color")
    var linkColor: String = "#1684ff",
    @Expose
    @SerializedName("animation_config")
    var config: AnimConfig? = null
) {

    fun hasAnim(): Boolean {
        config?.let {
            // 动画图片数量大于60，不播放动画。
            return it.count > 0 && it.duration > 0 && it.count <= 60
        }
        return false
    }

    data class AnimConfig(
        @Expose
        @SerializedName("frame_count")
        var count: Int = 0,
        @Expose
        @SerializedName("duration")
        var duration: Long = 0L
    )


}