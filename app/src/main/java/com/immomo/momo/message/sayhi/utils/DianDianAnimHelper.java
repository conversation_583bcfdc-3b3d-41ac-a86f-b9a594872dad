package com.immomo.momo.message.sayhi.utils;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;

/**
 * Created by lei.ji<PERSON>n on 2019/4/9.
 */
public class DianDianAnimHelper {

    public static class LayoutParams {
        public static RelativeLayout.LayoutParams centerRelative(int w, int h) {
            RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(w, h);
            lp.addRule(RelativeLayout.CENTER_IN_PARENT, RelativeLayout.TRUE);
            return lp;
        }

        public static FrameLayout.LayoutParams centerFrameLayout(int w, int h) {
            FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(w, h);
            lp.gravity = Gravity.CENTER;
            return lp;
        }
    }

    public static class Listener {

        public static AnimatorListenerAdapter showOnlyDuringAnim(final View view) {
            return new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    view.setVisibility(View.GONE);
                }

                @Override
                public void onAnimationStart(Animator animation) {
                    view.setVisibility(View.VISIBLE);
                }
            };
        }
    }
}
