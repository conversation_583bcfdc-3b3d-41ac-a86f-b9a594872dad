package com.immomo.momo.message.sayhi.activity

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import com.cosmos.mdlog.MDLog
import com.immomo.framework.base.BaseActivity
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.R
import com.immomo.momo.appconfig.model.AppConfigV2
import com.immomo.momo.globalevent.GlobalEventManager
import com.immomo.momo.likematch.slidestack.BaseSlideStackView
import com.immomo.momo.likematch.slidestack.SlideConst
import com.immomo.momo.message.sayhi.SayHiArgs
import com.immomo.momo.message.sayhi.itemmodel.bean.LikeSayHi
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiStackCardInfo
import com.immomo.momo.message.sayhi.presenter.ISayHiGiftPresenter
import com.immomo.momo.message.sayhi.presenter.SayHiStackGiftPresenter
import com.immomo.momo.message.sayhi.stack.SayHiGiftStackAdapter
import com.immomo.momo.message.sayhi.stack.SayHiGiftStackView
import com.immomo.momo.message.sayhi.utils.OnSendFinishListener
import com.immomo.momo.message.sayhi.utils.SayHiConst
import com.immomo.momo.message.sayhi.utils.SayHiQuickReplyHelperView
import com.immomo.momo.message.sayhi.utils.SayHiReportUtil
import com.immomo.momo.platform.utils.PlatformReportHelper
import com.immomo.momo.service.bean.SayhiSession
import java.util.*

class SayHiGiftActivity : BaseActivity(), GlobalEventManager.Subscriber {
    private var mPresenter: ISayHiGiftPresenter? = null
    private var slideStackView: SayHiGiftStackView? = null
    private var tvIndex: TextView? = null
    private var imgClose: View? = null

    private var stackAdapter = SayHiGiftStackAdapter()
    private var scrollAccumulateY = 0f
    private var prepared = false
    private var sayHiQuickReplyHelperView: SayHiQuickReplyHelperView? = null
    private var curSessionInfo: SayHiStackCardInfo? = null

    companion object {
        /**
         * 页面是否存活
         */
        @JvmStatic
        var isAlive = false
    }

    private val switchListener = object :
            BaseSlideStackView.CardSwitchListener {
        private var curShowIndex = -1


        override fun showGiftPanel(index: Int) {
            //do nothing
        }

        override fun onShow(index: Int) {
            val info = stackAdapter.getItem(index)
            curSessionInfo = info
            curShowIndex = index
            if (info != null) {
                val card = slideStackView?.getSlideItem(0)
                if (card != null) {
                    // create(IGreetLog::class.java).showGiftCard(info.sayHiInfo.momoid)
                    card.clickIgnore = {
                        // create(IGreetLog::class.java).clickGiftIgnore(info.sayHiInfo.momoid)
                        SayHiReportUtil.cardIgnore(info.sayHiInfo)
                        vanishCardTo(SlideConst.VANISH_TYPE_LEFT, SlideConst.SlideSource.CARD, true)
                    }
                    card.clickPass = {
                        SayHiReportUtil.cardConfirm(info.sayHiInfo)
                        vanishCardTo(
                                SlideConst.VANISH_TYPE_RIGHT,
                                SlideConst.SlideSource.CARD,
                                true
                        )
                    }
                    card.clickChat = { sessionInfo ->
                        SayHiReportUtil.cardChat(info.sayHiInfo)
                        sayHiQuickReplyHelperView?.showComment(
                                sessionInfo?.sayHiInfo, sessionInfo?.sayHiNetInfo,
                                object : OnSendFinishListener {
                                    override fun onSend() {
                                        vanishCardTo(
                                                SlideConst.VANISH_TYPE_RIGHT,
                                                SlideConst.SlideSource.CARD,
                                                false
                                        )
                                    }
                                })
                    }
                }
                mPresenter?.updateToHiIgnore(info.sayHiInfo.momoid)
                SayHiConst.onSessionExposure(info.sayHiInfo.momoid)
                SayHiReportUtil.reportSessionShown(info.sayHiInfo, info.sayHiNetInfo, true)
            }
            if (index == stackAdapter.size - 2) { // 如果滑到了最后一张
                GlobalEventManager.getInstance().sendEvent(GlobalEventManager.Event(SayHiConst.EVENT_SAYHI_LIST_LOADMORE_DATA).src(GlobalEventManager.EVN_NATIVE).dst(GlobalEventManager.EVN_NATIVE))
            }
            checkDataEmpty()
        }

        override fun onCardVanish(
                index: Int, type: Int, touchType: String, picDepth: Int, source: String,
                needRequest: Boolean, toApiParams: Map<String?, String?>?
        ) {
            scrollAccumulateY = 0f
            val info = stackAdapter.getItem(index)
            when (type) {
                SlideConst.VANISH_TYPE_LEFT -> onCardSlideToLeft(needRequest, info)

                SlideConst.VANISH_TYPE_RIGHT -> onCardSlideToRight(needRequest)

                else -> {
                }
            }
            val nextCardIndex = index + 1
            if (stackAdapter.isIndexValid(nextCardIndex)) {
                curShowIndex = nextCardIndex
            }

            // refreshCardCount(curShowIndex + 1, stackAdapter.size)
            refreshCardCount(stackAdapter.getCurrentTotal(true))
        }

        override fun onItemClick(cardImageView: View, index: Int, dianDianPhotoIndex: Int) {

        }

        override fun onSlide(rate: Float) {
            //do nothing
        }

        override fun onPreventRightSlide() {
        }

        override fun nextCardNeedAnimBlock(indexInDataList: Int) {
            setBtnLock(true)
        }
    }

    private fun notifySessionListChangePlace(sayHiInfo: SayhiSession?) {
        sayHiInfo ?: return
        val event = GlobalEventManager.Event(SayHiConst.EVENT_SAYHI_STACK_CARD_STACK_LEFT).src(GlobalEventManager.EVN_NATIVE).dst(GlobalEventManager.EVN_NATIVE)
        val data = hashMapOf<String, Any>()
        sayHiInfo.momoid?.also {
            data["momoid"] = it
        }
        event.msg(data)
        GlobalEventManager.getInstance().sendEvent(event)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_sayhi_gift)
        isAlive = true
        initViews()
        initData()
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(0, R.anim.layout_alpha_out)
    }

    override fun onPause() {
        super.onPause()
        sayHiQuickReplyHelperView?.onPause()
    }

    override fun onResume() {
        super.onResume()
        if (!prepared) {
            return
        }
        sayHiQuickReplyHelperView?.onResume()
        slideStackView?.onResume()
    }

    override fun onDestroy() {
        isAlive = false
        SayHiConst.unExposureSayHiList?.clear()
        slideStackView?.onDestroy()
        GlobalEventManager.getInstance().unregister(this, GlobalEventManager.EVN_NATIVE)
        super.onDestroy()
    }

    override fun initStatusBar() {
        super.initStatusBar()
        val window = window
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        window.decorView.systemUiVisibility =
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
        getWindow().statusBarColor = UIUtils.getColor(R.color.transparent)
    }

    private fun initViews() {
        tvIndex = findViewById(R.id.tv_index) as TextView?
        imgClose = findViewById(R.id.img_close)
        slideStackView = findViewById(R.id.slideStackView) as? SayHiGiftStackView
        slideStackView?.setRemainCount(KV.getUserInt(AppConfigV2.SPKeys.KEY_HI_LOADMORE_REMAIN, 3))
        slideStackView?.setAdapter(stackAdapter)
        slideStackView?.setCardSwitchListener(switchListener)
        // slideStackView?.setNoSlide(MomoKit.isManUser())
        sayHiQuickReplyHelperView = findViewById(R.id.quick_reply_view) as SayHiQuickReplyHelperView?

        imgClose?.setOnClickListener {
            SayHiConst.clearSlideCardData()
            finish()
            SayHiReportUtil.cardClose(curSessionInfo?.sayHiInfo)
        }

    }

    private fun initData() {
        if (SayHiConst.unExposureSayHiList == null || SayHiConst.unExposureSayHiList?.isEmpty() == true) {
            finish()
            return
        }
        mPresenter = SayHiStackGiftPresenter()
        refreshCards()
        prepared = true
        GlobalEventManager.getInstance().register(this, GlobalEventManager.EVN_NATIVE)
    }

    private fun refreshCards() {
        stackAdapter.addTotalCount(SayHiConst.unExposureSayHiList?.size ?: 0, true)
        slideStackView?.fillData(SayHiConst.getSayHiCard())
        refreshCardCount()
    }

    private fun checkDataEmpty() {
        if (slideStackView?.noDataAvailable() == true) {
            if (SayHiConst.needShowCardStack()) { // 如果加载更多有新的数据
                SayHiConst.refreshLoadMoreCards()
                refreshCards()
            } else {
                finish()
            }
        }
    }

    private fun vanishCardTo(slideType: Int, source: String, needRequest: Boolean) {
        if (slideStackView?.noDataAvailable() == true) {
            return
        }
        slideStackView?.orderViewStack()
        slideStackView?.vanishOnBtnClick(slideType, source, needRequest, null)
    }


    private fun onCardSlideToLeft(needRequest: Boolean, sayhiInfo: SayHiStackCardInfo?) {
        if (needRequest) {
            postIgnoreOrLike(
                    SayHiArgs.IGNORE,
                    LikeSayHi.Requst.TYPE_LEFT
            )
            sayhiInfo?.also {
                notifySessionListChangePlace(it.sayHiInfo)
            }
        }
    }

    private fun onCardSlideToRight(needRequest: Boolean) {
        if (needRequest) {
            postIgnoreOrLike(
                    SayHiArgs.LIKE,
                    LikeSayHi.Requst.TYPE_RIGHT
            )
        }
    }

    private fun postIgnoreOrLike(
            likeType: Int, consumeType: Int
    ) {
        val info = slideStackView?.getInfoAt(slideStackView?.showingDataIndex ?: -1)
        mPresenter?.postIgnoreOrLike(
                likeType,
                info?.sayHiInfo,
                consumeType
        )
    }

    private fun refreshCardCount() {
        if (stackAdapter.size == 0) {
            return
        }
        stackAdapter.getCurrentTotal(false)?.let { refreshCardCount(it) }
    }

    private fun refreshCardCount(currentIndex: IntArray) {
        refreshCardCount(currentIndex[0], currentIndex[1])

    }

    private fun refreshCardCount(current: Int, total: Int) {
        tvIndex?.visibility = if (total > 0 && total >= current) View.VISIBLE else View.GONE
        tvIndex?.text = String.format(Locale.getDefault(), "新招呼（%d/%d）", current, total)
    }

    /**
     * 准备播放动画时不允许点击及拖动卡片
     *
     * @param isAniming
     */
    fun setBtnLock(isAniming: Boolean) {
        slideStackView?.btnLock = isAniming
    }

    override fun onBackPressed() {
        super.onBackPressed()
        SayHiConst.clearSlideCardData()
        SayHiReportUtil.cardClose(curSessionInfo?.sayHiInfo)
    }

    fun afterBlockAndReportSuccess(event:String, remoteId: String? = null, action: Int? = null) {
        val event = GlobalEventManager.Event(event).src(GlobalEventManager.EVN_NATIVE).dst(GlobalEventManager.EVN_NATIVE)
        val data = hashMapOf<String, Any>()
        remoteId?.also {
            data["momoid"] = it
        }
        action?.also {
            data["action"] = action
        }
        curSessionInfo?.sayHiInfo?.momoid?.also {
            data["userid"] = it
        }
        event.msg(data)
        GlobalEventManager.getInstance().sendEvent(event)
        vanishCardTo(
                SlideConst.VANISH_TYPE_LEFT,
                SlideConst.SlideSource.CARD,
                false
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (PlatformReportHelper.ACT_RES_REPORT == requestCode && data != null) {
            val status = data.getIntExtra(
                    PlatformReportHelper.WEB_RESULT_STATUS,
                    PlatformReportHelper.ACTION_FAILED
            )
            if (status == PlatformReportHelper.ACTION_SUCCESS) {
                val action = data.getIntExtra(PlatformReportHelper.WEB_RESULT_ACTION, -1)
                afterBlockAndReportSuccess(SayHiConst.EVENT_SAYHI_STACK_CARD_WEB_REPORT, null, action)
            }
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    override fun onGlobalEventReceived(event: GlobalEventManager.Event?) {
        event?:return
        when(event.name) {
            SayHiConst.EVENT_SAYHI_CARD_FINISHED_LOADMORE_DATA -> { // 当下一页的数据加载到了时
                if (SayHiConst.needShowCardStack()) {
                    SayHiConst.refreshLoadMoreCards()
                    val list = SayHiConst.getSayHiCard()
                    if (list.isNotEmpty()) {
                        stackAdapter.addTotalCount(list.size, false)
                        slideStackView?.appendData(list)
                        if (tvIndex?.visibility != View.VISIBLE) {
                            refreshCardCount(stackAdapter.getCurrentTotal(true))
                        }
                    }
                }
            }
        }
    }

}