package com.immomo.momo.message.sayhi.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.widget.ImageView
import android.widget.TextView
import com.immomo.momo.MomoKit
import com.immomo.momo.R
import com.immomo.momo.android.view.OnAnimationEndListener

/**
 * <AUTHOR>
 * @data 2020-08-25.
 */

class FemaleSayHiInputView : SayHiInputView {
    //蓝色按钮消失动画
    private var femaleGreetDisappear: Animation? = null

    //蓝色按钮显示动画
    private var femaleGreetAppear: Animation? = null

    //输入框消失动画
    private var femaleGreetAlpha2: Animation? = null
    var card_middle_btn: View? = null
    var rl_middle: View? = null
    var likeButton: ImageView? = null
    var inputHint: TextView? = null


    constructor(context: Context) : super(context)

    constructor(context: Context, attributeSet: AttributeSet) : super(context, attributeSet)

    constructor(context: Context, attributeSet: AttributeSet, defStyleAttr: Int) : super(
        context,
        attributeSet,
        defStyleAttr
    )

    override fun createView() {
        LayoutInflater.from(context).inflate(R.layout.layout_female_say_hi_input, this, true)
        dislikeButton = findViewById(R.id.card_left_btn)
        likeButton = findViewById(R.id.card_right_btn)
        card_middle_btn = findViewById(R.id.card_middle_btn)
        rl_middle = findViewById(R.id.rl_middle)
        llStartChat = findViewById(R.id.ll_start_chat)
        ivFlash = findViewById(R.id.iv_flash)
        if (com.immomo.momo.util.MomoKit.isDarkMode(context)) {
            ivFlash?.visibility = GONE
        }
        rlFemaleInput = findViewById(R.id.rl_female_input)
        inputHint = findViewById(R.id.input_hint)
        card_middle_btn?.setOnClickListener {
            sayHiInputListener?.showCommentEdit()
        }

        rl_middle?.setOnClickListener {
            sayHiInputListener?.showCommentEdit()
        }
        likeButton?.setOnClickListener {
            sayHiInputListener?.onLikeBtnClick()
        }

    }

    fun initMiddleBtnAnim() {
        //蓝色按钮消失动画 消失后执行
        femaleGreetDisappear =
            AnimationUtils.loadAnimation(MomoKit.getContext(), R.anim.anim_female_greet_disappear)
        femaleGreetDisappear?.setAnimationListener(object : OnAnimationEndListener() {
            override fun onAnimationEnd(animation: Animation) {
                card_middle_btn?.visibility = View.GONE
            }
        })
        card_middle_btn?.startAnimation(femaleGreetDisappear)
    }

    fun startMiddleBtnAnim() {
        if (card_middle_btn?.visibility == View.GONE) {
            card_middle_btn?.clearAnimation()
            femaleGreetAppear =
                AnimationUtils.loadAnimation(MomoKit.getContext(), R.anim.anim_female_greet_appear)
            card_middle_btn?.visibility = View.VISIBLE
            card_middle_btn?.startAnimation(femaleGreetAppear)
            femaleGreetAlpha2 = AnimationUtils.loadAnimation(
                MomoKit.getContext(),
                R.anim.anim_female_greet_alpha_to_one
            )
            femaleGreetAlpha2?.setAnimationListener(object : OnAnimationEndListener() {
                override fun onAnimationEnd(animation: Animation) {
                    llStartChat?.visibility = View.INVISIBLE
                }
            })
            llStartChat?.animation = femaleGreetAlpha2
        }
    }

    fun middleBanVisible(): Boolean = card_middle_btn?.visibility == View.VISIBLE


    fun scaleMiddle(p: Float) {
        rl_middle?.scaleX = p
        rl_middle?.scaleY = p
        card_middle_btn?.scaleX = p
        card_middle_btn?.scaleY = p
    }

    fun getRLMiddleWidth(): Int = rl_middle?.width ?: 0

    fun getRLMiddleHeight(): Int = rl_middle?.height ?: 0


    fun setRLMiddleRLayoutParams(ll: ViewGroup.LayoutParams) {
        rl_middle?.layoutParams = ll
    }

    fun getRLMiddleRLayoutParams(): ViewGroup.LayoutParams? = rl_middle?.layoutParams

    fun cancelMiddleBtnAnim() {
        femaleGreetDisappear?.let {
            if (!it.hasEnded()) {
                it.cancel()
            }
        }
    }

    fun cancelAnim() {
        femaleGreetAlpha2?.let {
            if (!it.hasEnded()) {
                it.cancel()
            }
        }
        femaleGreetAppear?.let {
            if (!it.hasEnded()) {
                it.cancel()
            }
        }
        cancelMiddleBtnAnim()
    }

    fun clearAnim() {
        card_middle_btn?.clearAnimation()
        rl_middle?.clearAnimation()
    }

    fun scaleLikeButton(f: Float) {
        likeButton?.scaleX = f
        likeButton?.scaleY = f
    }

    override fun setLikeBtnRes(resId: Int) {
        likeButton?.setImageResource(resId)
    }

    override fun setInputHint(text: String) {
        inputHint?.text = text
    }
}
