package com.immomo.momo.message.paper.chat.multi

import android.view.View
import com.immomo.momo.R
import com.immomo.momo.message.paper.PaperConfig
import com.immomo.momo.message.paper.BasePaperContainerFragment
import com.immomo.momo.message.paper.common.ChatAudioCoverPaperFragment

/**
 * <AUTHOR>
 * @data 2020-12-18.
 */

class MultiChatTopPaperFragment : BasePaperContainerFragment() {

    companion object {
        @JvmStatic
        fun newInstance(): MultiChatTopPaperFragment {
            return MultiChatTopPaperFragment()
        }
    }

    override fun getPagers(): MutableList<PaperConfig>? =
            mutableListOf(
                    PaperConfig(ChatAudioCoverPaperFragment.newInstance())
            )

    override fun getPageLayout(): Int = R.layout.paper_multi_chat_top

    override fun onPageLoad() {
    }

    override fun initPageViews(contentView: View?) {
    }

}