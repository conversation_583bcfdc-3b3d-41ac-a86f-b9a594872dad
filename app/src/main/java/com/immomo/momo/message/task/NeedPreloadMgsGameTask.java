package com.immomo.momo.message.task;

import com.immomo.mgs.sdk.ui.MgsView;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.group.mgs_game.GroupFileUtil;
import com.immomo.momo.message.contract.IMsgChatDataHolder;
import com.immomo.momo.message.contract.IMsgChatRecycler;
import com.immomo.momo.protocol.http.GroupApi;
import com.immomo.momo.service.bean.Message;
import com.immomo.momo.service.bean.message.Type28Content;
import com.immomo.momo.util.StringUtils;

import org.json.JSONObject;

import java.util.List;

import static com.immomo.momo.service.bean.Message.CONTENTTYPE_MESSAGE_MGS_GROUP;

/**
 * <AUTHOR>
 * @date 2019/5/18
 * @description com.immomo.momo.message.task
 */
public class NeedPreloadMgsGameTask extends MomoTaskExecutor.Task<Object, Object, Object> {
    private IMsgChatRecycler msgChatRecycler;
    private IMsgChatDataHolder msgChatData;

    public NeedPreloadMgsGameTask(IMsgChatRecycler msgChatRecycler, IMsgChatDataHolder msgChatData) {
        this.msgChatRecycler = msgChatRecycler;
        this.msgChatData = msgChatData;
    }


    @Override
    protected Object executeTask(Object... objects) throws Exception {
        boolean needPreload = false;
        Message messageCurrent = null;

        if (msgChatRecycler == null || msgChatData == null) {
            return null;
        }

        List<Message> messageList = msgChatData.getMessageList();
        if (messageList == null || messageList.size() == 0) {
            return null;
        }

        for (Message message : messageList) {
            if (CONTENTTYPE_MESSAGE_MGS_GROUP == message.contentType) {
                needPreload = true;
                messageCurrent = message;
                break;
            }
        }
        if (!needPreload) {
            return null;
        }

        String oldConfig = GroupFileUtil.getLocal(messageCurrent.getMessageContent(Type28Content.class).appId);
        if (!StringUtils.isEmpty(oldConfig)) {
            return null;
        }

        //下载数据并且保存
        String config = GroupApi.getInstance().getMgsGroupConfig(messageCurrent.getMessageContent(Type28Content.class).appId, "group","");
        GroupFileUtil.saveLocal(messageCurrent.getMessageContent(Type28Content.class).appId, config);

        //im有配置信息需要动态填充，没有不处理(下载游戏zip包config)
        JSONObject configOb = new JSONObject(config);
        JSONObject appConfigOb = configOb.optJSONObject("appConfig");
        if (appConfigOb == null) {//后台数据有问题
            return null;
        }

        if (!StringUtils.isEmpty(messageCurrent.getMessageContent(Type28Content.class).develop)) {
            JSONObject developOb = new JSONObject(messageCurrent.getMessageContent(Type28Content.class).develop);
            appConfigOb.put("dev", developOb);
        }
        MgsView.downloadGame(appConfigOb.toString());
        return null;
    }

    @Override
    protected void onTaskError(Exception e) {
    }
}
