package com.immomo.momo.message.sayhi.usecase

import com.immomo.framework.rxjava.interactor.UseCase
import com.immomo.mmutil.task.MMThreadExecutors
import com.immomo.momo.service.bean.SayhiSession
import com.immomo.momo.service.sessions.SessionService
import io.reactivex.Flowable

/**
 * 心动招呼已读
 */
class HeartbeatSessionAllReadUseCase(var fromType: Int) : UseCase<Boolean, String>(
    MMThreadExecutors.Message, MMThreadExecutors.Main
) {

    override fun buildUseCaseFlowable(params: String?): Flowable<Boolean> {
        return Flowable.fromCallable<Boolean> {
            when (fromType) {
                -1 -> {
                    SessionService.getInstance().updateAllHiHeartbeat(SayhiSession.FROM_TYPE_NORMAL)
                    SessionService.getInstance().updateAllHiHeartbeat(SayhiSession.FROM_TYPE_GIFT)
                    SessionService.getInstance().updateAllHiHeartbeat(SayhiSession.FROM_TYPE_LIVE)
                }

                SayhiSession.FROM_TYPE_GIFT -> {
                    SessionService.getInstance().updateAllHiHeartbeat(SayhiSession.FROM_TYPE_GIFT)
                }

                SayhiSession.FROM_TYPE_LIVE -> {
                    SessionService.getInstance().updateAllHiHeartbeat(SayhiSession.FROM_TYPE_LIVE)
                }
            }
            return@fromCallable true
        }
    }

}