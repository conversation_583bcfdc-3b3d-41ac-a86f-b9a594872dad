package com.immomo.momo.message.task

import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.momo.message.bean.UserEntryChatBean
import com.immomo.momo.message.http.SpamSessionApi

class UserChatEntryTask(var remoteId: String,
                        var chatScene: String,
                        var isPeekMode: Boolean,
                        var chatHistory: String,
                        var block: (UserEntryChatBean?) -> Unit) :
    MomoTaskExecutor.Task<Any, Any, UserEntryChatBean>("") {

    override fun executeTask(vararg params: Any): UserEntryChatBean? {
        return SpamSessionApi.getInstance().getSpamStatus(remoteId, chatScene, isPeekMode,chatHistory)
    }

    override fun onTaskSuccess(result: UserEntryChatBean?) {
        block.invoke(result)
    }
}