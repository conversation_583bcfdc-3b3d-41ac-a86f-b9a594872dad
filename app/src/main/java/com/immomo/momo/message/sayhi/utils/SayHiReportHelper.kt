package com.immomo.momo.message.sayhi.utils

import android.content.DialogInterface
import com.immomo.framework.base.BaseActivity
import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.momo.android.view.dialog.MAlertDialog
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo
import com.immomo.momo.message.sayhi.task.ReportForbiddenTask
import com.immomo.momo.platform.utils.PlatformReportHelper
import com.immomo.momo.service.bean.SayhiSession
import java.lang.ref.WeakReference

object SayHiReportHelper {

    @JvmStatic
    fun showReportDialog(activity: BaseActivity?, sayhiSession: SayhiSession, sayHiNetInfo: SayHiInfo?) {
        activity ?: return
        val momoid = sayhiSession.momoid
        //拉黑并全站禁言他
        val forbiddenDialog = MAlertDialog(activity)
        forbiddenDialog.setTitle("提示")
        var content = "如果该招呼让你感到被冒犯，你可以选择匿名禁言" + (if (sayhiSession.user?.isFemale == true) "她" else "他") + "并拉黑（每日一次），或者举报"
        sayHiNetInfo?.also {
            if (!it.forbiddenBtnContent.isNullOrBlank()) {
                content = it.forbiddenBtnContent
            }
        }
        forbiddenDialog.setMessage(content)
        forbiddenDialog.setButton(
                MAlertDialog.INDEX_LEFT,
                "匿名禁言" + (if (sayhiSession.user?.isFemale == true) "她" else "他") + "并拉黑"
        ) { _: DialogInterface?, _: Int ->
            MomoTaskExecutor.executeUserTask(
                    activity.taskTag,
                    ReportForbiddenTask(WeakReference(activity), momoid.toString())
            )
        }
        forbiddenDialog.setButton(
                MAlertDialog.INDEX_RIGHT, "举报"
        ) { _: DialogInterface?, _: Int ->
            PlatformReportHelper.startReportByMomoid(
                    activity, PlatformReportHelper.REPORT_BIZ_GREETING_CARD,
                    momoid, PlatformReportHelper.REPORT_TYPE_SOURCE_FROM_NONE
            )
        }
        forbiddenDialog.setButton(
                MAlertDialog.INDEX_THIRD,
                "取消",
                forbiddenDialog.defaultButtonOnclick
        )
        forbiddenDialog.setSupportDark(true)
        activity.showDialog(forbiddenDialog)
    }

}