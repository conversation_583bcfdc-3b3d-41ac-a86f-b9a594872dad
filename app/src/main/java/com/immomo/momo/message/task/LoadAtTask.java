package com.immomo.momo.message.task;

import android.text.TextUtils;

import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.eventbus.DataEvent;
import com.immomo.momo.message.activity.GroupChatActivity;
import com.immomo.momo.message.bean.MessageGiftData;
import com.immomo.momo.message.contract.IMsgChatDataHolder;
import com.immomo.momo.message.contract.IMsgChatRecycler;
import com.immomo.momo.message.paper.event.PaperEvent;
import com.immomo.momo.message.presenter.GroupChatCommonPresenter;
import com.immomo.momo.service.bean.Message;
import com.immomo.momo.service.bean.User;

import java.util.ArrayList;
import java.util.List;

import de.greenrobot.event.EventBus;

import static com.immomo.momo.mvp.message.view.BaseMessageActivity.PAGE_SIZE;


/**
 * 滚到@位置的异步任务
 *
 * <AUTHOR>
 * date 2020/8/8
 */
public class LoadAtTask extends MomoTaskExecutor.Task<Object, Object, List<Message>> {

    private GroupChatActivity mActivity;
    private IMsgChatRecycler mMsgChatRecycler;
    private IMsgChatDataHolder mMsgChatData;
    private User currentUser;
    private GroupChatCommonPresenter chatCommonPresenter;
    private int atPos = -1;
    private int scrollType;
    private Message giftContent;


    public LoadAtTask(int scrollType) {
        super();
        this.scrollType = scrollType;
    }

    public LoadAtTask(GroupChatActivity mActivity, IMsgChatRecycler msgChatRecycler, IMsgChatDataHolder msgChatData,
                      User currentUser, GroupChatCommonPresenter chatCommonPresenter, int scrollType) {
        super();
        this.mActivity = mActivity;
        this.mMsgChatRecycler = msgChatRecycler;
        this.mMsgChatData = msgChatData;
        this.currentUser = currentUser;
        this.chatCommonPresenter = chatCommonPresenter;
        this.scrollType = scrollType;
    }

    @Override
    protected List<Message> executeTask(Object... params) {
        mActivity.mIMLogRecorder.logMsg(this, "LoadAtTask", " start executeTask ");
        List<Message> tempMsgs = new ArrayList<>(mMsgChatData.getMessageList());
        List<Message> messages = null;
        if (mActivity.getUnReadScrollCount() > 0) {
            messages = mActivity.loadMoreMessages(mActivity.getUnReadScrollCount(), true, false);
            mActivity.mIMLogRecorder.logLoadMoreMessagesForAt(messages);
            tempMsgs.addAll(0, messages);
        }
        int index = 0;
        if (mActivity.getTotalUnreadCount() < PAGE_SIZE && tempMsgs.size() >= PAGE_SIZE) {
            //找到未读消息的位置
            index = PAGE_SIZE - mActivity.getTotalUnreadCount();
        }
        for (; index < tempMsgs.size(); index++) {
            if (scrollType == 1) {
                if (tempMsgs.get(index).isAtMe) {
                    atPos = index;
                    break;
                }
            } else if (scrollType == 2) {
                Message msg = tempMsgs.get(index);
                if (msg.isGiftMsg() && TextUtils.equals(msg.receiveId, currentUser.momoid)) {
                    atPos = index;
                    giftContent = msg;
                }
            }
        }
        if (null != messages) {
            messages = chatCommonPresenter.distinctMessage(messages, mMsgChatData);
        }
        mActivity.mIMLogRecorder.logMsg(this, "LoadAtTask", " finish executeTask ");
        return messages;
    }

    @Override
    protected void onTaskSuccess(List<Message> messages) {
        if (null != messages) {
            mMsgChatData.addItemModels(0, messages);
        }
        if (atPos < 0) {
            atPos = mMsgChatData.getCount() - 1;
        }
        mActivity.mIMLogRecorder.logMsg(this, "LoadAtTask", " onTaskSuccess mMsgChatData: " + mMsgChatData.getCount()+",atPos:"+atPos);
        final int headerCount = mMsgChatData.getHeaderViewsCount();
        mMsgChatRecycler.post(() -> {
            mMsgChatRecycler.smoothScrollToPosition(atPos + headerCount);
            // 跳转后播放动画
            if (giftContent != null) {
                EventBus.getDefault().post(new DataEvent<>(PaperEvent.PAPER_EVENT_PLAY_GIFT_BY_MSG,  new MessageGiftData(giftContent,mActivity.getTaskTag().toString())));
            }
        });
    }
}
