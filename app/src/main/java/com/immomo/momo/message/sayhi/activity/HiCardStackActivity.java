package com.immomo.momo.message.sayhi.activity;

import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.PersistableBundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.immomo.framework.account.MessageManager;
import com.immomo.framework.base.BaseActivity;
import com.immomo.framework.base.BaseFragment;
import com.immomo.framework.base.BaseReceiver;
import com.immomo.framework.base.IStepConfigDataProvider;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.framework.utils.UIUtils;
import com.immomo.mmstatistics.event.ClickEvent;
import com.immomo.mmstatistics.event.Event;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.android.view.tips.TipManager;
import com.immomo.momo.android.view.tips.tip.ITip;
import com.immomo.momo.businessmodel.statistics.BusinessConfig;
import com.immomo.momo.businessmodel.statistics.SayHiCardConfig;
import com.immomo.momo.im.GiftSayHiAppConfigV1;
import com.immomo.momo.likematch.tools.TipsUtil;
import com.immomo.momo.maintab.sessionlist.SessionListFragment;
import com.immomo.momo.message.activity.HiSessionListActivity;
import com.immomo.momo.message.receiver.BlockUserReceiver;
import com.immomo.momo.message.sayhi.FemaleSelectQuestionDialog;
import com.immomo.momo.message.sayhi.SayHiArgs;
import com.immomo.momo.message.sayhi.SayHiStackCache;
import com.immomo.momo.message.sayhi.contract.IHiCardsStackContract;
import com.immomo.momo.message.sayhi.contract.presenter.HiCardsStackPresenter;
import com.immomo.momo.message.sayhi.itemmodel.SubmitReplySetting;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiListResult;
import com.immomo.momo.message.sayhi.utils.SayHiConst;
import com.immomo.momo.message.sayhi.widget.HintPopupWindow;
import com.immomo.momo.personalprofile.bean.PersonalProfileQuestion;
import com.immomo.momo.platform.utils.PlatformReportHelper;
import com.immomo.momo.protocol.imjson.receiver.MessageKeys;
import com.immomo.momo.service.bean.Message;
import com.immomo.momo.service.sessions.SessionRepository;
import com.immomo.momo.statistics.EVAction;
import com.immomo.momo.statistics.EVPage;
import com.immomo.momo.util.BroadcastHelper;
import com.immomo.momo.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import cn.dreamtobe.kpswitch.util.StatusBarHeightUtil;


/**
 * 8.18后的Session列表中"收到的招呼" - 新招呼卡片堆栈页
 */
public class HiCardStackActivity extends BaseActivity implements
        IHiCardsStackContract.IView, MessageManager.MessageSubscriber, IStepConfigDataProvider<BusinessConfig> {
    public static final String EMPTY_DIRECT_GOTO = "empty_direct_goto";
    public static final String SHOW_EMPTY_VIEW = "show_empty_view";
    public static final String KEY_MOMOID = "KEY_MOMOID";
    public static final String KEY_LIKE_ME_ID = "KEY_LIKE_ME_ID";
    public static final String KEY_FROM_SOURCE = "key_from_source";
    /**
     * 不适用本地缓存
     */
    public static final String KEY_NO_CACHE = "KEY_NO_CACHE";
    public static final int RES_HI_AUTO_REPLY_SETTING = 100;

    private IHiCardsStackContract.IPresenter mPresenter;
    private View menuHistoryEntry;
    private String mLikeMeId;
    private boolean isToolBarEnable = true;
    private HintPopupWindow popWindow;
    private View smartBoxAnchor;
    private View autoReplySettingEntry;
    private String fragmentTag;
    private String reportOptionUserId;
    private TextView tvCardCount;
    //是否是第一次加载loadfragment
    private boolean isFirstLoadFragment = true;
    private BlockUserReceiver blockUserReceiver;
    private FemaleSelectQuestionDialog femaleSelectQuestionDialog;
    private boolean isFromPushInsertTop = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            //activity被回收，已经add到FragmentManager中的fragment不被释放，activity恢复后，
            // 旧的fragment的内存实例始终存在，且view也始终在展示，使用这种方式fix
            savedInstanceState.remove("android:support:fragments");
        }
        super.onCreate(savedInstanceState);
        initWindow();
        setContentView(R.layout.activity_say_hi_stack);
        initViews();
        initData();
        initReceiver();
    }

    @Override
    public void onSaveInstanceState(Bundle outState, PersistableBundle outPersistentState) {
        super.onSaveInstanceState(outState, outPersistentState);
        if (outState != null) {
            //activity被回收，已经add到FragmentManager中的fragment不被释放，activity恢复后，
            // 旧的fragment的内存实例始终存在，且view也始终在展示，使用这种方式fix
            outState.remove("android:support:fragments");
        }
    }

    /**
     * 初始化请求数据
     */
    private void initData() {
        mLikeMeId = getIntent().getStringExtra(KEY_LIKE_ME_ID);
        isFromPushInsertTop = StringUtils.isNotBlank(mLikeMeId);
        mPresenter = new HiCardsStackPresenter(this);
        SayHiConst.recommendSayHiId(mLikeMeId);
        if (getIntent().getBooleanExtra(KEY_NO_CACHE, false)) {
            refreshDataNoCache();  //请求新街口的数据，不要用本地缓存
        } else {
            refreshData();
        }
    }

    private void initViews() {
        tvCardCount = (TextView) findViewById(R.id.stack_card_count);

        // 设置了全屏，额外添加一个状态栏的高度来占位
        View appbar = getToolbarHelper().getAppBarLayout();
        resizeTopMargin(appbar);
        resizeTopMargin(tvCardCount);

        if (com.immomo.momo.util.MomoKit.INSTANCE.isDarkMode(this)) {
            setStatusBarColor(Color.BLACK, false);
            getWindow().setNavigationBarColor(Color.BLACK);
        }
    }

    private void resizeTopMargin(View v) {
        int statusHeight = StatusBarHeightUtil.getStatusBarHeight(thisActivity());
        ViewGroup.LayoutParams lp = v != null ? v.getLayoutParams() : null;
        if (lp instanceof ViewGroup.MarginLayoutParams) {
            ((ViewGroup.MarginLayoutParams) lp).topMargin = statusHeight;
        }
    }

    public void refreshCardCount(int current, int total) {
        if (tvCardCount == null) {
            return;
        }
        tvCardCount.setVisibility((total > 0 && total >= current) ? View.VISIBLE : View.GONE);
        tvCardCount.setText(String.format(Locale.getDefault(), "%d/%d", current, total));
    }

    private void initWindow() {
        Window window = getWindow();
        if (window != null && window.getDecorView() != null) { // 设置全屏使得底部按钮不被键盘顶起
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            window.setStatusBarColor(Color.TRANSPARENT);
            //设置白底黑字
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                        View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            }
        } else if (window != null) {
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        }
    }

    @Override
    protected void onDestroy() {
        if (blockUserReceiver != null) {
            BroadcastHelper.unregisterBroadcast(this, blockUserReceiver);
            blockUserReceiver = null;
        }
        closeDialog();
        TipManager.unbindActivity(thisActivity());
        mPresenter.onDestroy();
        SayHiConst.clearRecommendSayHiId();
        if (GiftSayHiAppConfigV1.isOpenExp() && isFromPushInsertTop) {
            SayHiConst.refreshGiftMsgSessionState();
        }
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        if (dissmissPopWindow()) {
            return;
        }
        super.onBackPressed();
    }

    @Override
    protected void onPause() {
        if (isInitialized()) {
            mPresenter.updateUnreadStatus();
        }
        super.onPause();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Fragment fragment = getCurrentFragment();
        switch (requestCode) {
            case RES_HI_AUTO_REPLY_SETTING: // 设置完自动回复文案
                onAutoReplySettingReturn(resultCode, data, fragment);
                break;
            case PlatformReportHelper.ACT_RES_REPORT: // 举报和拉黑
                if (data != null && PlatformReportHelper.ACTION_SUCCESS ==
                        data.getIntExtra(PlatformReportHelper.WEB_RESULT_STATUS, PlatformReportHelper.ACTION_FAILED)) {
                    onReportSuccess(data);
                }
                break;
            default:
                break;
        }
    }

    private void onReportSuccess(Intent data) {
        if (data == null || mPresenter == null) {
            return;
        }
        int action = data.getIntExtra(PlatformReportHelper.WEB_RESULT_ACTION, -1);
        if (action == PlatformReportHelper.ACTION_BLOCK || action == PlatformReportHelper.ACTION_REPORT_AND_BLOCK) {
            Toaster.showInvalidate("拉黑成功");
        } else {
            Toaster.showInvalidate("举报成功");
        }
        afterReportSuccess(reportOptionUserId);
    }

    public void afterReportSuccess(String reportUserid) {
        mPresenter.blockAndDeleteSession(reportUserid, -1, true, null);
        Fragment fragment = getCurrentFragment();
        if (fragment instanceof StackSayHiFragment) {
            ((StackSayHiFragment) fragment).onBlockSuccess(reportUserid);
        }
    }

    public void reportUser(String momoId) {
        reportOptionUserId = momoId;
        logReport();
        PlatformReportHelper.startReportByMomoid(thisActivity(), PlatformReportHelper.REPORT_BIZ_GREETING_CARD,
                momoId, PlatformReportHelper.REPORT_TYPE_SOURCE_FROM_NONE);
    }

    private void logReport() {
        ClickEvent.create().page(EVPage.Msg.SayhiCard).action(EVAction.Content.Report).submit();
    }

    private void onAutoReplySettingReturn(int resultCode, Intent data, Fragment fragment) {
        if (!KV.getUserBool(SPKeys.User.SayHi.KEY_POP_WINDOW_LEFT_SLIDE_LAST_DATA, false)) {
            TipsUtil.showTip(thisActivity(), autoReplySettingEntry, "设置和修改右滑文案",
                    0, -UIUtils.getPixels(5), ITip.Triangle.TOP);
            KV.saveUserValue(SPKeys.User.SayHi.KEY_POP_WINDOW_LEFT_SLIDE_LAST_DATA, true);
        }
        String reply = data != null ? data.getStringExtra(SubmitReplySetting.KEY_SAVED_REPLY) : "";
        if (resultCode == RESULT_OK && !TextUtils.isEmpty(reply)
                && fragment instanceof StackSayHiFragment && fragment.isAdded()) {
            ((StackSayHiFragment) fragment).refreshReplyMessage(reply);
        }
    }


    @Override
    protected void initToolbar() {
        super.initToolbar();
        toolbarHelper.showShadow(false);
        smartBoxAnchor = findViewById(R.id.view_show_smartbox);
        autoReplySettingEntry = findViewById(R.id.auto_reply_setting_entry);
        autoReplySettingEntry.setVisibility(MomoKit.isManUser() ? View.GONE : View.VISIBLE);
        autoReplySettingEntry.setOnClickListener(v -> {
            if (!isToolBarEnable || hidePanel()) {  // 点击标题栏，相当于焦点转移，故关闭其他窗口
                return;
            }
            if (MomoKit.getCurrentUser().isFemale()) {
                gotoFemaleDialog();
            }

        });
        menuHistoryEntry = findViewById(R.id.history_sayhi_entry);
        menuHistoryEntry.setOnClickListener(v -> {
            if (!isToolBarEnable || hidePanel()) {// 点击标题栏，相当于焦点转移，故关闭其他窗口
                return;
            }
            gotoHistorySayHiSession();
        });
    }

    /**
     * 跳转女性选择问题弹窗
     */
    private void gotoFemaleDialog() {
        String sex = "他";
        Fragment currentFragment = getCurrentFragment();
        if (currentFragment instanceof StackSayHiFragment) {
            SayHiInfo showingItem = ((StackSayHiFragment) currentFragment).getShowingItem();
            if (showingItem != null && showingItem.user != null) {
                sex = showingItem.user.isMale() ? "他" : "她";
            }
        }
        mPresenter.loadFemaleQuestion(false, false, sex);
    }

    public void clearUnreadAnGoHistory() {
        MomoTaskExecutor.executeUserTask(getTaskTag(), new MomoTaskExecutor.Task() {
            @Override
            protected Object executeTask(Object[] objects) throws Exception {
                new SessionRepository().updateAllSayhiInnerIgnore();
                return null;
            }

            @Override
            protected void onTaskSuccess(Object o) {
                SayHiStackCache.INSTANCE.setData(new ArrayList<>());
                gotoHistorySayHiSession();
                finish();
            }
        });
    }

    public void gotoHistorySayHiSession() {
        Intent it = new Intent(getContext(), HiSessionListActivity.class);
        startActivity(it);
    }


    private void initReceiver() {
        // 注册广播接收器 监听广播类型[状态消息/新消息]
        MessageManager.registerMessageReceiver(String.valueOf(hashCode()), this, 700,
                MessageKeys.Action_HiMessage,
                MessageKeys.Action_UpdateMessage,
                SessionListFragment.Action_SessionChanged);

        blockUserReceiver = new BlockUserReceiver(this);
        blockUserReceiver.setReceiveListener(new BaseReceiver.IBroadcastReceiveListener() {
            @Override
            public void onReceive(Intent intent) {
                reportOptionUserId = intent.getStringExtra(BlockUserReceiver.KEY_REMOTEID);
                afterReportSuccess(reportOptionUserId);
            }
        });
        BroadcastHelper.registerBroadcast(this, blockUserReceiver, BlockUserReceiver.ACTION);
    }


    @Override
    public void refreshData() {
        toLoadFragment(false);
        mPresenter.loadCardData(mLikeMeId, false);
        mLikeMeId = null;
    }

    /**
     * 请求新街口的数据，不要用本地缓存
     */
    public void refreshDataNoCache() {
        toLoadFragment(false);
        SayHiStackCache.INSTANCE.forceRefresh();   //不使用本地缓存
        mPresenter.loadCardDataNoCache(mLikeMeId, false);
        mLikeMeId = null;
    }

    public void toLoadFragment(boolean showEmpty) {
        Bundle bundle = new Bundle();
        if (isFirstLoadFragment) {
            SayHiArgs.setEmptyDirectGoto(bundle);
            isFirstLoadFragment = false;
        }
        if (showEmpty) {
            SayHiArgs.showEmptyView(bundle);
        }
        showFragment(LoadSayHiFragment.newInstance(bundle));
    }

    public void toStackFragment() {
        showFragment(StackSayHiFragment.newInstance());
    }

    private void showFragment(BaseFragment pFragment) {
        if (isClosing() || pFragment == null) {
            return;
        }
        if (pFragment instanceof StackSayHiFragment) {
            pFragment.setArguments(getIntent().getExtras());
        }
        fragmentTag = pFragment.getClass().getName();
        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();

        transaction.replace(R.id.layout_content, pFragment, fragmentTag);
        transaction.commitAllowingStateLoss();
    }

    public void loadMore() {
        mPresenter.loadCardData(null, true);
    }


    public void showPopWindow(int type, View.OnClickListener clickListener, List<CharSequence> descList,
                              String clickGoto, String guidephoto, int logType) {
        dissmissPopWindow();
        if (isFinishing() || isDestroyed() || smartBoxAnchor == null) return;
        popWindow = new HintPopupWindow(thisActivity());
        popWindow.setType(type, clickListener, descList, clickGoto, guidephoto, logType);
        popWindow.showAsDropDown(smartBoxAnchor);
    }

    public boolean dissmissPopWindow() {
        if (popWindow != null && popWindow.isShowing()) {
            popWindow.dismiss();
            return true;
        }
        return false;
    }

    /**
     * ******************* IMatchingPeopleView  START ********************
     */
    @Override
    public void closeActivity() {
        finish();
    }

    @Override
    public BaseActivity getContext() {
        return this;
    }

    @Override
    public boolean isClosing() {
        return isDestroyed() || isFinishing();
    }

    public void setToolBarEnable(boolean enable) {
        isToolBarEnable = enable;
    }

    private boolean hidePanel() {
        Fragment fragment = getCurrentFragment();
        if (fragment instanceof StackSayHiFragment && fragment.isAdded()) {
            return ((StackSayHiFragment) fragment).hideCommentLayout();
        }
        return false;
    }

    private Fragment getCurrentFragment() {
        return getSupportFragmentManager().findFragmentByTag(fragmentTag);
    }

    public SayHiListResult getSayHiList() {
        return mPresenter.getSayHiList();
    }

    public void toggleHistoryEntry(boolean visible) {
        menuHistoryEntry.setVisibility(visible ? View.VISIBLE : View.INVISIBLE);
        if (!MomoKit.isManUser()) {
            autoReplySettingEntry.setVisibility(visible ? View.VISIBLE : View.INVISIBLE);
        }
    }

    @Override
    public void showEmptyViewOrGoHiSessionList() {
        Fragment currentFragment = getCurrentFragment();
        if (currentFragment instanceof LoadSayHiFragment) {
            ((LoadSayHiFragment) currentFragment).showEmptyViewOrGoHiSessionList();
        } else if (currentFragment instanceof StackSayHiFragment) {
            ((StackSayHiFragment) currentFragment).checkDataEmpty();
        } else {
            clearUnreadAnGoHistory();
        }
    }

    @Override
    public void loadDataSuc(SayHiListResult result) {
        toggleHistoryEntry(true); // 成功拉取招呼，可以进入历史消息
        Fragment currentFragment = getCurrentFragment();
        if (currentFragment instanceof StackSayHiFragment) {
            ((StackSayHiFragment) currentFragment).appendCards(result, result.getData());
        } else {
            toStackFragment();
        }
    }


    @Override
    public void showErrorView() {
        Fragment currentFragment = getCurrentFragment();
        if (currentFragment instanceof LoadSayHiFragment) {
            ((LoadSayHiFragment) currentFragment).showEmptyView();
        } else if (currentFragment == null) {
            toLoadFragment(true);
        }
    }

    @Override
    public void showFemaleQuestionDialog(List<PersonalProfileQuestion> questions, boolean isFromOutSide, boolean isRedEnvelope, String sex) {
        if (femaleSelectQuestionDialog == null) {
            femaleSelectQuestionDialog = new FemaleSelectQuestionDialog(this);
        }
        femaleSelectQuestionDialog.setData(questions, sex);
        femaleSelectQuestionDialog.setListener(new FemaleSelectQuestionDialog.onClickListener() {
            @Override
            public void onSureClick(String questionId, String question, boolean selected) {
                KV.saveUserValue(SPKeys.FemaleGreet.KEY_FEMALE_SELECT_STATUS, selected);
                if (getCurrentFragment() instanceof StackSayHiFragment && getCurrentFragment().isAdded()) {
                    ((StackSayHiFragment) getCurrentFragment()).setStackViewPreventStatus();
                    ((StackSayHiFragment) getCurrentFragment()).refreshReplyMessage(question);
                }
                mPresenter.setReplyQuestion(questionId, question, isFromOutSide, isRedEnvelope, selected);
            }

            @Override
            public void requestNew() {
                loadFemaleQuestion(false, false, "");
            }

        });
        femaleSelectQuestionDialog.setOnDismissListener(dialog -> {
            if (getCurrentFragment() instanceof StackSayHiFragment && getCurrentFragment().isAdded()) {
                if (isFromOutSide) {
                    ((StackSayHiFragment) getCurrentFragment()).onRightBtnClick(true);
                } else {
                    ((StackSayHiFragment) getCurrentFragment()).setStackViewPreventStatus();
                }
            }
        });
        femaleSelectQuestionDialog.show();
    }


    public void loadFemaleQuestion(boolean isFromOutSide, boolean isRedEnvelope, String sex) {
        mPresenter.loadFemaleQuestion(isFromOutSide, isRedEnvelope, sex);
    }

    /**
     * 回复设置成功
     *
     * @param reply
     * @param isFromOutSide true 来自右滑时弹出的设置问题弹窗
     */
    @Override
    public void onSettingSavedSuccess(String reply, boolean isFromOutSide, boolean isRedEnvelope) {
        if (!TextUtils.isEmpty(reply) && getCurrentFragment() instanceof StackSayHiFragment && getCurrentFragment().isAdded()) {
            ((StackSayHiFragment) getCurrentFragment()).refreshReplyMessage(reply);

            if (!KV.getUserBool(SPKeys.User.SayHi.KEY_POP_WINDOW_LEFT_SLIDE_LAST_DATA, false) && KV.getUserBool(SPKeys.FemaleGreet.KEY_FEMALE_SELECT_STATUS, false)) {
                TipsUtil.showTip(thisActivity(), autoReplySettingEntry, "设置和修改右滑文案",
                        0, -UIUtils.getPixels(5), ITip.Triangle.TOP);
                KV.saveUserValue(SPKeys.User.SayHi.KEY_POP_WINDOW_LEFT_SLIDE_LAST_DATA, true);
            }
        }

        //右滑时toast改为已通过
        if (isFromOutSide) {
            Toaster.show(isRedEnvelope ? UIUtils.getString(R.string.sayhi_stack_pass_red_envelope) : "已通过");
        } else {
            Toaster.show("设置成功");
        }
    }

    @Override
    public void onSettingError() {
        Toaster.show("设置问题失败");
    }

    @Override
    public SayHiInfo getCurrentCardInfo() {
        Fragment currentFragment = getCurrentFragment();
        if (currentFragment instanceof StackSayHiFragment) {
            return ((StackSayHiFragment) currentFragment).getCurrentCardUser();
        }
        return null;
    }

    @Override
    public boolean onMessageReceive(Bundle bundle, String action) {
        switch (action) {
            case MessageKeys.Action_UpdateMessage:
                Fragment fragment = getCurrentFragment();
                if (bundle.getInt(MessageKeys.Key_ChatType) == Message.CHATTYPE_USER
                        && fragment instanceof StackSayHiFragment) {
                    return ((StackSayHiFragment) fragment).updateSingleMessage(
                            bundle.getString(MessageKeys.Key_RemoteId),
                            bundle.getString(MessageKeys.Key_MessageId)
                    );
                }
                break;
            default:
                break;
        }
        return false;
    }

    public int getLastRemain() {
        return mPresenter != null && mPresenter.getSayHiList() != null ? mPresenter.getSayHiList().getRemain() : 0;
    }

    public boolean hasMore() {
        return mPresenter != null ? mPresenter.hasMore() : false;
    }

    @Nullable
    @Override
    public Event.Page getPVPage() {
        return EVPage.Msg.SayhiCard;
    }

    @Override
    public BusinessConfig getStepConfigData() {
        return SayHiCardConfig.INSTANCE;
    }
    //</editor-fold>
}
