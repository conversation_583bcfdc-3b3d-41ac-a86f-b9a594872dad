package com.immomo.momo.message.sayhi.itemmodel.bean

import com.immomo.momo.message.sayhi.utils.NewSayHiConst
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.SayhiSession

data class SayHiStackCardInfo(val sayHiInfo: SayhiSession, var sayHiNetInfo: SayHiInfo? = null) {
    var msgGift: Message? = null

    /**
     * 是否是新招呼在消息列表中
     */
    var isNewHiInSessionList = false

    /**
     * 是否是新招呼在消息列
     */
    var isNewHiInStackCard = false

    /**
     * 小卡的状态
     */
    var cardShowType: Int = NewSayHiConst.CARD_TYPE_TYPE_NORMAL

    /**
     * 请求结果卡片的时间
     */
    var requestTime = 0L

    override fun equals(other: Any?): Boolean {
        return (other as? SayHiStackCardInfo?)?.let {
            sayHiInfo.momoid == it.sayHiInfo.momoid
        } ?: false
    }

}
