package com.immomo.momo.message.sayhi.activity;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;

import com.immomo.framework.base.BaseActivity;
import com.immomo.framework.task.BaseDialogTask;
import com.immomo.mmstatistics.event.ClickEvent;
import com.immomo.mmstatistics.event.ExposureEvent;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.R;
import com.immomo.momo.android.view.MomoSwitchButton;
import com.immomo.momo.eventbus.DataEvent;
import com.immomo.momo.eventbus.EventKeys;
import com.immomo.momo.message.helper.FrequentPreferenceHelper;
import com.immomo.momo.message.sayhi.NewSayHiStackCache;
import com.immomo.momo.message.sayhi.SayHiStackCache;
import com.immomo.momo.protocol.http.SettingApi;
import com.immomo.momo.statistics.EVAction;
import com.immomo.momo.statistics.EVPage;
import com.immomo.momo.util.StringUtils;

import de.greenrobot.event.EventBus;

/**
 * Created by zhangye on 2020/8/5.
 */
public class HarassChangeActivity extends BaseActivity {
    public static final String KEY_REMOTE_ID = "key_remote_id";
    private MomoSwitchButton viewBlank;
    private View btnConfirm;
    private String remoteId;
    private View viewClose;

    public static void startActivity(Activity context, String remoteId) {
        Intent intent = new Intent(context, HarassChangeActivity.class);
        intent.putExtra(KEY_REMOTE_ID, remoteId);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_blank_harass_card);

        initView();
        initEvent();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        MomoTaskExecutor.cancleAllTasksByTag(hashCode());
    }

    @Override
    protected boolean isSupportSwipeBack() {
        return false;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        return keyCode == KeyEvent.KEYCODE_BACK;
    }

    private void initView() {
        viewBlank = (MomoSwitchButton) findViewById(R.id.view_blank);
        btnConfirm = findViewById(R.id.btn_confirm);
        viewClose = findViewById(R.id.view_close);

        viewBlank.setChecked(FrequentPreferenceHelper.i().isHarassGreetingOpen());
        ExposureEvent.create(ExposureEvent.Type.Normal).page(EVPage.Msg.SayhiCard).action(EVAction.Window.SaoraoSwitch).submit();
        remoteId = getIntent().getStringExtra(KEY_REMOTE_ID);
    }

    private void initEvent() {
        btnConfirm.setOnClickListener(v -> {
            ClickEvent.create()
                    .action(EVAction.Window.SaoraoConfirm)
                    .page(EVPage.Msg.SayhiCard)
                    .putExtra("status", viewBlank.isChecked() ? 1 : 0)
                    .submit();
            if (FrequentPreferenceHelper.i().isHarassGreetingOpen() == viewBlank.isChecked()) {
                finish();
                return;
            }
            SayHiStackCache.INSTANCE.forceRefresh();
            NewSayHiStackCache.INSTANCE.forceRefresh();
            MomoTaskExecutor.executeUserTask(hashCode(), new BlockHarassGreetingTask(viewBlank.isChecked()));

        });
        viewBlank.setOnClickListener(v -> viewBlank.setChecked(!viewBlank.isChecked()));
        viewClose.setOnClickListener(v -> finish());
    }

    private class BlockHarassGreetingTask extends BaseDialogTask<Object, Object, String> {
        private boolean isBlock;

        public BlockHarassGreetingTask(boolean isBlock) {
            this.isBlock = isBlock;
        }

        @Override
        protected String executeTask(Object... objects) throws Exception {
            return SettingApi.getInstance().blockHarassGreeting(isBlock);
        }

        @Override
        protected void onTaskSuccess(String result) {
            super.onTaskSuccess(result);
            if (StringUtils.notEmpty(result)) {
                Toaster.show(result);
            }
            FrequentPreferenceHelper.i().saveHarassGreetingSetting(isBlock ? 1 : 0);

            if (isBlock) {
                EventBus.getDefault().post(new DataEvent<>(EventKeys.Block.SHOW_BLOCK, remoteId));
            }
            EventBus.getDefault().post(new DataEvent<>(EventKeys.SayHiCard.CARD_RELOAD, null));
            finish();
        }

        @Override
        protected void onTaskError(Exception e) {
            super.onTaskError(e);
            finish();
        }
    }
}
