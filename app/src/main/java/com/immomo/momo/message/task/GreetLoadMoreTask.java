package com.immomo.momo.message.task;

import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.message.sayhi.activity.GreetDialog;
import com.immomo.momo.mvp.message.view.BaseMessageActivity;
import com.immomo.momo.service.bean.Message;

import java.util.ArrayList;
import java.util.List;


/**
 * 招呼页面加载更多的异步任务
 * <AUTHOR>
 * date 2020/8/15
 */
public class GreetLoadMoreTask extends MomoTaskExecutor.Task<Object, Object, List<Message>> {

    private GreetDialog mActivity;


    public GreetLoadMoreTask(GreetDialog mActivity) {
        this.mActivity = mActivity;
    }

    @Override
    protected void onPreTask() {
    }

    @Override
    protected List<Message> executeTask(Object[] params) throws Exception {

        long beginTime = System.nanoTime();
        List<Message> messages = new ArrayList<Message>();
        // 从数据库获取
        if (mActivity.hasMoreMessage) {
            messages = mActivity.loadMoreMessages(BaseMessageActivity.PAGE_SIZE + 1, false, false);
            mActivity.mIMLogRecorder.logLoadMoreMessages(messages);
        }
        //单位变成毫秒
        long executeTime = (System.nanoTime() - beginTime) / 1000000;
        if (executeTime > 0 && executeTime < 200) {
            Thread.sleep(200 - executeTime);
        }

        return messages;
    }

    @Override
    protected void onTaskSuccess(List<Message> messages) {
        if (!mActivity.hasMoreMessage && !mActivity.hasMoreCache) {
            mActivity.msgChatRecycler.removeOverScroll();
        } else {
            mActivity.msgChatRecycler.restoreOverScroll();
        }
        mActivity.msgChatRecycler.setRefreshing(false);
        if (mActivity.mMgsMessageHelper != null) {
            mActivity.mMgsMessageHelper.onPullToRefreshSuccess(mActivity.msgChatRecycler);
        }
        if (messages.size() > 0) {
            mActivity.msgChatData.moreMessageComplete(messages);
        }
        mActivity.msgChatRecycler.tryEndInflateInChain("");
    }

    @Override
    protected void onTaskError(Exception e) {
        super.onTaskError(e);
    }
}
