package com.immomo.momo.message.sayhi.activity

import android.content.Intent
import com.immomo.framework.base.BaseActivity
import com.immomo.framework.cement.SimpleCementAdapter
import com.immomo.momo.mvp.common.presenter.ITipsPresenter
import com.immomo.momo.personalprofile.bean.PersonalProfileQuestion
import com.immomo.momo.service.bean.SayhiSession

interface ISayHiListView {
    fun getActivity(): BaseActivity?

    fun isForeground(): Boolean

    fun updateLiveSession(lastSession: SayhiSession?)

    fun updateGiftSession(lastSession: SayhiSession?)

    fun updateTitle()

    fun setLoadMoreVis(vis: Boolean)

    fun loadCompleted()

    fun loadFailed()

    fun addTips(msg: ITipsPresenter.TipsMessage?)

    fun removeTips(msg: ITipsPresenter.TipsMessage?)

    fun showFemaleQuestionDialog(
        questions: List<PersonalProfileQuestion>,
        isFromOutSide: Boolean,
        isRedEnvelope: Boolean,
        sex: String?
    )

    /**
     * 女性设置问题成功
     *
     * @param reply
     * @param isFromOutSide
     */
    fun onSettingSavedSuccess(reply: String?, isFromOutSide: Boolean, isRedEnvelope: Boolean)

    fun setAdapter(adapter: SimpleCementAdapter)
    fun getIntent(): Intent?
    fun scrollToTop()


}