package com.immomo.momo.message.paper.common

import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.mmutil.task.ThreadUtils
import com.immomo.momo.service.bean.Wallpaper

/**
 * <AUTHOR>
 * @data 2020-12-21.
 */

class BackgroundPresenter(var fragment: BackgroundPaperFragment?) {

    fun loadBackground(chatType: Int, chatId: String?, resId: String?) {
        val wallpaper = Wallpaper("", resId)
        MomoTaskExecutor.executeTask(ThreadUtils.TYPE_RIGHT_NOW, hashCode(), BackgroundTask(chatType, chatId, wallpaper))
    }

    fun onDestroy() {
        fragment = null
    }

}