package com.immomo.momo.message.sayhi.activity.reflect

import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.os.Message
import android.view.View
import android.widget.ImageView
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.cosmos.mdlog.MDLog
import com.immomo.framework.account.MessageManager
import com.immomo.framework.base.BaseActivity
import com.immomo.framework.base.IStepConfigDataProvider
import com.immomo.framework.cement.CementModel
import com.immomo.framework.cement.eventhook.OnClickEventHook
import com.immomo.framework.cement.eventhook.OnLongClickEventHook
import com.immomo.framework.rxjava.interactor.CommonSubscriber
import com.immomo.framework.utils.UIUtils
import com.immomo.framework.view.TopTipView
import com.immomo.framework.view.TopTipView.TopTipEventListener
import com.immomo.framework.view.recyclerview.LoadMoreRecyclerView
import com.immomo.framework.view.recyclerview.itemdecoration.LinearPaddingItemDecoration
import com.immomo.lcapt.evlog.EVLog.create
import com.immomo.mmstatistics.event.ClickEvent
import com.immomo.mmstatistics.event.Event
import com.immomo.mmutil.UIHandler
import com.immomo.mmutil.task.MMThreadExecutors
import com.immomo.mmutil.task.ThreadUtils
import com.immomo.mmutil.toast.Toaster
import com.immomo.momo.MomoKit
import com.immomo.momo.R
import com.immomo.momo.UrlConstant
import com.immomo.momo.android.broadcast.FriendListReceiver
import com.immomo.momo.android.view.dialog.MAlertDialog
import com.immomo.momo.android.view.dialog.MAlertListDialog
import com.immomo.momo.businessmodel.statistics.BusinessConfig
import com.immomo.momo.businessmodel.statistics.IPageDurationHelper
import com.immomo.momo.businessmodel.statistics.SayHiListConfig
import com.immomo.momo.businessmodel.usermodel.IUserModel
import com.immomo.momo.eventbus.DataEvent
import com.immomo.momo.eventbus.EventKeys
import com.immomo.momo.flashchat.weight.IndexCementAdapter
import com.immomo.momo.globalevent.GlobalEventManager
import com.immomo.momo.innergoto.helper.NavigateHelper
import com.immomo.momo.maintab.sessionlist.ISessionListView2
import com.immomo.momo.message.ChatHelper
import com.immomo.momo.message.activity.ChatActivity
import com.immomo.momo.message.activity.HarassGreetingSessionActivity
import com.immomo.momo.message.bean.ChatBusinessType
import com.immomo.momo.message.contract.NewSayHiSessionListContract.INewSayHiSessionListPresenter
import com.immomo.momo.message.contract.NewSayHiSessionListContract.INewSayHiSessionListView
import com.immomo.momo.message.helper.FrequentPreferenceHelper
import com.immomo.momo.message.sayhi.activity.HiCardStackActivity
import com.immomo.momo.message.sayhi.itemmodel.bean.LikeSayHi
import com.immomo.momo.message.sayhi.itemmodel.newhi.NewSayHiUserItemModel
import com.immomo.momo.message.sayhi.presenter.NewSayHiSessionListPresenter
import com.immomo.momo.message.sayhi.utils.NewSayHiConst
import com.immomo.momo.message.task.ChatLogRunnable
import com.immomo.momo.messages.service.SingleMsgService
import com.immomo.momo.mvp.common.model.ModelManager.getInstance
import com.immomo.momo.mvp.common.presenter.ITipsPresenter
import com.immomo.momo.mvp.common.presenter.TipsPresenter
import com.immomo.momo.mvp.message.contract.IMessageLog
import com.immomo.momo.mvp.message.view.BaseMessageActivity
import com.immomo.momo.platform.utils.PlatformReportHelper
import com.immomo.momo.protocol.imjson.receiver.MessageKeys
import com.immomo.momo.service.bean.SayhiSession
import com.immomo.momo.service.bean.User
import com.immomo.momo.service.user.UserService
import com.immomo.momo.setting.BasicUserInfoUtil
import com.immomo.momo.setting.activity.HarassGreetingSettingActivity
import com.immomo.momo.setting.activity.ShenghaoAntiDisturbActivity
import com.immomo.momo.setting.bean.SettingUrlConfig.gotoNotificationNoticeSetting
import com.immomo.momo.statistics.EVAction
import com.immomo.momo.statistics.EVPage
import com.immomo.momo.statistics.logrecord.viewhelper.RecyclerViewExposureLogHelper
import com.immomo.momo.util.MomoKit.isDarkMode
import com.immomo.momo.util.StringUtils
import com.immomo.momo.util.dispose
import de.greenrobot.event.EventBus
import io.reactivex.Flowable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.util.Date
import java.util.UUID

/**
 * 收到的招呼列表页
 * 展示[SayhiSession.FROM_TYPE_NORMAL] 类型的session
 * 和 [SayhiSession.FROM_TYPE_HARASS_GREETING] 类型中的带有部分骚扰招呼的session
 *
 *
 * 同时还展示[SayhiSession.FROM_TYPE_LIVE] 类型session
 * 和 [SayhiSession.FROM_TYPE_GIFT] 类型session的入口 [HarassGreetingSettingActivity]
 */
class NewSayHiSessionListActivity : BaseActivity(), MessageManager.MessageSubscriber,
    IPageDurationHelper, INewSayHiSessionListView, IStepConfigDataProvider<BusinessConfig?>,
    GlobalEventManager.Subscriber {

    companion object {
        private const val REFRESH_COUNT_BOTTON = 7168
    }

    private val sessionMsgTag = hashCode() + 1
    private val actionArr = arrayOf("悄悄查看", "删除", "举报")

    private val topTipView by lazy { findViewById(R.id.tip_view) as TopTipView }
    private val tipsPresenter by lazy { TipsPresenter(topTipView) }
    private val listView by lazy {
        (findViewById(R.id.listview) as LoadMoreRecyclerView).apply {
            setOnLoadMoreListener {
                presenter.loadSessionListMore()
            }
        }
    }
    private val userModel = getInstance().getModel(IUserModel::class.java)
    private val userService by lazy { UserService.getInstance() }
    private val mLayoutManager by lazy { LinearLayoutManager(this) }
    private val adapter = IndexCementAdapter()
    private val icToolbarAllHarass by lazy { findViewById(R.id.ic_toolbar_all_harass) as ImageView }
    private val icToolbarAllUnread by lazy { findViewById(R.id.ic_toolbar_all_unread) as ImageView }

    /**
     * 当前被举报需要处理的用户
     */
    private var reportOptionUserId: String? = null

    // 列表处理
    private val presenter: INewSayHiSessionListPresenter by lazy {
        NewSayHiSessionListPresenter(this, adapter, lifecycleScope, intent)
    }
    private val handler = SessionHandler(this)
    private var mDisposable: Disposable? = null
    private val mViewHarassTip by lazy { findViewById(R.id.view_harass_tip) }

    override val stepConfigData: BusinessConfig
        get() = SayHiListConfig

    class SessionHandler(hiSessionListActivity: NewSayHiSessionListActivity?) :
        UIHandler<NewSayHiSessionListActivity?>(hiSessionListActivity) {
        override fun handleMessage(msg: Message) {
            val activity = ref ?: return
            when (msg.what) {
                REFRESH_COUNT_BOTTON -> {
                    activity.showMessageCount()
                    activity.refreshHeaderBtn()
                }

                else -> {}
            }
        }
    }

    /**
     * 注册广播接收器 监听广播类型[状态消息/新消息]
     */
    fun initReceiver() {
        MessageManager.registerMessageReceiver(
            sessionMsgTag, this, 700,
            MessageKeys.Action_HiMessage,
            MessageKeys.Action_UpdateMessage,
            ISessionListView2.Action_SessionChanged
        )
        EventBus.getDefault().register(this)
    }

    override fun isLightTheme(): Boolean {
        return !isDarkMode(this)
    }

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_sessionlist_new_sayhi)
        NewSayHiConst.refreshViewSize()
        // 移除通知栏提示
        MomoKit.getApp().removeSayhiNotify()
        initInternal()
        initViews()
        initEvents()
        initAdapter()
        loadData(false)
        initReceiver()
        initDebug()
    }

    /**
     * 测试功能
     */
    private fun initDebug() {
        NewSayHiConst.initDebug(toolbarHelper.toolbar)
    }

    private fun initAdapter() {
        listView.layoutManager = mLayoutManager
        listView.itemAnimator = null
        listView.addItemDecoration(LinearPaddingItemDecoration(0, UIUtils.getPixels(15f), 0))
        listView.setVisibleThreshold(2)
        listView.addOnScrollListener(RecyclerViewExposureLogHelper.getOnScrollListener())
        adapter.addEventHook(object :
            OnClickEventHook<NewSayHiUserItemModel.ViewHolder>(NewSayHiUserItemModel.ViewHolder::class.java) {
            override fun onClick(
                view: View,
                viewHolder: NewSayHiUserItemModel.ViewHolder,
                position: Int,
                rawModel: CementModel<*>
            ) {
                if (rawModel is NewSayHiUserItemModel) {
                    onItemClickSession(rawModel.model.sayHiSession)
                }
            }

            override fun onBindMany(viewHolder: NewSayHiUserItemModel.ViewHolder): List<View> {
                return listOf(viewHolder.itemView)
            }
        })
        adapter.addEventHook(object :
            OnLongClickEventHook<NewSayHiUserItemModel.ViewHolder>(NewSayHiUserItemModel.ViewHolder::class.java) {
            override fun onLongClick(
                view: View,
                viewHolder: NewSayHiUserItemModel.ViewHolder,
                position: Int,
                rawModel: CementModel<*>
            ): Boolean {
                if (rawModel is NewSayHiUserItemModel) {
                    showActionDialog(rawModel.model.sayHiSession)
                }
                return true
            }

            override fun onBindMany(viewHolder: NewSayHiUserItemModel.ViewHolder): List<View> {
                return listOf(viewHolder.itemView)
            }
        })
        listView.adapter = adapter
    }

    private fun initInternal() {
        presenter.init()
    }

    /**
     * 初始化界面元素
     */
    fun initViews() {
        title = "收到的招呼"
        icToolbarAllUnread.setOnClickListener {
            showClearMsgDialog()
        }
        icToolbarAllHarass.setOnClickListener {
            showHarass()
        }
    }

    private fun showHarass() {
        NavigateHelper.startWebview(
            thisActivity(), UrlConstant.URL_HARASS_GREETING_INTRO
                    + "&annoy_status=" + (if (FrequentPreferenceHelper.i().isHarassGreetingOpen) 1 else 0)
        )
    }

    fun initEvents() {
        GlobalEventManager.getInstance().register(this, GlobalEventManager.EVN_NATIVE)
        topTipView.setTopTipEventListener(object : TopTipEventListener {
            override fun onTopTipClick(view: View, tipsMessage: ITipsPresenter.TipsMessage?) {
                if (tipsMessage != null && thisActivity() != null) {
                    if (ITipsPresenter.TipsMessage.ID_SAY_HI_TOO_MUCH_MSG == tipsMessage.getId()) {
                        gotoNotificationNoticeSetting(thisActivity())
                        tipsPresenter.removeTips(ITipsPresenter.TipsMessage.ID_SAY_HI_TOO_MUCH_MSG)
                    } else if (ITipsPresenter.TipsMessage.ID_HARASS_GREETING == tipsMessage.getId()) {
                        HarassGreetingSettingActivity.startActivity(
                            thisActivity(),
                            HarassGreetingSettingActivity.HARASS_GREETING_OPEN,
                            HarassGreetingSettingActivity.KEY_FROM_SESSION,
                            HarassGreetingSessionActivity.REQ_HARASS_GREETING
                        )
                        topTipView.visibility = View.GONE
                    } else if (ITipsPresenter.TipsMessage.ID_SHENGHAO_AVERT_DISTURB == tipsMessage.getId()) {
                        startActivity(
                            Intent(
                                thisActivity(),
                                ShenghaoAntiDisturbActivity::class.java
                            )
                        )
                        topTipView.visibility = View.GONE
                    }
                }
            }

            override fun onTopTipShown(view: View, tipsMessage: ITipsPresenter.TipsMessage?) {
                //do nothing
            }

            override fun onTopTipHide(view: View, tipsMessage: ITipsPresenter.TipsMessage?) {
                //do nothing
            }
        })
        mViewHarassTip.setOnClickListener { v: View? ->
            val it = Intent(thisActivity(), HarassGreetingSessionActivity::class.java)
            it.putExtra(
                HarassGreetingSessionActivity.KEY_FROM_PAGE,
                HarassGreetingSessionActivity.PAGE_FROM_HI_LIST
            )
            startActivity(it)
            create(IMessageLog::class.java).logClickHarass()
        }
    }

    fun loadData(isReload: Boolean) {
        // 请求卡片数据
        if (!isReload) {
            reloadCardData(true)
        }
        // 请求列表数据
        presenter.loadSessionListData(isReload)
    }

    private fun onItemClickSession(hiSession: SayhiSession) {
        val intent = Intent(this@NewSayHiSessionListActivity, ChatActivity::class.java)
        intent.putExtra(ChatActivity.REMOTE_USER_ID, hiSession.momoid)
        intent.putExtra(
            BaseMessageActivity.EXTRA_KEY_FROM,
            BaseMessageActivity.EXTRA_VALUE_FROM_HIACTIVITY
        )
        intent.putExtra(
            ChatHelper.KEY_BUSINESS_TYPE,
            if (hiSession.sessionBusinessType == SayhiSession.BUSINESS_TYPE_KLIAO_MATCH) ChatBusinessType.CHAT_BUSINESS_KLIAO_MATCH else ""
        )
        startActivity(intent)
        updateCountTitleFromDB()
        presenter.consumeSayhi(hiSession.momoid, LikeSayHi.Requst.TYPE_READ)
        ClickEvent.create().page(EVPage.Msg.SayhiList).action(EVAction.List.ImPage)
            .putExtra("momoid", hiSession.momoid).submit()
    }

    private fun showActionDialog(sayhiSession: SayhiSession) {
        val mdialog = MAlertListDialog(this, actionArr)
        mdialog.setTitle(R.string.dialog_title_avatar_long_press)
        mdialog.setOnItemSelectedListener { index: Int ->
            val select = actionArr[index]
            if ("删除" == select) {
                presenter.deleteSession(sayhiSession.momoid, true)
                presenter.consumeSayhi(sayhiSession.momoid, LikeSayHi.Requst.TYPE_DEL)
            } else if ("举报" == select) {
                reportOptionUserId = sayhiSession.momoid
                PlatformReportHelper.startReportByMomoid(
                    thisActivity(), PlatformReportHelper.REPORT_BIZ_GREETING_CARD,
                    sayhiSession.momoid, PlatformReportHelper.REPORT_TYPE_SOURCE_FROM_NONE
                )
            } else if ("悄悄查看" == select) {
                if (userModel.currentUser?.isMomoVip == true) {
                    val intent = Intent(thisActivity(), ChatActivity::class.java)
                    intent.putExtra(ChatActivity.REMOTE_USER_ID, sayhiSession.momoid)
                    intent.putExtra(ChatHelper.KEY_VIEWMODEL, ChatHelper.VIEWMODEL_PEEK)
                    intent.putExtra(
                        ChatHelper.KEY_BUSINESS_TYPE,
                        if (sayhiSession.sessionBusinessType == SayhiSession.BUSINESS_TYPE_KLIAO_MATCH) ChatBusinessType.CHAT_BUSINESS_KLIAO_MATCH else ""
                    )
                    startActivity(intent)
                } else {
                    showBuyVipDialog()
                }
            }
        }
        mdialog.setSupportDark(true)
        showDialog(mdialog)
    }

    private fun showClearMsgDialog() {
        val dialog = MAlertDialog.makeConfirm(
            thisActivity(),
            "本操作将清除当前未读消息提示，确认进行此操作吗?",
            { dialog1: DialogInterface?, which: Int ->
                presenter.clearAllUnread {
                    updateCountTitleFromDB()
                    refreshHeaderBtn()
                    presenter.clearLastSessionUnread(SayhiSession.FROM_TYPE_LIVE)
                    presenter.clearLastSessionUnread(SayhiSession.FROM_TYPE_GIFT)
                    presenter.consumeIgnore()
                }
            },
            { dialog12: DialogInterface?, which: Int -> closeDialog() })
        dialog.setSupportDark(true)
        showDialog(dialog)
    }

    override fun onResume() {
        super.onResume()
        checkStartIM()
        MomoKit.getApp().removeSayhiNotify()
        presenter.onResume()
        if (FrequentPreferenceHelper.i().isHarassGreetingOpen) {
            mViewHarassTip.visibility = View.VISIBLE
            create(IMessageLog::class.java).logShowHarassHi()
        } else {
            mViewHarassTip.visibility = View.GONE
        }
    }

    private fun checkStartIM() {
        kotlin.runCatching {
            MomoKit.getApp().asyncWatchIMService()
        }
    }

    override fun onPause() {
        if (isInitialized) {
            tipsPresenter.removeTips(ITipsPresenter.TipsMessage.ID_HARASS_GREETING)
            presenter.updateUnreadStatus()
        }
        presenter.onPause()
        super.onPause()
    }

    private fun updateCountTitleFromDB() {
        presenter.updateCountTitleFromDB()
    }

    override fun getActivity(): BaseActivity {
        return this
    }

    override fun isActivityForeground() = isForeground

    override fun updateTitle() {
        handler.sendEmptyMessage(REFRESH_COUNT_BOTTON)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        outState.putString("reportOptionUserId", reportOptionUserId)
        super.onSaveInstanceState(outState)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        kotlin.runCatching {
            reportOptionUserId = savedInstanceState.getString("reportOptionUserId")
        }
        super.onRestoreInstanceState(savedInstanceState)
    }

    override fun onDestroy() {
        NewSayHiConst.release()
        GlobalEventManager.getInstance().unregister(this, GlobalEventManager.EVN_NATIVE)
        MessageManager.unregisterMessageReceiver(sessionMsgTag)
        EventBus.getDefault().unregister(this)
        presenter.destroy()
        NewSayHiConst.refreshGiftMsgSessionState()
        super.onDestroy()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (PlatformReportHelper.ACT_RES_REPORT == requestCode && data != null) {
            val status = data.getIntExtra(
                PlatformReportHelper.WEB_RESULT_STATUS,
                PlatformReportHelper.ACTION_FAILED
            )
            if (status == PlatformReportHelper.ACTION_SUCCESS) {
                val action = data.getIntExtra(PlatformReportHelper.WEB_RESULT_ACTION, -1)
                reportUserChat(action, reportOptionUserId)
            }
        } else if (NewSayHiConst.STACK_CARD_REQUEST_CODE == requestCode && data != null) {
            val filterIds = data.getStringArrayListExtra(NewSayHiConst.RESULT_PARAM_FILTER_ID)
            if (!filterIds.isNullOrEmpty()) {
                presenter.pushNotInLocalMomoids(filterIds)
            }
            val hasDealGift = data.getBooleanExtra(NewSayHiConst.RESULT_PARAM_HAS_DEAL_GIFT, false)
            val hasDealLive = data.getBooleanExtra(NewSayHiConst.RESULT_PARAM_HAS_DEAL_LIVE, false)
            if (hasDealGift) {
                presenter.updateFolderSession(SayhiSession.FROM_TYPE_GIFT, true)
            }
            if (hasDealLive) {
                presenter.updateFolderSession(SayhiSession.FROM_TYPE_LIVE, true)
            }
        } else if (NewSayHiConst.FOLD_INNER_SESSIONS_REQUEST_CODE == requestCode) {
            presenter.refreshAllHiCardData()
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    private fun blockUser() {
        dispose(mDisposable)
        mDisposable =
            Flowable.fromCallable<User> {
                val user = userService.getSimpleUser(reportOptionUserId)
                if (user != null) {
                    user.relation = User.RELATION_NONE //修改关系为陌生人
                    user.blocktime = Date()
                    userService.addBlackUser(user) //插入黑名单列表
                    userService.saveUserSimple(user)
                    updateFansList(reportOptionUserId)
                    return@fromCallable user
                }
                User()
            }.subscribeOn(Schedulers.from(MMThreadExecutors.User))
                .observeOn(MMThreadExecutors.Main.scheduler)
                .subscribeWith(object : CommonSubscriber<User?>() {
                    override fun onNext(user: User?) {
                        super.onNext(user)
                        user ?: return
                        if (StringUtils.notEmpty(user.momoid)) {
                            EventBus.getDefault()
                                .post(DataEvent(EventKeys.Block.ADD_BLOCK, user.momoid))
                        }
                    }
                })
    }

    override fun onMessageReceive(bundle: Bundle, action: String): Boolean {
        kotlin.runCatching {
            return presenter.onMessageReceive(bundle, action)
        }
        return false
    }

    override fun removeTips(msg: ITipsPresenter.TipsMessage?) {
        msg ?: return
        tipsPresenter.removeTips(msg)
    }

    override fun addTips(msg: ITipsPresenter.TipsMessage?) {
        msg ?: return
        tipsPresenter.addTips(msg)
    }

    override fun closeActivity() {
        finish()
    }

    override fun onLoadListComplete(state: Int) {
        listView.setLoadMoreComplete()
    }

    override fun reloadCardData(reload: Boolean) {
        presenter.loadCardData(reload)
    }

    override fun jumpToBigCard(pageFromSource: String?, clearJumpAni: Boolean) {
        NewSayHiCardActivity.start(this, null, pageFromSource, null, clearJumpAni)
    }

    /**
     * 更新粉丝列表数目 *
     */
    fun updateFansList(removeId: String?) {
        val fan = userService.findFans(removeId)
        if (fan != null) {
            userService.removeFans(fan.momoid)
            if (BasicUserInfoUtil.getFollowerCount() > 0) {
                BasicUserInfoUtil.setFollowerCount(BasicUserInfoUtil.getFollowerCount() - 1)
            }
        }
        val intent = Intent(FriendListReceiver.ACTION_DELETE_FANS)
        intent.putExtra(FriendListReceiver.KEY_MOMOID, removeId)
        intent.putExtra(FriendListReceiver.KEY_NEW_FANS, BasicUserInfoUtil.getNewFansCount())
        intent.putExtra(FriendListReceiver.KEY_TOTAL_FANS, BasicUserInfoUtil.getFollowerCount())
        intent.putExtra(FriendListReceiver.KEY_TOTAL_FRIENDS, BasicUserInfoUtil.getFollowingCount())
        FriendListReceiver.send(intent) //更新粉丝列表界面
    }

    fun showBuyVipDialog() {
        val dialog = MAlertDialog.makeConfirm(
            thisActivity(), "会员查看消息可不标记已读",
            "取消", "开通会员",
            { dialog1: DialogInterface?, which: Int -> closeDialog() },
            { dialog12: DialogInterface?, which: Int ->
                NavigateHelper.startWebview(
                    thisActivity(),
                    UrlConstant.URL_MEMBER_CENTER
                )
            })

        dialog.setTitle("提示")
        dialog.setSupportDark(true)
        showDialog(dialog)
    }

    private fun showMessageCount() {
        title = if (presenter.getAllUnreadUserCount() > 0) {
            val allUnreadUserCount = presenter.getAllUnreadUserCount()
            val cntShow = if (allUnreadUserCount > 99) "99+" else allUnreadUserCount.toString()
            "收到的招呼 ($cntShow)"
        } else {
            "收到的招呼"
        }
    }

    private fun refreshHeaderBtn() {
        icToolbarAllUnread.visibility = if (presenter.hasItem()) View.VISIBLE else View.GONE
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        intent ?: return
        val pushCardId = intent.getStringExtra(HiCardStackActivity.KEY_LIKE_ME_ID) ?: ""
        if (StringUtils.isNotBlank(pushCardId)) { // push进入重新打开卡片页面
            finish()
            startActivity(intent)
        }
    }

    /**
     * 收到的事件
     */
    fun onEvent(event: DataEvent<String>) {
        if (event.equals(EventKeys.Block.ADD_BLOCK)) {
            val momoid = event.data
            if (StringUtils.notEmpty(momoid)) {
                presenter.dealSessionBroadcast(momoid)
            }
        }

        if (event.equals(EventKeys.SayHiCard.CARD_RELOAD)) {
            loadData(true)
        }
    }

    //<editor-fold desc="IPageDurationHelper">
    private var pageLogId: String? = null

    override fun getPageLogID(enter: Boolean): String? {
        if (enter) {
            pageLogId = UUID.randomUUID().toString()
        }
        return pageLogId
    }

    override fun getPageSource(): String? {
        return javaClass.simpleName
    }

    override fun getPVPage(): Event.Page {
        return EVPage.Msg.SayhiList
    }

    override fun onGlobalEventReceived(event: GlobalEventManager.Event?) {
        event?.also {
            when (event.name) {
                NewSayHiConst.EVENT_SAYHI_LIST_LOADMORE_DATA -> { // 加载更多
                    MDLog.i(NewSayHiConst.TAG, "EVENT_SAYHI_LIST_LOADMORE_DATA")
                    presenter.loadMoreSlideCard(reload = false, isFromCardStack = true)
                }

                NewSayHiConst.EVENT_SAYHI_STACK_CARD_REPORT -> {
                    (event.msg["momoid"] as? String?)?.also {
                        presenter.deleteSession(it, true)
                        blockUser()
                    }
                }

                NewSayHiConst.EVENT_SAYHI_STACK_CARD_WEB_REPORT -> { // 举报网页
                    (event.msg["action"] as? Int?)?.also {
                        val userid = event.msg["userid"] as String?
                        reportUserChat(it, userid)
                    }
                }

                NewSayHiConst.EVENT_SAYHI_STACK_CARD_STACK_SHOW -> {    // 当卡片曝光时
                    (event.msg["momoid"] as? String?)?.also {
                        presenter.updateSessionReaded(it)
                    }
                }

                NewSayHiConst.EVENT_ON_INNER_SESSION_DELETE -> { // 当内部session删除
                    (event.msg["momoid"] as? String?)?.also {
                        presenter.checkDeleteTinyCardModel(it)
                    }
                }

                NewSayHiConst.EVENT_ON_CARD_BACK_CLOSE_CONFIG -> { // 关闭页面
                    finish()
                    overridePendingTransition(0, 0)
                }
            }
        }
    }

    /**
     * 处理举报事件
     */
    private fun reportUserChat(action: Int, curUserid: String?) {
        if (curUserid.isNullOrBlank()) return
        presenter.consumeSayhi(curUserid, LikeSayHi.Requst.TYPE_REPORT)
        if (action == PlatformReportHelper.ACTION_BLOCK || action == PlatformReportHelper.ACTION_REPORT_AND_BLOCK) {
            //举报拉黑成功，更新相关数据
            presenter.deleteSession(curUserid, true)
            Toaster.showInvalidate("拉黑成功")
            blockUser()
        } else {
            Toaster.showInvalidate("举报成功")
            SingleMsgService.getInstance().updateSayhiInnerIgnore(curUserid)
            presenter.dealSessionBroadcast(curUserid)
        }
    }

}