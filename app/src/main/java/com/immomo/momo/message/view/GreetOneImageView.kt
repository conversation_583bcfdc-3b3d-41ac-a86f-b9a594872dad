package com.immomo.momo.message.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.ImageView
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.kotlin.ImageType
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.R

/**
 * <AUTHOR>
 * @data 2020-08-25.
 */

class GreetOneImageView : FrameLayout {
    lateinit var firstImageView: ImageView

    constructor(context: Context) : super(context)

    constructor(context: Context, attributeSet: AttributeSet) : super(context, attributeSet)

    constructor(context: Context, attributeSet: AttributeSet, defStyleAttr: Int) : super(
            context,
            attributeSet,
            defStyleAttr
    )

    init {
        createView()
    }

    private fun createView() {
        LayoutInflater.from(context).inflate(R.layout.layout_greet_one_image, this, true)
        firstImageView = findViewById(R.id.iv_one_image_first)
    }

    fun setImageUrl(urls: MutableList<String>?) {
        urls?.let {
            if (it.isNotEmpty()) {
                ImageLoader.load(it[0])
                        .imageType(ImageType.URL)
                        .cornerRadius(UIUtils.getPixels(4f))
                        .into(firstImageView)
            }
        }

    }


}
