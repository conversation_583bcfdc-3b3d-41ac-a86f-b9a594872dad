package com.immomo.momo.message.sayhi.stack;

import android.animation.AnimatorSet;
import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.Animation;

import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.mmstatistics.event.ExposureEvent;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.momo.MomoKit;
import com.immomo.momo.android.view.tips.tip.ITip;
import com.immomo.momo.android.view.tips.tip.OnTipHideListener;
import com.immomo.momo.likematch.slidestack.BaseSlideStackView;
import com.immomo.momo.likematch.slidestack.GuideAnimStatusListener;
import com.immomo.momo.likematch.widget.StackAnimHelper;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo;
import com.immomo.momo.message.sayhi.utils.FemaleRedEnvelopeHelper;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.statistics.EVAction;
import com.immomo.momo.statistics.EVPage;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Created by jinxiao on 16/2/3.
 */
public class SayHiStackView extends BaseSlideStackView<SayHiInfo, SayHiSlideCard> implements TextWatcher {
    private AtomicBoolean mNeedShowGuide = new AtomicBoolean(false); // 是否需要显示引导
    private GuideAnimStatusListener mGuideAnimStatusListener;
    private TipListener tipListener;
    private AnimatorSet mGuideAnim;

    public SayHiStackView(Context context) {
        this(context, null);
    }

    public SayHiStackView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SayHiStackView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected GestureDetector.OnGestureListener initGestureListener() {
        return new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
                // dx是滑动速率，不是两次触摸事件的距离
                float deltaY = Math.abs(e2.getRawY() - e1.getRawY());
                float deltaX = Math.abs(e2.getRawX() - e1.getRawX());
                double tan30 = Math.tan(45.0f / 180.0f * Math.PI);
                // 允许横向拖动，禁止纵向拖动，即横向滑动角度小于30degree时可以触发卡片拖拽，touch不往下传递
                return Math.abs(deltaX) > 45 && // 最小滑动距离7
                        Math.abs(deltaX) > Math.abs(deltaY) * 1.0f / tan30;
            }
        };
    }

    @Override
    protected void destroy(boolean completelyDestroy) {
        super.destroy(completelyDestroy);
        if (mGuideAnim != null) {
            mGuideAnim.removeAllListeners();
            mGuideAnim.cancel();
            mGuideAnim = null;
        }
    }

    /**
     * 左右滑动画后如果有tips引导则继续播放
     */
    public void showTipsProfileClick() {
        SayHiSlideCard card = getSlideItem(0);
        if (card != null) {
            card.showTipsAfterEnterGuide(tip -> playGuideBySequence());
        }
    }

    /**
     * 隐私，禁言的提示引导
     */
    public void showTipsPrivacy() {
        SayHiSlideCard card = getSlideItem(0);
        if (card != null) {
            card.showPrivacyTipsAfterEnterGuide(new OnTipHideListener() {
                @Override
                public void onHide(ITip tip) {
                    //曝光埋点
                    exposureTipsLog();
                    playGuideBySequence();
                }
            });
        }
    }

    /**
     * 禁言引导曝光埋点
     */
    private void exposureTipsLog() {
        //保存引导记录
        KV.saveUserValue(SPKeys.User.SayHi.KEY_SAY_HI_FORBIDDEN_TIPS, true);
        User user = MomoKit.getCurrentUser();
        ExposureEvent.create(ExposureEvent.Type.Normal)
                .page(EVPage.Msg.SayhiCard)
                .action(EVAction.Top.BanGuide)
                .putExtra("momo_id", user.momoid)
                .submit();
    }

    /**
     * 整个进入动画播放完
     */
    public void onEnterGuideEnd() {
        setSlideContentVisible(true);
    }

    public void onAnimEnd(){
        // 恢复各种状态
        if (mGuideAnimStatusListener != null) {
            mGuideAnimStatusListener.onEnd();
        }
        setBtnLock(false);
        mNeedShowGuide.set(false);
    }

    @Override
    protected boolean isFirstCardAnimCustom() {
        return false;
    }

    public void setSlideContentVisible(boolean visible) {
        if (visible && adapter.getSize() > 0) {
            // 等引导动画全完成后，再播放卡片上的动画
            playAnimOnCards(adapter.getItem(0), 0);

            SayHiSlideCard firstCard = getSlideItem(0);
            if (firstCard != null) {
                firstCard.logHeadVisiblePart();
            }
        }
    }

    @Override
    public void playAnimOnCards(SayHiInfo info, int indeXInViewList) {
        SayHiSlideCard viewPager = getSlideItem(indeXInViewList);
        if (viewPager == null) {
            return;
        }
        viewPager.playAnimOnCard();
    }


    @Override
    protected void recallCardReset(SayHiSlideCard recallCard, int indexInDataList, SayHiInfo info) {

    }


    @Override
    protected void onLayout(boolean changed, final int left, int top, final int right,
                            int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        // 引导动画相关
        if (mNeedShowGuide.get()) {
            SayHiSlideCard topView = viewList.get(0);
            if (topView == null) {
                return;
            }

            topView.bringToFront();
        }
    }

    public List<SayHiSlideCard> getCardViewLists() {
        return viewList;
    }

    public void playGuideBySequence() {
        SayHiInfo info = adapter.getShowingItem();
        if (FemaleRedEnvelopeHelper.INSTANCE.shouldShowRedEnvelopeTip(info)) {
            if (tipListener != null) {
                tipListener.onShowRedEnvelopeTip();
            }
        } else if (KV.getUserBool(SPKeys.User.SayHi.KEY_IS_CLICK_PROFILE_TIPS_SHOWN, true)) {
            KV.saveUserValue(SPKeys.User.SayHi.KEY_IS_CLICK_PROFILE_TIPS_SHOWN, false);
            showTipsProfileClick();
        } else if (checkShowForbiddenGuide()) {
            showTipsPrivacy();
        } else {
            // 动画结束，可以点击
            onEnterGuideEnd();
        }
    }

    /**
     * 检查是否需要展示禁言引导（仅展示了图标的女性用户有引导）
     */
    private boolean checkShowForbiddenGuide() {
        User user = MomoKit.getCurrentUser();
        if (user == null) {
            return false;
        }
        //是否需要播放回复引导
        boolean isSayHiShowGuide = KV.getUserBool(SPKeys.User.SayHi.KEY_SAY_HI_STACK_CARDS_INPUT_TIPS, true);
        //是否未播放过禁言引导
        boolean showedForbiddenGuide = KV.getUserBool(SPKeys.User.SayHi.KEY_SAY_HI_FORBIDDEN_TIPS, false);
        //未展示过引导
        //招呼页引导较为复杂，先展示卡片动画，结束后，展示点击资料tip，之后展示上滑引导与回复引导，
        //因禁言引导优先级最低，但与点击资料tip处于同层逻辑，因此判断回复引导是否已展示后，再展示禁言引导，已达到下次进入招呼页后，展示禁言引导的目标
        return user.isFemale() && !showedForbiddenGuide && !isSayHiShowGuide;
    }

    public void setGuideAnimStatusListener(GuideAnimStatusListener listener) {
        this.mGuideAnimStatusListener = listener;
    }

    public void setTipListener(TipListener tipListener) {
        this.tipListener = tipListener;
    }

    public void startCardCameFromLeftAnim(Animation.AnimationListener listener) {
        long startOffset = 200L;
        int totalDataCnt = Math.min(getChildCount(), Math.min(3, adapter.getSize()));  // only first 3 card to anim
        List<View> cardToAnim = new ArrayList<>();
        for (int i = totalDataCnt - 1; i >= 0; i--) {
            View child = getSlideItem(i);
            cardToAnim.add(child);
            child.setVisibility(INVISIBLE);  // hide before anim
        }
        MomoMainThreadExecutor.postDelayed(getTaskTag(), new Runnable() {
            @Override
            public void run() {
                StackAnimHelper.rotateViewsFromLeftOutScreen(getTaskTag(), listener, startOffset, 700, cardToAnim.toArray(new View[0]));
            }
        }, 300L);
    }

    //<editor-fold desc="点击了卡片上的假输入框，fragment的输入框改变，卡片上的假输入框也要随之改变内容">
    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        SayHiSlideCard card = getSlideItem(0);
        if (card != null) {
            card.setfakeInputTextOnCard(s);
        }
    }
    //</editor-fold >

    public interface TipListener{
        void onShowRedEnvelopeTip();
    }
}
