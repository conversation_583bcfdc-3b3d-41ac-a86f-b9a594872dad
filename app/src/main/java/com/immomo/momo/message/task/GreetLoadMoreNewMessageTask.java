package com.immomo.momo.message.task;

import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.message.sayhi.activity.GreetDialog;
import com.immomo.momo.messages.service.SingleMsgServiceV2;
import com.immomo.momo.service.bean.Message;

import java.util.ArrayList;
import java.util.List;

import static com.immomo.momo.mvp.message.view.BaseMessageActivity.PAGE_SIZE;


/**
 * 单人聊天加载更多新消息的异步任务
 * <AUTHOR>
 * date 2020/8/15
 */
public class GreetLoadMoreNewMessageTask extends MomoTaskExecutor.Task<Object, Object, List<Message>> {

    private GreetDialog mActivity;


    public GreetLoadMoreNewMessageTask(GreetDialog mActivity) {
        this.mActivity = mActivity;
    }

    @Override
    protected List<Message> executeTask(Object... params) throws Exception {
        if (mActivity.msgChatData.isEmpty()){
            return new ArrayList<Message>();
        }
        Message message = mActivity.msgChatData.getMessageList().get(mActivity.msgChatData.getCount() - 1);
        List<Message> messages = SingleMsgServiceV2.getService().findMessageBy(
                mActivity.getMomoID(), PAGE_SIZE + 1, message, true, false);
        if (messages.size() == PAGE_SIZE + 1) {
            mActivity.setHasMoreNewerMessage(true);
            messages.remove(messages.size() - 1);
        } else {
            mActivity.setHasMoreNewerMessage(false);
        }

        mActivity.preHandleMessages(messages, false);
        return messages;
    }

    @Override
    protected void onTaskFinish() {
        mActivity.msgChatRecycler.setLoadMoreComplete();
    }

    @Override
    protected void onTaskSuccess(List<Message> messages) {
        if (messages.size() > 0) {
            mActivity.msgChatData.addItemModels(messages);
        }
    }

    @Override
    protected void onTaskError(Exception e) {
        //do nothing
    }
}
