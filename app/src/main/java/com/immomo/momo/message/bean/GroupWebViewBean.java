package com.immomo.momo.message.bean;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * 群组webView展示
 * <AUTHOR>
 */
public class GroupWebViewBean {
    //1为显示入口 0为不显示入口
    @Expose
    private int status;
    //入口链接 status=0时 无此字段
    @SerializedName("goto")
    @Expose
    private String jumpUrl;
    @Expose
    private String gid;//群组id（

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }



    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }


    public boolean isShow() {
        return  status == 1;
    }
}
