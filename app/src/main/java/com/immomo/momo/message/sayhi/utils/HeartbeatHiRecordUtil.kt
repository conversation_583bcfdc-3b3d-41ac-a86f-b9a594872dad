package com.immomo.momo.message.sayhi.utils

import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.mmstatistics.event.ClickEvent
import com.immomo.mmstatistics.event.ExposureEvent
import com.immomo.momo.MomoKit
import com.immomo.momo.statistics.EVAction
import com.immomo.momo.statistics.EVPage

object HeartbeatHiRecordUtil {

    @JvmStatic
    fun btnClick(toMomoid: String?, gender: String?, switch: Boolean) {
        ClickEvent.create()
            .page(EVPage.Other.SayHiRecommendNp)
            .action(EVAction.Nav.SayhiHeart)
            .requireId("19103")
            .putExtra("to_momoid", toMomoid.safe())
            .putExtra("to_gender", gender.safe())
            .putExtra("switch", if (switch) "on" else "off")
            .submit()
    }

    @JvmStatic
    fun btnExposure(toMomoid: String, gender: String) {
        ExposureEvent.create(ExposureEvent.Type.Normal)
            .page(EVPage.Other.SayHiRecommendNp)
            .action(EVAction.Nav.SayhiHeart)
            .requireId("19104")
            .putExtra("to_momoid", toMomoid.safe())
            .putExtra("to_gender", gender.safe())
            .putExtra("is_vip", getVipState())
            .submit()
    }

    @JvmStatic
    fun msgExposure(fromMomoid: String) {
        ExposureEvent.create(ExposureEvent.Type.Normal)
            .page(EVPage.Other.SayHiRecommendNp)
            .action(EVAction.Float.HeartbeatSayhi)
            .requireId("19105")
            .putExtra("from_momoid", fromMomoid)
            .submit()
    }

    private fun getVipState(): String {
        var vipState = "user"
        MomoKit.getCurrentUser()?.also {
            if (it.isSvip()) {
                vipState = "svip"
            } else if (it.isVip) {
                vipState = "vip"
            }
        }
        return vipState
    }

}