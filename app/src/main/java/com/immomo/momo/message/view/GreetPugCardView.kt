package com.immomo.momo.message.view

import android.content.Context
import android.util.AttributeSet
import android.widget.FrameLayout
import com.immomo.momo.message.bean.GreetCardData

/**
 * <AUTHOR>
 * @data 2020-08-25.
 */

abstract class GreetPugCardView : FrameLayout {

    var data: GreetCardData? = null
    var remoteId: String? = null
    var cardClickListener: CardClick? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attributeSet: AttributeSet) : super(context, attributeSet)

    constructor(context: Context, attributeSet: AttributeSet, defStyleAttr: Int) : super(
            context,
            attributeSet,
            defStyleAttr
    )


    init {
        createView()
    }

    abstract fun createView()


    fun setCardData(greetCardData: GreetCardData?) {
        data = greetCardData
        refresh()
    }

    abstract fun refresh()


    interface CardClick {
        fun onCardClick(clickData: Any?, filterSource:Boolean = false)
    }

}
