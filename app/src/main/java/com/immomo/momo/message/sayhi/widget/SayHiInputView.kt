package com.immomo.momo.message.sayhi.widget

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.animation.Animation
import android.widget.FrameLayout
import android.widget.ImageView
import com.immomo.momo.util.MomoKit

/**
 * <AUTHOR>
 * @data 2020-08-25.
 */

abstract class SayHiInputView : FrameLayout {

    protected var dislikeButton: View? = null
    protected var llStartChat: View? = null
    protected var ivFlash: ImageView? = null
    protected var rlFemaleInput: View? = null
    var sayHiInputListener: SayHiInputListener? = null


    constructor(context: Context) : super(context)

    constructor(context: Context, attributeSet: AttributeSet) : super(context, attributeSet)

    constructor(context: Context, attributeSet: AttributeSet, defStyleAttr: Int) : super(
        context,
        attributeSet,
        defStyleAttr
    )


    init {
        createView()

    }

    abstract fun createView()

    fun startFlash(tvFlashAnim: Animation?) {
        if (MomoKit.isDarkMode(context)) {
            ivFlash?.visibility = GONE
            return
        }
        ivFlash?.visibility = View.VISIBLE
        tvFlashAnim?.let {
            ivFlash?.startAnimation(it)
        }
    }

    fun clearFlashAnim() {
        ivFlash?.clearAnimation()
    }

    fun clearFlashColorFilter() {
        ivFlash?.clearColorFilter()
    }

    fun clearStartChatAnim() {
        llStartChat?.clearAnimation()
    }

    fun setStartChatVisible() {
        //显示输入框
        llStartChat?.visibility = View.VISIBLE
    }

    fun startChatAnim(animation: Animation) {
        llStartChat?.startAnimation(animation)
    }

    fun scaleDisLikeButton(f: Float) {
        dislikeButton?.scaleX = f
        dislikeButton?.scaleY = f
    }

    fun getDisLikeBtn(): View? = dislikeButton

    open fun setLikeBtnRes(resId: Int) {
        //do nothing
    }

    open fun setInputHint(text: String) {
        //do nothing
    }

    interface SayHiInputListener {
        fun showCommentEdit()

        fun onLikeBtnClick()
    }

}
