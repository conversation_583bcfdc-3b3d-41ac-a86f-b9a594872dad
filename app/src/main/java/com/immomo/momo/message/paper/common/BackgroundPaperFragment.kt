package com.immomo.momo.message.paper.common

import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.graphics.drawable.LayerDrawable
import android.view.View
import android.widget.ImageView
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.R
import com.immomo.momo.eventbus.DataEvent
import com.immomo.momo.message.paper.BasePaperFragment
import com.immomo.momo.message.paper.event.BackgroundInfo
import com.immomo.momo.message.paper.event.PaperEvent
import com.immomo.momo.mvp.message.view.BaseMessageActivity
import com.immomo.momo.util.MomoKit


/**
 * 聊天背景
 * <AUTHOR>
 * @data 2020-12-18.
 */

open class BackgroundPaperFragment : BasePaperFragment() {

    private var mImageView: ImageView? = null
    private var presenter: BackgroundPresenter? = null
    var chatBgDrawable: Drawable? = null


    companion object {
        fun newInstance(): BackgroundPaperFragment {
            return BackgroundPaperFragment()
        }
    }

    override fun getContainerId(): Int = R.id.chat_background_paper_container

    override fun getPageLayout(): Int = R.layout.paper_chat_background


    override fun initPageViews(contentView: View?) {
        presenter = BackgroundPresenter(this)
        mImageView = findViewById(R.id.iv_paper_background)
    }

    override fun onPageLoad() {

    }


    private fun onBackgroundLoaded(bitmap: Bitmap?) {
        if (bitmap == null) {
            showNormalBg()
        } else {
            val bg = BitmapDrawable(UIUtils.getResources(), bitmap)
            val maskDrawable = ColorDrawable(Color.parseColor("#33000000"))
            val finalDrawable = LayerDrawable(arrayOf(bg, maskDrawable))
            finalDrawable.setLayerInset(0, 0, 0, 0, 0)
            finalDrawable.setLayerInset(1, 0, 0, 0, 0)

            chatBgDrawable = finalDrawable
            activity?.window?.setBackgroundDrawable(chatBgDrawable)
            getBaseActivity()?.switchToDarkTheme()
        }
        getBaseActivity()?.setRootViewBackgroundTransparent()
    }

    open fun showNormalBg() {
        chatBgDrawable = getNormalBg()
        activity?.window?.setBackgroundDrawable(chatBgDrawable)
        getBaseActivity()?.switchToLightTheme()
    }

    open fun getNormalBg(): Drawable = if (MomoKit.isDarkMode(context))
        ColorDrawable(Color.parseColor("#000000")) else ColorDrawable(Color.parseColor("#F7F7F7"))

    override fun onEvent(event: DataEvent<Any>) {
        super.onEvent(event)
        when (event.action) {
            PaperEvent.PAPER_EVENT_BACKGROUND_TASK_SUCCESS -> {
                onBackgroundLoaded(event.data as? Bitmap)
            }

            PaperEvent.PAPER_EVENT_LOAD_BACKGROUND -> {
                (event.data as? BackgroundInfo)?.let {
                    presenter?.loadBackground(it.chatType, it.chatId, it.resId)
                }
            }
        }
    }


    override fun onDestroy() {
        super.onDestroy()
        presenter?.onDestroy()
    }

    open fun getBaseActivity(): BaseMessageActivity? {
        (activity as? BaseMessageActivity)?.let {
            return it
        }
        return null
    }

}