package com.immomo.momo.message.task;

import android.content.Context;
import android.graphics.Bitmap;

import com.cosmos.mdlog.MDLog;
import com.immomo.LogTag;
import com.immomo.mmutil.StringUtils;
import com.immomo.mmutil.task.ThreadUtils;
import com.immomo.momo.Configs;
import com.immomo.momo.MomoKit;
import com.immomo.momo.android.synctask.Callback;
import com.immomo.momo.android.synctask.MomoThread;
import com.immomo.momo.util.MediaFileUtil;
import com.immomo.momo.util.NetBitmap;
import com.immomo.momo.util.image.ImageDownloadHttpClient;
import com.immomo.momo.util.image.ImageProessCallback;

import java.io.File;

/**
 * Created by zhao.yaosheng on 2019/1/28.
 */
public class LoadWallpaperThread extends MomoThread<Bitmap> {

    private String imageId;
    private String url;
    private ImageProessCallback processcallback;

    public LoadWallpaperThread(String imageId, String url, Callback<Bitmap> callback, ImageProessCallback processcallback) {
        super(callback);
        if (StringUtils.isEmpty(imageId)) {
            throw new RuntimeException(new NullPointerException(
                    "imageId == null || \"\".equals(imageId)"));
        }
        this.imageId = imageId;
        this.url = url;
        this.processcallback = processcallback;
    }

    @Override
    public void run() {
        Bitmap bitmap = null;
        try {
            NetBitmap netBitmap = ImageDownloadHttpClient.downloadBitmap(url, processcallback);
            bitmap = netBitmap.bitmap;
            String path = MomoKit.getContext().getDir("wallpaper", Context.MODE_PRIVATE).getAbsolutePath();
            File file = new File(path, "/" + imageId + Configs.PICTURE_SUFFIX);
            if (bitmap != null && file != null) {
                Bitmap.CompressFormat compressFormat = null;
                if ("image/png".equals(netBitmap.contentType)) {
                    compressFormat = Bitmap.CompressFormat.PNG;
                } else {
                    compressFormat = Bitmap.CompressFormat.JPEG;
                }
                MediaFileUtil.saveImageFile(bitmap, file, compressFormat);
            }
        } catch (Exception e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        } finally {
            callBack(bitmap);
        }
    }

    @Override
    public void execute() {
        ThreadUtils.execute(ThreadUtils.TYPE_RIGHT_NOW, this);
    }
}
