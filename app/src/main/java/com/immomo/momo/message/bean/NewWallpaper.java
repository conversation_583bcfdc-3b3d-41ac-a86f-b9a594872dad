package com.immomo.momo.message.bean;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/1/17.
 */
public class NewWallpaper {

    public NewWallpaper(int type) {
        this.type = type;
    }

    @Expose
    private String id;

    @Expose
    @SerializedName("s")
    private String smallPic;

    @Expose
    @SerializedName("l")
    private String largePic;

    // 0:第一张默认图； 1：api加载过来的图；
    private int type;

    // 当前图片的下载进度
    private int progress;

    // 当前图片是否正在下载
    private boolean isLoading;

    private long filelenght;

    // 当前是否是选中状态
    private boolean isSelected;

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }

    public long getFilelenght() {
        return filelenght;
    }

    public void setFilelenght(long filelenght) {
        this.filelenght = filelenght;
    }

    public boolean isLoading() {
        return isLoading;
    }

    public void setLoading(boolean loading) {
        isLoading = loading;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSmallPic() {
        return smallPic;
    }

    public void setSmallPic(String smallPic) {
        this.smallPic = smallPic;
    }

    public String getLargePic() {
        return largePic;
    }

    public void setLargePic(String largePic) {
        this.largePic = largePic;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getProgress() {
        return progress;
    }

    public void setProgress(int progress) {
        this.progress = progress;
    }
}
