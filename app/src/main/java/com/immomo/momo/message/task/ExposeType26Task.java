package com.immomo.momo.message.task;

import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.messages.service.SingleMsgService;
import com.immomo.momo.service.bean.Message;

public class ExposeType26Task extends MomoTaskExecutor.Task<Object, Object, Object> {

    Message exposeMsg;

    public ExposeType26Task(Message exposeMsg) {
        this.exposeMsg = exposeMsg;
    }

    @Override
    protected Object executeTask(Object... objects) throws Exception {
        SingleMsgService.getInstance().update(exposeMsg.remoteId, exposeMsg);
        return null;
    }

    @Override
    protected void onTaskError(Exception e) {
        // 不做提示
    }
}