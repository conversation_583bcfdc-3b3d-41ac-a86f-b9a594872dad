package com.immomo.momo.message.sayhi.utils

import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.mmstatistics.event.ClickEvent
import com.immomo.mmstatistics.event.Event
import com.immomo.mmstatistics.event.ExposureEvent
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo
import com.immomo.momo.message.sayhi.stack.NewSayHiCardStackView
import com.immomo.momo.mulog.MULogConstants
import com.immomo.momo.mulog.MURealtimeLog
import com.immomo.momo.mulog.pair.MUPairItem
import com.immomo.momo.service.bean.SayhiSession
import com.immomo.momo.service.bean.User
import com.immomo.momo.statistics.EVAction
import com.immomo.momo.statistics.EVPage
import com.immomo.momo.util.CollectionUtils
import com.immomo.momo.util.GsonUtils
import com.immomo.momo.util.StringUtils

/**
 * 打招呼打点曝光
 */
object NewSayHiReportUtil {

    /**
     * 招呼空页面文字闲聊匹配按钮点击
     */
    fun emptyListMatchClick() {
        ClickEvent.create()
            .requireId("18864")
            .page(Event.Page("msg.sayhi_list"))
            .action(Event.Action("content.textmatch_button"))
            .submit()
    }

    /**
     * 招呼空页面文字匹配入口展示
     */
    fun emptyListMatchShow() {
        ExposureEvent.create(ExposureEvent.Type.Normal)
            .requireId("18863")
            .page(Event.Page("msg.sayhi_list"))
            .action(Event.Action("content.textmatch_button"))
            .submit()
    }

    /**
     * 未读招呼入口点击
     */
    fun unreadTinyCardClick(sayHiInfo: SayhiSession?, count: Int) {
        sayHiInfo ?: return
        ClickEvent.create()
            .requireId("18862")
            .page(Event.Page("msg.sayhi_list"))
            .action(Event.Action("content.unread_greet"))
            .putExtra("count", count)
            .putExtra("card_id", sayHiInfo.momoid)
            .submit()
    }

    /**
     * 招呼列表中未读cell的展示
     */
    fun unreadTinyCardShow(count: Int) {
        ExposureEvent.create(ExposureEvent.Type.Normal)
            .requireId("18861")
            .page(Event.Page("msg.sayhi_list"))
            .action(Event.Action("list.unread_greet"))
            .putExtra("count", count)
            .submit()
    }

    /**
     * 招呼卡片收起按钮点击/或者点击空白处
     */
    fun clickBigCardClose(clickFrom: String) {
        ClickEvent.create()
            .requireId("18860")
            .page(Event.Page("msg.sayhi_card"))
            .action(Event.Action("content.close"))
            .putExtra("click_poc", clickFrom)
            .submit()
    }

    /**
     * 招呼卡片多头像点击
     */
    fun clickBigCardAvatar(photoId: String, imageType: String) {
        ClickEvent.create()
            .requireId("18859")
            .page(Event.Page("msg.sayhi_card"))
            .action(Event.Action("content.multiphoto"))
            .putExtra("photo_id", photoId)
            .putExtra("photo_text", imageType)
            .submit()
    }

    /**
     * 上报session曝光
     */
    fun reportBigCardShown(
        sayHiSession: SayhiSession,
        sayHiInfo: SayHiInfo,
        indexInStack: Int,
        pageFromSource: String?
    ) {
        var source = "msg"
        if ("nearby" == pageFromSource) {
            source = "nearby"
        }
        val user = sayHiInfo.user
        val photos = sayHiInfo.greetCardImgs.safe()
        val exposureEvent = ExposureEvent.create(ExposureEvent.Type.Normal)
            .requireId("18858")
            .page(Event.Page("msg.sayhi_card"))
            .action(Event.Action("content.feature"))
            .putExtra("photo_number", photos.size)
            .putExtra("photoid", if (photos.isNotEmpty()) photos[0].origin else "")
            .putExtra("is_spray", if (sayHiInfo.getUser().isMarkedSpray) "1" else "0")
            .putExtra(
                "set_photoid",
                if (sayHiInfo.originalFirstPhoto != null) sayHiInfo.originalFirstPhoto else ""
            )
            .putExtra("introduce", GsonUtils.g().toJson(sayHiInfo.getMarks()))
        exposureEvent.putExtra("ban_type", if (sayHiInfo.forbiddenBtnShow == 1) "1" else "0")
        if (user != null) {
            exposureEvent.putExtra("momo_id", user.getMomoid())
            exposureEvent.putExtra("distance", sayHiInfo.getUser().distanceString)
            exposureEvent.putExtra(
                "msg_num",
                if (sayHiInfo.getMessages() == null) "0" else sayHiInfo.getMessages().size.toString() + ""
            )
            if (user.fortuneLevel > 0) {
                exposureEvent.putExtra("wealth_rank", user.fortuneLevel)
            } else {
                exposureEvent.putExtra("wealth_rank", "")
            }
            exposureEvent.putExtra(
                "vip_rank",
                if (user.isMomoVip()) user.getVipActivityLevel() else 0
            )
            exposureEvent.putExtra(
                "svip_rank",
                if (user.isSvip()) user.getSvipActivityLevel() else 0
            )
        }
        exposureEvent.putExtra("time_length", if (sayHiInfo.isOnline) "online" else "hiding")
        exposureEvent.putExtra("is_gift", if (sayHiInfo.hasGiftMessage()) "1" else "0")
        exposureEvent.putExtra("sayhi_source", sayHiInfo.sourceText)
        exposureEvent.putExtra(
            "fans_sign",
            if (User.RELATION_FANS == sayHiInfo.relation) "1" else "0"
        )
        exposureEvent.putExtra("logmap", sayHiInfo.logMapString)
        exposureEvent.putExtra("pos", indexInStack.toString() + "")
        exposureEvent.putExtra("source", source)
        val marks = sayHiInfo.marks.safe() // 印记
        exposureEvent.putExtra("info", StringUtils.join(marks, "·"))
        if (sayHiInfo.passby != null && StringUtils.isNotEmpty(sayHiInfo.passby.location)) {
            exposureEvent.putExtra("place", sayHiInfo.passby.location)
        } else {
            exposureEvent.putExtra("place", "")
        }

        if (sayHiInfo.passby != null && StringUtils.isNotEmpty(sayHiInfo.passby.timeText)) {
            exposureEvent.putExtra("time", sayHiInfo.passby.timeText)
        } else {
            exposureEvent.putExtra("time", "")
        }
        exposureEvent.putExtra("remote_info", sayHiInfo.greetCardMark.safe())
        exposureEvent.submit()
    }

    /**
     * 直播招呼入口曝光
     */
    fun sessionListLiveItemExp() {
        ExposureEvent.create(ExposureEvent.Type.Normal)
            .requireId("18911")
            .page(Event.Page("msg.sayhi_list"))
            .action(Event.Action("list.live_greet_cell"))
            .submit()
    }

    /**
     * 直播招呼入口点击
     */
    fun sessionListGiftItemClick() {
        ClickEvent.create()
            .requireId("18912")
            .page(Event.Page("msg.sayhi_list"))
            .action(Event.Action("list.live_greet_cell"))
            .submit()
    }

    /**
     * 礼物招呼入口曝光
     */
    fun sessionListGiftItemExp() {
        ExposureEvent.create(ExposureEvent.Type.Normal)
            .requireId("18913")
            .page(Event.Page("msg.sayhi_list"))
            .action(Event.Action("list.gift_greet_cell"))
            .submit()
    }

    /**
     * 礼物招呼入口点击
     */
    fun sessionListLiveItemClick() {
        ClickEvent.create()
            .requireId("18914")
            .page(Event.Page("msg.sayhi_list"))
            .action(Event.Action("list.gift_greet_cell"))
            .submit()
    }

    /**
     * 当对话按钮点击
     */
    fun onHiClickEvent(
        sayHiInfo: SayHiInfo?, slideStackView: NewSayHiCardStackView, pageFromSource: String?
    ) {
        val info = sayHiInfo ?: return
        var source = "msg"
        if ("nearby" == pageFromSource) {
            source = "nearby"
        }
        val user = info.getUser()
        val detail = info.moreDetail
        val socialCapital = detail?.socialCapital

        val clickEvent: ClickEvent = ClickEvent.create()
            .page(EVPage.Msg.SayhiCard)
            .action(EVAction.Content.Feature)
            .requireId("12917")

        clickEvent.putExtra(
            "photo_number",
            if (info.greetCardPhotos != null) info.greetCardPhotos.size else 0
        )
            .putExtra("time_length", if (info.isOnline) "online" else "hiding")
            .putExtra(
                "logmap",
                if (StringUtils.isEmpty(info.logMapString)) "" else info.logMapString
            )
            .putExtra("introduce", GsonUtils.g().toJson(info.getMarks()))
            .putExtra(
                "photoid",
                if (CollectionUtils.isEmpty(info.greetCardPhotos)) "" else info.greetCardPhotos[0]
            )
            .putExtra("fans_sign", if (User.RELATION_FANS == info.relation) "1" else "0")
            .putExtra("is_gift", if (info.hasGiftMessage()) "1" else "0")
            .putExtra("sayhi_source", info.sourceText)
            .putExtra("ban_type", if (info.forbiddenBtnShow == 1) "1" else "0")
            .putExtra("source", source)
            .putExtra("info", StringUtils.join(socialCapital, "·"))
            .putExtra("feed_pos", slideStackView.getShowingDataIndex().toString() + "")
            .putExtra(
                "set_photoid",
                if (info.originalFirstPhoto != null) info.originalFirstPhoto else ""
            )
        if (user != null) {
            clickEvent.putExtra("momo_id", user.getMomoid())
            clickEvent.putExtra(
                "distance",
                if (StringUtils.isEmpty(info.getUser().distanceString)) "" else info.getUser().distanceString
            )
            clickEvent.putExtra(
                "msg_num",
                if (info.getMessages() == null) "0" else info.getMessages().size.toString() + ""
            )
            if (user.fortuneLevel > 0) {
                clickEvent.putExtra("wealth_rank", user.fortuneLevel)
            } else {
                clickEvent.putExtra("wealth_rank", "")
            }
            clickEvent.putExtra("vip_rank", if (user.isMomoVip) user.getVipActivityLevel() else 0)
            clickEvent.putExtra("svip_rank", if (user.isSvip()) user.getSvipActivityLevel() else 0)
        }
        clickEvent.submit()
    }

}