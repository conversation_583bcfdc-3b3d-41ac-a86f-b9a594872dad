package com.immomo.momo.message.sayhi.utils;

import android.text.TextUtils;

import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo;
import com.immomo.momo.service.bean.Message;

import java.util.List;

import androidx.annotation.NonNull;


/**
 * 招呼消息工具类
 *
 * <AUTHOR>
 * date 2020/5/25
 */
public class SayHiMessageUtils {
    /**
     * 判断是招呼消息 if逻辑需要确认
     *
     * @param message
     * @return
     */
    public static boolean isSayHiMessage(@NonNull Message message) {
        if (message.contentType != Message.CONTENTTYPE_MESSAGE_NOTICE &&
                message.contentType != Message.CONTENTTYPE_MESSAGE_SIMPLE_ACTIONLIST) {   //文本or虚拟礼物or新版资料页的图片
            return true;
        } else {
            return false;
        }
    }

    /**
     * 向上按钮展示的获取招呼卡片消息数
     * 去除contentType为CONTENTTYPE_MESSAGE_NOTICE的消息，若最后为0，再走原有的逻辑
     *
     * @param info
     * @return
     */
    public static int getSize(@NonNull SayHiInfo info) {
        int result = 0;
        if (info.messages != null && info.messages.size() > 0) {
            for (Message message : info.messages) {
                if (SayHiMessageUtils.isSayHiMessage(message)) {
                    result++;
                }
            }
        }
        return result;
    }

    /**
     * 获取对应momoId的招呼信息
     *
     * @param momoId
     * @param list
     * @return
     */
    public static SayHiInfo getSayHiInfo(String momoId, List<SayHiInfo> list) {
        if (list != null && !list.isEmpty()) {
            for (SayHiInfo sayHiInfo : list) {
                if (sayHiInfo != null && sayHiInfo.user != null &&
                        TextUtils.equals(sayHiInfo.user.momoid, momoId)) {
                    return sayHiInfo;
                }
            }
        }
        return null;
    }

    /**
     * 根据消息的发送方的性别获取展示内容
     */
    public static String getGenderGreet(Message message) {
        if (message != null && message.owner != null && message.owner.getGender() == 1) {   //push推送来的用户打招呼
            return "他向你打了个招呼";
        } else {  //女用户打招呼
            return "她向你打了个招呼";
        }
    }

    /**
     * 若服务器没有返回有效的数据，默认根据第一个message中的用户返回提示语
     *
     * @param messages
     * @return
     */
    public static String getGenderGreet(@NonNull List<Message> messages) {
        for (Message message : messages) {
            if (message.owner == null) {
                continue;
            } else {
                return getGenderGreet(message);
            }
        }
        return "";
    }
}
