package com.immomo.momo.message.paper.common

import android.graphics.Color
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.graphics.drawable.LayerDrawable
import android.os.Build
import com.cosmos.mdlog.MDLog
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.LogTag
import com.immomo.momo.MomoKit
import com.immomo.momo.R
import com.immomo.momo.message.activity.ChatActivity

/**
 * <AUTHOR>
 * @data 6/23/21.
 */

class SingleChatBackgroundPaperFragment : BackgroundPaperFragment() {

    companion object {
        fun newInstance(): SingleChatBackgroundPaperFragment {
            return SingleChatBackgroundPaperFragment()
        }
    }

    override fun onPageLoad() {
        super.onPageLoad()
        showNormalBg()
    }

    override fun showNormalBg() {
        chatBgDrawable = getNormalBg()
        activity?.window?.setBackgroundDrawable(chatBgDrawable)
        getChatActivity()?.switchToLightTheme()
    }

    private fun getBgDrawable(): Drawable {
        return try {
            val bgID = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                if (MomoKit.isManUser()) R.drawable.bg_single_chat_male else R.drawable.bg_single_chat_female
            } else {
                //8.0以下系统，使用小图片，减少内存使用
                if (MomoKit.isManUser()) R.drawable.bg_single_chat_male_small else R.drawable.bg_single_chat_female_small
            }

            val bt = (resources.getDrawable(bgID, null) as BitmapDrawable).bitmap
            val bg = BitmapDrawable(resources, bt)
            val bgDrawable = if (com.immomo.momo.util.MomoKit.isDarkMode(activity))
                ColorDrawable(Color.parseColor("#000000")) else ColorDrawable(Color.parseColor("#FFF7F7F7"))
            val finalDrawable = LayerDrawable(arrayOf(bgDrawable, bg))
            finalDrawable.setLayerInset(0, 0, 0, 0, 0)
            finalDrawable.setLayerInset(
                1,
                0,
                0,
                0,
                UIUtils.getScreenHeight() - UIUtils.getPixels(200f)
            )
            finalDrawable
        } catch (e: OutOfMemoryError) {
            //如果发生了oom，不使用图片背景，背景为白色
            MDLog.i(LogTag.COMMON, "getBgDrawable is OutOfMemoryError")
            ColorDrawable(UIUtils.getColor(R.color.C_11))
        }
    }


    override fun getNormalBg(): Drawable = getBgDrawable()

    private fun getChatActivity(): ChatActivity? {
        (activity as? ChatActivity)?.let {
            return it
        }
        return null
    }

}