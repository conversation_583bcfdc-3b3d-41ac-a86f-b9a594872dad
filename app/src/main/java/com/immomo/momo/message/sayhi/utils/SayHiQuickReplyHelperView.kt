package com.immomo.momo.message.sayhi.utils

import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.os.Build
import android.util.AttributeSet
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewStub
import android.view.ViewTreeObserver
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import cn.dreamtobe.kpswitch.util.KPSwitchConflictUtil
import cn.dreamtobe.kpswitch.util.KeyboardUtil
import com.cosmos.mdlog.MDLog
import com.immomo.framework.cement.CementAdapter
import com.immomo.framework.view.inputpanel.impl.MomoInputPanel
import com.immomo.framework.view.inputpanel.impl.emote.EmoteChildPanel
import com.immomo.framework.view.inputpanel.impl.emote.EmoteConstants
import com.immomo.marry.architecture.repository.KliaoMarryMainRepository
import com.immomo.mmutil.task.MomoMainThreadExecutor
import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.mmutil.toast.Toaster
import com.immomo.momo.R
import com.immomo.momo.android.view.BindPhoneTipView
import com.immomo.momo.android.view.MEmoteEditeText
import com.immomo.momo.android.view.tips.TipManager
import com.immomo.momo.feed.listener.CommentAtTextChangeListener
import com.immomo.momo.likematch.slidestack.SlideConst
import com.immomo.momo.message.sayhi.SayHiArgs
import com.immomo.momo.message.sayhi.itemmodel.bean.LikeSayHi
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo
import com.immomo.momo.message.sayhi.task.PostIgnoreOrLike
import com.immomo.momo.mvp.emotion.models.AbstractEmoteItemModel
import com.immomo.momo.service.bean.SayhiSession
import com.immomo.momo.util.BindPhoneHelper

/**
 * 打招呼回复控件
 */
class SayHiQuickReplyHelperView @JvmOverloads constructor(
        context: Context,
        attributes: AttributeSet? = null,
        defStyleAttr: Int = 0
) : ConstraintLayout(context, attributes, defStyleAttr) {

    companion object {
        @JvmStatic
        fun init(activity: Activity?) {
            activity ?: return
            val window = activity.window
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP && window != null && window.decorView != null) { // 设置全屏使得底部按钮不被键盘顶起
                window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
                window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                        View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                window.statusBarColor = Color.TRANSPARENT
                //设置白底黑字
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    activity.window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
                }
            } else window?.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        }
    }

    private var commentLayout: View? = null
    private var bindPhoneTipView: BindPhoneTipView? = null
    private var editText: MEmoteEditeText? = null
    private var mListener: CommentAtTextChangeListener? = null
    private var mInputPanel: MomoInputPanel? = null
    private var sendButton: View? = null
    private var emoteButton: View? = null
    private var visibleBtn: View? = null
    private var keyboardShowingListener: KeyboardUtil.OnKeyboardShowingListener? = null
    private var onKeyboardShowingGlobalListener: ViewTreeObserver.OnGlobalLayoutListener? = null
    private var switchPanelListener: KPSwitchConflictUtil.SwitchClickListener? = null
    private var sendFinishListener: OnSendFinishListener? = null
    /**
     * 当前选中的会话
     */
    private var curSelectedSayHiInfo: SayHiInfo? = null

    private var curSelectedSayHiSession: SayhiSession? = null

    private fun resetCurSessionInfo(sayHiInfo: SayHiInfo?) {
        curSelectedSayHiInfo = sayHiInfo
    }

    private fun resetCurSession(session: SayhiSession?) {
        curSelectedSayHiSession = session
    }

    init {
        LayoutInflater.from(context).inflate(R.layout.layout_sayhi_quick_reply_view, this, true)
        setOnClickListener {
            hideComment()
        }
    }

    /**
     *
     */
    private fun bindData(session: SayhiSession?, sayHiInfo: SayHiInfo?) {
        resetCurSessionInfo(sayHiInfo)
        resetCurSession(session)
    }

    private fun initEditTextViews() {
        if (commentLayout != null) return
        findViewById<ViewStub>(R.id.replay_input_viewstub)?.inflate()?.also { vstub ->
            commentLayout = vstub.findViewById(R.id.feed_comment_input_layout)
            bindPhoneTipView = vstub.findViewById<BindPhoneTipView>(R.id.tip_bind_phone).apply {
                setNeedCheck(true)
                setMode(BindPhoneHelper.MODE_IM)
                updateStatus()
            }
            editText = vstub.findViewById(R.id.tv_feed_editer)
            editText?.setHint(R.string.sayhi_stack_input_to_chat)
            mInputPanel = vstub.findViewById(R.id.simple_input_panel)
            sendButton = vstub.findViewById(R.id.feed_send_layout)
            val tvSend = vstub.findViewById<TextView>(R.id.send_comment_btn)
            if (tvSend != null) {
                tvSend.text = "发送"
            }
            visibleBtn = vstub.findViewById(R.id.iv_private_comment)
            visibleBtn?.visibility = View.GONE
            emoteButton = vstub.findViewById(R.id.iv_feed_emote)
            initEditText()
        }
    }

    private fun initEditText() {
        // mListener?.setAtTextChange(this)
        editText?.also {
            // mListener = CommentAtTextChangeListener(activity, editText)
            // it.addTextChangedListener(mListener) // at功能
            it.imeOptions = EditorInfo.IME_ACTION_SEND
            it.setOnEditorActionListener { v: TextView?, actionId: Int, event: KeyEvent? ->
                if (actionId == EditorInfo.IME_ACTION_SEND) {
                    sendMessage(it.text.toString())
                    return@setOnEditorActionListener true
                }
                false
            }
        }
        initInputMethod()
//        hideComment()
    }

    private fun sendMessage(content: String?) {
        if (checkSendSafe()) return
        if (content.isNullOrBlank()) {
            Toaster.show("消息不能为空")
            return
        }
        beforeMessageAboutToSend()
        postIgnoreOrLike(SayHiArgs.LIKE, SlideConst.SlideSource.CARD, content, 0, null, LikeSayHi.Requst.TYPE_RIGHT, null)
    }

    /**
     * 检查输入安全
     */
    private fun checkSendSafe(): Boolean {
        if (curSelectedSayHiInfo == null && curSelectedSayHiSession == null) {
            hideComment()
            return true
        }
        return false
    }

    private fun postIgnoreOrLike(likeType: Int, source: String?, messageText: String?, messageType: Int, toApiParams: Map<String, String?>?, consumeType: Int, touchType: String?) {
        var sayHiInfo = curSelectedSayHiInfo
        if (sayHiInfo == null) {
            sayHiInfo = SayHiInfo()
            sayHiInfo.user = curSelectedSayHiSession?.user
        }
        val request = LikeSayHi.Requst(
                likeType,
                source,
                sayHiInfo,
                messageText,
                messageType,
                toApiParams,
                consumeType,
                touchType
        )
        request.needPostMomoid = true
        MomoTaskExecutor.executeTask(
                MomoTaskExecutor.EXECUTOR_TYPE_USER,
                KliaoMarryMainRepository.getTaskTag(),
                PostIgnoreOrLike(request)
        )
        sendFinishListener?.onSend()
    }

    private fun beforeMessageAboutToSend() {
        hideComment()
        clearEditText()
        // Toaster.show("已发送")
    }

    private fun clearEditText() {
        editText?.setText("")
    }

    private fun initInputMethod() {
        //translucentStatusBar 为true，所以要设置为 true
        (context as Activity?).also { activity ->
            if (MomoInputPanel.isPanelInFullscreen(activity)) {
                mInputPanel?.isFullScreenActivity = true
            }
            //绑定面板
            onKeyboardShowingGlobalListener = KeyboardUtil.attach(activity, mInputPanel, getKeyboardShowingListener())
            KPSwitchConflictUtil.attach(mInputPanel, emoteButton, editText, getSwitchPanelListener())

            //初始化表情面板
            val emotePanel = EmoteChildPanel(activity)
            //设定输入表情类型：除了自定义表情之外都支持
            emotePanel.emoteFlag = EmoteChildPanel.Emote_Comment
            //绑定EditText
            emotePanel.setEditText(editText)
            //设置支持系统暗黑资源
            emotePanel.setSupportDark(true)
            emotePanel.setDeleteDarkStyle()
            emotePanel.setEmoteSelectedListener { adapter: CementAdapter?, model: AbstractEmoteItemModel<*>, type: Int -> sendEmoteComment(model.data.toString(), type) }
            mInputPanel?.addPanels(emotePanel)
            // 点击发送评论按钮
            sendButton?.setOnClickListener { v: View? -> sendMessage(editText?.text.toString()) }
        }
    }

    private fun sendEmoteComment(emote: String?, type: Int) {
        //发送大表情
        if (type == EmoteConstants.TYPE_IMAGE_SINGLE) {
            if (checkSendSafe()) return
            beforeMessageAboutToSend()
            postIgnoreOrLike(SayHiArgs.LIKE, SlideConst.SlideSource.CARD, emote, type, null, LikeSayHi.Requst.TYPE_RIGHT, null)
        }
    }

    private fun getSwitchPanelListener(): KPSwitchConflictUtil.SwitchClickListener? {
        if (switchPanelListener == null) {
            switchPanelListener = object : KPSwitchConflictUtil.SwitchClickListener {
                override fun onClickSwitch(switchToPanel: Boolean) {
                    if (switchToPanel) {
                        editText?.clearFocus()
                        mInputPanel?.showPanel()
                    } else {
                        editText?.requestFocus()
                    }
                }

                override fun onBeforeClick(): Boolean {
                    return true
                }
            }
        }
        return switchPanelListener
    }

    private fun getKeyboardShowingListener(): KeyboardUtil.OnKeyboardShowingListener? {
        if (keyboardShowingListener == null) {
            keyboardShowingListener = KeyboardUtil.OnKeyboardShowingListener { isShowing ->
                //监听到输入框收起时，隐藏评论view
                MDLog.i("SayHiQuickReplyHelper", "OnKeyboardShowingListener isShowing=$isShowing   mInputPanel?.visibility=${mInputPanel?.visibility}")
                if (!isShowing && mInputPanel?.visibility != View.VISIBLE) {
                    MomoMainThreadExecutor.post(hashCode()) { hideComment() }
                }
            }
        }
        return keyboardShowingListener
    }

    /**
     * 显示聊天弹窗
     */
    fun showComment(session: SayhiSession?, sayHiInfo: SayHiInfo?, listener: OnSendFinishListener? = null) {
        this.sendFinishListener = listener
        bindData(session, sayHiInfo)
        if (visibility != View.VISIBLE) {
            visibility = View.VISIBLE
        }
        initEditTextViews()
        commentLayout?.also {
            if (it.visibility != VISIBLE) {
                it.visibility = VISIBLE
            }
        }
        //如果没有显示表情面板或者输入法，则显示输入法
        mInputPanel?.also {
            if (!it.isPanelOrKeyboardShowing) {
                it.showKeyboard(editText)
            }
        }
        editText?.also {
            it.setSelection(it.text.toString().length)
        }
        this.sendFinishListener?.onCommentShow(true)
    }

    /**
     * 隐藏聊天弹窗
     */
    fun hideComment(): Boolean {
        if (commentLayout?.visibility == View.VISIBLE) {
            mInputPanel?.hidePanelAndKeyboard()
            commentLayout?.visibility = View.GONE
            dismiss()
            return true
        }
        dismiss()
        return false
    }

    private fun dismiss() {
        if (visibility != GONE) {
            visibility = GONE
            this.sendFinishListener?.onCommentShow(false)
        }
    }

    fun onResume() {

    }

    fun onPause() {
        hideComment()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        (context as Activity?)?.also {
            KeyboardUtil.detach(it, onKeyboardShowingGlobalListener)
            TipManager.unbindActivity(it)
        }
        keyboardShowingListener = null
        switchPanelListener = null
    }

}

interface OnSendFinishListener {
    fun onSend()

    fun onCommentShow(isShow: Boolean) {}
}