package com.immomo.momo.message.sayhi.task

import com.immomo.framework.task.BaseDialogTask
import com.immomo.momo.personalprofile.bean.PersonalProfileQuestion
import com.immomo.momo.personalprofile.module.data.api.ProfileApi.getFemaleRecommendQuestion

class GetQuestionListTask(
    var isFromOutSide: <PERSON><PERSON><PERSON>,
    var isRedEnvelope: <PERSON><PERSON><PERSON>,
    var sex: String?,
    var callback: (questions: List<PersonalProfileQuestion>?) -> Unit
) : BaseDialogTask<Any, Any, List<PersonalProfileQuestion>>("") {

    @Throws(Exception::class)
    override fun executeTask(vararg objects: Any?): List<PersonalProfileQuestion>? {
        return getFemaleRecommendQuestion()
    }

    override fun onTaskSuccess(questions: List<PersonalProfileQuestion>?) {
        callback.invoke(questions)
    }


}