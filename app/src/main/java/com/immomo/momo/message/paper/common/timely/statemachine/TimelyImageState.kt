package com.immomo.momo.message.paper.common.timely.statemachine

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.immomo.framework.imageloader.ImageType
import com.immomo.moment.mediautils.VideoDataRetrieverBySoft
import com.immomo.momo.message.paper.common.timely.TimelyCameraPaperFragment
import com.immomo.momo.multpic.entity.Photo
import com.immomo.momo.util.MediaFileUtil
import java.util.*


class TimelyImageState(stateManager: TimelyStateManager) :
    AbsBackToCloseState(stateManager) {

    fun takePhotoByVideo(mVideoPath: String?) {
        val vdrbs = VideoDataRetrieverBySoft()
        vdrbs.init(mVideoPath)
        val frameBitmap: Bitmap? = vdrbs.getFrameAtTime(vdrbs.duration / 2)
        stateMachine.multiRecorder?.cancelRecording()
        stateMachine.releaseRecord()

        frameBitmap?.let { bitmap ->
            val picFile = MediaFileUtil.getImageFile(
                UUID.randomUUID().toString(),
                ImageType.IMAGE_TYPE_CHAT_LARGE
            )
            picFile.outputStream().use {
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, it)
            }

            val photos = mutableListOf(
                Photo(
                    0,
                    picFile.absolutePath
                ).apply {
                    this.category = TimelyCameraPaperFragment.TIMELY
                    this.isTakePhoto = true
                    this.isFromCamera = true
                    this.isOriginal = false
                    this.isCheck = true
                    this.tempPath = picFile.absolutePath


                    val options = BitmapFactory.Options()
                    options.inJustDecodeBounds = true
                    BitmapFactory.decodeFile(picFile.absolutePath, options)
                    this.width = options.outWidth
                    this.height = options.outHeight
                    options.inJustDecodeBounds = false
                })
            runInUI {
                photos.let {
                    transition {
                        TimelyReadySendState(stateMachine).apply {
                            this.photos = it
                            showReadyView()
                        }
                    }
                }
            }
        } ?: let {
            cancelRecord()
        }

    }

    private fun cancelRecord() {
        runInUI {
            stateMachine.multiRecorder?.cancelRecording()
            stateMachine.mView.rollback2Ready()
        }
    }

}

