package com.immomo.momo.message.task;

import com.cosmos.mdlog.MDLog;
import com.immomo.momo.LogTag;
import com.immomo.momo.protocol.http.UserApi;

/**
 * Created by wangduanqing on 2017/8/31.
 */

public class ChatLogRunnable implements Runnable {
    private String momoid;

    public ChatLogRunnable(String momoid) {
        this.momoid = momoid;
    }

    @Override
    public void run() {
        try {
            UserApi.getInstance().viewChatLog(momoid);
        } catch (Exception e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
    }
}
