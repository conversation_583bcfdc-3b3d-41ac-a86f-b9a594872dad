package com.immomo.momo.message.task

import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.immomo.momo.mvp.message.task.ShowTipsTask
import com.immomo.momo.mvp.message.view.BaseMessageActivity
import java.lang.ref.WeakReference

/**
 *
 * author: hongming.wei
 * data: 2021/10/22
 */
class RecyclerScrollListener(context: BaseMessageActivity) : RecyclerView.OnScrollListener() {

    private var mActivity: WeakReference<BaseMessageActivity>? = null
    private var mLinearManager: LinearLayoutManager? = null

    init {
        mActivity = WeakReference(context)
    }


    override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
        super.onScrollStateChanged(recyclerView, newState)
        val activity = mActivity?.get()
        activity?.listScrollStateChange(newState)
        if (newState == RecyclerView.SCROLL_STATE_IDLE) {
            if (activity?.isInit == true) {
                var task = activity.showTipsTask
                task?.let {
                    recyclerView.removeCallbacks(it)
                }

                if (activity.msgChatRecycler.getLastVisiblePosition() == activity.msgChatData.getCount() - 1 &&
                    activity.paperCommonViewModel?.isNewMessageMaskShow() == true
                ) {
                    task = ShowTipsTask(activity, recyclerView)
                    recyclerView.postDelayed(task, 100)
                    activity.showTipsTask = task
                }

                if (activity.msgChatRecycler.getFirstVisiblePosition() == 0) {
                    activity.msgChatRecycler.setLoadMoreStart()
                }
            }
        }
    }

    override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
        super.onScrolled(recyclerView, dx, dy)
        val activity = mActivity?.get()
        if (mLinearManager == null) {
            mLinearManager = recyclerView.layoutManager as LinearLayoutManager?
        }
        mLinearManager?.let {
            activity?.listScroll(it.findFirstVisibleItemPosition(), it.childCount, it.itemCount)
        }
    }
}