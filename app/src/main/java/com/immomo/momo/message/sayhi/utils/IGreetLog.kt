package com.immomo.momo.message.sayhi.utils

import com.immomo.lcapt.evlog.anno.ClickPoint
import com.immomo.lcapt.evlog.anno.ExposurePoint
import com.immomo.lcapt.evlog.anno.Param
import com.immomo.lcapt.evlog.anno.ParamMap

interface IGreetLog {

    @ClickPoint(page = "msg.sayhi_card", action = "float.gift_close", requireId = "17386")
    fun clickGiftClose(@Param(value = "remote_id") id: String?)

    @ClickPoint(page = "msg.sayhi_card", action = "float.gift_ignore", requireId = "17385")
    fun clickGiftIgnore(@Param(value = "remote_id") id: String?)

    @ExposurePoint(page = "msg.sayhi_card", action = "float.sayhi_gift", requireId = "17384")
    fun showGiftCard(@Param(value = "remote_id") id: String?)

    @ClickPoint(page = "msg.sayhi_card", action = "float.gift_allow", requireId = "17383")
    fun clickGiftAgree(@Param(value = "remote_id") id: String?)

    @ClickPoint(page = "msg.sayhi_card", action = "list.sayhi_text", requireId = "17382")
    fun clickSayhi(
        @Param(value = "remote_id") id: String?, @Param(value = "is_gift") is_gift: Int?,
        @Param(value = "trends") hasFeed: Int, @Param("mark") hasMark: Int
    )

    @ClickPoint(page = "msg.sayhi_card", action = "list.avatar", requireId = "17381")
    fun clickSayhiAvatar(
        @Param(value = "remote_id") id: String?,
        @Param(value = "is_gift") is_gift: Int?,
        @Param(value = "trends") hasFeed: Int, @Param("mark") hasMark: Int
    )

    @ClickPoint(page = "msg.sayhi_card", action = "list.chat_button", requireId = "17379")
    fun clickSayhiBtn(
        @Param(value = "remote_id") id: String?,
        @Param(value = "is_gift") is_gift: Int?,
        @Param(value = "trends") hasFeed: Int, @Param("mark") hasMark: Int
    )

    @ClickPoint(page = "msg.sayhi_card", action = "list.allow_button", requireId = "17380")
    fun clickSayhiBtnChat(
        @Param(value = "remote_id") id: String?,
        @Param(value = "is_gift") is_gift: Int?,
        @Param(value = "trends") hasFeed: Int, @Param("mark") hasMark: Int
    )

    @ExposurePoint(page = "msg.sayhi_card", action = "list.feature", requireId = "17378")
    fun showItemSayhi(
        @Param(value = "remote_id") id: String?,
        @Param(value = "is_gift") is_gift: Int?,
        @Param(value = "trends") hasFeed: Int, @Param("mark") hasMark: Int
    )

    @ExposurePoint(
        page = "msg.sayhi_card",
        action = "content.redpocket_module",
        requireId = "18870"
    )
    fun showGiftCardRedPacket(@Param(value = "remoteID") id: String?)

    @ExposurePoint(page = "msg.chatlist", action = "content.redpocket_text", requireId = "18869")
    fun showSayHiSessionRedPacket()
}