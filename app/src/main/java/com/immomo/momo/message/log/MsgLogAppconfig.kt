package com.immomo.momo.message.log

import com.immomo.annotations.appconfig.AppConfigField
import com.immomo.annotations.appconfig.appconfigv2.AppConfigV2

/**
 * Created by huang<PERSON>lian<PERSON><PERSON> on 2024/8/8.
 *
 * Momo Tech 2011-2024 © All Rights Reserved.
 */
@AppConfigV2
object MsgLogAppconfig {
    @AppConfigField(key = "load_log", mark = "566", defValue = "0")
    val loadLog = 0

    @AppConfigField(key = "msg_list_limit", mark = "566", defValue = "0")
    val msgListLimit = 0

    @AppConfigField(key = "session_load_log", mark = "566", defValue = "0")
    val sessionLoadLog = 0
}