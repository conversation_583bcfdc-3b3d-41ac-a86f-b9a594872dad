package com.immomo.momo.message.sayhi.utils

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.text.style.ReplacementSpan
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.feed.fragment.RoundBackgroundColorSpan

class RoundBackgroundColorSpan(val bgColor: Int, val textColor: Int) : ReplacementSpan() {

    val TEXT_SIZE = UIUtils.sp2pix(10f)
    val TEXT_PADDING = UIUtils.getPixels(5f)
    private val mRectPaddingVerticalPx = UIUtils.getPixels(2.5f)
    private var mSize = 0

    private var mTagPaint = Paint()

    init {
        mTagPaint = Paint()
        mTagPaint.textSize = RoundBackgroundColorSpan.TEXT_SIZE.toFloat()
        mTagPaint.color = textColor
        mTagPaint.isAntiAlias = true
        mTagPaint.textAlign = Paint.Align.CENTER
    }

    override fun getSize(
        paint: Paint, text: CharSequence?, start: Int, end: Int, fm: Paint.FontMetricsInt?
    ): Int {
        val textSize = paint.textSize
        paint.textSize = TEXT_SIZE.toFloat()
        mSize =
            paint.measureText(text, start, end).toInt() + 2 * TEXT_PADDING
        paint.textSize = textSize
        return mSize + UIUtils.getPixels(6f)
    }

    override fun draw(
        canvas: Canvas, text: CharSequence?,
        start: Int, end: Int,
        x: Float, top: Int, y: Int, bottom: Int,
        paint: Paint
    ) {
        val color = paint.color
        val rect = createRect(x, y, paint)
        if ((end - start) <= 1) {
            drawBgOval(rect, canvas, paint)
        } else {
            drawBgRect(rect, canvas, paint)
        }
        drawText(rect, canvas, text ?: "", start, end)
        paint.color = color
    }

    private fun drawText(rect: RectF, canvas: Canvas, text: CharSequence, start: Int, end: Int) {
        val tagFontMetrics = mTagPaint.fontMetricsInt
        val textCenterX = rect.left + (rect.right - rect.left) / 2
        val rectCenterY = rect.bottom - (rect.bottom - rect.top) / 2
        val tagBaseLineY =
            rectCenterY + (tagFontMetrics.descent - tagFontMetrics.ascent) / 2f - tagFontMetrics.descent
        canvas.drawText(text, start, end, textCenterX, tagBaseLineY, mTagPaint)
    }

    private fun drawBgRect(rectF: RectF, canvas: Canvas, paint: Paint) {
        paint.color = bgColor
        canvas.drawRoundRect(
            rectF,
            UIUtils.getPixels(7.5f).toFloat(),
            UIUtils.getPixels(7.5f).toFloat(),
            paint
        )
    }

    private fun drawBgOval(rectF: RectF, canvas: Canvas, paint: Paint) {
        paint.color = bgColor
        canvas.drawOval(
            rectF,
            paint
        )
    }

    private fun createRect(x: Float, y: Int, paint: Paint): RectF {
        val fontMetrics = paint.fontMetricsInt
        val top = y + fontMetrics.ascent + mRectPaddingVerticalPx
        val right = x + mSize
        val bottom = y + fontMetrics.descent - mRectPaddingVerticalPx
        return RectF(x, top.toFloat(), right, bottom.toFloat())
    }
}

