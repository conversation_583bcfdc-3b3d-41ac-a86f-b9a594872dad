package com.immomo.momo.message.sayhi.stack

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.cosmos.mdlog.MDLog
import com.immomo.android.module.fundamental.Badge.parseUniformLabelTheme2Model
import com.immomo.android.module.fundamental.Badge.toDisplay
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.base.BaseActivity
import com.immomo.framework.cement.CementModel
import com.immomo.framework.cement.CementViewHolder
import com.immomo.framework.cement.SimpleCementAdapter
import com.immomo.framework.cement.eventhook.OnClickEventHook
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.utils.UIUtils
import com.immomo.framework.view.recyclerview.itemdecoration.LinearPaddingItemDecoration
import com.immomo.kotlin.extern.isNullOrEmpty
import com.immomo.mmstatistics.event.ClickEvent
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.molive.kotlin.extensions.isNotNullOrEmpty
import com.immomo.momo.R
import com.immomo.momo.android.view.RoundCornerFrameLayout
import com.immomo.momo.gotologic.GotoDispatcher
import com.immomo.momo.likematch.slidestack.BaseSlideCard
import com.immomo.momo.likematch.slidestack.BaseSlideStackView
import com.immomo.momo.message.NewSayHiPageConfigV2
import com.immomo.momo.message.NewSayUIConfigV1
import com.immomo.momo.message.helper.FrequentPreferenceHelper
import com.immomo.momo.message.sayhi.MessageParser
import com.immomo.momo.message.sayhi.MessageParser.extraHarassMessage
import com.immomo.momo.message.sayhi.contract.ISayhiCardDataProvider
import com.immomo.momo.message.sayhi.itemmodel.SayHiSmallImageSelectedItemModel
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiPhotoInfo
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiStackCardInfo
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiUserMark
import com.immomo.momo.message.sayhi.itemmodel.message.BaseMsgItemModel
import com.immomo.momo.message.sayhi.itemmodel.message.ImageMsgItemModel
import com.immomo.momo.message.sayhi.utils.NewSayHiConst
import com.immomo.momo.message.sayhi.utils.NewSayHiReportUtil
import com.immomo.momo.message.sayhi.utils.SayHiReportHelper
import com.immomo.momo.message.sayhi.widget.FlexBoxLayoutMaxLines
import com.immomo.momo.message.sayhi.widget.UserMarkView
import com.immomo.momo.personalprofile.widget.gravitysnaphelper.GravitySnapRecyclerView
import com.immomo.momo.router.ProfileRealAuth
import com.immomo.momo.router.ProfileRouter
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.User
import com.immomo.momo.service.sessions.SessionService
import com.immomo.momo.statistics.EVAction
import com.immomo.momo.statistics.EVPage
import com.immomo.momo.util.ColorUtils
import com.immomo.momo.util.view.BadgeView
import com.immomo.svgaplayer.view.MomoSVGAImageView
import info.xudshen.android.appasm.AppAsm.getRouter
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.math.abs

class NewSayHICardSlideCard @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : BaseSlideCard<SayHiStackCardInfo>(context, attrs, defStyleAttr) {

    companion object {
        val MAX_LIST_HEIGHT = UIUtils.getPixels(160f)

        val MAX_LIST_PADDING_30 = UIUtils.getPixels(30f)

        val MAX_LIST_PADDING_40 = UIUtils.getPixels(40f)

        const val SOURCE_START = "通过"
        const val SOURCE_END = "向你打招呼"
    }

    private val slideCardContainer by lazy { findViewById<FrameLayout>(R.id.slide_card_container) }
    private val imgAvatar by lazy { findViewById<ImageView>(R.id.img_avatar) }
    private val tvName by lazy { findViewById<TextView>(R.id.tv_name) }
    private val viewUniform by lazy { findViewById<BadgeView>(R.id.view_uniform) }
    private val imgAuth by lazy { findViewById<ImageView>(R.id.img_auth) }
    private val ivLocation by lazy { findViewById<ImageView>(R.id.iv_location) }
    private val locationDistance by lazy { findViewById<TextView>(R.id.location_distance) }
    private val ivReport by lazy { findViewById<ImageView>(R.id.iv_report) }
    private val photoSelectorRV by lazy { findViewById<GravitySnapRecyclerView>(R.id.list_photos) }
    private val messageList by lazy { findViewById<RecyclerView>(R.id.message_list) }
    private val slideLikeContainer by lazy { findViewById<ConstraintLayout>(R.id.slide_like_container) }
    private val likeIcon by lazy { findViewById<ImageView>(R.id.ic_slide_like) }
    private val slideDislikeContainer by lazy { findViewById<ConstraintLayout>(R.id.slide_dislike_container) }
    private val dislikeIcon by lazy { findViewById<ImageView>(R.id.ic_slide_dislike) }
    private val tvUserMark by lazy { findViewById<TextView>(R.id.tv_user_mark) }
    private val imgAvatarLeft by lazy { findViewById<View>(R.id.img_avatar_left) }
    private val topHarassTop by lazy { findViewById<View>(R.id.top_harass_top) }
    private val harassLeftIcon by lazy { findViewById<ImageView>(R.id.harass_left_icon) }
    private val harassContent by lazy { findViewById<TextView>(R.id.harass_content) }
    private val imgAvatarRight by lazy { findViewById<View>(R.id.img_avatar_right) }
    private val onlineContainer by lazy {
        findViewById<RoundCornerFrameLayout>(R.id.user_online_status_container)
    }
    private val userFromSourceContainer by lazy { findViewById<TextView>(R.id.tv_hi_source_from) }   // 招呼来源展示
    private val tvNewHiTag by lazy { findViewById<TextView>(R.id.tv_new_hi_tag) }   // 新招呼标记
    private val svgaLikeBg by lazy { findViewById<MomoSVGAImageView>(R.id.svga_like_bg) }   // 卡片喜欢动画
    private val slideCardViewContainer by lazy { findViewById<ConstraintLayout>(R.id.slidecard_content_container) }   // 卡片喜欢动画
    private val flexLabelView: FlexBoxLayoutMaxLines by lazy { findViewById<FlexBoxLayoutMaxLines>(R.id.view_tag) }
    private var smallLayoutManager: LinearLayoutManager? = null
    private val smallAdapter by lazy { SimpleCementAdapter() }

    // 消息列表配置
    private var msgLayoutManager: LinearLayoutManager? = null
    private val msgAdapter by lazy { SimpleCementAdapter() }

    private var photoData = mutableListOf<String>()
    private var selectModel: SayHiSmallImageSelectedItemModel? = null

    private var cardListener: BaseSlideStackView.CardSwitchListener? = null
    private var curSelectPosition = 0   // 当前点击的数据

    private var msgDataProvider: ISayhiCardDataProvider? = null
    var curSayHiInfo: SayHiStackCardInfo? = null
    private var harassGotoStr: String? = null

    private var isSlidedCard = false // 卡片是否被划走了

    private val MESSAGE_TIME_LIMIT = System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000L

    override fun onClick(v: View?) {}

    override fun getLayoutId(): Int = R.layout.new_say_hi_gift_slide_card

    override fun init() {}

    override fun fillData(
        info: SayHiStackCardInfo?, index: Int, listener: BaseSlideStackView.CardSwitchListener?
    ) {
        kotlin.runCatching {
            y = 0f
            curSelectPosition = 0
            slideCardContainer.setOnClickListener {
                listener?.onItemClick(imgAvatar, index, index)
            }
            isSlidedCard = false
            curSayHiInfo = info
            hideSlideTipView()
            this.cardListener = listener
            if (info == null) {
                return
            }
            val sayHiNetInfo = info.sayHiNetInfo
            // 刷新第一个头像
            refreshFirstAvatar(info)
            // 刷新名字和真人认证的信息
            refreshNameWithAuth(info)
            // 刷新标签信息
            refreshUniformLabel(info)
            // 位置及在线信息
            refreshCardTopTag(info)
            // 处理头像和消息
            refreshContentView(info)
            // 举报
            initReport(info)
            // 处理骚扰用户提醒
            harassTopTip(info.sayHiNetInfo)
            if (NewSayUIConfigV1.isUserNewTag()) {
                tvUserMark.visibility = View.GONE
                refreshMarkList(info)
            } else {
                // 处理印记
                refreshUserMark(sayHiNetInfo)
            }
        }
    }

    private fun refreshMarkList(hiInfo: SayHiStackCardInfo) {
        val greetCardMarkList = hiInfo.sayHiNetInfo?.greetCardMarkList ?: kotlin.run {
            this.flexLabelView.visibility = View.GONE
            return
        }
        if (greetCardMarkList.isNotEmpty()) {
            this.flexLabelView.visibility = View.VISIBLE
            if (this.flexLabelView.flexItemCount > 0) {
                this.flexLabelView.removeAllViews()
            }
            for (mark in greetCardMarkList) {
                val view = createUserMark(mark, false)
                this.flexLabelView.addView(view, view.layoutParams)
            }
        } else {
            this.flexLabelView.visibility = View.GONE
        }
    }

    private fun createUserMark(mark: SayHiUserMark, isGift: Boolean): View {
        val itemView = UserMarkView(context)
        itemView.setInfo(mark, isGift)
        val layoutParams =
            LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        layoutParams.rightMargin = UIUtils.getPixels(5f)
        layoutParams.topMargin = UIUtils.getPixels(5f)
        itemView.layoutParams = layoutParams
        return itemView
    }

    private fun harassTopTip(sayHiNetInfo: SayHiInfo?) {
        harassGotoStr = sayHiNetInfo?.highestTopBarInfo?.gotoStr.safe()
        val highestTopBarInfo = sayHiNetInfo?.highestTopBarInfo ?: kotlin.run {
            topHarassTop.visibility = GONE
            return
        }
        val bgColor =
            ColorUtils.parseAllColor(highestTopBarInfo.bgColor.safe(), R.color.black_opacity_40)
        val icon = highestTopBarInfo.icon.safe()
        val textColor = ColorUtils.parseAllColor(highestTopBarInfo.textColor.safe(), R.color.white)
        ImageLoader.load(icon).into(harassLeftIcon)
        harassContent.setTextColor(textColor)
        harassContent.text = highestTopBarInfo.tips.safe()
        topHarassTop.setBackgroundColor(bgColor)
        topHarassTop.visibility = VISIBLE
        topHarassTop.setOnClickListener {
            harassGotoStr?.also {
                if (it.isNotBlank()) {
                    GotoDispatcher.action(it, context).execute()
                }
            }
        }
    }

    private fun refreshFirstAvatar(info: SayHiStackCardInfo) {
        kotlin.runCatching {
            val greetCardImgs = info.sayHiNetInfo?.greetCardImgs
            if (!greetCardImgs.isNullOrEmpty()) {
                val photoUrlAvatars = greetCardImgs.first()
                dealFoldScreen()
                if (photoUrlAvatars != null && photoUrlAvatars.origin?.isNotBlank() == true) {
                    ImageLoader.load(photoUrlAvatars.origin).placeholder(R.color.color_ebebeb)
                        .into(imgAvatar)
                }
                imgAvatarLeft.setOnClickListener {
                    if (photoSelectorRV.visibility == VISIBLE) {
                        val jumpPosition = curSelectPosition - 1
                        if (jumpPosition < 0) {
                            return@setOnClickListener
                        }
                        val itemCount = smallAdapter.itemCount
                        if (jumpPosition >= itemCount) {
                            return@setOnClickListener
                        }
                        val cementModel = smallAdapter.dataModels[jumpPosition]
                        scrollToAndSelect(jumpPosition, cementModel)
                    }
                }
                imgAvatarRight.setOnClickListener {
                    if (photoSelectorRV.visibility == VISIBLE) {
                        val jumpPosition = curSelectPosition + 1
                        val itemCount = smallAdapter.itemCount
                        if (jumpPosition >= itemCount) {
                            return@setOnClickListener
                        }
                        val cementModel = smallAdapter.dataModels[jumpPosition]
                        scrollToAndSelect(jumpPosition, cementModel)
                    }
                }
            } else {
                ImageLoader.load(R.color.color_ebebeb).placeholder(R.color.color_ebebeb)
                    .into(imgAvatar)
            }
        }
    }

    private fun refreshNameWithAuth(info: SayHiStackCardInfo) {
        tvName.text = info.sayHiNetInfo?.name.safe()
        tvName.setOnClickListener {
            getRouter(ProfileRouter::class.java).gotoProfile(context, info.sayHiInfo.momoid)
            ClickEvent.create().action(EVAction.Content.Call)
                .page(EVPage.Msg.SayhiCard)
                .putExtra("momoid", info.sayHiInfo.momoid)
                .submit()
        }
        info.sayHiInfo.user?.let {
            if (it.isVip || it.isSvip()) {
                tvName.setTextColor(UIUtils.getColor(R.color.font_vip_name))
            } else {
                tvName.setTextColor(UIUtils.getColor(R.color.color_323333_to_80f))
            }
        }
        val realAuth = info.sayHiNetInfo?.realAuth
        if (realAuth != null && realAuth.status == ProfileRealAuth.SUCCESS) {
            imgAuth.visibility = VISIBLE
            ImageLoader.load(realAuth.icon).into(imgAuth)
            imgAuth.setOnClickListener {
                GotoDispatcher.action(realAuth.gotoStr, context).execute()
            }
        } else {
            imgAuth.visibility = GONE
        }
    }

    private fun refreshUniformLabel(info: SayHiStackCardInfo) {
        val uniformLabel = info.sayHiNetInfo?.uniformLabels
        viewUniform.toDisplay(uniformLabel.parseUniformLabelTheme2Model(), viewUniform)
    }

    private fun refreshUserMark(sayHiNetInfo: SayHiInfo?) {
        tvUserMark.visibility = if (sayHiNetInfo?.greetCardMark.isNullOrEmpty()) GONE else {
            tvUserMark.text = sayHiNetInfo?.greetCardMark
            VISIBLE
        }
    }

    private fun initReport(info: SayHiStackCardInfo) {
        ivReport.setOnClickListener {
            (context as? BaseActivity?)?.also {
                SayHiReportHelper.showReportDialog(it, info.sayHiInfo, info.sayHiNetInfo)
            }
        }
    }

    private fun refreshCardTopTag(info: SayHiStackCardInfo) {
        val sayHiNetInfo = info.sayHiNetInfo
        //  定位信息
        val avatarTag = sayHiNetInfo?.avatarTag
        val avatarTagText = avatarTag?.text.safe()
        if (avatarTag != null && avatarTagText.isNotNullOrEmpty()) {
            locationDistance.text = avatarTagText
            val textColor = avatarTag.textColor.safe()
            locationDistance.setTextColor(ColorUtils.parseColor(textColor, Color.BLACK))
            val bgColor = avatarTag.bgColor.safe()
            onlineContainer.setBackgroundColor(ColorUtils.parseColor(bgColor, Color.WHITE))
            if (!avatarTag.icon.isNullOrBlank()) {
                ImageLoader.load(avatarTag.icon).into(ivLocation)
            }
            onlineContainer.visibility = VISIBLE
        } else {
            onlineContainer.visibility = GONE
        }
        // 招呼来源
        var cardSourceOld = sayHiNetInfo?.getSourceText().safe()
        val cardSourceText = sayHiNetInfo?.cardSourceText.safe()
        if (cardSourceText.isNullOrEmpty()) {
            if (NewSayHiPageConfigV2.sourceExtractEnableConfig
                && cardSourceOld.isNotBlank()
                && cardSourceOld.startsWith(SOURCE_START)
                && cardSourceOld.endsWith(SOURCE_END)
            ) {
                cardSourceOld = cardSourceOld.replaceFirst(SOURCE_START, "")
                cardSourceOld = cardSourceOld.replace(SOURCE_END, "")
                userFromSourceContainer.text = "来自$cardSourceOld"
                userFromSourceContainer.visibility = View.VISIBLE
            } else {
                userFromSourceContainer.visibility = View.GONE
            }
        } else {
            userFromSourceContainer.visibility = View.VISIBLE
            userFromSourceContainer.text = "来自$cardSourceText"
        }
        // 展示新招呼
        if (info.isNewHiInStackCard) {
            tvNewHiTag.visibility = View.VISIBLE
        } else {
            tvNewHiTag.visibility = View.GONE
        }
    }

    private fun dealFoldScreen() {
        val screenWidth = UIUtils.getScreenWidth()
        val screenHeight = UIUtils.getScreenHeight()
        val sizeAspect = screenHeight.toFloat() / screenWidth // 折叠屏，越来越小
        val smallMinAspect = 16f / 14   // 1.142857142857143
        val middleMinAspect = 2000f / 1080 // 1.777777777777778
        val longAspect = 2150f / 1080    // 1.777777777777778
        // MDLog.i("dealFoldScreen", "$sizeAspect   smallMinAspect=$smallMinAspect   smallBigAspect=$longAspect")
        if (longAspect > sizeAspect) {
            (imgAvatar.layoutParams as? ConstraintLayout.LayoutParams)?.also {
                it.dimensionRatio = if (sizeAspect >= middleMinAspect && sizeAspect < longAspect) {
                    "1.2"
                } else if (sizeAspect > smallMinAspect && sizeAspect <= middleMinAspect) {
                    "1.5"
                } else {
                    imgAvatar.scaleType = ImageView.ScaleType.CENTER
                    "2.5"
                }
                imgAvatar.layoutParams = it
            }
        }
    }

    private fun refreshContentView(info: SayHiStackCardInfo) {
        (context as? BaseActivity?)?.also {
            it.lifecycleScope.launch(MMDispatchers.User) {
                var msgModelList: List<CementModel<*>> = mutableListOf() // 消息展示列表
                SessionService.getInstance().filterMsgForSayHiRecommend(
                    arrayListOf(info.sayHiNetInfo),
                    arrayListOf<String>(),
                    MESSAGE_TIME_LIMIT,
                    "",
                    Int.MAX_VALUE,
                    true
                )
                val rawMessages: MutableList<Message>? = info.sayHiNetInfo?.getMessages()
                if (rawMessages != null) {
                    val iterator = rawMessages.iterator()
                    while (iterator.hasNext()) {
                        val message = iterator.next()
                        if (FrequentPreferenceHelper.i().isHarassGreetingOpen
                            && message.sayHiFrom == Message.SAY_HI_TYPE_HARASS
                        ) { // 移除spam消息
                            iterator.remove()
                        } else if (message.contentType == Message.CONTENTTYPE_MESSAGE_NOTICE) { // 移除notice消息
                            iterator.remove()
                        }
                    }
                    val momoid = info.sayHiInfo.momoid
                    info.sayHiNetInfo?.hasSpamMessage =
                        extraHarassMessage(rawMessages, momoid, true) > 0
                    // 展示包含愿望回答的消息，按时间顺序展示
                    msgModelList = MessageParser.newSayHiGenerateMsgModelList(
                        rawMessages, initMsgProvider(), null
                    )
                }
                withContext(MMDispatchers.Main) {
                    if (msgLayoutManager == null) {
                        msgLayoutManager = LinearLayoutManager(it, RecyclerView.VERTICAL, false)
                        messageList.layoutManager = msgLayoutManager
                        messageList.adapter = msgAdapter
                        messageList.addItemDecoration(
                            LinearPaddingItemDecoration(0, 0, UIUtils.getPixels(10f))
                        )
                    }
                    msgAdapter.clearData()
                    msgAdapter.updateDataList(msgModelList.safe())
                    refreshSmallPreview(info.sayHiNetInfo)
                    refreshCardTopTag(info) // 刷新来源招呼
                    dealMsgImgHeight(msgModelList.size)
                }
            }
        }
    }

    /**
     * 根据消息
     */
    private fun dealMsgImgHeight(size: Int) {
        messageList.post {
            (messageList.layoutParams as? ConstraintLayout.LayoutParams)?.also {
                if (size < 3) {
                    slideCardViewContainer.setPadding(0, 0, 0, MAX_LIST_PADDING_40)
                } else {
                    slideCardViewContainer.setPadding(0, 0, 0, MAX_LIST_PADDING_30)
                }
                val height = messageList.height
                if (height > MAX_LIST_HEIGHT) {
                    it.height = MAX_LIST_HEIGHT
                } else {
                    it.height = ViewGroup.LayoutParams.WRAP_CONTENT
                }
                messageList.layoutParams = it
            }
        }
    }

    private fun initMsgProvider(): ISayhiCardDataProvider? {
        if (msgDataProvider == null) {
            msgDataProvider = object : ISayhiCardDataProvider {
                private var imageMessages: MutableList<Message>? = null

                /**
                 * 纪录当前正处于下载状态的消息ID，用于重绘界面时能够恢复之前下载动画
                 */
                private val downloadingMessageIds = HashSet<String>()

                //标记已经下载了图片的消息，如果下载了图片，就直接展示
                private val downloadedImageMsgIds = HashSet<String>()

                override fun getUser(): User? {
                    return curSayHiInfo?.sayHiNetInfo?.user
                }

                override fun getActivity(): Activity? {
                    return context as? Activity?
                }

                override fun removeModel(profileModel: CementModel<*>?) {}

                override fun getCardSwitchListener(): ISayHiCardSwitchLisener? {
                    return null
                }

                override fun getSayhiInfo(): SayHiInfo? {
                    return curSayHiInfo?.sayHiNetInfo
                }

                override fun getTaskTag(): String {
                    return <EMAIL>
                }

                override fun reportUser(momoid: String) {}

                override fun getImageMessages(): List<Message> {
                    return imageMessageList
                }

                override fun notifyModelChange(model: CementModel<*>?) {
                    model?.let { msgAdapter.notifyDataChanged(it) }
                }

                override fun showDialog(dialog: Dialog) {
                    val activity: Activity? = activity
                    if (activity == null || activity.isFinishing || activity.isDestroyed) {
                        return
                    }
                    if (activity is BaseActivity) {
                        activity.showDialog(dialog)
                    } else {
                        dialog.show()
                    }
                }

                override fun isMsgDownloaded(msgId: String): Boolean {
                    return downloadedImageMsgIds.contains(msgId)
                }

                override fun isMsgDownloading(msgId: String): Boolean {
                    return downloadingMessageIds.contains(msgId)
                }

                override fun onMsgDownloaded(msgId: String) {
                    downloadedImageMsgIds.add(msgId)
                    downloadingMessageIds.remove(msgId)
                }

                override fun onMsgStartDownloding(msgId: String) {
                    downloadingMessageIds.add(msgId)
                }

                override fun isSayhi(): Boolean {
                    return true
                }

                private val imageMessageList: List<Message>
                    get() {
                        if (imageMessages == null) {
                            imageMessages = mutableListOf()
                            val models: List<CementModel<*>> = msgAdapter.dataModels
                            for (i in models.indices) {
                                val m = models[i]
                                val isImage = m is ImageMsgItemModel
                                if (isImage && (m as BaseMsgItemModel<*>).message != null) {
                                    (m as? BaseMsgItemModel<*>)?.message?.let {
                                        imageMessages?.add(it)
                                    }
                                }
                            }
                        }
                        return imageMessages as MutableList<Message>
                    }
            }
        }
        return msgDataProvider
    }

    private fun refreshSmallPreview(sayHiNetInfo: SayHiInfo?) {
        if (smallLayoutManager == null) {
            initSmallPhotosView()
        }
        sayHiNetInfo ?: return
        photoData.clear()
        val photos = mutableListOf<String>()
        val greetCardImgs = sayHiNetInfo.greetCardImgs
        greetCardImgs?.forEach { // 动态图片
            val smallImg = it.small
            val originImg = it.origin
            if (smallImg?.isNotBlank() == true) {
                photos.add(smallImg)
            }
            if (originImg?.isNotBlank() == true) {
                photoData.add(originImg)
            }
        }
        smallAdapter.clearData()
        val count = photos.size
        if (greetCardImgs != null && count > 1) {
            val cementModels = transSmallPreview(greetCardImgs)
            smallAdapter.addDataList(cementModels)
            selectModel = cementModels.first() as SayHiSmallImageSelectedItemModel
            photoSelectorRV.visibility = View.VISIBLE
        } else {
            photoSelectorRV.visibility = View.GONE
        }
    }

    private fun transSmallPreview(sayHiPhotoInfos: MutableList<SayHiPhotoInfo>?): List<CementModel<*>> {
        smallAdapter.clearFooters()
        val models: MutableList<SayHiSmallImageSelectedItemModel> = ArrayList()
        if (sayHiPhotoInfos == null) return models
        sayHiPhotoInfos.forEachIndexed { i, it ->
            val url = it.small.safe()
            val smallModel =
                SayHiSmallImageSelectedItemModel(url, false, i == 0, photoSelectorRV, it.label)
            if (i == 0) {
                smallModel.selected = true
            }
            models.add(smallModel)
        }
        return models
    }

    private fun initSmallPhotosView() {
        smallLayoutManager = LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
        photoSelectorRV.layoutManager = smallLayoutManager
        photoSelectorRV.adapter = smallAdapter
        photoSelectorRV.itemAnimator = null
        photoSelectorRV.requestDisallowInterceptTouchEvent(true)
        smallAdapter.addEventHook(object : OnClickEventHook<CementViewHolder>(
            CementViewHolder::class.java
        ) {
            override fun onClick(
                view: View, viewHolder: CementViewHolder, position: Int, rawModel: CementModel<*>
            ) {
                scrollToAndSelect(position, rawModel)
            }

            override fun onBindMany(viewHolder: CementViewHolder): List<View> {
                return listOf(viewHolder.itemView)
            }
        })
    }

    /**
     * 选中了头像
     */
    private fun scrollToAndSelect(position: Int, model: CementModel<*>) {
        if (selectModel == model) return
        if (position < photoData.size) {
            ImageLoader.load(photoData[position]).placeholder(R.color.color_ebebeb).into(imgAvatar)
        }
        if (model is SayHiSmallImageSelectedItemModel && selectModel != model) { // 刷新选中状态
            selectModel?.selected = false
            selectModel?.let {
                smallAdapter.notifyDataChanged(it)
            }
            model.selected = true
            selectModel = model
            smallAdapter.notifyDataChanged(model)
            curSelectPosition = position
            NewSayHiReportUtil.clickBigCardAvatar(model.imgUrl.safe(), model.imageType.safe())
        }
    }

    override fun scaleLikeIconWhileSlide(percentage: Float) {
        if (isSlidedCard) { // 被划走了不设置动画了
            return
        }
        MDLog.i(NewSayHiConst.TAG, "scaleLikeIconWhileSlide   percentage=$percentage")
        if (percentage > 0) {
            slideLikeContainer.alpha = percentage
            likeIcon.alpha = percentage
            likeIcon.scaleX = percentage
            likeIcon.scaleY = percentage
            if (svgaLikeBg.alpha == 0f) {
                svgaLikeBg.alpha = 1f
                svgaLikeBg.stopAnimCompletely()
                svgaLikeBg.startSVGAAnim("sayhi_card_like_bg.svga", 1)
            }
        } else if (percentage < 0) {
            svgaLikeBg.alpha = 0f
            val safePercent = abs(percentage)
            slideDislikeContainer.alpha = safePercent
            dislikeIcon.alpha = safePercent
            dislikeIcon.scaleX = percentage
            dislikeIcon.scaleY = percentage
        } else {
            hideSlideTipView()
        }
        cardListener?.onSlide(percentage)
    }

    private fun hideSlideTipView() {
        MDLog.i("scaleLikeIconWhileSlide", "hideSlideTipView")
        svgaLikeBg.alpha = 0f
        slideLikeContainer.alpha = 0f
        slideDislikeContainer.alpha = 0f
        isSlidedCard = false
    }

    fun setSlidedCard(alreadySlided: Boolean) {
        isSlidedCard = alreadySlided
    }

    override fun endAnim() {
    }

    override fun onDestroy() {
        svgaLikeBg.stopAnimCompletely()
    }

    override fun resetViewsOnCard(changedView: BaseSlideCard<SayHiStackCardInfo>?) {

    }

    fun refreshCardMsg(sessionId: String) {
        curSayHiInfo?.also {
            if (sessionId == it.sayHiInfo.momoid) {
                MDLog.i(NewSayHiConst.TAG, "refreshCardMsg=$sessionId")
                // 更新了卡片数据
                refreshContentView(it)
            }
        }
    }
}