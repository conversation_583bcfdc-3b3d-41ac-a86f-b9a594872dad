package com.immomo.momo.message.sayhi.contract;

import com.immomo.framework.base.BaseActivity;
import com.immomo.momo.maintab.model.SayHiListReqParam;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiListResult;
import com.immomo.momo.personalprofile.bean.PersonalProfileQuestion;

import java.util.List;
import java.util.Map;

/**
 * Created by lei.jialin on 2019/4/4.
 */
public interface IHiCardsStackContract {

    interface IPresenter {

        void pushNotInLocalMomoids(SayHiListResult list);

        void onDestroy();

        boolean onBackPressed();

        SayHiListResult getSayHiList();

        SayHiListReqParam getCurReqParam();

        void blockAndDeleteSession(String reportOptionUserId, int action, boolean deleteMsg, Map<String, String> otherParams);

        /**
         * 请求卡片数据
         *
         * @param remoteId
         * @param isLoadMore false重新请求数据
         */
        void loadCardData(String remoteId, boolean isLoadMore);

        /**
         * 请求无本地缓存卡片列表数据(如推送过来的)
         *
         * @param remoteId
         * @param isLoadMore
         */
        void loadCardDataNoCache(String remoteId, boolean isLoadMore);

        boolean hasMore();

        void updateUnreadStatus();

        /**
         * 加载女性回答问题（接口随机返回）
         *
         * @param isFromOutSide true 阻断弹出dialog
         * @param sex
         */
        void loadFemaleQuestion(boolean isFromOutSide, boolean isRedEnvelope, String sex);

        /**
         * 设置问题
         *
         * @param questionId
         * @param question
         * @param fromOutSide true设置点击 false 弹窗
         * @param isSelect
         */
        void setReplyQuestion(String questionId, String question, boolean fromOutSide,boolean isRedEnvelope, boolean isSelect);
    }

    interface IView {

        void refreshData();

        void closeActivity();

        BaseActivity getContext();

        boolean isClosing();

        void toggleHistoryEntry(boolean visible);

        void showEmptyViewOrGoHiSessionList();

        void loadDataSuc(SayHiListResult result);

        void showErrorView();

        void showFemaleQuestionDialog(List<PersonalProfileQuestion> questions, boolean isFromOutSide,boolean isRedEnvelope, String sex);

        /**
         * 女性设置问题成功
         *
         * @param reply
         * @param isFromOutSide
         */
        void onSettingSavedSuccess(String reply, boolean isFromOutSide , boolean isRedEnvelope);

        /**
         * 女性设置问题失败
         */
        void onSettingError();

        SayHiInfo getCurrentCardInfo();
    }
}
