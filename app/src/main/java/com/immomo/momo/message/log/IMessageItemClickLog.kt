package com.immomo.momo.message.log

import com.immomo.lcapt.evlog.anno.ClickPoint
import com.immomo.lcapt.evlog.anno.ExposurePoint
import com.immomo.lcapt.evlog.anno.Param

interface IMessageItemClickLog {
    @ClickPoint(page = "msg.chatpage", action = "bubble_long_press", requireId = "13609")
    fun longClickMessage(@Param(value = "setting") setting: String)

    @ClickPoint(page = "msg.chatpage", action = "bubble_setting", requireId = "13621")
    fun clickBubbleSetting(@Param(value = "setting") setting: String)

    @ExposurePoint(page = "msg.sayhi_from", action = "svip_sayhi_notic", requireId = "13736")
    fun showUpgradeNotice(
        @Param(value = "contact_momo_id") momoId: String,
        @Param(value = "msg_id") msgId: String
    )

    @ClickPoint(page = "msg.sayhi_from", action = "svip_sayhi_notice", requireId = "13737")
    fun clickUpgradeNotice(
        @Param(value = "contact_momo_id") momoId: String,
        @Param(value = "msg_id") msgId: String
    )

    @ExposurePoint(page = "msg.chatpage", action = "content.chatexception", requireId = "17025")
    fun showSpamDialog()

    @ExposurePoint(page = "msg.chatlist", action = "content.userexception", requireId = "17026")
    fun showSpamGuide()

    @ClickPoint(page = "msg.chatlist", action = "content.userexception", requireId = "17027")
    fun clickSpamGuide()

}