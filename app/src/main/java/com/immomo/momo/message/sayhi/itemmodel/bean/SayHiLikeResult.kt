package com.immomo.momo.message.sayhi.itemmodel.bean

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

class SayHiLikeResult {
    var ec = -1

    @Expose
    @SerializedName("redPacket")
    var redPacketInfo: SayHiLikeRedPacket? = null

    @Expose
    @SerializedName("toast")
    var toast: String? = ""

    @Expose
    @SerializedName("msg_text")
    var msg_text: String? = ""
}


class SayHiLikeRedPacket {
    @Expose
    @SerializedName("received")
    var successRedPacket: SayHiRedPacket? = null

    @Expose
    @SerializedName("failToast")
    var failToast: String? = ""
}
