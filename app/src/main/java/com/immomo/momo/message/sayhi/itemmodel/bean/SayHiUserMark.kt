package com.immomo.momo.message.sayhi.itemmodel.bean

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import java.io.Serializable

class SayHiUserMark : Serializable {
    @Expose
    var text: String? = null

    @Expose
    var icon: String? = null
}

class SayHiPhotoInfo : Serializable {
    @Expose
    @SerializedName("imageUrl")
    var imageUrl: String? = null

    @Expose
    var action: String? = null

    @Expose
    @SerializedName("small")
    var small: String? = null

    @Expose
    @SerializedName("origin")
    var origin: String? = null

    @Expose
    @SerializedName("label")
    var label: String? = null    // 类型，feed动态，avatar/feed
}