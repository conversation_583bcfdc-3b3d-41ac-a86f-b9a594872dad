package com.immomo.momo.message.bean;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;


public class GroupActiveKitBeanWrapper {

    @Expose
    @SerializedName(value = "item")
    private GroupActiveKitBean bean;

    @Expose
    @SerializedName(value = "show_task_guide")
    private int showTaskGuide;

    public GroupActiveKitBean getBean() {
        return bean;
    }

    public void setBean(GroupActiveKitBean bean) {
        this.bean = bean;
    }

    public int getShowTaskGuide() {
        return showTaskGuide;
    }

    public void setShowTaskGuide(int showTaskGuide) {
        this.showTaskGuide = showTaskGuide;
    }
}
