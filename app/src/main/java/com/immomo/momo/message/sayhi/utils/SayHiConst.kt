package com.immomo.momo.message.sayhi.utils

import com.cosmos.mdlog.MDLog
import com.immomo.kotlin.extern.isNotNullOrEmpty
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.momo.im.GiftSayHiAppConfigV1
import com.immomo.momo.maintab.session2.SessionUpdateBundle
import com.immomo.momo.maintab.session2.data.manager.SessionKey.Companion.fromString
import com.immomo.momo.maintab.session2.data.manager.SessionManager.Companion.get
import com.immomo.momo.maintab.session2.defs.GiftSayHiSessionDefinition
import com.immomo.momo.message.sayhi.SayHiStackCache
import com.immomo.momo.message.sayhi.SayHiUiTest
import com.immomo.momo.message.sayhi.itemmodel.bean.LikeSayHi
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiStackCardInfo
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiUserModel
import com.immomo.momo.message.sayhi.task.PostIgnoreOrLike
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.sessions.SessionService
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.ArrayList

object SayHiConst {

    const val TAG = "SayHiConst"

    const val MAX_SHOW_CARD_STACK = 2

    /**
     * 打招呼加载下一页
     */
    const val EVENT_SAYHI_LIST_LOADMORE_DATA = "event_sayhi_list_loadmore_data"

    const val EVENT_SAYHI_STACK_CARD_REPORT = "event_sayhi_stack_card_report"

    const val EVENT_SAYHI_STACK_CARD_WEB_REPORT = "event_sayhi_stack_card_web_report"

    /**
     * 当卡片左滑不举报时，测试要将列表的未曝光迁移到曝光
     */
    const val EVENT_SAYHI_STACK_CARD_STACK_LEFT = "event_sayhi_stack_card_stack_left"

    /**
     * 上次请求接口的时间
     */
    const val KEY_LAST_NEW_SAYHI_REQUEST_TIME = "key_last_new_sayhi_request_time"

    /**
     * 卡片完成加载更多数据
     */
    const val EVENT_SAYHI_CARD_FINISHED_LOADMORE_DATA = "event_sayhi_card_finished_loadmore_data"

    /**
     * 未曝光的打招呼列表
     */
    var unExposureSayHiList: LinkedHashMap<String, SayHiStackCardInfo>? = null

    /**
     * 未曝光的加载更多的数据
     */
    private var unExposureLoadMoreSayHiList: LinkedHashMap<String, SayHiStackCardInfo>? = null

    /**
     * 记录已经曝光的session用于分组
     */
    private var alreadyExposureSessions = arrayListOf<String>()

    var pageTag: Long = 0

    var sayhiCardPushId: String? = "" // push卡片推送的id

    fun initPage() {
        pageTag = System.currentTimeMillis()
    }

    /**
     * 获取当前需要展示的卡片集合
     */
    fun getSayHiCard(): MutableList<SayHiStackCardInfo> {
        val listData = mutableListOf<SayHiStackCardInfo>()
        unExposureSayHiList?.forEach {
            listData.add(it.value)
        }
        return listData
    }

    /**
     * 当session已经曝光了
     */
    fun onSessionExposure(sessionId: String?) {
        sessionId ?: return
        alreadyExposureSessions.add(sessionId)
    }

    /**
     * 检查是否已经本地记录曝光
     */
    fun checkSessionExposure(sessionId: String?): Boolean {
        sessionId ?: return false
        return alreadyExposureSessions.contains(sessionId)
    }

    /**
     * 增加加载更多翻卡
     */
    fun appendLoadMoreCard(model: SayHiUserModel?) {
        model ?: return
        if (unExposureLoadMoreSayHiList == null) {
            unExposureLoadMoreSayHiList = LinkedHashMap()
        }
        val sayHiSession = model.sayHiSession
        unExposureLoadMoreSayHiList?.put(
            sayHiSession.momoid,
            SayHiStackCardInfo(sayHiSession, model.sayHiInfo)
        )
    }

    /**
     * 是否应该加载更多打招呼
     */
    fun needShowCardStack(): Boolean {
        return (unExposureLoadMoreSayHiList?.size ?: 0) >= MAX_SHOW_CARD_STACK
    }

    /**
     * 插入礼物消息
     */
    fun insertGiftMessage(msg: Message?) {
        msg ?: return
        val remoteId = msg.remoteId
        unExposureSayHiList?.get(remoteId)?.msgGift = msg
        unExposureLoadMoreSayHiList?.get(remoteId)?.msgGift = msg
    }

    fun refreshLoadMoreCards() {
        unExposureSayHiList?.clear()
        unExposureLoadMoreSayHiList?.let {
            unExposureSayHiList?.putAll(it)
            it.clear()
        }
    }

    /**
     * 清理卡片数据
     */
    fun clearSlideCardData() {
        unExposureLoadMoreSayHiList?.clear()
        unExposureLoadMoreSayHiList = null
        unExposureSayHiList?.clear()
        unExposureSayHiList = null
    }

    /**
     * 释放资源
     */
    fun release() {
        clearSlideCardData()
    }

    @JvmStatic
    fun onSayHiUnlockToChat(momoid: String?) {
        momoid?.isNotNullOrEmpty {
            if (SayHiUiTest.isTest()) {
                val request = LikeSayHi.Requst(it, LikeSayHi.Requst.TYPE_READ, -1)
                request.justConsumeType = true
                MomoTaskExecutor.executeTask(
                    MomoTaskExecutor.EXECUTOR_TYPE_USER,
                    hashCode(),
                    PostIgnoreOrLike(request)
                )
            }
        }
    }

    @JvmStatic
    fun refreshGiftMsgUnread() {
        GlobalScope.launch(MMDispatchers.User) {
            kotlin.runCatching {
                // 清理所有未读消息状态
                SessionService.getInstance().updateAllGiftSayhiOnlyInStackIgnore(
                    System.currentTimeMillis()
                )
                // 重新刷新礼物招呼
                get().syncSession(
                    SessionUpdateBundle.ClearUnread(fromString(GiftSayHiSessionDefinition.SAYHI))
                )
            }
        }
    }

    @JvmStatic
    fun refreshGiftMsgSessionState() {
        GlobalScope.launch(MMDispatchers.User) {
            kotlin.runCatching {
                // 重新刷新礼物招呼
                get().syncSession(
                    SessionUpdateBundle.ReloadInfo(fromString(GiftSayHiSessionDefinition.SAYHI))
                )
            }
        }
    }

    @JvmStatic
    fun recommendSayHiId(mLikeMeId: String?) {
        sayhiCardPushId = mLikeMeId
    }

    @JvmStatic
    fun clearRecommendSayHiId() {
        sayhiCardPushId = null
    }

    /**
     * 关闭卡片页面删除礼物卡片
     */
    @JvmStatic
    fun checkDeleteGiftCard(unReadCards: ArrayList<SayHiInfo>) {
        if (GiftSayHiAppConfigV1.isOpenExp()) {
            GlobalScope.launch(MMDispatchers.User) {
                kotlin.runCatching {
                    unReadCards.iterator().also {
                        while (it.hasNext()) {
                            val sayHiInfo = it.next()
                            val sayhiSession =
                                SessionService.getInstance().getSayhiSession(sayHiInfo.momoid)
                            MDLog.i("checkDeleteGiftCard=", sayhiSession?.momoid)
                            if (sayhiSession != null && sayhiSession.isFromGift) {
                                it.remove()
                                if (unReadCards.isEmpty()) {
                                    SayHiStackCache.clearCache()
                                    return@launch
                                }
                            }
                        }
                    }
                    SayHiStackCache.setData(unReadCards)
                }
            }
        } else {
            SayHiStackCache.setData(unReadCards)
        }
    }

}