package com.immomo.momo.message.bean;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class GreetSession {

    /**
     * title : 推荐招呼
     * list : ["100312","269114576","464926926","41388501","220393760"]
     */

    @Expose
    @SerializedName("title")
    private String title;
    @Expose
    @SerializedName("list")
    private List<String> list;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<String> getList() {
        return list;
    }

    public void setList(List<String> list) {
        this.list = list;
    }
}
