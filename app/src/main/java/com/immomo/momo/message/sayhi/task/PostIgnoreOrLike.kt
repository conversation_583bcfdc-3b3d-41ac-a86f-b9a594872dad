package com.immomo.momo.message.sayhi.task

import android.os.Bundle
import android.text.TextUtils
import com.immomo.framework.view.inputpanel.impl.emote.EmoteConstants
import com.immomo.mmutil.StringUtils
import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.mmutil.toast.Toaster
import com.immomo.momo.MomoKit
import com.immomo.momo.innergoto.matcher.SayHiMatcher
import com.immomo.momo.maintab.sessionlist.SessionListFragment
import com.immomo.momo.message.MsgUnreadProcessor
import com.immomo.momo.message.helper.MessageHelper
import com.immomo.momo.message.sayhi.SayHiUiTest
import com.immomo.momo.message.sayhi.activity.HiCardStackActivity
import com.immomo.momo.message.sayhi.activity.SayHiListActivity.Companion.Action_Session_REPLAY
import com.immomo.momo.message.sayhi.itemmodel.bean.LikeSayHi.Requst
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiLikeResult
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiRedPacket
import com.immomo.momo.message.sayhi.utils.NewSayHiSessionFlowUtil
import com.immomo.momo.messages.service.SingleMsgService
import com.immomo.momo.protocol.http.SayHiApi
import com.immomo.momo.protocol.imjson.receiver.MessageKeys
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.bean.User
import com.immomo.momo.service.sessions.LastMsgCache
import com.immomo.momo.service.sessions.MessageServiceHelper
import com.immomo.momo.service.sessions.SessionService


class PostIgnoreOrLike(private val pm: Requst) :
    MomoTaskExecutor.Task<Any, Any, SayHiLikeResult>("") {

    var eventListener: OnPostIgnoreOrLikeListener? = null
    var showNotiViewBlock: ((result: SayHiRedPacket?) -> Unit)? = null

    override fun executeTask(vararg objects: Any): SayHiLikeResult {
        val toUser: User? = pm.info?.getUser()
        NewSayHiSessionFlowUtil.onDealHiCard()
        if (pm.isLike && toUser != null) {
            // 转移招呼会话到正常会话列表中去（只是数据库迁移）
            SessionService.getInstance().moveSayhiToSession(pm.momoid)
            SingleMsgService.getInstance().updateMessagesIgnore(pm.momoid)
            sendHasRead(pm)
        } else if (pm.isIgnore) {
            SingleMsgService.getInstance().updateSayhiInnerIgnore(pm.momoid)
            sendHasRead(pm)
            kotlin.runCatching {
                if (pm.isHeartbeatSvip) { // 心动招呼处理
                    val momoid = pm.momoid
                    if (StringUtils.isNotBlank(momoid)) {
                        SingleMsgService.getInstance().updateHeartbeatHiRead(pm.momoid)
                    }
                }
            }
        }
        val sayHiLikeResult = SayHiApi.getInstance().postIgnoreOrLike(pm)
        val chars = pm.messageText
        if (pm.isLike && !TextUtils.isEmpty(chars)) {
            if (pm.messageType == EmoteConstants.TYPE_IMAGE_SINGLE) {
                sendEmoteMessage(chars, pm.user, pm.messageType)
            } else {
                sendTextMessage(toUser, chars.toString())
            }
        }
        return sayHiLikeResult
    }

    override fun onTaskSuccess(sayHiLikeResult: SayHiLikeResult) {
        if (sayHiLikeResult.ec == 0 && pm.isLike) {
            broadcastSessionChange(pm.momoid)
        }
        eventListener?.onSuccess()
        eventListener = null

        if (StringUtils.isNotEmpty(sayHiLikeResult.redPacketInfo?.failToast)) {
            Toaster.show(sayHiLikeResult.redPacketInfo?.failToast ?: "")
        } else {
            showNotiViewBlock?.invoke(sayHiLikeResult.redPacketInfo?.successRedPacket)
        }
    }

    private fun sendHasRead(pm: Requst) {
        if (pm.info == null) {
            return
        }
        if (pm.info.getMessages() != null) {
            for (message in pm.info.getMessages()) {
                MsgUnreadProcessor.putUnread(pm.momoid, message.msgId)
            }
        }
        MsgUnreadProcessor.sendHasRead(pm.momoid, true)
    }

    private fun broadcastSessionChange(momoid: String) {
        // 转移招呼会话到正常会话列表中去后，发出广播通知对话列表更新
        if (!TextUtils.isEmpty(momoid)) {
            val bundle = Bundle()
            bundle.putString(SessionListFragment.Key_SessionId, "u_$momoid")
            bundle.putInt(SessionListFragment.Key_SessionType, Session.TYPE_CHAT)
            bundle.putInt(SessionListFragment.Key_SayHiSessionLikeType, pm.like)
            if (pm.needPostMomoid) {
                bundle.putString(SessionListFragment.Key_ChatId, momoid)
            }
            MomoKit.getApp().dispatchMessage(bundle, SessionListFragment.Action_SessionChanged)
            if (SayHiUiTest.isTest()) {
                bundle.putString(MessageKeys.Key_RemoteId, momoid)
                MomoKit.getApp().dispatchMessage(bundle, Action_Session_REPLAY)
            }
        }
    }

    fun sendTextMessage(user: User?, content: String?) {
        if (user == null) return
        val message = MessageHelper.getInstance()
            .packetTextMessage(content, user, null, Message.CHATTYPE_USER)
        message.newSource =
            SayHiMatcher.buildSayHiSourceByParams(HiCardStackActivity::class.java.name, "", null)
        sendMessage(message)
    }

    private fun sendEmoteMessage(emote: CharSequence, toUser: User?, type: Int) {
        //发送大表情
        if (type == EmoteConstants.TYPE_IMAGE_SINGLE) {
            val message = MessageHelper.getInstance()
                .sendEmotionMessage(emote.toString(), toUser, null, Message.CHATTYPE_USER, type)
            message.newSource = SayHiMatcher.buildSayHiSourceByParams(
                HiCardStackActivity::class.java.name,
                "",
                null
            )
            sendMessage(message)
        }
    }

    fun sendMessage(message: Message?) {
        MessageServiceHelper.getInstance().saveSent(message)
        MomoKit.getApp().sendMessage(message)
        LastMsgCache.onSendNewMsg(message)
    }

    override fun onTaskError(e: Exception?) {
        super.onTaskError(e)
        eventListener?.onError()
        eventListener = null
    }
}

/**
 *
 */
interface OnPostIgnoreOrLikeListener {
    fun onError()

    fun onSuccess()
}