package com.immomo.momo.message.paper.common

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.ViewStub
import androidx.lifecycle.ViewModelProvider
import com.cosmos.mdlog.MDLog
import com.immomo.framework.account.MessageManager
import com.immomo.framework.base.BaseActivity
import com.immomo.framework.imageloader.ImageType
import com.immomo.momo.LogTag
import com.immomo.momo.MomoKit
import com.immomo.momo.R
import com.immomo.momo.eventbus.DataEvent
import com.immomo.momo.gift.GiftCategoryConstants
import com.immomo.momo.gift.base.ContinuityGiftPlayBean
import com.immomo.momo.gift.bean.GiftDynamic
import com.immomo.momo.message.bean.MessageGiftData
import com.immomo.momo.message.paper.BasePaperFragment
import com.immomo.momo.message.paper.PaperCommonViewModel
import com.immomo.momo.message.paper.chat.ChatPaperBooleanListener
import com.immomo.momo.message.paper.event.PaperEvent
import com.immomo.momo.message.paper.event.PlayGiftData
import com.immomo.momo.mvp.message.gift.ChatGiftChecker
import com.immomo.momo.mvp.message.gift.ChatGiftPlayer
import com.immomo.momo.mvp.message.gift.IChatGiftPlayer
import com.immomo.momo.mvp.message.view.BaseMessageActivity
import com.immomo.momo.protocol.imjson.receiver.MessageKeys
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.User
import com.immomo.momo.service.bean.message.Type15Content
import com.immomo.momo.service.sessions.SessionUserCache
import com.immomo.momo.util.LruCache

/**
 * 礼物动画播放
 * <AUTHOR>
 * @data 2020-12-18.
 */

open class GiftEffectPaperFragment : BasePaperFragment(), MessageManager.MessageSubscriber {

    var chatGiftPlayer: IChatGiftPlayer<ContinuityGiftPlayBean>? = null
    private var mContentView: View? = null
    val chatGiftChecker = ChatGiftChecker()
    private var mPaperCommonViewModel: PaperCommonViewModel? = null
    private var cache = LruCache<String, Message>(50)

    companion object {
        fun newInstance(): GiftEffectPaperFragment {
            return GiftEffectPaperFragment()
        }
    }

    override fun getContainerId(): Int = R.id.chat_gift_effect_paper_container

    override fun getPageLayout(): Int = R.layout.paper_chat_gift_effect


    override fun initPageViews(contentView: View?) {
        initViewWithEvent(contentView)
        getBaseActivity()?.let {
            initViewModel(it)
        }
    }

    fun initViewWithEvent(contentView: View?) {
        mContentView = contentView
        initGift()
        MessageManager.registerMessageReceiver(
            this.hashCode(),
            this,
            800,
            MessageKeys.Key_Gift_Dynamic_Action
        )
    }

    fun initViewModel(it: BaseActivity) {
        mPaperCommonViewModel = ViewModelProvider(it).get(PaperCommonViewModel::class.java)
        mPaperCommonViewModel?.chatGiftPlayingListener = object : ChatPaperBooleanListener {
            override fun onResult(): Boolean {
                return chatGiftPlayer?.isGiftPlaying() ?: false
            }
        }
    }

    override fun onPageLoad() {

    }

    private fun initGift() {
        chatGiftChecker.setListener { giftDynamic ->
            playGifByGifDynamic(giftDynamic)
        }
        val view = mContentView?.findViewById<ViewStub>(R.id.gift_show_anim)
        view?.let { viewStub ->
            chatGiftPlayer = ChatGiftPlayer(viewStub)
        }
    }

    open fun playGif(giftPlayBean: ContinuityGiftPlayBean, level: Int) {
        //多选编辑时，进场特效暂时屏蔽
        getBaseActivity()?.let {
            if (!it.mIsShowMultiChoice) {
                chatGiftPlayer?.play(giftPlayBean, level)
            }
        }
    }

    private fun playGifByGifDynamic(dynamic: GiftDynamic) {
        MDLog.i(LogTag.Gift.mm_gift, "playGifByGifDynamic")
        val giftBean = dynamic.transform() ?: return
        playGif(giftBean, 1)
    }


    fun playGiftByMsg(gift: Message?) {
        if (null == gift) {
            return
        }

        val currentGift = gift.messageContent as? Type15Content
        //hideTunnel 1 不显示动画和礼物通道
        if (currentGift == null || currentGift.hideTunnel == 1) {
            return
        }
        var continueSendGiftNum = currentGift.giftNum
        if (continueSendGiftNum > 1) { // 连送档位礼物播放效果
            for (i in 0 until continueSendGiftNum) {
                val startPlayGiftAnimation = startPlayGiftAnimation(gift, currentGift, true, i)
                if (i == 0 && startPlayGiftAnimation) {
                    startPlayGiftAnimation(gift, currentGift, true, i, true)
                }
            }
        } else { // 普通礼物连送效果
            startPlayGiftAnimation(gift, currentGift)
        }
    }

    private fun startPlayGiftAnimation(
        gift: Message,
        currentGift: Type15Content,
        isContinueGift: Boolean = false,
        continueGiftIndex: Int = 0,
        isAddEmptyGift: Boolean = false
    ): Boolean {
        var isAddedComboExcitationVideo = false // 是否添加了连送激励视频特效，需要补充一次点击普通礼物消息补充托盘
        var giftUser: User? = null
        giftUser = gift.owner as? User
        if (giftUser == null) {
            if (gift.receive) {
                giftUser = SessionUserCache.getUser(gift.remoteId)
                if (giftUser == null) {
                    giftUser = User()
                }
                giftUser.setMomoid(gift.remoteId)
            } else {
                giftUser = MomoKit.getApp().currentUser
                if (giftUser == null) {
                    giftUser = User()
                }
            }
        }

        val giftBean = ContinuityGiftPlayBean()
        giftBean.avatarUrl = giftUser.loadImageId
        giftBean.avatarUrlType = ImageType.IMAGE_TYPE_ALBUM_SMALL
        giftBean.giftUrl = currentGift.pic
        var isNeedClearGiftEffect = false // 是否应该清除动画特效
        if (isContinueGift) { // 记录连送按钮
            giftBean.repeatTimes =
                currentGift.repeatTimes - currentGift.giftNum + continueGiftIndex + 1
            isNeedClearGiftEffect = currentGift.isAnimationCombo && continueGiftIndex > 0 || isAddEmptyGift
            MDLog.i(
                "startPlayGiftAnimation", giftBean.repeatTimes.toString() + "   "
                        + currentGift.repeatTimes + " isNeedClearGiftEffect=$isNeedClearGiftEffect"
            )
        } else {
            giftBean.repeatTimes = currentGift.repeatTimes
        }
        if (!isNeedClearGiftEffect) { // 连送礼物只需播一次动画
            giftBean.giftEffect = currentGift.giftEffect
        }
        giftBean.momoId = giftUser.id
        giftBean.giftId = currentGift.giftId
        giftBean.repeatId = currentGift.repeatId
        giftBean.title = currentGift.giftText1
        giftBean.desc = currentGift.giftText2
        giftBean.isEffectGame = currentGift.isEffect
        giftBean.receiverId = currentGift.receiveId
        giftBean.animType = ChatGiftPlayer.getAnimTypeByLevel(currentGift.level)
        giftBean.luaPageUrl = currentGift.luaPageUrl
        val textList = currentGift.textList
        if (!textList.isNullOrEmpty()) {
            giftBean.textList = textList
        }
        val imageList = currentGift.imageList
        if (!imageList.isNullOrEmpty()) {
            giftBean.imageList = imageList
        }
        if (!isForceNoBlurIcon()
            && (GiftCategoryConstants.FLASH == currentGift.appId.toString() || GiftCategoryConstants.FLASH_VOICE == currentGift.appId.toString())
            && MomoKit.getCurrentOrGuestMomoId() == gift.receiveId
        ) {
            giftBean.isAvatarBlur = true
        }
        if (null != currentGift.cpEffect) {
            giftBean.giftEffect = currentGift.cpEffect
            if (currentGift.sender != null) {
                giftBean.senderName = currentGift.sender.name
                giftBean.avatarUrl = currentGift.sender.avatar
            }
            if (currentGift.receiver != null) {
                giftBean.receiverName = currentGift.receiver.name
                giftBean.receiverAvatarUrl = currentGift.receiver.avatar
            }
        }
        if (currentGift.isAnimationCombo && !isNeedClearGiftEffect && !isAddEmptyGift) {
            giftBean.animType = currentGift.animationLevel
            isAddedComboExcitationVideo = true
        }
        if (!isContinueGift && cache.containsKey(gift.messageId)) {
            return isAddedComboExcitationVideo
        }
        cache.put(gift.messageId, gift)
        playGif(giftBean, currentGift.level)
        return isAddedComboExcitationVideo
    }

    open fun isForceNoBlurIcon() = false

    open fun checkGifDynamic(bundle: Bundle?) {
        if (bundle == null) {
            return
        }
        getBaseActivity()?.let {
            val dynamic = bundle.getParcelable<GiftDynamic>(MessageKeys.Key_Gift_Dynamic) ?: return
            MDLog.i(LogTag.Gift.mm_gift, dynamic.toString())
            MDLog.i(LogTag.Gift.mm_gift, it.remoteId)
            if (it.chatType == Message.CHATTYPE_USER && !dynamic.isChatType) {
                return
            }
            if (it.chatType == Message.CHATTYPE_GROUP && !dynamic.isGroupType) {
                return
            }
            if (!TextUtils.equals(dynamic.target_id, it.remoteId)) {
                return
            }
            chatGiftChecker.check(dynamic)
        }
    }

    override fun onResume() {
        super.onResume()
        chatGiftPlayer?.onResume()
    }

    override fun onPause() {
        super.onPause()
        chatGiftPlayer?.onPause()
    }


    override fun onMessageReceive(bundle: Bundle?, action: String?): Boolean {
        when (action) {
            MessageKeys.Key_Gift_Dynamic_Action -> {
                MDLog.i(LogTag.Gift.mm_gift, "Key_Gift_Dynamic_Action")
                checkGifDynamic(bundle)
            }
        }
        return false
    }


    override fun onEvent(event: DataEvent<Any>) {
        super.onEvent(event)
        MDLog.i("GiftEffectPaperFragment", "event.action:${event.action}")
        when (event.action) {
            PaperEvent.PAPER_EVENT_PLAY_GIFT_BY_MSG -> {
                (event.data as? MessageGiftData)?.let {
                    if (it.tagId == getBaseActivity()?.taskTag?.toString()) {
                        (it.message as? Message)?.let { message ->
                            playGiftByMsg(message)
                        }
                    }
                }
            }

            PaperEvent.PAPER_EVENT_PLAY_GIFT -> {
                (event.data as? PlayGiftData)?.let {
                    it.giftPlayBean?.let { continuityGiftPlayBean ->
                        playGif(continuityGiftPlayBean, it.level)
                    }
                }
            }

            PaperEvent.PAPER_EVENT_GIFT_CHECKER_ADD -> {
                (event.data as? String)?.let {
                    chatGiftChecker.addMsgId(it)
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        MessageManager.unregisterMessageReceiver(this.hashCode())
        chatGiftChecker.release()
        chatGiftPlayer?.onDestroy()

    }

    fun getBaseActivity(): BaseMessageActivity? {
        (activity as? BaseMessageActivity)?.let {
            return it
        }
        return null
    }

}