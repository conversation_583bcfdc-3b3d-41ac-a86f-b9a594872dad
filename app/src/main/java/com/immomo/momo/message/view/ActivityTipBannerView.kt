package com.immomo.momo.message.view

import android.animation.Animator
import android.animation.ObjectAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.momo.R
import com.immomo.momo.gotologic.GotoDispatcher
import com.immomo.momo.message.bean.MaskChatActivityInfo
import com.immomo.momo.message.bean.SetMaskChatActivityData
import com.immomo.push.util.MomoMainThreadExecutor

/**
 * 蒙面活动
 */
class ActivityTipBannerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    val containerOneView: ConstraintLayout by lazy { findViewById<ConstraintLayout>(R.id.container_one) }
    val tipIconView: ImageView by lazy { findViewById<ImageView>(R.id.tip_icon) }
    val tipContentView: TextView by lazy { findViewById<TextView>(R.id.tip_content) }
    val containerTwoView: ConstraintLayout by lazy { findViewById<ConstraintLayout>(R.id.container_two) }
    val tipIconTwoView: ImageView by lazy { findViewById<ImageView>(R.id.tip_icon_two) }
    val tipContentTwoView: TextView by lazy { findViewById<TextView>(R.id.tip_content_two) }
    private var curActivityInfo: MaskChatActivityInfo? = null
    private val animationList = mutableListOf<Animator>()

    init {
        inflate(context, R.layout.layout_activity_tip_banner_view, this)
        setOnClickListener {
            curActivityInfo?.action?.takeIf { it.isNotEmpty() }?.also {
                GotoDispatcher.action(it, context).execute()
            }
        }
    }

    fun bindData(activityInfo: MaskChatActivityInfo) {
        MomoMainThreadExecutor.cancelAllRunnables(hashCode())
        this.curActivityInfo = activityInfo
        resetViewData(activityInfo, tipContentView, tipIconView)
    }

    private fun resetViewData(
        activityInfo: MaskChatActivityInfo, textView: TextView, iconView: ImageView
    ) {
        textView.text = activityInfo.title?.safe()
        activityInfo.getIconWithDark()?.takeIf { it.isNotEmpty() }?.also {
            ImageLoader.load(it).into(iconView)
        }
    }

    fun refreshView(setMaskChatActivityData: SetMaskChatActivityData) {
        MomoMainThreadExecutor.cancelAllRunnables(hashCode())
        cancelAnimation()
        setMaskChatActivityData.dynamic?.also {
            this.curActivityInfo = it
            resetViewData(it, tipContentTwoView, tipIconTwoView)
            startAnimationAlpha(containerTwoView, containerOneView)
            setMaskChatActivityData.persist?.also {
                MomoMainThreadExecutor.postDelayed(hashCode(), {
                    this.curActivityInfo = it
                    resetViewData(it, tipContentView, tipIconView)
                    startAnimationAlpha(containerOneView, containerTwoView)
                }, 2000)
            }
        } ?: run {
            setMaskChatActivityData.persist?.also {
                bindData(it)
            }
        }
    }

    private fun startAnimationAlpha(viewAlphaIn: View, viewAlphaOut: View) {
        ObjectAnimator.ofFloat(viewAlphaIn, "alpha", 0f, 1f).setDuration(200).apply {
            animationList.add(this)
            start()
        }
        ObjectAnimator.ofFloat(viewAlphaOut, "alpha", 1f, 0f).setDuration(200).apply {
            animationList.add(this)
            start()
        }
    }

    private fun cancelAnimation() {
        animationList.forEach {
            it.cancel()
        }
        animationList.clear()
    }

    fun onDestroy() {
        cancelAnimation()
        MomoMainThreadExecutor.cancelAllRunnables(hashCode())
    }

}