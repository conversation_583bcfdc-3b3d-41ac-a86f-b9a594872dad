package com.immomo.momo.message.http

import com.immomo.momo.protocol.http.core.HttpClient

/**
 * -----------------------------------------------------------------
 * Copyright (C)  sun
 * Create: 2022/9/13 2:21 下午
 * -----------------------------------------------------------------
 */
class NoteFeedApi: HttpClient() {
    companion object {
        val instance: NoteFeedApi by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            NoteFeedApi()
        }
    }

    fun noteFeedApplyUnlock(remoteId: String, event: String): String? {
        val url = "$HttpsHost/v4/feed/note/lock"
        val map = mapOf(
            "remoteId" to remoteId,
            "event" to event,
            "source" to "alert"
        )
        return doPost(url, map)
    }
}