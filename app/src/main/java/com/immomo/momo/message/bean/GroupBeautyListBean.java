package com.immomo.momo.message.bean;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.immomo.momo.util.StringUtils;


/**
 * <AUTHOR>
 */
public class GroupBeautyListBean {
    @Expose
    @SerializedName("momoid")
    public String momoid;
    @Expose
    @SerializedName("icon")
    public String icon;

    @Expose
    @SerializedName("dynamic_icon")
    public String dynamicIcon;

    public boolean hasNormalIcon() {
        return !StringUtils.isEmpty(icon);
    }

    public boolean hasDynamicIcon() {
        return !StringUtils.isEmpty(dynamicIcon);
    }
}
