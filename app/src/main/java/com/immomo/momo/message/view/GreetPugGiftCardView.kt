package com.immomo.momo.message.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.viewpager.widget.MomoViewPager
import androidx.viewpager.widget.PagerAdapter
import androidx.viewpager.widget.ViewPager
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.kotlin.ImageType
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.utils.UIUtils
import com.immomo.mmutil.task.MomoMainThreadExecutor
import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.momo.R
import com.immomo.momo.gift.GiftCategoryConstants
import com.immomo.momo.gift.bean.CommonGetGiftResult
import com.immomo.momo.gift.bean.CommonGetGiftV1Result
import com.immomo.momo.gift.net.GetGiftListTask
import com.immomo.momo.message.bean.GreetGiftCardData
import com.immomo.momo.message.bean.GreetGiftData
import com.immomo.momo.mvp.message.GiftDataProvider
import com.immomo.momo.voicechat.widget.SimplePageIndicator

/**
 * <AUTHOR>
 * @data 2020-08-25.
 */

open class GreetPugGiftCardView : GreetPugCardView {

    private var mPugTextView: TextView? = null
    private var viewPager: MomoViewPager? = null
    private var pageIndicator: SimplePageIndicator? = null
    private var giftDatas: MutableList<GreetGiftData>? = null
    private var clicked = false


    constructor(context: Context) : super(context)

    constructor(context: Context, attributeSet: AttributeSet) : super(context, attributeSet)

    constructor(context: Context, attributeSet: AttributeSet, defStyleAttr: Int) : super(
            context,
            attributeSet,
            defStyleAttr
    )

    override fun createView() {
        LayoutInflater.from(context).inflate(R.layout.layout_greet_gift_card, this, true)
        mPugTextView = findViewById(R.id.tv_greet_gift_card_pug)
        viewPager = findViewById(R.id.tv_greet_gift_card_viewpager)
        pageIndicator = findViewById(R.id.tv_greet_gift_card_indicator)
    }

    override fun refresh() {
        (data as? GreetGiftCardData)?.let {
            mPugTextView?.text = it.pugText
            giftDatas = it.giftDatas
            getGiftList()
            visibility = View.VISIBLE
            return
        }
        visibility = View.GONE
    }


    private fun initViewPager(giftDatas: MutableList<GreetGiftData>) {
        if (giftDatas.isEmpty()) {
            viewPager?.visibility = View.GONE
            pageIndicator?.visibility = View.GONE
            return
        }
        viewPager?.visibility = View.VISIBLE
        pageIndicator?.visibility = View.VISIBLE
        viewPager?.setScrollHorizontalEnabled(true)
        viewPager?.adapter = object : PagerAdapter() {
            override fun getCount(): Int {
                return giftDatas.size
            }

            override fun isViewFromObject(view: View, viewObject: Any): Boolean {
                return view === viewObject
            }

            override fun instantiateItem(container: ViewGroup, position: Int): Any {
                val rootView = LayoutInflater.from(context).inflate(R.layout.layout_greet_gift_card_gift, null)
                val textView = rootView.findViewById<TextView>(R.id.iv_greet_gift_card_gift_msg)
                textView.text = giftDatas[position].text
                val imageView = rootView.findViewById<ImageView>(R.id.iv_greet_gift_card_gift_icon)
                giftDatas[position].baseGift?.let {
                    ImageLoader.load(it.img)
                            .imageType(ImageType.URL)
                            .cornerRadius(UIUtils.getPixels(3f))
                            .into(imageView)
                }
                textView.setOnClickListener {
                    if (!clicked) {
                        clicked = true
                        cardClickListener?.onCardClick(giftDatas[position].baseGift)
                        MomoMainThreadExecutor.postDelayed(getTaskTag(), {
                            clicked = false
                        }, 2000L)
                    }
                }
                container.addView(rootView)
                return rootView
            }

            override fun destroyItem(container: ViewGroup, position: Int, viewObject: Any) {
                container.removeView(viewObject as? View)
            }
        }
        viewPager?.clearOnPageChangeListeners()
        viewPager?.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                //
            }

            override fun onPageSelected(position: Int) {
                pageIndicator?.setCurrentPage(position, giftDatas.size)
            }

            override fun onPageScrollStateChanged(state: Int) {
                //
            }
        })

        pageIndicator?.setCurrentPage(0, giftDatas.size)
        pageIndicator?.setPageColor(UIUtils.getColor(R.color.whitewith40tran))
        pageIndicator?.setSelectedColor(UIUtils.getColor(R.color.white))
        if (giftDatas.size > 1) {
            pageIndicator?.visibility = View.VISIBLE
        }
    }


    private fun getGiftList() {
        if (canShowGiftPanelByCache()) {
            val result = GiftDataProvider.getInstance().getCache(GiftCategoryConstants.GREET)
            onGetGiftList(result)
        } else {
            getGiftFromNet()
        }
    }

    private fun getGiftFromNet() {
        MomoTaskExecutor.executeUserTask(getTaskTag(), GetGiftListTask(GiftCategoryConstants.GREET, remoteId, true, object : GetGiftListTask.GetGiftListener {

            override fun onGetGiftListSuccess(task: GetGiftListTask, result: CommonGetGiftResult?) {
                GiftDataProvider.getInstance().addCache(GiftCategoryConstants.GREET, result)
                onGetGiftList(result)
            }

            override fun onGetGiftListFail(task: GetGiftListTask, e: Exception) {
                //
            }
        }))
    }


    private fun onGetGiftList(result: CommonGetGiftResult?) {
        (result as? CommonGetGiftV1Result)?.let {
            it.gifts?.let { baseGiftList ->
                giftDatas?.let { greetDataList ->
                    val list = mutableListOf<GreetGiftData>()
                    for (greetGiftData in greetDataList) {
                        for (baseGift in baseGiftList) {
                            if (greetGiftData.id == baseGift.id) {
                                greetGiftData.baseGift = baseGift
                                list.add(greetGiftData)
                                break
                            }
                        }
                    }
                    initViewPager(list)
                }
            }
        }

    }


    private fun canShowGiftPanelByCache(): Boolean {
        val result = GiftDataProvider.getInstance().getCache(GiftCategoryConstants.GREET)
        return result != null && result is CommonGetGiftV1Result && result.gifts != null && !checkTime(result.cacheTtl)
    }

    /**
     * 为true代表需要刷新
     *
     * @return
     */
    private fun checkTime(cacheTtl: Int): Boolean {
        val lastGiftListUpdateTime = KV.getUserLong(
                com.immomo.android.router.momo.SPKeys.User.Gift.KEY_LAST_GET_GIFT_LIST_TIME + GiftCategoryConstants.GREET, 0L)
        return lastGiftListUpdateTime == 0L || Math.abs(System.currentTimeMillis() - lastGiftListUpdateTime) > cacheTtl * 1000
    }

    override fun onDetachedFromWindow() {
        MomoMainThreadExecutor.cancelAllRunnables(getTaskTag())
        super.onDetachedFromWindow()
    }

    private fun getTaskTag(): String {
        return this.javaClass.name + '@'.toString() + Integer.toHexString(this.hashCode())
    }

}
