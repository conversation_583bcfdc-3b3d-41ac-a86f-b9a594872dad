package com.immomo.momo.message.sayhi.itemmodel.newhi.sub

import android.animation.ObjectAnimator
import android.graphics.Rect
import android.view.View
import android.view.animation.AnimationUtils
import android.view.animation.DecelerateInterpolator
import android.widget.ImageView
import android.widget.TextView
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import com.cosmos.mdlog.MDLog
import com.immomo.android.module.fundamental.Badge.parseUniformLabelTheme2Model
import com.immomo.android.module.fundamental.Badge.toDisplay
import com.immomo.framework.cement.CementAdapter
import com.immomo.framework.cement.CementViewHolder
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.kotlin.ImageType
import com.immomo.mmutil.task.MomoMainThreadExecutor
import com.immomo.momo.MomoKit
import com.immomo.momo.R
import com.immomo.momo.message.NewSayUIConfigV1
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiRecommendSubCardModel
import com.immomo.momo.message.sayhi.itemmodel.newhi.BaseNewSayHiItemModel
import com.immomo.momo.message.sayhi.utils.NewSayHiConst
import com.immomo.momo.util.view.BadgeView
import com.immomo.svgaplayer.SVGAAnimListenerAdapter
import com.immomo.svgaplayer.view.MomoSVGAImageView
import com.mln.watcher.safe

/**
 * 招呼折叠聚合位
 */
class NewSayHiTinyCardItemModel(
    val model: SayHiRecommendSubCardModel,
    private var onFirstAniFished: ((view: View, closeAni:Boolean) -> Unit)? = null
) : BaseNewSayHiItemModel<NewSayHiTinyCardItemModel.ViewHolder>() {

    private val scaleAnim by lazy {
        AnimationUtils.loadAnimation(MomoKit.getContext(), R.anim.ls_anim_sayhi_unread_red_scale_in)
    }

    private val mViewVisible = Rect()

    init {
        id(hashCode())
    }

    override fun bindData(holder: ViewHolder) {
        super.bindData(holder)
        this.viewHolder = holder
        holder.svgaNewHiProgress.stopAnimCompletely()
        val cardStackCardInfo = model.cardStackCardInfo
        val showFirstLoad =
            cardStackCardInfo?.cardShowType == NewSayHiConst.CARD_TYPE_FIRST_LOAD    // 首次加载动画卡片
        val showNewInsert =
            cardStackCardInfo?.cardShowType == NewSayHiConst.CARD_TYPE_NEW_INSERT    // 新卡插入
        val showFinishInsert =
            cardStackCardInfo?.cardShowType == NewSayHiConst.CARD_TYPE_FINISH_INSERT    // 新卡插入已经播放完成动画
        MDLog.i(
            "NewSayHiTinyCardItemModel",
            "${cardStackCardInfo?.cardShowType}     momoid${cardStackCardInfo?.sayHiInfo?.momoid}"
        )
        cardStackCardInfo?.sayHiNetInfo?.also {
            val closeAni = NewSayUIConfigV1.closeAni()
            if (showFirstLoad && !closeAni) {
                holder.itemView.visibility = View.GONE
            } else {
                holder.itemView.visibility = View.VISIBLE
            }
            val greetCardPhotos = it.greetCardImgs
            if (greetCardPhotos != null && greetCardPhotos.size > 0) {
                val small = greetCardPhotos.first().small
                ImageLoader.load(small)
                    .imageType(ImageType.URL)
                    .cornerRadius(NewSayHiConst.ITEM_CARD_CORNER)
                    .placeholder(R.drawable.bg_10dp_round_corner_f9f9f9_262626)
                    .into(holder.icon)
            }
            holder.tvName.text = it.user.name.safe()
            holder.tvImprint.toDisplay(
                it.smallUniformLabels.parseUniformLabelTheme2Model(), holder.tvImprint
            )
            holder.newHiRed.visibility = View.GONE
            holder.bgNewHi.visibility = View.GONE
            if (showFirstLoad) {
                if (closeAni) {
                    onFirstAniFished?.invoke(holder.itemView, true)
                } else {
                    holder.startGuidAnimation(onFirstAniFished)
                }
            } else if (showFinishInsert) { // 插入新招呼新动画完成
                holder.newHiRed.visibility = View.VISIBLE
            } else if (showNewInsert) { // 插入新招呼
                holder.svgaNewHiProgress.visibility = View.VISIBLE
                holder.svgaNewHiProgress.startSVGAAnimWithListener(
                    "saihi_new_tiny_card_bg.svga", 2, object : SVGAAnimListenerAdapter() {
                        override fun onFinished() {
                            super.onFinished()
                            holder.svgaNewHiProgress.visibility = View.GONE
                        }
                    })
                holder.newHiRed.visibility = View.VISIBLE
                holder.newHiRed.startAnimation(scaleAnim)
            }
        }
        cardStackCardInfo?.also {
            it.cardShowType = if (showNewInsert) { // 如果是进插入卡片标记为已经播放完成插入动画
                NewSayHiConst.CARD_TYPE_FINISH_INSERT
            } else if (showFinishInsert) {
                it.cardShowType
            } else NewSayHiConst.CARD_TYPE_TYPE_NORMAL
        }
    }

    override fun detachedFromWindow(holder: ViewHolder) {
        super.detachedFromWindow(holder)
        kotlin.runCatching {
            MomoMainThreadExecutor.cancelAllRunnables(hashCode())
            onFirstAniFished = null
            holder.svgaNewHiProgress.stopAnimCompletely()
            holder.animatorRightIn?.cancel()
        }
    }

    override fun getLayoutRes(): Int = R.layout.new_sayhi_recommend_sub_card_sessionlist

    override fun getViewHolderCreator() = CementAdapter.IViewHolderCreator { view ->
        ViewHolder(view)
    }

    class ViewHolder(itemView: View) : CementViewHolder(itemView) {
        private val cardContainer: View by lazy { itemView.findViewById<View>(R.id.card_container) }
        val bgNewHi: View by lazy { itemView.findViewById<View>(R.id.bg_new_hi) }
        val svgaNewHiProgress by lazy { itemView.findViewById<View>(R.id.svga_new_hi_progress) as MomoSVGAImageView }
        val icon by lazy { itemView.findViewById<View>(R.id.icon) as ImageView }
        val newHiRed by lazy { itemView.findViewById<View>(R.id.new_hi_red) as ImageView }
        val tvName by lazy { itemView.findViewById<View>(R.id.tv_name) as TextView }
        val tvImprint by lazy { itemView.findViewById<View>(R.id.tv_imprint) as BadgeView }
        var animatorRightIn: ObjectAnimator? = null

        init {
            if (NewSayHiConst.cardHeight > 0 && NewSayHiConst.cardWidth > 0) {
                val params = cardContainer.layoutParams
                params.width = NewSayHiConst.cardWidth
                params.height = NewSayHiConst.cardHeight
                cardContainer.layoutParams = params
            }
        }

        fun startGuidAnimation(onAnimationEnd: ((view: View, closeAni: Boolean) -> Unit)? = null) {
            MDLog.i("startGuidAnimation", "getAdapterPosition=${layoutPosition}")
            animatorRightIn = ObjectAnimator.ofFloat(
                itemView, "translationX", NewSayHiConst.screenWidth.toFloat(), 0f
            ).apply {
                interpolator = DecelerateInterpolator()
                duration = 500
                // 动画集合
                startDelay = layoutPosition * 50L
                doOnStart {
                    itemView.visibility = View.VISIBLE
                }
                doOnEnd {
                    if (NewSayHiConst.isCardPageShowing) {
                        return@doOnEnd
                    }
                    MomoMainThreadExecutor.postDelayed(hashCode(), {
                        onAnimationEnd?.invoke(itemView, false)
                    }, 200)
                }
                start()
            }
        }

    }

}