package com.immomo.momo.message.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import com.immomo.momo.R
import com.immomo.momo.message.bean.GreetImageCardData

/**
 * <AUTHOR>
 * @data 2020-08-25.
 */

class GreetPugImageCardView : GreetPugCardView {

    private var mPugTextView: TextView? = null
    private var mImageContainer: FrameLayout? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attributeSet: AttributeSet) : super(context, attributeSet)

    constructor(context: Context, attributeSet: AttributeSet, defStyleAttr: Int) : super(
            context,
            attributeSet,
            defStyleAttr
    )

    override fun createView() {
        LayoutInflater.from(context).inflate(R.layout.layout_greet_image_card, this, true)
        mPugTextView = findViewById(R.id.tv_greet_image_card_pug)
        mImageContainer = findViewById(R.id.greet_card_image_container)
    }

    override fun refresh() {
        (data as? GreetImageCardData)?.let {
            mPugTextView?.text = it.pugText
            addImageView(it)
            visibility = View.VISIBLE
            return
        }
        visibility = View.GONE
    }

    private fun addImageView(greetImageCardData: GreetImageCardData) {
        mImageContainer?.removeAllViews()
        greetImageCardData.images?.let {
            when (it.size) {
                0 -> {
                    //不添加
                }
                1 -> {
                    val imageView = GreetOneImageView(context)
                    imageView.setImageUrl(it)
                    mImageContainer?.addView(imageView)
                }
                2 -> {
                    val imageView = GreetTwoImageView(context)
                    imageView.setImageUrl(it)
                    mImageContainer?.addView(imageView)
                }
                3 -> {
                    val imageView = GreetThreeImageView(context)
                    imageView.setImageUrl(it)
                    mImageContainer?.addView(imageView)
                }
                else -> {
                    val imageView = GreetThreeImageView(context)
                    imageView.setImageUrl(it)
                    mImageContainer?.addView(imageView)
                }
            }
        }


    }


}
