package com.immomo.momo.message.sayhi.utils

import android.app.Activity
import android.view.ViewGroup
import android.widget.FrameLayout
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiRedPacket
import com.immomo.momo.message.sayhi.widget.SayhiRedPacketNoticeView
import java.util.concurrent.LinkedBlockingQueue


object SayHiMaleRedPacketHelper {

    fun showPacketNoticeView(activity: Activity, sayHiRedPacket: SayHiRedPacket?) {
        if (sayHiRedPacket == null) {
            return
        }
        val decorView = activity.window.decorView as FrameLayout
        addView(decorView, activity, sayHiRedPacket)
    }

    fun addView(vg: ViewGroup, activity: Activity, sayHiRedPacket: SayHiRedPacket) {
        val targetView = SayhiRedPacketNoticeView(activity, sayHiRedPacket, { view ->
            (view as? SayhiRedPacketNoticeView)?.hide {
                var parent = view.parent as? ViewGroup
                parent?.removeView(view)
            }
        })
        vg.addView(targetView)

        targetView.startShowAnim()
    }

}