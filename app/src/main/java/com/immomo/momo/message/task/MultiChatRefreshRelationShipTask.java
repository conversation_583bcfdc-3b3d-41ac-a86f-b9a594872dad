package com.immomo.momo.message.task;

import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.discuss.bean.Discuss;
import com.immomo.momo.discuss.service.DiscussService;
import com.immomo.momo.message.activity.MultiChatActivity;

import java.lang.ref.WeakReference;

public class MultiChatRefreshRelationShipTask extends MomoTaskExecutor.Task<Object, Object, Void> {

    private final WeakReference<MultiChatActivity> mActivityRef;
    private String curMomoId;
    private String curDisId;
    private int discussStatus;

    private boolean bothRelation = false;

    public MultiChatRefreshRelationShipTask(
            MultiChatActivity mActivity,
            String curMomoId,
            String curDisId,
            int status) {
        this.mActivityRef = new WeakReference<>(mActivity);
        this.curMomoId = curMomoId;
        this.curDisId = curDisId;
        this.discussStatus = status;
    }

    @Override
    protected Void executeTask(Object... objects) throws Exception {
        // 判断当前用户是否属于该讨论组
        bothRelation = DiscussService.getInstance().isDiscussMember(curMomoId, curDisId)
                && discussStatus != Discuss.STATUS_BANDED;
        return null;
    }

    @Override
    protected void onTaskSuccess(Void aVoid) {
        if (mActivityRef != null && mActivityRef.get() != null) {
            mActivityRef.get().setBothRelation(bothRelation);
            mActivityRef.get().updateRelationShip();
        }
    }
}
