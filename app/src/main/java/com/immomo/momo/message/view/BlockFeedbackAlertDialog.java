package com.immomo.momo.message.view;

import android.app.Dialog;
import android.content.Context;
import android.view.View;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import com.immomo.framework.imageloader.ImageType;
import com.immomo.framework.imageloader.ImageLoaderX;
import com.immomo.momo.R;

/**
 * Created by huang.liangjie on 2018/10/11.
 * <p>
 * Momo Tech 2011-2018 © All Rights Reserved.
 */
public class BlockFeedbackAlertDialog extends Dialog {
    private TextView confirmBtn;
    private TextView titleTv, contentTv;
    private ImageView avatarImg;

    public BlockFeedbackAlertDialog(Context context) {
        super(context, R.style.AlertDialogStyle_Round15);
        requestWindowFeature(Window.FEATURE_NO_TITLE);


        setCancelable(true);
        setCanceledOnTouchOutside(true);

        setContentView(R.layout.layout_harass_feedback);

        confirmBtn = findViewById(R.id.tv_btn_confirm);
        confirmBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

    }

    public void updateData(String avatar, String title, String text) {
        titleTv = findViewById(R.id.citycard_title);
        titleTv.setText(title);

        contentTv = findViewById(R.id.citycard_content);
        contentTv.setText(text);

        avatarImg = findViewById(R.id.iv_avatar);
        ImageLoaderX.load(avatar).type(ImageType.IMAGE_TYPE_ALBUM_250x250).showDefault().into(avatarImg);
    }
}
