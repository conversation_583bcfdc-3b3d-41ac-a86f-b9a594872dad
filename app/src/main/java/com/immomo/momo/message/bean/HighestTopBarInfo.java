package com.immomo.momo.message.bean;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * 聊天打招呼顶部bar的数据
 */
public class HighestTopBarInfo {

    @Expose
    @SerializedName("id")
    private String id;
    @Expose
    @SerializedName("icon")
    private String icon;
    @Expose
    @SerializedName("tips")
    private String tips;
    @Expose
    @SerializedName("bg_color")
    private String bgColor;
    @Expose
    @SerializedName("text_color")
    private String textColor;

    @Expose
    @SerializedName("action")
    private String gotoStr;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }

    public String getBgColor() {
        return bgColor;
    }

    public void setBgColor(String bgColor) {
        this.bgColor = bgColor;
    }

    public String getTextColor() {
        return textColor;
    }

    public void setTextColor(String textColor) {
        this.textColor = textColor;
    }

    public String getGotoStr() {
        return gotoStr;
    }

    public void setGotoStr(String gotoStr) {
        this.gotoStr = gotoStr;
    }
}
