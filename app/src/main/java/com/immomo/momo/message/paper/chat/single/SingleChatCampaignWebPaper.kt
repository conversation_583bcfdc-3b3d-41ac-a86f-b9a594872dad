package com.immomo.momo.message.paper.chat.single

import android.graphics.Color
import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.widget.FrameLayout
import android.widget.RelativeLayout
import com.immomo.framework.base.BaseActivity
import com.immomo.mmutil.StringUtils
import com.immomo.momo.R
import com.immomo.momo.globalevent.GlobalEventManager
import com.immomo.momo.mk.MomoMKWebViewHelper
import com.immomo.momo.mk.bridges.GlobalEventBridge
import com.immomo.momo.performance.SimpleViewStubProxy
import immomo.com.mklibrary.core.base.ui.MKWebView
import immomo.com.mklibrary.core.ui.SetUIBtnParams
import immomo.com.mklibrary.core.ui.SetUIParams

/**
 *
 * author: hongming.wei
 * data: 2024/11/21
 */
class SingleChatCampaignWebPaper(private val mActivity: BaseActivity?, private val roomView: View?) {

    private var webViewStubProxy: SimpleViewStubProxy<FrameLayout>? = null
    private var mWebContainer: FrameLayout? = null
    private var mWebRelative: RelativeLayout? = null
    private var mkWebViewHelper: MomoMKWebViewHelper? = null
    private var mKWebView: MKWebView? = null
    private var mCurrentUrl: String? = null

    companion object {
        const val SOURCE_TEXT_CHAT = "textChat"
        const val SOURCE_BOX_MESSAGE = "boxMessage"
    }

    fun initWebViewStub(jumpUrl: String) {
        val viewStub: View? = roomView?.findViewById(R.id.viewstub_campaign_webview_container)
        if (viewStub != null) {
            webViewStubProxy = SimpleViewStubProxy(viewStub as? ViewStub)
        }
        if (webViewStubProxy != null) {
            webViewStubProxy?.visibility = View.VISIBLE
            mWebRelative = webViewStubProxy?.getView(R.id.chat_campaign_web_layout) as? RelativeLayout
            mWebContainer = webViewStubProxy?.getView(R.id.chat_campaign_web_container) as? FrameLayout
            mWebContainer?.let {
                mCurrentUrl = jumpUrl
                initWebView(it, jumpUrl)
            }
        }
    }


    private fun initWebView(parent: FrameLayout, jumpUrl: String) {
        mActivity?.let {
            parent.removeAllViews()
            mKWebView = MKWebView(it)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                mKWebView?.setLayerType(View.LAYER_TYPE_HARDWARE, null)
            }
            mKWebView?.setBackgroundColor(Color.TRANSPARENT)
            parent.addView(mKWebView, ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT))
            mkWebViewHelper = object : MomoMKWebViewHelper() {
                override fun clearRightButton() {}
                override fun closePage() {}
                override fun uiGoBack() {}
                override fun uiSetTitle(title: String) {}
                override fun uiShowHeaderBar(show: Boolean) {}
                override fun uiSetUI(uiParams: SetUIParams) {}
                override fun uiSetUIButton(params: SetUIBtnParams) {}
                override fun uiClosePopup() {
                    destroyWebView()
                }
            }
            mkWebViewHelper?.bindActivity(it, mKWebView)
            mkWebViewHelper?.initMomoWebView()
            mkWebViewHelper?.registerHighPriorityBridge(GlobalEventBridge(mKWebView))
            mKWebView?.loadUrl(jumpUrl)
        }
    }

    private fun destroyWebView() {
        webViewStubProxy?.visibility = View.GONE
        mWebRelative?.visibility = View.GONE
        mkWebViewHelper?.onPageDestroy()
        mkWebViewHelper = null
        mKWebView = null
        mCurrentUrl = null
    }

    fun isDifferentUrl(jumpUrl: String): Boolean {
        return !StringUtils.equalsNonNull(mCurrentUrl, jumpUrl)
    }

    fun isWebViewEmpty(): Boolean {
        return mKWebView == null
    }

    fun loadNewUrl(jumpUrl: String) {
        mKWebView?.loadUrl(jumpUrl)
    }


    fun onDestroy() {
        destroyWebView()
    }

    fun showWebView(isShow: Boolean) {
        mWebRelative?.visibility = if (isShow) View.VISIBLE else View.GONE
    }


    fun sendWebInformRefreshEvent(source: String) {
        GlobalEventManager
            .getInstance()
            .sendEvent(GlobalEventManager
                .Event("refreshTasks")
                .src("native")
                .dst("mk")
                .msg(mapOf("source" to source)))
    }
}