package com.immomo.momo.message.view

import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.view.animation.LinearInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.R
import com.immomo.momo.android.view.FixAspectRatioFrameLayout
import com.immomo.momo.util.SimpleAnimationListener
import java.util.*
import kotlin.concurrent.schedule

class TimelyTouchView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    private var schedule: TimerTask? = null
    var isLongPress = false
    var onLongPressListen: OnLongPressListen? = null
    var downTime = 0L
    private var rawY = 0f

    private val DEFAULT_SIZE = UIUtils.getPixels(46f)
    private val RECORD_SIZE = UIUtils.getPixels(100f)
    private val DEFAULT_RADIU = UIUtils.getPixels(12f)
    private val RECORD_RADIU = UIUtils.getPixels(49f)
    private val parentView: FixAspectRatioFrameLayout
    private val bgView: View
    private val defaultView: ImageView
    private val recrodView: View
    private var btnAnim: ValueAnimator? = null
    private var dismissAnim: AlphaAnimation? = null
    private val MOVE_MAX = UIUtils.getPixels(200f)

    init {
        bgView = View(context).apply {
            background = UIUtils.getDrawable(R.color.color_ffffff)
        }
        defaultView = ImageView(context).apply {
            setImageResource(R.drawable.ic_timely_defaultbtn)
        }

        recrodView = View(context).apply {
            background = UIUtils.getDrawable(R.drawable.bg_5dp_corner_f46161)
        }

        parentView = FixAspectRatioFrameLayout(context).apply {
            addView(
                bgView, LayoutParams(
                    LayoutParams.MATCH_PARENT,
                    LayoutParams.MATCH_PARENT
                )
            )

            addView(defaultView, LayoutParams(
                UIUtils.getPixels(23f),
                UIUtils.getPixels(23f)
            ).apply {
                gravity = Gravity.CENTER
            })
            addView(recrodView, LayoutParams(
                UIUtils.getPixels(20f),
                UIUtils.getPixels(20f)
            ).apply {
                gravity = Gravity.CENTER
            })
        }
        addView(parentView, LayoutParams(
            LayoutParams.WRAP_CONTENT,
            LayoutParams.WRAP_CONTENT
        ).apply {
            gravity = Gravity.CENTER
        })

        setDefaultView()
    }

    fun setDefaultView() {
        parentView.layoutParams.let {
            it.width = DEFAULT_SIZE
            it.height = DEFAULT_SIZE
            parentView.layoutParams = it
        }
        defaultView.alpha = 1f
        recrodView.alpha = 0f
        bgView.alpha = 1f
        parentView.setRadius(DEFAULT_RADIU)
        parentView.alpha = 1f
        parentView.visibility = View.VISIBLE
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> {
                downTime = System.currentTimeMillis()
                rawY = event.rawY
                onLongPressListen?.onLongPress()
                schedule = Timer().schedule(1000L) {
                    isLongPress = true
                    post {
                        changeStateAnim()
                    }
                }
            }
            MotionEvent.ACTION_MOVE -> {
                var percent = (rawY - event.rawY) / MOVE_MAX
                if (percent < 0) {
                    percent = 0f
                } else if (percent > 1) {
                    percent = 1f
                }
                onLongPressListen?.onMove(percent)
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                downTime = 0
                onLongPressListen?.onCancelPress(isLongPress)
                rawY = 0f
                isLongPress = false
                schedule?.cancel()
                schedule = null
            }
        }
        return true
    }

    fun doDismissAnim() {
        btnAnim?.cancel()
        dismissAnim = AlphaAnimation(1f, 0f).apply {
            duration = 150
            interpolator = LinearInterpolator()
            setAnimationListener(object : SimpleAnimationListener() {
                override fun onAnimationEnd(animation: Animation?) {
                    super.onAnimationEnd(animation)
                    visibility = View.GONE
                }
            })
            parentView.startAnimation(this)
        }
    }

    private fun changeStateAnim() {
        btnAnim = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = 300
            interpolator = LinearInterpolator()
            addUpdateListener {
                val value = it.animatedValue as Float

                parentView.layoutParams.let {
                    val size = DEFAULT_SIZE + (RECORD_SIZE - DEFAULT_SIZE) * value
                    it.width = size.toInt()
                    it.height = size.toInt()
                    parentView.layoutParams = it
                }
                parentView.setRadius((DEFAULT_RADIU + (RECORD_RADIU - DEFAULT_RADIU) * value).toInt())
                bgView.alpha = 1 - 0.2f * value
                defaultView.alpha = 1 - value
                recrodView.alpha = value

            }
            start()
        }
    }

    interface OnLongPressListen {
        fun onLongPress()
        fun onCancelPress(isLongPress: Boolean)
        fun onMove(percent: Float)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        schedule?.cancel()
        btnAnim?.cancel()
        dismissAnim?.cancel()
    }
}