package com.immomo.momo.message.task;

import android.view.View;
import android.widget.TextView;

import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.message.activity.GroupChatActivity;
import com.immomo.momo.message.contract.IMsgChatDataHolder;
import com.immomo.momo.message.contract.IMsgChatRecycler;
import com.immomo.momo.message.helper.MgsMessageHelper;
import com.immomo.momo.service.bean.Message;

import java.util.List;

import static com.immomo.momo.mvp.message.view.BaseMessageActivity.PAGE_SIZE;


/**
 * 加载更多的异步任务
 * <AUTHOR>
 * date 2020/8/8
 */
public class LoadMoreTask extends MomoTaskExecutor.Task<Object, Object, List<Message>> {

    private GroupChatActivity mActivity;
    private IMsgChatRecycler mMsgChatRecycler;
    private IMsgChatDataHolder mMsgChatData;
    private MgsMessageHelper mMgsMessageHelper;
    private TextView unreadTipView;
    private String traceId;
    /**
     * 加载更多页数
     */
    private int loadNums;


    public LoadMoreTask(GroupChatActivity mActivity, IMsgChatRecycler msgChatRecycler,
                        IMsgChatDataHolder msgChatData, MgsMessageHelper mMgsMessageHelper,
                        TextView unreadTipView, Object... objects) {
        super(objects);
        this.mActivity = mActivity;
        this.mMsgChatRecycler = msgChatRecycler;
        this.mMsgChatData = msgChatData;
        this.mMgsMessageHelper = mMgsMessageHelper;
        this.unreadTipView = unreadTipView;
    }

    @Override
    protected void onPreTask() {
    }

    @Override
    protected List<Message> executeTask(Object... params) throws Exception {

        loadNums++;
        long beginTime = System.nanoTime();
        List<Message> messages = mActivity.loadMoreMessages(PAGE_SIZE + 1, false, false);
        mActivity.mIMLogRecorder.logLoadMoreMessages(messages);
        //单位变成毫秒
        long executeTime = (System.nanoTime() - beginTime) / 1000000;
        if (executeTime > 0 && executeTime < 200) {
            Thread.sleep(200 - executeTime);
        }

        return messages;
    }

    @Override
    protected void onTaskSuccess(List<Message> messages) {

        if (!mActivity.hasMoreMessage) {
            mMsgChatRecycler.removeOverScroll();
        } else {
            mMsgChatRecycler.restoreOverScroll();
        }
        mMsgChatRecycler.setRefreshing(false);
        if (mMgsMessageHelper != null) {
            mMgsMessageHelper.onPullToRefreshSuccess(mMsgChatRecycler);
        }
        if (messages.size() <= 0) {
            return;
        }
        //没有未读或者用户自己通过滚动阅读了一页，就把未读消息提示隐藏
        if ((mActivity.getUnReadScrollCount() <= 0 || loadNums == 1) &&
                unreadTipView.getVisibility() == View.VISIBLE) {
            unreadTipView.setVisibility(View.GONE);
        }
        mActivity.msgChatData.moreMessageComplete(messages);
        mMsgChatRecycler.tryEndInflateInChain(traceId);
    }

    @Override
    protected void onTaskError(Exception e) {
        super.onTaskError(e);
    }
}
