package com.immomo.momo.message.sayhi.itemmodel.newhi.sub

import android.view.View
import com.immomo.framework.cement.CementAdapter
import com.immomo.framework.cement.CementViewHolder
import com.immomo.momo.R
import com.immomo.momo.message.sayhi.itemmodel.newhi.BaseNewSayHiItemModel
import com.immomo.momo.message.sayhi.utils.NewSayHiConst

/**
 * 招呼折叠聚合位占位
 */
class NewSayHiTinyCardEmptyItemModel :
    BaseNewSayHiItemModel<NewSayHiTinyCardEmptyItemModel.ViewHolder>() {

    init {
        id(hashCode())
    }

    override fun bindData(holder: ViewHolder) {
        super.bindData(holder)
        this.viewHolder = holder
    }

    override fun getLayoutRes(): Int = R.layout.new_sayhi_recommend_sub_card_empty_sessionlist

    override fun getViewHolderCreator() = CementAdapter.IViewHolderCreator { view ->
        ViewHolder(view)
    }

    class ViewHolder(itemView: View) : CementViewHolder(itemView) {

        private val cardContainer: View by lazy { itemView.findViewById<View>(R.id.card_container) }

        init {
            if (NewSayHiConst.cardHeight > 0 && NewSayHiConst.cardWidth > 0) {
                val params = cardContainer.layoutParams
                params.width = NewSayHiConst.cardWidth
                params.height = NewSayHiConst.cardHeight
                cardContainer.layoutParams = params
            }
        }

    }

}