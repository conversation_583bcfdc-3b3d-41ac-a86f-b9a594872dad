package com.immomo.momo.message.paper.chat.flash

import android.view.View
import com.immomo.momo.R
import com.immomo.momo.message.paper.BasePaperContainerFragment
import com.immomo.momo.message.paper.PaperConfig
import com.immomo.momo.message.paper.common.ChatAudioCoverPaperFragment
import com.immomo.momo.message.paper.common.GiftEffectPaperFragment

/**
 * <AUTHOR>
 * @data 2020-12-18.
 */

class FlashChatTopPaperFragment : BasePaperContainerFragment() {

    companion object {
        @JvmStatic
        fun newInstance(): FlashChatTopPaperFragment {
            return FlashChatTopPaperFragment()
        }
    }

    override fun getPagers(): MutableList<PaperConfig>? =
        mutableListOf(
            PaperConfig(GiftEffectPaperFragment.newInstance()),
            PaperConfig(ChatAudioCoverPaperFragment.newInstance())
        )

    override fun getPageLayout(): Int = R.layout.paper_flash_chat_top

    override fun onPageLoad() {
    }

    override fun initPageViews(contentView: View?) {
    }

}