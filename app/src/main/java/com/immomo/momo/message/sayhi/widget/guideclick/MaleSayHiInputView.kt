package com.immomo.momo.message.sayhi.widget.guideclick

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import com.immomo.momo.R
import com.immomo.momo.message.sayhi.widget.SayHiInputView

/**
 * <AUTHOR>
 * @data 2020-08-25.
 */

class MaleSayHiInputView : SayHiInputView {

    constructor(context: Context) : super(context)

    constructor(context: Context, attributeSet: AttributeSet) : super(context, attributeSet)

    constructor(context: Context, attributeSet: AttributeSet, defStyleAttr: Int) : super(
            context,
            attributeSet,
            defStyleAttr
    )

    override fun createView() {
        LayoutInflater.from(context).inflate(R.layout.layout_male_say_hi_input, this, true)
        dislikeButton = findViewById(R.id.card_left_btn)
        llStartChat = findViewById(R.id.ll_start_chat)
        ivFlash = findViewById(R.id.iv_flash)
        rlFemaleInput = findViewById(R.id.rl_female_input)
        rlFemaleInput?.setOnClickListener {
            sayHiInputListener?.showCommentEdit()
        }
    }


}
