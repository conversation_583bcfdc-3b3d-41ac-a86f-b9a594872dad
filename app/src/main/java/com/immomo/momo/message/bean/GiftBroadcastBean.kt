package com.immomo.momo.message.bean

import android.os.Parcel
import android.os.Parcelable
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import com.immomo.momo.service.bean.message.Type15Content

class GiftBroadcastBean(
    @Expose
    @SerializedName("broadcastId")
    val broadcastId: String, // 广播 ID
    @Expose
    @SerializedName("giveUser")
    val giveUser: UserInfo, // 送礼用户信息
    @Expose
    @SerializedName("receiveUser")
    val receiveUser: UserInfo, // 接收礼物用户信息
    @Expose
    @SerializedName("giftIcon")
    val giftIcon: String, // 礼物图标链接
    @Expose
    @SerializedName("sendGiftGroupId")
    val sendGiftGroupId: String, // 送礼物的群组 ID
    @Expose
    @SerializedName("groupId")
    val groupId: String, // 卡片推向的群ID
    @Expose
    @SerializedName("cardLevel")
    val cardLevel: Int, // 卡片等级
    @Expose
    @SerializedName("cardDressup")
    val cardDressup: CardDressup, // 卡片装扮信息
    @Expose
    @SerializedName("giftMessage")
    var giftMessage: Type15Content, // 礼物消息，使用已有类型
    @Expose
    @SerializedName("time")
    var time: Long // 时间
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString() ?: "",
        parcel.readParcelable(UserInfo::class.java.classLoader) ?: UserInfo("", "", "", ""),
        parcel.readParcelable(UserInfo::class.java.classLoader) ?: UserInfo("", "", "", ""),
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readInt(),
        parcel.readParcelable(CardDressup::class.java.classLoader) ?: CardDressup(0, "", "", "", "", "", "", "", "", ""),
        parcel.readParcelable(Type15Content::class.java.classLoader) ?: Type15Content(),
        parcel.readLong()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(broadcastId)
        parcel.writeParcelable(giveUser, flags)
        parcel.writeParcelable(receiveUser, flags)
        parcel.writeString(giftIcon)
        parcel.writeString(sendGiftGroupId)
        parcel.writeString(groupId)
        parcel.writeInt(cardLevel)
        parcel.writeParcelable(cardDressup, flags)
        parcel.writeParcelable(giftMessage, flags)
        parcel.writeLong(time)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<GiftBroadcastBean> {
        override fun createFromParcel(parcel: Parcel): GiftBroadcastBean {
            return GiftBroadcastBean(parcel)
        }

        override fun newArray(size: Int): Array<GiftBroadcastBean?> {
            return arrayOfNulls(size)
        }
    }
}

// 用户信息类，实现 Parcelable 接口
class UserInfo(
    @Expose
    @SerializedName("momoId")
    val momoId: String, // 用户 ID
    @Expose
    @SerializedName("name")
    val name: String, // 用户名称
    @Expose
    @SerializedName("avatar")
    val avatar: String, // 用户头像链接
    @Expose
    @SerializedName("action")
    val action: String // 用户动作
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: ""
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(momoId)
        parcel.writeString(name)
        parcel.writeString(avatar)
        parcel.writeString(action)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<UserInfo> {
        override fun createFromParcel(parcel: Parcel): UserInfo {
            return UserInfo(parcel)
        }

        override fun newArray(size: Int): Array<UserInfo?> {
            return arrayOfNulls(size)
        }
    }
}

// 更新后的卡片装扮信息类，实现 Parcelable 接口
class CardDressup(
    @Expose
    @SerializedName("resourceType")
    val resourceType: Int, // 1 ®svga
    @Expose
    @SerializedName("currentResourceUrl")
    val currentResourceUrl: String, // 成员资源链接
    @Expose
    @SerializedName("otherResourceUrl")
    val otherResourceUrl: String, // 访客资源链接
    @Expose
    @SerializedName("btnTitle")
    val btnTitle: String, // 按钮标题
    @Expose
    @SerializedName("btnTitleColor")
    val btnTitleColor: String, // 按钮标题颜色
    @Expose
    @SerializedName("cardTitle")
    val cardTitle: String, // 卡片标题
    @Expose
    @SerializedName("cardTitleColor")
    val cardTitleColor: String, // 卡片标题颜色
    @Expose
    @SerializedName("cardDesc")
    val cardDesc: String, // 卡片描述
    @Expose
    @SerializedName("cardDescColor")
    val cardDescColor: String, // 卡片描述颜色
    @Expose
    @SerializedName("buttonAction")
    val buttonAction: String  // 按钮动作
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readInt(),
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: ""
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(resourceType)
        parcel.writeString(currentResourceUrl)
        parcel.writeString(otherResourceUrl)
        parcel.writeString(btnTitle)
        parcel.writeString(btnTitleColor)
        parcel.writeString(cardTitle)
        parcel.writeString(cardTitleColor)
        parcel.writeString(cardDesc)
        parcel.writeString(cardDescColor)
        parcel.writeString(buttonAction)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<CardDressup> {
        override fun createFromParcel(parcel: Parcel): CardDressup {
            return CardDressup(parcel)
        }

        override fun newArray(size: Int): Array<CardDressup?> {
            return arrayOfNulls(size)
        }
    }
}
