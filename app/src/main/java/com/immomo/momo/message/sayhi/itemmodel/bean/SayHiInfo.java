package com.immomo.momo.message.sayhi.itemmodel.bean;

import androidx.annotation.Keep;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.immomo.android.module.fundamental.Badge.UniformLabelsBean;
import com.immomo.android.module.nearbypeople.data.api.response.theme.AvatarTag;
import com.immomo.framework.common.UniqueHash;
import com.immomo.momo.message.bean.HighestTopBarInfo;
import com.immomo.momo.message.sayhi.SayHiArgs;
import com.immomo.momo.microvideo.model.WithUniqueIdentity;
import com.immomo.momo.router.ProfileRealAuth;
import com.immomo.momo.service.bean.Message;
import com.immomo.momo.service.bean.User;

import java.io.Serializable;
import java.util.List;


/**
 * Created by lei.jialin on 2019/4/8.
 */
public class SayHiInfo implements WithUniqueIdentity<SayHiInfo> {
    @Expose
    @SerializedName("name")
    private String name;

    @Expose
    @SerializedName("nameColor")
    private String nameColor;

    @Expose
    @SerializedName("marks")
    public List<String> marks;

    @Expose
    @SerializedName("markList")
    public List<SayHiUserMark> markList;    // 卡片使用，印记列表，第一条推荐印记，然后资料印记，以上都没有取在线距离

    @Expose
    @SerializedName("avatarTag")
    public AvatarTag avatarTag;

    @Expose
    @SerializedName("uniformLabels")
    public UniformLabelsBean uniformLabels;

    @Expose
    @SerializedName("is_mark_spray")
    public int isMarkSpray;
    @Expose
    @SerializedName("cellgoto")
    public String cellgoto;
    @Expose
    @SerializedName("logid")
    public String logid;

    @Expose
    @SerializedName("passby")
    public PassBy passby;

    @Expose
    @SerializedName("msgSource")
    public int msgSource; //67 擦肩而过

    /**
     * 显示在卡片页的个人提示文案
     */
    @Expose
    @SerializedName("like_msg")
    public String mLikeMsg;
    public User user;
    public List<Message> messages; //最新的、未读、招呼
    public List<Message> gifMessages;
    public String sourceText;
    //是否将禁言拉黑按钮替换更多按钮
    @Expose
    @SerializedName("forbidden_btn_show")
    private int forbiddenBtnShow;
    @Expose
    @SerializedName("forbidden_btn_content")
    private String forbiddenBtnContent;
    @Expose
    @SerializedName("relation")
    private String relation;
    @Expose
    @SerializedName("online_status_mark")
    private int onlineStatusMark;   // 在线

    @Expose
    @SerializedName("original_first_photo")
    private String originalFirstPhoto;

    @Expose
    @SerializedName("feedImageList")
    public List<SayHiPhotoInfo> feedImageList;//动态推荐图

    @Expose
    @SerializedName("greet_card_photos")
    private List<String> greetCardPhotos; // 服务器控制实验头像顺序

    @Expose
    @SerializedName("name_goto")
    private String nameGoto;

    @Expose
    @SerializedName("achievement_list")
    private List<Achievement> mAchievementList;

    private DetailOneSayhi.Response moreDetail;

    private Message topGiftMsg;

    public boolean hasSpamMessage;

    private String logMapString;

    @Expose
    @SerializedName("realAuth")
    public ProfileRealAuth realAuth;

    @Expose
    @SerializedName("photoUrl")
    public AvatarUrlInfo photoUrl;  // 头像地址（全链）

    @Expose
    @SerializedName("markDesc")
    private String markDesc = "";   // 招呼列表使用，印记展示，服务端兜底在线距离和星座

    @Expose
    @SerializedName("exposeStatus")
    public boolean exposeStatus;    // 曝光标记，true表示已经曝光，false表示未曝光(推荐数据透传)

    @Expose
    @SerializedName("highestTopInfo")
    public HighestTopBarInfo highestTopBarInfo;

    @Expose
    @SerializedName("showPoi")
    private String showPoi = "";   // 用户远距离poi

    public SayHiRedPacket maleUserRedPacket;

    @Expose
    @SerializedName("greetCardMark")
    private String greetCardMark = "";   // 招呼一句话印记，新增

    @Expose
    @SerializedName("greetCardImgs")
    private List<SayHiPhotoInfo> greetCardImgs; //动态推荐图

    @Expose
    @SerializedName("smallUniformLabels")
    private UniformLabelsBean smallUniformLabels;   // 只有性别年龄标签

    @Expose
    @SerializedName("greetCardMarkList")
    private List<SayHiUserMark> greetCardMarkList;

    public String cardSourceText; // 卡片的新来源

    private boolean isHeartbeat;   // 是否是心动招呼

    private boolean isFirstCard; // 是否是第一张卡片

    public String getOriginalFirstPhoto() {
        return originalFirstPhoto;
    }

    public void setOriginalFirstPhoto(String originalFirstPhoto) {
        this.originalFirstPhoto = originalFirstPhoto;
    }

    public void setMessages(List<Message> messages) {
        this.messages = messages;
    }

    public List<Message> getMessages() {
        return messages;
    }

    public void setMarks(List<String> marks) {
        this.marks = marks;
    }

    public List<String> getMarks() {
        return marks;
    }

    public String getMomoid() {
        return user != null ? user.momoid : "";
    }

    public String getCellgoto() {
        return cellgoto;
    }

    public String getAllMsgId() {
        StringBuilder normal = getAllMsgIdFrom(messages, 0);
        if (normal.length() > 0) {
            return normal.toString();
        }
        return "";
    }

    private StringBuilder getAllMsgIdFrom(List<Message> msg, int totalMsgCount) {
        StringBuilder sb = new StringBuilder();
        if (msg == null || msg.isEmpty() || totalMsgCount > SayHiArgs.getMaxLogMsgCount())
            return sb;
        int count = msg.size();
        sb.append(msg.get(0).msgId);
        for (int i = 1; i < count; i++) {
            if (i + totalMsgCount > SayHiArgs.getMaxLogMsgCount()) {
                break;
            }
            sb.append(",").append(msg.get(i).msgId);
        }
        return sb;
    }


    public void setCellgoto(String cellgoto) {
        this.cellgoto = cellgoto;
    }

    @Override
    public long uniqueId() {
        return UniqueHash.id(getMomoid());
    }

    @Override
    public Class<SayHiInfo> getClazz() {
        return SayHiInfo.class;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public User getUser() {
        return user;
    }

    public String getSourceText() {
        return sourceText;
    }

    public void setSourceText(String sourceText) {
        this.sourceText = sourceText;
    }

    public boolean hasGiftMessage() {
        return topGiftMsg != null;
    }

    public String getLogid() {
        return logid;
    }

    public void setLogid(String logid) {
        this.logid = logid;
    }

    public String getUserAvatar() {
        return getUser() != null ? getUser().getAvatar() : "";
    }

    public int getForbiddenBtnShow() {
        return forbiddenBtnShow;
    }

    public void setForbiddenBtnShow(int forbiddenBtnShow) {
        this.forbiddenBtnShow = forbiddenBtnShow;
    }

    public String getForbiddenBtnContent() {
        return forbiddenBtnContent;
    }

    public void setForbiddenBtnContent(String forbiddenBtnContent) {
        this.forbiddenBtnContent = forbiddenBtnContent;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public List<String> getGreetCardPhotos() {
        return greetCardPhotos;
    }

    public boolean isFeMale() {
        return user != null && user.isFemale();
    }

    public void setMoreDetail(DetailOneSayhi.Response moreDetail) {
        this.moreDetail = moreDetail;
    }

    public DetailOneSayhi.Response getMoreDetail() {
        return moreDetail;
    }

    public boolean isOnline() {
        return onlineStatusMark == 1;
    }

    public void setTopGiftMsg(Message topGiftMsg) {
        this.topGiftMsg = topGiftMsg;
    }

    public Message getTopGiftMsg() {
        return topGiftMsg;
    }

    public void setNameGoto(String nameGoto) {
        this.nameGoto = nameGoto;
    }

    public String getNameGoto() {
        return nameGoto;
    }

    public boolean hasMarks() {
        return marks != null && !marks.isEmpty();
    }

    public String getLogMapString() {
        return logMapString;
    }

    public void setLogMapString(String logMapString) {
        this.logMapString = logMapString;
    }

    public void setAchievementList(List<Achievement> achievementList) {
        mAchievementList = achievementList;
    }

    public List<Achievement> getAchievementList() {
        return mAchievementList;
    }

    public boolean isRedEnvelopeHi() {
        return getMoreDetail() != null && getMoreDetail().getIsRedEnvelopeHello() == 1;
    }

    public String getMarkDesc() {
        return markDesc;
    }

    public void setMarkDesc(String markDesc) {
        this.markDesc = markDesc;
    }

    public HighestTopBarInfo getChatHighestTopBarInfo() {
        return highestTopBarInfo;
    }

    public void setChatHighestTopBarInfo(HighestTopBarInfo highestTopBarInfo) {
        this.highestTopBarInfo = highestTopBarInfo;
    }

    public String getShowPoi() {
        return showPoi;
    }

    public void setShowPoi(String showPoi) {
        this.showPoi = showPoi;
    }

    public String getGreetCardMark() {
        return greetCardMark;
    }

    public void setGreetCardMark(String greetCardMark) {
        this.greetCardMark = greetCardMark;
    }

    public List<SayHiPhotoInfo> getGreetCardImgs() {
        return greetCardImgs;
    }

    public void setGreetCardImgs(List<SayHiPhotoInfo> greetCardImgs) {
        this.greetCardImgs = greetCardImgs;
    }

    public UniformLabelsBean getSmallUniformLabels() {
        return smallUniformLabels;
    }

    public void setSmallUniformLabels(UniformLabelsBean smallUniformLabels) {
        this.smallUniformLabels = smallUniformLabels;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameColor() {
        return nameColor;
    }

    public void setNameColor(String nameColor) {
        this.nameColor = nameColor;
    }

    public String getCardSourceText() {
        return cardSourceText;
    }

    public void setCardSourceText(String cardSourceText) {
        this.cardSourceText = cardSourceText;
    }

    public boolean isHeartbeat() {
        return isHeartbeat;
    }

    public void setHeartbeat(boolean heartbeat) {
        isHeartbeat = heartbeat;
    }

    public boolean isFirstCard() {
        return isFirstCard;
    }

    public void setFirstCard(boolean firstCard) {
        isFirstCard = firstCard;
    }

    public List<SayHiUserMark> getGreetCardMarkList() {
        return greetCardMarkList;
    }

    public void setGreetCardMarkList(List<SayHiUserMark> greetCardMarkList) {
        this.greetCardMarkList = greetCardMarkList;
    }

    /**
     * 用户头像信息
     */
    @Keep
    public static class AvatarUrlInfo implements Serializable {

        @Expose
        @SerializedName("small")
        public String small;

        @Expose
        @SerializedName("origin")
        public String origin;

    }

}