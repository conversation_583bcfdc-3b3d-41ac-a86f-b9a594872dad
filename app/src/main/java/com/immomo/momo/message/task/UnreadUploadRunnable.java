package com.immomo.momo.message.task;

import com.cosmos.mdlog.MDLog;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.im.IMJPacket;
import com.immomo.moarch.account.AccountKit;
import com.immomo.momo.LogTag;
import com.immomo.momo.home.manager.HomeUnreadManager;
import com.immomo.momo.maingroup.manager.FrameConfigConst;
import com.immomo.momo.message.helper.UnreadCountHelper;
import com.immomo.momo.protocol.imjson.IMJApi;
import com.immomo.momo.service.sessions.MessageServiceHelper;

/**
 * 前后台切换时上报各类型的消息未读数
 */
public class UnreadUploadRunnable implements Runnable {

    @Override
    public void run() {
        try {
            if (!AccountKit.getAccountManager().isOnline()) {
                return;
            }
            int sessionUnread = HomeUnreadManager.INSTANCE.getHomeUnreadCount(FrameConfigConst.KEY_HOME_CHAT);
            int gotochatUnread = MessageServiceHelper.getInstance().getGotoSessionUnread(true);
            int p2pUnread = MessageServiceHelper.getInstance().getCharMessageUnread(true);
            int groupUnread = MessageServiceHelper.getInstance().getGroupMessageUnread()
                    + MessageServiceHelper.getInstance().getDiscussMessageUnread();
            int noreplyUnread = MessageServiceHelper.getInstance().getSayHiMessageUnread();
            MDLog.d(LogTag.Message.Message, "gotochatUnread: " + gotochatUnread + "; p2pUnread：" + p2pUnread + "; groupUnread：" + groupUnread  + "; noreplyUnread：" + noreplyUnread);
            int noticeUnread = KV.getUserInt(SPKeys.User.NoticeMsg.KEY_INTERACTION_NOTICE_UNREAD_COUNT, 0);
            boolean needUpload = compareUnreadCount(sessionUnread, gotochatUnread, p2pUnread, groupUnread, noticeUnread, noreplyUnread);

            if (needUpload) {
                IMJPacket packet = IMJApi.uploadUnreadMessageCount(sessionUnread, gotochatUnread, p2pUnread, groupUnread, noticeUnread, noreplyUnread);
                if (packet != null) {
                    updateUnreadCount(sessionUnread, gotochatUnread, p2pUnread, groupUnread,noticeUnread, noreplyUnread);
                }
            }
        } catch (Exception e) {
            MDLog.printErrStackTrace(LogTag.Message.Message, e);
        }
    }

    /**
     * 比较未读数是否发生变化
     */
    private boolean compareUnreadCount(int sessionUnread, int gotochatUnread, int p2pUnread, int groupUnread, int noticeUnread, int noreplyUnread) {
        int oldGotoChatUnread = UnreadCountHelper.INSTANCE.getGotochatUnread();
        int oldP2pUnread = UnreadCountHelper.INSTANCE.getP2pUnread();
        int oldGroupUnread = UnreadCountHelper.INSTANCE.getGroupUnread();
        int oldNoticeUnread = UnreadCountHelper.INSTANCE.getNoticeUnread();
        int oldNoreplyUnread = UnreadCountHelper.INSTANCE.getNoreplyUnread();
        int oldSessionUnread = UnreadCountHelper.INSTANCE.getSessionUnread();
        if (oldSessionUnread != sessionUnread
                || oldGotoChatUnread != gotochatUnread
                || oldP2pUnread != p2pUnread
                || oldGroupUnread != groupUnread
                || oldNoticeUnread != noticeUnread
                || oldNoreplyUnread != noreplyUnread) {
            return true;
        }
        return false;
    }

    /**
     * 更新未读数
     */
    private void updateUnreadCount(int sessionUnread, int gotochatUnread, int p2pUnread, int groupUnread, int noticeUnread, int noreplyUnread) {
        UnreadCountHelper.INSTANCE.setGotochatUnread(gotochatUnread);
        UnreadCountHelper.INSTANCE.setP2pUnread(p2pUnread);
        UnreadCountHelper.INSTANCE.setGroupUnread(groupUnread);
        UnreadCountHelper.INSTANCE.setNoticeUnread(noticeUnread);
        UnreadCountHelper.INSTANCE.setNoreplyUnread(noreplyUnread);
        UnreadCountHelper.INSTANCE.setSessionUnread(sessionUnread);
    }

}
