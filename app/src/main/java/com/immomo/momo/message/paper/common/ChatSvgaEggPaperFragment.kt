package com.immomo.momo.message.paper.common

import android.view.View
import com.immomo.momo.R
import com.immomo.momo.android.view.easteregg.ChatSvgaEggUtils
import com.immomo.momo.eventbus.DataEvent
import com.immomo.momo.message.paper.BasePaperFragment
import com.immomo.momo.message.paper.event.MessageInitData
import com.immomo.momo.message.paper.event.PaperEvent
import com.immomo.momo.mvp.message.view.BaseMessageActivity
import com.immomo.momo.service.bean.Message
import com.immomo.svgaplayer.view.MomoSVGAImageView

/**
 * 新年彩蛋动画播放
 * <AUTHOR>
 * @data 2020-12-18.
 */

class ChatSvgaEggPaperFragment : BasePaperFragment() {


    var svgaImageView: MomoSVGAImageView? = null
    var chatSvgaEggUtils: ChatSvgaEggUtils? = null

    companion object {
        fun newInstance(): ChatSvgaEggPaperFragment {
            return ChatSvgaEggPaperFragment()
        }
    }

    override fun getContainerId(): Int = R.id.chat_svga_egg_paper_container

    override fun getPageLayout(): Int = R.layout.paper_chat_svga_egg


    override fun initPageViews(contentView: View?) {
        svgaImageView = contentView?.findViewById(R.id.svga_chat_egg_animation)
        chatSvgaEggUtils = ChatSvgaEggUtils()

    }

    override fun onPageLoad() {

    }

    override fun onEvent(event: DataEvent<Any>) {
        super.onEvent(event)
        when (event.action) {
            PaperEvent.PAPER_EVENT_PULL_MESSAGE_IN_WINDOW,
            PaperEvent.PAPER_EVENT_ADD_REMOTE_MESSAGE_LIST -> {
                (event.data as? Message)?.let {
                    chatSvgaEggUtils?.showSvgaEggViewWhenReady(it, svgaImageView)
                }
            }
            PaperEvent.PAPER_EVENT_INIT_MESSAGE_LIST -> {
                (event.data as? MessageInitData)?.let {
                    it.message?.let { list ->
                        chatSvgaEggUtils?.showSvgaEggViewWhenReady(list, it.unreadCount, svgaImageView)

                    }
                }
            }
        }
    }


    override fun onDestroy() {
        super.onDestroy()
        chatSvgaEggUtils?.release(svgaImageView)
    }

    fun getBaseActivity(): BaseMessageActivity? {
        (activity as? BaseMessageActivity)?.let {
            return it
        }
        return null
    }

}