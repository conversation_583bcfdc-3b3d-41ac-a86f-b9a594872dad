package com.immomo.momo.message.paper.common

import android.graphics.Color
import android.os.Build
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.widget.FrameLayout
import androidx.lifecycle.ViewModelProvider
import com.immomo.momo.R
import com.immomo.momo.eventbus.DataEvent
import com.immomo.momo.message.ChatWebPlaceHelper
import com.immomo.momo.message.ChatWebPlaceHelper.Companion.adjustMarginTop
import com.immomo.momo.message.activity.ChatActivity
import com.immomo.momo.message.iview.IChatWebView
import com.immomo.momo.message.paper.BasePaperFragment
import com.immomo.momo.message.paper.PaperCommonViewModel
import com.immomo.momo.message.paper.event.PaperEvent
import com.immomo.momo.message.presenter.ChatWebPresenter
import com.immomo.momo.message.presenter.IChatWebPresenter
import com.immomo.momo.mk.MomoMKWebViewHelper
import com.immomo.momo.mk.bridges.GlobalEventBridge
import com.immomo.momo.performance.SimpleViewStubProxy
import de.greenrobot.event.EventBus
import immomo.com.mklibrary.core.base.ui.MKWebView
import immomo.com.mklibrary.core.ui.SetUIBtnParams
import immomo.com.mklibrary.core.ui.SetUIParams

/**
 * 单聊商业网页入口
 * <AUTHOR>
 */

class SingleChatWebPaperFragment : BasePaperFragment(), IChatWebView {

    private var mIChatWebPresenter: IChatWebPresenter? = null
    private var webViewStubProxy: SimpleViewStubProxy<FrameLayout>? = null
    private var mWebViewAction: String? = null
    private var mkWebViewHelper: MomoMKWebViewHelper? = null
    private var mWebContainer: FrameLayout? = null
    private var mContentView: View? = null
    private var mPaperCommonViewModel: PaperCommonViewModel? = null
    private var isActivityInform: Boolean = false

    companion object {
        fun newInstance(): SingleChatWebPaperFragment {
            return SingleChatWebPaperFragment()
        }
    }

    override fun getContainerId(): Int = R.id.chat_web_paper_container

    override fun getPageLayout(): Int = R.layout.paper_single_chat_web


    override fun initPageViews(contentView: View?) {
        mContentView = contentView
        initWebInfo()
        getChatActivity()?.let {
            mPaperCommonViewModel = ViewModelProvider(it).get(PaperCommonViewModel::class.java)
            initChatWebPlaceHelper()
        }
    }

    private fun initChatWebPlaceHelper() {
        mPaperCommonViewModel?.chatWebPlaceHelper = ChatWebPlaceHelper()
        mPaperCommonViewModel?.chatWebPlaceHelper?.listener = object : ChatWebPlaceHelper.ChatWebPlaceHelperListener {
            override fun adjustPlace(marginTop: Int) {
                adjustMarginTop(marginTop, mWebContainer)
            }
        }
    }

    override fun onPageLoad() {

    }

    private fun initWebInfo() {
        getChatActivity()?.let {
            if (it.isFullModel) {
                mIChatWebPresenter = ChatWebPresenter(this, it.remoteId)
                mIChatWebPresenter?.getWebInfo()
            }
        }
    }

    override fun closeWeb() {
        webViewStubProxy?.visibility = View.GONE
    }

    override fun isWebShow(): Boolean {
        return webViewStubProxy?.visibility == View.VISIBLE
    }

    override fun openWeb(url: String) {
        EventBus.getDefault().post(DataEvent(PaperEvent.PAPER_EVENT_LOVE_APARTMENT_REFRESH, ""))
        getChatActivity()?.let {
            if (it.mIsShowMultiChoice || isActivityInform) {
                mWebViewAction = url
                return
            }
            mWebViewAction = null
            initWebViewStub(url)
        }
    }

    private fun initWebViewStub(jumpUrl: String) {
        val viewStub: View? = mContentView?.findViewById(R.id.viewstub_webview_container)
        if (viewStub != null) {
            webViewStubProxy = SimpleViewStubProxy(viewStub as? ViewStub)
        }
        if (webViewStubProxy != null) {
            webViewStubProxy?.visibility = View.VISIBLE
            mWebContainer = webViewStubProxy?.getView(R.id.chat_web_container) as? FrameLayout
            mWebContainer?.let {
                initWebView(it, jumpUrl)
            }
            mPaperCommonViewModel?.chatWebPlaceHelper?.let {
                adjustMarginTop(it.getMarginTop(), mWebContainer)
            }
        }
    }

    private fun initWebView(parent: FrameLayout, jumpUrl: String) {
        getChatActivity()?.let {
            parent.removeAllViews()
            val webView = MKWebView(it)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                webView.setLayerType(View.LAYER_TYPE_HARDWARE, null)
            }
            webView.setBackgroundColor(Color.TRANSPARENT)
            parent.addView(webView, ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT))
            mkWebViewHelper = object : MomoMKWebViewHelper() {
                override fun clearRightButton() {}
                override fun closePage() {}
                override fun uiGoBack() {}
                override fun uiSetTitle(title: String) {}
                override fun uiShowHeaderBar(show: Boolean) {}
                override fun uiSetUI(uiParams: SetUIParams) {}
                override fun uiSetUIButton(params: SetUIBtnParams) {}
                override fun uiClosePopup() {
                    destroyWebView()
                }
            }
            mkWebViewHelper?.bindActivity(it, webView)
            mkWebViewHelper?.initMomoWebView()
            mkWebViewHelper?.registerHighPriorityBridge(GlobalEventBridge(webView))
            webView.loadUrl(jumpUrl)
        }

    }

    private fun destroyWebView() {
        mWebViewAction = null
        webViewStubProxy?.visibility = View.GONE
        mkWebViewHelper?.onPageDestroy()
        mkWebViewHelper = null
    }

    override fun onEvent(event: DataEvent<Any>) {
        super.onEvent(event)
        when (event.action) {
            PaperEvent.PAPER_EVENT_CHANGE_FULL_MODE -> {
                initWebInfo()
            }
            PaperEvent.PAPER_EVENT_HIDE_CHAT_FORWARD -> {
                if (!TextUtils.isEmpty(mWebViewAction)) {
                    openWeb(mWebViewAction!!)
                }
            }
            PaperEvent.PAPER_EVENT_HIDE_CHAT_WEB -> {
                setFromTextChat()
            }
        }
    }

    override fun onDestroy() {
        if (mIChatWebPresenter != null) {
            mIChatWebPresenter!!.onDestroy()
        }
        destroyWebView()
        super.onDestroy()
    }

    private fun getChatActivity(): ChatActivity? {
        (activity as? ChatActivity)?.let {
            return it
        }
        return null
    }

    private fun setFromTextChat() {
        getChatActivity()?.let {
            isActivityInform = true
            destroyWebView()
        }
    }

}