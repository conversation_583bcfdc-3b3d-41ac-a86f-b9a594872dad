package com.immomo.momo.message.sayhi.widget.guideclick;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.OvershootInterpolator;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.immomo.momo.R;

import java.util.Arrays;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public class GuideScrollDownView extends BaseGuideClickView {
    private TextView tv;
    private ImageView thumb;
    private RelativeLayout mainLayout;
    private View thumbLayout;


    public GuideScrollDownView(@NonNull Context context) {
        this(context, null);
    }

    public GuideScrollDownView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public GuideScrollDownView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void initView(Context context) {
        inflate(context, R.layout.view_sayhi_guide_scroll_down, this);
        mainLayout = (RelativeLayout) findViewById(R.id.guide_root_layout);
        tv = (TextView) findViewById(R.id.tv_guide);
        thumb = (ImageView) findViewById(R.id.guide_click_thumb);
        thumbLayout = findViewById(R.id.thumb_layout);
    }

    @Override
    protected void initAnim(List<Animator> sequenceList) {
        ValueAnimator alphaIn = ValueAnimator.ofFloat(0f, 1f);
        alphaIn.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                Float v = (Float) animation.getAnimatedValue();
                mainLayout.setAlpha(v);
                OnGuideClickViewCallback c = getCallback();
                if (c != null) {
                    c.onInOutAlphaChange(v);
                }
            }
        });
        alphaIn.setDuration(300L);

        ValueAnimator jumpIn = ValueAnimator.ofFloat(0f, 1f);
        jumpIn.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                Float v = (Float) animation.getAnimatedValue();
                tv.setAlpha(v);
                tv.setScaleX(v);
                tv.setScaleY(v);
            }
        });
        jumpIn.setDuration(400L);
        jumpIn.setInterpolator(new OvershootInterpolator(2.5f));

        ValueAnimator thumbAnimator = ValueAnimator.ofFloat(1.0f, -1f);
        thumbAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                Float v = (Float) animation.getAnimatedValue();
                thumb.setAlpha(1 - Math.abs(v));
                thumb.setTranslationY((thumbLayout.getHeight() - thumb.getHeight()) * 0.5f * v);
            }
        });
        thumbAnimator.setDuration(1000L);
        thumbAnimator.setRepeatCount(2);


        ValueAnimator hideAnim = ValueAnimator.ofFloat(1f, 0f);
        hideAnim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                if (isAutoHide()) {
                    Float v = (Float) animation.getAnimatedValue();
                    mainLayout.setAlpha(v);
                    OnGuideClickViewCallback c = getCallback();
                    if (c != null) {
                        c.onInOutAlphaChange(v);
                    }
                }
            }
        });
        hideAnim.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                if (isAutoHide()) {
                    hide();
                }
            }
        });
        sequenceList.addAll(Arrays.asList(alphaIn, jumpIn, thumbAnimator, hideAnim));
    }

    protected void presetView() {
        tv.setAlpha(0f);
        mainLayout.setAlpha(0f);
        thumb.setAlpha(0f);
        super.presetView();
    }

    public void setTipText(CharSequence tipText) {
        tv.setText(tipText);
    }
}