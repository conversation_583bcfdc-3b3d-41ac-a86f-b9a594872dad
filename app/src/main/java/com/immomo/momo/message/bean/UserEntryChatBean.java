package com.immomo.momo.message.bean;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.immomo.momo.aichat.AIChatEntryBean;

/**
 * author: hongming.wei
 * data: 2024/1/10
 */
public class UserEntryChatBean {

    @Expose
    @SerializedName("remote")
    private RemoteModel remoteModel;

    @Expose
    @SerializedName("fingerGuess")
    private FingerGuessModel fingerGuessModel;

    @Expose
    @SerializedName("highestTopInfo")
    private HighestTopBarInfo highestTopBarInfo;
    @Expose
    @SerializedName("aiChat")
    private AIChatEntryBean aiChat;

    @Expose
    @SerializedName("textMatchActivity")
    private TextMatchActivityModel textMatchActivity;

    public FingerGuessModel getFingerGuessModel() {
        return fingerGuessModel;
    }

    public RemoteModel getRemoteModel() {
        return remoteModel;
    }

    public void setFingerGuessModel(FingerGuessModel fingerGuessModel) {
        this.fingerGuessModel = fingerGuessModel;
    }


    public void setRemoteModel(RemoteModel remoteModel) {
        this.remoteModel = remoteModel;
    }

    public HighestTopBarInfo getChatHighestTopBarInfo() {
        return highestTopBarInfo;
    }

    public void setChatHighestTopBarInfo(HighestTopBarInfo highestTopBarInfo) {
        this.highestTopBarInfo = highestTopBarInfo;
    }

    public AIChatEntryBean getAiChat() {
        return aiChat;
    }

    public void setAiChat(AIChatEntryBean aiChat) {
        this.aiChat = aiChat;
    }

    public TextMatchActivityModel getTextMatchActivity() {
        return textMatchActivity;
    }

    public void setTextMatchActivity(TextMatchActivityModel textMatchActivity) {
        this.textMatchActivity = textMatchActivity;
    }

    public class RemoteModel {

        @Expose
        @SerializedName("spamStatus")
        private int spamStatus;


        public int getSpamStatus() {
            return spamStatus;
        }

        public void setSpamStatus(int spamStatus) {
            this.spamStatus = spamStatus;
        }
    }


    public class FingerGuessModel {

        @Expose
        @SerializedName("enabled")
        private int enabled;

        @Expose
        @SerializedName("roomId")
        private String roomId;

        @Expose
        @SerializedName("roomStatus")
        private int roomStatus;

        @Expose
        @SerializedName("currentRoundDetail")
        private CurrentRoundDetailModel currentRoundDetail;

        public CurrentRoundDetailModel getCurrentRoundDetail() {
            return currentRoundDetail;
        }

        public int getEnabled() {
            return enabled;
        }

        public String getRoomId() {
            return roomId;
        }

        public int getRoomStatus() {
            return roomStatus;
        }

        public void setCurrentRoundDetail(CurrentRoundDetailModel currentRoundDetail) {
            this.currentRoundDetail = currentRoundDetail;
        }

        public void setEnabled(int enabled) {
            this.enabled = enabled;
        }

        public void setRoomId(String roomId) {
            this.roomId = roomId;
        }

        public void setRoomStatus(int roomStatus) {
            this.roomStatus = roomStatus;
        }

        public class CurrentRoundDetailModel{

            @Expose
            @SerializedName("status")
            private int status;
            @Expose
            @SerializedName("creator")
            private String creator;
            @Expose
            @SerializedName("winner")
            private String winner;
            @Expose
            @SerializedName("startTime")
            private long startTime;
            @Expose
            @SerializedName("endTime")
            private long endTime;


            public String getCreator() {
                return creator;
            }

            public long getEndTime() {
                return endTime;
            }

            public long getStartTime() {
                return startTime;
            }

            public int getStatus() {
                return status;
            }

            public String getWinner() {
                return winner;
            }

            public void setCreator(String creator) {
                this.creator = creator;
            }

            public void setEndTime(long endTime) {
                this.endTime = endTime;
            }

            public void setStartTime(long startTime) {
                this.startTime = startTime;
            }

            public void setStatus(int status) {
                this.status = status;
            }

            public void setWinner(String winner) {
                this.winner = winner;
            }
        }
    }

    public class TextMatchActivityModel {

        @Expose
        @SerializedName("activityUrl")
        private String activityUrl;

        @Expose
        @SerializedName("guideAction")
        private String guideAction;

        public String getActivityUrl() {
            return activityUrl;
        }

        public String getGuideAction() {
            return guideAction;
        }

        public void setActivityUrl(String activityUrl) {
            this.activityUrl = activityUrl;
        }

        public void setGuideAction(String guideAction) {
            this.guideAction = guideAction;
        }
    }

}


