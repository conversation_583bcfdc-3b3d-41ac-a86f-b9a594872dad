package com.immomo.momo.message.task;

import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.message.activity.GroupChatActivity;
import com.immomo.momo.message.contract.IMsgChatDataHolder;
import com.immomo.momo.message.contract.IMsgChatRecycler;
import com.immomo.momo.messages.service.GroupMsgService;
import com.immomo.momo.messages.service.GroupMsgServiceV2;
import com.immomo.momo.mvp.message.view.BaseMessageActivity;
import com.immomo.momo.service.bean.Message;

import java.util.ArrayList;
import java.util.List;


/**
 * 加载更多新消息的异步任务
 * <AUTHOR>
 * date 2020/8/8
 */
public class LoadMoreNewMessageTask extends MomoTaskExecutor.Task<Object, Object, List<Message>> {

    private GroupChatActivity mActivity;
    private IMsgChatRecycler mMsgChatRecycler;
    private IMsgChatDataHolder mMsgChatData;
    private String mCurrentGroupGid;


    public LoadMoreNewMessageTask(GroupChatActivity mActivity, IMsgChatDataHolder msgChatData,
                                  IMsgChatRecycler msgChatRecycler, String mCurrentGroupGid, Object... objects) {
        super(objects);
        this.mActivity = mActivity;
        this.mMsgChatData = msgChatData;
        this.mMsgChatRecycler = msgChatRecycler;
        this.mCurrentGroupGid = mCurrentGroupGid;
    }

    @Override
    protected List<Message> executeTask(Object... params) throws Exception {
        if (mActivity.msgChatData.isEmpty()){
            return new ArrayList<Message>();
        }
        Message message = mMsgChatData.getMessageList().get(mMsgChatData.getCount() - 1);
        List<Message> messages = GroupMsgServiceV2.getService().findMessageBy(
                mCurrentGroupGid, BaseMessageActivity.PAGE_SIZE + 1, message, true, false);

        if (messages.size() == BaseMessageActivity.PAGE_SIZE + 1) {
            mActivity.setHasMoreNewerMessage(true);
            messages.remove(messages.size() - 1);
        } else {
            mActivity.setHasMoreNewerMessage(false);
        }

        mActivity.preHandleMessages(messages);
        return messages;
    }

    @Override
    protected void onTaskFinish() {
        mMsgChatRecycler.setLoadMoreComplete();
    }

    @Override
    protected void onTaskSuccess(List<Message> messages) {
        if (messages.size() > 0) {
            mMsgChatData.addItemModels(messages);
        }
    }

    @Override
    protected void onTaskError(Exception e) {
        //show some error
    }
}
