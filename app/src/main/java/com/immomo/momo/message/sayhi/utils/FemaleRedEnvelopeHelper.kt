package com.immomo.momo.message.sayhi.utils

import com.immomo.framework.storage.kv.KV
import com.immomo.framework.storage.preference.SPKeys
import com.immomo.lcapt.evlog.EVLog
import com.immomo.momo.message.sayhi.IFemaleRedEnvelopeLog
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo

object FemaleRedEnvelopeHelper {

    fun isRedEnvelopeHi(info: SayHiInfo?): Boolean {
        return info?.isRedEnvelopeHi == true
    }

    fun shouldShowRedEnvelopeTip(info: SayHiInfo?): Boolean {
        return isRedEnvelopeHi(info)
                && !KV.getUserBool(SPKeys.User.SayHi.KEY_HAS_SHOW_RED_ENVELOP_TIP, false)
    }

    fun redEnvelopeShow(info: SayHiInfo?) {
        info?.let { it ->
            if (isRedEnvelopeHi(it)) {
                EVLog.create(IFemaleRedEnvelopeLog::class.java).show(it.momoid)
            }
        }
    }
}