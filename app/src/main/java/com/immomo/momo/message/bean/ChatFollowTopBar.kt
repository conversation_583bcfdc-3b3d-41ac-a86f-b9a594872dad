package com.immomo.momo.message.bean

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import java.io.Serializable

/**
 * Created by z<PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/7/24.
 */
data class ChatFollowTopBar(
        @SerializedName("operation") @Expose val operation: String?,
        @SerializedName("remoteid") @Expose val remoteid: String?,
        @SerializedName("topbar") @Expose val topbar: ChatFollowTopBarInfo?
) : Serializable {

    companion object {
        const val TRANSMIT = "chat_follow_topbar_data_transmit"
    }

}