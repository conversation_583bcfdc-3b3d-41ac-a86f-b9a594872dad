package com.immomo.momo.message.sayhi.widget.guideclick;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.app.Activity;
import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.cosmos.mdlog.MDLog;
import com.immomo.LogTag;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Created by lei.jialin on 2020-07-01.
 */
public abstract class BaseGuideClickView extends FrameLayout {
    private int[] targetviewWH;
    private int[] boundsviewLoc;
    private int[] boundsviewWH;
    /**
     * targetView左上角坐标
     */
    private int[] locationTargetView;

    /**
     * 点击正确位置后的Callback
     */
    private OnGuideClickViewCallback callback;

    /**
     * 手指当前落点位置
     */
    private int x, y;

    private AnimatorSet animInSet = new AnimatorSet();
    private Animator.AnimatorListener animatorListener;
    private boolean isAutoHide;
    private List<Animator> sequenceList = new ArrayList<>();


    public BaseGuideClickView(@NonNull Context context) {
        this(context, null);
    }

    public BaseGuideClickView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BaseGuideClickView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setClipChildren(false);
        init(context);

    }

    private void init(Context context) {
        initView(context);
        initAnim(sequenceList);
    }

    public void cancelAnim() {
        if (animInSet != null) {
            if (animInSet.isRunning()) {
                animInSet.cancel();
            }
            animInSet.removeListener(animatorListener);
        }
    }

    public boolean isAutoHide() {
        return isAutoHide;
    }

    public void setTargetParams(int[] targetWH, int[] position) {
        targetviewWH = new int[2];
        targetviewWH[0] = targetWH[0];
        targetviewWH[1] = targetWH[1];
        this.locationTargetView = new int[2];
        locationTargetView[0] = position[0];
        locationTargetView[1] = position[1];
    }

    protected abstract void initView(Context context);

    protected abstract void initAnim(List<Animator> sequenceList);

    /*ViewGroud的addView*/
    public void addCustomView(ViewGroup parentView, View view) {
        try {
            ViewGroup oldParent = (ViewGroup) view.getParent();
            if (oldParent != null) {
                oldParent.removeView(view);
            }
            parentView.addView(view);
        } catch (Exception e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
    }

    public BaseGuideClickView autoHide(boolean autoHide) {
        isAutoHide = autoHide;
        return this;
    }

    public void show() {
        try {
            show((ViewGroup) ((Activity) getContext()).getWindow().getDecorView());
        } catch (Exception e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
    }

    protected void presetView() {
        setVisibility(VISIBLE);
    }

    public void addSequenceAnimator(int index, Animator objectAnimator) {
        sequenceList.add(index, objectAnimator);
    }

    public void hide() {
        cancelAnim();
        try {
            setVisibility(GONE);
            ((FrameLayout) ((Activity) getContext()).getWindow().getDecorView()).removeView(this);
        } catch (Exception e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
    }

    public void show(ViewGroup viewGroup) {
        if (viewGroup == null) return;
        beforeShow();
        addCustomView(viewGroup, this);
        doRealShow();
    }

    protected void doRealShow() {
        if (!sequenceList.isEmpty()) {
            animInSet.playSequentially(sequenceList);
            sequenceList.clear();
        }
        if (animInSet != null && !animInSet.isRunning()) {
            animInSet.start();
        }
    }


    private void beforeShow() {
        cancelAnim();
        presetView();
        if (animatorListener != null) {
            animInSet.removeListener(animatorListener);
            animInSet.addListener(animatorListener);
        }
    }

    public void setAnimatorListener(Animator.AnimatorListener animatorListener) {
        this.animatorListener = animatorListener;
    }

    public void setOnCorrectClickCallback(OnGuideClickViewCallback onClickCallback) {
        this.callback = onClickCallback;
    }

    public void setBoundsParams(int[] boundsWH, int[] boundsPosition) {
        boundsviewLoc = new int[2];
        boundsviewLoc[0] = boundsPosition[0];
        boundsviewLoc[1] = boundsPosition[1];
        boundsviewWH = new int[2];
        boundsviewWH[0] = boundsWH[0];
        boundsviewWH[1] = boundsWH[1];
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            x = (int) event.getX();
            y = (int) event.getY();
        }
        if (event.getAction() == MotionEvent.ACTION_UP ||
                event.getAction() == MotionEvent.ACTION_MOVE) {
            return true;
        }

        if (callback != null) {
            if (clickOutofRegin()) {  //点击在有效区域外，则直接消失
                hide();
                callback.onClickOutOfRange();
            } else if (clickCorrectPlace()) {
                hide();
                callback.onClickedGuideView();
            } else {
                callback.onNotClickGuideRegin();
            }
        }
        return true;
    }

    /**
     * 有效点击范围：点击落点在bounds边界外，则整个view消失 （针对InWindow的坐标系，(0,0)则表示状态栏左下方）
     * 如果存在有效点击范围(不为0)，且点击不在有效区域内，消失
     */
    public boolean clickOutofRegin() {
        if (boundsviewLoc == null) {
            return false;
        }
        if (x > boundsviewLoc[0] && x < boundsviewLoc[0] + boundsviewWH[0]) {
            if (y > boundsviewLoc[1] && y < boundsviewLoc[1] + boundsviewWH[1]) {
                return false;
            }
        }

        return true; // 没有有效区域
    }

    public OnGuideClickViewCallback getCallback() {
        return callback;
    }

    /**
     * 是否点击在有效区域内的正确位置
     */
    public boolean clickCorrectPlace() {
        // 如果点击了TargetView的正确位置
        if (locationTargetView == null || targetviewWH == null)
            return false;
        if (x > locationTargetView[0] && x < locationTargetView[0] + targetviewWH[0]) {
            if (y > locationTargetView[1] && y < locationTargetView[1] + targetviewWH[1]) {
                return true;
            }
        }
        return false;
    }

    public boolean isAnimating() {
        return animInSet != null && animInSet.isRunning();
    }

}