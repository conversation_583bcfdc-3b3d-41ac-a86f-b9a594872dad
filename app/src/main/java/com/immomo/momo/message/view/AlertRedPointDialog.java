package com.immomo.momo.message.view;

import android.content.Context;
import android.os.Build;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.immomo.momo.R;
import com.immomo.momo.android.view.dialog.MAlertListDialog;
import com.immomo.momo.message.MessageBubbleHelper;
import com.immomo.momo.message.chatmsg.absitemmodel.AbsChildItemModel;
import com.immomo.momo.util.MomoKit;

import java.util.Arrays;
import java.util.List;

public class AlertRedPointDialog extends MAlertListDialog {

    public AlertRedPointDialog(Context paramContext) {
        super(paramContext);
    }

    public AlertRedPointDialog(Context paramContext, String[] items) {
        this(paramContext, Arrays.asList(items));
    }

    public AlertRedPointDialog(Context paramContext, List<String> optionList) {
        this(paramContext);
        setAdatper(new MMAdapter(getContext(), optionList));
    }

    protected class MMAdapter extends MAdapter {

        public MMAdapter(Context context, List<String> items) {
            super(context, items);
        }

        @Override
        public View getView(int position, View itemView, ViewGroup parent) {
            String item = (String) getItem(position);
            if (itemView == null) {
                if (supportDark && MomoKit.INSTANCE.isDarkMode(getContext())) {
                    itemView = inflater.inflate(R.layout.listitem_msg_dialog_dark, null);
                } else {
                    itemView = inflater.inflate(R.layout.listitem_msg_dialog, null);
                }

            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                itemView.setForceDarkAllowed(false);
            }

            TextView textView = itemView.findViewById(R.id.textview);
            textView.setText(item);
            View redDot = itemView.findViewById(R.id.iv_red_dot);

            if (position == items.size() - 1 &&
                    (AbsChildItemModel.STR_USE_OTHER_BUBBLE.equals(item) || AbsChildItemModel.STR_SET_MY_BUBBLE.equals(item))) {
                if (MessageBubbleHelper.INSTANCE.shouldRedDotShow()) {
                    redDot.setVisibility(View.VISIBLE);
                }
            } else {
                redDot.setVisibility(View.INVISIBLE);
            }
            return itemView;
        }
    }
}
