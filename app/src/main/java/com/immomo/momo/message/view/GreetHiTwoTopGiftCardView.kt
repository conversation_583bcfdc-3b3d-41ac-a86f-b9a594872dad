package com.immomo.momo.message.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.molive.kotlin.extensions.isNotNullOrEmpty
import com.immomo.molive.thirdparty.master.flame.danmakufix.danmaku.util.SystemClock
import com.immomo.momo.R
import com.immomo.momo.gift.bean.BaseGift
import com.immomo.momo.greet.result.GreetMessageResult
import com.immomo.momo.greet.result.GreetMessageResult.HiGiftGuide
import com.immomo.momo.greet.util.GreetHiGifTopRecord
import com.immomo.momo.message.bean.GreetHiTopGiftCardData
import com.immomo.svgaplayer.view.MomoSVGAImageView
import com.mln.watcher.safe

/**
 * 打招呼置顶礼物
 */

open class GreetHiTwoTopGiftCardView : GreetPugCardView {

    private var hiGiftGuide: HiGiftGuide? = null

    private var lastClickTime = 0L // 点击防抖动

    private val hotHeadSvga by lazy { findViewById<MomoSVGAImageView>(R.id.hot_head_svga) }
    private val hotHeadTxt by lazy { findViewById<TextView>(R.id.hot_head_txt) }
    private val headTitle by lazy { findViewById<TextView>(R.id.head_title) }
    private val headSubtitle by lazy { findViewById<TextView>(R.id.head_subtitle) }
    private val cardLeftBg by lazy { findViewById<View>(R.id.card_left_bg) }
    private val cardLeftBgForeground by lazy { findViewById<View>(R.id.card_left_bg_foreground) }
    private val giftIconLeft by lazy { findViewById<ImageView>(R.id.gift_icon_left) }
    private val giftPriceLeft by lazy { findViewById<TextView>(R.id.gift_price_left) }
    private val giftTipLeft by lazy { findViewById<TextView>(R.id.gift_tip_left) }
    private val cardRightBg by lazy { findViewById<View>(R.id.card_right_bg) }
    private val cardRightBgForeground by lazy { findViewById<View>(R.id.card_right_bg_foreground) }
    private val giftIconRight by lazy { findViewById<ImageView>(R.id.gift_icon_right) }
    private val giftPriceRight by lazy { findViewById<TextView>(R.id.gift_price_right) }
    private val giftTipRight by lazy { findViewById<TextView>(R.id.gift_tip_right) }

    private var greetNum = ""
    private var toSayhiId = ""
    private var toSayhiGender = ""

    constructor(context: Context) : super(context)

    constructor(context: Context, attributeSet: AttributeSet) : super(context, attributeSet)

    constructor(context: Context, attributeSet: AttributeSet, defStyleAttr: Int) : super(
        context,
        attributeSet,
        defStyleAttr
    )

    override fun createView() {
        LayoutInflater.from(context).inflate(R.layout.layout_greet_hi_two_top_gift_card, this, true)
    }

    override fun refresh() {
        (data as? GreetHiTopGiftCardData)?.hiGiftGuide?.let {
            hiGiftGuide = it
            initTopGiftView(it)
            visibility = View.VISIBLE
            return
        }
        visibility = View.GONE
    }

    private fun initTopGiftView(hiGiftGuide: HiGiftGuide) {
        toSayhiId = hiGiftGuide.toSayhiId.safe()
        toSayhiGender = hiGiftGuide.toSayhiGender.safe()
        hiGiftGuide.guideCard?.also {
            it.top?.text?.also { headTitleTxt ->
                hotHeadTxt.text = headTitleTxt
            }
            val icon = it.top?.icon?.safe()
            if (icon.isNotNullOrEmpty()) {
                hotHeadSvga.startSVGAAnim(icon, -1)
            }
            greetNum = it.top?.greetNum.toString()
        }
        hiGiftGuide.guideCard?.also {
            headTitle.text = it.title.safe()
            headSubtitle.text = it.subTitle.safe()
            it.gift?.forEachIndexed { index, giftCardData ->
                if (index == 0) { // 左边卡片
                    ImageLoader.load(giftCardData.icon.safe()).into(giftIconLeft)
                    giftPriceLeft.text = giftCardData.text.safe()
                    val topTip = giftCardData.topText ?: ""
                    if (topTip.isNotEmpty()) {
                        giftTipLeft.text = topTip
                        giftTipLeft.visibility = View.VISIBLE
                    }
                    cardLeftBg.setOnClickListener {
                        onGiftClick(giftCardData)
                    }
                } else if (index == 1) { // 右边卡片
                    ImageLoader.load(giftCardData.icon.safe()).into(giftIconRight)
                    giftPriceRight.text = giftCardData.text.safe()
                    val topTip = giftCardData.topText ?: ""
                    if (topTip.isNotEmpty()) {
                        giftTipRight.text = topTip
                        giftTipRight.visibility = View.VISIBLE
                    }
                    cardRightBg.setOnClickListener {
                        onGiftClick(giftCardData)
                    }
                }
            }
        }
        GreetHiGifTopRecord.greetCardExp(toSayhiId, toSayhiGender, greetNum)
    }

    private fun onGiftClick(giftCardData: GreetMessageResult.HiGuideCardGift) {
        val uptimeMillis = SystemClock.uptimeMillis()
        if (uptimeMillis - lastClickTime < 1000) {
            return
        }
        lastClickTime = uptimeMillis
        val productId: String = giftCardData.productId.safe()
        val appId = giftCardData.appId.safe()
        if (productId.isNotBlank() && appId.isNotBlank()) {
            val giftSendData = BaseGift()
            giftSendData.id = productId
            giftSendData.appId = appId
            giftSendData.price = giftCardData.price
            giftSendData.useConfigAppId = true
            cardClickListener?.onCardClick(giftSendData, true)
            GreetHiGifTopRecord.greetCardClick(toSayhiId, toSayhiGender, greetNum, productId, giftCardData.topText)
        }
    }


    override fun onDetachedFromWindow() {
        hotHeadSvga.stopAnimCompletely()
        super.onDetachedFromWindow()
    }

}
