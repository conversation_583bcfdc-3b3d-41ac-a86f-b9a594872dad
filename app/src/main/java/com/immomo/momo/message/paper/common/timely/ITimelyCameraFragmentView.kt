package com.immomo.momo.message.paper.common.timely

import androidx.fragment.app.Fragment
import com.immomo.momo.moment.model.MicroVideoModel
import com.immomo.momo.multpic.entity.Photo

interface ITimelyCameraFragmentView {
    fun getFragment(): Fragment

    fun updateViewInit()
    fun updateViewReady()
    fun updateRecording()
    fun updateRecordFinish(microVideoModel: MicroVideoModel?, photos: MutableList<Photo>?)
    fun rollback2Ready()
    fun updateViewVideoProgress(progress: Int, total: Int)
}