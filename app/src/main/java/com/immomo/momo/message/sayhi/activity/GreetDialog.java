package com.immomo.momo.message.sayhi.activity;

import static com.immomo.momo.message.helper.fastreply.AIChatCardManager.KEY_SHOW_NEARBY_SAYHI_REPLY;
import static com.immomo.momo.message.helper.fastreply.AIChatCardManager.KEY_WINDOW_KEYCLOSE;
import static com.immomo.momo.message.helper.fastreply.AIChatCardManager.KEY_WINDOW_KEYUP;
import static com.immomo.momo.pay.PayConst.VipSource.SOURCE_HEART_BEAT_HI;
import static com.immomo.momo.pay.PayConst.VipSource.SOURCE_SVIP_SAY_HI;
import static com.immomo.momo.protocol.imjson.receiver.MessageKeys.KEY_STATUS;
import static com.immomo.momo.protocol.imjson.receiver.MessageKeys.Key_Mode;
import static com.immomo.momo.util.BindPhoneHelper.MODE_IM;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.media.AudioManager;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.animation.Animation;
import android.view.animation.ScaleAnimation;
import android.view.inputmethod.InputMethodManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.customview.widget.ViewDragHelper;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.cosmos.mdlog.MDLog;
import com.google.gson.reflect.TypeToken;
import com.immomo.android.router.pay.PayConst;
import com.immomo.framework.account.MessageManager;
import com.immomo.framework.base.BaseReceiver.IBroadcastReceiveListener;
import com.immomo.framework.kotlin.ImageLoader;
import com.immomo.framework.rxjava.interactor.CommonSubscriber;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.framework.task.BaseDialogTask;
import com.immomo.framework.utils.UIUtils;
import com.immomo.game.util.DpPxUtil;
import com.immomo.lcapt.evlog.EVLog;
import com.immomo.mmstatistics.event.Event;
import com.immomo.mmstatistics.event.ExposureEvent;
import com.immomo.mmutil.log.Log4Android;
import com.immomo.mmutil.task.MMThreadExecutors;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.task.ThreadUtils;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.UrlConstant;
import com.immomo.momo.agora.mr.conflictHelper.VideoConflictConfig;
import com.immomo.momo.agora.mr.conflictHelper.VideoConflictNewHelper;
import com.immomo.momo.aichat.AIChatEntryBean;
import com.immomo.momo.android.broadcast.FileUploadProgressReceiver;
import com.immomo.momo.android.broadcast.FriendListReceiver;
import com.immomo.momo.android.broadcast.ReflushUserProfileReceiver;
import com.immomo.momo.android.broadcast.ReflushVipReceiver;
import com.immomo.momo.android.broadcast.SynCloudMsgReceiver;
import com.immomo.momo.android.synctask.Callback;
import com.immomo.momo.android.synctask.ReportOrBlockTask;
import com.immomo.momo.android.view.BindPhoneTipView;
import com.immomo.momo.android.view.ChatBottomTipView;
import com.immomo.momo.android.view.ResizableEmoteInputView;
import com.immomo.momo.android.view.dialog.MAlertListDialog;
import com.immomo.momo.android.view.dialog.OnItemSelectedListener;
import com.immomo.momo.android.view.tips.TipManager;
import com.immomo.momo.android.view.tips.tip.ITip;
import com.immomo.momo.android.view.tips.triangle.BottomTriangleDrawable;
import com.immomo.momo.android.view.tips.triangle.TriangleDrawable;
import com.immomo.momo.appconfig.model.AppConfigV2;
import com.immomo.momo.businessmodel.statistics.IPageDurationHelper;
import com.immomo.momo.businessmodel.statistics.PageStepHelper;
import com.immomo.momo.businessmodel.usermodel.IUserModel;
import com.immomo.momo.common.AppKit;
import com.immomo.momo.common.ClickUtils;
import com.immomo.momo.common.view.dialog.CommonTipDialog;
import com.immomo.momo.dynamicresources.ResourceCallbackAdapter;
import com.immomo.momo.dynamicresources.ResourceChecker;
import com.immomo.momo.emotionstore.activity.MainEmotionActivity;
import com.immomo.momo.eventbus.DataEvent;
import com.immomo.momo.eventbus.EventKeys;
import com.immomo.momo.gift.GiftCategoryConstants;
import com.immomo.momo.gift.bean.BaseGift;
import com.immomo.momo.gift.bean.GiftReceiver;
import com.immomo.momo.gift.manager.GreetChatPageGiftManager;
import com.immomo.momo.globalevent.GlobalEventManager;
import com.immomo.momo.greet.GreetGuideDialog;
import com.immomo.momo.greet.GreetHelper;
import com.immomo.momo.greet.presenter.GreetPresenterImpl;
import com.immomo.momo.greet.presenter.IGreetPresenter;
import com.immomo.momo.greet.repository.GreetReflowGiftCheckResult;
import com.immomo.momo.greet.result.GreetMessageResult;
import com.immomo.momo.greet.result.GreetRecommendChatResult;
import com.immomo.momo.greet.view.IGreetView;
import com.immomo.momo.homepage.view.CommonWhiteDialogParam;
import com.immomo.momo.innergoto.callback.GotoCallback;
import com.immomo.momo.innergoto.helper.ActivityHandler;
import com.immomo.momo.innergoto.helper.NavigateHelper;
import com.immomo.momo.innergoto.matcher.SayHiMatcher;
import com.immomo.momo.location.FromMessageGetLocationCallBack;
import com.immomo.momo.maintab.session2.apt.SessionAppConfigV2Getter;
import com.immomo.momo.maintab.sessionlist.SessionListFragment;
import com.immomo.momo.maintab.sessionlist.SessionListReceiver;
import com.immomo.momo.message.ChatHelper;
import com.immomo.momo.message.MsgUnreadProcessor;
import com.immomo.momo.message.activity.ChatActivity;
import com.immomo.momo.message.activity.ChatBGSettingActivity;
import com.immomo.momo.message.activity.ChatSettingActivity;
import com.immomo.momo.message.adapter.items.MessageAction;
import com.immomo.momo.message.bean.GreetCardData;
import com.immomo.momo.message.bean.GreetHiTopGiftCardData;
import com.immomo.momo.message.bean.MessageGiftData;
import com.immomo.momo.message.business.textchat.CheckTextChatFlagTask;
import com.immomo.momo.message.chatmsg.itemmodel.child.AudioChildItemModel;
import com.immomo.momo.message.contract.ChatEditTopNoticeContract;
import com.immomo.momo.message.helper.FrequentPreferenceHelper;
import com.immomo.momo.message.helper.GreetViewHelperKt;
import com.immomo.momo.message.helper.MessageHelper;
import com.immomo.momo.message.helper.fastreply.AIChatCardManager;
import com.immomo.momo.message.helper.fastreply.IFastReplyLog;
import com.immomo.momo.message.helper.msgview.ViewPriorityManager;
import com.immomo.momo.message.paper.PaperFragmentHelper;
import com.immomo.momo.message.paper.chat.greet.GreetChatTopPaperFragment;
import com.immomo.momo.message.paper.event.PaperEvent;
import com.immomo.momo.message.receiver.ChatBackgroundReceiver;
import com.immomo.momo.message.sayhi.HeartbeatSvipHiConfigV1;
import com.immomo.momo.message.sayhi.ISayHiDialogLog;
import com.immomo.momo.message.sayhi.MessageParser;
import com.immomo.momo.message.sayhi.SayHiArgs;
import com.immomo.momo.message.sayhi.utils.HeartbeatHiRecordUtil;
import com.immomo.momo.message.sayhi.utils.IGreetPushGuideLog;
import com.immomo.momo.message.sayhi.utils.NewSayHiConst;
import com.immomo.momo.message.sayhi.utils.SayhiPushGuideABTest;
import com.immomo.momo.message.task.GreetLoadMoreNewMessageTask;
import com.immomo.momo.message.task.GreetLoadMoreTask;
import com.immomo.momo.message.task.util.MsgFilterUtil;
import com.immomo.momo.message.view.ChatHalfGreetHeadView;
import com.immomo.momo.message.view.ChatVerticalSlideRelationLayout;
import com.immomo.momo.message.view.GreetFeedView;
import com.immomo.momo.message.view.GreetPugCardView;
import com.immomo.momo.message.view.NearbyPeopleCardGreetHeadView;
import com.immomo.momo.messages.ChatMsgSaver;
import com.immomo.momo.messages.memcache.ChatMsgMemCache;
import com.immomo.momo.messages.service.SingleMsgService;
import com.immomo.momo.messages.service.SingleMsgServiceV2;
import com.immomo.momo.mulog.pair.MUPairItem;
import com.immomo.momo.multpic.entity.Photo;
import com.immomo.momo.mvp.common.model.ModelManager;
import com.immomo.momo.mvp.common.presenter.ITipsPresenter;
import com.immomo.momo.mvp.message.contract.IMessageLog;
import com.immomo.momo.mvp.message.panel.ChatPanelBusinessType;
import com.immomo.momo.mvp.message.panel.ChatPanelHandler;
import com.immomo.momo.mvp.message.panel.ChatPanelItem;
import com.immomo.momo.mvp.message.view.BaseMessageActivity;
import com.immomo.momo.mvp.message.view.base.ChatInputShowFrom;
import com.immomo.momo.pay.IGetBusinessScene;
import com.immomo.momo.pay.PayVipBootHelper;
import com.immomo.momo.performance.SimpleViewStubProxy;
import com.immomo.momo.permission.PermissionUtil;
import com.immomo.momo.platform.utils.BlockHelper;
import com.immomo.momo.protocol.http.GreetApi;
import com.immomo.momo.protocol.http.MessageApi;
import com.immomo.momo.protocol.http.UserApi;
import com.immomo.momo.protocol.imjson.IMJApi;
import com.immomo.momo.protocol.imjson.IMJMOToken;
import com.immomo.momo.protocol.imjson.dispatch.MsgLogUtil;
import com.immomo.momo.protocol.imjson.event.IMEventReporter;
import com.immomo.momo.protocol.imjson.event.IMOfflineEvent;
import com.immomo.momo.protocol.imjson.log.IMLocalLogger;
import com.immomo.momo.protocol.imjson.receiver.BaseMessageKeys;
import com.immomo.momo.protocol.imjson.receiver.MessageKeys;
import com.immomo.momo.protocol.imjson.util.Debugger;
import com.immomo.momo.service.bean.FolderType;
import com.immomo.momo.service.bean.Message;
import com.immomo.momo.service.bean.MessageMapLocation;
import com.immomo.momo.service.bean.MessageVideoDefArgument;
import com.immomo.momo.service.bean.Preference;
import com.immomo.momo.service.bean.SayhiSession;
import com.immomo.momo.service.bean.Session;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.service.bean.WebApp;
import com.immomo.momo.service.bean.message.Type20Content;
import com.immomo.momo.service.bean.message.Type21Content;
import com.immomo.momo.service.bean.message.Type27Content;
import com.immomo.momo.service.model.FastReplyData;
import com.immomo.momo.service.model.SendMsgParams;
import com.immomo.momo.service.sessions.MessageServiceHelper;
import com.immomo.momo.service.sessions.SessionService;
import com.immomo.momo.service.sessions.SessionUserCache;
import com.immomo.momo.service.singlechat.ChatSettingService;
import com.immomo.momo.service.singlechat.SingleChatSetting;
import com.immomo.momo.service.user.UserService;
import com.immomo.momo.setting.BasicUserInfoUtil;
import com.immomo.momo.setting.activity.HarassGreetingSettingActivity;
import com.immomo.momo.statistics.EVAction;
import com.immomo.momo.statistics.EVPage;
import com.immomo.momo.test.dbcheck.JDBTimeoutHandler;
import com.immomo.momo.util.AppCodec;
import com.immomo.momo.util.DataUtil;
import com.immomo.momo.util.GsonUtils;
import com.immomo.momo.util.OnlineStatusUtils;
import com.immomo.momo.util.StringUtils;
import com.immomo.momo.util.fabricmomo.FabricLogger;
import com.immomo.momo.videochat.friendvideo.friend.FriendQChatInfo;
import com.immomo.momo.videochat.friendvideo.single.ui.FriendQChatActivity;
import com.immomo.svgaplayer.SVGAAnimListenerAdapter;
import com.immomo.svgaplayer.view.MomoSVGAImageView;
import com.immomo.thirdparty.push.PushUtils;

import org.json.JSONObject;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Callable;

import de.greenrobot.event.EventBus;
import io.reactivex.Flowable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * 聊天界面
 *
 * <AUTHOR>
 */
@SuppressLint("LogUse")
public class GreetDialog extends BaseMessageActivity implements IPageDurationHelper, IGetBusinessScene, IGreetView {
    public static final String FROM_SOURCE_HIPSTER = "hipster";


    public static final String REMOTE_USER_ID = "remoteUserID";
    public static final String VIEWMODEL_PEEK = "peek";
    public static final String KEY_VIEWMODEL = "viewmodel";
    public static final String KEY_AUTO_START_FRIEND_QCHAT = "auto_start_friend_qchat";
    public static final String KEY_BUSINESS_TYPE = "key_business_type";
    public static final String KEY_NEED_PULL_FEED_UPDATE = "key_need_pull_feed_update";
    public static final String KEY_SESSION_RECOMMEND_TYPE = "key_session_recommend_type";
    public static final String KEY_FRIEND_CHAT_TYPE = "key_friend_chat_type";
    public static final String TAG_RAISE_FIRE = "TAG_RAISE_FIRE";
    public static final String BUSINESS_TYPE_KLIAO_MATCH = "hongniang";
    private static final String MK_NOTIFICATION_GREET_SHOW_GIFTVIEW = "mkNotification_greet_show_giftView";

    public static final String FEED_SOURCE = "feed_source";
    public static final String MODE = "mode";
    public static final String NEARBY_PEOPLE_CARD = "nearby_people_card";
    public static final String HEART_BEAT_SVIP_CNT = "HEART_BEAT_SVIP_CNT";
    public static final String HEART_BEAT_SVIP_TOAST = "HEART_BEAT_SVIP_TOAST";
    public static final String REFLOW_GIFT_CHECK_SHOW_KEYBOARD = "reflow_gift_check_show_keyboard";

    public static final String QaMessageItemTaskTag = "QA_MESSAGE_ITEM_TASK_TAG";
    private static String[] GiftOptions = new String[]{"赠送礼物", "赠送表情", "赠送会员"};
    private ReflushUserProfileReceiver reflushUserProfileReceiver;
    private FileUploadProgressReceiver fileUploadProgressReceiver;
    public User remoteUser;
    private boolean isSayhiSession = false;
    private boolean isMatchSession = false;
    /**
     * 是否已经迁移招呼到正常对话
     */
    private boolean hasTransferHiSession = false;
    private boolean isReplyed = false;
    private FriendListReceiver friendListReceiver = null;
    private SynCloudMsgReceiver cloudmsgReceiver = null;
    private ChatBackgroundReceiver backgroundReceiver;
    private String viewModel;

    //还未滚动至用户视线里的的未读消息数
    private int unreadScrollCount = 0;
    //未读数和silent数之和
    private int totalUnreadCount = 0;
    //刷新距离时间图标的定时器TAG
    protected final String RefreshTimerTag = "ChatrefreshTimerTag";
    private final String DismissBottomTipsTag = "DismissBottomTipsTag";

    private GreetLoadMoreNewMessageTask loadMoreNewMessageTask;
    //8.7.9 底部提示信息
    private SimpleViewStubProxy<ChatBottomTipView> bottomTipViewSimpleViewStubProxy;
    private ChatVerticalSlideRelationLayout rootView;

    private FrameLayout mGreetHeadContainer;
    @Nullable
    private ChatHalfGreetHeadView mChatHalfGreetHeadView;
    @Nullable
    private NearbyPeopleCardGreetHeadView mNearbyPeopleCardGreetHeadView;
    private final String DismissGreetGiftTag = "DismissGreetGiftTag";

    @Nullable
    private IGreetPresenter mIGreetPresenter;
    private View mInputBackgoundView;
    private ImageView mGreetListMask;
    private AnimatorSet mGreetExitAnimatorSet;
    private boolean isNewIntentSmall = false;
    private Disposable mUserDisposable;

    private GlobalEventManager.Subscriber subscriber;
    private Message lastUnreadGiftMsg;
    private LinearLayout llEdit;
    private ValueAnimator valueAnimator;
    private ValueAnimator showAnimator;

    // 是否和红娘关联的会话消息，用于自己发送消息时添加红娘标识
    private String businessType;
    /**
     * 是否来自文字匹配
     */
    private boolean isFromTextChat = false;
    @Nullable
    private FrameLayout mGreetCardContainer;
    @Nullable
    private View mInputGreetGiftBtn;
    private View mGreetInputContainer;

    private BindPhoneTipView bindPhoneTipView;

    //输入框默认左间距
    private int llEditDefaultMarginLeft = UIUtils.getPixels(-14f);

    @Nullable
    private ChatEditTopNoticeContract.IChatEditTopNoticeView editTopNoticeView;
    // 打招呼礼物面板
    public GreetChatPageGiftManager greetChatPageGiftManager;
    private boolean firstShow = true;
    private ValueAnimator mSmallValueAnimator;
    private ValueAnimator mLargeValueAnimator;
    private static final int LARGE_GREET_UI_HEIGHT = 507;
    private static final int LARGE_NEARBY_CARD_UI_HEIGHT = 386;
    private static final int SMALL_GREET_UI_HEIGHT = 426;
    private int largeUiHeight = LARGE_GREET_UI_HEIGHT;

    /**
     * 显示商业招呼(获取招呼面板引导送礼文案)
     */

    private boolean getGreetTask = false;
    private ITip iTip;

    private boolean isFromNearbyPeople = false;
    private boolean isSendTextMessage = false;

    public AIChatCardManager aiChatCardManager = new AIChatCardManager(viewPriorityManager, this);
    private String normalHint = "礼貌地打个招呼...";

    //发文本时，键盘是否是弹起的状态
    private boolean currentIsKeyBoradShown;
    private GreetMessageResult.HeartbeatSvipData svipHeartBeat = null;

    private boolean needShowKeyboardDelay;  // 是否需要延时启动键盘
    private boolean isOpenHeartbeatSvip; // 是否开启了心动喜欢消息发送
    //push权限引导横幅
    private SimpleViewStubProxy viewStubPushGuide;
    private SimpleViewStubProxy stubHeartbeatSayhi;
    private ConstraintLayout heartbeatHiContainer;
    private MomoSVGAImageView btnHeartbeatSvip;
    private TextView tvHeartbeatSvipCnt;
    private TextView ivPushGuideOpen;
    private TextView tvClose;
    private GreetMessageResult.HiGiftGuide greetHiTopGiftCardData;
    private SimpleViewStubProxy<ConstraintLayout> greetReflowManGiftStub;
    private ConstraintLayout greetReflowManGift;
    private ScaleAnimation reflowGiftAnimation;
    private ReflushVipReceiver refreshVipReceiver;
    // 助聊面板与大表情互斥
    private boolean isPendingShowAIForEmotion = false;

    @Override
    public int getChatType() {
        return Message.CHATTYPE_USER;
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (MsgUnreadProcessor.hasUnread(getMomoID())) {
            resetUnreadMessages();
        }
        startRefreshTimer();
        if (null != msgChatData) {
            msgChatData.onResume();
        }
        MomoKit.getApp().asyncWatchIMService();
        PushUtils.addObserver(PushUtils.TYPE_CHAT, this);
        initPushGuideTips();
    }

    @Override
    protected void initData() {
        super.initData();
        MomoTaskExecutor.executeMessageTask(getTaskTag(), new CheckTextChatFlagTask(getChatId(), fromTextChat -> {
            isFromTextChat = fromTextChat;
            return Unit.INSTANCE;
        }, new Function0<Unit>() {
            @Override
            public Unit invoke() {
                return Unit.INSTANCE;
            }
        }));
    }

    @Override
    protected int getLayoutResource() {
        return R.layout.activity_greet_dialog;
    }

    @Override
    protected void initPaper() {
        super.initPaper();
        PaperFragmentHelper.INSTANCE.add(getSupportFragmentManager(),
                GreetChatTopPaperFragment.newInstance(), R.id.paper_greet_chat_top_container);
    }

    @Override
    protected void initBaseUi() {
        super.initBaseUi();
        mInputBackgoundView = findViewById(R.id.input_bottom_layout_background);
        rootView = (ChatVerticalSlideRelationLayout) findViewById(R.id.root_view);
        mGreetInputContainer = findViewById(R.id.greet_input_container);
        if (!AppKit.getAccountManager().isOnline()) {
            hideInputLayout();
        }
        bottomTipViewSimpleViewStubProxy = new SimpleViewStubProxy<>((ViewStub) findViewById(R.id.chat_bottom_tip_vs));
        bottomTipViewSimpleViewStubProxy.addInflateListener(new SimpleViewStubProxy.OnInflateListener<ChatBottomTipView>() {
            @Override
            public void onInflate(ChatBottomTipView view) {
                MomoMainThreadExecutor.postDelayed(DismissBottomTipsTag, new Runnable() {
                    @Override
                    public void run() {
                        hideBottomTipsView();
                    }
                }, 3000);
            }
        });
        viewStubPushGuide = new SimpleViewStubProxy((ViewStub) findViewById(R.id.tip_push_switch_guide));

        findViewById(R.id.layout_root).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        bindPhoneTipView = (BindPhoneTipView) findViewById(R.id.tip_bind_phone);

        if (HeartbeatSvipHiConfigV1.isUserNewUI()) {
            showHeartbeatTips();
        }
    }

    private void showHeartbeatTips() {
        stubHeartbeatSayhi = new SimpleViewStubProxy((ViewStub) findViewById(R.id.stub_heartbeat_sayhi));
        stubHeartbeatSayhi.addInflateListener(view -> {
            heartbeatHiContainer = (ConstraintLayout) view;
            btnHeartbeatSvip = view.findViewById(R.id.btn_heartbeat_svip);
            tvHeartbeatSvipCnt = view.findViewById(R.id.tv_heartbeat_svip_cnt);
            btnHeartbeatSvip.setOnClickListener(v -> {
                if (!MomoKit.isSVipUser() && !isOpenHeartbeatSvip) {
                    initVipReceiver();
                    PayVipBootHelper.startPayVipPage(this, PayConst.VipType.SVIP_TYPE, SOURCE_HEART_BEAT_HI);
                } else {
                    if (svipHeartBeat != null) {
                        if (svipHeartBeat.remain > 0) {
                            if (svipHeartBeat.enable) {
                                switchHeartbeatBtn(isOpenHeartbeatSvip);
                                HeartbeatHiRecordUtil.btnClick(getMomoID(), svipHeartBeat.remoteGender, isOpenHeartbeatSvip);
                            } else {
                                String toast = svipHeartBeat.toast;
                                if (StringUtils.isNotBlank(toast)) {
                                    Toaster.show(toast);
                                }
                            }
                        } else {
                            Toaster.show("今日剩余心动招呼次数为0，明天再来吧～");
                        }
                    }
                }
            });
        });
        stubHeartbeatSayhi.setVisibility(View.VISIBLE);
        btnHeartbeatSvip.startSVGAAnimAndStepToPercentage(getHeartbeatBtnIcon(true), 1, new SVGAAnimListenerAdapter() {
            @Override
            public void onStart() {
                super.onStart();
                btnHeartbeatSvip.stopAnimCompletely();
                btnHeartbeatSvip.stepToPercentage(1.0, false);
            }
        }, 1.0);
    }

    private void initVipReceiver() {
        if (refreshVipReceiver == null) {
            refreshVipReceiver = new ReflushVipReceiver(this);
            refreshVipReceiver.setReceiveListener(intent -> {
                if (ReflushVipReceiver.ACTION_BECOMESVIP.equals(intent.getAction())) { // 成为svip
                    if (mIGreetPresenter != null) {
                        MomoMainThreadExecutor.postDelayed(hashCode(), () -> mIGreetPresenter.refreshGreetConfigData(), 1000);
                    }
                }
            });
        }
    }

    private void switchHeartbeatBtn(boolean isOpenHeartbeatSvip) {
        switchHeartbeatBtnUIState(isOpenHeartbeatSvip);
        resetEditBg();
        if (aiChatCardManager != null) {
            aiChatCardManager.setHeartbeatHiOpen(this.isOpenHeartbeatSvip);
        }
    }

    /**
     * 刷新心动按钮的 UI
     *
     * @param isOpenHeartbeatSvip
     */
    private void switchHeartbeatBtnUIState(boolean isOpenHeartbeatSvip) {
        btnHeartbeatSvip.stopAnimCompletely();
        String btnUrl = getHeartbeatBtnIcon(isOpenHeartbeatSvip);
        btnHeartbeatSvip.startSVGAAnimAndStepToPercentage(btnUrl, 1, new SVGAAnimListenerAdapter() {
            @Override
            public void onFinished() {
                super.onFinished();
                btnHeartbeatSvip.stopAnimCompletely();
                btnHeartbeatSvip.stepToPercentage(1.0, false);
            }
        }, 0);
        this.isOpenHeartbeatSvip = !isOpenHeartbeatSvip;
    }

    private String getHeartbeatBtnIcon(boolean isOpenHeartbeatSvip) {
        String btnUrl;
        if (com.immomo.momo.util.MomoKit.INSTANCE.isDarkMode(this)) {
            if (isOpenHeartbeatSvip) {
                btnUrl = "hi_heartbeat_close_dark.svga";
            } else {
                btnUrl = "hi_heartbeat_open_dark.svga";
            }
        } else {
            if (isOpenHeartbeatSvip) {
                btnUrl = "hi_heartbeat_close.svga";
            } else {
                btnUrl = "hi_heartbeat_open.svga";
            }
        }
        return btnUrl;
    }

    public void defaultShowInput() {
        mMessageEditText.post(this::showInputMethod);
    }

    @Override
    public String getGiftSource() {
        String giftSource = getIntent().getStringExtra(KEY_SOURCE_DATA);
        if (StringUtils.isNotEmpty(giftSource)) {
            return giftSource;
        } else {
            return null;
        }
    }

    //<editor-fold desc="半屏幕模式">

    /**
     * 由于对话页面是SingleTask需要处理newIntent。
     */
    private void halfModeDealNewIntent() {
        getIntent().putExtra(BaseMessageActivity.EXTRA_KEY_FROM, getFrom());
        isNewIntentSmall = true;
        initGreetHalfMode();
        panelHandler.initView();
    }

    //</editor-fold>

    @Override
    public void onTopTipHide(View view, ITipsPresenter.TipsMessage tipsMessage) {
        super.onTopTipHide(view, tipsMessage);
        try {
            if (MomoKit.getApp().getSqliteInstance() == null) {
                FabricLogger.logException(new Exception(tipsMessage.toString()));
            }
        } catch (Exception ex) {
            MDLog.printErrStackTrace(LogTag.COMMON, ex);
        }
    }

    @Override
    public void onMenuItemClicked(final Object menuItem) {
        if (menuItem instanceof WebApp) {
            WebApp app = (WebApp) menuItem;
            switch (app.appid) {
                case WebApp.ID_PRESENT:
                    if (!bothRelation) {
                        Toaster.show("相互关注之后才能赠送");
                    } else {
                        sendGift();
                    }
                    return;
                case WebApp.ID_VIDEO:
                    takeMessageVideo();
                    return;
                case WebApp.ID_VIDEOCHAT:
                case WebApp.ID_QCHAT:
                    FriendQChatActivity.requestChat(thisActivity(), getChatId(), FriendQChatInfo.TYPE_FRIEND_VIDEO);
                    return;
                case WebApp.ID_VOICE_QCHAT:
                    try {
                        String gotoStr = String.format("[|" + com.immomo.android.module.kliao.gotos.GotoKeys.GOTO_STAR_SQUARE_CHAT_PROFILE + "|{\"momoid\":\"%s\",\"type\":\"3\",\"index\":\"1\",\"source\":\"webapp\",\"chatfrom\":\"voice\"}]", getChatId());
                        ActivityHandler.executeAction(gotoStr, this);
                    } catch (Exception e) {
                    }
                    break;
                case WebApp.ID_TALENT_INVITE: {
                    if (ResourceChecker.needBlockService(ResourceChecker.BUSINESS_TYPE_KLIAO, new ResourceCallbackAdapter() {
                        @Override
                        public void onSuccess() {
                            onMenuItemClicked(new WebApp(WebApp.ID_TALENT_INVITE));
                        }
                    })) {
                        return;
                    }

                    try {
                        String gotoStr = String.format("[|" + com.immomo.android.module.kliao.gotos.GotoKeys.GOTO_STAR_SQUARE_CHAT_PROFILE + "|{\"momoid\":\"%s\",\"source\":\"webapp\"}]", getChatId());
                        ActivityHandler.executeAction(gotoStr, GreetDialog.this);
                    } catch (Exception e) {
                        MDLog.printErrStackTrace(LogTag.COMMON, e);
                    }
                }
                break;
                case WebApp.ID_HONGBAO_PERSONAL_FRIEND: {
                    if (KV.getUserBool(AppConfigV2.SPKeys.KEY_HONGBAO_BOTH_RELATION, true)) {
                        if (!bothRelation) {
                            Toaster.show("相互关注之后才能发送红包");
                        } else {
                            sendHongbao(app);
                        }
                    } else {
                        sendHongbao(app);
                    }
                }
                break;
                default:
                    super.onMenuItemClicked(app);
                    return;
            }
        } else {
            super.onMenuItemClicked(menuItem);
        }
    }

    @Override
    public User getUser(Message message) {
        if (!message.receive) {
            return currentUser;
        } else {
            return remoteUser;
        }
    }

    private void sendHongbao(WebApp menuItem) {
        if (StringUtils.notEmpty(menuItem.gotoStr)) {
            NavigateHelper.startActivityByWebAppGOTO(thisActivity(), menuItem.gotoStr, chatID, getChatType(), null, null);
        }
    }

    private void sendGift() {
        final String[] giftOptions = new String[]{"赠送表情", "赠送会员"};
        MAlertListDialog dialog = new MAlertListDialog(GreetDialog.this, giftOptions);
        dialog.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                if (giftOptions[index].equals(GiftOptions[0])) {
                    String presentUrl = "https://m.immomo.com/inc/mall/my/want?remoteid=" + remoteUser.getId();
                    NavigateHelper.startPassportWebview(thisActivity(), presentUrl);
                } else if (giftOptions[index].equals(GiftOptions[1])) {
                    Intent intent = new Intent(getApplicationContext(), MainEmotionActivity.class);
                    intent.putExtra(MainEmotionActivity.KEY_GIFT_REMOTEID, remoteUser.momoid);
                    startActivity(intent);
                    panelHandler.hide();
                } else if (giftOptions[index].equals(GiftOptions[2])) {
                    NavigateHelper.startPassportWebview(getActivity(), UrlConstant.URL_BUY_VIP + remoteUser.momoid);
                    panelHandler.hide();
                } else {
                    MDLog.i(TAG, "Nothing matched");
                }
            }
        });
        dialog.setSupportDark(true);
        showDialog(dialog);
    }

    @Override
    protected void onChatPanelInflate(ResizableEmoteInputView view) {
        super.onChatPanelInflate(view);
        if (view != null) {
            view.setDeleteTransparent(R.drawable.bg_emote_float);
        }
    }

    @Override
    public void onSendGiftSuccess(long balance) {
        super.onSendGiftSuccess(balance);
    }

    /**
     * 启动定时器每一分钟刷新飞机标
     */
    private void startRefreshTimer() {
        MomoMainThreadExecutor.postDelayed(RefreshTimerTag, new Runnable() {
            @Override
            public void run() {
                refreshDistanceIcon(false);
                startRefreshTimer();
            }
        }, 60000);
    }

    @Override
    protected void initRelationBean() {
        final String momoid = getChatId();
        remoteUser = SessionUserCache.getUser(momoid);
        if (remoteUser == null) {
            remoteUser = new User(momoid);
        }
        if (mUserDisposable != null) {
            mUserDisposable.dispose();
        }
        mUserDisposable = Flowable.fromCallable(new Callable<Integer>() {
                    @Override
                    public Integer call() {

                        IUserModel mUserModel = ModelManager.getInstance().getModel(IUserModel.class);
                        if (mUserModel != null && remoteUser != null) {
                            User cacheUser = mUserModel.getUserFromDB(momoid);
                            if (cacheUser != null && cacheUser.getShowGreet() != -1) {
                                return cacheUser.getShowGreet();
                            }
                        }
                        return -1;
                    }
                }).subscribeOn(Schedulers.from(MMThreadExecutors.INSTANCE.getUser()))
                .observeOn(MMThreadExecutors.INSTANCE.getMain().getScheduler())
                .subscribeWith(new CommonSubscriber<Integer>() {
                    @Override
                    public void onNext(Integer showGreet) {
                        super.onNext(showGreet);
                        if (showGreet != -1) {
                            remoteUser.setShowGreet(showGreet);
                        }
                        loadUserInfoFromHttp(remoteUser);
                        refreshDistanceIcon(false);
                    }
                });
    }

    @Override
    protected String getRemoteName() {
        User user = SessionUserCache.getUser(getChatId());
        if (user != null) {
            return user.getDisplayName();
        }
        return getChatId();
    }

    @Override
    public void refreshAdapterUIThread() {
        msgChatData.notifyDataSetChanged();
        if (mMgsMessageHelper != null) {
            mMgsMessageHelper.canAutoPlayExecute = true;
        }
    }

    /**
     * 从网络加载用户信息并保存到数据库
     */
    private void loadUserInfoFromHttp(final User user) {
        MomoTaskExecutor.executeTask(MomoTaskExecutor.EXECUTOR_TYPE_USER, getTaskTag(), new MomoTaskExecutor.Task<Object, Object, Object>() {

            @Override
            protected void onPreTask() {
                super.onPreTask();

            }

            @Override
            protected Object executeTask(Object[] params) throws Exception {
                UserApi.getInstance().downloadAndSaveSimpleUser(user, user.momoid);
                UserService.getInstance().saveUserSimple(user);
                SessionUserCache.insert(user.momoid, user);
                bothRelation = User.RELATION_BOTH.equals(user.relation);
                return null;
            }

            @Override
            protected void onTaskSuccess(Object o) {
                refreshAdapterUIThread();
                refreshGreetHalfTitle();
                if (isFromFeedLike()) {
                    initMessageInputText();
                }
            }

            @Override
            protected void onCancelled() {
                super.onCancelled();

            }

            @Override
            protected void onTaskFinish() {
                super.onTaskFinish();

            }
        });
    }

    @Override
    public boolean gameTogetherItemCanShow() {
        return (super.gameTogetherItemCanShow() && remoteUser != null && !remoteUser.isOfficial());
    }

    /**
     * 初始化相互关系
     */
    @Override
    public void initRelationship() {
        // 从数据库加载相互关系
        bothRelation = User.RELATION_BOTH.equals(remoteUser.relation);

        if (!AppKit.getAccountManager().isOnline()) {
            return;
        }
        if (Debugger.isDebuggable() && remoteUser.momoid.equals(Debugger.LoggerSessionId)) {
            return;
        }

        // 用户距离和状态
        MomoTaskExecutor.executeTask(MomoTaskExecutor.EXECUTOR_TYPE_USER, getTaskTag(), new MomoTaskExecutor.Task<Object, Integer, Object>() {
            @Override
            protected Object executeTask(Object[] params) throws Exception {
                //从网络请求相互关系
                try {
                    remoteUser.relation = IMJApi.getRelation(remoteUser.momoid);
                    bothRelation = User.RELATION_BOTH.equals(remoteUser.relation);
                    publishProgress(0);
                } catch (Exception e) {
                    Log4Android.getInstance().e(e);
                }

                // 获取距离
                try {
                    boolean ignoreFeedUpdate = !getIntent().getBooleanExtra(KEY_NEED_PULL_FEED_UPDATE, true);
                    int guideType = getIntent().getIntExtra(KEY_SESSION_RECOMMEND_TYPE, -1);
                    SingleChatSetting setting = ChatSettingService.getInstance().getOrNew(getChatId());
                    IMJApi.getDistance(setting, remoteUser, ignoreFeedUpdate, guideType, 1);
                    SessionUserCache.update(chatID, remoteUser);
                    // 设置更新时间变量
                    publishProgress(1);
                    UserService.getInstance().updateDistanceAndHiddenMode(remoteUser);
                    publishProgress(3);

                } catch (Exception e) {
                    Log4Android.getInstance().e(TAG, e);
                }
                return null;
            }

            @Override
            protected void onProgressUpdate(Integer... values) {
                if (isDestroyed()) {
                    return;
                }
                if (null != values && values.length > 0) {
                    int value = values[0].intValue();
                    switch (value) {
                        case 1:
                            refreshDistanceIcon(false);
                            broadcastOnlineStatueChange(remoteUser);
                            if (mChatHalfGreetHeadView != null) {
                                mChatHalfGreetHeadView.setData(remoteUser);
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
        });
    }

    private void broadcastOnlineStatueChange(User user) {
        if (user == null || TextUtils.isEmpty(user.momoid) || user.isOfficial()) {
            return;
        }
        Intent it = new Intent(SessionListReceiver.ActionRequestOnlineStatue);
        it.putExtra(SessionListReceiver.KEY_SESSION_ID, MessageServiceHelper.getSessionIdOfSingleChat(user.momoid));
        LocalBroadcastManager.getInstance(thisActivity()).sendBroadcast(it);
    }

    @Override
    public void onTopTipClick(View v, ITipsPresenter.TipsMessage tipsMsg) {
        super.onTopTipClick(v, tipsMsg);
        if (tipsMsg != null && tipsMsg.getId() == ITipsPresenter.TipsMessage.ID_CHAT_SPECIAL_FRIEND) {
            String webUrl = getString(R.string.setting_hide_first_special_tip_url);
            NavigateHelper.startPassportWebview(this, webUrl);
            KV.saveUserValue(Preference.KEY_CHAT_HAS_SHOW_SPECIAL_FRIEND, true);
            removeTips(tipsMsg);
        }
    }

    /**
     * 男性用户回复开场(针对女性回复优化)
     *
     * @param key momoId + remoteMomoId
     */
    protected void initInputHint(String key) {
        if (TextUtils.equals(KV.getUserStr(SPKeys.FemaleGreet.KEY_INPUT_HINT, ""), key) && MomoKit.getCurrentUser().isMale()) {
            mMessageEditText.setHint("认真的回答，更容易被回复");
            KV.removeUserValue(SPKeys.FemaleGreet.KEY_INPUT_HINT);
        }
    }

    private boolean needHideUserStatusLayout() {
        return !AppKit.getAccountManager().isOnline() || (remoteUser != null && remoteUser.official);
    }

    private void refreshDistanceIcon(boolean timeAction) {
        if (needHideUserStatusLayout()) {
            //访客模式不显示距离
            return;
        }
        refreshDistance();
    }

    public void refreshDistance() {
        StringBuilder builder = new StringBuilder();
        if (OnlineStatusUtils.isOpenUserOnline(remoteUser)) {
            builder.append(remoteUser.getOnlineTime(false));
        }
        if (remoteUser.getDistance() != -2) {
            if (OnlineStatusUtils.isOpenUserOnline(remoteUser)) {
                builder.append(" · ");
            }
            builder.append(remoteUser.distanceString);
        }
        int state = View.VISIBLE;
        if (TextUtils.isEmpty(builder.toString())) {
            builder.append("隐身");
            state = View.GONE;
        }
    }

    private List<Message> prepareMessages() {
        if (isInJumpMode) {
            Message jumpMessage = SingleMsgService.getInstance().findMessageById(getMomoID(), jump2messageId, true);
            if (jumpMessage != null && jumpMessage.contentType != Message.CONTENTTYPE_MESSAGE_NOTICE) {
                return jump2Message(jumpMessage);
            } else {
                Toaster.show("消息已被撤销或删除");
                isInJumpMode = false;
            }
        }

        if (lastUnreadGiftMsg != null) {
            if (isSayhiSession) {
                unreadScrollCount = SingleMsgService.getInstance().getLiveHiUnreadCountByRemoteid(remoteUser.momoid);
                totalUnreadCount = unreadScrollCount;
            } else {
                int unreadMessageCount = SingleMsgService.getInstance().getUnreadedCount(remoteUser.momoid);
                int silentCount = SingleMsgService.getInstance().getSilentCountByRemoteid(remoteUser.momoid);
                unreadScrollCount = unreadMessageCount + silentCount;
                totalUnreadCount = unreadScrollCount;
            }
        }
        List<Message> messages = loadMoreMessages(PAGE_SIZE + 1, false, true);
        mIMLogRecorder.logInitPage(messages);
        return messages;
    }

    @Override
    public List<Message> initMessages() {
        isSayhiSession = SessionService.getInstance().hasSayhiSession(remoteUser.momoid);
        isMatchSession = SessionService.getInstance().checkMatchExist(remoteUser.momoid);
        //do not handle SayHiSession in JumpMode
        isInJumpMode = !isSayhiSession && isInJumpMode;

        mIMLogRecorder.logMsg(" isSayhiSession: " + isSayhiSession + " isInJumpMode: " + isInJumpMode);

        if (isSayhiSession) {
            lastUnreadGiftMsg = SingleMsgService.getInstance().getSayhiLastUnreadGiftMessage(remoteUser.momoid);
        } else {
            lastUnreadGiftMsg = SingleMsgService.getInstance().getLastUnreadGiftMessage(remoteUser.momoid);
        }

        List<Message> messages = prepareMessages();
        if (messages.size() > 0) {
            if (!VIEWMODEL_PEEK.equals(viewModel)) {

                ChatMsgSaver.getInstance().updateMessagesIgnore(Message.CHATTYPE_USER, remoteUser.momoid, isSayhiSession);
            } else {
                // 悄悄查看模式，不改变气泡状态。
            }
        }
        return messages;
    }

    @Override
    public void initMessagesFinish(List<Message> messages) {
        // 加载消息
        msgChatData.clearMessage();
        msgChatData.addItemModels(0, messages);
        if (lastUnreadGiftMsg != null) {
            if (unreadScrollCount < 100) {
                MomoTaskExecutor.executeMessageTask(getTaskTag(), new LoadATTask(lastUnreadGiftMsg));
            }
            lastUnreadGiftMsg = null;
        }

        if (!hasMoreMessage) {
            msgChatRecycler.removeOverScroll();
        } else {
            msgChatRecycler.restoreOverScroll();
        }

        tryScrollToJumpPosition();

        aiChatCardManager.setSInitMessageFinish(true);
        aiChatCardManager.onInitMessagesFinish(getChatId());

        mIMLogRecorder.logInitMessageFinish(messages);

        aiChatCardManager.listenLastMessageChange(msgChatData);
    }

    @Override
    protected void initBroadcastReceiver() {
        MessageManager.registerMessageReceiver(
                this.hashCode(),
                this,
                800,
                MessageKeys.Action_UserMessge,
                MessageKeys.Action_HiMessage,
                MessageKeys.Action_UserLocalMessage,
                MessageKeys.Action_MessgeStatus,
                BaseMessageKeys.Action_EmoteUpdates,
                MessageKeys.Action_Logger,
                MessageKeys.Action_UpdateMessage,
                MessageKeys.Action_StarQChat_RefreshData,
                MessageKeys.ACTION_BIG_DATA_REPLAY,
                MessageKeys.ACTION_SAYHI_VIP,
                MessageKeys.Action_Message_Update_Mode,
                MessageKeys.ACTION_HEART_BEAT_SVIP
        );

        friendListReceiver = new FriendListReceiver(this);
        fileUploadProgressReceiver = new FileUploadProgressReceiver(this);
        reflushUserProfileReceiver = new ReflushUserProfileReceiver(this);
        cloudmsgReceiver = new SynCloudMsgReceiver(this);
        backgroundReceiver = new ChatBackgroundReceiver(this);

        cloudmsgReceiver.setReceiveListener(new CloudMsgReceiverListener(this));
        friendListReceiver.setReceiveListener(new FriendListReciverListener(this));
        fileUploadProgressReceiver.setReceiveListener(new FileUploadProgressReceiverListener(this));
        reflushUserProfileReceiver.setReceiveListener(new RefreshUserProfileListener(this, remoteUser));
        backgroundReceiver.setReceiveListener(new ChatBackgroundReceiverListener(this));
    }

    @Override
    public int getBusinessScene() {
        return IGetBusinessScene.USER_MESSAGE;
    }

    //滚到@位置
    private class LoadATTask extends MomoTaskExecutor.Task<Object, Object, List<Message>> {
        private int atPos = -1;
        private Message lastUnreadGif;

        public LoadATTask(Message lastUnreadGif) {
            super();
            this.lastUnreadGif = lastUnreadGif;
        }

        @Override
        protected List<Message> executeTask(Object... params) {
            List<Message> tempMsgs = msgChatData.getMessageList();
            List<Message> messages = null;
            if (unreadScrollCount > 0) {
                messages = loadMoreMessages(unreadScrollCount, true, false);
                mIMLogRecorder.logLoadMoreMessagesForAt(messages);
                tempMsgs.addAll(0, messages);
            }
            int index = 0;
            if (totalUnreadCount < PAGE_SIZE && tempMsgs.size() >= PAGE_SIZE) {
                //找到未读消息的位置
                index = PAGE_SIZE - totalUnreadCount;
            }
            for (int size = tempMsgs.size(); index < size; index++) {
                if (lastUnreadGif != null) {
                    Message msg = tempMsgs.get(index);
                    if (TextUtils.equals(msg.msgId, lastUnreadGif.msgId)) {
                        //有礼物消息优先礼物消息
                        atPos = index;
                    }
                }
            }

            if (null != messages) {
                messages = distinctMessage(messages);
            }
            return messages;
        }

        @Override
        protected void onTaskSuccess(List<Message> messages) {
            if (null != messages) {
                Log4Android.getInstance().d(TAG, "LoadATTask size--" + messages.size() + " atPos--" + atPos);
                msgChatData.addItemModels(0, messages);
            }
            if (atPos < 0) {
                atPos = msgChatData.getCount() - 1;
            }
            final int headerCount = msgChatData.getHeaderViewsCount();
            MomoMainThreadExecutor.post(new Runnable() {
                @Override
                public void run() {
                    msgChatRecycler.smoothScrollToPosition(atPos + headerCount);
                    // 跳转后播放动画
                    if (lastUnreadGif != null) {
                        EventBus.getDefault().post(new DataEvent<>(PaperEvent.PAPER_EVENT_PLAY_GIFT_BY_MSG, new MessageGiftData(lastUnreadGif, getTaskTag().toString())));
                    }
                }
            });
        }
    }

    /**
     * 去除消息列表中已经含有的重复消息
     *
     * @param messages
     * @return
     */
    protected ArrayList<Message> distinctMessage(List<Message> messages) {
        ArrayList<Message> newList = new ArrayList<>();
        //临时缓存msgID，效率高
        Set<String> set = new HashSet<>();
        List<Message> tempMessages = msgChatData.getMessageList();
        if (null != tempMessages) {
            for (Message m : tempMessages) {
                set.add(m.msgId);
            }
        }
        for (Message msg : messages) {
            if (set.add(msg.msgId)) {
                newList.add(msg);
            }
        }
        return newList;
    }

    @Override
    protected void clearMessageWhenFromFullSearch() {
        if (msgChatData != null) msgChatData.clearMessage();
    }

    @Override
    protected void tryExitJumpMode() {
        if (isInJumpMode) {
            isInJumpMode = false;
            setHasMoreNewerMessage(false);
            msgChatData.clearMessage();
            initMessagesFinish(initMessages());
        }
    }

    @Override
    protected void tryScrollToJumpPosition() {
        if (isInJumpMode) {
            jumpToMessageAtPosition();
        }
    }

    @Override
    protected boolean isSayHiChatSession() {
        return isSayhiSession;
    }

    public void preHandleMessages(List<Message> messages, boolean isInit) {
        boolean hasUnread = false;

        for (Message message : messages) {
            setUserToDBMessage(message);
            if (message.receive) {
                if (message.status == Message.STATUS_RECE_UNREADED
                        || message.status == Message.STATUS_IGNORE
                        || message.status == Message.STATUS_IGNORE_HI
                        || message.status == Message.STATUS_IGNORE_HISTORY
                        || message.status == Message.STATUS_IGNORE_LIVE
                        || message.status == Message.STATUS_IGNORE_NEW
                        || message.status == Message.STATUS_RECE_SILENT) {
                    MsgUnreadProcessor.putUnread(getMomoID(), message.msgId);
                    if (message.status == Message.STATUS_RECE_UNREADED) {
                        hasUnread = true;
                    }
                }
                if (message.status != Message.STATUS_CLOUD) {
                    message.status = Message.STATUS_RECEIVER_READED;
                }
            } else if (message.status == Message.STATUS_POSITIONING) {
                FromMessageGetLocationCallBack.getInstance(message.msgId).addCallback(new GetLocationFinishCallback(message));
            }

        }

        Log4Android.getInstance().i(TAG, "--------hasUnreaded=" + hasUnread);

        if (msgChatData.isEmpty() && hasUnread) {
            // 如果当前对话是招呼 则移除“招呼通知”
            if (isSayhiSession) {
                MomoKit.getApp().removeSayhiNotify();
            } else {
                // 清除通知栏提示
                MomoKit.getApp().removeMessageNotify();
            }
        }

        sendHasReadMark();
    }

    @Override
    protected List<Message> jump2Message(Message jumpMessage) {
        if (!isInJumpMode) {
            return new ArrayList<>();
        }

        List<Message> smallerIdMessages = SingleMsgServiceV2.getService().findMessageBy(
                getMomoID(),
                JUMP_HALF_PAGE_SIZE + 1,
                jumpMessage,
                false,
                false
        );
        if (smallerIdMessages != null && smallerIdMessages.size() == JUMP_HALF_PAGE_SIZE + 1) {
            smallerIdMessages.remove(0);
            hasMoreMessage = true;
        } else {
            hasMoreMessage = false;
        }

        List<Message> largerIdMessage = SingleMsgServiceV2.getService().findMessageBy(
                getMomoID(),
                JUMP_HALF_PAGE_SIZE + 1,
                jumpMessage,
                true,
                false
        );
        if (largerIdMessage != null && largerIdMessage.size() == JUMP_HALF_PAGE_SIZE + 1) {
            largerIdMessage.remove(JUMP_HALF_PAGE_SIZE);
            setHasMoreNewerMessage(true);
        } else {
            setHasMoreNewerMessage(false);
        }

        jump2messagePos = smallerIdMessages != null ? smallerIdMessages.size() : 0;

        List<Message> messages = new ArrayList<>();
        if (smallerIdMessages != null) {
            messages.addAll(smallerIdMessages);
        }
        messages.add(jumpMessage);
        messages.addAll(largerIdMessage);

        preHandleMessages(messages, true);

        mIMLogRecorder.logInitPageForJumpMode(messages);

        return messages;
    }

    /**
     * 从数据库加载信息到适配器
     *
     * @return <p/>
     * 默认读取21条消息记录，如果能读到21条，向界面添加20条记录，并标记“还有更多消息”；
     * 反之则读到多少条就像界面添加多少条，并标记“没有更多消息”
     */
    public List<Message> loadMoreMessages(int listCount, boolean ignoreLoadMoreBtn, boolean isInit) {
        if (listCount <= 0) {
            return new ArrayList<>();
        }
        long id = JDBTimeoutHandler.checkMe();
        List<Message> messages = null;

        if (isSayhiSession) {
            //SayHiSession沿用之前的方法
            messages = SingleMsgService.getInstance().findSayhiMessageByRemoteId(getMomoID(), msgChatData.getCount(), listCount);

            if (messages.size() > 0) {
                // 如果招呼表和正常消息表都有数据需要迁移招呼表数据到正常消息表，并从正常消息表中读取数据
                int count = SingleMsgService.getInstance().getMessageCount(getMomoID());
                if (count > 0) {
                    SingleMsgService.getInstance().transferSayhi2Chat(getMomoID());
                    messages = loadMessagesFromNormalTable(listCount, isInit);
                    isSayhiSession = false;
                } else {
                    //处理筛选出来的消息中的骚扰消息
                    if (FrequentPreferenceHelper.i().isHarassGreetingOpen()
                            && isFromNormalHiActivity()) {
                        MessageParser.INSTANCE.extraHarassMessage(messages, getRemoteId(), false);
                    }
                }
            }
        } else {
            messages = loadMessagesFromNormalTable(listCount, isInit);
        }

        if (!ignoreLoadMoreBtn) {
            if (messages.size() > PAGE_SIZE) {
                messages.remove(0);
                hasMoreMessage = true;
            } else {
                hasMoreMessage = false;
            }
        }

        messages = MsgFilterUtil.distinct(messages, msgChatData.getMessageList());
        // 对话列表先接收到新消息，该新消息还未完成入库。需要合并两个数据集合并排序。
        if (isInit && msgChatData.getCount() != 0) {
            messages.addAll(msgChatData.getMessageList());
            Collections.sort(messages, new Message.MsgComparatorByTime());
        }
        unreadScrollCount -= messages.size();
        preHandleMessages(messages, isInit);
        JDBTimeoutHandler.unCheckMe(id);
        return messages;
    }

    /**
     * 从正常招呼表里加载数据
     *
     * @param count
     * @return
     */
    private List<Message> loadMessagesFromNormalTable(int count, boolean isInit) {
        Message oldestMsg = msgChatData.isEmpty() ? null : msgChatData.getMessageList().get(0);
        return SingleMsgServiceV2.getService().findMessageBy(getMomoID(), count, oldestMsg, false, isInit);
    }

    private void setUserToDBMessage(Message message) {
        if (message.owner == null) {
            if (message.receive) {
                message.owner = remoteUser;
            } else {
                message.owner = currentUser;
            }
        }
    }

    private Message setUserToNewMessage(Message message) {
        if (message == null) {
            return null;
        }

        if (message.receive) {
            message.owner = remoteUser;
            // 添加到界面后，消息体设置为已读。保存在数据库之前，要特别注意不能改变它的状态。
            if (!VIEWMODEL_PEEK.equals(viewModel)) {
                message.status = Message.STATUS_RECEIVER_READED;
            }
        } else {
            message.owner = currentUser;
        }

        return message;
    }

    private void sendHasReadMark() {
        MsgUnreadProcessor.removeNotify(getMomoID());
        if (!VIEWMODEL_PEEK.equals(viewModel)) {
            MsgUnreadProcessor.sendHasRead(getMomoID(), isSayhiSession);
        } else {
            // 悄悄查看模式不发已读，也不标记消息未已读状态
        }
    }

    @Override
    public synchronized void closeDialog() {
        super.closeDialog();
    }

    @Override
    protected String getGiftSceneId() {
        return GiftCategoryConstants.SINGLE_CHAT;
    }

    @Override
    public boolean onMessageReceive(Bundle bundle, String action) {
        switch (action) {
            case MessageKeys.Action_UserMessge:
            case MessageKeys.Action_HiMessage:
            case MessageKeys.Action_Logger: {
                boolean isForeground = isForeground();
                if (handleUserMessageReceive(bundle, isForeground)) {
                    return false;
                }
                // 界面在前台，才拦截广播
                return isForeground;
            }
            case MessageKeys.Action_MessgeStatus:
                if (bundle.getInt(MessageKeys.Key_ChatType) == Message.CHATTYPE_USER) {
                    String remoteId = bundle.getString(MessageKeys.Key_RemoteId);
                    if (invalidRemoteIdOrChatID(remoteId)) {
                        return false;
                    }

                    String type = bundle.getString(MessageKeys.Key_Type);
                    // 已读状态
                    if (MessageKeys.MsgStatus_Readed.equals(type)) {
                        dealReadedMark(bundle.getStringArray(MessageKeys.Key_MessageId));
                    } else { // 其他类型状态
                        dealStatusMark(type, bundle.getString(MessageKeys.Key_MessageId), bundle);
                    }
                    return false;
                }
                break;
            case MessageKeys.Action_Message_Update_Mode:
                String remoteId = bundle.getString(MessageKeys.Key_RemoteId);
                if (invalidRemoteIdOrChatID(remoteId)) {
                    return false;
                }
                String msgId = bundle.getString(MessageKeys.Key_MessageId);
                String mode = bundle.getString(Key_Mode);
                int status = bundle.getInt(KEY_STATUS);
                updateMode(msgId, mode, status);
                break;
            case BaseMessageKeys.Action_EmoteUpdates:
                Log4Android.getInstance().i(TAG, "Action_EmoteUpdates---------------");
                refreshAdapter();
                return true;
            case MessageKeys.Action_UserLocalMessage: {
                if (invalidRemoteIdOrChatID(bundle.getString(MessageKeys.Key_RemoteId))) {
                    return false;
                }
                Message message = (Message) bundle.getSerializable(MessageKeys.Key_MessageObject);
                setUserToNewMessage(message);
                addRemoteMessage(msgChatData, message);
                return true;
            }
            case MessageKeys.Action_UpdateMessage:
                if (bundle.getInt(MessageKeys.Key_ChatType) == Message.CHATTYPE_USER) {
                    if (invalidRemoteIdOrChatID(bundle.getString(MessageKeys.Key_RemoteId))) {
                        return false;
                    }
                    updateAdapterMessage(bundle.getString(MessageKeys.Key_MessageId), bundle.getParcelable(MessageKeys.Key_MessageObject));
                    if (bundle.getBoolean(MessageKeys.Key_Withdraw)) {
                        doRetractMes(bundle.getString(MessageKeys.Key_MessageId));
                    }
                    return true;
                }
                break;
            case MessageKeys.Action_StarQChat_RefreshData:
                Message message = bundle.getParcelable(MessageKeys.Action_StarQChat_RefreshData);
                //pullMessageInWindow(message);
                addMessageToAdapter(message);
                break;
            case MessageKeys.ACTION_HEART_BEAT_SVIP:
                String toast = bundle.getString(HEART_BEAT_SVIP_TOAST, "");
                int remainCnt = bundle.getInt(HEART_BEAT_SVIP_CNT, 0);
                if (StringUtils.isNotBlank(toast)) {
                    Toaster.show(toast);
                }
                if (this.isOpenHeartbeatSvip) { // 当当前处于关闭状态的时候则不用重新关闭
                    switchHeartbeatBtn(true);
                }
                svipHeartBeat.remain = remainCnt;
                resetHeartbeatCnt(svipHeartBeat.remain);
                break;
            case MessageKeys.ACTION_SAYHI_VIP:
                int type = bundle.getInt("type");
                if (!MomoKit.isSVipUser() && type == 1) {
                    PayVipBootHelper.startPayVipPage(
                            getGreetContext(),
                            PayConst.VipType.SVIP_TYPE,
                            SOURCE_SVIP_SAY_HI
                    );
                }
                break;
            default:
                break;
        }

        return false;
    }

    @Override
    protected void reSendMessage(Message message) {
        Map<String, String> extraData = message.getExtraData();
        if (extraData != null && extraData.containsKey(SayHiArgs.EXTRA_HEART_BEAT_HI)) { // 心动招呼处理
            Map<String, String> extra = message.getExtra();
            if (extra != null && !extra.containsKey(SayHiArgs.EXTRA_HEART_BEAT_HI)) {
                String key = SayHiArgs.EXTRA_HEART_BEAT_HI;
                extra.put(key, extraData.get(key));
            }
        }
        super.reSendMessage(message);
    }

    private void updateMode(String msgId, String mode, int status) {
        if (!TextUtils.isEmpty(msgId)) {
            Message message = msgChatData.getMessage(msgId);
            if (message != null) {
                int index = msgChatData.getMessageList().indexOf(message);
                message.status = status;
                message.safePutExtraData(Key_Mode, mode);
                msgChatData.updateMessage(index, message);
                msgChatData.notifyItemModel(index);
            }
        }
    }

    /**
     * 处理新的用户消息
     *
     * @param bundle
     * @return
     */
    private boolean handleUserMessageReceive(Bundle bundle, boolean isForeground) {
        String remoteId = bundle.getString(MessageKeys.Key_RemoteId);
        if (invalidRemoteIdOrChatID(remoteId)) {
            MsgLogUtil.logDebug("remoteId 不一致，不分发消息");
            return true;
        }

        @SuppressWarnings("unchecked")
        List<Message> list = bundle.getParcelableArrayList(MessageKeys.Key_MessageArray);
        if (list == null || list.isEmpty()) {
            if (isForeground) {
                IMEventReporter.eventOfflineTime(IMOfflineEvent.MSG_UI_NOTIFY_CHAT_LIST_EMPTY,
                        MUPairItem.id("GreetDialog"));
            }
            return true;
        }
        boolean hasType21GuideMsg = false;

        mIMLogRecorder.logReceiveNewMessages(list);

        for (Message message : list) {
            String msgId = message.msgId;
            int contentType = message.contentType;
            if (contentType != Message.CONTENTTYPE_MESSAGE_NOTICE
                    && contentType != Message.CONTENTTYPE_MESSAGE_UPDATE_NOTICE) {
                if (message.status != Message.STATUS_RECEIVER_READED && message.receive) {
                    MsgUnreadProcessor.putUnread(getMomoID(), msgId);
                }
            }
            setUserToNewMessage(message);
            // 自己发送的礼物,或者接收方是自己的礼物,需要播动画
            if (message.isGiftMsg()) {
                EventBus.getDefault().post(new DataEvent<>(PaperEvent.PAPER_EVENT_PLAY_GIFT_BY_MSG, new MessageGiftData(message, getTaskTag().toString())));
            }

            if (message.isGiftMissionMsg()) {
                if (!message.receive) {
                    chatGiftPresenter.updateBalance();
                }
            }

            if (message.receive && !bundle.getBoolean("isFromFriendQchatFinish", false)) {
                updateRemoteUserDistance(message);
            }

            if (contentType == Message.CONTENTTYPE_MESSAGE_NOTICE_PIC && !hasType21GuideMsg) {
                Type21Content type21Content = (Type21Content) message.messageContent;
                if (type21Content != null) {
                    hasType21GuideMsg = type21Content.hasGuideStyle();
                }
            }
        }

        // 删除相邻的Type21 style引导消息。
        if (hasType21GuideMsg) {
            if (msgChatData.getCount() > 1) {
                Message lastShowMessage = msgChatData.getMessage(msgChatData.getCount() - 1);
                if (lastShowMessage.contentType == Message.CONTENTTYPE_MESSAGE_NOTICE_PIC) {
                    Type21Content type21Content = (Type21Content) lastShowMessage.messageContent;
                    if (type21Content != null && type21Content.hasGuideStyle()) {
                        deleteMessage(lastShowMessage);
                    }
                }
            }
        }
        addRemoteMessageList(msgChatData, list);

        // 界面在前台状态，才已读
        if (isForeground()) {
            sendHasReadMark();
        }

        return false;
    }

    /**
     * onMessageReceive中，处理相关消息，首先判断chatID或remoteId是否无法获取
     * 如无法获取代表为非法\失效消息，对于此类消息中断后续逻辑，防止异常出现
     */
    private boolean invalidRemoteIdOrChatID(String remoteId) {
        return (StringUtils.isEmpty(chatID) || !chatID.equals(remoteId));
    }

    private void updateRemoteUserDistance(Message message) {
        //好友快聊消息不更新距离（自己插的数据）
        if (message.contentType == Message.CONTENTTYPE_MESSAGE_FRIEND_QCHAT) {
            return;
        }
        Date oldDate = remoteUser.getLocationTimestamp();
        long oldLocTime = oldDate == null ? 0 : oldDate.getTime();

        Date messageDate = message.distanceTime;
        long messageLocTime = messageDate == null ? 0 : messageDate.getTime();

        boolean needShowAnim = false;

        remoteUser.setLocationTimestamp(messageDate);
        remoteUser.setDistance(message.distance);

        if (message.distance >= 0 && (System.currentTimeMillis() - messageLocTime) < 15 * 60 * 1000 && (System.currentTimeMillis() - oldLocTime) > 15 * 60 * 1000) {
            needShowAnim = true;
        }

        refreshDistanceIcon(needShowAnim);

    }

    /**
     * 处理消息已读状态
     *
     * @param msgIds
     */
    private void dealReadedMark(String[] msgIds) {
        if (!DataUtil.hasValue(msgIds)) { // 老版本已读消息协议
            for (Message message : msgChatData.getMessageList()) {
                if (!message.receive && message.status == Message.STATUS_SENDED) {
                    message.status = Message.STATUS_SEND_READED;
                }
            }
        } else { // 新版本已读
            List<Message> messages = msgChatData.getMessageList();
            for (String msgId : msgIds) {
                int position = messages.indexOf(new Message(msgId));
                if (position > -1) {
                    messages.get(position).status = Message.STATUS_SEND_READED;
                }
            }
        }
        refreshAdapter();
    }

    private void updateAdapterMessage(String msgId, Message newMsg) {
        Message msg = new Message(msgId);
        int index = msgChatData.getPosition(msg);
        if (index >= 0) {
            if (newMsg == null) {
                if (isSayhiSession) {
                    msg = SingleMsgService.getInstance().findSayhiMessage(getChatId(), msgId);
                } else {
                    msg = SingleMsgService.getInstance().findMessageById(getChatId(), msgId);
                }
            } else {
                msg = newMsg;
            }
            if (msg != null) {
                msgChatData.replaceItemModel(index, msg);
            }
        }
    }

    /**
     * 处理已读以外的其他消息状态
     */
    public void dealStatusMark(String serverType, String msgId, Bundle bundle) {

        if (!DataUtil.hasValue(msgId)) {
            return;
        }

        // 处理其他类型的状态标记
        int position = msgChatData.getPosition(new Message(msgId));
        Log4Android.getInstance().i(TAG, "position:" + position + "  serverType:" + serverType);
        if (position < 0) {
            return;
        }
        final Message m = msgChatData.getMessage(position);
        if (m == null) {
            return;
        }
        switch (serverType) {
            case MessageKeys.MsgStatus_Success:
                if (m.status != Message.STATUS_SEND_READED) {
                    m.status = Message.STATUS_SENDED;
                }
                break;
            case MessageKeys.MsgStatus_Distance:
                if (bundle != null) {
                    try {
                        m.distance = bundle.getInt(MessageKeys.Key_Distance, -1);
                        long time = bundle.getLong(MessageKeys.Key_DistanceTime, -1);
                        m.distanceTime = time > 0 ? new Date(time) : null;
                        remoteUser.isDeviation = bundle.getInt(MessageKeys.Key_Deviation, 0) == 1;
                    } catch (Exception ignored) {
                    }
                }

                // 距离有效 但没有附带隐身参数时 默认将隐身设置为可见 仅聊天界面使用 不保存
                if (m.distance >= 0 && remoteUser.hiddenmode == User.HIDDENMODE_HIDE_ALL) {
                    remoteUser.hiddenmode = User.HIDDENMODE_VISIBLE;
                }

                updateRemoteUserDistance(m);
                break;
            case MessageKeys.MsgStatus_Sending:
                m.status = Message.STATUS_SENDING;
                String mmid = isSayhiSession ? SayhiSession.SAYHI_MOMOID : remoteUser.momoid;
                Message msg = SingleMsgService.getInstance().getMessage(mmid, msgId);
                if (null != msg) {
                    m.fileName = msg.fileName;
                }
                break;
            case MessageKeys.MsgStatus_Failed:
                m.status = Message.STATUS_FAILED;
                break;
            case MessageKeys.MsgStatus_Failed_Harass:
                m.status = Message.STATUS_FAILED_BLOCK_MSG;
                break;
            case MessageKeys.MsgStatus_Failed_Fraud:   //反欺诈
                m.status = Message.STATUS_FRAUD;
                break;
            default:
                break;
        }
        refreshAdapter();
    }

    @Override
    public void handMessageAction(final Message message, MessageAction action, Object... params) {
        super.handMessageAction(message, action);

        if (thisActivity() == null || thisActivity().isFinishing()) {
            return;
        }

        switch (action) {
            case WaveHand2: {
                deleteMessage(message);
                if (params != null && params.length > 0 && String.class.isInstance(params[0])) {
                    MomoTaskExecutor.executeUserTask(getTaskTag(), new InstantQuizTask((String) params[0]));
                }
                break;
            }
            case Answer: {
                MomoTaskExecutor.executeUserTask(getTaskTag(), new InstantAnswerTask(message));
                break;
            }
            case HarassGreeting: {
                boolean isBlock = FrequentPreferenceHelper.i().isHarassGreetingOpen();
                if (isBlock) {
                    Toaster.show("骚扰拦截已开启，无需重复操作");
                } else {
                    HarassGreetingSettingActivity.startActivity(thisActivity(),
                            HarassGreetingSettingActivity.HARASS_GREETING_OPEN, HarassGreetingSettingActivity.KEY_FROM_CHAT);
                }
                break;
            }
            case Report: {
                quickReport();
                break;
            }
            case DismissType21Guide: {
                if (params != null && params.length > 0 && String.class.isInstance(params[0])) {
                    MomoTaskExecutor.executeUserTask(getTaskTag(), new DeleteMessageTask(message, (String) params[0]));
                }
                break;
            }
            case UpdateMessage: {
                updateMessage(message);
            }
            break;
            case ExposeMessage: {
                MomoTaskExecutor.executeUserTask(getTaskTag(), new ExposeType26Task(message));
            }
            break;
        }
    }

    @Override
    protected void resendHarassGreeting(Message message) {
        MomoTaskExecutor.executeUserTask(getTaskTag(), new SendRelativeMessageTask(message, getSayHiSource()));
    }

    private void quickReport() {
        MAlertListDialog mdialog = new MAlertListDialog(
                GreetDialog.this, R.array.chat_quick_report_dialog_item);
        mdialog.setTitle("确认举报");
        mdialog.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                switch (index) {
                    case 0: {
                        // 举报
                        MomoTaskExecutor.executeInnerTask(getTaskTag(), new ReportOrBlockTask(
                                thisActivity(), currentUser, remoteUser, false, false, BlockHelper.BLOCK_FROM_CHAT) {
                            @Override
                            protected void onTaskSuccess(Boolean result) {
                                super.onTaskSuccess(result);

                                if (result && thisActivity() != null) {
                                    showReportDialog();
                                }
                            }
                        });

                        break;
                    }
                    case 1: {
                        // 举报并拉黑
                        MomoTaskExecutor.executeInnerTask(getTaskTag(), new ReportOrBlockTask(
                                thisActivity(), currentUser, remoteUser, true, false, BlockHelper.BLOCK_FROM_CHAT) {
                            @Override
                            protected Boolean executeTask(String... params) throws Exception {
                                super.executeTask(params);
                                // 拉黑并举报需要删除本地消息
                                if (isSayhiSession) {
                                    SessionService.getInstance().deleteSayhiSession(remoteUser.momoid, true);
                                } else {
                                    String sessionId = Session.getSessionId(remoteUser.momoid, Session.TYPE_CHAT);
                                    if (SessionService.getInstance().checkExist(sessionId)) {
                                        SessionService.getInstance().delete(sessionId, true);
                                    }
                                }
                                return true;
                            }

                            @Override
                            protected void onTaskSuccess(Boolean result) {
                                super.onTaskSuccess(result);

                                if (result) {
                                    showReportAndBlockDialog();
                                }
                            }
                        });
                    }
                    break;
                    case 2:
                        // 取消
                        closeDialog();
                        break;
                }

            }
        });
        mdialog.setSupportDark(true);
        showDialog(mdialog);
    }

    private void showReportDialog() {
        CommonTipDialog tipDialog = new CommonTipDialog(
                thisActivity(), "举报已提交",
                "我们会尽快处理，核实后对方招呼\n等功能将被限制，感谢举报",
                R.drawable.ic_vector_report);
        tipDialog.setCallback(new CommonTipDialog.Callback() {
            @Override
            public void onConfirmed() {
                finish();
            }
        });
        tipDialog.setShowClose(false);
        showDialog(tipDialog);
    }

    private void showReportAndBlockDialog() {
        CommonTipDialog tipDialog = new CommonTipDialog(
                thisActivity(), "举报已提交",
                "对方不会再出现在你的陌陌中，\n我们会尽快合适处理，感谢举报",
                R.drawable.ic_vector_report_and_block);
        tipDialog.setCallback(new CommonTipDialog.Callback() {
            @Override
            public void onConfirmed() {
                Bundle bundle = new Bundle();
                String sessionId = Session.getSessionId(remoteUser.momoid, Session.TYPE_CHAT);
                bundle.putString(SessionListFragment.Key_SessionId, sessionId);
                bundle.putInt(SessionListFragment.Key_SessionType, Session.TYPE_CHAT);
                MomoKit.getApp().dispatchMessage(bundle, SessionListFragment.Action_SessionChanged);
                finish();
            }
        });

        tipDialog.setShowClose(false);
        showDialog(tipDialog);
    }

    private class SendRelativeMessageTask extends BaseDialogTask<Object, Object, Message> {
        private SendMsgParams sendMsgParams = new SendMsgParams();

        public SendRelativeMessageTask(Message message, String source) {
            sendMsgParams.setText(message.getContent());
            if (message.messageContent instanceof Type20Content) {
                sendMsgParams.setFeedId(((Type20Content) message.messageContent).feedId);
            } else if (message.messageContent instanceof Type27Content) {
                sendMsgParams.setFeedId(((Type27Content) message.messageContent).feedid);
            }
            sendMsgParams.setRemoteId(message.remoteId);
            sendMsgParams.setSource(source);
            sendMsgParams.setMessageId(message.msgId);
        }

        public SendRelativeMessageTask(SendMsgParams msgParams) {
            sendMsgParams = msgParams;
        }


        @Override
        protected Message executeTask(Object... params) throws Exception {
            Message relativeMsg = MessageApi.getInstance().sendRelativeMessage(sendMsgParams);
            updateMessageStatus(relativeMsg);
            return relativeMsg;
        }

        @Override
        protected void onTaskSuccess(Message message) {
            super.onTaskSuccess(message);

            dealStatusMark(MessageKeys.MsgStatus_Success, sendMsgParams.getMessageId(), null);
        }

    }

    @Nullable
    @Override
    public String getSayHiSource() {
        if (isFirstMessage()) {
            if (getIntent() != null) {
                getIntent().putExtra(BaseMessageActivity.EXTRA_KEY_FROM, PageStepHelper.INSTANCE.getFeedUpStepConfig().getLogSource());
                getIntent().putExtra(com.immomo.framework.base.BaseActivity.KEY_FROM, PageStepHelper.INSTANCE.getFeedUpStepConfig().getLogSource());
            }

            return SayHiMatcher.buildSayHiSourceByIntent(getIntent(),
                    true, "", String.valueOf(ChatActivity.SHOW_MODE_GREET_HALF), 0, "");
        }

        return null;
    }

    private String getChatSource() {
        if (isFromFeedLike()) {
            return getIntent().getStringExtra(FEED_SOURCE);
        }
        return "";
    }

    @Override
    protected GotoCallback handleWebAppQuiz() {
        return new GotoCallback() {
            @Override
            public void callBack(String jsonStr) {
                MomoTaskExecutor.executeUserTask(getTaskTag(), new InstantQuizTask(jsonStr));
            }

            @Override
            public void errorCallBack(Exception e) {

            }
        };
    }

    /**
     * 语音播放完毕之后的操作
     *
     * @param obj
     */
    protected boolean onAudioCompleted(Message obj) {
        // 如果下一条是未播放的语音消息，则继续往下播放
        int currentMessageProstion = msgChatData.getPosition(obj);
        if (++currentMessageProstion < msgChatData.getCount()) {
            Message msg = msgChatData.getMessage(currentMessageProstion);
            if (msg != null && msg.receive && msg.contentType == Message.CONTENTTYPE_MESSAGE_AUDIO && !msg.isPlayed) {
                AudioChildItemModel.playAudio(msg, this);
                return true;
            }
        }

        return false;
    }

    @Override
    protected void initInternal() {
        getScreenSize();
        viewModel = getIntent().getStringExtra(KEY_VIEWMODEL);

        im = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
        audiomanager = (AudioManager) getSystemService(AUDIO_SERVICE);

        if (getIntent().getBooleanExtra(KEY_AUTO_START_FRIEND_QCHAT, false) && !isFinishing()) {
            FriendQChatActivity.requestChat(this, getChatId(),
                    getIntent().getIntExtra(KEY_FRIEND_CHAT_TYPE, FriendQChatInfo.TYPE_FRIEND_VIDEO));
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (getIntent().getBooleanExtra(KEY_AUTO_START_FRIEND_QCHAT, false)) {
            FriendQChatActivity.requestChat(this, getChatId(),
                    getIntent().getIntExtra(KEY_FRIEND_CHAT_TYPE, FriendQChatInfo.TYPE_FRIEND_VIDEO));
        }

        halfModeDealNewIntent();
    }

    @Override
    protected void onListViewPullToRefresh() {
        msgChatRecycler.setLoadMoreStart();
        MomoTaskExecutor.executeMessageTask(getTaskTag(), new GreetLoadMoreTask(this));
    }

    @Override
    protected void onListViewLoadMore() {
        if (loadMoreNewMessageTask != null && !loadMoreNewMessageTask.isCancelled()) {
            loadMoreNewMessageTask.cancel(true);
            loadMoreNewMessageTask = null;
        }
        if (msgChatData == null || msgChatData.getCount() == 0) {
            msgChatRecycler.setLoadMoreComplete();
            return;
        }
        loadMoreNewMessageTask = new GreetLoadMoreNewMessageTask(this);
        MomoTaskExecutor.executeMessageTask(getTaskTag(), loadMoreNewMessageTask);
    }

    @Override
    protected void resetUnreadMessages() {
        sendHasReadMark();
        if (isSayhiSession) {
            MomoKit.getApp().removeSayhiNotify();
        } else {
            MomoKit.getApp().removeMessageNotify();
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        getWindow().setFormat(PixelFormat.TRANSLUCENT);

        businessType = getIntent().getStringExtra(KEY_BUSINESS_TYPE);

        super.onCreate(savedInstanceState);

        if (currentUser == null || TextUtils.isEmpty(chatID)) {
            return;
        }

        dealHalfModeTop();
        initGreetHalfMode();

        rootView.setCallback(new ChatVerticalSlideRelationLayout.VideoVerticalSlideCallback() {

            @Override
            public boolean canSlideDown(View child) {
                return !panelHandler.isPanelShow() && child == rootLayout
                        && !isKeyboardShown && (greetChatPageGiftManager != null && !greetChatPageGiftManager.isGiftPanelShowing())
                        && (msgChatData.getCount() == 0 || msgChatRecycler.getFirstVisiblePosition() == 0);
            }

            @Override
            public void onSettlingDown() {
                rootLayout.setVisibility(View.GONE);
                finish();
            }

            @Override
            public void onViewDragStateChanged(int state) {
                if (state == ViewDragHelper.STATE_DRAGGING) {
                    GreetDialog.super.hideAllInputMethod();
                }
            }
        });

        EventBus.getDefault().register(this);
        initInputHint(remoteUser.getMomoid() + MomoKit.getCurrentUser().getMomoid());
        initBindPhoneTip();
        initGreetAdapterListener();

    }

    private void initBindPhoneTip() {
        bindPhoneTipView.setMode(MODE_IM);
        bindPhoneTipView.updateStatus();
    }

    @Override
    protected void initEvents() {
        super.initEvents();
        subscriber = new GlobalEventManager.Subscriber() {
            @Override
            public void onGlobalEventReceived(GlobalEventManager.Event event) {
                if (event == null) {
                    return;
                }
                Map<String, Object> msg = event.getMsg();

                switch (event.getName()) {
                    case MK_NOTIFICATION_GREET_SHOW_GIFTVIEW:
                        if (msg != null) {
                            Object aSwitch = msg.get("switch");
                            if (aSwitch instanceof Integer) {
                                if (((Integer) aSwitch) == 1 && mIGreetPresenter != null) {
                                    showGreetGiftPanelView(false);
                                }
                            }
                        }
                        break;
                    default:
                        break;
                }
            }
        };
        GlobalEventManager.getInstance().register(subscriber, GlobalEventManager.EVN_NATIVE);
    }

    @Override
    protected void onStart() {
        super.onStart();
        Log4Android.getInstance().i(TAG, "onStart");
    }

    @Override
    protected void onPause() {
        super.onPause();
        MomoMainThreadExecutor.cancelAllRunnables(RefreshTimerTag);
        if (isInitialized()) {

            if (isMatchSession && isReplyed) {
                SessionService.getInstance().updateSessionFolderType(remoteUser.momoid, FolderType.Default);
            }

            // 发送广播告知界面当前用户对话已经结束
            Bundle bundle = new Bundle();
            bundle.putString(SessionListFragment.Key_SessionId, MessageServiceHelper.getSessionIdOfSingleChat(remoteUser.momoid));
            bundle.putString(SessionListFragment.Key_ChatId, remoteUser.momoid);
            bundle.putInt(SessionListFragment.Key_SessionType, Session.TYPE_CHAT);
            if (isFromHiActivity()) {
                bundle.putBoolean(SessionListFragment.Key_SessionBackFromSayHi, true);
            } else {
                bundle.putBoolean(SessionListFragment.Key_SessionBackFromSayHi, false);
            }
            MomoKit.getApp().dispatchMessage(bundle, SessionListFragment.Action_SessionChanged);

            // 本次是招呼会话，但是却不是从招呼列表进入的。那么如果回复过消息的话，应该通知sessionlist更新招呼会话
            if (isSayhiSession && !isFromHiActivity()) {
                bundle = new Bundle();
                bundle.putString(SessionListFragment.Key_SessionId, Session.ID.SayhiSession);
                bundle.putInt(SessionListFragment.Key_SessionType, Session.TYPE_SAYHI);
                MomoKit.getApp().dispatchMessage(bundle, SessionListFragment.Action_SessionChanged);
            }
        }

        if (isSayhiSession && isReplyed) {
            isSayhiSession = false;
            //如果回复过招呼，则把招呼的内存缓存删除，避免外露的残留
            ChatMsgMemCache.INSTANCE.clearBySession(Session.ID.SayhiSession);
        }
        PushUtils.removeObserver(PushUtils.TYPE_CHAT);
        aiChatCardManager.releaseAnim();
    }

    /**
     * 是否来自招呼列表
     *
     * @return
     */
    private boolean isFromHiActivity() {
        return EXTRA_VALUE_FROM_HIACTIVITY.equals(chatFrom) || EXTRA_VALUE_FROM_HARASS_HI_ACTIVITY.equals(chatFrom);
    }

    private boolean isFromNormalHiActivity() {
        return EXTRA_VALUE_FROM_HIACTIVITY.equals(chatFrom);
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().unregister(this);
        if (btnHeartbeatSvip != null) {
            btnHeartbeatSvip.stopAnimCompletely();
        }
        if (reflowGiftAnimation != null) {
            reflowGiftAnimation.cancel();
        }
        if (mIGreetPresenter != null) {
            mIGreetPresenter.onDestroy();
        }
        if (mUserDisposable != null) {
            mUserDisposable.dispose();
        }
        cancelDismissKeyBoardCheck();
        super.onDestroy();
        if (valueAnimator != null && valueAnimator.isRunning()) {
            valueAnimator.cancel();
        }
        if (showAnimator != null && showAnimator.isRunning()) {
            showAnimator.cancel();
        }
        GlobalEventManager.getInstance().unregister(subscriber, GlobalEventManager.EVN_NATIVE);
        if (refreshVipReceiver != null) {
            unregisterReceiver(refreshVipReceiver);
        }
        unregistReceiver(friendListReceiver);
        unregistReceiver(fileUploadProgressReceiver);
        unregistReceiver(reflushUserProfileReceiver);
        unregistReceiver(cloudmsgReceiver);
        unregistReceiver(backgroundReceiver);
        TipManager.unbindActivity(getActivity());

        MomoTaskExecutor.cancleAllTasksByTag(TAG_RAISE_FIRE);
        MomoTaskExecutor.cancleAllTasksByTag(getTaskTag());
        MomoMainThreadExecutor.cancelAllRunnables(DismissBottomTipsTag);
        MomoMainThreadExecutor.cancelAllRunnables(DismissGreetGiftTag);
        MomoMainThreadExecutor.cancelAllRunnables(QaMessageItemTaskTag);
        MomoMainThreadExecutor.cancelAllRunnables(hashCode());
        hideBottomTipsView();
        if (greetChatPageGiftManager != null) {
            greetChatPageGiftManager.onDestroy();
        }
        viewPriorityManager.destroy();
        stopLargeGreetAnim();
        stopSmallGreetAnim();
        aiChatCardManager.onDestroy();
        if (bindPhoneTipView != null) {
            bindPhoneTipView.release();
        }
    }

    @Override
    public void finish() {
        if (isFromNearbyPeople && isSendTextMessage) {
            setResult(RESULT_OK);
        }
        super.finish();
        GreetDialog.this.overridePendingTransition(R.anim.normal, R.anim.layout_alpha_out);
    }

    private void unregistReceiver(BroadcastReceiver receiver) {
        if (receiver != null) {
            unregisterReceiver(receiver);
        }
    }

    @Override
    protected void onProfileButtonClicked(View v) {
        Intent intent = new Intent(thisActivity(), ChatSettingActivity.class);
        intent.putExtra(ChatSettingActivity.KEY_MOMOID, getIntent().getStringExtra(REMOTE_USER_ID));
        intent.putExtra(ChatSettingActivity.KEY_CHAT_ID, chatID);
        intent.putExtra(ChatSettingActivity.KEY_FROM_SAYHI, true);
        startActivity(intent);
    }

    @Override
    public void updateMessage(Message message) {
        if (!VIEWMODEL_PEEK.equals(viewModel)) {
            super.updateMessage(message);
        }
    }

    @Override
    public String getChatId() {
        return getIntent().getStringExtra(GreetDialog.REMOTE_USER_ID);
    }

    @Override
    public Message sendTextMessage(String textContent) {
        tryExitJumpMode();

        Message message = MessageHelper.getInstance().packetTextMessage(textContent, remoteUser, null, Message.CHATTYPE_USER);
        if (isFirstMessage()) {
            // 首条消息
            addSayhiSource(message);
        }
        addMessageExtra(message);
        isSendTextMessage = true;
        currentIsKeyBoradShown = true;
        return message;
    }

    @Override
    protected void buildMessageWhenUserSend(Message message) {
        if (isOpenHeartbeatSvip) { // 心动招呼
            message.safePutExtraData(SayHiArgs.EXTRA_HEART_BEAT_HI, "1");
            message.safePutExtra(SayHiArgs.EXTRA_HEART_BEAT_HI, "1");
        }
    }

    private void addMessageExtra(Message message) {
        if (message == null) {
            return;
        }
        if (isFromTextChat) {
            message.safePutExtra(IMJMOToken.ProductLine.KEY, IMJMOToken.ProductLine.TEXT_CHAT);
        }
    }

    @Override
    protected Message sendEmotionMessage(String tag, int emoteType) {
        tryExitJumpMode();

        Message message = MessageHelper.getInstance().sendEmotionMessage(tag, remoteUser, null, Message.CHATTYPE_USER, emoteType);
        if (isFirstMessage()) {
            // 首条消息
            addSayhiSource(message);
        }
        return message;
    }

    @Override
    protected Message sendAudioMessage(String filename, long audioTime) {
        tryExitJumpMode();

        return MessageHelper.getInstance().sendAudioMessage(filename, audioTime, remoteUser, null, Message.CHATTYPE_USER);
    }

    @Override
    protected Message sendVideoMessage(String filename, float videoRatio, long videoTime, MessageVideoDefArgument defs) {
        tryExitJumpMode();
        return MessageHelper.getInstance().sendVideoMessage(filename, videoRatio, videoTime, remoteUser, null, Message.CHATTYPE_USER, defs);
    }

    /**
     * 发送表情语音
     *
     * @param filename
     * @param videoRatio
     * @param videoTime
     * @param defs
     * @return
     */
    @Override
    protected Message sendAnimojiMessage(String filename, float videoRatio, long videoTime, MessageVideoDefArgument defs) {
        tryExitJumpMode();
        return MessageHelper.getInstance().sendAnimojiMessage(filename, videoRatio, videoTime, remoteUser, null, Message.CHATTYPE_USER, defs);
    }

    @Override
    protected Message sendMapMessage(Message message, MessageMapLocation location, Callback<FromMessageGetLocationCallBack.GetLocation> callback) {
        tryExitJumpMode();

        message.remoteId = remoteUser.momoid;
        message.distance = remoteUser.getDistance();
        message.messageTime = AppCodec.enMsgT();
        message.msgId = AppCodec.enMsgId(currentUser.momoid, null, remoteUser.momoid, message.messageTime);
        if (isFirstMessage()) {
            // 首条消息
            addSayhiSource(message);
        }
        MessageHelper.getInstance().sendMapMessage(message, location, callback, null, Message.CHATTYPE_USER);
        return message;
    }

    @Override
    protected Message sendImageMessage(File imageprocessPic, boolean isOriginImage) {
        tryExitJumpMode();

        Message message = MessageHelper.getInstance().sendImageMessage(imageprocessPic, remoteUser, null, Message.CHATTYPE_USER, isOriginImage);
        if (isFirstMessage()) {
            // 首条消息
            addSayhiSource(message);
        }

        return message;
    }

    @Override
    protected List<Message> sendImageMessage(List<Photo> items) {
        tryExitJumpMode();

        List<Message> messages = new ArrayList<>(6);
        int index = 0;
        for (Photo item : items) {
            if (StringUtils.notEmpty(item.getTempPath())) {
                Message message = MessageHelper.getInstance().sendImageMessage(new File(item.getTempPath()), remoteUser, null, Message.CHATTYPE_USER, item);
                message.imageFaceDetect = item.faceDetect;
                if (index == 0 && isFirstMessage()) {
                    // 首条消息
                    addSayhiSource(message);
                }
                messages.add(message);
                index++;
            }
        }
        return messages;
    }

    @Override
    protected Message sendImageMessage(Photo photo) {
        tryExitJumpMode();
        if (StringUtils.notEmpty(photo.getTempPath())) {
            Message message = MessageHelper.getInstance().sendImageMessage(new File(photo.getTempPath()), remoteUser, null, Message.CHATTYPE_USER, photo);
            message.imageFaceDetect = photo.faceDetect;
            if (isFirstMessage()) {
                addSayhiSource(message);
            }
            return message;
        }
        return null;
    }

    @Override
    protected boolean needClearEmoteRedDot() {
        return true;
    }

    @Override
    protected void onSendClicked() {
        //动态相关
        if (isEditTopNoticeVisible()) {
            String textContent = mMessageEditText.getText().toString().trim();
            if (TextUtils.isEmpty(textContent)) { //当输入的都是空格时，清空输入框内容
                mMessageEditText.setText("");
                return;
            }
            if (editTopNoticeView != null) {
                editTopNoticeView.sendRelativeMessage(textContent, getRemoteId(), getSayHiSource(), getChatSource());
            }
            mMessageEditText.getText().clear();
        } else {
            if (!couldSendMessage()) {
                return;
            }
            super.onSendClicked();

            hideInputMethod();

            if (showSayhiReply()) {
                aiChatCardManager.hideFastReplyView();
                aiChatCardManager.setShouldShow(false);
            }
        }
    }

    @Override
    public boolean couldSendMessage() {
        return bindPhoneTipView.getVisibility() != View.VISIBLE
                || !bindPhoneTipView.needShowBindTip();
    }

    @Override
    public void pullMessageInWindow(Message message) {
        if (message == null) {
            return;
        }
        if (isForbiddenMsg(true)) {
            return;
        }
        if (!message.receive) {
            isReplyed = true;
        }
        if (StringUtils.equalsNonNull(BUSINESS_TYPE_KLIAO_MATCH, businessType)) {
            message.isKliaoMatchMsg = true;
        }

        setUserToNewMessage(message);
        msgChatData.addItemModel(message);

        /**
         * 回复后，如果是招呼会话，则转为普通会话
         * FIX:之前是在 {@link #onPause()} 里面进行迁移，会导致用户自己发的消息，比招呼消息，早进入消息表
         * 现在消息统一改为根据ID来排序，就会出现打招呼消息在回复的消息后面的问题
         */
        transformSayHiSessionToNormal();

        super.pullMessageInWindow(message);
        if (msgChatRecycler != null) {
            MomoMainThreadExecutor.postDelayed(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    msgChatRecycler.smoothScrollBy(UIUtils.getScreenHeight(), 200);
                }
            }, 40);
        }
        // 发过消息后，就不再是悄悄模式了
        if (VIEWMODEL_PEEK.equals(viewModel)) {
            viewModel = null;
            // 退出悄悄查看时发之前的已读
            sendHasReadMark();
            // 退出悄悄查看时重新清掉气泡状态
            ChatMsgSaver.getInstance().updateMessagesIgnore(Message.CHATTYPE_USER, remoteUser.momoid, isSayhiSession);
        }
        if (StringUtils.isNotEmpty(message.newSource)) {
            sendGreetMessageSourceAndTargetId(message);
        }
    }

    private void transformSayHiSessionToNormal() {
        //补充：迁移招呼时，保留旧的逻辑，避免在onPause里面根据 isSayHisession 的逻辑遗漏
        if (!hasTransferHiSession && isSayhiSession && isReplyed) {
            long start = System.currentTimeMillis();
            //把招呼消息迁入到单人会话消息表里
            SingleMsgService.getInstance().transferSayhi2Chat(getChatId());
            hasTransferHiSession = true;
            MDLog.d(LogTag.COMMON, "首次回复招呼，转移会话为普通聊天 time-cost=%d", (System.currentTimeMillis() - start));
        }
    }

    //是否被举报禁言
    protected boolean isForbiddenMsg(boolean isShowNotice) {
        boolean isForbidden = mIGreetPresenter != null && mIGreetPresenter.isReportForbidden();
        if (isShowNotice && isForbidden) {
            mIGreetPresenter.showForbiddenNotice();
        }
        return isForbidden;
    }

    @Override
    protected void pullMessageInWindow(List<Message> messages) {
        if (messages == null) {
            return;
        }
        if (isForbiddenMsg(true)) {
            return;
        }
        for (Message message : messages) {
            if (!message.receive) {
                isReplyed = true;
            }

            setUserToNewMessage(message);
        }
        msgChatData.addItemModels(messages);
        super.pullMessageInWindow(messages);
        // 发过消息后，就不再是悄悄模式了
        if (VIEWMODEL_PEEK.equals(viewModel)) {
            viewModel = null;
            // 退出悄悄查看时发之前的已读
            sendHasReadMark();
            // 退出悄悄查看时重新清掉气泡状态
            ChatMsgSaver.getInstance().updateMessagesIgnore(Message.CHATTYPE_USER, remoteUser.momoid, isSayhiSession);
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
    }

    @Override
    protected List<Message> loadImageMessages() {
        if (isSayhiSession) {
            return SingleMsgService.getInstance().findSayhiMessageByRemoteIdContentType(getMomoID(), Message.CONTENTTYPE_MESSAGE_IMAGE);
        } else {
            return SingleMsgService.getInstance().findMessageByRemoteIdContentType(getMomoID(), Message.CONTENTTYPE_MESSAGE_IMAGE);
        }
    }

    /**
     * list滑动
     *
     * @param scrollState
     */
    @Override
    public void listScrollStateChange(final int scrollState) {
        if (mMgsMessageHelper != null) {
            mMgsMessageHelper.listScrollStateChangeMgs(scrollState, getRecyclerView());
        }
    }

    @Override
    public void listScroll(int firstVisibleItem, int visibleItemCount, int totalItemCount) {
        if (mMgsMessageHelper != null) {
            mMgsMessageHelper.listOnScrollMgs(getRecyclerView());
        }
    }

    public String getMomoID() {
        String tempRemoteId = null;
        if (!AppKit.getAccountManager().isOnline()) {
            tempRemoteId = getChatId();
        } else {
            tempRemoteId = remoteUser.momoid;
        }
        return tempRemoteId;
    }

    /**
     * 判断是否是发送的第一条消息
     * TODO 目前方式待优化，最终方案是通过查询是否发送过消息。
     *
     * @return
     */
    private boolean isFirstMessage() {
        if (!bothRelation) {
            if (msgChatData.isEmpty()) {
                return true;
            } else if (msgChatData.getCount() == 1) {
                int firstMsgContentType = msgChatData.getMessage(0).contentType;
                return firstMsgContentType == Message.CONTENTTYPE_MESSAGE_WAVE
                        || firstMsgContentType == Message.CONTENTTYPE_MESSAGE_USER_PROFILE
                        || firstMsgContentType == Message.CONTENTTYPE_MESSAGE_NOTICE;
            }
        }
        return false;
    }

    private void addSayhiSource(Message message) {
        if (message == null) {
            return;
        }
        Log4Android.getInstance().i(TAG, "chatFrom=" + chatFrom);
        String fromName = getIntent().getStringExtra(KEY_FROM_DATA);
        message.source = SayHiMatcher.buildSayHiSourceDeprecated(chatFrom, fromName);
        message.newSource = getSayHiSource();
        Log4Android.getInstance().i(TAG, "@@@@@@@say hi=" + message.newSource);
    }

    @Override
    protected void onRecordStart() {
        if (!bothRelation) {
            return;
        }
        super.onRecordStart();
    }

    /**
     * 修改黑名单列表 *
     */
    private void changeBlockList() {
        if (!StringUtils.isEmpty(chatID)) {
            ThreadUtils.execute(ThreadUtils.TYPE_RIGHT_NOW, () ->
                    UserService.getInstance().removeBlackUser(chatID) // 本地数据库移除
            );
            EventBus.getDefault()
                    .post(new DataEvent<>(EventKeys.Block.DELETE_BLOCK, chatID));
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            // 非双向好友情况下 点击录音按钮提示&不跳转
            case R.id.message_btn_gotoaudio:
                if (!TextUtils.isEmpty(getLogTypeStr())) {
                    logPanelItemClick(ChatPanelBusinessType.TYPE_VOICE);
                }
                if (bothRelation) {
                    if (VideoConflictNewHelper.conflictWith(VideoConflictConfig.BusinessType.COMMON)) {
                        return;
                    }
                    audioSwitch(v);
                } else {
                    Toaster.show(R.string.message_not_bothfollow);
                }
                return;

            default:
                break;
        }

        super.onClick(v);
    }

    public void changeRelationAndUpdateFriendList(String momoid) {
        if (remoteUser == null) {
            return;
        }
        IUserModel userModel = ModelManager.getModel(IUserModel.class);
        if (User.RELATION_NONE.equals(remoteUser.relation)) {
            remoteUser.relation = User.RELATION_FOLLOW;
            BasicUserInfoUtil.INSTANCE.setFollowingCount(BasicUserInfoUtil.INSTANCE.getFollowingCount() + 1);
        } else if (User.RELATION_FANS.equals(remoteUser.relation)) {
            remoteUser.relation = User.RELATION_BOTH;
            BasicUserInfoUtil.INSTANCE.setFriendCount(BasicUserInfoUtil.INSTANCE.getFriendCount() + 1);
            bothRelation = true;
            BasicUserInfoUtil.INSTANCE.setFollowingCount(BasicUserInfoUtil.INSTANCE.getFollowingCount() + 1);
        }
        MomoTaskExecutor.executeMessageTask(getTaskTag(), new RefreshDBTask(momoid, true));
    }

    @Override
    protected void onLayoutSizeComingLarge() {
        super.onLayoutSizeComingLarge();
        // 弹出输入法的时候返回键监听不到事件 只能通过判断界面大小来处理

        if (mHeightOffset < UIUtils.getPixels(50)) {
            //有些键盘弹出高度会有小幅度的变化，会误以为是键盘关闭
            return;
        }

        MomoMainThreadExecutor.post(new Runnable() {
            @Override
            public void run() {
                if (iTip != null) {
                    iTip.hide();
                }
                //键盘隐藏，显示快捷输入
                if (!panelHandler.isPanelShow()) {
                    if (firstShow) {
                        showSmallFirst();
                    } else {
                        showLargeGreetUI();
                    }
                }
            }
        });
    }

    @Override
    protected void onLayoutSizeComingSmall() {
        super.onLayoutSizeComingSmall();
        if (isNewIntentSmall) {
            isNewIntentSmall = false;
            return;
        }
        if (mHeightOffset < UIUtils.getPixels(50)) {
            //有些键盘弹出高度会有小幅度的变化，会误以为是键盘打开
            return;
        }
        msgChatRecycler.smoothScrollBy(UIUtils.getScreenHeight(), 500);
        MomoMainThreadExecutor.post(new Runnable() {
            @Override
            public void run() {
                showGreetChat();
                showSmallGreetUI();
            }
        });
    }

    @Override
    public void onPanelShow(ChatPanelItem item) {
        super.onPanelShow(item);
        showSmallGreetUI();
    }

    @Override
    public void onPanelHide() {
        super.onPanelHide();
    }

    @Override
    protected void showEmotionSearchView() {
        super.showEmotionSearchView();

        hideBottomTipsView();
        if (mInputGreetGiftBtn != null) {
            mInputGreetGiftBtn.setVisibility(View.GONE);
        }
        if (heartbeatHiContainer != null) {
            heartbeatHiContainer.setVisibility(View.GONE);
        }
    }

    @Override
    public void showEmotionListView() {
        super.showEmotionListView();

        hideBottomTipsView();
        aiChatCardManager.hideFastReplyView();
        isPendingShowAIForEmotion = true;
    }

    @Override
    public void hideEmotionSearchView() {
        MomoMainThreadExecutor.post(() -> {
            GreetDialog.super.hideEmotionSearchView();
            if (mInputGreetGiftBtn != null) {
                mInputGreetGiftBtn.setVisibility(View.VISIBLE);
            }
            if (heartbeatHiContainer != null) {
                heartbeatHiContainer.setVisibility(View.VISIBLE);
            }
        });
        if (isPendingShowAIForEmotion) {
            isPendingShowAIForEmotion = false;
            updateAiCardSiteScene(isAnyInputShown);
            aiChatCardManager.showFastReplyView();
        }
    }

    @Override
    public void onBackPressed() {
        try {
            if (isFromFeedLike()) {
                resetInputMsg();
            }
            if (!animateExitGreetCard()) {
                super.onBackPressed();
            }

        } catch (Throwable e) {
            finish();
        }
    }

    @Override
    protected void showInformativeLayout() {
        super.showInformativeLayout();
    }

    @Override
    protected void hideAllInputMethod() {
        showInformativeLayout();
        super.hideAllInputMethod();
        showGreetGiftPanelView(false);
    }

    @Override
    protected boolean isEnableEmotionAutoSearch() {
        return false;
    }

    public void showBottomTipsView(String text) {
        ChatBottomTipView bottomTipView = bottomTipViewSimpleViewStubProxy.getStubView();
        if (!StringUtils.isEmpty(text)) {
            bottomTipView.setText(text);
        }
        bottomTipView.setVisibility(View.VISIBLE);
        //隐藏掉别的底部条
        hideEmotionSearchView();
    }

    public void hideBottomTipsView() {
        if (bottomTipViewSimpleViewStubProxy == null || !bottomTipViewSimpleViewStubProxy.isInflate() || bottomTipViewSimpleViewStubProxy.getStubView().getVisibility() == View.GONE) {
            return;
        }
        bottomTipViewSimpleViewStubProxy.getStubView().setVisibility(View.GONE);
    }

    private static class RefreshUserProfileListener implements IBroadcastReceiveListener {
        private WeakReference<User> remoteUser;
        private WeakReference<GreetDialog> mActivity;

        RefreshUserProfileListener(GreetDialog activity, User user) {
            this.mActivity = new WeakReference<>(activity);
            this.remoteUser = new WeakReference<User>(user);
        }

        @Override
        public void onReceive(Intent intent) {
            final String momoid = intent.getStringExtra(ReflushUserProfileReceiver.KEY_MOMOID);
            GreetDialog chatActivity = mActivity.get();
            User user = remoteUser.get();
            if (chatActivity == null || user == null) return;
            if (!StringUtils.isEmpty(momoid) && TextUtils.equals(user.momoid, momoid)) {
                UserService.getInstance().get(user, momoid);
                chatActivity.refreshAdapter();
            }
        }
    }

    private class CloudMsgReceiverListener implements IBroadcastReceiveListener {
        private WeakReference<GreetDialog> mActivity;

        CloudMsgReceiverListener(GreetDialog activity) {
            mActivity = new WeakReference<>(activity);
        }

        @Override
        public void onReceive(Intent intent) {
            GreetDialog activity = mActivity.get();
            if (activity == null) {
                return;
            }
            activity.msgChatRecycler.scrollToBottom();
            msgChatData.clearMessage();
            activity.initData();
        }
    }

    private class FriendListReciverListener implements IBroadcastReceiveListener {
        private WeakReference<GreetDialog> mActivity;

        FriendListReciverListener(GreetDialog activity) {
            mActivity = new WeakReference<GreetDialog>(activity);
        }

        @Override
        public void onReceive(Intent intent) {
            GreetDialog activity = mActivity.get();
            if (activity == null) return;
            String momoid = intent.getStringExtra(FriendListReceiver.KEY_MOMOID);
            Log4Android.getInstance().d(TAG, "got a friendListBroadcast momoid:" + momoid);
            if (TextUtils.equals(activity.remoteUser.momoid, momoid)) {
                String action = intent.getAction();
                if (FriendListReceiver.ACTION_CHAT_FOLLOW.equals(action)) {
                    changeRelationAndUpdateFriendList(momoid);
                    changeBlockList();
                } else {
                    MomoTaskExecutor.executeMessageTask(getTaskTag(), new RefreshDBTask(momoid, false));
                }
            }
        }
    }

    private void refreshRelation(String relationStr) {
        if (User.RELATION_BOTH.equals(relationStr)) {
            bothRelation = true;
            isSayhiSession = false;
        } else if (User.RELATION_FOLLOW.equals(relationStr)) {
            bothRelation = false;
            isSayhiSession = false;
        } else {
            bothRelation = false;
        }
    }

    private class RefreshDBTask extends MomoTaskExecutor.Task<Object, Object, String> {
        private String momoid;
        private boolean isAddFriend;

        public RefreshDBTask(String momoid, boolean isAddFriend) {
            this.momoid = momoid;
            this.isAddFriend = isAddFriend;
        }

        @Override
        protected String executeTask(Object... params) throws Exception {
            if (isAddFriend) {
                UserService.getInstance().addFriend(remoteUser);
                Intent intent = new Intent(FriendListReceiver.ACTION_ADD_FRIEND);
                intent.putExtra(FriendListReceiver.KEY_MOMOID, chatID);
                intent.putExtra(FriendListReceiver.KEY_NEW_FANS, BasicUserInfoUtil.INSTANCE.getNewFansCount());
                intent.putExtra(FriendListReceiver.KEY_TOTAL_FANS, BasicUserInfoUtil.INSTANCE.getFollowerCount());
                intent.putExtra(FriendListReceiver.KEY_TOTAL_FRIENDS, BasicUserInfoUtil.INSTANCE.getFollowingCount());
                intent.putExtra(FriendListReceiver.KEY_RELATION, remoteUser.relation);
                sendBroadcast(intent);

                UserService.getInstance().updateRelationString(remoteUser.momoid, remoteUser.relation);
            }
            UserService.getInstance().get(remoteUser, momoid);
            return UserService.getInstance().getRelationString(momoid);
        }

        @Override
        protected void onTaskSuccess(@Nullable String relationStr) {
            refreshRelation(relationStr);
        }
    }

    private class ChatBackgroundReceiverListener implements IBroadcastReceiveListener {
        private WeakReference<GreetDialog> mActivity;

        public ChatBackgroundReceiverListener(GreetDialog activity) {
            mActivity = new WeakReference<GreetDialog>(activity);
        }

        @Override
        public void onReceive(Intent intent) {
            GreetDialog activity = mActivity.get();
            if (activity == null) return;
            if (ChatBackgroundReceiver.ACTION.equals(intent.getAction())) {
                String resId = intent.getStringExtra(ChatBGSettingActivity.KEY_RESOURSEID);
                String smallResId = intent.getStringExtra(ChatBGSettingActivity.KEY_SMALL_RESOURSEID);
                activity.remoteUser.chatBackgroud = resId;
                UserService.getInstance().updateChatBackground(resId, smallResId, chatID);
            }
        }
    }

    private class FileUploadProgressReceiverListener implements IBroadcastReceiveListener {
        private WeakReference<GreetDialog> mActivity;

        FileUploadProgressReceiverListener(GreetDialog activity) {
            mActivity = new WeakReference<GreetDialog>(activity);
        }

        @Override
        public void onReceive(Intent intent) {
            if (thisActivity() == null) {
                return;
            }
            updateLoadingProgress(intent, msgChatData);
        }
    }

    private class InstantQuizTask extends MomoTaskExecutor.Task<Object, Object, Message> {
        @Nullable
        private String messageStr;

        public InstantQuizTask(@Nullable String messageStr) {
            this.messageStr = messageStr;
        }

        @Override
        protected Message executeTask(Object... params) throws Exception {
            Message message = MessageApi.parseSendMessage(messageStr);
            MessageServiceHelper.getInstance().saveSent(message);
            return message;
        }

        @Override
        protected void onTaskSuccess(@Nullable Message message) {
            if (message == null) return;

            if (!message.receive) {
                isReplyed = true;
            }

            addMessageToAdapter(message);
        }
    }

    private class InstantAnswerTask extends MomoTaskExecutor.Task<Object, Object, Message> {
        @NonNull
        private Message message;

        public InstantAnswerTask(@NonNull Message message) {
            this.message = message;
        }

        @Override
        protected Message executeTask(Object... params) throws Exception {
            MessageServiceHelper.getInstance().saveSent(message);
            return message;
        }

        @Override
        protected void onTaskSuccess(@Nullable Message message) {
            if (message == null) return;

            if (!message.receive) {
                isReplyed = true;
            }
            addMessageToAdapter(message);
        }
    }

    private class DeleteMessageTask extends BaseDialogTask<Object, Object, Object> {
        private Message message;
        private Message sendMsg;
        private String msgJson;

        public DeleteMessageTask(Message message, String msgJson) {
            this.message = message;
            this.msgJson = msgJson;
        }

        @Override
        protected Object executeTask(Object... objects) throws Exception {
            if (StringUtils.notEmpty(msgJson)) {
                sendMsg = MessageApi.parseSendMessage(msgJson);
                updateMessageStatus(sendMsg);
                MessageServiceHelper.getInstance().delete(message, isSayHiChatSession());
                IMLocalLogger.log("GreetDialog DeleteMessageTask delete msg");
            }

            return null;
        }

        @Override
        protected void onTaskSuccess(Object o) {
            super.onTaskSuccess(o);

            if (sendMsg != null) {
                addMessageToAdapter(sendMsg);
                msgChatData.removeMessage(message);

                dealStatusMark(MessageKeys.MsgStatus_Success, sendMsg.msgId, null);
            }
        }
    }

    private class ExposeType26Task extends MomoTaskExecutor.Task<Object, Object, Object> {

        Message exposeMsg;

        public ExposeType26Task(Message exposeMsg) {
            this.exposeMsg = exposeMsg;
        }

        @Override
        protected Object executeTask(Object... objects) throws Exception {
            SingleMsgService.getInstance().update(exposeMsg.remoteId, exposeMsg);
            return null;
        }

        @Override
        protected void onTaskError(Exception e) {
            // 不做提示
        }
    }

    //</editor-fold>

    //<editor-fold desc="IPageDurationHelper">
    private String pageLogId = null;

    @Nullable
    @Override
    public String getPageLogID(boolean enter) {
        if (enter) {
            pageLogId = UUID.randomUUID().toString();
        }
        return pageLogId;
    }

    @Nullable
    @Override
    public String getPageSource() {
        return getClass().getSimpleName();
    }
    //</editor-fold>

    private void addMessageToAdapter(Message msg) {
        if (msg == null) {
            return;
        }
        setUserToNewMessage(msg);

        msgChatData.addItemModel(msg);

        if (msgChatRecycler != null) {
            msgChatRecycler.scrollToBottom();
        }
    }

    @Nullable
    @Override
    public Event.Page getPVPage() {
        return EVPage.Msg.ChatPage;
    }

    @Nullable
    @Override
    public Map<String, String> getPVExtra() {
        Map<String, String> map = new HashMap<>();
        if (remoteUser != null && StringUtils.isNotEmpty(remoteUser.momoid)) {
            map.put("to_momo_id", remoteUser.momoid);
        }
        return map;
    }

    @Override
    public void setDraft() {
        super.setDraft();
    }

    @Override
    protected void onInputMethodShowed(ChatInputShowFrom showFrom) {
        super.onInputMethodShowed(showFrom);
        checkAiChatGuide(true);
        currentIsKeyBoradShown = false;
        dismissReflowGiftView(true);
    }

    @Override
    protected void showEmotionPanel() {
        dismissReflowGiftView(false);
        super.showEmotionPanel();
    }

    @Override
    protected void onInputMethodEclipsed() {
        super.onInputMethodEclipsed();
        checkAiChatGuide(false);
    }

    //折叠屏横屏ai助聊招呼展示处理,非折叠屏正常展示
    private void checkAiChatGuide(boolean isShowInput) {
        updateAiCardSiteScene(isShowInput);
        MomoMainThreadExecutor.post(new Runnable() {
            @Override
            public void run() {
                if ((float) UIUtils.getScreenHeight() / (float) UIUtils.getScreenWidth() < 1.5f) { //折叠屏横屏
                    if (isShowInput) {
                        aiChatCardManager.hideFastReplyView();
                        mMessageEditorGallery.requestLayout();
                    } else {
                        aiChatCardManager.showFastReplyView();
                    }
                } else {
                    aiChatCardManager.showFastReplyView(); //是否真正展示ui由manager内部决定
                }
            }
        });
    }

    private void updateAiCardSiteScene(boolean isShowInput) { // 在ai助聊面板展示前调用
        if (aiChatCardManager == null) {
            return;
        }
        if (isShowInput) {
            aiChatCardManager.setSiteScene(KEY_WINDOW_KEYUP);
        } else {
            aiChatCardManager.setSiteScene(KEY_WINDOW_KEYCLOSE);
        }
    }


    private boolean showSayhiReply() { //展示从附近动态过来的招呼推荐
        return getIntent() != null && getIntent().getBooleanExtra(KEY_SHOW_NEARBY_SAYHI_REPLY, false);
    }

    @Override
    public void hideAllInputView() {
        hideAllInputMethod();
    }

    //防止表情面板和图片面板返回不同的显示/隐藏状态
    private String currentBusinessName = "";

    @Override
    public void onChatPanelHide(String businessName) {
        super.onChatPanelHide(businessName);
        //表情面板关掉（但不是因为弹出键盘关掉的）此时不是输入状态
        if (currentBusinessName.equals(businessName) && !willShowKeyboard) {
            MomoMainThreadExecutor.post(new Runnable() {
                @Override
                public void run() {
                    if (greetChatPageGiftManager != null && !greetChatPageGiftManager.isShow()) {
                        showLargeGreetUI();
                    }
                }
            });
        }
    }

    @Override
    public void onChatPanelShow(String businessName) {
        currentBusinessName = businessName;
        super.onChatPanelShow(businessName);
        //表情面板弹出，礼物按钮消失，显示快捷输入
        if (greetChatPageGiftManager != null) {
            greetChatPageGiftManager.hideGiftPanel();
        }
    }

    @Override
    protected void onEditorTextTouch() {
        super.onEditorTextTouch();
        //输入框点击，将要弹出键盘
        if (greetChatPageGiftManager != null) {
            greetChatPageGiftManager.hideGiftPanel();
        }
    }

    //======================== 招呼 start====================================

    private void initGreetHalfMode() {
        initGreetHead();
        initGreetChatGiftManager();
        initGreetPanel();
        showBottomFunctionLayout(false);
        setSupportSwipeBack(false);
        rootView = (ChatVerticalSlideRelationLayout) findViewById(R.id.root_view);
        //是点赞的招呼，并且有动态
        if (getIntent().hasExtra(ChatHelper.KEY_EDIT_NOTICE) && isFromFeedLike()) {
            initGreetFeedView();
        }
        initGreetPresenter();
        initGreetEdit();
        mGreetCardContainer = (FrameLayout) findViewById(R.id.greet_half_card_container);
        llEdit = (LinearLayout) findViewById(R.id.editor_layout);
        resetEditBg();

        boolean hideGreetKeyboard = NewSayHiConst.INSTANCE.getHideGreetKeyboard();
        if (!hideGreetKeyboard) { // 不隐藏键盘
            defaultShowInput();
        } else {
            needShowKeyboardDelay = true;
        }
        String giftSource = getIntent().getStringExtra(KEY_SOURCE_DATA);
        if (FROM_SOURCE_HIPSTER.equals(giftSource)) {
            mMessageEditText.clearFocus();
            hideInputMethod();
        }
    }

    private void resetEditBg() {
        if (this.isOpenHeartbeatSvip) {
            llEdit.setBackground(getDrawable(R.drawable.bg_greet_hi_edit_yellow));
        } else {
            llEdit.setBackground(getDrawable(R.drawable.bc_greet_btn_white));
        }
    }

    private void updateLlEditMargin(int marLeft) {
        ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) llEdit.getLayoutParams();
        layoutParams.leftMargin = marLeft + llEditDefaultMarginLeft;
        llEdit.setLayoutParams(layoutParams);
    }

    /**
     * 显示底部招呼面板
     */
    private void initGreetPanel() {
        mInputGreetGiftBtn = findViewById(R.id.input_greet_gift_btn);
        mInputGreetGiftBtn.setVisibility(View.VISIBLE);
        if (mInputGreetGiftBtn != null) {
            mInputGreetGiftBtn.setOnClickListener(v -> {
                if (!isForbiddenMsg(true)) {
                    //关掉权限引导弹窗
                    if (isShowingPushGuide()) {
                        viewStubPushGuide.setVisibility(View.GONE);
                    }
                    showGreetGiftPanelView(!greetChatPageGiftManager.isShow());
                }
            });
        }
    }

    private void initGreetPresenter() {
        mIGreetPresenter = new GreetPresenterImpl(this, getChatId());
    }

    private void initGreetHead() {
        mGreetHeadContainer = (FrameLayout) findViewById(R.id.chat_greet_head_container);
        mGreetHeadContainer.removeAllViews();

        isFromNearbyPeople = NEARBY_PEOPLE_CARD.equals(getIntent().getStringExtra(MODE));
        if (isFromNearbyPeople) {
            mNearbyPeopleCardGreetHeadView = new NearbyPeopleCardGreetHeadView(getBaseContext());
            mNearbyPeopleCardGreetHeadView.setClose(() -> {
                onBackPressed();
                return null;
            });
            mGreetHeadContainer.addView(mNearbyPeopleCardGreetHeadView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            mGreetListMask = (ImageView) findViewById(R.id.greet_list_mask);
            mGreetListMask.setVisibility(View.VISIBLE);
            largeUiHeight = LARGE_NEARBY_CARD_UI_HEIGHT;
        } else {
            largeUiHeight = LARGE_GREET_UI_HEIGHT;
            mChatHalfGreetHeadView = new ChatHalfGreetHeadView(thisActivity());
            mGreetHeadContainer.addView(mChatHalfGreetHeadView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            mChatHalfGreetHeadView.setData(remoteUser);
            mGreetListMask = (ImageView) findViewById(R.id.greet_list_mask);
            mGreetListMask.setVisibility(View.VISIBLE);
            mChatHalfGreetHeadView.rootView.setOnClickListener(v -> onBackPressed());
        }
        if (com.immomo.momo.util.MomoKit.INSTANCE.isDarkMode(this)) {
            mGreetListMask.setVisibility(View.GONE);
        }

        tvClose = (TextView) findViewById(R.id.bar_left_close);
        if (tvClose != null) {
            tvClose.setVisibility(View.VISIBLE);
            tvClose.setOnClickListener(v -> {
                finish();
                overridePendingTransition(0, 0);
            });
        }
    }

    @Override
    public void onGreetMessageComplete(boolean needCheckShowKeyboard) {
        if (needCheckShowKeyboard && needShowKeyboardDelay) {
            defaultShowInput();
        }
    }

    private void refreshGreetHalfTitle() {
        if (mChatHalfGreetHeadView != null) {
            mChatHalfGreetHeadView.setData(remoteUser);
        }
    }

    private void dealHalfModeTop() {
        getWindow().setBackgroundDrawableResource(R.color.blackwith40tran);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP && getWindow() != null) {
            getWindow().setStatusBarColor(Color.TRANSPARENT);
        }
        setStatusBarTheme(true);
    }

    private void initGreetAdapterListener() {
        msgChatData.setChangedListener((items, size) -> {
            if (size > 0) {
                hideNewGreetPugCard();
            }
        });
    }

    private void initGreetEdit() {
        if (isFromFeedLike()) {
            initMessageInputText();
        } else {
            mMessageEditText.setHint("礼貌地打个招呼...");
            fillDefaultTxt();
        }

        mMessageEditText.setMaxLines(1);
        mMessageEditText.setSingleLine();
        mMessageEditText.setEllipsize(TextUtils.TruncateAt.END);

    }

    private void fillDefaultTxt() {
        Intent intent = getIntent();
        if (intent != null) {
            String fillTxt = intent.getStringExtra(ChatHelper.KEY_FILL_TXT);
            if (StringUtils.isEmpty(mMessageEditText.getText()) && !TextUtils.isEmpty(fillTxt)) {
                mMessageEditText.setText(fillTxt);
            }
        }
    }

    private void initMessageInputText() {
        if (MomoKit.getCurrentUser() != null && MomoKit.getCurrentUser().isFemale()) {
            if (StringUtils.isEmpty(mMessageEditText.getText())) {
                mMessageEditText.setText("感谢你赞了我的动态");
                if (mMessageEditText.getText() != null) {
                    mMessageEditText.setSelection(mMessageEditText.getText().length());
                }
            }
        } else {
            if (remoteUser != null) {
                if (remoteUser.isFemale()) {
                    mMessageEditText.setHint("感谢下她的点赞吧");
                } else {
                    mMessageEditText.setHint("感谢下他的点赞吧");
                }
            }
        }
    }

    private void initGreetFeedView() {
        if (editTopNoticeView == null) {
            editTopNoticeView = new GreetFeedView(rootView, getIntent());
        }
        editTopNoticeView.setEditTopNoticeListener(new ChatEditTopNoticeContract.EditTopNoticeListener() {
            @Override
            public void onFullChatClick() {

            }

            @Override
            public void onHideHalfMode() {

            }

            @Override
            public void onSendSuccess(Message msg) {
                addMessageToAdapter(msg);
            }

            @Override
            public void onCloseActivity() {
            }
        });
        editTopNoticeView.initEditTopNoticeView();
        editTopNoticeView.refreshEditTopNoticeView();
    }

    private void initGreetChatGiftManager() {
        if (greetChatPageGiftManager != null) {
            return;
        }
        greetChatPageGiftManager = new GreetChatPageGiftManager((ViewStub) findViewById(R.id.greet_half_gift_panel_stub), GreetDialog.this, getChatType());
        greetChatPageGiftManager.setSceneId(getChatId());
        greetChatPageGiftManager.setGiftSource(getGiftSource());
        greetChatPageGiftManager.setSayhiSource(getSayHiSource());
        greetChatPageGiftManager.setChatGiftEventListener(new GreetChatPageGiftManager.ChatGiftEventListener() {
            @Override
            public void goToChooseReceiver() {
            }

            @Override
            public void sendGiftSuccess(long balance, BaseGift baseGift) {
                if (baseGift != null && GreetHelper.GREET_GIFT.equals(baseGift.getSendFrom())) {
                    sendGiftGreetMsg(baseGift.getId());
                }
            }

            @Override
            public boolean couldSendGift() {
                return couldSendMessage();
            }

            @Override
            public void clickSendGift(BaseGift baseGift) {
                greetGiftClick();
            }

            @Override
            public void onLongClick(BaseGift baseGift) {
            }
        });

        greetChatPageGiftManager.setEventListener(visible -> {
            dismissReflowGiftView(false);
            if (visible) {
                logGiftPanelPoint();
                showGiftInputUI();
                showLargeGreetUI();
            } else {
                showNormalInputUI();
            }
        });
        String sendId = getChatId();
        User send = SessionUserCache.getUser(sendId);
        if (send == null) {
            greetChatPageGiftManager.setGiftReceiver(new GiftReceiver(sendId, null, sendId));
        } else {
            greetChatPageGiftManager.setGiftReceiver(new GiftReceiver(sendId, send.getLoadImageId(), send.getDisplayName()));
        }
    }

    private void dismissReflowGiftView(boolean checkKeyboardDismiss) {
        if (greetReflowManGift != null && greetReflowManGift.getVisibility() != View.VISIBLE)
            return;
        cancelDismissKeyBoardCheck();
        if (checkKeyboardDismiss) {
            MomoMainThreadExecutor.postDelayed(REFLOW_GIFT_CHECK_SHOW_KEYBOARD.hashCode(), () -> {
                if (isKeyboardShown) {
                    forceDismissKeyBoard();
                }
            }, 100);
        } else {
            forceDismissKeyBoard();
        }
    }

    private void cancelDismissKeyBoardCheck() {
        MomoMainThreadExecutor.cancelAllRunnables(REFLOW_GIFT_CHECK_SHOW_KEYBOARD.hashCode());
    }

    private void forceDismissKeyBoard() {
        if (greetReflowManGift != null) {
            greetReflowManGift.setVisibility(View.GONE);
        }
    }

    private void logGiftPanelPoint() {
        Intent intentData = getIntentData();
        if (intentData != null && FROM_SOURCE_HIPSTER.equals(intentData.getStringExtra(KEY_SOURCE_DATA))) {
            EVLog.create(ISayHiDialogLog.class)
                    .logHipsterGiftOnlyShow(
                            String.valueOf(getMomoID()),
                            intentData.getIntExtra(ChatHelper.KEY_FROM_FEED_POSITION, -1),
                            String.valueOf(intentData.getStringExtra(ChatHelper.KEY_FEED_ID))
                    );
        }
    }

    /**
     * 加载打招呼礼物面板
     */
    private void loadGreetGiftPanel() {
        String sendId = getChatId();
        User send = SessionUserCache.getUser(sendId);
        showInitTopGift();
        if (send == null) {
            greetChatPageGiftManager.showGiftPanel(new GiftReceiver(sendId, null, sendId));
        } else {
            greetChatPageGiftManager.showGiftPanel(new GiftReceiver(sendId, send.getLoadImageId(), send.getDisplayName()));
        }
    }

    /**
     * 置顶礼物面板提示
     */
    private void showInitTopGift() {
        if (greetHiTopGiftCardData != null && greetHiTopGiftCardData.category != null) {
            String categoryId = greetHiTopGiftCardData.category.categoryId;
            if (StringUtils.isNotBlank(categoryId)) {
                greetChatPageGiftManager.setAppId(categoryId);
            }
            greetChatPageGiftManager.setGiftTopData(greetHiTopGiftCardData.category);
        }
    }

    @Override
    public void showGreetGiftPanelView(boolean show) {
        if (show) {
            GreetHelper.saveShowGreetGift();
            if (greetChatPageGiftManager == null) {
                initGreetChatGiftManager();
            }
            loadGreetGiftPanel();
        } else {
            if (greetChatPageGiftManager != null) {
                greetChatPageGiftManager.hideGiftPanel();
            }
        }
        hideAllInputMethod(true);
    }

    private boolean isGreetGiftPanelShow() {
        if (greetChatPageGiftManager != null) {
            return greetChatPageGiftManager.isGiftPanelShow();
        }
        return false;
    }

    private void showGreetChat() {
        //展示push权限引导不展示气泡
        if (!isGreetGiftPanelShow() && !getGreetTask
                && !(isNeedShowPushGuide() || isShowingPushGuide())) {   //礼物面板展示后隐藏打招呼
            getGreetTask = true;
            MomoTaskExecutor.executeUserTask(getTaskTag(), new GreetChatTask(remoteUser.momoid));
        }
    }

    @Override
    public boolean showGiftPanel(String toId, String name, boolean onlyShowPanel) {
        if (!isGreetGiftPanelShow()) {
            hideAllInputView();
            showGreetGiftPanelView(false);
        }
        return true;

    }

    @Override
    public void onGiftCardDataGet(GreetMessageResult.HiGiftGuide giftGuide) {
        greetHiTopGiftCardData = giftGuide;
    }

    @Override
    public void onEnterDataGet(GreetMessageResult greetMessageResult, boolean firstIn) {
        boolean switchCurBtnState = false;
        if (tvHeartbeatSvipCnt != null && greetMessageResult != null) {
            svipHeartBeat = greetMessageResult.svipHeartBeat;
            if (svipHeartBeat != null && HeartbeatSvipHiConfigV1.isUserNewUI()) {
                resetHeartbeatCnt(svipHeartBeat.remain);
                if (svipHeartBeat.remain > 0 && svipHeartBeat.defaultSwitch == 1) {
                    switchCurBtnState = true;
                }
                if (KV.getUserBool(SPKeys.User.SayHi.KEY_HI_HEARTBEAT_SVIP_TIPS, true)) {
                    MomoMainThreadExecutor.postDelayed(hashCode(), () ->
                            showTips(heartbeatHiContainer, svipHeartBeat.getTip(), true), 700);
                    KV.saveUserValue(SPKeys.User.SayHi.KEY_HI_HEARTBEAT_SVIP_TIPS, false);
                }
                if (firstIn) { // 首次进入打点
                    String remoteGender = svipHeartBeat.remoteGender;
                    HeartbeatHiRecordUtil.btnExposure(getMomoID(), remoteGender);
                }
            }
        }
        if (switchCurBtnState) {
            switchHeartbeatBtn(false);
        }
    }

    private void resetHeartbeatCnt(int remain) {
        if (tvHeartbeatSvipCnt != null) {
            if (remain < 0) {
                remain = 0;
            }
            tvHeartbeatSvipCnt.setText(String.valueOf(remain));
            if (MomoKit.isSVipUser() || remain > 0) {
                tvHeartbeatSvipCnt.setVisibility(View.VISIBLE);
            } else {
                tvHeartbeatSvipCnt.setVisibility(View.GONE);
            }
        }
    }

    /**
     * 回流男送礼展示
     *
     * @param reflowManGift
     */
    @Override
    public void onReflowManGiftShow(GreetMessageResult.ReflowManGift reflowManGift) {
        try {
            if (reflowManGift != null && StringUtils.isNotBlank(reflowManGift.giftId) && StringUtils.isNotBlank(reflowManGift.giftAppId)) {
                if (greetReflowManGift == null) {
                    greetReflowManGiftStub = new SimpleViewStubProxy<>((ViewStub) findViewById(R.id.stub_greet_reflow_man_gift));
                    greetReflowManGiftStub.addInflateListener(view -> {
                        greetReflowManGift = view;
                        greetReflowManGift.setClickable(true);
                        greetReflowManGift.setOnClickListener(v -> {
                        });
                        greetReflowManGift.setVisibility(View.INVISIBLE);
                    });
                    greetReflowManGiftStub.setVisibility(View.VISIBLE);
                }
                ImageView ivReflowGiftClose = greetReflowManGift.findViewById(R.id.iv_reflow_gift_close);
                ImageView ivReflowGiftIcon = greetReflowManGift.findViewById(R.id.iv_reflow_gift_icon);
                TextView tvReflowTitle = greetReflowManGift.findViewById(R.id.tv_reflow_title);
                TextView tvReflowSubtitle = greetReflowManGift.findViewById(R.id.tv_reflow_subtitle);
                TextView tvReflowGiftSend = greetReflowManGift.findViewById(R.id.tv_reflow_gift_send);
                ImageLoader.load(reflowManGift.icon).into(ivReflowGiftIcon);
                String title = reflowManGift.title;
                if (title == null) title = "";
                String subTitle = reflowManGift.subTitle;
                if (subTitle == null) subTitle = "";
                String btnTxt = reflowManGift.btnTxt;
                if (btnTxt == null) btnTxt = "送她";
                tvReflowTitle.setText(title);
                tvReflowSubtitle.setText(subTitle);
                tvReflowGiftSend.setText(btnTxt);
                ivReflowGiftClose.setOnClickListener(view -> dismissReflowGiftView(false));
                tvReflowGiftSend.setOnClickListener(view -> {
                    dismissReflowGiftView(false);
                    if (!ClickUtils.isFastClick()) {
                        if (mIGreetPresenter != null) {
                            mIGreetPresenter.getGreetQueryReflowGift(reflowManGift);
                        }
                    }
                });
                if (mMessageLayoutEditorText != null) {
                    mMessageLayoutEditorText.clearFocus();
                }
                hideAllInputMethod(true);
                showLargeGreetUI();
                greetReflowManGift.setVisibility(View.VISIBLE);
                reflowGiftAnimation = new ScaleAnimation(
                        0f, 1f, 0f, 1f,
                        Animation.RELATIVE_TO_SELF, 0.5f,
                        Animation.RELATIVE_TO_SELF, 1f
                );
                reflowGiftAnimation.setDuration(300);
                greetReflowManGift.startAnimation(reflowGiftAnimation);
                NewSayHiConst.INSTANCE.setSayhiKeyboardState(true);
                EVLog.create(IMessageLog.class).onReflowGiftShow(getRemoteId());
            }
        } catch (Throwable throwable) {
            MDLog.printErrStackTrace(LogTag.COMMON, throwable);
        }
    }

    @Override
    public void onGreetQueryReflowGiftSuccess(GreetReflowGiftCheckResult greetMessageResult,
                                              GreetMessageResult.ReflowManGift reflowManGift) {
        try {
            if (greetChatPageGiftManager != null) {
                BaseGift baseGift = new BaseGift();
                baseGift.setId(reflowManGift.giftId);
                baseGift.setAppId(reflowManGift.giftAppId);
                baseGift.setIs_package(1);
                baseGift.setFreeGift(true);
                BaseGift.Package aPackage = new BaseGift.Package();
                aPackage.setRemain(reflowManGift.remain);
                aPackage.setLabel("");
                baseGift.setaPackage(aPackage);
                greetChatPageGiftManager.sendGift(baseGift);
            }
        } catch (Throwable throwable) {
            MDLog.printErrStackTrace(LogTag.COMMON, throwable);
        }
    }

    @Override
    public void showGreetCard(@Nullable GreetCardData greetCardData) {
        if (greetCardData instanceof GreetHiTopGiftCardData) {
            greetHiTopGiftCardData = ((GreetHiTopGiftCardData) greetCardData).getHiGiftGuide();
            if (greetHiTopGiftCardData != null) {
                greetHiTopGiftCardData.toSayhiId = remoteUser.getMomoid();
                greetHiTopGiftCardData.toSayhiGender = remoteUser.getSex();
                GreetMessageResult.HiGiftGuideCategory category = greetHiTopGiftCardData.category;
                if (category != null && StringUtils.isNotBlank(category.categoryId)) {
                    greetChatPageGiftManager.setAppId(category.categoryId);
                }
            }
        }
        //push引导展示时不展示招呼卡片
        if (isShowingPushGuide() || isNeedShowPushGuide()) {
            return;
        }
        if (mGreetCardContainer != null && msgChatData.getCount() <= 0 && !isFromFeedLike() && greetCardData != null) {
            mGreetCardContainer.removeAllViews();
            GreetPugCardView view = GreetViewHelperKt.getGreetPugCardView(this, greetCardData);
            if (view != null) {
                mGreetCardContainer.addView(view);
                view.setRemoteId(getChatId());
                view.setCardData(greetCardData);
                view.setCardClickListener((clickData, filterSource) -> {
                    if (clickData instanceof BaseGift) {
                        BaseGift baseGift = (BaseGift) clickData;
                        if (greetChatPageGiftManager != null) {
                            if (!filterSource) {
                                baseGift.setSendFrom(GreetHelper.GREET_GIFT);
                            }
                            greetChatPageGiftManager.sendGift(baseGift);
                        }
                    }
                });
                mGreetCardContainer.setVisibility(View.VISIBLE);
                if (mMessageInputView != null && StringUtils.isNotEmpty(greetCardData.getGuidanceText())) {
                    mMessageEditText.setHint(greetCardData.getGuidanceText());
                }
            }
        }
        logCard(greetCardData);
    }

    @Override
    public Intent getIntentData() {
        return getIntent();
    }

    @Override
    public void showFastReply(Bundle bundle) {
        showBigDataReply(bundle);
    }

    @Override
    public void setBigDataFastReply(String bigDataJson) {
        if (StringUtils.isNotEmpty(bigDataJson)) {
            if (aiChatCardManager.compareLevel(ViewPriorityManager.LEVEL_SAY_HI_FAST_REPLY_ITEM)
                    && AIChatCardManager.toFastReplyDataList(bigDataJson) != null
                    && AIChatCardManager.toFastReplyDataList(bigDataJson).size() > 0) {
                aiChatCardManager.setShouldShow(true);
                List<FastReplyData> list = AIChatCardManager.toFastReplyDataList(bigDataJson);
                aiChatCardManager.setFastReplyData(list);
                aiChatCardManager.onInitMessagesFinish(getChatId());
                if (showSayhiReply()) {
                    mMessageEditText.setHint("请输入消息...");
                    StringBuilder ids = new StringBuilder();
                    if (list != null) {
                        for (int i = 0; i < list.size(); i++) {
                            ids.append(list.get(i).getId());
                            if (i + 1 < list.size()) {
                                ids.append(",");
                            }
                        }
                    }
                    EVLog.create(IFastReplyLog.class).showFastReplyItem(remoteUser != null ? remoteUser.momoid : "", ids.toString());

                    aiChatCardManager.setClickBlock((data) -> {
                        MomoMainThreadExecutor.post(() -> {
                            if (data != null) {
                                getIntent().putExtra(AIChatCardManager.KEY_FAST_SAYHI_FROM, "shortcut");
                                getIntent().putExtra(AIChatCardManager.KEY_FAST_SAYHI_TEXT_ID, data.getId());

                                EVLog.create(IFastReplyLog.class).replyFastItemClick(remoteUser != null ? remoteUser.momoid : "", data.getId());

                                Message message = sendTextMessage(data.getText());
                                if (message != null) {
                                    message.status = Message.STATUS_SENDED;
                                }
                                pullMessageInWindow(message);
                                aiChatCardManager.hideFastReplyView();
                                aiChatCardManager.setShouldShow(false);
                            }
                        });
                        return null;
                    });
                }
            }
        }
    }

    @Override
    public void showAiChatCard(AIChatEntryBean aiChat) {
        mMessageEditText.setHint("回复刚看过的动态...");
        if (aiChatCardManager.compareLevel(ViewPriorityManager.LEVEL_AI_CHAT)) {
            try {
                String feedId = getIntent().getStringExtra(ChatHelper.KEY_FROM_FEEDID);
                aiChatCardManager.setMRecommendDetail(aiChat.getRecommendDetail());
                aiChatCardManager.setMBlurDetail(aiChat.getBlurDetail());
                aiChatCardManager.setMCountDetail(aiChat.getCountDetail());
                aiChatCardManager.setMShowRefresh(aiChat.getRefreshShow());
                aiChatCardManager.setFeedId(feedId);
                aiChatCardManager.onShowAIResult(true); // greet 接口新字段
            } catch (Exception e) {
                MDLog.e(TAG, e.getMessage(), e);
            }
        }
    }


    @Override
    public AIChatCardManager getAIChatCard() {
        return aiChatCardManager;
    }

    @Override
    public void showFastReplyCard(Bundle bundle) {
        if (bundle == null) {
            return;
        }
        String jsonString = bundle.getString(AIChatCardManager.KEY_TRANSMIT_REPLY_CARD);
        String remoteId = bundle.getString(AIChatCardManager.KEY_REMOTE_ID);
        String feedId = getIntent().getStringExtra(ChatHelper.KEY_FROM_FEEDID);
        if (TextUtils.isEmpty(jsonString) || !TextUtils.equals(remoteId, chatID)) {
            return;
        }
        mMessageEditText.setHint("回复刚看过的动态...");
        if (aiChatCardManager.compareLevel(ViewPriorityManager.LEVEL_SAY_HI_FAST_REPLY_CARD)) {
            try {
                GreetMessageResult.HelloText helloText = GsonUtils.g().fromJson(
                        jsonString,
                        new TypeToken<GreetMessageResult.HelloText>() {
                        }.getType());
                aiChatCardManager.setShouldShow(true);
                aiChatCardManager.setFastReplyCardData(helloText);
                aiChatCardManager.onInitMessagesFinish(getChatId());
                EVLog.create(IFastReplyLog.class).showReplyCard(remoteId,
                        helloText.helloMsgId,
                        feedId != null ? feedId : "",
                        helloText.source,
                        helloText.logMap);

                if (showSayhiReply()) {
                    aiChatCardManager.setClickCardBlock((data) -> {
                        getIntent().putExtra(AIChatCardManager.KEY_FAST_SAYHI_FROM, "model");
                        getIntent().putExtra(AIChatCardManager.KEY_FAST_SAYHI_TEXT_ID, data.helloMsgId);
                        MomoTaskExecutor.executeUserTask(TAG, new MomoTaskExecutor.Task<Void, Void, Message>() {
                            @Override
                            protected Message executeTask(Void... voids) {
                                Message message = sendTextMessage(data.message);
                                message.status = Message.STATUS_SENDED;
                                return message;
                            }

                            @Override
                            protected void onTaskSuccess(Message message) {
                                if (message == null) {
                                    return;
                                }

                                aiChatCardManager.hideFastReplyView();
                                aiChatCardManager.setShouldShow(false);
                                mMessageEditText.setHint("请输入消息...");

                                SendMsgParams sendMsgParams = new SendMsgParams();
                                sendMsgParams.setText(message.getContent());
                                sendMsgParams.setFeedId(feedId != null ? feedId : "");
                                sendMsgParams.setRemoteId(message.remoteId);
                                sendMsgParams.setSource(getSayHiSource());
                                sendMsgParams.setMessageId(message.msgId);
                                sendMsgParams.setChatSource(data.source);
                                sendMsgParams.setSignCode(data.signCode);
                                sendMsgParams.setHelloMsgId(data.helloMsgId);
                                sendMsgParams.setHeartbeatHiOpen(isOpenHeartbeatSvip);
                                if (data.logMap != null) {
                                    sendMsgParams.setLogMap(new JSONObject(data.logMap).toString());
                                }

                                EVLog.create(IFastReplyLog.class).replyCardClick(remoteId,
                                        helloText.helloMsgId,
                                        feedId != null ? feedId : "",
                                        data.source,
                                        data.logMap);
                                MomoTaskExecutor.executeUserTask(getTaskTag(), new SendRelativeMessageTask(sendMsgParams));
                            }

                            @Override
                            protected void onTaskError(Exception e) {
                                Toaster.show("发送失败，请重试");
                            }
                        });
                        return null;
                    });
                }
            } catch (Exception e) {
                MDLog.printErrStackTrace(LogTag.COMMON, e);
            }

        }
    }


    @Override
    protected void hideInputLayout() {
        mGreetInputContainer.setVisibility(View.GONE);
    }

    @Override
    public void showInputLayout() {
        mGreetInputContainer.setVisibility(View.VISIBLE);
    }

    @Override
    public void adjustGiftOnlyMode(boolean giftOnlyMode) {
        if (giftOnlyMode) {
            if (mRecyclerView != null) {
                mRecyclerView.setVisibility(View.INVISIBLE);
            }
            hideInputMethod();
            hideInputLayout();
            if (tvClose != null) {
                tvClose.setOnClickListener(v -> finish());
            } else {
                MDLog.e("GreetDialog", "关闭按钮不见了？");
            }
            MomoMainThreadExecutor.post(() -> {
                if (mMessageEditText != null) {
                    mMessageEditText.clearFocus();
                }
            });
        } else {
            if (mRecyclerView != null) {
                mRecyclerView.setVisibility(View.VISIBLE);
            }
            showInputLayout();
        }
        showGreetGiftPanelView(giftOnlyMode);
        View panelView = greetChatPageGiftManager.getPanelView();
        if (panelView == null) {
            return;
        }
        ViewGroup.LayoutParams layoutParams = panelView.getLayoutParams();
        greetChatPageGiftManager.giftOnlyMode(giftOnlyMode);

        if (giftOnlyMode) {
            layoutParams.height = contentRootLayout.getLayoutParams().height - UIUtils.getPixels(20);
        } else {
            layoutParams.height = UIUtils.getPixels(250);
        }
        panelView.setLayoutParams(layoutParams);
        //调整内部礼物面板部分尺寸
        View innerContainer = panelView.findViewById(R.id.gift_pannel_inner_container);
        if (innerContainer == null) {
            return;
        }
        LinearLayout.LayoutParams innerContainerLP = (LinearLayout.LayoutParams) innerContainer.getLayoutParams();
        if (innerContainerLP == null) {
            return;
        }
        if (giftOnlyMode) {
            innerContainerLP.height = ViewGroup.LayoutParams.MATCH_PARENT;
            innerContainerLP.bottomMargin = UIUtils.getPixels(25);
        } else {
            innerContainerLP.height = UIUtils.getPixels(225);
            innerContainerLP.bottomMargin = 0;
        }
        innerContainer.setLayoutParams(innerContainerLP);
    }

    private void logCard(@Nullable GreetCardData greetCardData) {
        ExposureEvent.create(ExposureEvent.Type.Normal)
                .page(EVPage.Msg.SayhiCard)
                .action(EVAction.Window.Sayhi)
                .putExtra("to_momo_id", getChatId())
                .putExtra("prompt_text", greetCardData == null ? "" : greetCardData.getPugText())
                .putExtra("prompt_id", greetCardData == null ? "" : greetCardData.getPugId())
                .submit();
    }

    /**
     * 发送新消息后，招呼卡片需要隐藏
     */
    private void hideNewGreetPugCard() {
        if (mGreetCardContainer != null) {
            mGreetCardContainer.removeAllViews();
            mGreetCardContainer.setVisibility(View.GONE);
        }
    }

    private void sendGiftGreetMsg(String giftId) {
        if (mIGreetPresenter != null) {
            String msg = mIGreetPresenter.getGreetMsgByGiftId(giftId);
            if (StringUtils.isNotEmpty(msg)) {
                Message message = sendTextMessage(msg);
                pullMessageInWindow(message);
            }
        }
        hideNewGreetPugCard();
    }

    private void showNormalInputUI() {
        dismissReflowGiftView(true);
        updateAiCardSiteScene(true);
        aiChatCardManager.showFastReplyView();

        mInputBackgoundView.setBackground(null);
        if (mInputGreetGiftBtn != null) {
            mInputGreetGiftBtn.setBackgroundResource(R.drawable.bg_input_greet_gift);
        }
        mMessageInputView.setBackgroundResource(R.drawable.bg_message_input);
        valueAnimator = ValueAnimator.ofFloat(15f, 0f);
        valueAnimator.addUpdateListener(animation -> {
            float margin = (float) animation.getAnimatedValue();
            LinearLayout.LayoutParams ll = (LinearLayout.LayoutParams) mGreetInputContainer.getLayoutParams();
            ll.leftMargin = UIUtils.getPixels(margin);
            ll.rightMargin = UIUtils.getPixels(margin);
            mGreetInputContainer.setLayoutParams(ll);

        });
        valueAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                resetEditBg();
            }

            @Override
            public void onAnimationStart(Animator animation) {
                super.onAnimationStart(animation);
                resetEditBg();
            }
        });
        valueAnimator.start();

    }

    private void showGiftInputUI() {
        aiChatCardManager.hideFastReplyView();
        mInputBackgoundView.setBackgroundResource(R.drawable.bg_ffffff_15_conner);
        if (mInputGreetGiftBtn != null) {
            mInputGreetGiftBtn.setBackgroundResource(R.drawable.bg_input_greet_gift);
        }
        mMessageInputView.setBackgroundResource(R.drawable.bg_message_input_fff9f9f9);
        showAnimator = ValueAnimator.ofFloat(0f, 15f);
        showAnimator.addUpdateListener(animation -> {
            float margin = (float) animation.getAnimatedValue();
            LinearLayout.LayoutParams ll = (LinearLayout.LayoutParams) mGreetInputContainer.getLayoutParams();
            ll.leftMargin = UIUtils.getPixels(margin);
            ll.rightMargin = UIUtils.getPixels(margin);
            mGreetInputContainer.setLayoutParams(ll);
        });
        showAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                resetEditBg();
            }

            @Override
            public void onAnimationStart(Animator animation) {
                super.onAnimationStart(animation);
                resetEditBg();
            }
        });

        showAnimator.start();
    }

    private void showSmallFirst() {
        firstShow = false;
        ViewGroup.LayoutParams llContent = contentRootLayout.getLayoutParams();
        llContent.height = UIUtils.getPixels(SMALL_GREET_UI_HEIGHT);
        contentRootLayout.setLayoutParams(llContent);
        RelativeLayout.LayoutParams cardContainerLayoutParams = (RelativeLayout.LayoutParams) mGreetCardContainer.getLayoutParams();
        cardContainerLayoutParams.topMargin = UIUtils.getPixels(10);
        mGreetCardContainer.setLayoutParams(cardContainerLayoutParams);
    }

    private void showLargeGreetUI() {
        ViewGroup.LayoutParams ll = contentRootLayout.getLayoutParams();
        if (ll.height == UIUtils.getPixels(largeUiHeight)) {
            return;
        }
        stopLargeGreetAnim();
        mLargeValueAnimator = ValueAnimator.ofInt(UIUtils.getPixels(SMALL_GREET_UI_HEIGHT), UIUtils.getPixels(largeUiHeight));
        mLargeValueAnimator.addUpdateListener(animation -> {
            int height = (int) animation.getAnimatedValue();
            ViewGroup.LayoutParams llContent = contentRootLayout.getLayoutParams();
            llContent.height = height;
            contentRootLayout.setLayoutParams(llContent);
        });
        mLargeValueAnimator.start();

        RelativeLayout.LayoutParams cardContainerLayoutParams = (RelativeLayout.LayoutParams) mGreetCardContainer.getLayoutParams();
        cardContainerLayoutParams.topMargin = UIUtils.getPixels(10);
        mGreetCardContainer.setLayoutParams(cardContainerLayoutParams);
    }

    private void showSmallGreetUI() {
        ViewGroup.LayoutParams ll = contentRootLayout.getLayoutParams();
        if (ll.height == UIUtils.getPixels(SMALL_GREET_UI_HEIGHT)) {
            return;
        }

        stopSmallGreetAnim();
        mSmallValueAnimator = ValueAnimator.ofInt(UIUtils.getPixels(largeUiHeight), UIUtils.getPixels(SMALL_GREET_UI_HEIGHT));
        mSmallValueAnimator.addUpdateListener(animation -> {
            int height = (int) animation.getAnimatedValue();
            ViewGroup.LayoutParams llContent = contentRootLayout.getLayoutParams();
            llContent.height = height;
            contentRootLayout.setLayoutParams(llContent);
        });
        mSmallValueAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                msgChatRecycler.scrollToBottom();
                super.onAnimationEnd(animation);
            }
        });
        mSmallValueAnimator.start();

        RelativeLayout.LayoutParams cardContainerLayoutParams = (RelativeLayout.LayoutParams) mGreetCardContainer.getLayoutParams();
        cardContainerLayoutParams.topMargin = UIUtils.getPixels(10);
        mGreetCardContainer.setLayoutParams(cardContainerLayoutParams);
    }

    private void stopSmallGreetAnim() {
        if (mSmallValueAnimator != null) {
            mSmallValueAnimator.cancel();
        }
    }

    private void stopLargeGreetAnim() {
        if (mLargeValueAnimator != null) {
            mLargeValueAnimator.cancel();
        }
    }

    private boolean animateExitGreetCard() {
        if (mGreetExitAnimatorSet != null || mGreetHeadContainer == null) {
            return false;
        }
        GreetDialog.super.hideAllInputMethod();
        mGreetExitAnimatorSet = new AnimatorSet();
        mGreetExitAnimatorSet.setDuration(400);
        View listViewRoot = findViewById(R.id.layout_root);
        int screenHeight = UIUtils.getScreenHeight();
        ObjectAnimator animatorHead = ObjectAnimator.ofFloat(mGreetHeadContainer, View.TRANSLATION_Y, 0, screenHeight);
        ObjectAnimator animatorList = ObjectAnimator.ofFloat(listViewRoot, View.TRANSLATION_Y, 0, screenHeight);
        mGreetExitAnimatorSet.playTogether(animatorHead, animatorList);
        mGreetExitAnimatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                finish();
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                finish();
            }
        });
        mGreetExitAnimatorSet.start();
        return true;
    }

    private boolean isEditTopNoticeVisible() {
        if (editTopNoticeView != null) {
            return editTopNoticeView.isEditTopNoticeVisible();
        }
        return false;
    }

    @Override
    public void showGuideDialog(CommonWhiteDialogParam dialogParam) {
        GreetGuideDialog greetGuideDialog = GreetGuideDialog.getDialog(thisActivity(), dialogParam);
        greetGuideDialog.isShowBtnClose(false);
        showDialog(greetGuideDialog);
    }

    public void greetGiftClick() {
        MomoMainThreadExecutor.cancelAllRunnables(DismissGreetGiftTag);
    }

    private boolean isFromFeedLike() {
        return SOURCE_NOTICE_FEED_LIKE.equals(getIntent().getStringExtra(FEED_SOURCE));
    }

    public void onEvent(DataEvent event) {
        if (EventKeys.Greet.SHOW_GIFT_PANEL.equals(event.getAction())) {
            hideAllInputView();
            showGreetGiftPanelView(true);
        }
    }

    @Override
    public int getPanelType() {
        return ChatPanelHandler.PANEL_TYPE_GREET;
    }

    @Override
    public boolean isFullModel() {
        return false;
    }

    @Override
    public Context getGreetContext() {
        return thisActivity();
    }

    @Override
    protected boolean otherPanelOpen() {
        return isGreetGiftPanelShow();
    }

    /**
     * 获取招呼面板引导送礼文案的异步任务
     */
    private class GreetChatTask extends MomoTaskExecutor.Task<Void, Object, GreetRecommendChatResult> {

        private String mRemoteId;

        public GreetChatTask(String mRemoteId) {
            this.mRemoteId = mRemoteId;
        }

        @Override
        protected GreetRecommendChatResult executeTask(Void... voids) throws Exception {
            GreetRecommendChatResult result = GreetApi.getInstance().getGreetRecommendChat(mRemoteId);
            return result;
        }

        @Override
        protected void onTaskSuccess(GreetRecommendChatResult result) {
            if (result != null && !TextUtils.isEmpty(result.mMsg) && !isEmotionSearching) {//只有接口返回成功，且msg有值才显示
                showTips(mInputGreetGiftBtn, result.mMsg, false);
            }
        }

        @Override
        protected void onTaskError(Exception e) {
            MDLog.printErrStackTrace(TAG, e);
        }
    }

    private void showTips(View tipView, String tipContent, boolean dealTouch) {
        TipManager tipManager = TipManager.bindActivity(thisActivity())
                .setTouchToHideAll(true)
                .setTouchHideNeedNotfiy(false);

        tipManager.checkViewCanShowTip(tipView, v -> {
            TriangleDrawable drawable = new BottomTriangleDrawable().
                    setColor(UIUtils.getColor(R.color.default_tip_color));
            iTip = tipManager
                    .setBackground(UIUtils.getDrawable(R.drawable.bg_corner_8dp_4e7fff))
                    .setTriangles(null, null, null, drawable)
                    .setTouchToHideAll(true)
                    .setTextPadding(DpPxUtil.dp2px(thisActivity(), 20), DpPxUtil.dp2px(thisActivity(), 13),
                            DpPxUtil.dp2px(thisActivity(), 20), DpPxUtil.dp2px(thisActivity(), 13))
                    .setTextSize(DpPxUtil.dp2px(thisActivity(), 15)) //px textsize 15sp  距离10dp
                    .setMarginEdge(UIUtils.getPixels(10))
                    .showTipView(tipView, tipContent, 0, UIUtils.getPixels(-10F), ITip.Triangle.BOTTOM)
                    .autoHide(5000L);
            if (dealTouch) {
                tipManager.setOnClickListener(v1 -> {
                    try {
                        tipManager.removeAllTipView();
                    } catch (Throwable throwable) {
                        MDLog.printErrStackTrace(LogTag.COMMON, throwable);
                    }
                });
            }
        });
    }

    @Override
    public void sendGreetMessage(int greetId, String greetContent) {

    }

    @Override
    public void getGreetRecommendMessageSuccess(List<GreetMessageResult.GreetMsg> msgBeanList) {

    }

    private boolean isShowingPushGuide() {
        if (viewStubPushGuide != null && viewStubPushGuide.getVisibility() == View.VISIBLE) {
            return true;
        }
        return false;
    }

    private boolean isNeedShowPushGuide() {
        if (SayhiPushGuideABTest.INSTANCE.isShowGuideTest()
                && com.immomo.momo.util.MomoKit.INSTANCE.getSystemNotificationStatus() == 0) {
            int timeInterval = SessionAppConfigV2Getter.get().greetPushGuideInterval();
            long lastPushTipsGuideTime = KV.getUserLong(SPKeys.PushGuide.KEY_GREET_SHOW_PUSH_GUIDE_STAMP, 0L);
            return System.currentTimeMillis() - lastPushTipsGuideTime > timeInterval * 3600000L;
        }
        return false;
    }

    private void initPushGuideTips() {
        if (com.immomo.momo.util.MomoKit.INSTANCE.getSystemNotificationStatus() != 0) {
            if (isShowingPushGuide()) {
                viewStubPushGuide.setVisibility(View.GONE);
                return;
            }
            return;
        }
        if (isShowingPushGuide()) {
            return;
        }
        if (isNeedShowPushGuide()) {
            showPushGuide();
        }
    }

    private void showPushGuide() {
        if (!viewStubPushGuide.isInflate()) {
            EVLog.create(IGreetPushGuideLog.class).showGuide();
        }
        ivPushGuideOpen = viewStubPushGuide.getStubView().findViewById(R.id.tv_open);
        ivPushGuideOpen.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (PermissionUtil.biggerAndroid13()) {
                    PermissionUtil.getInstance().check13Notification(thisActivity());
                } else {
                    com.immomo.momo.util.MomoKit.INSTANCE.gotoApplicationNotifySettings();
                }
                EVLog.create(IGreetPushGuideLog.class).openNotice();
            }
        });
        viewStubPushGuide.setVisibility(View.VISIBLE);
        KV.saveUserValue(SPKeys.PushGuide.KEY_GREET_SHOW_PUSH_GUIDE_STAMP, System.currentTimeMillis());
    }

    private void sendGreetMessageSourceAndTargetId(Message message) {
        Map<String, Object> map = new HashMap<>();
        map.put("extra", message.newSource);
        map.put("text", message.getContent());
        map.put("targetId", message.remoteId);

        GlobalEventManager.Event event = new GlobalEventManager.Event("NTF_NATIVE_IM_SAY_HELLO")
                .src(GlobalEventManager.EVN_NATIVE)
                .dst(GlobalEventManager.EVN_LUA)
                .msg(map);
        GlobalEventManager.getInstance().sendEvent(event);
    }

    //======================== 招呼 end ====================================

}