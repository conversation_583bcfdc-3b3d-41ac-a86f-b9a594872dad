package com.immomo.momo.message.sayhi.itemmodel.bean;

import android.text.TextUtils;

import com.immomo.momo.message.sayhi.SayHiArgs;
import com.immomo.momo.message.sayhi.utils.FemaleRedEnvelopeHelper;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class LikeSayHi {
    public static class Requst {
        //1:已读、2:已左滑、3:已右滑、4:已删除、5:已悄悄查看、6:全部忽略未读 7:举报
        public static final int TYPE_READ = 1;
        public static final int TYPE_LEFT = 2;
        public static final int TYPE_RIGHT = 3;
        public static final int TYPE_DEL = 4;
        public static final int TYPE_SEE = 5;
        public static final int TYPE_IGNORE_ALL = 6;
        public static final int TYPE_IGNORE_GIFT = 7;
        public static final int TYPE_IGNORE_LIVE = 8;
        public static final int TYPE_REPORT = 9;


        private CharSequence messageText;
        private int messageType;
        private Map<String, String> toApiParams;
        private String source;
        private int like;
        private SayHiInfo info;

        public int consumeType;
        private String remoteId;
        private long lastGreetTime;

        private String actionType;

        public boolean justConsumeType; // 只传参数consumeType和momoid

        public boolean needPostMomoid = false;

        public boolean isRedPacket = false;

        public boolean isHeartbeatSvip = false; // 是否是心动招呼

        public Requst(int like, String source, SayHiInfo info, CharSequence messageText,
                      int messageType, Map<String, String> toApiParams, int consumeType, String actionType) {
            this.source = source;
            this.like = like;
            this.info = info;
            this.toApiParams = toApiParams;
            this.messageText = messageText;
            this.messageType = messageType;
            this.consumeType = consumeType;
            this.actionType = actionType;
        }

        public Requst(String remoteId, int consumeType, int like) {
            this.remoteId = remoteId;
            this.consumeType = consumeType;
            this.like = like;
        }

        public Requst(String remoteId, int consumeType) {
            this.remoteId = remoteId;
            this.consumeType = consumeType;
        }

        public Requst(int consumeType, long lastGreetTime) {
            this.consumeType = consumeType;
            this.lastGreetTime = lastGreetTime;
        }

        public void setRemoteId(String remoteId) {
            this.remoteId = remoteId;
        }

        public Map<String, String> toMap() {
            Map<String, String> params = new HashMap<>();
            params.put("remoteid", getMomoid());
            params.put("consume_type", consumeType + "");
            if (justConsumeType) {
                return params;
            }
            params.put("like", String.valueOf(like));
            params.put("allmsgid", info != null ? info.getAllMsgId() : "");
            if (!isReplyMessage()) { // 需要api代发消息
                params.put("like_msg", info != null ? info.mLikeMsg : "");
            }
            params.put("is_replyed", isReplyMessage() ? "1" : "0");

            if (lastGreetTime > 0) {
                params.put("last_greet_time", lastGreetTime + "");
            }
            if (toApiParams != null) {
                params.putAll(toApiParams);
            }
            if (info != null && info.getLogMapString() != null) {
                params.put("logmap", info.getLogMapString());
            }
            String marks = info != null ? StringUtils.join(info.getMarks(), ",") : "";
            if (!TextUtils.isEmpty(marks)) {
                params.put("marks", marks);
            }
            if (StringUtils.notEmpty(actionType)) {
                params.put("action_type", actionType);
            }
            User user = info != null ? info.user : null;
            String distance = user != null && user.showDistance() ? user.distanceString : "";
            if (!TextUtils.isEmpty(distance)) {
                params.put("distance", distance);
            }
            String online = user != null && user.feedOnlineStatus == 1 ? "在线" : "";
            if (!TextUtils.isEmpty(online)) {
                params.put("online", online);
            }
            String avatar = user != null ? user.getAvatar() : "";
            if (!TextUtils.isEmpty(avatar)) {
                params.put("photoID", avatar);
            }
            params.put("is_red_packet", FemaleRedEnvelopeHelper.INSTANCE.isRedEnvelopeHi(info) ? "1" : "0");
            if (StringUtils.notEmpty(source)) {
                params.put("source", source);
            }
            params.put("isRedPacket", isRedPacket ? "1" : "0");
            return params;
        }

        public boolean isReplyMessage() {
            return !TextUtils.isEmpty(messageText);
        }

        public String getMomoid() {
            if (!TextUtils.isEmpty(remoteId)) {
                return remoteId;
            } else {
                return info != null ? info.getMomoid() : "";
            }
        }

        public boolean isLike() {
            return this.like == SayHiArgs.LIKE;

        }

        public boolean isIgnore() {
            return this.like == SayHiArgs.IGNORE;
        }

        public SayHiInfo getInfo() {
            return info;
        }

        public int getMessageType() {
            return messageType;
        }

        public CharSequence getMessageText() {
            return messageText;
        }

        public User getUser() {
            return info != null ? info.getUser() : null;
        }

        public int getLike() {
            return like;
        }

        public boolean isHeartbeatSvip() {
            return isHeartbeatSvip;
        }

        public void setHeartbeatSvip(boolean heartbeatSvip) {
            isHeartbeatSvip = heartbeatSvip;
        }
    }

}