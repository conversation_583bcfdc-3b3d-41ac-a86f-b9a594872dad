package com.immomo.momo.message.paper

import android.os.Bundle
import android.os.Looper
import android.view.View
import com.cosmos.mdlog.MDLog
import com.immomo.framework.base.BaseFragment
import com.immomo.momo.eventbus.DataEvent
import de.greenrobot.event.EventBus

/**
 * <AUTHOR>
 * @data 2020-12-18.
 */


abstract class BasePaperFragment : BaseFragment() {

    val logTag = "BasePaperFragment"

    override fun getLayout(): Int = getPageLayout()


    override fun onLoad() {
        onPageLoad()
    }

    override fun initViews(contentView: View?) {
        initPageViews(contentView)
        EventBus.getDefault().register(this)
    }


    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    abstract fun getPageLayout(): Int

    abstract fun initPageViews(contentView: View?)

    abstract fun onPageLoad()

    /**
     * 定义容器id
     * 定级Fragment(作为容器)返回 0 即可
     */
    abstract fun getContainerId(): Int


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        MDLog.d(logTag, "${this::class.java.simpleName} onCreate")

    }

    override fun onResume() {
        super.onResume()
        MDLog.d(logTag, "${this::class.java.simpleName} onResume")
    }

    open fun onEvent(event: DataEvent<Any>) {
        if (Looper.myLooper() != Looper.getMainLooper()) {
            MDLog.e(logTag, "${event.action},请在主线程发送事件")
        }

    }

}