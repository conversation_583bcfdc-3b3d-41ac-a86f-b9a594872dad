package com.immomo.momo.message.task;

import static com.immomo.momo.share2.ShareConstant.KEY_MOMO_CONTACTS;
import static com.immomo.momo.share2.ShareConstant.KEY_MOMO_FEED;
import static com.immomo.momo.share2.ShareConstant.KEY_QQ;
import static com.immomo.momo.share2.ShareConstant.KEY_WEIXIN_FRIEND;
import static com.immomo.momo.share2.ShareConstant.KEY_WEIXIN_QUAN;
import static com.immomo.momo.share2.listeners.GroupInviteShareListener.FROM_GROUP_INVITE_ACTIVITY;

import android.app.Activity;

import com.cosmos.mdlog.MDLog;
import com.immomo.LogTag;
import com.immomo.android.router.share.ShareDialogConfig;
import com.immomo.android.router.share.ShareRouter;
import com.immomo.android.router.share.model.PageConfig;
import com.immomo.framework.imageloader.ImageLoaderUtil;
import com.immomo.framework.imageloader.ImageType;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.group.bean.Group;
import com.immomo.momo.share3.ShareConst;
import com.immomo.momo.share3.listeners.GroupInviteShareListener;
import com.immomo.momo.util.MediaFileUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import info.xudshen.android.appasm.AppAsm;

/**
 * <AUTHOR>
 * @date 2019/5/9
 * @description 群组分享弹窗调用
 */
public class GroupShareTask extends MomoTaskExecutor.Task<Object, Object, Boolean> {

    private Activity activity;
    private Group group;
    private GroupInviteShareListener shareListener;

    public GroupShareTask(Activity activity, Group group) {
        this.group = group;
        this.activity = activity;
    }

    @Override
    protected Boolean executeTask(Object... objects) throws Exception {
        initShareListenerInfo();
        return true;
    }

    @Override
    protected void onTaskError(Exception e) {
        Toaster.show("初始化分享数据失败");
    }

    @Override
    protected void onTaskSuccess(Boolean success) {
        if (activity == null) {
            Toaster.show("初始化分享数据失败");
            return;
        }

        ShareDialogConfig.Builder builder = new ShareDialogConfig.Builder(activity);

        com.immomo.android.router.share.model.ShareData shareData = new com.immomo.android.router.share.model.ShareData();
        shareData.fromType = ShareConst.GROUP;
        shareData.sceneId = ShareConst.SCENE_GROUP_INVITE;
        shareData.shareDialogMsg = "你将把群卡片分享给 %s?";
        shareData.shareDialogTitle = "分享";
        shareData.msgFrom = group.gid;
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("gid", group.gid);
            shareData.extra = jsonObject.toString();
        } catch (JSONException e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
        builder.mShareData(shareData)
               .useCommonShare(true)
               .loadShareAppFromServer(true)
               .mShareListener(shareListener);

        // 分享选项
        List<String> apps = new ArrayList<>();
        apps.add(KEY_MOMO_FEED);
        apps.add(KEY_MOMO_CONTACTS);
        apps.add(KEY_WEIXIN_FRIEND);
        apps.add(KEY_QQ);
        apps.add(KEY_WEIXIN_QUAN);
        builder.mPageConfig(new PageConfig.Builder()
                                    .apps(apps).supportDark(true)
                                    .build());

        AppAsm.getRouter(ShareRouter.class).showShareDialog(builder.build());
    }

    /**
     * 初始化分享弹窗
     */
    private void initShareListenerInfo() {
        shareListener = new GroupInviteShareListener(activity);

        int fromTag = FROM_GROUP_INVITE_ACTIVITY;
        int showIndex = 2;
        int hideMode;
        String iconUrl;
        String name;
        String sign;
        File shareFile;
        if (group != null) {
            hideMode = group.hideMode;
            iconUrl = ImageLoaderUtil.getConfiguration().getImageIdTranslater()
                                     .generateImageUrl(group.getLoadImageId(), ImageType.IMAGE_TYPE_ALBUM_SMALL);
            name = group.name;
            sign = group.sign;
            if (group.photos != null && group.photos.length > 0 && group.photos[0] != null) {
                shareFile = MediaFileUtil.getImageFile(group.photos[0], ImageType.IMAGE_TYPE_ALBUM_SMALL);
            } else {
                shareFile = null;
            }
        } else {
            hideMode = 0;
            iconUrl = "";
            name = "";
            sign = "";
            shareFile = null;
        }
        shareListener.setData(fromTag, showIndex, hideMode, iconUrl, name, sign, group != null ? group.gid : "", shareFile);
    }
}
