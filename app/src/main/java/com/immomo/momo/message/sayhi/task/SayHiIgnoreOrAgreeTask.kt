package com.immomo.momo.message.sayhi.task

import android.os.Bundle
import android.text.TextUtils
import com.cosmos.mdlog.MDLog
import com.immomo.framework.task.BaseDialogTask
import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.momo.LogTag
import com.immomo.momo.MomoKit
import com.immomo.momo.maintab.model.AbsSession
import com.immomo.momo.maintab.sessionlist.ISessionListView2
import com.immomo.momo.maintab.sessionlist.SessionListFragment
import com.immomo.momo.message.NewSayUIConfigV1
import com.immomo.momo.message.sayhi.SayHiUiTest
import com.immomo.momo.message.sayhi.activity.SayHiListActivity.Companion.Action_Session_REPLAY
import com.immomo.momo.message.sayhi.itemmodel.bean.IgnoreOrPassParam
import com.immomo.momo.message.sayhi.utils.NewSayHiSessionFlowUtil
import com.immomo.momo.messages.service.SingleMsgService
import com.immomo.momo.protocol.http.SayHiApi
import com.immomo.momo.protocol.imjson.receiver.MessageKeys
import com.immomo.momo.service.bean.Session
import com.immomo.momo.service.sessions.SessionService
import com.immomo.momo.util.StringUtils

class SayHiIgnoreOrAgreeTask(
    private val pm: IgnoreOrPassParam.Requst,
    var showLoading: Boolean = true
) :
    BaseDialogTask<Any, Any, Boolean>("") {

    override fun onPreTask() {
        if (showLoading) {
            super.onPreTask()
        }
    }

    override fun executeTask(vararg objects: Any): Boolean {
        if (StringUtils.isEmpty(pm.remoteId) || pm.sayhiSession == null) {
            return false
        }
        NewSayHiSessionFlowUtil.onDealHiCard()
        if (pm.isLike) {
            // 转移招呼会话到正常会话列表中去（只是数据库迁移）
            SessionService.getInstance().moveSayhiToSession(pm.remoteId)
            SingleMsgService.getInstance().updateMessagesIgnore(pm.remoteId)
        } else if (pm.isIgnore) {
            SingleMsgService.getInstance().updateSayhiInnerIgnore(pm.remoteId)
        }
        return SayHiApi.getInstance().postIgnoreOrLike(pm)
    }

    override fun onTaskSuccess(aBoolean: Boolean) {
        if (aBoolean) {
            if (pm.isLike) {
                broadcastSessionChange(pm.remoteId)
                updateReadStatus(pm.remoteId)
            } else if (pm.isIgnore) {
                updateReadStatus(pm.remoteId)
            }
        }
    }

    private fun updateReadStatus(momoid: String?, pass: Boolean = false) {
        if (StringUtils.isEmpty(momoid)) {
            return
        }
        MomoTaskExecutor.executeUserTask(
            hashCode(),
            object : MomoTaskExecutor.Task<Any?, Any?, Any?>("") {
                override fun executeTask(objects: Array<Any?>): Any? {
                    // 通过忽略变成read状态
                    SessionService.getInstance().updateSayHiRead(momoid)
                    return null
                }

                override fun onTaskSuccess(o: Any?) {
                    val bundle = Bundle()
                    bundle.putString(ISessionListView2.Key_SessionId, Session.ID.SayhiSession)
                    bundle.putInt(ISessionListView2.Key_SessionType, AbsSession.TYPE_SAYHI)
                    bundle.putString(ISessionListView2.Key_ChatId, momoid)
                    MomoKit.getApp()
                        .dispatchMessage(bundle, ISessionListView2.Action_SessionChanged)
                    if (pass) {
                        bundle.putString(MessageKeys.Key_RemoteId, momoid)
                        MomoKit.getApp().dispatchMessage(
                            bundle, Action_Session_REPLAY
                        )
                    }
                }

                override fun onTaskError(e: Exception) {
                    MDLog.printErrStackTrace(LogTag.COMMON, e)
                }
            })
    }

    private fun broadcastSessionChange(momoid: String?) {
        // 转移招呼会话到正常会话列表中去后，发出广播通知对话列表更新
        if (!TextUtils.isEmpty(momoid)) {
            val bundle = Bundle()
            bundle.putString(SessionListFragment.Key_SessionId, "u_$momoid")
            bundle.putInt(SessionListFragment.Key_SessionType, Session.TYPE_CHAT)
            bundle.putInt(SessionListFragment.Key_SayHiSessionLikeType, pm.like)
            bundle.putInt(SessionListFragment.Key_SayHiSessioncardType, pm.consumeType)
            if (NewSayUIConfigV1.isUserNewUI()) {
                bundle.putString(MessageKeys.Key_RemoteId, momoid)
                bundle.putString(ISessionListView2.Key_ChatId, momoid)
            }
            MomoKit.getApp().dispatchMessage(bundle, SessionListFragment.Action_SessionChanged)
            if (SayHiUiTest.isTest()) {
                bundle.putString(MessageKeys.Key_RemoteId, momoid)
                MomoKit.getApp().dispatchMessage(bundle, Action_Session_REPLAY)
            }
        }
    }
}