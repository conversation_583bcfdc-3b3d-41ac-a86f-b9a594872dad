package com.immomo.momo.message.view

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.immomo.framework.base.BaseActivity
import com.immomo.framework.task.BaseDialogTask
import com.immomo.mmstatistics.event.ClickEvent
import com.immomo.mmstatistics.event.ExposureEvent
import com.immomo.mmstatistics.event.TaskEvent
import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.mmutil.toast.Toaster
import com.immomo.momo.MomoKit
import com.immomo.momo.R
import com.immomo.momo.android.broadcast.ReflushUserProfileReceiver
import com.immomo.momo.protocol.http.UserApi
import com.immomo.momo.statistics.EVAction
import com.immomo.momo.statistics.EVPage
import com.immomo.momo.util.StringUtils
import java.lang.Exception

class SyncQAToProfileDialogActivity : BaseActivity() {

    private var questionTv: TextView? = null
    private var answerTv: TextView? = null
    private var titleTv: TextView? = null
    private var descTv: TextView? = null
    private var confirmBtn: Button? = null
    private var cancelTv: TextView? = null
    private var closeImg: ImageView? = null
    private var containerView: View? = null
    private var questionId: String? = null
    private var question: String? = null
    private var answer: String? = null
    private var rootView: View? = null
    private var remoteId: String? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_sync_qa_to_profile)
        window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        window.statusBarColor = Color.TRANSPARENT
        questionId = intent.getStringExtra(KEY_ID)
        question = intent.getStringExtra(KEY_TITLE)
        answer = intent.getStringExtra(KEY_ANSWER)
        remoteId = intent.getStringExtra(KEY_REMOTE_ID)
        initView()
        initData()
        initEvent()
    }

    fun initView() {
        rootView = findViewById(R.id.root)
        questionTv = findViewById(R.id.question) as TextView?
        answerTv = findViewById(R.id.answer) as TextView?
        titleTv = findViewById(R.id.title) as TextView?
        descTv = findViewById(R.id.content) as TextView?
        confirmBtn = findViewById(R.id.confirm) as Button?
        cancelTv = findViewById(R.id.cancel) as TextView?
        closeImg = findViewById(R.id.close) as ImageView?
        containerView = findViewById(R.id.container)
    }

    fun initData() {
        question?.let {
            questionTv?.text = it
        }
        answer?.let {
            answerTv?.text = it
        }
        titleTv?.text = "她喜欢了你的回答"
        descTv?.text = "展示到个人主页，吸引更多人"
    }

    fun initEvent() {
        confirmBtn?.setOnClickListener {
            confirmClickLog()
            MomoTaskExecutor.executeUserTask(hashCode(), EditQaTask())
            finish()
        }
        cancelTv?.setOnClickListener { finish() }
        closeImg?.setOnClickListener { finish() }
        rootView?.setOnClickListener { finish() }
        containerView?.setOnClickListener { }
    }

    fun confirmClickLog() {
        ClickEvent.create()
            .page(EVPage.Msg.ChatPage)
            .action(EVAction.Float.QuestionGuide)
            .putExtra("momoid", remoteId)
            .submit()
    }

    override fun onResume() {
        super.onResume()
        exposureLog()
    }

    fun exposureLog() {
        ExposureEvent.create(ExposureEvent.Type.Normal)
            .page(EVPage.Msg.ChatPage)
            .action(EVAction.Float.QuestionGuide)
            .putExtra("question_id", questionId)
            .putExtra("momoid", remoteId)
            .submit()
    }

    private inner class EditQaTask() : BaseDialogTask<Any?, Any, String?>("") {
        override fun executeTask(vararg params: Any?): String? {
            return UserApi.getInstance().editGreetQA(questionId, answer)
        }

        override fun onTaskSuccess(result: String?) {
            super.onTaskSuccess(result)
            taskLog(true)
            if (StringUtils.isNotEmpty(result)) {
                Toaster.show(result)
            }
            val intent = Intent(ReflushUserProfileReceiver.ACTION)
            intent.putExtra(ReflushUserProfileReceiver.KEY_FROM_ACTIVITY, activity?.javaClass?.simpleName)
            intent.putExtra(ReflushUserProfileReceiver.KEY_IS_REFRESH_ALL_USER, true)
            intent.putExtra(ReflushUserProfileReceiver.KEY_NEED_REGRESH_FROM_API, true)
            sendBroadcast(intent)
        }

        override fun onTaskError(e: Exception?) {
            super.onTaskError(e)
            taskLog(false)
        }
    }

    fun taskLog(isSuccess: Boolean) {
        TaskEvent.create().page(EVPage.Msg.ChatPage)
            .action(EVAction.Content.QuestionSet)
            .status(if (isSuccess) TaskEvent.Status.Success else TaskEvent.Status.Fail)
            .type("publish")
            .putExtra("question_id", questionId)
            .putExtra("momoid", remoteId)
            .putExtra("set_source", "weak")
            .submit()
    }


    companion object {
        const val KEY_ID = "qId"
        const val KEY_TITLE = "qTitle"
        const val KEY_ANSWER = "qAnswer"
        const val KEY_REMOTE_ID = "fr"

        fun startActivity(context: Context?, id: String?, title: String?, anwser: String?, remoteId: String?) {
            val intent = Intent(context, SyncQAToProfileDialogActivity::class.java)
            intent.putExtra(KEY_ID, id)
            intent.putExtra(KEY_TITLE, title)
            intent.putExtra(KEY_ANSWER, anwser)
            intent.putExtra(KEY_REMOTE_ID, remoteId)
            context?.startActivity(intent)

        }
    }

}