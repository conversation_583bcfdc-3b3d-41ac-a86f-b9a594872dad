package com.immomo.momo.message.sayhi.widget;

import android.content.Context;
import android.graphics.Color;
import android.view.View;
import android.widget.RelativeLayout;

import com.immomo.framework.utils.UIUtils;
import com.immomo.momo.R;
import com.immomo.momo.likematch.widget.AbstractThemePopupWindow;
import com.immomo.momo.personalprofile.usecase.LogProfileUtils;

import java.util.List;

/**
 * Created by lei.jialin on 2019/7/16.
 */
public class HintPopupWindow extends AbstractThemePopupWindow {
    public static final int LEFT_OR_RIGHT_SLIDE = 0x3001;  // 左右滑带有解释的引导弹窗
    public static final int WISH_SETTING = 0x3002;  // 愿望问答设置

    public HintPopupWindow(Context context) {
        super(context);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.layout_pop_up_window_hint;
    }

    @Override
    protected boolean autoDismissAfterClick(int type) {
        return true;
    }

    @Override
    protected void fillTypeData(int type, List<CharSequence> descList, String clickGoto, String guidephoto) {
        switch (type) {
            case LEFT_OR_RIGHT_SLIDE:
                loadTitles(descList);
                noDivider();
                setOnlySingleButtonBlue();
                loadGuidePhoto(guidephoto);
                singleButton.setText("知道了");
                break;
            case WISH_SETTING:
                loadTitles(descList);
                noDivider();
                setOnlySingleButtonBlue();
                resizeMargin(guideDescTv, 0, UIUtils.getPixels(40), 0, 0);
                guideIv.setVisibility(View.GONE);
                tvLink.setVisibility(View.VISIBLE);
                tvLink.setText(R.string.talk_about_it_later);
                tvLink.setOnClickListener(v -> {
                    LogProfileUtils.INSTANCE.logSayhi5TimesWishSettingClick("notset");
                    dismiss();
                });
                singleButton.setText(R.string.goto_setting_now);
                break;
        }
    }

    private void setOnlySingleButtonBlue() {
        singleButton.setPadding(UIUtils.getPixels(80), UIUtils.getPixels(15), UIUtils.getPixels(80), UIUtils.getPixels(15));
        singleButton.setBackgroundResource(R.drawable.bg_30dp_round_corner_blue_3bb3fa);
        singleButton.setTextColor(Color.WHITE);
        tvLink.setVisibility(View.INVISIBLE);
    }


    @Override
    protected void logClick(int type, String btn, int logType) {
        switch (type) {
            case WISH_SETTING:
                LogProfileUtils.INSTANCE.logSayhi5TimesWishSettingClick("set");
                break;
        }
    }

    protected void resizeMargin(View view, Integer leftMargins, Integer topMargins, Integer rightMargins, Integer bottomMargins) {
        RelativeLayout.LayoutParams p = (RelativeLayout.LayoutParams) view.getLayoutParams();
        p.setMargins(leftMargins != null ? leftMargins : p.leftMargin,
                topMargins != null ? topMargins : p.topMargin,
                rightMargins != null ? rightMargins : p.rightMargin,
                bottomMargins != null ? bottomMargins : p.bottomMargin
        );
        view.setLayoutParams(p);
    }
}
