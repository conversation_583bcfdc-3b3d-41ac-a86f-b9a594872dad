package com.immomo.momo.message.paper.common.timely.statemachine

import com.immomo.framework.statemachine.AbstractState

abstract class AbsBackToCloseState(stateManager: TimelyStateManager) :
    AbstractState<TimelyStateManager>(stateManager) {

    fun backToCloseState() {
        transition {
            TimelyCloseState(stateMachine).apply {
                this.doClose()
            }
        }
    }

    override fun hashTag() = hashCode()

    fun rollback2Ready() {
        val timelyReadyState = TimelyReadyState(stateMachine)
        transition { timelyReadyState }

    }
}