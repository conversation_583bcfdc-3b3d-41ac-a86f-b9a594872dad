package com.immomo.momo.message.sayhi.activity.reflect

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.os.Bundle
import android.os.SystemClock
import android.view.View
import android.view.WindowManager
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.view.animation.Animation.AnimationListener
import android.view.animation.AnimationSet
import android.view.animation.DecelerateInterpolator
import android.view.animation.TranslateAnimation
import android.widget.ImageView
import android.widget.TextView
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.interpolator.view.animation.FastOutLinearInInterpolator
import androidx.interpolator.view.animation.LinearOutSlowInInterpolator
import androidx.lifecycle.lifecycleScope
import com.cosmos.mdlog.MDLog
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.framework.base.BaseActivity
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.kotlin.ImageType
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.utils.UIUtils
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.mmutil.task.MomoMainThreadExecutor
import com.immomo.momo.R
import com.immomo.momo.appconfig.model.AppConfigV2
import com.immomo.momo.globalevent.GlobalEventManager
import com.immomo.momo.likematch.slidestack.BaseSlideStackView
import com.immomo.momo.likematch.slidestack.SlideConst
import com.immomo.momo.message.NewSayHiPageConfigV2
import com.immomo.momo.message.NewSayUIConfigV1
import com.immomo.momo.message.apt.NewSayHiPageConfigV2Getter
import com.immomo.momo.message.sayhi.NewSayHiStackCache
import com.immomo.momo.message.sayhi.SayHiArgs
import com.immomo.momo.message.sayhi.activity.HiCardStackActivity
import com.immomo.momo.message.sayhi.itemmodel.bean.LikeSayHi
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiStackCardInfo
import com.immomo.momo.message.sayhi.presenter.ISayHiStackCardPresenter
import com.immomo.momo.message.sayhi.presenter.NewSayHiStackCardInnerPresenter
import com.immomo.momo.message.sayhi.stack.NewSayHICardSlideCard
import com.immomo.momo.message.sayhi.stack.NewSayHiCardStackAdapter
import com.immomo.momo.message.sayhi.stack.NewSayHiCardStackView
import com.immomo.momo.message.sayhi.utils.NewSayHiConst
import com.immomo.momo.message.sayhi.utils.NewSayHiReportUtil
import com.immomo.momo.message.sayhi.utils.OnSendFinishListener
import com.immomo.momo.message.sayhi.utils.SayHiQuickReplyHelperView
import com.immomo.momo.message.sayhi.widget.NewHiCardReloadTagView
import com.immomo.momo.platform.utils.PlatformReportHelper
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.math.abs

class NewSayHiCardActivity : BaseActivity(), GlobalEventManager.Subscriber {

    companion object {

        const val TAG_SLIDE_CARD = "tag_slide_card"

        val TAG_SLIDE_CARD_EMPTY = "tag_slide_card_empty".hashCode() // 卡片滑空

        fun start(
            context: Context?,
            clickView: View?,
            pageSource: String?,
            cardStackCardInfo: SayHiStackCardInfo? = null,
            closeBigCardAni: Boolean = false
        ) {
            if (closeBigCardAni) {
                startByPosition(
                    context, UIUtils.getPixels(20F), UIUtils.getPixels(200F),
                    clickView, pageSource, false, cardStackCardInfo, closeBigCardAni = true
                )
            } else if (NewSayHiPageConfigV2.cardStackAniEnableConfig) {
                (context as? BaseActivity?)?.lifecycleScope?.launch(MMDispatchers.User) {
                    NewSayHiConst.jumpStackCardBg = getViewBitmap(clickView)
                    withContext(MMDispatchers.Main) {
                        var x = 0
                        var y = 0
                        clickView?.also {
                            val location = IntArray(2)
                            clickView.getLocationOnScreen(location)
                            x = location[0]
                            y = location[1]
                        }
                        startByPosition(
                            context, x, y, clickView, pageSource, false, cardStackCardInfo
                        )
                    }
                }
            } else {
                startByPosition(context, 0, 0, clickView, pageSource, true, cardStackCardInfo)
            }
        }

        fun startByPosition(
            context: Context?,
            x: Int,
            y: Int,
            clickView: View?,
            pageSource: String?,
            filterAni: Boolean,
            cardStackCardInfo: SayHiStackCardInfo?,
            closeBigCardAni: Boolean = false
        ) {
            val activity = (context as? Activity) ?: return
            kotlin.runCatching {
                val intent = Intent(activity, NewSayHiCardActivity::class.java)
                intent.putExtra(NewSayHiConst.CARD_PARAM_VIEW_X, x)
                intent.putExtra(NewSayHiConst.CARD_PARAM_VIEW_Y, y)
                intent.putExtra(NewSayHiConst.CARD_EMPTY_TRANS_ANIMATION, clickView == null)
                intent.putExtra(NewSayHiConst.CARD_FILTER_ANIMATION, filterAni)
                intent.putExtra(NewSayHiConst.CARD_CLOSE_BG_CARD_ANI, closeBigCardAni)
                intent.putExtra(HiCardStackActivity.KEY_FROM_SOURCE, pageSource.safe())
                cardStackCardInfo?.also {
                    intent.putExtra(NewSayHiConst.CARD_PARAM_CLICK_CARD, it.sayHiInfo.momoid)
                }
                activity.startActivityForResult(
                    intent,
                    NewSayHiConst.STACK_CARD_REQUEST_CODE
                )
                activity.overridePendingTransition(0, R.anim.anim_alpha_out)
            }
        }

        private fun getViewBitmap(view: View?): Bitmap? {
            view ?: return null
            var bitmap: Bitmap? = null
            kotlin.runCatching {
                var width = view.width
                var height = view.height
                if (width <= 0) {
                    width = NewSayHiConst.cardWidth
                }
                if (height <= 0) {
                    height = NewSayHiConst.cardHeight
                }
                bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
                    .apply {
                        val canvas = Canvas(this)
                        view.draw(canvas)
                    }
            }.onFailure {
            }
            return bitmap
        }

        val keyboardSwitchHeight = UIUtils.getPixels(200f)
    }

    private val sayHiQuickReplyHelperView by lazy { findViewById(R.id.quick_reply_view) as SayHiQuickReplyHelperView }
    private val slideStackView by lazy { findViewById(R.id.slideStackView) as NewSayHiCardStackView }
    private val tvIndex by lazy { findViewById(R.id.tv_index) as TextView }
    private val imgClose by lazy { findViewById(R.id.img_close) as View }
    private val imgCloseTop by lazy { findViewById(R.id.close_container) as View }
    private val iconSayhiReplyBtn by lazy { findViewById(R.id.icon_sayhi_reply_btn) as View }
    private val iconSayhiIgnoreBtn by lazy { findViewById(R.id.icon_sayhi_ignore_btn) as View }
    private val iconSayhiConfirmBtn by lazy { findViewById(R.id.icon_sayhi_confirm_btn) as View }
    private val iconSayhiIgnoreBtnBg by lazy { findViewById(R.id.icon_sayhi_ignore_btn_bg) as View }
    private val iconSayhiConfirmBtnBg by lazy { findViewById(R.id.icon_sayhi_confirm_btn_bg) as View }
    private val containerTinyCard by lazy { findViewById(R.id.iv_card_bg) as View }
    private val reloadRefreshTag by lazy { findViewById(R.id.reload_tag) as NewHiCardReloadTagView }
    private val bgView by lazy { findViewById(R.id.bg_view) as View }
    private val viewContainer by lazy { findViewById(R.id.view_container) as View }
    private val emptyView by lazy { findViewById(R.id.empty_view) as TextView }
    private val rfTinyCardView by lazy { findViewById(R.id.rf_tiny_card_view) as ImageView }

    private val tvSayhiDislike by lazy { findViewById(R.id.tv_sayhi_dislike) as TextView }
    private val tvSayhiDislikeBg by lazy { findViewById(R.id.tv_sayhi_dislike_bg) as TextView }
    private val tvSayhiReply by lazy { findViewById(R.id.tv_sayhi_reply) as TextView }
    private val tvSayhiLike by lazy { findViewById(R.id.tv_sayhi_like) as TextView }
    private val tvSayhiLikeBg by lazy { findViewById(R.id.tv_sayhi_like_bg) as TextView }

    private var translateCardAni: ObjectAnimator? = null

    private val mPresenter: ISayHiStackCardPresenter by lazy { NewSayHiStackCardInnerPresenter() }
    private var stackAdapter = NewSayHiCardStackAdapter()
    private var prepared = false
    private var curSessionInfo: SayHiStackCardInfo? = null
    private var forbiddenClick = false

    private var onPageBtnInAni1: AnimationSet? = null
    private var onPageBtnInAni2: AnimationSet? = null
    private var onPageBtnInAni3: AnimationSet? = null

    private var slideStackViewYIndex: Float? = null
    private var curShowCardView: NewSayHICardSlideCard? = null

    private var tinyViewX: Float = 0f
    private var tinyViewY: Float = 0f
    private var jumpTinyCardId: String? = null

    // 进场动画
    private val rotateAnimationSet by lazy { AnimatorSet() }
    private val cardTransAnimatorSet by lazy { AnimatorSet() }
    private val bgAnimatorAlphaSet by lazy { AnimatorSet() }
    private val aniDuration = 500L
    private var alphaNoAnimator: ObjectAnimator? = null
    private var btnScaleAnimatorSet: AnimatorSet? = null
    private var hasReadGiftHi = false // 是否已经已读了礼物招呼
    private var hasReadLiveHi = false // 是否已经已读了直播招呼
    private var pageFromSource: String? = null
    private var lastRequestTime = -1L
    private var useEmptyAnimation = false   // 不使用动画
    private var checkPushCard: SayHiStackCardInfo? = null // 检查的push卡片
    private var forceFilterAnimation = false // 强制禁止动画
    private var closeCardIntoAnimation = false // 关闭进入动画
    private var hasDealClearCards = mutableSetOf<String>() // 已经处理了的卡片
    private var lastDealCardId: String? = null // 最后已经处理过的卡片id
    private var closeAllHiTask = false // 关闭所有招呼页面

    private val switchListener = object : BaseSlideStackView.CardSwitchListener {

        override fun showGiftPanel(index: Int) {}

        override fun onShow(index: Int) {
            MomoMainThreadExecutor.cancelAllRunnables(TAG_SLIDE_CARD_EMPTY)
            val info = stackAdapter.getItem(index)
            if (info != null) {
                curSessionInfo = info
                curShowCardView = slideStackView.getSlideItem(0)
                // refreshCardShowReadState(info)
                if (info.sayHiInfo.isFromGift) {
                    hasReadGiftHi = true
                } else if (info.sayHiInfo.isFromLive) {
                    hasReadLiveHi = true
                }
                info.sayHiNetInfo?.also {
                    kotlin.runCatching {
                        NewSayHiReportUtil.reportBigCardShown(info.sayHiInfo, it, index, pageFromSource)
                    }
                }
            }
            val curTime = SystemClock.uptimeMillis()
            if (slideStackView.needMoreData()) { // 如果滑到了倒数第四张
                if (curTime - 2000 > lastRequestTime) {
                    lastRequestTime = curTime
                    GlobalEventManager.getInstance().sendEvent(
                        GlobalEventManager.Event(NewSayHiConst.EVENT_SAYHI_LIST_LOADMORE_DATA)
                            .src(GlobalEventManager.EVN_NATIVE).dst(GlobalEventManager.EVN_NATIVE)
                    )
                }
            }
            checkDataEmpty()
            resetBtnAniState()
            MDLog.i(
                NewSayHiConst.TAG,
                "onShow index=$index  size=${stackAdapter.size}  info=${info?.sayHiNetInfo?.user?.name}"
            )
        }

        override fun onCardVanish(
            index: Int, type: Int, touchType: String, picDepth: Int, source: String,
            needRequest: Boolean, toApiParams: Map<String?, String?>?
        ) {
            val info: SayHiStackCardInfo? = stackAdapter.getItem(index)
            if (info != null) {
                lastDealCardId = info.sayHiInfo.momoid // 记录已经处理的momoid，处理完了则不需要发filter
            }
            curShowCardView?.setSlidedCard(true)
            when (type) {
                SlideConst.VANISH_TYPE_LEFT -> onCardSlideToLeft(needRequest, info)

                SlideConst.VANISH_TYPE_RIGHT -> onCardSlideToRight(needRequest)

                else -> {
                }
            }
            info?.also {
                refreshCardShowReadState(it)
                NewSayHiConst.onCardDealRecord(info.sayHiInfo)
                MDLog.i(
                    NewSayHiConst.TAG,
                    "onCardVanish index=$index  size=${stackAdapter.unReadCount}  adaptercnt=${info.sayHiInfo.momoid}"
                )
            } // 处理卡片已读
            refreshCardCount(false)
            MomoMainThreadExecutor.cancelAllRunnables(TAG_SLIDE_CARD_EMPTY)
            if (stackAdapter.unReadCount == 1 || stackAdapter.unReadCount == 0) { // 说明到了最后一个
                MomoMainThreadExecutor.postDelayed(TAG_SLIDE_CARD_EMPTY, {
                    finish()
                }, 600)
            }
            kotlin.runCatching { // 提前赋值下一张卡片数据，避免onShow回调过慢2~3秒
                val sayHiStackCardInfo = stackAdapter.getItem(index + 1)
                if (sayHiStackCardInfo != null) {
                    curSessionInfo = sayHiStackCardInfo
                }
            }
        }

        override fun onItemClick(cardImageView: View, index: Int, dianDianPhotoIndex: Int) {
            closePage("blank")
        }

        override fun onSlide(percentage: Float) {
            //do nothing
            val onSlide = (1 + 0.15f * abs(percentage))
            MDLog.i(NewSayHiConst.TAG, "percentage=$percentage onSlide=$onSlide")
            if (percentage > 0) {
                iconSayhiConfirmBtn.scaleX = onSlide
                iconSayhiConfirmBtn.scaleY = onSlide
                iconSayhiConfirmBtnBg.visibility = View.VISIBLE
                iconSayhiIgnoreBtnBg.visibility = View.GONE
                iconSayhiConfirmBtnBg.alpha = percentage
                iconSayhiConfirmBtnBg.scaleX = onSlide
                iconSayhiConfirmBtnBg.scaleY = onSlide
            } else if (percentage < 0) {
                iconSayhiIgnoreBtn.scaleX = onSlide
                iconSayhiIgnoreBtn.scaleY = onSlide
                iconSayhiIgnoreBtnBg.visibility = View.VISIBLE
                iconSayhiConfirmBtnBg.visibility = View.GONE
                iconSayhiIgnoreBtnBg.alpha = -percentage
                iconSayhiIgnoreBtnBg.scaleX = onSlide
                iconSayhiIgnoreBtnBg.scaleY = onSlide
            } else {
                resetBtnAniState()
            }
        }

        override fun onPreventRightSlide() {
        }

        override fun nextCardNeedAnimBlock(indexInDataList: Int) {
            setBtnLock(true)
        }
    }

    fun refreshCardShowReadState(info: SayHiStackCardInfo) {
        val momoid = info.sayHiInfo.momoid
        hasDealClearCards.add(momoid)
        lifecycleScope.launch(MMDispatchers.Message) { // 标记用户消息为已读
            kotlin.runCatching {
                NewSayHiConst.onHiCardDeal(momoid)
                NewSayHiConst.onSessionMessageRead(momoid, info.sayHiInfo)
            }
        }
    }

    private fun resetBtnAniState() {
        MDLog.i(NewSayHiConst.TAG, "resetBtnAniState")
        iconSayhiConfirmBtn.scaleX = 1f
        iconSayhiConfirmBtn.scaleY = 1f
        iconSayhiConfirmBtnBg.visibility = View.GONE
        iconSayhiConfirmBtnBg.alpha = 1f
        iconSayhiIgnoreBtn.scaleX = 1f
        iconSayhiIgnoreBtn.scaleY = 1f
        iconSayhiIgnoreBtnBg.visibility = View.GONE
        iconSayhiIgnoreBtnBg.alpha = 1f
        resetCardItemClickState()
    }

    private fun resetCardItemClickState() {
        MomoMainThreadExecutor.cancelAllRunnables(TAG_SLIDE_CARD.hashCode())
        MomoMainThreadExecutor.postDelayed(TAG_SLIDE_CARD.hashCode(), {
            forbiddenClick = false
        }, 500)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_new_sayhi_card_stack)
        initViews()
        initData()
        NewSayHiConst.isCardPageShowing = true
        startIntoAnimation()
    }

    private fun startIntoAnimation() {
        if (closeCardIntoAnimation) {
            containerTinyCard.visibility = View.GONE
            alphaNoAnimator = ObjectAnimator.ofFloat(viewContainer, View.ALPHA, 0f, 1f).apply {
                duration = 200
                start()
            }
            slideStackView.visibility = View.VISIBLE
            intent.putExtra(NewSayHiConst.CARD_CLOSE_BG_CARD_ANI, false)
            intent.putExtra(NewSayHiConst.CARD_EMPTY_TRANS_ANIMATION, false)
        } else if (forceFilterAnimation) {
            containerTinyCard.visibility = View.GONE
            alphaNoAnimator = ObjectAnimator.ofFloat(viewContainer, View.ALPHA, 0f, 1f).apply {
                duration = aniDuration
                doOnEnd {
                    slideStackView.visibility = View.VISIBLE
                }
                start()
            }
            intent.putExtra(NewSayHiConst.CARD_FILTER_ANIMATION, false)
        } else if (!useEmptyAnimation) {
            startFirstInAni()
        } else {
            containerTinyCard.visibility = View.GONE
            slideStackView.visibility = View.VISIBLE
        }
    }

    private fun startPageAnimation(show: Boolean) {
        onPageBtnInAni1 = onPageBtnInAni(iconSayhiIgnoreBtn, show)
        onPageBtnInAni2 = onPageBtnInAni(iconSayhiReplyBtn, show)
        onPageBtnInAni3 = onPageBtnInAni(iconSayhiConfirmBtn, show)
    }

    override fun onBackPressed() {
        if (NewSayUIConfigV1.closeAni()) {
            closeAllHiTask = true
        }
        super.onBackPressed()
        NewSayHiReportUtil.clickBigCardClose("back_pressed")
    }

    override fun finish() {
        NewSayHiConst.needRefreshTinyCard = true
        recordLastCardDealByFinish()
//        val ids = arrayListOf<String>()
//        curSessionInfo?.also { // 处理过滤
//            val momoid = it.sayHiInfo.momoid
//            if (lastDealCardId.isNullOrEmpty() || lastDealCardId != it.sayHiInfo.momoid) {
//                ids.add(momoid)
//            }
//            if (!hasDealClearCards.contains(momoid)) {
//                refreshCardShowReadState(it)
//            }
//        }
        val intent = Intent()
        // intent.putStringArrayListExtra(NewSayHiConst.RESULT_PARAM_FILTER_ID, ids)
        intent.putExtra(NewSayHiConst.RESULT_PARAM_HAS_DEAL_GIFT, hasReadGiftHi)
        intent.putExtra(NewSayHiConst.RESULT_PARAM_HAS_DEAL_LIVE, hasReadLiveHi)
        setResult(NewSayHiConst.STACK_CARD_RESULT_CODE, intent)
        if (closeAllHiTask) {
            super.finish()
            overridePendingTransition(0, R.anim.anim_slide_out_to_right)
        } else {
            startFinishAnimation()
        }
    }

    override fun onPause() {
        super.onPause()
        sayHiQuickReplyHelperView.onPause()
    }

    override fun onResume() {
        super.onResume()
        if (!prepared) {
            return
        }
        sayHiQuickReplyHelperView.onResume()
        slideStackView.onResume()
    }

    override fun initStatusBar() {
        super.initStatusBar()
        val window = window
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        window.decorView.systemUiVisibility =
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
        getWindow().statusBarColor = UIUtils.getColor(R.color.transparent)
    }

    private fun initViews() {
        slideStackView.setRemainCount(KV.getUserInt(AppConfigV2.SPKeys.KEY_HI_LOADMORE_REMAIN, 3))
        slideStackView.setAdapter(stackAdapter)
        slideStackView.setCardSwitchListener(switchListener)
        iconSayhiIgnoreBtn.setOnClickListener {   // 忽略
            if (forbiddenClick) return@setOnClickListener
            curSessionInfo ?: return@setOnClickListener
            vanishCardTo(SlideConst.VANISH_TYPE_LEFT, SlideConst.SlideSource.CARD, true)
        }
        iconSayhiReplyBtn.setOnClickListener {  // 回复
            if (forbiddenClick) return@setOnClickListener
            val stackCardInfo = curSessionInfo ?: return@setOnClickListener
            sayHiQuickReplyHelperView.showComment(
                stackCardInfo.sayHiInfo, stackCardInfo.sayHiNetInfo,
                object : OnSendFinishListener {
                    override fun onSend() {
                        vanishCardTo(
                            SlideConst.VANISH_TYPE_RIGHT, SlideConst.SlideSource.CARD, false
                        )
                    }

                    override fun onCommentShow(isShowing: Boolean) {
                        // 获取初始位置
                        val slideCard = curShowCardView ?: return
                        val startY = getCardViewYIndex()
                        translateCardAni?.cancel()
                        translateCardAni = if (isShowing) { // 向上平移动画
                            ObjectAnimator.ofFloat(
                                slideCard, "y", startY, startY - keyboardSwitchHeight
                            )
                        } else { // 向下平移动画, 回到原点
                            ObjectAnimator.ofFloat(
                                slideCard, "y", startY - keyboardSwitchHeight, startY
                            )
                        }
                        translateCardAni?.interpolator = DecelerateInterpolator()
                        translateCardAni?.duration = 200
                        translateCardAni?.start()
                    }
                })
            kotlin.runCatching {
                NewSayHiReportUtil.onHiClickEvent(
                    stackCardInfo.sayHiNetInfo, slideStackView, pageFromSource
                )
            }
        }
        iconSayhiConfirmBtn.setOnClickListener {    // 通过
            if (forbiddenClick) return@setOnClickListener
            vanishCardTo(SlideConst.VANISH_TYPE_RIGHT, SlideConst.SlideSource.CARD, true)
        }
        reloadRefreshTag.setOnClickListener {
            if (forbiddenClick) return@setOnClickListener
            NewSayHiConst.newHiInCardReceive.clear()
            intent.putExtra(NewSayHiConst.CARD_EMPTY_TRANS_ANIMATION, true)
            intent.putExtra(NewSayHiConst.CARD_PARAM_CLICK_CARD, "")
            intent.putExtra(NewSayHiConst.CARD_CLOSE_BG_CARD_ANI, false)
            recreate()
        }
        if (NewSayUIConfigV1.closeAni()) {
            imgCloseTop.visibility = View.VISIBLE
            imgClose.visibility = View.INVISIBLE
            imgCloseTop.setOnClickListener {
                closePage("top_close")
            }
        } else {
            imgCloseTop.visibility = View.GONE
            imgClose.visibility = View.VISIBLE
            imgClose.setOnClickListener {
                closePage("pack")
            }
        }
        NewSayHiConst.initDebug(tvIndex)
        refreshConfigButtonTxt()
    }

    private fun refreshConfigButtonTxt() {
        NewSayHiPageConfigV2Getter.get().cardActionIgnoreTitle()?.also {
            if (it.isNotBlank()) {
                tvSayhiDislike.text = it
                tvSayhiDislikeBg.text = it
            }
        }
        NewSayHiPageConfigV2Getter.get().cardActionReplyTitle()?.also {
            if (it.isNotBlank()) {
                tvSayhiReply.text = it
            }
        }
        NewSayHiPageConfigV2Getter.get().cardActionAgreeTitle()?.also {
            if (it.isNotBlank()) {
                tvSayhiLike.text = it
                tvSayhiLikeBg.text = it
            }
        }
    }

    private fun closePage(type: String) {
        if (forbiddenClick) return
        NewSayHiReportUtil.clickBigCardClose(type)
        finish()
    }

    private fun getCardViewYIndex(): Float {
        if (slideStackViewYIndex == null) {
            slideStackViewYIndex = slideStackView.y
        }
        return slideStackViewYIndex.safe()
    }

    private fun initData() {
        intent?.also {
            tinyViewX = it.getIntExtra(NewSayHiConst.CARD_PARAM_VIEW_X, 0).toFloat()
            tinyViewY = it.getIntExtra(NewSayHiConst.CARD_PARAM_VIEW_Y, 0).toFloat()
            jumpTinyCardId = it.getStringExtra(NewSayHiConst.CARD_PARAM_CLICK_CARD)
            pageFromSource = it.getStringExtra(HiCardStackActivity.KEY_FROM_SOURCE) ?: ""
            useEmptyAnimation = it.getBooleanExtra(NewSayHiConst.CARD_EMPTY_TRANS_ANIMATION, false)
            forceFilterAnimation = it.getBooleanExtra(NewSayHiConst.CARD_FILTER_ANIMATION, false)
            closeCardIntoAnimation = it.getBooleanExtra(NewSayHiConst.CARD_CLOSE_BG_CARD_ANI, false)
        }
        NewSayHiConst.refreshAllCardData() // 刷新所有数据
        NewSayHiConst.newHiInCardReceive.clear()
        val originHiCardData = NewSayHiConst.sayHiCardData.toMutableList()
        if (originHiCardData.isEmpty()) {
            finish()
            return
        }
        val jumpStackCardBg = NewSayHiConst.jumpStackCardBg
        val findCardData = jumpTinyCardId?.takeIf { it.isNotBlank() }?.let { momoid ->
            originHiCardData.find { momoid == it.sayHiInfo.momoid }
        }
        val firstCardInfo = findCardData ?: originHiCardData.firstOrNull()
        firstCardInfo?.also {
            checkPushCard = it
            if (jumpStackCardBg != null) {
                rfTinyCardView.setImageBitmap(jumpStackCardBg)
            } else {
                refreshFirstCard(it)
            }
        }
        NewSayHiConst.jumpStackCardBg = null
        refreshCards(findCardData, originHiCardData)
        prepared = true
        GlobalEventManager.getInstance().register(this, GlobalEventManager.EVN_NATIVE)
    }

    private fun refreshFirstCard(cardInfo: SayHiStackCardInfo) {
        cardInfo.sayHiNetInfo?.also {
            val greetCardPhotos = it.greetCardImgs.safe()
            if (greetCardPhotos.isNotEmpty()) {
                val small = greetCardPhotos.first().small.safe()
                ImageLoader.load(small)
                    .imageType(ImageType.URL)
                    .cornerRadius(NewSayHiConst.ITEM_CARD_CORNER * 2)
                    .placeholder(R.drawable.bg_10dp_round_corner_f9f9f9_262626)
                    .into(rfTinyCardView)
            }
        }
    }

    private fun refreshCards(
        findCardData: SayHiStackCardInfo?,
        originSayHiCardData: MutableList<SayHiStackCardInfo>
    ) {
        val sayHiCardData = kotlin.runCatching {
            findCardData?.let { findData ->
                originSayHiCardData.filter { findData.sayHiInfo.momoid != it.sayHiInfo.momoid }
                    .toMutableList().apply { add(0, findData) }
            } ?: originSayHiCardData
        }.getOrDefault(originSayHiCardData)
        stackAdapter.addTotalCount(sayHiCardData.size, true)
        slideStackView.fillData(sayHiCardData)
        refreshCardCount(true)
    }

    private fun checkDataEmpty() {
        if (slideStackView.noDataAvailable()) {
            finish()
        }
    }

    private fun vanishCardTo(slideType: Int, source: String, needRequest: Boolean) {
        if (slideStackView.noDataAvailable()) {
            return
        }
        slideStackView.orderViewStack()
        slideStackView.vanishOnBtnClick(slideType, source, needRequest, null)
    }

    private fun onCardSlideToLeft(needRequest: Boolean, sayhiInfo: SayHiStackCardInfo?) {
        forbiddenClick = true
        resetCardItemClickState()
        if (needRequest) {
            postIgnoreOrLike(SayHiArgs.IGNORE, LikeSayHi.Requst.TYPE_LEFT)
        }
        startBtnAnimation(true)
    }

    private fun startBtnAnimation(isLeft: Boolean) {
        val scaleSize = 1.15f
        if (isLeft) {
            kotlin.runCatching {
                val startScaleX = iconSayhiIgnoreBtn.scaleX
                val startScaleY = iconSayhiIgnoreBtn.scaleY
                val scaleXBtn =
                    ObjectAnimator.ofFloat(iconSayhiIgnoreBtn, "scaleX", startScaleX, scaleSize, 1f)
                val scaleYBtn =
                    ObjectAnimator.ofFloat(iconSayhiIgnoreBtn, "scaleY", startScaleY, scaleSize, 1f)
                val scaleXBtnBg =
                    ObjectAnimator.ofFloat(
                        iconSayhiIgnoreBtnBg,
                        "scaleX",
                        startScaleX,
                        scaleSize,
                        1f
                    )
                val scaleYBtnBg =
                    ObjectAnimator.ofFloat(
                        iconSayhiIgnoreBtnBg,
                        "scaleY",
                        startScaleY,
                        scaleSize,
                        1f
                    )
                val alphaIn = ObjectAnimator.ofFloat(iconSayhiIgnoreBtnBg, View.ALPHA, 0f, 1f, 0f)
                btnScaleAnimatorSet = AnimatorSet()
                btnScaleAnimatorSet?.duration = 500
                btnScaleAnimatorSet?.doOnStart { iconSayhiIgnoreBtnBg.visibility = View.VISIBLE }
                btnScaleAnimatorSet?.doOnEnd { iconSayhiIgnoreBtnBg.visibility = View.GONE }
                btnScaleAnimatorSet?.playTogether(
                    scaleXBtn, scaleYBtn, scaleXBtnBg, scaleYBtnBg, alphaIn
                )
                btnScaleAnimatorSet?.start()
            }.onFailure {
                iconSayhiIgnoreBtnBg.visibility = View.GONE
            }
        } else {
            kotlin.runCatching {
                val startScaleX = iconSayhiConfirmBtn.scaleX
                val startScaleY = iconSayhiConfirmBtn.scaleY
                val scaleXBtn =
                    ObjectAnimator.ofFloat(
                        iconSayhiConfirmBtn,
                        "scaleX",
                        startScaleX,
                        scaleSize,
                        1f
                    )
                val scaleYBtn =
                    ObjectAnimator.ofFloat(
                        iconSayhiConfirmBtn,
                        "scaleY",
                        startScaleY,
                        scaleSize,
                        1f
                    )
                val scaleXBtnBg =
                    ObjectAnimator.ofFloat(
                        iconSayhiConfirmBtnBg,
                        "scaleX",
                        startScaleX,
                        scaleSize,
                        1f
                    )
                val scaleYBtnBg =
                    ObjectAnimator.ofFloat(
                        iconSayhiConfirmBtnBg,
                        "scaleY",
                        startScaleY,
                        scaleSize,
                        1f
                    )
                val alphaIn = ObjectAnimator.ofFloat(iconSayhiConfirmBtnBg, View.ALPHA, 0f, 1f, 0f)
                btnScaleAnimatorSet = AnimatorSet()
                btnScaleAnimatorSet?.duration = 500
                btnScaleAnimatorSet?.doOnStart { iconSayhiConfirmBtnBg.visibility = View.VISIBLE }
                btnScaleAnimatorSet?.doOnEnd { iconSayhiConfirmBtnBg.visibility = View.GONE }
                btnScaleAnimatorSet?.playTogether(
                    scaleXBtn, scaleYBtn, scaleXBtnBg, scaleYBtnBg, alphaIn
                )
                btnScaleAnimatorSet?.start()
            }.onFailure {
                iconSayhiConfirmBtnBg.visibility = View.GONE
            }
        }
    }

    private fun onCardSlideToRight(needRequest: Boolean) {
        forbiddenClick = true
        resetCardItemClickState()
        if (needRequest) {
            postIgnoreOrLike(
                SayHiArgs.LIKE,
                LikeSayHi.Requst.TYPE_RIGHT
            )
        }
        startBtnAnimation(false)
    }

    private fun postIgnoreOrLike(likeType: Int, consumeType: Int) {
        val info = slideStackView.getInfoAt(slideStackView.showingDataIndex)
        mPresenter.postIgnoreOrLike(likeType, info?.sayHiInfo, consumeType)
    }

    private fun refreshCardCount(fromInit: Boolean) {
        var noDealSessionCnt = NewSayHiConst.getAllUnreadUsersData(true).size
        if (fromInit) { // 首次进入如果是push卡片没有消息数据，则需要+1
            checkPushCard?.also {
                if (it.sayHiInfo.isFromPushCard && it.sayHiInfo.unreadMsgCount <= 0) {
                    // noDealSessionCnt++  // 如果是push卡片中招呼还没来，此时要主动+1
                }
            }
        }
        tvIndex.visibility = if (noDealSessionCnt > 0) {
            tvIndex.text = "未读招呼（$noDealSessionCnt）"
            View.VISIBLE
        } else View.GONE
    }

    /**
     * 准备播放动画时不允许点击及拖动卡片
     *
     * @param isAniming
     */
    fun setBtnLock(isAniming: Boolean) {
        slideStackView.btnLock = isAniming
    }

    fun afterBlockAndReportSuccess(
        event: String, remoteId: String? = null, action: Int? = null
    ) {
        kotlin.runCatching {
            val event = GlobalEventManager.Event(event).src(GlobalEventManager.EVN_NATIVE)
                .dst(GlobalEventManager.EVN_NATIVE)
            val data = hashMapOf<String, Any>()
            remoteId?.also {
                data["momoid"] = it
            }
            action?.also {
                data["action"] = action
            }
            curSessionInfo?.sayHiInfo?.momoid?.also {
                data["userid"] = it
            }
            event.msg(data)
            GlobalEventManager.getInstance().sendEvent(event)
            vanishCardTo(SlideConst.VANISH_TYPE_LEFT, SlideConst.SlideSource.CARD, false)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (PlatformReportHelper.ACT_RES_REPORT == requestCode && data != null) {
            val status = data.getIntExtra(
                PlatformReportHelper.WEB_RESULT_STATUS,
                PlatformReportHelper.ACTION_FAILED
            )
            if (status == PlatformReportHelper.ACTION_SUCCESS) {
                val action = data.getIntExtra(PlatformReportHelper.WEB_RESULT_ACTION, -1)
                afterBlockAndReportSuccess(
                    NewSayHiConst.EVENT_SAYHI_STACK_CARD_WEB_REPORT,
                    null,
                    action
                )
            }
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    override fun onGlobalEventReceived(event: GlobalEventManager.Event?) {
        event ?: return
        when (event.name) {
            NewSayHiConst.EVENT_SAYHI_CARD_FINISHED_LOADMORE_DATA -> { // 当下一页的数据加载到了时
                lastRequestTime = 0L
                if (NewSayHiConst.needShowCardStack()) {
                    val list = NewSayHiConst.getUnExposureLoadMoreSayHiList()
                    MDLog.i(
                        NewSayHiConst.TAG,
                        "EVENT_SAYHI_CARD_FINISHED_LOADMORE_DATA=list=${list.size}"
                    )
                    if (list.isNotEmpty()) {
                        MomoMainThreadExecutor.cancelAllRunnables(TAG_SLIDE_CARD_EMPTY)
                        stackAdapter.addTotalCount(list.size, false)
                        slideStackView.appendData(list)
                    }
                    NewSayHiConst.refreshLoadMoreCards()
                    MDLog.i(
                        NewSayHiConst.TAG,
                        "EVENT_SAYHI_CARD_FINISHED_LOADMORE_DATA=allData=${NewSayHiConst.sayHiCardData.size}"
                    )
                }
            }

            NewSayHiConst.EVENT_SAYHI_CARD_ON_NEW_HI -> {
                val newInsertCardList = NewSayHiConst.newInsertCardList
                if (newInsertCardList.isNotEmpty()) {
                    reloadRefreshTag.setInfo(newInsertCardList)
                    reloadRefreshTag.visibility = View.VISIBLE
                } else {
                    reloadRefreshTag.visibility = View.GONE
                }
            }

            NewSayHiConst.EVENT_SAYHI_PUSH_SESSION_NEW -> {
                val msg = event.msg ?: return
                val sessionId = (msg[NewSayHiConst.PARAM_NEW_MSG_SESSION] as? String?).safe()
                curShowCardView?.refreshCardMsg(sessionId)
            }
        }
    }

    private fun startFirstInAni() {
        kotlin.runCatching {
            containerTinyCard.visibility = View.VISIBLE
            val cardHeight = NewSayHiConst.cardHeight
            val cardWidth = NewSayHiConst.cardWidth
            val screenHeight = NewSayHiConst.screenHeight
            val screenWidth = NewSayHiConst.screenWidth
            slideStackView.visibility = View.INVISIBLE
            // 创建旋转动画
            val rotateBigCard = ObjectAnimator.ofFloat(slideStackView, "rotationY", 90f, 0f)
            rotateBigCard.duration = aniDuration / 2
            val rotateTinyCard = ObjectAnimator.ofFloat(containerTinyCard, "rotationY", 0f, -90f)
            rotateTinyCard.duration = aniDuration / 2
            rotateTinyCard.doOnEnd { // 小卡动画播放完成
                containerTinyCard.visibility = View.GONE
                slideStackView.visibility = View.VISIBLE
            }
            rotateBigCard.interpolator = LinearOutSlowInInterpolator()
            rotateAnimationSet.playSequentially(rotateTinyCard, rotateBigCard)
            rotateAnimationSet.start()
            // 创建平移动画
            val centerStartViewX = tinyViewX + NewSayHiConst.cardWidth / 2
            val centerStartViewY = tinyViewY + NewSayHiConst.cardHeight / 2
            val centerScreenX = screenWidth / 2
            val centerScreenY = screenHeight / 2
            val startX = centerStartViewX - centerScreenX
            val startY = -(centerScreenY - centerStartViewY) - UIUtils.getPixels(20F)
            val translateTinyCardX = ObjectAnimator.ofFloat(
                containerTinyCard, "translationX", startX, 0f
            )
            val translateTinyCardY = ObjectAnimator.ofFloat(
                containerTinyCard, "translationY", startY, 0f
            )
            val translateBigCardX = ObjectAnimator.ofFloat(
                slideStackView, "translationX", startX, 0f
            )
            val translateBigCardY = ObjectAnimator.ofFloat(
                slideStackView, "translationY", startY, 0f
            )
            // 创建缩放动画
            val slowInterpolator = FastOutLinearInInterpolator() // 慢慢快
            val startScaleX = cardWidth.toFloat() / UIUtils.getScreenWidth()
            val startScaleY = cardHeight.toFloat() / UIUtils.getScreenHeight() + 0.05F
            val scaleXBigCard = ObjectAnimator.ofFloat(slideStackView, "scaleX", startScaleX, 1f)
            val scaleYBigCard = ObjectAnimator.ofFloat(slideStackView, "scaleY", startScaleY, 1f)
            val scaleTinyCardX =
                ObjectAnimator.ofFloat(containerTinyCard, "scaleX", startScaleX, 1f)
            val scaleTinyCardY =
                ObjectAnimator.ofFloat(containerTinyCard, "scaleY", startScaleY, 1f)
            scaleXBigCard.interpolator = slowInterpolator
            scaleYBigCard.interpolator = slowInterpolator
            scaleTinyCardX.interpolator = slowInterpolator
            scaleTinyCardY.interpolator = slowInterpolator
            // 卡片动画集合
            cardTransAnimatorSet.playTogether(
                translateTinyCardX, translateTinyCardY, translateBigCardX, translateBigCardY,
                scaleXBigCard, scaleYBigCard, scaleTinyCardX, scaleTinyCardY
            )
            cardTransAnimatorSet.duration = aniDuration
            cardTransAnimatorSet.doOnEnd {
                // emptyView.visibility = View.VISIBLE
            }
            cardTransAnimatorSet.start()
            // 背景的隐藏动画
            startBgAlphaAnimation(true)
            // 底部按钮的动画
            startPageAnimation(true)
        }
    }

    private fun startBgAlphaAnimation(show: Boolean) {
        kotlin.runCatching {
            var startAlpha = 0f
            var endAlpha = 1f
            if (!show) {
                startAlpha = 1f
                endAlpha = 1f
            }
            val bgAlpha = ObjectAnimator.ofFloat(bgView, View.ALPHA, startAlpha, endAlpha)
            val closeBtnAlpha = ObjectAnimator.ofFloat(imgClose, View.ALPHA, startAlpha, endAlpha)
            val titleAlpha = ObjectAnimator.ofFloat(tvIndex, View.ALPHA, startAlpha, endAlpha)
            bgAnimatorAlphaSet.playTogether(bgAlpha, closeBtnAlpha, titleAlpha)
            bgAnimatorAlphaSet.duration = aniDuration / 2
            bgAnimatorAlphaSet.start()
        }
    }

    private fun onPageBtnInAni(btnView: View, show: Boolean): AnimationSet {
        val animationSet = AnimationSet(true)
        kotlin.runCatching {
            var fromY = 1.5f
            var endY = 0f
            if (!show) {
                fromY = 0f
                endY = 1.5f
            }
            val aniTrans = TranslateAnimation(
                Animation.RELATIVE_TO_SELF, 0f,
                Animation.RELATIVE_TO_SELF, 0f,
                Animation.RELATIVE_TO_SELF, fromY,
                Animation.RELATIVE_TO_SELF, endY
            ).apply {
                fillAfter = false // 动画结束后不保持在结束状态
            }
            val aniAlpha = startAlphaAnimation(null)
            animationSet.apply {
                duration = aniDuration / 2
                addAnimation(aniTrans)
                addAnimation(aniAlpha)
            }
            animationSet.setAnimationListener(object : AnimationListener {
                override fun onAnimationStart(p0: Animation?) {
                }

                override fun onAnimationEnd(p0: Animation?) {
                    if (!show) {
                        btnView.visibility = View.GONE
                    }
                }

                override fun onAnimationRepeat(p0: Animation?) {
                }

            })
            btnView.startAnimation(animationSet)
        }
        return animationSet
    }

    private fun startAlphaAnimation(btnView: View?): AlphaAnimation {
        val aniAlpha = AlphaAnimation(0f, 1f).apply {
            fillAfter = false // 动画结束后不保持在结束状态
        }
        kotlin.runCatching {
            btnView?.also {
                aniAlpha.duration = aniDuration / 2
                btnView.startAnimation(aniAlpha)
            }
        }
        return aniAlpha
    }

    /**
     * 退出动画
     */
    private fun startFinishAnimation() {
        kotlin.runCatching {
            startBgAlphaAnimation(false)
            val cardHeight = NewSayHiConst.cardHeight
            val cardWidth = NewSayHiConst.cardWidth
            val screenHeight = NewSayHiConst.screenHeight
            val screenWidth = NewSayHiConst.screenWidth
            // 创建平移动画
            val centerStartViewX = tinyViewX + NewSayHiConst.cardWidth / 2
            val centerStartViewY = tinyViewY + NewSayHiConst.cardHeight / 2
            val centerScreenX = screenWidth / 2
            val centerScreenY = screenHeight / 2
            val startX = centerStartViewX - centerScreenX
            val startY = -(centerScreenY - centerStartViewY) - UIUtils.getPixels(20F)
            val translateBigCardX = ObjectAnimator.ofFloat(
                slideStackView, "translationX", 0f, startX
            )
            val translateBigCardY = ObjectAnimator.ofFloat(
                slideStackView, "translationY", 0f, startY
            )
            // 创建缩放动画
            val slowInterpolator = FastOutLinearInInterpolator() // 慢慢快
            val startScaleX = cardWidth.toFloat() / UIUtils.getScreenWidth()
            val startScaleY = cardHeight.toFloat() / UIUtils.getScreenHeight() + 0.05F
            val scaleXBigCard = ObjectAnimator.ofFloat(slideStackView, "scaleX", 1f, startScaleX)
            val scaleYBigCard = ObjectAnimator.ofFloat(slideStackView, "scaleY", 1f, startScaleY)
            scaleXBigCard.interpolator = slowInterpolator
            scaleYBigCard.interpolator = slowInterpolator
            // 隐藏动画
            val viewContainerAlpha = ObjectAnimator.ofFloat(viewContainer, View.ALPHA, 1f, 0f)
            val animationSet = AnimatorSet()
            animationSet.playTogether(
                translateBigCardX, translateBigCardY,
                scaleXBigCard, scaleYBigCard,
                viewContainerAlpha
            )
            animationSet.doOnEnd {
                super.finish()
                overridePendingTransition(0, 0)
            }
            animationSet.duration = aniDuration / 2
            animationSet.start()
            startPageAnimation(false)
        }.onFailure {
            super.finish()
            overridePendingTransition(0, 0)
        }
    }

    override fun onDestroy() {
        kotlin.runCatching {
            NewSayHiConst.needRefreshTinyCard = true
            recordLastCardDealByFinish()
            NewSayHiConst.isCardPageShowing = false
            NewSayHiConst.onTinyCardSeeFinish()
            if (closeAllHiTask) {
                GlobalEventManager.getInstance().sendEvent(
                    GlobalEventManager.Event(NewSayHiConst.EVENT_ON_CARD_BACK_CLOSE_CONFIG)
                        .src(GlobalEventManager.EVN_NATIVE).dst(GlobalEventManager.EVN_NATIVE)
                )
            }
            MomoMainThreadExecutor.cancelAllRunnables(TAG_SLIDE_CARD_EMPTY)
            MomoMainThreadExecutor.cancelAllRunnables(TAG_SLIDE_CARD)
            alphaNoAnimator?.cancel()
            translateCardAni?.cancel()
            onPageBtnInAni1?.cancel()
            onPageBtnInAni2?.cancel()
            onPageBtnInAni3?.cancel()
            btnScaleAnimatorSet?.cancel()
            rotateAnimationSet.cancel()
            cardTransAnimatorSet.cancel()
            bgAnimatorAlphaSet.cancel()
            slideStackView.onDestroy()
            GlobalEventManager.getInstance().unregister(this, GlobalEventManager.EVN_NATIVE)
        }
        super.onDestroy()
    }

    private fun recordLastCardDealByFinish() {
        kotlin.runCatching {
            NewSayHiConst.newHiInCardReceive.clear()
//            curSessionInfo?.also {
//                if (it.sayHiInfo.isFromPushCard) {
//                    refreshCardShowReadState(it)
//                }
//                NewSayHiConst.onCardDealRecord(it.sayHiInfo)
//            }
        }
    }

}