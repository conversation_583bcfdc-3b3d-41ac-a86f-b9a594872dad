package com.immomo.momo.message.sayhi.widget

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.os.Build
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.utils.UIUtils
import com.immomo.mls.`fun`.other.Size
import com.immomo.mls.utils.ViewShadowHelper
import com.immomo.mmutil.task.MomoMainThreadExecutor
import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.momo.R
import com.immomo.momo.gotologic.GotoDispatcher
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiRedPacket
import com.immomo.momo.util.SimpleAnimatorListener
import com.immomo.momo.util.StringUtils
import kotlinx.android.synthetic.main.layout_say_hi_redpacket_notice.view.layout_notice

class SayhiRedPacketNoticeView @JvmOverloads constructor(
    var mContext: Context,
    var sayHiRedPacket: SayHiRedPacket, var block: (view: View) -> Unit,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RelativeLayout(mContext, attrs, defStyleAttr) {

    private var mRootView: View
    private var animatorSet: AnimatorSet? = null
    private var slideOut: ObjectAnimator? = null

    init {
        LayoutInflater.from(mContext).inflate(R.layout.layout_say_hi_redpacket_notice, this)
        mRootView = findViewById<View>(R.id.layout_notice)

        findViewById<TextView>(R.id.red_packet_title).text = sayHiRedPacket.title
        findViewById<TextView>(R.id.red_packet_sub).text = sayHiRedPacket.subTitle
        findViewById<TextView>(R.id.red_packet_time).text = sayHiRedPacket.timeText

        mRootView.setOnClickListener {
            if (StringUtils.notEmpty(sayHiRedPacket.clickGoto)) {
                GotoDispatcher.action(sayHiRedPacket.clickGoto, context).execute()
            }
            block.invoke(this)
        }

        sayHiRedPacket.subTitle?.let {
            val subtitle = SpannableString(it + (sayHiRedPacket.price?:""))
            val colorSpan = ForegroundColorSpan(UIUtils.getColor(R.color.color_F85543))
            subtitle.setSpan(colorSpan,it.length -1, subtitle.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            findViewById<TextView>(R.id.red_packet_sub).text = subtitle
        }

        findViewById<ImageView>(R.id.icon_red_packet)?.let {
            ImageLoader.load(sayHiRedPacket.icon).canUseNight(true).into(it)
        }


        val viewShadowHelper = ViewShadowHelper()
        viewShadowHelper.setShadowData(
            0, Size(-UIUtils.getPixels(1f) * 1f, -UIUtils.getPixels(1f) * 1f),
            UIUtils.getPixels(28f) * 1f, 0.2f
        )
        viewShadowHelper.setOutlineProvider(mRootView)
    }


    fun startShowAnim() {
        visibility = GONE
        animatorSet?.cancel()
        if (animatorSet == null) {
            animatorSet = AnimatorSet()
        }
        val slideIn = ObjectAnimator.ofFloat(this, "translationY", -32f, 0f)
        slideIn.setDuration(200)
        val alphaIn = ObjectAnimator.ofFloat(this, "alpha", 0f, 1f)
        alphaIn.setDuration(200)
        animatorSet?.playTogether(slideIn, alphaIn)
        animatorSet?.addListener(object : SimpleAnimatorListener() {
            override fun onAnimationStart(animation: Animator) {
                visibility = VISIBLE
            }

            override fun onAnimationEnd(animation: Animator) {
                // 延迟一段时间后移除视图
                MomoMainThreadExecutor.postDelayed(
                    hashCode(),
                    Runnable {
                        block.invoke(this@SayhiRedPacketNoticeView)
                        clearAnimation()
                    },
                    3000
                )
            }
        })

        animatorSet?.start()
    }

    fun hide(blockHide: () -> Unit) {
        slideOut?.cancel()
        if (slideOut == null) {
            slideOut = ObjectAnimator.ofFloat(this, "alpha", 1f, 0f)
        }

        slideOut?.setDuration(200)
        slideOut?.addListener(object : SimpleAnimatorListener() {
            override fun onAnimationEnd(animation: Animator) {
                blockHide.invoke()
            }

            override fun onAnimationCancel(animation: Animator) {
                visibility = GONE
            }
        })
        slideOut?.start()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        MomoMainThreadExecutor.cancelAllRunnables(hashCode())
        animatorSet?.cancel()
        animatorSet = null
        slideOut?.cancel()
        slideOut = null
    }
}