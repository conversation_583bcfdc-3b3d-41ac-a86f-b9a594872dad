package com.immomo.momo.message.paper.chat.group

import android.view.View
import com.immomo.momo.R
import com.immomo.momo.group.audio.presentation.view.GroupAudioPaperFragment
import com.immomo.momo.message.paper.BasePaperContainerFragment
import com.immomo.momo.message.paper.PaperConfig
import com.immomo.momo.message.paper.common.GroupChatTopBarPaperFragment

/**
 * <AUTHOR>
 * @data 2020-12-18.
 */

class GroupChatMidPaperFragment : BasePaperContainerFragment() {

    companion object {
        @JvmStatic
        fun newInstance(): GroupChatMidPaperFragment {
            return GroupChatMidPaperFragment()
        }
    }

    override fun getPagers(): MutableList<PaperConfig>? =
            mutableListOf(
                    PaperConfig(GroupChatTopBarPaperFragment.newInstance()),
                    PaperConfig(GroupAudioPaperFragment.newInstance())
            )

    override fun getPageLayout(): Int = R.layout.paper_group_chat_mid

    override fun onPageLoad() {
    }

    override fun initPageViews(contentView: View?) {
    }

}