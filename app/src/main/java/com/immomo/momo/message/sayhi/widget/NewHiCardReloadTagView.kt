package com.immomo.momo.message.sayhi.widget

import android.animation.ObjectAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.core.animation.doOnEnd
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.R
import com.immomo.momo.android.view.RoundCornerLinearLayout
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiStackCardInfo
import com.mln.watcher.safe

/**
 * 新用户重新加载view
 */
class NewHiCardReloadTagView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RoundCornerLinearLayout(context, attrs, defStyleAttr) {

    private val icon1 by lazy { findViewById<ImageView>(R.id.icon_1) }
    private val icon2 by lazy { findViewById<ImageView>(R.id.icon_2) }
    private val icon3 by lazy { findViewById<ImageView>(R.id.icon_3) }
    private val tvMark by lazy { findViewById<TextView>(R.id.tv_mark) }

    init {
        LayoutInflater.from(context).inflate(R.layout.view_sayhi_new_user_reload_tag, this)
        setRadius(UIUtils.getPixels(20f))
    }

    fun setInfo(newHiCards: MutableList<SayHiStackCardInfo>) {
        kotlin.runCatching {
            val size = newHiCards.size
            icon1.visibility = VISIBLE
            icon2.visibility = VISIBLE
            icon3.visibility = VISIBLE
            visibility = VISIBLE
            if (size == 1) {
                icon2.visibility = GONE
                icon3.visibility = GONE
                loadIcon(icon1, newHiCards[0])
            } else if (size == 2) {
                icon3.visibility = GONE
                loadIcon(icon1, newHiCards[0])
                loadIcon(icon2, newHiCards[1])
            } else if (size <= 0) {
                visibility = GONE
            } else {
                loadIcon(icon1, newHiCards[0])
                loadIcon(icon2, newHiCards[1])
                loadIcon(icon3, newHiCards[2])
            }
            tvMark.text = if (size > 99) "有99+个新招呼" else "有${size}个新招呼"
            showTag(true)
        }
    }

    private fun loadIcon(icon: ImageView, sayHiInfo: SayHiStackCardInfo) {
        sayHiInfo.sayHiNetInfo?.greetCardImgs?.first()?.also {
            ImageLoader.load(it.small.safe()).cornerRadius(UIUtils.getPixels(18f)).into(icon)
        }
    }

    fun showTag(isShow: Boolean) {
        var startA = 1f
        var endA = 0f
        if (isShow) {
            startA = 0f
            endA = 1f
        }
        val reloadRefreshTagAni = ObjectAnimator.ofFloat(this, View.ALPHA, startA, endA)
        reloadRefreshTagAni.duration = 500
        reloadRefreshTagAni.doOnEnd {
            this.visibility = if (isShow) View.VISIBLE else View.GONE
        }
        reloadRefreshTagAni.start()
    }

}