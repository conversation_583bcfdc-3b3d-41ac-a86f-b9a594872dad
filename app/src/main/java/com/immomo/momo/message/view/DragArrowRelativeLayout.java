package com.immomo.momo.message.view;

import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.RelativeLayout;

/**
 * Created by huang.lian<PERSON><PERSON>e on 2017/8/22.
 *
 * Momo Tech 2011-2017 © All Rights Reserved.
 */

public class DragArrowRelativeLayout extends RelativeLayout {
    private DragListener dragListener;

    public DragArrowRelativeLayout(Context context) {
        super(context);
    }

    public DragArrowRelativeLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public DragArrowRelativeLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_UP:
                if (dragListener != null) {
                    dragListener.onDragEnd();
                }
                break;
        }
        return true;

    }

    public void setDragListener(DragListener dragListener) {
        this.dragListener = dragListener;
    }

    public interface DragListener {
        void onDragEnd();
    }

}
