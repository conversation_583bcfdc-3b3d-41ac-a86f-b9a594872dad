package com.immomo.momo.message.task.util

import com.immomo.molive.kotlin.extensions.isNotNullOrEmpty
import com.immomo.momo.message.helper.MessageConfigV2
import com.immomo.momo.mulog.MULog
import com.immomo.momo.mulog.MULogConstants
import com.immomo.momo.mulog.pair.MUPairItem
import com.immomo.momo.service.bean.Message

/**
 * CREATED BY liu.chong
 * AT 2021/11/9
 */
object MsgFilterUtil {
    @JvmStatic
    fun distinct(msgList: MutableList<Message>, adapterList: List<Message>): MutableList<Message> {
        val set = HashSet<String>()
        val list = ArrayList<Message>()
        for (e in msgList) {
            val key = e.msgId
            if (set.add(key) && adapterList.contains(e).not())
                list.add(e)
        }
        return list
    }

    fun distinctFilterNullId(
        msgList: MutableList<Message>,
        adapterList: List<Message>
    ): MutableList<Message> {
        val set = HashSet<String>()
        val list = ArrayList<Message>()
        for (e in msgList) {
            val key = e.msgId
            if (key?.endsWith("lastnotice") == true
                && MessageConfigV2.lastNoticeText().isNotNullOrEmpty()
            ) {
                e.setContent(MessageConfigV2.lastNoticeText())
            }
            if (key.isNullOrBlank()) {
                uploadMsgIdNull(e)
            } else {
                if (set.add(key) && adapterList.contains(e).not())
                    list.add(e)
            }
        }
        return list
    }

    private fun uploadMsgIdNull(message: Message): Boolean {
        kotlin.runCatching {
            // 使用离线日志记录
            MULog.business(MULogConstants.BUSINESS_MOMO_BASIC)
                .secondLBusiness("message")
                .thirdLBusiness("msg_id_null")
                .addBodyItem(MUPairItem.category(message.chatType.toString()))
                .addBodyItem(MUPairItem.msg(message.content ?: ""))
                .addBodyItem(MUPairItem.type(message.contentType))
                .addBodyItem(MUPairItem("from", message.remoteId ?: ""))
                .addBodyItem(MUPairItem.createTime(message.timestampMillis))
                .commit()
        }

        return true
    }
}