package com.immomo.momo.message.paper.common.timely.statemachine

import com.immomo.momo.moment.model.MicroVideoModel
import com.immomo.momo.multpic.entity.Photo


class TimelyReadySendState(stateManager: TimelyStateManager) :
    AbsBackToCloseState(stateManager) {
    var microVideoModel: MicroVideoModel? = null
    var photos: MutableList<Photo>? = null

    fun showReadyView() {
        stateMachine.mView.updateRecordFinish(microVideoModel, photos)
    }

    fun sendMsg(txt: String) {
        microVideoModel?.let {
            stateMachine.mPaperCommonViewModel?.timelyCameraListener?.sendTimelyVideo(it, txt)
        }
        photos?.forEach {
            it.categoryParams = hashMapOf("moment_text" to txt)
        }
        photos?.let {
            stateMachine.mPaperCommonViewModel?.timelyCameraListener?.sendTimelyImage(it)
        }
    }
}

