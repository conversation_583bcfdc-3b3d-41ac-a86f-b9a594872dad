package com.immomo.momo.message.sayhi.widget;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.immomo.framework.imageloader.ImageLoaderX;
import com.immomo.framework.utils.UIUtils;
import com.immomo.momo.R;
import com.immomo.momo.likematch.tools.BasicAnimHelper;
import com.immomo.momo.likematch.tools.RandomUtils;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public class PopOutViewRelativeLayout extends RelativeLayout {
    private static final float POP_VIEW_TRANS_Y = UIUtils.getPixels(20);
    private static final int MAX_CNT = 4;
    public String[] imgs;
    private AnimatorSet[] popingPicAnim = new AnimatorSet[MAX_CNT];
    private ImageView[] popPics = new ImageView[MAX_CNT];
    private int curPic = 0;
    private View targetView;
    private float circleRadius = UIUtils.getPixels(120);
    private float popViewTransY = POP_VIEW_TRANS_Y;
    private Drawable[] drawables;

    public PopOutViewRelativeLayout(@NonNull Context context) {
        this(context, null);
    }

    public PopOutViewRelativeLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PopOutViewRelativeLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        inflate(getContext(), R.layout.circle_target_poping_out_view, this);

        popPics[0] = (ImageView) findViewById(R.id.pop_pic_0);
        popPics[1] = (ImageView) findViewById(R.id.pop_pic_1);
        popPics[2] = (ImageView) findViewById(R.id.pop_pic_2);
        popPics[3] = (ImageView) findViewById(R.id.pop_pic_3);
//        popPics[4] = (ImageView) findViewById(R.id.pop_pic_4);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
    }

    public void stopAnim() {
        if (popingPicAnim == null) return;
        for (AnimatorSet set : popingPicAnim) {
            if (set != null && set.isRunning()) {
                set.cancel();
            }
        }
    }

    public void setTargetView(View targetView) {
        this.targetView = targetView;
    }

    public void setImgs(String[] imgs) {
        this.imgs = imgs;
    }

    public void nextPopingOutLittlePicture(long delay, long duration) {
        if (!checkValid()) {
            return;
        }
        int indexInView = popNextOne();
        int total = popPics.length;
        final int cur = indexInView % total;
        int pre = (indexInView - 1 + total) % total;
        if (popingPicAnim[cur] != null && popingPicAnim[cur].isRunning()) {
            return;
        }

        float centerX;
        float centerY;
        if (targetView != null) {
            centerX = targetView.getLeft() + (targetView.getWidth() - popPics[cur].getHeight()) / 2.0f;
            centerY = targetView.getTop() + (targetView.getHeight() - popPics[cur].getHeight()) / 2.0f;
        } else {
            centerX = getLeft() + (getWidth() - popPics[cur].getHeight()) / 2.0f;
            centerY = getTop() + (getHeight() - popPics[cur].getHeight()) / 2.0f;
        }
        float angle = RandomUtils.random(0, 360);
        int x = (int) (centerX + circleRadius * Math.cos(angle * Math.PI / 180.f));
        int y = (int) (centerY + circleRadius * Math.sin(angle * Math.PI / 180.f) + popViewTransY);
        if (imgs != null && imgs.length > 0) {
            ImageLoaderX.load(imgs[cur % imgs.length]).width(UIUtils.getPixels(60)).height(UIUtils.getPixels(60)).into(popPics[cur]);
        } else if (drawables != null && cur >= 0 && cur < drawables.length) {
            popPics[cur].setImageDrawable(drawables[cur]);
        }

        popPics[cur].setAlpha(0f);
        popPics[cur].setTranslationX(x);
        popPics[cur].setTranslationY(y);
        popingPicAnim[cur] = new AnimatorSet();
        List<Animator> animatorList = new ArrayList<>();
        BasicAnimHelper.alpha(animatorList, popPics[cur], delay, duration, null, 0f, 1.0f);
        BasicAnimHelper.TransY(animatorList, popPics[cur], delay, duration, null, popPics[cur].getTranslationY() - popViewTransY);
        long endDelay = RandomUtils.random((int) (delay * 2), (int) (delay * 5));
        BasicAnimHelper.alpha(animatorList, popPics[cur], endDelay, duration, null, 1.0f, 0f);
        BasicAnimHelper.TransY(animatorList, popPics[cur], endDelay, duration, null, popPics[cur].getTranslationY());
        popPics[cur].bringToFront();

        popingPicAnim[cur].playTogether(animatorList);
        popingPicAnim[cur].start();
    }

    private boolean checkValid() {
        return popPics != null && popPics.length > 0;
    }

    private synchronized int popNextOne() {
        return curPic += 1;
    }

    public void setImgDrawables(Drawable[] drawables) {
        if (drawables == null) return;
        this.drawables = drawables;
    }

    public boolean isAnimating() {
        if (popingPicAnim == null) return false;
        for (AnimatorSet set : popingPicAnim) {
            if (set != null && set.isRunning()) {
                return true;
            }
        }
        return false;
    }
}
