package com.immomo.momo.message.task;

import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.message.activity.MultiChatActivity;
import com.immomo.momo.messages.service.DiscussMsgService;
import com.immomo.momo.messages.service.DiscussMsgServiceV2;
import com.immomo.momo.service.bean.Message;

import java.util.ArrayList;
import java.util.List;

import static com.immomo.momo.mvp.message.view.BaseMessageActivity.PAGE_SIZE;


/**
 * 多人聊天加载更多新消息
 * <AUTHOR>
 * date 2020/8/15
 */
public class MultiChatLoadMoreNewMessageTask extends MomoTaskExecutor.Task<Object, Object, List<Message>> {

    private MultiChatActivity mActivity;


    public MultiChatLoadMoreNewMessageTask(MultiChatActivity mActivity) {
        this.mActivity = mActivity;
    }

    @Override
    protected List<Message> executeTask(Object... params) throws Exception {
        if (mActivity.msgChatData.isEmpty()){
            return new ArrayList<Message>();
        }
        Message message = mActivity.msgChatData.getMessageList().get(mActivity.msgChatData.getCount() - 1);
        List<Message> messages = DiscussMsgServiceV2.getService().findMessageBy(
                mActivity.currentDiscuss.id,
                PAGE_SIZE + 1,
                message,
                true,
                false
        );
        if (messages.size() == PAGE_SIZE + 1) {
            mActivity.setHasMoreNewerMessage(true);
            messages.remove(messages.size() - 1);
        } else {
            mActivity.setHasMoreNewerMessage(false);
        }

        mActivity.preHandleMessages(messages);
        return messages;
    }

    @Override
    protected void onTaskFinish() {
        mActivity.msgChatRecycler.setLoadMoreComplete();
    }

    @Override
    protected void onTaskSuccess(List<Message> messages) {
        if (messages.size() > 0) {
            mActivity.msgChatData.addItemModels(messages);
        }
    }

    @Override
    protected void onTaskError(Exception e) {
        //show some error
    }
}