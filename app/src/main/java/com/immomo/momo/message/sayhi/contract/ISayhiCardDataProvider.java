package com.immomo.momo.message.sayhi.contract;

import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo;
import com.immomo.momo.message.sayhi.stack.ISayHiCardSwitchLisener;

/**
 * Created by lei.ji<PERSON>n on 2020-06-28.
 */
public interface ISayhiCardDataProvider extends IMessageDataProvider {
    ISayHiCardSwitchLisener getCardSwitchListener();

    SayHiInfo getSayhiInfo();

    String getTaskTag();

    void reportUser(String momoid);
}
