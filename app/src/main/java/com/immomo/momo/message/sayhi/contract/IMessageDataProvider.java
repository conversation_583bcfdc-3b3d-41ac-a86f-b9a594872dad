package com.immomo.momo.message.sayhi.contract;

import android.app.Activity;
import android.app.Dialog;

import com.immomo.framework.cement.CementModel;
import com.immomo.momo.service.bean.Message;
import com.immomo.momo.service.bean.User;

import java.util.List;

/**
 * Created by lei.jialin on 2019-11-15.
 */
public interface IMessageDataProvider {

    /**
     * 获取用户数据
     */
    User getUser();

    /**
     * 获取Activity对象
     */
    Activity getActivity();

    /**
     * 移除某一个指定的model
     *
     * @param profileModel 需要移除的model对象
     */
    void removeModel(CementModel profileModel);

    List<Message> getImageMessages();

    void notifyModelChange(CementModel model);

    void showDialog(Dialog dialog);

    /**
     * 消息是否已经下载完成过
     * */
    boolean isMsgDownloaded(String msgId);

    /**
     * 消息下载完成
     * */
    void onMsgDownloaded(String msgId);

    /**
     * 消息正在下载
     * */
    boolean isMsgDownloading(String msgId);

    /**
     * 消息开始下载
     * */
    void onMsgStartDownloding(String msgId);

    /**
     * 是否是招呼界面
     * */
    boolean isSayhi();
}
