package com.immomo.momo.message.log

import com.immomo.lcapt.evlog.EVLog
import com.immomo.lcapt.evlog.anno.Param
import com.immomo.lcapt.evlog.anno.TaskPoint
import com.immomo.momo.businessmodel.statistics.PageStepHelper.getPreStepConfig
import com.immomo.momo.businessmodel.statistics.StepConfigData
import com.immomo.momo.message.log.apt.MsgLogAppconfigGetter

/**
 * Created by huang.liangjie on 2024/8/8.
 *
 * Momo Tech 2011-2024 © All Rights Reserved.
 */
interface IMessageListLog {
    @TaskPoint(requireId = "18455", page = "msg.chatpage", action = "list.load")
    fun msgListLoad(
        @Param("loadTime") loadTime: Int,
        @Param("state") state: Int,
        @Param("msgCount") msgCount: Int,
        @Param("chatId") chatId: String,
        @Param("chatType") chatType: Int,
        @Param("source") source: String
    )

    class MessageLoadLogger {
        private var startTime = 0L
        private var uploaded = false

        private var isEnable = false
        private var limitTime = 0

        init {
            isEnable = (MsgLogAppconfigGetter.get().loadLog() == 1)
            limitTime = MsgLogAppconfigGetter.get().msgListLimit()
        }

        fun loadStart() {
            startTime = System.currentTimeMillis()
        }

        fun <T : StepConfigData> loadEnd(
            state: Int, msgCount: Int, chatId: String,
            chatType: Int, excludes: Class<T>
        ) {
            if (!isEnable) {
                return
            }
            if (uploaded) {
                return
            }
            uploaded = true

            val endTime = System.currentTimeMillis()
            val loadTime = endTime - startTime

            if (loadTime <= limitTime) {
                return
            }

            val source = getPreStepConfig(excludes).logSource
            EVLog.create(IMessageListLog::class.java).msgListLoad(
                loadTime.toInt(), state, msgCount,
                chatId, chatType, source
            )
            startTime = 0
        }
    }
}