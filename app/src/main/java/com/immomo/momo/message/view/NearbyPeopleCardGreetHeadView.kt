package com.immomo.momo.message.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import com.immomo.momo.R


class NearbyPeopleCardGreetHeadView : FrameLayout {
    private var closeView: View? = null
    var close: (() -> Unit)? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attributeSet: AttributeSet) : super(context, attributeSet)

    constructor(context: Context, attributeSet: AttributeSet, defStyleAttr: Int) : super(
        context,
        attributeSet,
        defStyleAttr
    )

    init {
        LayoutInflater.from(context).inflate(R.layout.layout_chat_nearby_people_card_greet_head, this, true)
        closeView = findViewById(R.id.nearby_card_head_close)
        closeView?.setOnClickListener {
            close?.invoke()
        }
    }
}