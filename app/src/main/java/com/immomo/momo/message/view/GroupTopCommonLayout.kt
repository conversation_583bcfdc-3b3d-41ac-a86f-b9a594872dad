package com.immomo.momo.message.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.immomo.android.module.mlnpm.util.safe
import com.immomo.momo.R

class GroupTopCommonLayout @JvmOverloads constructor(
    context: Context,
    attributes: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attributes, defStyleAttr) {

    private val tvTitle: TextView by lazy { findViewById<TextView>(R.id.tv_title) }
    private val tvSubtitle: TextView by lazy { findViewById<TextView>(R.id.tv_subtitle) }
    private val tvBtn: TextView by lazy { findViewById<TextView>(R.id.btn) }

    init {
        LayoutInflater.from(context).inflate(R.layout.layout_gourp_onlooker_top_bar, this)
    }

    fun setTitle(title: String?): GroupTopCommonLayout {
        tvTitle.text = title.safe()
        return this
    }

    fun setSubTitle(title: String?): GroupTopCommonLayout {
        tvSubtitle.text = title.safe()
        return this
    }

    fun setBtnText(title: String, select: Boolean): GroupTopCommonLayout {
        tvBtn.text = title
        tvBtn.isSelected = select
        return this
    }

    fun isCanClick(): Boolean {
        return !tvBtn.isSelected
    }

    fun setOnClick(onClick: OnClickListener) {
        tvBtn.setOnClickListener(onClick)
    }

}