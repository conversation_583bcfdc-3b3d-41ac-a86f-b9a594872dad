package com.immomo.momo.message.sayhi.utils

import android.content.Intent
import android.graphics.Bitmap
import android.view.View
import com.alibaba.fastjson.JSON
import com.cosmos.mdlog.MDLog
import com.immomo.framework.utils.UIUtils
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.momo.BuildConfig
import com.immomo.momo.MomoKit
import com.immomo.momo.globalevent.GlobalEventManager
import com.immomo.momo.im.GiftSayHiAppConfigV1.isOpenExp
import com.immomo.momo.maintab.session2.SessionUpdateBundle
import com.immomo.momo.maintab.session2.data.manager.SessionKey.Companion.fromString
import com.immomo.momo.maintab.session2.data.manager.SessionManager.Companion.get
import com.immomo.momo.maintab.session2.defs.GiftSayHiSessionDefinition
import com.immomo.momo.maintab.session2.defs.SayHiSessionDefinition
import com.immomo.momo.message.NewSayUIConfigV1
import com.immomo.momo.message.sayhi.SayHiArgs
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiStackCardInfo
import com.immomo.momo.messages.service.SingleMsgService
import com.immomo.momo.mulog.MULogConstants
import com.immomo.momo.mulog.MURealtimeLog
import com.immomo.momo.mulog.pair.MUPairItem
import com.immomo.momo.service.bean.SayhiSession
import com.immomo.momo.test.qaspecial.TestSettingActivity
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.concurrent.CopyOnWriteArrayList

object NewSayHiConst {

    const val TAG = "NewSayHiConst"

    const val STACK_CARD_REQUEST_CODE = 1001 // 翻卡页面结束
    const val STACK_CARD_RESULT_CODE = 1002 // 请求数据
    const val FOLD_INNER_SESSIONS_REQUEST_CODE = 1011 // 礼物或者直播页面
    const val FOLD_INNER_SESSIONS_RESULT_CODE = 1012 // 礼物或者直播页面结束
    const val RESULT_PARAM_FILTER_ID = "result_param_filter_id" // 需要过滤的id
    const val RESULT_PARAM_HAS_DEAL_GIFT = "result_param_has_deal_gift" // 是否处理了礼物招呼
    const val RESULT_PARAM_HAS_DEAL_LIVE = "result_param_has_deal_live" // 是否处理了直播招呼
    const val CARD_PARAM_VIEW_X = "card_param_view_x"
    const val CARD_PARAM_VIEW_Y = "card_param_view_y"
    const val CARD_PARAM_CLICK_CARD = "card_param_click_card"
    const val CARD_EMPTY_TRANS_ANIMATION = "card_empty_trans_animation"
    const val CARD_FILTER_ANIMATION = "card_filter_animation"
    const val CARD_CLOSE_BG_CARD_ANI = "card_close_bg_card_ani" // 关闭第一次动画

    const val CARD_TYPE_TYPE_NORMAL = 0   // 普通卡片加载类型
    const val CARD_TYPE_FIRST_LOAD = 1    // 首次加载需要使用加载动画
    const val CARD_TYPE_NEW_INSERT = 2    // 有新的招呼插入卡牌
    const val CARD_TYPE_FINISH_INSERT = 3    // 有新的招呼已经插入卡牌并且播放完成了动画，只需保持红点状态

    var hideGreetKeyboard = false // 是否隐藏打招呼键盘

    var jumpStackCardBg: Bitmap? = null

    @JvmStatic
    var screenWidth = UIUtils.getScreenWidth()

    @JvmStatic
    var screenHeight = UIUtils.getScreenHeight()

    @JvmStatic
    var ITEM_CARD_CORNER = UIUtils.getPixels(10F)

    @JvmStatic
    var cardWidth = (screenWidth - UIUtils.getPixels(11.5f) * 2) / 4

    @JvmStatic
    var cardHeight = cardWidth * 200 / 162

    fun refreshViewSize() {
        screenWidth = UIUtils.getScreenWidth()
        screenHeight = UIUtils.getScreenHeight()
        ITEM_CARD_CORNER = UIUtils.getPixels(10F)
        cardWidth = (screenWidth - UIUtils.getPixels(11.5f) * 2) / 4
        cardHeight = cardWidth * 200 / 162
    }

    /**
     * 打招呼加载下一页
     */
    const val EVENT_SAYHI_LIST_LOADMORE_DATA = "event_sayhi_list_loadmore_data"

    /**
     * 在卡片页面触发举报事件时需要删除session
     */
    const val EVENT_SAYHI_STACK_CARD_REPORT = "event_sayhi_stack_card_report"

    /**
     * 在卡片页面触发举报事件时需要删除session
     */
    const val EVENT_SAYHI_STACK_CARD_WEB_REPORT = "event_sayhi_stack_card_web_report"

    /**
     * 当卡片曝光时需要更新列表的未读数
     */
    const val EVENT_SAYHI_STACK_CARD_STACK_SHOW = "event_sayhi_stack_card_stack_show"

    const val EVENT_ON_INNER_SESSION_DELETE = "event_on_inner_session_delete"

    /**
     * 卡片完成加载更多数据
     */
    const val EVENT_SAYHI_CARD_FINISHED_LOADMORE_DATA = "event_sayhi_card_finished_loadmore_data"
    const val EVENT_ON_CARD_BACK_CLOSE_CONFIG = "event_on_card_back_close_config" // 关闭所有招呼页面

    // push来的招呼来了新消息
    const val EVENT_SAYHI_PUSH_SESSION_NEW = "event_sayhi_push_session_new"
    const val PARAM_NEW_MSG_SESSION = "param_new_msg_session"

    /**
     *  当新用户收到时需要刷新
     */
    const val EVENT_SAYHI_CARD_ON_NEW_HI = "event_sayhi_card_on_new_hi"

    const val CARD_MODEL_STATE_LOADING = 0
    const val CARD_MODEL_STATE_NORMAL = 1
    const val CARD_MODEL_STATE_ERROR = 2

    /**
     * 划卡页面是否正在打开
     */
    var isCardPageShowing = false
    var needRefreshTinyCard = false // 需要刷新小卡数据

    // 这部分用户记录卡片数据
    @Volatile
    var sayHiCardData = CopyOnWriteArrayList<SayHiStackCardInfo>() //展示的所有卡片数据

    @Volatile
    private var newLoadMoreCardList = CopyOnWriteArrayList<SayHiStackCardInfo>()  //未曝光的加载更多的数据

    @Volatile
    var newInsertCardList = CopyOnWriteArrayList<SayHiStackCardInfo>()  //卡片页面在时手打的新卡片的数据

    // 这部分用户处理卡片计数
    private var allUnreadUsers = linkedSetOf<SayhiSession>() // 所有未读用户
    private var cardFilterUsers = hashSetOf<SayhiSession>() // 服务器过滤的卡片用户
    private val cardHasDealFilterUsers = hashSetOf<SayhiSession>() // 已经处理过的新招呼
    private var newUsersSessions = hashSetOf<SayhiSession>()    // 新插入的用户个数
    var newHiInCardReceive = hashSetOf<SayhiSession>() // 在卡片页面收到的新招呼需要过滤记录未读数
    private var gotoPageNewUsersSessions = mutableListOf<SayhiSession>()

    fun appendPushCard(sayhiSession: SayhiSession?) {
        kotlin.runCatching {
            sayhiSession ?: return
            allUnreadUsers.add(sayhiSession)
        }
    }

    fun readAllUnDealUsers() {
        cardHasDealFilterUsers.addAll(allUnreadUsers)
    }

    fun hasNewCardData(): Boolean {
        return sayHiCardData.size > 0 || newLoadMoreCardList.size > 0 || newInsertCardList.size > 0
    }

    /**
     * 是否有未加载的卡片
     */
    fun hasNoLoadCards(): Boolean {
        return !hasNewCardData() && getAllUnreadUsersData().size > 0
    }

    fun refreshNewHiUsers() {
        kotlin.runCatching {
            gotoPageNewUsersSessions = if (SayHiArgs.isOpenBlock())
                SingleMsgService.getInstance().getAllSayHiUnreadWithPartSpamUserUserId(isOpenExp())
            else
                SingleMsgService.getInstance().getAllSayhiUnreadUserId(isOpenExp())
            MDLog.i(
                TAG,
                "gotoPageNewUsersSessions=" + JSON.toJSONString(gotoPageNewUsersSessions.map { it.momoid })
            )
        }
    }

    /**
     * 是否为刚进入页面的招呼
     */
    fun isNewUsersIntoPage(sayHiInfo: SayhiSession) = gotoPageNewUsersSessions.contains(sayHiInfo)

    /**
     * 是否已经处理过卡片
     */
    fun hasDealSession(sayHiInfo: SayhiSession): Boolean {
        return cardHasDealFilterUsers.contains(sayHiInfo)
    }

    fun onCardDealRecord(sayHiInfo: SayhiSession) {
        kotlin.runCatching {
            MDLog.i(TAG, "onCardDealRecord=${sayHiInfo.momoid}")
            appendOnlyDealCardId(sayHiInfo)
            checkRemoveStackCard(sayHiInfo, true)
        }
    }

    fun clearTinyCardDataByType(fromType: Int) {
        kotlin.runCatching {
            extracted(sayHiCardData, fromType)
            extracted(newLoadMoreCardList, fromType)
            extracted(newInsertCardList, fromType)
        }
    }

    private fun extracted(checkCardData: MutableList<SayHiStackCardInfo>, fromType: Int) {
        kotlin.runCatching {
            val needRemoveList = arrayListOf<SayHiStackCardInfo>()
            for (checkCard in checkCardData) {
                if ((fromType == SayhiSession.FROM_TYPE_LIVE && checkCard.sayHiInfo.isFromLive) ||
                    (fromType == SayhiSession.FROM_TYPE_GIFT && checkCard.sayHiInfo.isFromGift)
                ) {
                    val sayHiInfo = checkCard.sayHiInfo
                    appendOnlyDealCardId(sayHiInfo)
                    needRemoveList.add(checkCard)
                }
            }
            checkCardData.removeAll(needRemoveList)
        }
    }

    fun appendOnlyDealCardId(sayHiInfo: SayhiSession) {
        kotlin.runCatching {
            if (!cardHasDealFilterUsers.contains(sayHiInfo)) {
                cardHasDealFilterUsers.add(sayHiInfo)
            }
        }
    }

    /**
     * 检查删除卡片
     */
    fun checkRemoveStackCard(checkSession: SayhiSession?, forceDelete: Boolean = false) {
        kotlin.runCatching {
            checkSession ?: return
            val momoid = checkSession.momoid
            if (!forceDelete && checkSession.unreadMsgCount > 0) return
            MDLog.i(TAG, "checkRemoveStackCard sayHiCardData=${sayHiCardData.size}")
            onSessionReadOrDelete(momoid)
        }
    }

    /**
     * 增加过滤卡片用户
     */
    fun appendCardFilterUser(userSessions: List<SayhiSession>) {
        kotlin.runCatching {
            cardFilterUsers.addAll(userSessions)
        }
    }

    /**
     * 获取未读招呼人数
     */
    fun getAllUnreadUsersData(fromCard: Boolean = false): HashSet<SayhiSession> {
        val finalUnreadUsers = hashSetOf<SayhiSession>()
        kotlin.runCatching {
            finalUnreadUsers.addAll(allUnreadUsers)
            finalUnreadUsers.addAll(newUsersSessions)
            if (cardFilterUsers.size > 0 || cardHasDealFilterUsers.size > 0) {
                finalUnreadUsers.addAll(newUsersSessions)
                finalUnreadUsers.removeAll(cardFilterUsers)
                finalUnreadUsers.removeAll(cardHasDealFilterUsers)
                if (fromCard) {
                    finalUnreadUsers.removeAll(newHiInCardReceive)
                }
            }
            if (BuildConfig.DEBUG) {
                GlobalScope.launch(MMDispatchers.User) {
                    MDLog.d(
                        TAG, "\n" +
                                "allUnreadUsers=${JSON.toJSON(allUnreadUsers.map { it.momoid })}\n" +
                                "newUsersSessions=${JSON.toJSON(newUsersSessions.map { it.momoid })}\n" +
                                "cardFilterUsers=${JSON.toJSON(cardFilterUsers.map { it.momoid })}\n" +
                                "cardHasDealFilterUsers=${JSON.toJSON(cardHasDealFilterUsers.map { it.momoid })}\n" +
                                "finalUnreadUsers=${JSON.toJSON(finalUnreadUsers.map { it.momoid })}"
                    )
                }
            }
        }
        return finalUnreadUsers
    }

    /**
     * 添加滑卡数据
     */
    fun addStackCardData(sessionList: MutableList<SayHiStackCardInfo>, append: Boolean = false) {
        kotlin.runCatching {
            if (!append) {
                sayHiCardData.clear()
            }
            sayHiCardData.addAll(sessionList)
        }
    }

    /**
     * 是否应该加载更多打招呼
     */
    fun needShowCardStack(): Boolean {
        return newLoadMoreCardList.size > 0
    }

    /**
     * 将下一页数据添加到卡片集合中
     */
    fun refreshLoadMoreCards() {
        kotlin.runCatching {
            newLoadMoreCardList.let {
                sayHiCardData.addAll(it)
                it.clear()
            }
        }
    }

    /**
     * 处理新插入数据
     */
    fun refreshNewInsertCards() {
        kotlin.runCatching {
            newInsertCardList.let {
                sayHiCardData.addAll(0, it)
                it.clear()
            }
        }
    }

    /**
     * 刷新所有数据
     */
    fun refreshAllCardData() {
        kotlin.runCatching {
            refreshLoadMoreCards()
            refreshNewInsertCards()
        }
    }

    fun getUnExposureLoadMoreSayHiList(): MutableList<SayHiStackCardInfo> {
        return newLoadMoreCardList
    }

    /**
     * 查询所有的未读用户
     */
    fun refreshAllUnreadHiUsers(fromInit: Boolean = false): List<SayhiSession> {
        kotlin.runCatching {
            val allUnreadUsersList =
                SingleMsgService.getInstance().getAllSayHiWithPartSpamUserUserNew(isOpenExp())
            if (fromInit) {
                allUnreadUsers.clear()
                allUnreadUsers.addAll(allUnreadUsersList)
                MDLog.d(
                    TAG,
                    "refreshAllUnreadHiUsers allUnreadUsers=${JSON.toJSON(allUnreadUsers.map { it.momoid })}\n"
                )
            }
            return allUnreadUsersList
        }
        return arrayListOf()
    }

    /**
     * 添加下一页数据
     */
    fun appendLoadMoreCards(sessionCardList: MutableList<SayHiStackCardInfo>) {
        kotlin.runCatching {
            if (sessionCardList.isNotEmpty()) {
                newLoadMoreCardList.addAll(sessionCardList)
            }
        }
    }

    /**
     * 小卡列表
     */
    fun getTinyCardList(needRefreshData: Boolean = false): MutableList<SayHiStackCardInfo> {
        kotlin.runCatching {
            if (needRefreshData) {  // 需要刷新所有的数据
                refreshAllCardData()
            }
            return if (sayHiCardData.size > 0) {
                if (sayHiCardData.size > 4) {
                    sayHiCardData.subList(0, 4)
                } else {
                    sayHiCardData
                }
            } else if (newLoadMoreCardList.size > 0) {
                if (newLoadMoreCardList.size > 4) {
                    newLoadMoreCardList.subList(0, 4)
                } else {
                    newLoadMoreCardList
                }
            } else {
                mutableListOf()
            }
        }
        return mutableListOf()
    }

    /**
     * 当小卡片点击的时候需要移动位置
     */
    fun onTinyCardClick(sayHiInfo: SayHiInfo?) {
        kotlin.runCatching {
            sayHiInfo ?: return
            for (checkCard in sayHiCardData) {
                checkCard.cardShowType = CARD_TYPE_TYPE_NORMAL
                checkCard.isNewHiInSessionList = false
            }
        }
    }

    fun onTinyCardSeeFinish() {
        kotlin.runCatching {
            for (checkCard in sayHiCardData) {
                checkCard.cardShowType = CARD_TYPE_TYPE_NORMAL
                checkCard.isNewHiInSessionList = false
                checkCard.isNewHiInStackCard = false
            }
        }
    }

    /**
     * 刷新数据
     */
    fun onSessionReadOrDelete(momoid: String) {
        kotlin.runCatching {
            checkSessionReadOrDelete(sayHiCardData, momoid)
            checkSessionReadOrDelete(newInsertCardList, momoid)
            checkSessionReadOrDelete(newLoadMoreCardList, momoid)
        }
    }

    private fun checkSessionReadOrDelete(
        checkCards: MutableList<SayHiStackCardInfo>?, momoid: String?
    ) {
        kotlin.runCatching {
            checkCards ?: return
            if (checkCards.isEmpty()) return
            momoid?.takeIf { it.isNotBlank() }.also {
                for (checkCard in checkCards) {
                    if (checkCard.sayHiInfo.momoid == momoid) {
                        checkCards.remove(checkCard)
                        return@runCatching
                    }
                }
            }
        }
    }

    /**
     * 当卡片被处理了
     */
    fun onHiCardDeal(momoid: String?) {
        checkSessionReadOrDelete(sayHiCardData, momoid)
    }

    /**
     * 添加新招呼卡片
     */
    fun appendNewHiCards(sayHiStackCardInfo: MutableList<SayHiStackCardInfo>) {
        kotlin.runCatching {
            newInsertCardList.addAll(0, sayHiStackCardInfo)
            sayHiStackCardInfo.forEach {
                newUsersSessions.add(it.sayHiInfo)
                if (isCardPageShowing) { // 卡片页面需要单独排除未读数
                    if (!newHiInCardReceive.contains(it.sayHiInfo)) {
                        newHiInCardReceive.add(it.sayHiInfo)
                    }
                }
            }
            if (isCardPageShowing) {
                GlobalEventManager.getInstance().sendEvent(
                    GlobalEventManager.Event(EVENT_SAYHI_CARD_ON_NEW_HI)
                        .src(GlobalEventManager.EVN_NATIVE).dst(GlobalEventManager.EVN_NATIVE)
                )
            }
        }
    }

    fun initDebug(view: View) {
        if (MomoKit.isInnerNet() || com.immomo.momo.util.MomoKit.isDebugAlphaOrBetaVersion()) {
            view.setOnLongClickListener {
                val context = view.context
                context.startActivity(Intent(context, TestSettingActivity::class.java))
                true
            }
        }
    }

    @JvmStatic
    fun refreshGiftMsgSessionState() {
        GlobalScope.launch(MMDispatchers.User) {
            kotlin.runCatching {
                // 重新刷新礼物招呼
                get().syncSession(
                    SessionUpdateBundle.ClearUnread(fromString(SayHiSessionDefinition.SAYHI))
                )
                if (isOpenExp()) {
                    get().syncSession(
                        SessionUpdateBundle.ReloadInfo(fromString(GiftSayHiSessionDefinition.SAYHI))
                    )
                }
            }
        }
    }

    fun onAllSessionReadClear() {
        kotlin.runCatching {
            allUnreadUsers.clear()
            cardFilterUsers.clear()
            newInsertCardList.clear()
            newUsersSessions.clear()
            sayHiCardData.clear()
            newLoadMoreCardList.clear()
        }
    }

    fun onSessionRemovedEvent(momoid: String?) {
        momoid ?: return
        if (!NewSayUIConfigV1.isUserNewUI()) {
            return
        }
        val msg = HashMap<String, Any>()
        msg["momoid"] = momoid
        GlobalEventManager.getInstance().sendEvent(
            GlobalEventManager.Event(
                EVENT_ON_INNER_SESSION_DELETE
            ).msg(msg)
                .src(GlobalEventManager.EVN_NATIVE).dst(GlobalEventManager.EVN_NATIVE)
        )
    }

    fun release() {
        kotlin.runCatching {
            gotoPageNewUsersSessions.clear()
            needRefreshTinyCard = false
            newHiInCardReceive.clear()
            cardHasDealFilterUsers.clear()
            allUnreadUsers.clear()
            cardFilterUsers.clear()
            newInsertCardList.clear()
            newUsersSessions.clear()
            sayHiCardData.clear()
            newLoadMoreCardList.clear()
            jumpStackCardBg?.recycle()
        }
    }

    fun onSessionMessageRead(momoid: String?, sayHiInfo: SayhiSession, notNotify: Boolean = false) {
        kotlin.runCatching {
            momoid ?: return
            if (sayHiInfo.isFromGift || sayHiInfo.isFromLive) {
                SingleMsgService.getInstance().updateSayhiInnerIgnoreLive(momoid)
            } else {
                SingleMsgService.getInstance().updateSayhiInnerIgnore(momoid)
            }
            if (notNotify) return
            val msgData = mutableMapOf<String, Any>()
            msgData["momoid"] = momoid
            GlobalEventManager.getInstance().sendEvent(
                GlobalEventManager.Event(EVENT_SAYHI_STACK_CARD_STACK_SHOW).msg(msgData)
                    .src(GlobalEventManager.EVN_NATIVE).dst(GlobalEventManager.EVN_NATIVE) // 需要刷新列表
            )
        }
    }

    fun setSayhiKeyboardState(isHide: Boolean) {
        hideGreetKeyboard = isHide
        MDLog.i("getHideGreetKeyboard", "setSayhiKeyboardState=$isHide")
    }

    /**
     * 当有已读发生时
     */
    fun recordErrorSessionRead(momoid: String) {
        kotlin.runCatching {
            val idsUnread = allUnreadUsers.joinToString(separator = ",") { it.momoid }
            val idsFilter = cardHasDealFilterUsers.joinToString(separator = ",") { it.momoid }
            MURealtimeLog.business(MULogConstants.BUSINESS_MOMO_BASIC)
                .secondLBusiness("sayhi")
                .thirdLBusiness("recordErrorSessionRead")
                .addBodyItem(MUPairItem.content("idsUnread=$idsUnread\nidsFilter=$idsFilter"))
                .addBodyItem(MUPairItem.errorMsg(momoid))
                .commit()
        }
    }

}