package com.immomo.momo.message.paper.common

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import com.immomo.framework.account.MessageManager
import com.immomo.momo.R
import com.immomo.momo.eventbus.DataEvent
import com.immomo.momo.expand.allNotNull
import com.immomo.momo.gift.base.ContinuityGiftPlayBean
import com.immomo.momo.globalevent.GlobalEventManager
import com.immomo.momo.message.ChatHelper
import com.immomo.momo.message.activity.ChatActivity
import com.immomo.momo.message.helper.LoveApartmentWebHelper
import com.immomo.momo.message.paper.BasePaperFragment
import com.immomo.momo.message.paper.PaperCommonViewModel
import com.immomo.momo.message.paper.event.PaperEvent
import com.immomo.momo.message.paper.event.PlayGiftData
import com.immomo.momo.protocol.imjson.receiver.MessageKeys
import de.greenrobot.event.EventBus

/**
 * 爱情公寓
 * <AUTHOR>
 * @data 2020-12-18.
 */
class LoveApartmentPaperFragment : BasePaperFragment(), MessageManager.MessageSubscriber {

    private var loveApartmentHelper: LoveApartmentWebHelper? = null
    private var mContentView: View? = null
    private var subscriber: GlobalEventManager.Subscriber? = null

    companion object {
        fun newInstance(): LoveApartmentPaperFragment {
            return LoveApartmentPaperFragment()
        }
    }

    override fun getContainerId(): Int = R.id.chat_love_apartment_paper_container

    override fun getPageLayout(): Int = R.layout.paper_chat_love_appartment


    override fun initPageViews(contentView: View?) {
        mContentView = contentView
        MessageManager.registerMessageReceiver(
                this.hashCode(),
                this,
                800,
                MessageKeys.ACTION_LOVE_APARTMENT
        )
        subscriber = GlobalEventManager.Subscriber { event ->
            val msg = event?.msg
            when (event?.name) {
                ChatHelper.MK_EVENT_LOVE_APARTMENT_CLOSE_TOP_WEBVIEW -> {
                    loveApartmentHelper?.forceCloseWebView()
                }
            }
        }
        subscriber?.let {
            GlobalEventManager.getInstance().register(it, GlobalEventManager.EVN_NATIVE)
        }
    }

    override fun onPageLoad() {

    }

    override fun onResume() {
        super.onResume()
        loveApartmentHelper?.onResume()
    }

    override fun onMessageReceive(bundle: Bundle?, action: String?): Boolean {
        when (action) {
            MessageKeys.ACTION_LOVE_APARTMENT -> {
                refreshLoveApartment(bundle)
            }
        }
        return false
    }

    private fun refreshLoveApartment() {
        initLoveApartMent()
        getChatActivity()?.let {
            loveApartmentHelper?.initEntryInfo(it.isFullModel,it.chatId) // 半屏不展示爱情公寓
        }
    }

    // 从set来刷新爱情公寓
    private fun refreshLoveApartment(bundle: Bundle?) {
        val remoteId = if (bundle != null) bundle.getString(MessageKeys.Key_RemoteId) else ""
        // 非当前会话对象，不接受
        getChatActivity()?.let {
            if (!TextUtils.equals(it.chatId, remoteId)) return
            initLoveApartMent()
            loveApartmentHelper?.refreshFromSet(it.isFullModel, bundle, it.remoteUser, it.chatId, it.isForeground)

        }
    }

    private fun initLoveApartMent() {
        if (loveApartmentHelper != null) {
            return
        }
        mContentView?.let {
            loveApartmentHelper = LoveApartmentWebHelper()
        }

        loveApartmentHelper?.setCallback(object : LoveApartmentWebHelper.Callback {
            override fun onLoveApartmentPlayGift(giftBean: ContinuityGiftPlayBean?) {
                if (giftBean != null) {
                    EventBus.getDefault().post(DataEvent(PaperEvent.PAPER_EVENT_PLAY_GIFT, PlayGiftData(giftBean, giftBean.animType)))
                }
            }

            override fun onReInitWebView() { // 彻底销毁后重建webview
                initLoveApartmentWebView()
            }

        })
        initLoveApartmentWebView()
    }

    private fun initLoveApartmentWebView() {
        allNotNull(activity, mContentView) { activity, view ->
            loveApartmentHelper?.initWebView(activity, view.findViewById(R.id.top_webview_container))
        }
    }

    private fun destroyLoveApartment() {
        loveApartmentHelper?.onViewDestroy()
        loveApartmentHelper = null
    }

    override fun onEvent(event: DataEvent<Any>) {
        super.onEvent(event)
        when (event.action) {
            PaperEvent.PAPER_EVENT_LOVE_APARTMENT_REFRESH -> {
                refreshLoveApartment()
            }
        }
    }

    override fun onDestroy() {
        destroyLoveApartment()
        subscriber?.let {
            GlobalEventManager.getInstance().unregister(it, GlobalEventManager.EVN_NATIVE)
        }
        super.onDestroy()
    }

    private fun getChatActivity(): ChatActivity? {
        (activity as? ChatActivity)?.let {
            return it
        }
        return null
    }

}