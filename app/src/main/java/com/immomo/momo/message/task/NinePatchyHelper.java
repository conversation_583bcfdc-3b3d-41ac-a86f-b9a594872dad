package com.immomo.momo.message.task;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.NinePatch;
import android.graphics.Rect;
import android.graphics.drawable.NinePatchDrawable;
import android.util.DisplayMetrics;
import android.view.View;

import com.cosmos.mdlog.MDLog;
import com.immomo.framework.utils.UIUtils;
import com.immomo.molive.foundation.ninepatchchunk.NinePatchChunk;
import com.immomo.momo.LogTag;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;

public class NinePatchyHelper {

    private static BitmapFactory.Options getDpiOption(int inDensity) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        DisplayMetrics metrics = UIUtils.getDisplayMetrics();
        options.inDensity = inDensity; //Being DPI for MDPI
        options.inTargetDensity = metrics.densityDpi; //Being current DPI
        //inDensity/inTargetDensity are a ratio
        return options;
    }

    public static void setNinePatchBackground(View itemView, Rect padding, File file, int inDensity) {
        if (file == null || itemView == null) {
            return;
        }
        BitmapFactory.Options options = getDpiOption(inDensity);
        Bitmap b = BitmapFactory.decodeFile(file.getAbsolutePath(), options);
        setNinePatchBitmap(itemView, b, padding);
    }

    public static void setNinePatchBitmap(View itemView, Bitmap bitmap, Rect padding) {
        if (bitmap == null || itemView == null) {
            return;
        }
        try {
            NinePatchChunk chunk = NinePatchChunk.createCenterStretchingChunk(bitmap, padding);
            NinePatchChunk.createColorsArrayAndSet(chunk, bitmap.getWidth(), bitmap.getHeight());
            boolean result = NinePatch.isNinePatchChunk(chunk.toBytes());
            if (result) {
                NinePatchDrawable patchy = new NinePatchDrawable(UIUtils.getResources(), bitmap, chunk.toBytes(), chunk.padding, null);
                patchy.setTargetDensity(UIUtils.getDisplayMetrics());
                itemView.setBackground(patchy);
            }
        } catch (Exception e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
    }

    public static void setNinePatchBackground(View view, File file, int imageDensity, String fileName) {
        if (view == null || file == null) return;
        try {
            NinePatchDrawable d = NinePatchChunk.create9PatchDrawable(view.getContext(), new FileInputStream(file), imageDensity, fileName);
            view.setBackground(d);
        } catch (FileNotFoundException e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
    }
}
