package com.immomo.momo.message.paper.chat.single

import android.view.View
import com.immomo.momo.R
import com.immomo.momo.message.paper.BasePaperContainerFragment
import com.immomo.momo.message.paper.PaperConfig
import com.immomo.momo.message.paper.common.SingleChatBackgroundPaperFragment

/**
 * <AUTHOR>
 * @data 2020-12-18.
 */

class SingleChatBottomPaperFragment : BasePaperContainerFragment() {

    companion object {
        @JvmStatic
        fun newInstance(): SingleChatBottomPaperFragment {
            return SingleChatBottomPaperFragment()
        }
    }

    override fun getPagers(): MutableList<PaperConfig>? = mutableListOf(PaperConfig(SingleChatBackgroundPaperFragment.newInstance()))

    override fun getPageLayout(): Int = R.layout.paper_single_chat_bottom

    override fun onPageLoad() {
    }

    override fun initPageViews(contentView: View?) {
    }

}