package com.immomo.momo.gene.receiver

import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.immomo.framework.base.BaseReceiver
import com.immomo.momo.MomoKit

class SayHiChangedReceiver : BaseReceiver {

    constructor(context: Context?) : super(context) {
        register()
    }

    fun register() {
        register(ACTION_SAYHI_REMOVE, ACTION_SAYHI_REMOVE_ALL)
    }


    override fun register(vararg action: String) {
        val filter = IntentFilter()
        for (a in action) {
            filter.addAction(a)
        }
        LocalBroadcastManager.getInstance(MomoKit.getApp()).registerReceiver(this, filter)
    }

    fun unregister() {
        LocalBroadcastManager.getInstance(MomoKit.getApp()).unregisterReceiver(this)
    }

    companion object {
        const val ACTION_SAYHI_REMOVE = "action_sayhi_remove"
        const val ACTION_SAYHI_REMOVE_ALL = "action_sayhi_remove_all"

        const val KEY_MOMOID = "key_momoid"

        fun sendRemove(momoid: String) {
            val broadcast = Intent(ACTION_SAYHI_REMOVE)
            broadcast.putExtra(KEY_MOMOID, momoid)
            LocalBroadcastManager.getInstance(MomoKit.getApp()).sendBroadcast(broadcast)
        }

        fun sendRemoveAll() {
            val broadcast = Intent(ACTION_SAYHI_REMOVE_ALL)
            LocalBroadcastManager.getInstance(MomoKit.getApp()).sendBroadcast(broadcast)
        }

    }
}