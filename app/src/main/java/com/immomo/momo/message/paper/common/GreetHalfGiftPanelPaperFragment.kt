package com.immomo.momo.message.paper.common

import android.view.View
import android.widget.RelativeLayout
import androidx.lifecycle.ViewModelProvider
import com.immomo.framework.utils.UIUtils
import com.immomo.mmutil.task.MomoMainThreadExecutor
import com.immomo.momo.R
import com.immomo.momo.eventbus.DataEvent
import com.immomo.momo.gift.bean.BaseGift
import com.immomo.momo.gift.bean.GiftReceiver
import com.immomo.momo.gift.manager.GreetChatPageGiftManager
import com.immomo.momo.greet.GreetHelper
import com.immomo.momo.message.ChatHelper
import com.immomo.momo.message.activity.ChatActivity
import com.immomo.momo.message.paper.BasePaperFragment
import com.immomo.momo.message.paper.PaperCommonViewModel
import com.immomo.momo.message.paper.chat.ChatPaperBooleanListener
import com.immomo.momo.message.paper.event.PaperEvent
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.sessions.SessionUserCache

/**
 * 招呼礼物面板
 * <AUTHOR>
 * @data 2020-12-18.
 */

class GreetHalfGiftPanelPaperFragment : BasePaperFragment() {

    private var greetChatPageGiftManager: GreetChatPageGiftManager? = null

    private var mContentView: View? = null

    private var mPaperCommonViewModel: PaperCommonViewModel? = null


    companion object {
        fun newInstance(): GreetHalfGiftPanelPaperFragment {
            return GreetHalfGiftPanelPaperFragment()
        }
    }

    override fun getContainerId(): Int = R.id.chat_greet_gift_panel_paper_container

    override fun getPageLayout(): Int = R.layout.paper_chat_greet_half_gift_panel


    override fun initPageViews(contentView: View?) {
        mContentView = contentView
        getChatActivity()?.let {
            mPaperCommonViewModel = ViewModelProvider(it).get(PaperCommonViewModel::class.java)
            mPaperCommonViewModel?.greetGiftPanelShowListener = object : ChatPaperBooleanListener {
                override fun onResult(): Boolean {
                    return isGreetGiftPanelShow()
                }
            }
            if (it.isGreetHalfMode) {
                showGreetGiftFirst()
            }
        }

    }

    override fun onPageLoad() {

    }


    /**
     * 加载打招呼礼物面板
     */
    private fun loadGreetGiftPanel() {
        val sendId = getChatId()
        val send = SessionUserCache.getUser(sendId)
        if (send == null) {
            greetChatPageGiftManager?.showGiftPanel(GiftReceiver(sendId, null, sendId))
        } else {
            greetChatPageGiftManager?.showGiftPanel(GiftReceiver(sendId, send.loadImageId, send.displayName))
        }
    }

    private fun hide() {
        greetChatPageGiftManager?.hideGiftPanel()
    }

    private fun show() {
        GreetHelper.saveShowGreetGift()
        if (greetChatPageGiftManager == null && activity != null) {
            greetChatPageGiftManager = GreetChatPageGiftManager(mContentView?.findViewById(R.id.greet_half_gift_panel_stub),
                    activity, Message.CHATTYPE_USER)
            greetChatPageGiftManager?.setSceneId(getChatId())
            greetChatPageGiftManager?.setGiftSource(getGiftSource())
            greetChatPageGiftManager?.setChatGiftEventListener(object : GreetChatPageGiftManager.ChatGiftEventListener {
                override fun goToChooseReceiver() {}

                override fun sendGiftSuccess(balance: Long, baseGift: BaseGift) {
                }

                override fun couldSendGift(): Boolean = true

                override fun clickSendGift(baseGift: BaseGift) {
                    greetGiftClick()
                }

                override fun onLongClick(baseGift: BaseGift) {}
            })

            loadGreetGiftPanel()

            val layoutParams = greetChatPageGiftManager?.panelView?.layoutParams as? RelativeLayout.LayoutParams
            layoutParams?.bottomMargin = UIUtils.getPixels(80f)
            greetChatPageGiftManager?.panelView?.layoutParams = layoutParams

        } else if (greetChatPageGiftManager != null && greetChatPageGiftManager?.needGetData() == true) {
            loadGreetGiftPanel()
        }
        greetChatPageGiftManager?.panelView?.visibility = View.VISIBLE
    }


    private fun isGreetGiftPanelShow(): Boolean {
        return greetChatPageGiftManager?.isGiftPanelShow ?: false

    }

    private fun showGreetGiftFirst() {
        if (!GreetHelper.isFirstShowGreetGift()) {
            return
        }
        show()
        MomoMainThreadExecutor.postDelayed(ChatHelper.DismissGreetGiftTag, { hide() }, 3000)
    }

    private fun greetGiftClick() {
        MomoMainThreadExecutor.cancelAllRunnables(ChatHelper.DismissGreetGiftTag)
    }

    override fun onEvent(event: DataEvent<Any>) {
        super.onEvent(event)
        when (event.action) {
            PaperEvent.PAPER_EVENT_GREET_SHOW_GIFT_PANEL -> {
                show()
            }
            PaperEvent.PAPER_EVENT_GREET_HIDE_GIFT_PANEL -> {
                hide()
            }
        }
    }


    override fun onDestroy() {
        super.onDestroy()
        MomoMainThreadExecutor.cancelAllRunnables(ChatHelper.DismissGreetGiftTag)
        greetChatPageGiftManager?.onDestroy()
    }


    fun getChatId(): String {
        return getChatActivity()?.chatID ?: ""
    }


    fun getGiftSource(): String {

        return getChatActivity()?.giftSource ?: ""
    }

    private fun getChatActivity(): ChatActivity? {
        (activity as? ChatActivity)?.let {
            return it
        }
        return null
    }

}