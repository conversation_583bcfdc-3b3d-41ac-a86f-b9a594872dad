package com.immomo.momo.message.view;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.immomo.momo.R;
import com.immomo.momo.service.bean.message.Type22Content;

import java.util.ArrayList;
import java.util.List;

import static com.immomo.framework.common.CollectionsHelper.getOrNull;

/**
 * Created by xudshen on 06/09/2017.
 */

public class QuizAnswerListLayout extends LinearLayout {
    @NonNull
    private List<Type22Content.Answer> answers = new ArrayList<>();

    @Nullable
    private OnClickListener onClickListener;
    @Nullable
    private OnQuizAnswerClickListener onQuizAnswerClickListener;

    public QuizAnswerListLayout(Context context) {
        this(context, null, 0);
    }

    public QuizAnswerListLayout(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public QuizAnswerListLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        onClickListener = new OnClickListener() {
            @Override
            public void onClick(View v) {
                Object idxObj = v.getTag(R.id.message_quiz_answer_idx);
                if (idxObj != null) {
                    Type22Content.Answer answer = getOrNull(answers, (int) idxObj);
                    if (answer != null && onQuizAnswerClickListener != null) {
                        onQuizAnswerClickListener.onClicked(answer);
                    }
                }
            }
        };
    }

    public void refreshView(@Nullable List<Type22Content.Answer> newAnswers) {
        if (newAnswers == null) {
            newAnswers = new ArrayList<>();
        }
        if (isSameAnswer(answers, newAnswers)) {
            return;
        }

        removeAllViews();
        answers.clear();
        answers.addAll(newAnswers);
        for (int i = 0, length = answers.size(); i < length; i = i + 1) {
            TextView view = (TextView) LayoutInflater.from(getContext())
                    .inflate(R.layout.layout_message_quiz_answer, this, false);
            view.setTag(R.id.message_quiz_answer_idx, i);
            view.setText(answers.get(i).text);
            view.setOnClickListener(onClickListener);

            addView(view);
        }
    }

    public void setOnQuizAnswerClickListener(
            @Nullable OnQuizAnswerClickListener onQuizAnswerClickListener) {
        this.onQuizAnswerClickListener = onQuizAnswerClickListener;
    }

    public interface OnQuizAnswerClickListener {
        void onClicked(@NonNull Type22Content.Answer answer);
    }

    private static boolean isSameAnswer(@NonNull List<Type22Content.Answer> oldList,
                                        @NonNull List<Type22Content.Answer> newList) {
        if (oldList.size() != newList.size()) return false;
        for (int i = 0, length = oldList.size(); i < length; i = i + 1) {
            Type22Content.Answer oldAnswer = oldList.get(i),
                    newAnswer = newList.get(i);
            if (!TextUtils.equals(oldAnswer.text, newAnswer.text) ||
                    !TextUtils.equals(oldAnswer.gotoStr, newAnswer.gotoStr))
                return false;
        }
        return true;
    }
}
