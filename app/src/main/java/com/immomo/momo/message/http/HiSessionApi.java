package com.immomo.momo.message.http;

import com.immomo.momo.maintab.model.ReplySessionBean;
import com.immomo.momo.message.bean.GreetSession;
import com.immomo.momo.protocol.http.core.HttpClient;
import com.immomo.momo.util.GsonUtils;

import org.json.JSONObject;

import java.util.HashMap;


public class HiSessionApi extends HttpClient {

    public static HiSessionApi getInstance() {
        return HiSessionApiHolder.instance;
    }

    private static class HiSessionApiHolder {
        public static HiSessionApi instance = new HiSessionApi();
    }

    public GreetSession getHiSessionIdsFromApi(String oldestGreetTime) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("oldest_greet_time", oldestGreetTime);
        String url = V2 + "/user/greet/recommend";
        String result = doPost(url, params);
        JSONObject jsonObject = new JSONObject(result);
        JSONObject data = jsonObject.optJSONObject("data");
        return GsonUtils.g().fromJson(data.toString(), GreetSession.class);
    }

    public Object sessionItemClick(String momoId) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("to_momoid", momoId);
        String url = V2 + "/user/greet/click";
        return doPost(url, params);
    }

    public ReplySessionBean checkUnreplyByApi(String sessionIds) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("remoteids", sessionIds);
        String url = HttpsHost + "/v3/relation/greet/replied";
        String result = doPost(url, params);
        JSONObject jsonObject = new JSONObject(result);
        JSONObject data = jsonObject.optJSONObject("data");
        return GsonUtils.g().fromJson(data.toString(), ReplySessionBean.class);
    }
}
