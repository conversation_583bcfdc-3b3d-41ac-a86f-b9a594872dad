package com.immomo.momo.message.task;

import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.message.activity.ChatActivity;
import com.immomo.momo.message.task.util.MsgFilterUtil;
import com.immomo.momo.messages.service.SingleMsgService;
import com.immomo.momo.service.bean.Message;

import java.util.ArrayList;
import java.util.List;

import static com.immomo.momo.mvp.message.view.BaseMessageActivity.PAGE_SIZE;


/**
 * 单人聊天加载更多新消息的异步任务
 * <AUTHOR>
 * date 2020/8/15
 */
public class ChatLoadMoreNewMessageTask extends MomoTaskExecutor.Task<Object, Object, List<Message>> {

    private ChatActivity mActivity;


    public ChatLoadMoreNewMessageTask(ChatActivity mActivity) {
        this.mActivity = mActivity;
    }

    @Override
    protected List<Message> executeTask(Object... params) throws Exception {
        if (mActivity.msgChatData.isEmpty()){
            return new ArrayList<Message>();
        }
        Message message = mActivity.msgChatData.getMessageList().get(mActivity.msgChatData.getCount() - 1);
        List<Message> messages = SingleMsgService.getInstance().findNormalMessageByRemoteIdRange(
                mActivity.getMomoID(), message.id, true, PAGE_SIZE + 1);
        if (messages.size() == PAGE_SIZE + 1) {
            mActivity.setHasMoreNewerMessage(true);
            messages.remove(messages.size() - 1);
        } else {
            mActivity.setHasMoreNewerMessage(false);
        }
        messages = MsgFilterUtil.distinct(messages, mActivity.msgChatData.getMessageList());
        mActivity.preHandleMessages(messages, false);
        return messages;
    }

    @Override
    protected void onTaskFinish() {
        mActivity.msgChatRecycler.setLoadMoreComplete();
    }

    @Override
    protected void onTaskSuccess(List<Message> messages) {
        if (messages.size() > 0) {
            mActivity.msgChatData.addItemModels(messages);
        }
    }

    @Override
    protected void onTaskError(Exception e) {
        //do nothing
    }
}
