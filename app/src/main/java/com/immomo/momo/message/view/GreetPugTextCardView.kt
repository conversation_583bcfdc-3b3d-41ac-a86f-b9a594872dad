package com.immomo.momo.message.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import com.immomo.momo.R
import com.immomo.momo.message.bean.GreetTextCardData

/**
 * <AUTHOR>
 * @data 2020-08-25.
 */

class GreetPugTextCardView : GreetPugCardView {

    private var mPugTextView: TextView? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attributeSet: AttributeSet) : super(context, attributeSet)

    constructor(context: Context, attributeSet: AttributeSet, defStyleAttr: Int) : super(
            context,
            attributeSet,
            defStyleAttr
    )

    override fun createView() {
        LayoutInflater.from(context).inflate(R.layout.layout_greet_text_card, this, true)
        mPugTextView = findViewById(R.id.tv_greet_text_card_pug)

    }

    override fun refresh() {
        (data as? GreetTextCardData)?.let {
            mPugTextView?.text = it.pugText
            visibility = View.VISIBLE
            return
        }
        visibility = View.GONE
    }

}
