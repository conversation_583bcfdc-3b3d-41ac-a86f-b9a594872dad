package com.immomo.momo.message.paper.common.timely.statemachine

import com.immomo.framework.SPKeys
import com.immomo.framework.storage.kv.KV
import com.immomo.mmutil.NetUtils
import com.immomo.mmutil.log.Log4Android
import com.immomo.moment.config.MRecorderActions
import com.immomo.momo.message.helper.apt.MessageConfigV2Getter
import com.immomo.momo.moment.model.MicroVideoModel
import com.immomo.momo.moment.utils.RecoderFileUtils
import com.immomo.momo.util.VideoUtils
import com.immomo.momo.video.model.Video
import java.io.File
import java.io.IOException
import java.util.concurrent.atomic.AtomicReference


class TimelyVideoState(stateManager: TimelyStateManager) :
    AbsBackToCloseState(stateManager) {
    private var mVideoPath: String? = null
    var passTime = 0
    private var isTakePhoto = false
    private var isWillFinish = AtomicReference<Boolean>(false)
    private var isStart = AtomicReference<Boolean>(false)


    //最长15s，最短1s
    private val maxTime = MessageConfigV2Getter.get().timelyTimeLength()
    private val minTime = 1
    fun startRecord() {
        isWillFinish.set(false)
        isStart.set(false)
        generateVideoPath()?.let {
            mVideoPath = it
            stateMachine.multiRecorder?.let {
                it.setMediaOutPath(mVideoPath)
                stateMachine.mView.updateRecording()
                it.startRecording(object : MRecorderActions.OnRecordStartListener {
                    override fun onRecordStarted(isSuccessed: Boolean) {
                        if (isSuccessed) {
                            val segment = 2
                            loop(
                                hashTag().toString(),
                                1000 / segment,
                                maxTime * segment,
                                object : LoopRunnable {
                                    override fun loop(index: Long) {
                                        val current = index.toInt() / segment
                                        if (passTime != current) {
                                            runInUI {
                                                passTime = current
                                                updateViewVideoProgress()
                                            }
                                        }
                                        if (index.toInt() == 1) {
                                            isStart.set(true)
                                        }

                                        if (isWillFinish.get()) {
                                            runInUI {
                                                recordFinish(passTime >= minTime)
                                            }
                                        }
                                    }

                                    override fun timeout() {
                                        runInUI {
                                            passTime = maxTime
                                            updateViewVideoProgress()
                                            recordFinish(passTime >= minTime)
                                        }
                                    }
                                },
                                false
                            )

                        } else {
                            cancelRecord()
                        }
                    }
                })

            }
        }

    }


    fun updateViewVideoProgress() {
        if (!isTakePhoto) {
            stateMachine.mView.updateViewVideoProgress(passTime, maxTime)
        }
    }

    fun stopRecord(): Boolean {
        if (!isStart.get()) {
            return false
        }
        cancelLoop(hashTag().toString())
        stateMachine.multiRecorder?.finishRecord(object :
            MRecorderActions.OnRecordFinishedListener {
            override fun onFinishingProgress(progress: Int) {
            }

            override fun onRecordFinished() {
                if (isTakePhoto) {
                    transition2Image()
                } else {
                    if (File(mVideoPath).length() <= 0) {
                        cancelRecord()
                        return
                    }

                    val video = Video(0, mVideoPath).apply {
                        VideoUtils.getVideoFixMetaInfo(this)
                        this.size = File(mVideoPath).length().toInt()
                        this.isCQ = KV.getUserInt(SPKeys.User.MicroVideo.KEY_VIDEO_CQ, 1) == 1
                        if (this.length != 0L) {
                            this.avgBitrate = (this.size.toLong() * 8000 / this.length).toInt()
                        }
                    }

                    stateMachine.multiRecorder?.cancelRecording()
                    stateMachine.releaseRecord()
                    runInUI {
                        transition {
                            TimelyReadySendState(stateMachine).apply {
                                microVideoModel = MicroVideoModel().apply {
                                    this.video = video
                                    this.isWifi = NetUtils.isWifi()
                                }
                                this.showReadyView()
                            }
                        }
                    }
                }
            }

            override fun onFinishError(errMsg: String?) {
                cancelRecord()
            }

        })
        return true
    }

    private fun cancelRecord() {
        runInUI {
            stateMachine.multiRecorder?.cancelRecording()
            stateMachine.mView.rollback2Ready()
        }
    }

    private fun  generateVideoPath(): String? {
        val tempMomentDir = RecoderFileUtils.getRecoderCache() ?: return null
        //增加nomedia文件 防止临时的视频，被小米等手机的相册能够查看到
        val noMedia = File(tempMomentDir, ".nomedia")
        if (!noMedia.exists()) {
            try {
                noMedia.createNewFile()
            } catch (e: IOException) {
                Log4Android.getInstance().e(e)
            }
        }
        val file = File(tempMomentDir, System.currentTimeMillis().toString() + ".mp4")
        return file.absolutePath
    }

    fun transition2Image() {
        transition { TimelyImageState(stateMachine).apply { takePhotoByVideo(mVideoPath) } }
    }

    fun recordFinish(longPress: Boolean) {
        isTakePhoto = !longPress
        isWillFinish.set(true)
        stopRecord()
    }

}

