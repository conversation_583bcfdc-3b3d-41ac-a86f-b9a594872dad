package com.immomo.momo.message.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.BitmapShader;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Shader;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.widget.ImageView;

import com.immomo.momo.R;

/**
 * Created by yo<PERSON><PERSON><PERSON> on 15/12/21.
 */
public class BubbleImageView extends ImageView {

    private static final int LOCATION_LEFT = 0;
    private static final Bitmap.Config BITMAP_CONFIG = Bitmap.Config.ARGB_8888;
    private static final int COLORDRAWABLE_DIMENSION = 1;
    private int baseOnWidth = -1;
    private int baseOnHeight = -1;

    private int leftTopRadius = dp2px(5);
    private int leftBottomRadius = dp2px(5);
    private int rightTopRadius = dp2px(5);
    private int rightBottomRadius = dp2px(5);
    private int mArrowTop = dp2px(40);
    private int mArrowWidth = dp2px(20);
    private int mArrowHeight = dp2px(20);
    private int mArrowOffset = 0;
    private static final int ARROW_RADIUS = 4;//钝化尖角的角度
    private int mArrowLocation = LOCATION_LEFT;
    private boolean isLeft = true;
    private boolean isShowArrow = true;

    private Rect mDrawableRect;
    private Bitmap mSrcBitmap;
    private BitmapShader mBitmapShader;
    private Paint mBitmapPaint;
    private Paint mShapePaint;
    private Matrix mShaderMatrix;
    private int mBitmapWidth;
    private int mBitmapHeight;

    private float offset;

    private RectF rectPath;
    private RectF rect;
    private Path path = new Path();
    private Path pathLine = new Path();

    public BubbleImageView(Context context) {
        super(context);
        initView(null);
    }

    public BubbleImageView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView(attrs);
    }

    public BubbleImageView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        initView(attrs);
    }

    private void initView(AttributeSet attrs) {
        if (attrs != null) {
            TypedArray a = getContext().obtainStyledAttributes(attrs, R.styleable.BubbleImageView);
            isShowArrow = a.getBoolean(R.styleable.BubbleImageView_bubble_show_arrow, true);
            mArrowHeight = (int) a.getDimension(R.styleable.BubbleImageView_bubble_arrowHeight, mArrowHeight);
            mArrowOffset = (int) a.getDimension(R.styleable.BubbleImageView_bubble_arrowOffset, mArrowOffset);
            mArrowTop = (int) a.getDimension(R.styleable.BubbleImageView_bubble_arrowTop, mArrowTop);
            mArrowWidth = (int) a.getDimension(R.styleable.BubbleImageView_bubble_arrowWidth, mArrowWidth);
            mArrowLocation = a.getInt(R.styleable.BubbleImageView_bubble_arrowLocation, mArrowLocation);

            leftTopRadius = (int) a.getDimension(R.styleable.BubbleImageView_bubble_left_top_radius, leftTopRadius);
            leftBottomRadius = (int) a.getDimension(R.styleable.BubbleImageView_bubble_left_bottom_radius, leftBottomRadius);
            rightTopRadius = (int) a.getDimension(R.styleable.BubbleImageView_bubble_right_top_radius, rightTopRadius);
            rightBottomRadius = (int) a.getDimension(R.styleable.BubbleImageView_bubble_right_bottom_radius, rightBottomRadius);
            a.recycle();
        }

        offset = dp2px(1);
    }

    public void setIsLeft(boolean isLeft) {
        this.isLeft = isLeft;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        if (getDrawable() == null) {
            return;
        }
        //目前发现低分辨率屏幕有适配问题，划线必须单独处理
        if (rectPath == null) {
            rectPath = new RectF(getPaddingLeft() + offset,
                    getPaddingTop() + offset,
                    getRight() - getLeft() - getPaddingRight() - offset,
                    getBottom() - getTop() - getPaddingBottom() - offset);
        } else {
            rectPath.set(getPaddingLeft() + offset,
                    getPaddingTop() + offset,
                    getRight() - getLeft() - getPaddingRight() - offset,
                    getBottom() - getTop() - getPaddingBottom() - offset);
        }

        if (rect == null) {
            rect = new RectF(getPaddingLeft() + offset / 2,
                    getPaddingTop() + offset / 2,
                    getRight() - getLeft() - getPaddingRight() - offset / 2,
                    getBottom() - getTop() - getPaddingBottom() - offset / 2);
        } else {
            rect.set(getPaddingLeft() + offset / 2,
                    getPaddingTop() + offset / 2,
                    getRight() - getLeft() - getPaddingRight() - offset / 2,
                    getBottom() - getTop() - getPaddingBottom() - offset / 2);
        }

        if (isShowArrow) {
            if (isLeft) {
                leftPath(rect, path);
                leftPath(rectPath, pathLine);
            } else {
                rightPath(rect, path);
                rightPath(rectPath, pathLine);
            }
        } else {
            drawRadius(rect, path);
            drawRadius(rectPath, pathLine);
        }

        canvas.drawPath(path, mShapePaint);
        canvas.drawPath(pathLine, mBitmapPaint);
        canvas.drawPath(path, mBitmapPaint);
    }

    private void rightPath(RectF rect, Path path) {
        path.reset();

        path.moveTo(rightTopRadius * 2F, rect.top);
        path.lineTo(rect.width() - 2 * rightTopRadius - mArrowWidth, rect.top);
        path.arcTo(new RectF(rect.right - rightTopRadius * 2 - mArrowWidth, rect.top,
                rect.right - mArrowWidth, rightTopRadius * 2 + rect.top), 270, 90);

        path.lineTo(rect.right - mArrowWidth, mArrowTop);
        path.lineTo(rect.right - ARROW_RADIUS, (float) mArrowTop - mArrowOffset);
        RectF rectF = new RectF(rect.right - ARROW_RADIUS * 2, (float) mArrowTop - mArrowOffset,
                rect.right, mArrowTop - mArrowOffset + ARROW_RADIUS * 2f);
        path.arcTo(rectF, 270, 120);
        path.quadTo(rect.right - mArrowWidth / 3f * 2 - ARROW_RADIUS / 2f, mArrowTop + mArrowHeight / 3f * 2,
                rect.right - mArrowWidth, mArrowTop + mArrowHeight + ARROW_RADIUS * 2f);

        path.lineTo(rect.right - mArrowWidth, rect.height() - rightBottomRadius);
        path.arcTo(new RectF(rect.right - rightBottomRadius * 2 - mArrowWidth,
                rect.bottom - rightBottomRadius * 2, rect.right - mArrowWidth, rect.bottom), 0, 90);
        path.lineTo(rect.left + 2 * leftBottomRadius, rect.bottom);
        path.arcTo(new RectF(rect.left, rect.bottom - leftBottomRadius * 2,
                leftBottomRadius * 2 + rect.left, rect.bottom), 90, 90);
        path.lineTo(rect.left, rect.top + 2 * leftTopRadius);
        path.arcTo(new RectF(rect.left, rect.top, leftTopRadius * 2 + rect.left,
                leftTopRadius * 2 + rect.top), 180, 90);
        path.close();
    }

    private void leftPath(RectF rect, Path path) {
        path.reset();
        path.moveTo(rightTopRadius * 2f + mArrowWidth, rect.top);
        path.lineTo(rect.right - 2 * rightTopRadius, rect.top);
        path.arcTo(new RectF(rect.right - rightTopRadius * 2, rect.top, rect.right,
                rightTopRadius * 2 + rect.top), 270, 90);
        path.lineTo(rect.right, rect.bottom - 2 * rightBottomRadius);
        path.arcTo(new RectF(rect.right - rightBottomRadius * 2, rect.bottom - rightBottomRadius * 2,
                rect.right, rect.bottom), 0, 90);
        path.lineTo(rect.left + mArrowWidth + 2 * leftBottomRadius, rect.bottom);
        RectF rectF = new RectF(rect.left + mArrowWidth, rect.bottom - leftBottomRadius * 2,
                leftBottomRadius * 2 + rect.left + mArrowWidth, rect.bottom);
        path.arcTo(rectF, 90, 90);

        path.lineTo(rect.left + mArrowWidth, mArrowTop + mArrowHeight + ARROW_RADIUS / 2f * 3);
        path.quadTo(rect.left + mArrowWidth / 3f * 2 + ARROW_RADIUS / 2f, mArrowTop + mArrowHeight / 3f * 2,
                rect.left + ARROW_RADIUS / 2f, mArrowTop - mArrowOffset + ARROW_RADIUS * 2f);
        RectF rectR = new RectF(rect.left, (float)mArrowTop - mArrowOffset,
                rect.left + ARROW_RADIUS * 2f, mArrowTop - mArrowOffset + ARROW_RADIUS * 2f);
        path.arcTo(rectR, 100, 150);
        path.lineTo(rect.left + mArrowWidth, mArrowTop);

        path.lineTo(rect.left + mArrowWidth, rect.top + 2 * leftTopRadius);
        path.arcTo(new RectF(rect.left + mArrowWidth, rect.top, leftTopRadius * 2 + rect.left + mArrowWidth,
                leftTopRadius * 2 + rect.top), 180, 90);
        path.close();
    }

    private void drawRadius(RectF rect, Path path) {
        path.reset();
        float[] radii = new float[]{
                leftTopRadius, leftTopRadius,
                rightTopRadius, rightTopRadius,
                rightBottomRadius, rightBottomRadius,
                leftBottomRadius, leftBottomRadius};
        path.addRoundRect(rect, radii, Path.Direction.CCW);
        path.close();
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldW, int oldH) {
        super.onSizeChanged(w, h, oldW, oldH);
        setUp();
    }

    @Override
    public void setImageBitmap(Bitmap bm) {
        super.setImageBitmap(bm);
        mSrcBitmap = bm;
        setUp();
    }

    @Override
    public void setImageDrawable(Drawable drawable) {
        super.setImageDrawable(drawable);
        mSrcBitmap = getBitmapFromDrawable(drawable);
        setUp();
    }

    @Override
    public void setImageResource(int resId) {
        super.setImageResource(resId);
        mSrcBitmap = getBitmapFromDrawable(getDrawable());
        setUp();
    }

    private Bitmap getBitmapFromDrawable(Drawable drawable) {
        if (drawable == null) {
            return null;
        }
        if (drawable instanceof BitmapDrawable) {
            return ((BitmapDrawable) drawable).getBitmap();
        }
        try {
            Bitmap bitmap;
            if (drawable instanceof ColorDrawable) {
                bitmap = Bitmap.createBitmap(COLORDRAWABLE_DIMENSION, COLORDRAWABLE_DIMENSION, BITMAP_CONFIG);
            } else {
                bitmap = Bitmap.createBitmap(drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight(), BITMAP_CONFIG);
            }
            Canvas canvas = new Canvas(bitmap);
            drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
            drawable.draw(canvas);
            return bitmap;
        } catch (OutOfMemoryError e) {
            return null;
        }
    }

    private void setUp() {
        if (mSrcBitmap == null) {
            return;
        }

        mBitmapShader = new BitmapShader(mSrcBitmap, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP);

        mBitmapPaint = new Paint();
        mBitmapPaint.setAntiAlias(true);
        mBitmapPaint.setShader(mBitmapShader);

        mShapePaint = new Paint();
        mShapePaint.setStyle(Paint.Style.STROKE);
        mShapePaint.setAntiAlias(true);
        mShapePaint.setColor(getResources().getColor(R.color.blackwith10tran));
        mShapePaint.setStrokeWidth(offset);

        mBitmapHeight = mSrcBitmap.getHeight();
        mBitmapWidth = mSrcBitmap.getWidth();

        updateShaderMatrix();
        invalidate();
    }

    private void updateShaderMatrix() {
        float scale;
        float dx = 0;
        float dy = 0;

        mShaderMatrix = new Matrix();
        mShaderMatrix.set(null);

        mDrawableRect = new Rect(0, 0, getRight() - getLeft(), getBottom() - getTop());

        if (mBitmapWidth * mDrawableRect.height() > mDrawableRect.width() * mBitmapHeight) {
            scale = mDrawableRect.height() / (float) mBitmapHeight;
            dx = (mDrawableRect.width() - mBitmapWidth * scale) * 0.5f;
        } else {
            scale = mDrawableRect.width() / (float) mBitmapWidth;
            dy = (mDrawableRect.height() - mBitmapHeight * scale) * 0.5f;
        }

        mShaderMatrix.setScale(scale, scale);
        mShaderMatrix.postTranslate((int) (dx + 0.5f), (int) (dy + 0.5f));

        mBitmapShader.setLocalMatrix(mShaderMatrix);
    }

    private int dp2px(int dp) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, getContext().getResources().getDisplayMetrics());
    }

    public void setBaseOnSize(int baseOnWidth, int baseOnHeight) {
        this.baseOnWidth = baseOnWidth;
        this.baseOnHeight = baseOnHeight;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        if (baseOnHeight != -1 && baseOnWidth != -1) {
            int defW = getDefaultSize(getSuggestedMinimumWidth(), widthMeasureSpec);
            setMeasuredDimension(getDefaultSize(getSuggestedMinimumWidth(), widthMeasureSpec),
                    (int) (baseOnHeight * 1.0 / baseOnWidth * defW));
        } else {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        }

    }

}