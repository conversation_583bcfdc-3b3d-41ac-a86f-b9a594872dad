package com.immomo.momo.message.http;

import com.cosmos.mdlog.MDLog;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoKit;
import com.immomo.momo.aichat.AIChatManager;
import com.immomo.momo.message.bean.SpamSessionList;
import com.immomo.momo.message.bean.UserEntryChatBean;
import com.immomo.momo.message.business.textchat.CheckTextChatFlagTask;
import com.immomo.momo.permission.PermissionChecker;
import com.immomo.momo.protocol.http.core.HttpClient;
import com.immomo.momo.util.GsonUtils;

import org.json.JSONObject;

import java.util.HashMap;


public class SpamSessionApi extends HttpClient {

    public static SpamSessionApi getInstance() {
        return HiSessionApiHolder.instance;
    }

    private static class HiSessionApiHolder {
        public static SpamSessionApi instance = new SpamSessionApi();
    }

    public SpamSessionList getHiSessionIdsFromApi(String remoteids) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("remoteids", remoteids);
        String url = HttpsHost + "/v4/relation/message/user";
        String result = doPost(url, params);
        JSONObject jsonObject = new JSONObject(result);
        JSONObject data = jsonObject.optJSONObject("data");
        return GsonUtils.g().fromJson(data.toString(), SpamSessionList.class);
    }

    public UserEntryChatBean getSpamStatus(String remoteid, String chatScene,Boolean isPeek, String chatHistory) {
        HashMap<String, String> params = new HashMap<>();
        params.put("remoteId", remoteid);
        params.put("chatScene", chatScene);
        params.put("isPeek", isPeek ? "1" : "0");
        params.put("chatHistory", chatHistory);
        if (CheckTextChatFlagTask.Companion.isSessionTextChatFold(remoteid)) {
            params.put("isTextChat", "1");
        }
        // 新增经纬度
        if (MomoKit.getCurrentUser() != null && AIChatManager.Companion.isHasLocationPermission()){
            double[] location = MomoKit.getCurrentUserLocation();
            params.put("lng", location[0] + "");
            params.put("lat", location[1] + "");
        }
        String url = HttpsHost + "/v4/relation/user/entryChat";
        try {
            String result = doPost(url, params);
            JSONObject jsonObject = new JSONObject(result);
            JSONObject data = jsonObject.optJSONObject("data");
            return GsonUtils.g().fromJson(data.toString(), UserEntryChatBean.class);
        } catch (Exception e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
        return null;
    }

}
