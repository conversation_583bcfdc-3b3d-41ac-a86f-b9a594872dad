package com.immomo.momo.message.sayhi.stack;

import com.immomo.momo.android.view.MEmoteTextView;
import com.immomo.momo.likematch.slidestack.BaseSlideStackView;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayhiCardGuide;

import androidx.recyclerview.widget.RecyclerView;

/**
 * Created by lei.jialin on 2019/4/13.
 */
public interface ISayHiCardSwitchLisener extends BaseSlideStackView.CardSwitchListener {

    void toggleInputLayout(boolean show, int indexInStack, MEmoteTextView editeText);

    void onContentScrolled(RecyclerView recyclerView, int dx, int dy);

    boolean canShowTips(SayhiCardGuide profileClick);

    void setCardCanMove(boolean noMove);

    void onLoadDetail(boolean isSuccess, int indexInStack, SayHiInfo sayHiInfo);
}
