package com.immomo.momo.message.task;

import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.message.activity.GroupChatActivity;
import com.immomo.momo.message.contract.IMsgChatDataHolder;
import com.immomo.momo.message.contract.IMsgChatRecycler;
import com.immomo.momo.message.presenter.GroupChatCommonPresenter;
import com.immomo.momo.service.bean.Message;

import java.util.ArrayList;
import java.util.List;

import static com.immomo.momo.mvp.message.view.BaseMessageActivity.PAGE_SIZE;



/**
 * 获取所有未读消息的异步任务
 * <AUTHOR>
 * date 2020/8/8
 */
public class LoadAllUnreadMessageTask extends MomoTaskExecutor.Task<Object, Object, List<Message>> {

    private GroupChatActivity mActivity;
    private GroupChatCommonPresenter chatCommonPresenter;
    private IMsgChatDataHolder mMsgChatData;
    private IMsgChatRecycler mMsgChatRecycler;
    /**
     * 表示unreadCount%PAGE_SIZE的结果值
     */
    private int remainder;


    public LoadAllUnreadMessageTask(GroupChatActivity mActivity, GroupChatCommonPresenter chatCommonPresenter,
                                    IMsgChatDataHolder msgChatData, IMsgChatRecycler msgChatRecycler, Object... objects) {
        super(objects);
        this.mActivity = mActivity;
        this.chatCommonPresenter = chatCommonPresenter;
        this.mMsgChatData = msgChatData;
        this.mMsgChatRecycler = msgChatRecycler;
    }

    @Override
    protected List<Message> executeTask(Object... params) {
        if (mActivity.getUnReadScrollCount() <= 0) {
            mActivity.setUnReadScrollCount(0);
        }
        if (mActivity.getUnReadScrollCount() == 0 && mActivity.getTotalUnreadCount() < PAGE_SIZE) {
            //说明未读消息不超过20条，这时不需要数据加载，只要滚到 PAGE_SIZE - remainder 的位置
            remainder = mActivity.getTotalUnreadCount();
            return new ArrayList<>();
        }
        //防止未读消息数几万，最多load1000条
        if (mActivity.getUnReadScrollCount() > 1000) {
            mActivity.setUnReadScrollCount(1000);
        }
        List<Message> messages = mActivity.loadMoreMessages(mActivity.getUnReadScrollCount(), true, false);
        return chatCommonPresenter.distinctMessage(messages, mMsgChatData);
    }

    @Override
    protected void onTaskSuccess(List<Message> messages) {
        if (null != messages && messages.size() > 0) {
            mMsgChatData.addItemModels(0, messages);
        }
        int headerCount = mMsgChatData.getHeaderViewsCount();
        int pos = remainder == 0 ? 0 : PAGE_SIZE - remainder + headerCount;
        mMsgChatRecycler.smoothScrollToPosition(pos);
    }
}