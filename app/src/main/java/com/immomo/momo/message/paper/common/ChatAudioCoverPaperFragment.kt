package com.immomo.momo.message.paper.common

import android.view.View
import android.view.ViewStub
import com.immomo.momo.R
import com.immomo.momo.eventbus.DataEvent
import com.immomo.momo.message.paper.BasePaperFragment
import com.immomo.momo.message.paper.event.PaperEvent
import com.immomo.momo.mvp.message.view.BaseMessageActivity

/**
 * 音频播放提示
 * <AUTHOR>
 * @data 2020-12-18.
 */

class ChatAudioCoverPaperFragment : BasePaperFragment() {

    private var coverLayout: View? = null
    private var mContentView: View? = null

    companion object {
        fun newInstance(): ChatAudioCoverPaperFragment {
            return ChatAudioCoverPaperFragment()
        }
    }

    override fun getContainerId(): Int = R.id.chat_audio_cover_paper_container

    override fun getPageLayout(): Int = R.layout.paper_chat_audio_cover

    override fun initPageViews(contentView: View?) {
        mContentView = contentView

    }

    override fun onPageLoad() {

    }

    private fun initCoverLayout(visibility: Int) {
        if (null == coverLayout) {
            mContentView?.let {
                val viewStub = it.findViewById(R.id.viewstub_audio_coverlayout) as ViewStub
                coverLayout = viewStub.inflate()
                // 语音覆盖层不可点击
                coverLayout?.setOnTouchListener(View.OnTouchListener { v, event -> true })
            }
        }
        coverLayout?.visibility = visibility
    }

    private fun hideAudioCover() {
        if (null != coverLayout) {
            coverLayout?.visibility = View.GONE
        }
    }

    override fun onEvent(event: DataEvent<Any>) {
        super.onEvent(event)
        when (event.action) {
            PaperEvent.PAPER_EVENT_SHOW_AUDIO_COVER -> {
                initCoverLayout(View.VISIBLE)
            }
            PaperEvent.PAPER_EVENT_HIDE_AUDIO_COVER -> {
                hideAudioCover()
            }
        }
    }


    override fun onDestroy() {
        super.onDestroy()
    }

    fun getBaseActivity(): BaseMessageActivity? {
        (activity as? BaseMessageActivity)?.let {
            return it
        }
        return null
    }

}