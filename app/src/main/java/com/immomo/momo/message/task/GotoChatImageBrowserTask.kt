package com.immomo.momo.message.task

import android.text.TextUtils
import android.view.View
import com.cosmos.mdlog.MDLog
import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.mmutil.toast.Toaster
import com.immomo.momo.R
import com.immomo.momo.imagefactory.imageborwser.BusinessType
import com.immomo.momo.imagefactory.imageborwser.ImageBrowserConfig
import com.immomo.momo.message.chatmsg.helper.MsgAdaptHelper
import com.immomo.momo.message.chatmsg.itemmodel.child.BaseImageChildItemModel
import com.immomo.momo.message.chatmsg.itemmodelbinder.IMessagePageProvider
import com.immomo.momo.message.helper.MessageForwardUtils
import com.immomo.momo.mulog.MULogConstants
import com.immomo.momo.mulog.MURealtimeLog
import com.immomo.momo.mulog.pair.MUPairItem
import com.immomo.momo.service.bean.Message
import com.immomo.momo.util.DataUtil
import com.immomo.momo.util.ViewUtil

class GotoChatImageBrowserTask(
    private val mView: View,
    val mMessage: Message,
    val provider: IMessagePageProvider
) : MomoTaskExecutor.Task<String?, Void, List<Message>>("") {
    private val imageFileNames: MutableList<String> = ArrayList()
    private val originImageSizes: MutableList<Long> = ArrayList()

    /**
     * 图片是否为长图
     */
    private val isLongImages: MutableList<Boolean> = ArrayList()
    private val businessTypes: MutableList<BusinessType?> = ArrayList()

    private val messageIds: MutableList<String> = ArrayList()
    private var currentImageId: String? = null
    private val imageVisibleMessages: MutableList<Message> = ArrayList()
    override fun executeTask(vararg params: String?): List<Message> {
        //查DB的耗时操作
        return provider.getImageMessages() ?: listOf()
    }

    override fun onTaskSuccess(result: List<Message>?) {
        super.onTaskSuccess(result)
        dealWithImageMessages(result)
        gotoChatImageBrowser(mView)
    }

    override fun onTaskError(e: java.lang.Exception?) {
        Toaster.show("查看图片失败")
    }

    private fun dealWithImageMessages(imageMessages: List<Message?>?) {
        val size = imageMessages?.size ?: return
        currentImageId = DataUtil.parseFileName(mMessage)
        //非色情图片需要过滤未查看的模糊色情图片
        val filterOutPorn: Boolean = !mMessage.isBlurPorn
        for (i in 0 until size) {
            val msg = imageMessages[i] ?: continue
            val imageFileName = DataUtil.parseFileName(msg)
            MDLog.d(
                BaseImageChildItemModel::class.java.simpleName,
                "message:" + i + "\t" + imageMessages[i].hashCode() +
                        "\t" + imageMessages[i]
            )
            //线上观测日志
            if (TextUtils.isEmpty(imageFileName)) {
                MURealtimeLog.business(MULogConstants.BUSINESS_MOMO_BASIC)
                    .secondLBusiness("dbglog")
                    .thirdLBusiness("debug")
                    .addBodyItem(MUPairItem.category("msg-refact-imageTask"))
                    .addBodyItem(
                        MUPairItem.msg(
                            StringBuilder("msgId:")
                                .append(msg.msgId)
                                .append("  filename:").append(msg.fileName).toString()
                        )
                    )
                    .commit()

                continue
            }
            val isCurrentImage = TextUtils.equals(imageFileName, currentImageId)

            // 点击一张正常图片，携带所有聊天图片进入大图浏览页时，需要剔除未查看的模糊色情图片
            // 仅点击一张未查看的色情图片可以查看所有图片
            if (!isCurrentImage && filterOutPorn && msg.isBlurPorn) {
                continue
            }
            if (msg.chatType != Message.CHATTYPE_LETS_CHAT && msg.timestampMillis < System.currentTimeMillis() - Message.resourceExpireTime) {
                continue
            }
            if (msg.isSnapMsg) {
                continue
            }
            imageFileNames.add(imageFileName)

            //记录原图的大小，如果不是原图，则是－1
            originImageSizes.add(if (msg.isOriginImg) msg.originImgSize else -1)
            //记录图片是否为长图
            isLongImages.add(msg.imageType == Message.MSG_IMAGE_LONG)
            //记录图片是否实拍时刻
            businessTypes.add(MsgAdaptHelper.trans(msg))
            messageIds.add(msg.msgId)
            imageVisibleMessages.add(msg)
        }
    }

    /**
     * 跳转图片浏览页面
     *
     * @param view
     */
    private fun gotoChatImageBrowser(view: View) {
        val browserImageType: String
        val chatId: String
        when (mMessage.chatType) {
            Message.CHATTYPE_GROUP -> {
                browserImageType = ImageBrowserConfig.Type.TYPE_GCHAT
                chatId = mMessage.groupId
            }

            Message.CHATTYPE_DISCUSS -> {
                browserImageType = ImageBrowserConfig.Type.TYPE_DCHAT
                chatId = mMessage.discussId
            }

            Message.CHATTYPE_USER -> {
                browserImageType = ImageBrowserConfig.Type.TYPE_CHAT
                chatId = mMessage.remoteId
            }

            else -> {
                browserImageType = ImageBrowserConfig.Type.TYPE_CHAT
                chatId = mMessage.remoteId
            }
        }
        val imageBounds = ViewUtil.getImageBounds(view.findViewById(R.id.message_iv_msgimage))
        provider.getMessageActivity()?.get()?.let {
            if (mMessage.isSnapMsg) {
                MessageForwardUtils.gotoSnapImageBrowser(
                    it,
                    chatId,
                    mMessage,
                    browserImageType,
                    imageBounds,
                    false
                )
            } else {
                imageVisibleMessages.let { it1 ->
                    MessageForwardUtils.gotoChatImageBrowser(
                        it,
                        chatId,
                        it1,
                        mMessage,
                        imageFileNames,
                        messageIds,
                        isLongImages,
                        businessTypes,
                        originImageSizes,
                        browserImageType,
                        imageBounds,
                        false
                    )
                }
            }
        }
    }

}