package com.immomo.momo.message.paper.event

import com.immomo.momo.gift.base.ContinuityGiftPlayBean
import com.immomo.momo.service.bean.Message

/**
 * <AUTHOR>
 * @data 2020-12-21.
 */

object PaperEvent {
    const val PAPER_EVENT_BACKGROUND_TASK_SUCCESS = "paper_event_background_task_success"

    const val PAPER_EVENT_LOAD_BACKGROUND = "paper_event_load_background"

    const val PAPER_EVENT_ADD_REMOTE_MESSAGE_LIST = "paper_event_add_remote_message_list"

    const val PAPER_EVENT_PULL_MESSAGE_IN_WINDOW = "paper_event_pull_message_in_window"

    const val PAPER_EVENT_INIT_MESSAGE_LIST = "paper_event_init_message_list"

    const val PAPER_EVENT_SHOW_AUDIO_COVER = "paper_event_show_audio_cover"

    const val PAPER_EVENT_HIDE_AUDIO_COVER = "paper_event_hide_audio_cover"

    const val PAPER_EVENT_PLAY_GIFT_BY_MSG = "paper_event_play_gift_by_msg"

    const val PAPER_EVENT_PLAY_GIFT_BROADCAST = "paper_event_play_gift_broadcast"

    const val PAPER_EVENT_PLAY_FLASH_VOICE_GIFT_BY_MSG = "paper_event_play_flash_voice_gift_by_msg"

    const val PAPER_EVENT_PLAY_GIFT = "paper_event_play_gift"

    const val PAPER_EVENT_GIFT_CHECKER_ADD = "paper_event_gift_checker_add"

    const val PAPER_EVENT_GREET_SHOW_GIFT_PANEL = "paper_event_greet_show_gift_panel"

    const val PAPER_EVENT_GREET_HIDE_GIFT_PANEL = "paper_event_greet_hide_gift_panel"

    const val PAPER_EVENT_LOVE_APARTMENT_REFRESH = "paper_event_love_apartment_refresh"

    const val PAPER_EVENT_SHOW_TOP_BAR = "paper_event_show_top_bar"

    const val PAPER_EVENT_HIDE_TOP_BAR = "paper_event_hide_top_bar"

    const val PAPER_EVENT_CHECK_TOP_BRA_NOTICE = "paper_event_check_top_bar_notice"

    /**
     * 隐藏顶部提示栏
     */
    const val PAPER_EVENT_HIDE_TOP_BRA_NOTICE = "paper_event_hide_top_bar_notice"

    const val PAPER_EVENT_TOP_BRA_NOTICE_LAYOUT_VISIBLE =
        "paper_event_top_bar_notice_layout_visible"

    const val PAPER_EVENT_HIDE_NEW_MESSAGE_MASK = "paper_event_hide_new_message_mask"

    const val PAPER_EVENT_SHOW_NEW_MESSAGE_MASK = "paper_event_show_new_message_mask"

    const val PAPER_EVENT_SHOW_GREET_GIFT_BTN = "paper_event_show_greet_gift_btn"

    const val PAPER_EVENT_HIDE_GREET_GIFT_BTN = "paper_event_hide_greet_gift_btn"

    const val PAPER_EVENT_HIDE_GREET_GIFT_BTN_TIP = "paper_event_hide_greet_gift_btn_tip"

    const val PAPER_EVENT_CHANGE_FULL_MODE = "paper_event_change_full_mode"

    const val PAPER_EVENT_HIDE_CHAT_FORWARD = "paper_event_hide_chat_forward"

    const val PAPER_EVENT_HIDE_CHAT_WEB = "paper_event_hide_chat_web"

}


data class BackgroundInfo(val chatType: Int, val chatId: String?, val resId: String?)


data class MessageInitData(val message: List<Message>?, val unreadCount: Int)


data class PlayGiftData(val giftPlayBean: ContinuityGiftPlayBean?, val level: Int)


