package com.immomo.momo.message.sayhi.stack;

import static androidx.recyclerview.widget.RecyclerView.SCROLL_STATE_IDLE;
import static com.immomo.framework.storage.preference.SPKeys.User.SayHi.KEY_HAS_SHOW_MALE_REDPACKET_TIP;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Outline;
import android.text.Editable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.cosmos.mdlog.MDLog;
import com.immomo.framework.base.BaseActivity;
import com.immomo.framework.cement.CementModel;
import com.immomo.framework.cement.SimpleCementAdapter;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.framework.utils.UIUtils;
import com.immomo.framework.view.recyclerview.LoadMoreRecyclerView;
import com.immomo.mls.fun.other.Size;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.LogTag;
import com.immomo.momo.R;
import com.immomo.momo.android.view.MEmoteTextView;
import com.immomo.momo.android.view.tips.TipManager;
import com.immomo.momo.android.view.tips.tip.ITip;
import com.immomo.momo.android.view.tips.tip.OnTipHideListener;
import com.immomo.momo.likematch.slidestack.BaseSlideCard;
import com.immomo.momo.likematch.slidestack.BaseSlideStackView;
import com.immomo.momo.likematch.tools.TipsUtil;
import com.immomo.momo.message.sayhi.activity.HiCardStackActivity;
import com.immomo.momo.message.sayhi.contract.ISayhiCardDataProvider;
import com.immomo.momo.message.sayhi.itemmodel.RedPacketSayhiItemModel;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayhiCardGuide;
import com.immomo.momo.message.sayhi.itemmodel.message.BaseMsgItemModel;
import com.immomo.momo.message.sayhi.itemmodel.message.ImageMsgItemModel;
import com.immomo.momo.message.sayhi.presenter.ISayhiMessageContract;
import com.immomo.momo.message.sayhi.presenter.SayhiMessagePresenter;
import com.immomo.momo.message.sayhi.utils.SayHiViewShadowHelper;
import com.immomo.momo.message.sayhi.utils.SayHiViewShadowLargeHelper;
import com.immomo.momo.message.sayhi.widget.guideclick.GuideScrollDownView;
import com.immomo.momo.message.sayhi.widget.guideclick.OnGuideClickViewCallback;
import com.immomo.momo.service.bean.Message;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.statistics.logrecord.viewhelper.RecyclerViewExposureLogHelper;
import com.immomo.momo.util.MomoKit;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

import cn.dreamtobe.kpswitch.util.KeyboardUtil;
import cn.dreamtobe.kpswitch.util.StatusBarHeightUtil;


/**
 * 招呼卡片
 */
public class SayHiSlideCard extends BaseSlideCard<SayHiInfo> implements ISayhiMessageContract.IView {

    private View slideLinearLayout;
    private MEmoteTextView tvSlideHint;
    private ImageView likeIcon;
    private ImageView dislikeIcon;
    private ISayHiCardSwitchLisener cardSwitchListener;
    private View viewTriangle;
    private LoadMoreRecyclerView contentRV;
    private SayhiMessagePresenter presetner;
    private ISayhiCardDataProvider msgDataProvider;
    RecyclerView.OnScrollListener scrollListener;
    private View scrollBar;
    private View containerOfScrollBar;
    private GuideScrollDownView guideScrollDownView;
    private boolean hasShow;
    private FrameLayout.LayoutParams slideLayoutParams;

    public SayHiSlideCard(@NonNull Context context) {
        this(context, null);
    }

    public SayHiSlideCard(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SayHiSlideCard(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.say_hi_slide_card;
    }

    @Override
    protected boolean isLazyLoad() {
        return false;
    }

    @Override
    protected void init() {
        initView();
        initEvents();
    }

    private void initView() {
        viewTriangle = findViewById(R.id.triangle_wihite);
        tvSlideHint = (MEmoteTextView) findViewById(R.id.tv_slide_hint);
        likeIcon = (ImageView) findViewById(R.id.ic_like_in_card);
        dislikeIcon = (ImageView) findViewById(R.id.ic_dislike_in_card);

        slideLinearLayout = findViewById(R.id.slidecard_layout);
        slideLayoutParams = (FrameLayout.LayoutParams) slideLinearLayout.getLayoutParams();
        if (slideLayoutParams != null) {
            slideLayoutParams.bottomMargin = UIUtils.getPixels(22);
        }
        slideLinearLayout.requestLayout();

        scrollBar = findViewById(R.id.scroll_bar);
        containerOfScrollBar = findViewById(R.id.layout_scroll_bar);

        initContentRV();
        initCorner();
    }

    public void setCardTipSpace(boolean hasCardTopTip) {
        if (hasCardTopTip) {
            if (slideLayoutParams != null) {
                slideLayoutParams.topMargin = UIUtils.getPixels(150);
            }
        }
    }

    public void setLikeImageIcon(int resource) {
        likeIcon.setImageResource(resource);
    }

    private void initCorner() {
        SayHiViewShadowHelper viewShadowHelper = new SayHiViewShadowHelper();
        viewShadowHelper = new SayHiViewShadowLargeHelper();
        viewShadowHelper.setShadowData(0, new Size(-UIUtils.getPixels(3), -UIUtils.getPixels(3)),
                UIUtils.getPixels(15), 0.2f);
        viewShadowHelper.setOutlineProvider(slideLinearLayout);
        contentRV.setOutlineProvider(new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                outline.setRoundRect(0, 0, view.getWidth(), view.getHeight(), UIUtils.getDimensionPixelSize(R.dimen.sayhi_card_radius));
            }
        });
        contentRV.setClipToOutline(true);
    }

    private void initContentRV() {
        contentRV = findViewById(R.id.rv_content);
        contentRV.requestLayout();
        scrollListener = RecyclerViewExposureLogHelper.getOnScrollListener();
        contentRV.addOnScrollListener(scrollListener);
        contentRV.setLayoutManager(new LinearLayoutManager(getContext()));
        contentRV.setItemAnimator(null);
        contentRV.setOnLoadMoreListener(() -> {
            if (presetner != null) {
                presetner.requestLoadMore();
            }
        });
        //contentRV.addItemDecoration(new FadeItemDecoration(0));
        contentRV.addOnScrollListener(new RecyclerView.OnScrollListener() {

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    changeScrollBar(recyclerView);
                }
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                if (cardSwitchListener != null) {
                    cardSwitchListener.onContentScrolled(recyclerView, dx, dy);
                }
                changeScrollBar(recyclerView);
            }
        });
    }

    private void changeScrollBar(RecyclerView recyclerView) {
        //整体的总宽度，注意是整体，包括在显示区域之外的。
        int range = recyclerView.computeVerticalScrollRange();
        //显示区域的高度。
        int extent = recyclerView.computeVerticalScrollExtent();
        //已经向下滚动的距离，为0时表示已处于顶部。
        int offset = recyclerView.computeVerticalScrollOffset();

        if (range == extent) {
            showScrollBar(false);
            return;
        }
        showScrollBar(true);
        //计算比例
        float proportion = (offset) * 1.0f / (range - extent);
        proportion = Math.max(0f, Math.min(proportion, 1f));

        //计算滚动条宽度
        int transMaxRange = ((ViewGroup) scrollBar.getParent()).getHeight() - scrollBar.getHeight();
        //设置滚动条移动
        scrollBar.setTranslationY(transMaxRange * proportion);
    }

    private void showScrollBar(boolean show) {
        containerOfScrollBar.setVisibility(show ? View.VISIBLE : View.GONE);
        if (presetner != null) {
            presetner.showScrollBar(show);
        }
        if (show) {
            containerOfScrollBar.setBackgroundResource(R.drawable.bg_3dp_corner_26000000_black);
        }
    }


    private void initEvents() {
        presetner = new SayhiMessagePresenter();
        presetner.bindView(this);
    }

    @Override
    public void onClick(View view) {
    }
    //</editor-fold>

    //<editor-fold desc="fill info">

    /**
     * 目前2个地方用到点点SayHiCard：1.匹配页卡片栈 2.点点完善资料引导弹窗
     * 显示上的区别在于：在匹配页卡片上所有图像可点，点击进入大图浏览；引导弹窗中所有头像不可点
     *
     * @param info
     */
    @Override
    public void fillData(final SayHiInfo info, final int index,
                         final BaseSlideStackView.CardSwitchListener listener) {
        if (info == null) {
            return;
        }

        if (listener instanceof ISayHiCardSwitchLisener) {
            setCardSwitchListener((ISayHiCardSwitchLisener) listener);
        }
        setSlideHint(info.mLikeMsg);

        if (presetner != null) {
            presetner.setSayhiInfo(info);
            presetner.transData(index, cardSwitchListener);
        }
        contentRV.scrollToPosition(0);
    }

    public void setCardSwitchListener(ISayHiCardSwitchLisener cardSwitchListener) {
        this.cardSwitchListener = cardSwitchListener;
    }

    public void setSlideHint(String slideHint) {
        checkInflate();
        if (tvSlideHint == null) {
            return;
        }
        tvSlideHint.setText(String.valueOf(slideHint));
        setSlideHintVis(!TextUtils.isEmpty(slideHint));
    }
    //</editor-fold>

    //<editor-fold desc="卡片恢复" >
    @Override
    public void endAnim() {
        if (presetner != null) {
            presetner.setCanLog(false);
            presetner.setCanAnim(false);
            presetner.animInsertInputTips(false);
            presetner.destroy();
        }
    }

    @Override
    public void resetViewsOnCard(BaseSlideCard changedView) {
        dislikeIcon.setAlpha(0f);
        likeIcon.setAlpha(0f);
        tvSlideHint.setAlpha(0f);
        viewTriangle.setAlpha(0f);
    }
    //</editor-fold   >

    //<editor-fold desc="卡片附加动画">
    @Override
    public void scaleLikeIconWhileSlide(float percentage) {
        float scale = 1 + 0.2f * Math.abs(percentage);
        if (percentage >= 0) {
            likeIcon.setAlpha(percentage);
            likeIcon.setScaleX(scale);
            likeIcon.setScaleY(scale);
            tvSlideHint.setAlpha(percentage);
            viewTriangle.setAlpha(percentage);
        }
        if (percentage <= 0) {
            dislikeIcon.setAlpha(Math.abs(percentage));
            dislikeIcon.setScaleX(scale);
            dislikeIcon.setScaleY(scale);
        }
    }
    //</editor-fold >

    @Override
    public void onStop() {
        if (presetner != null) {
            presetner.onPause();
        }
    }

    @Override
    public void onResume() {
        if (presetner != null) {
            presetner.onResume();
        }
    }

    @Override
    public void onDestroy() {
        MomoMainThreadExecutor.cancelAllRunnables(getTaskTag());
        MomoTaskExecutor.cancleAllTasksByTag(getTaskTag());
        destroy();
        Activity activity = MomoKit.INSTANCE.getActivityWithView(this);
        if (activity instanceof BaseActivity) {
            ((BaseActivity) activity).closeDialog();
        }
    }

    public void setSlideHintVis(boolean vis) {
        if (tvSlideHint != null) {
            tvSlideHint.setVisibility(vis ? VISIBLE : GONE);
        }
        if (viewTriangle != null) {
            viewTriangle.setVisibility(vis ? VISIBLE : GONE);
        }
    }

    // 获取曾看到的最大深度
    @Override
    public int getPictureDepth() {
        return 0;
    }

    public void setfakeInputTextOnCard(Editable s) {
        if (presetner != null) {
            presetner.setTextForInput(s.toString());
        }
    }

    public void logExposure(String source) {
        if (presetner != null) {
            presetner.logSayhiInfoShow(source);
        }
        if (!isFirstCard()) logHeadVisiblePart();
    }

    public void playAnimOnCard() {
        if (presetner != null) {
            presetner.setCanAnim(true);
            presetner.setCanLog(true);
        }
        checkGuideAnim();
    }

    private void checkGuideAnim() {
        showMaleRedPaketNotice();
        // (不是第一张 || 本次没有播放enter动画)
        boolean noEnterGuide = presetner != null && presetner.getIndexInStack() > 0
                || cardSwitchListener != null && cardSwitchListener.canShowTips(SayhiCardGuide.ScrollDown);
        // 本次进来如果有播放过enterGuide，则不再播放以下的引导
        if (!noEnterGuide) {
            return;
        }
        if (canScrollDown() && !KV.getUserBool(SPKeys.User.SayHi.KEY_SAYHI_CARD_SCROLL_DOWN_GUIDE, false)) {
            //  判断可否向下滑动
            KV.saveUserValue(SPKeys.User.SayHi.KEY_SAYHI_CARD_SCROLL_DOWN_GUIDE, true);
            showScrollDownGuide();
        }
        if (presetner != null && KV.getUserBool(SPKeys.User.SayHi.KEY_SAY_HI_STACK_CARDS_INPUT_TIPS, true)) {
            // 输入框Tips和scrollDown引导互斥
            KV.saveUserValue(SPKeys.User.SayHi.KEY_SAY_HI_STACK_CARDS_INPUT_TIPS, false);
            presetner.animInsertInputTips(true);
        }
    }

    //<editor-fold desc="向下滑动引导">
    public void showScrollDownGuide() {
        if (guideScrollDownView == null) {
            initGuideScrollDown();
        }
        if (guideScrollDownView != null) {
            guideScrollDownView.autoHide(true).show();
        }
        if (presetner != null) {
            presetner.setMarksFlipperBlock(true);
        }
    }

    public boolean canScrollDown() {
        return contentRV != null && contentRV.canScrollVertically(1);
    }

    private void initGuideScrollDown() {
        guideScrollDownView = new GuideScrollDownView(getContext());
        guideScrollDownView.setTipText("向上滑动\n查看更多资料");
        int[] wh = new int[2];
        wh[0] = slideLinearLayout.getWidth();
        wh[1] = slideLinearLayout.getHeight();

        int[] xy = new int[2];
        slideLinearLayout.getLocationOnScreen(xy);
        guideScrollDownView.setBoundsParams(wh, xy);
        guideScrollDownView.setTargetParams(wh, xy);
        guideScrollDownView.setOnCorrectClickCallback(new OnGuideClickViewCallback() {
            @Override
            public void onClickedGuideView() {
                stopGuideAnim();
            }

            @Override
            public void onClickOutOfRange() {
                stopGuideAnim();
            }

            @Override
            public void onInOutAlphaChange(Float v) {
                // nothing
            }

            @Override
            public void onNotClickGuideRegin() {
                // nothing
            }

        });
        guideScrollDownView.setAnimatorListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                if (presetner != null) {
                    presetner.setMarksFlipperBlock(false);
                }
            }
        });
    }

    private void stopGuideAnim() {
        if (guideScrollDownView != null) {
            guideScrollDownView.hide();
        }
        if (presetner != null) {
            presetner.setMarksFlipperBlock(false);
        }
    }
    //</editor-fold>

    public void showTipsAfterEnterGuide(OnTipHideListener onTipHideListener) {
        View view = findViewById(R.id.profile_tip);
        if (view == null) {
            if (onTipHideListener != null) {
                onTipHideListener.onHide(null);
            }
            return;
        }
        TipsUtil.showTipHideListener(MomoKit.INSTANCE.getActivityWithView(this)
                , view, UIUtils.getString(R.string.sayhi_stack_cards_profile_click_tips),
                0, 0, ITip.Triangle.TOP, 3000L,
                onTipHideListener);
    }

    public void showPrivacyTipsAfterEnterGuide(OnTipHideListener onTipHideListener) {
        View view = findViewById(R.id.btn_privacy);
        if (view == null) {
            if (onTipHideListener != null) {
                onTipHideListener.onHide(null);
            }
            return;
        }
        TipsUtil.showTipHideListener(MomoKit.INSTANCE.getActivityWithView(this)
                , view, UIUtils.getString(R.string.sayhi_stack_cards_privacy_tips),
                0, 0, ITip.Triangle.TOP, 5000L,
                onTipHideListener);
    }


    //<editor-fold desc="刷新卡片内容"
    @Override
    public void showLoadMoreStart() {
        contentRV.setLoadMoreStart();
    }

    @Override
    public void showLoadMoreComplete() {
        contentRV.setLoadMoreComplete();
        logRedEnvelopShow();
    }

    @Override
    public void showLoadMoreFailed() {
        contentRV.setLoadMoreFailed();
        logRedEnvelopShow();
    }

    @Override
    public void showHasMore(boolean hasMore) {

    }

    @Override
    public void setAdapter(SimpleCementAdapter adapter) {
        contentRV.setAdapter(adapter);
    }

    @Override
    public void showRefreshStart() {

    }

    @Override
    public void showRefreshComplete() {

    }

    @Override
    public void showRefreshFailed() {

    }

    @Override
    public void showEmptyView() {

    }

    @Override
    public Context thisContext() {
        if (isAttachedToWindow()) {
            return MomoKit.INSTANCE.getActivityWithView(SayHiSlideCard.this);
        } else {
            return getContext();
        }
    }

    @Override
    public ISayhiCardDataProvider getSayhiDataProvider() {
        if (msgDataProvider == null) {
            msgDataProvider = new ISayhiCardDataProvider() {

                private List<Message> imageMessages;
                /**
                 * 纪录当前正处于下载状态的消息ID，用于重绘界面时能够恢复之前下载动画
                 */
                private HashSet<String> downloadingMessageIds = new HashSet<>();
                //标记已经下载了图片的消息，如果下载了图片，就直接展示
                private HashSet<String> downloadedImageMsgIds = new HashSet<>();

                @Override
                public User getUser() {
                    return presetner != null ? presetner.getUser() : null;
                }

                @Override
                public Activity getActivity() {
                    return MomoKit.INSTANCE.getActivityWithView(SayHiSlideCard.this);
                }

                @Override
                public void removeModel(CementModel profileModel) {
                    if (presetner != null) {
                        presetner.removeModel(profileModel);
                    }
                }

                @Override
                public ISayHiCardSwitchLisener getCardSwitchListener() {
                    return cardSwitchListener;
                }

                @Override
                public SayHiInfo getSayhiInfo() {
                    return presetner != null ? presetner.getSayHiInfo() : null;
                }

                @Override
                public String getTaskTag() {
                    return SayHiSlideCard.this.getTaskTag();
                }

                @Override
                public void reportUser(String momoid) {
                    Activity activity = getActivity();
                    if (activity instanceof HiCardStackActivity) {
                        ((HiCardStackActivity) activity).reportUser(momoid);
                    }
                }

                @Override
                public List<Message> getImageMessages() {
                    return getImageMessageList();
                }

                @Override
                public void notifyModelChange(CementModel model) {
                    if (presetner != null) {
                        presetner.notifyModelChange(model);
                    }
                }

                @Override
                public void showDialog(Dialog dialog) {
                    Activity activity = getActivity();
                    if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                        return;
                    }
                    if (activity instanceof BaseActivity) {
                        ((BaseActivity) activity).showDialog(dialog);
                    } else {
                        dialog.show();
                    }
                }

                @Override
                public boolean isMsgDownloaded(String msgId) {
                    return downloadedImageMsgIds.contains(msgId);
                }

                @Override
                public boolean isMsgDownloading(String msgId) {
                    return downloadingMessageIds.contains(msgId);
                }

                @Override
                public void onMsgDownloaded(String msgId) {
                    downloadedImageMsgIds.add(msgId);
                    downloadingMessageIds.remove(msgId);
                }

                @Override
                public void onMsgStartDownloding(String msgId) {
                    downloadingMessageIds.add(msgId);
                }

                @Override
                public boolean isSayhi() {
                    return true;
                }

                private List<Message> getImageMessageList() {
                    if (imageMessages == null) {
                        imageMessages = new ArrayList<>();
                        List<CementModel<?>> models = presetner.getAllMsgModels();
                        if (models == null) {
                            return imageMessages;
                        }
                        for (int i = 0; i < models.size(); i++) {
                            CementModel m = models.get(i);
                            boolean isImage = m instanceof ImageMsgItemModel;
                            if (isImage && ((BaseMsgItemModel) m).getMessage() != null) {
                                imageMessages.add(((BaseMsgItemModel) m).getMessage());
                            }
                        }
                    }
                    return imageMessages;
                }
            };
        }
        return msgDataProvider;
    }

    @Override
    public OnClickListener getInputScrollListener() {
        return v -> {
            if (contentRV != null) {
                int[] loc = new int[2];
                v.getLocationOnScreen(loc);
                int totalH = UIUtils.getScreenHeight() + StatusBarHeightUtil.getStatusBarHeight(v.getContext());
                int keyboardY = totalH - KeyboardUtil.getValidPanelHeight(v.getContext());
                contentRV.smoothScrollBy(0, -(keyboardY - loc[1]));
            }
        };
    }

    public void logHeadVisiblePart() {
        if (scrollListener != null) {
            scrollListener.onScrollStateChanged(contentRV, SCROLL_STATE_IDLE);
        }

    }

    @Override
    public String getTaskTag() {
        return String.valueOf(hashCode());
    }

    @Override
    public void destroy() {
        if (presetner != null) {
            presetner.destroy();
        }
        setCardSwitchListener(null);
    }

    @Override
    public void updateMessage(String remoteid, String msgId) {
        if (presetner != null) {
            presetner.loadMessageToUpdate(remoteid, msgId);
        }
    }
    //</editor-fold>

    public void setHasShow(boolean hasShow) {
        this.hasShow = hasShow;
        logRedEnvelopShow();
    }

    public void logRedEnvelopShow() {
        if (hasShow && presetner != null) {
            presetner.logRedEnvelopeShow();
        }
    }

    public void showMaleRedPaketNotice() {
        if (KV.getUserBool(KEY_HAS_SHOW_MALE_REDPACKET_TIP, false)) {
            return;
        }
        if (contentRV == null) {
            return;
        }

        try {
            int firstVisibleItemPosition = (contentRV.getLayoutManager() instanceof LinearLayoutManager) ?
                    ((LinearLayoutManager) contentRV.getLayoutManager()).findFirstVisibleItemPosition() : -1;
            int lastVisibleItemPosition = (contentRV.getLayoutManager() instanceof LinearLayoutManager) ?
                    ((LinearLayoutManager) contentRV.getLayoutManager()).findLastVisibleItemPosition() : -1;

            List<CementModel<?>> cementModelList = ((SimpleCementAdapter) contentRV.getAdapter()).getModels();
            if (cementModelList == null || cementModelList.isEmpty()) {
                return;
            }
            if (firstVisibleItemPosition < 0 || lastVisibleItemPosition < 0 || lastVisibleItemPosition >= cementModelList.size()) {
                return;
            }
            for (int i = firstVisibleItemPosition; i <= lastVisibleItemPosition; i++) {
                if (cementModelList.get(i) instanceof RedPacketSayhiItemModel) {
                    View targetItemView = contentRV.getChildAt(i - firstVisibleItemPosition);
                    if (targetItemView.getTag(R.id.tag_sayhi_red_packet_btn) != null) {
                        KV.saveUserValue(KEY_HAS_SHOW_MALE_REDPACKET_TIP, true);
                        showMaleUserRedPacketTip((View) targetItemView.getTag(R.id.tag_sayhi_red_packet_btn));
                    }
                }
            }
        } catch (Exception e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
    }

    private void showMaleUserRedPacketTip(View view) {
        if (view == null) {
            return;
        }
        TipManager.bindActivity(getSayhiDataProvider().getActivity()).checkViewCanShowTip(view,
                v ->
                        TipManager.bindActivity(getSayhiDataProvider().getActivity())
                                .setMarginEdge(UIUtils.getPixels(13f))
                                .setTouchToHideAll(true)
                                .showTipView(v, "领取红包并通过招呼",
                                        0, UIUtils.getPixels(-15f), ITip.Triangle.BOTTOM)
                                .autoHide(3000L));


    }

}