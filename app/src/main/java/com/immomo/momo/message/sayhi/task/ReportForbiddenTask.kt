package com.immomo.momo.message.sayhi.task

import android.app.Activity
import android.util.Pair
import com.immomo.framework.base.BaseActivity
import com.immomo.framework.task.BaseDialogTask
import com.immomo.mmstatistics.event.TaskEvent
import com.immomo.mmutil.toast.Toaster
import com.immomo.momo.R
import com.immomo.momo.android.view.dialog.MAlertDialog
import com.immomo.momo.message.sayhi.activity.HiCardStackActivity
import com.immomo.momo.message.sayhi.activity.SayHiGiftActivity
import com.immomo.momo.message.sayhi.activity.SayHiListActivity
import com.immomo.momo.message.sayhi.activity.reflect.NewSayHiCardActivity
import com.immomo.momo.message.sayhi.utils.SayHiConst
import com.immomo.momo.protocol.http.SayHiApi
import com.immomo.momo.statistics.EVAction
import com.immomo.momo.statistics.EVPage
import java.lang.ref.WeakReference

//拉黑禁言task
class ReportForbiddenTask(
    var weak: WeakReference<Activity>,
    var remoteId: String
) : BaseDialogTask<Any, Any, Pair<Int, String>>("") {


    override fun executeTask(vararg params: Any?): Pair<Int, String> {
        return SayHiApi.getInstance().reportForbidden(remoteId)
    }

    override fun onTaskSuccess(result: Pair<Int, String>?) {
        super.onTaskSuccess(result)

        if (result?.first == 1) {
            if (weak.get() is HiCardStackActivity || weak.get() is SayHiListActivity ||
                weak.get() is SayHiGiftActivity || weak.get() is NewSayHiCardActivity) {
                val mAlertDialog = MAlertDialog(weak.get(), false)
                mAlertDialog.setTitle("禁言成功")
                mAlertDialog.setMessage(result.second ?: "")
                mAlertDialog.setButton(
                    MAlertDialog.INDEX_RIGHT,
                    R.string.fine,
                    mAlertDialog.defaultButtonOnclick
                )
                mAlertDialog.setOnDismissListener {
                    if (weak.get() != null) {
                        (weak.get() as? HiCardStackActivity)?.afterReportSuccess(
                            remoteId
                        )
                        (weak.get() as? SayHiListActivity)?.afterBlockAndReportSuccess(
                            remoteId
                        )
                        reportCardEvent()
                    }
                }
                mAlertDialog.setSupportDark(true)
                (weak.get() as? BaseActivity)?.showDialog(
                    mAlertDialog
                )
            }
        } else {
            if (weak.get() is SayHiListActivity) {
                if (weak.get() != null) {
                    (weak.get() as SayHiListActivity).afterBlockAndReportSuccess(
                        remoteId
                    )
                }
            }
            reportCardEvent()
            Toaster.show(result?.second ?: "")
        }
        forbiddenTaskEvent(result?.first == 1, remoteId)
    }

    private fun reportCardEvent() {
        (weak.get() as? SayHiGiftActivity)?.afterBlockAndReportSuccess(SayHiConst.EVENT_SAYHI_STACK_CARD_REPORT, remoteId)
        (weak.get() as? NewSayHiCardActivity)?.afterBlockAndReportSuccess(SayHiConst.EVENT_SAYHI_STACK_CARD_REPORT, remoteId)
    }

    private fun forbiddenTaskEvent(isSuc: Boolean, remoteId: String) {
        TaskEvent.create().page(EVPage.Msg.SayhiCard)
            .action(EVAction.Window.Ban)
            .status(if (isSuc) TaskEvent.Status.Success else TaskEvent.Status.Fail)
            .type("detail")
            .putExtra("momoid", remoteId)
            .submit()
    }


}