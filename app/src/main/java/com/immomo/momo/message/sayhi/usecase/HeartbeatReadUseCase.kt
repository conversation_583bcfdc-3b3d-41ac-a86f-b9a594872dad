package com.immomo.momo.message.sayhi.usecase

import com.immomo.framework.rxjava.interactor.CommonSubscriber
import com.immomo.framework.rxjava.interactor.UseCase
import com.immomo.mmutil.StringUtils
import com.immomo.mmutil.task.MMThreadExecutors
import com.immomo.momo.messages.service.SingleMsgService
import com.immomo.momo.service.bean.SayhiSession
import com.immomo.momo.service.sessions.SessionService
import io.reactivex.Flowable

/**
 * 心动招呼已读
 */
class HeartbeatReadUseCase @JvmOverloads
constructor(
    var momoId: String, val checkWhenExist: Boolean = false
) :
    UseCase<Boolean, String>(
        MMThreadExecutors.Message, MMThreadExecutors.Main
    ) {
    override fun buildUseCaseFlowable(params: String?): Flowable<Boolean> {
        return Flowable.fromCallable<Boolean> {
            var needUpdate = true
            if (checkWhenExist) {
                val sayhiSessionOnly = SessionService.getInstance().getSayhiSessionOnly(momoId)
                if (sayhiSessionOnly.isHeartbeatHi != 1) {
                    needUpdate = false
                }
            }
            if (needUpdate) {
                SingleMsgService.getInstance().updateHeartbeatHiRead(momoId)
            }
            return@fromCallable true
        }
    }

    companion object {
        @JvmStatic
        fun checkHeartbeatHi(session: SayhiSession?): HeartbeatReadUseCase? {
            session ?: return null
            val momoid = session.momoid
            var heartbeatReadUseCase: HeartbeatReadUseCase? = null
            if (session.isHeartbeatHi == 1 && StringUtils.isNotBlank(momoid)) {
                heartbeatReadUseCase = HeartbeatReadUseCase(momoid)
                heartbeatReadUseCase.execute(CommonSubscriber<Boolean>())
                session.isHeartbeatHi = 0
            }
            return heartbeatReadUseCase
        }

        @JvmStatic
        fun checkHeartbeatHiWhenExist(momoid: String?): HeartbeatReadUseCase? {
            momoid ?: return null
            if (momoid.isBlank()) return null
            val heartbeatReadUseCase = HeartbeatReadUseCase(momoid, true)
            heartbeatReadUseCase.execute(object : CommonSubscriber<Boolean>() {
                override fun onError(exception: Throwable?) {}
            })
            return heartbeatReadUseCase
        }
    }
}