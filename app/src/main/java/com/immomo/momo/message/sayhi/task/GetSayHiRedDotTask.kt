package com.immomo.momo.message.sayhi.task

import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.momo.maintab.model.SayHiListReqParam
import com.immomo.momo.protocol.http.SayHiApi
import com.immomo.momo.service.daobase.BaseDao
import com.immomo.momo.service.sessions.SessionService
import com.immomo.momo.util.StringUtils
import java.util.Date

class GetSayHiRedDotTask(
    var successBlock: (intervalTime: Long) -> Unit
) : MomoTaskExecutor.Task<Any, Any, Long>("") {

    @Throws(Exception::class)
    override fun executeTask(vararg objects: Any?): Long {
        var sessions = SessionService.getInstance().getSayHiSessionsOrderTime(0, 20)

        var helloChatIds = ""
        sessions.forEach {
            if (it != null && StringUtils.notEmpty(it.momoid)) {
                helloChatIds = helloChatIds + it.momoid + ","
            }
        }

        var lastGreetTime: Long
        val session = SessionService.getInstance().findFirstSayHiSession()
        lastGreetTime = if (session != null) {
            BaseDao.toDbTime(session.fetchTime) / 1000L
        } else {
            System.currentTimeMillis() / 1000L
        }
        return SayHiApi.getInstance().getSayHiSessionRedDot(helloChatIds, lastGreetTime)
    }


    override fun onTaskSuccess(result: Long) {
        super.onTaskSuccess(result)
        successBlock.invoke(result)
    }

    override fun onTaskError(e: java.lang.Exception?) {
    }
}