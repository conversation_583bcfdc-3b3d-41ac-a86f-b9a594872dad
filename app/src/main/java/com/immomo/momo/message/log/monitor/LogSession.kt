package com.immomo.momo.message.log.monitor

import androidx.recyclerview.widget.RecyclerView

/**
 * CREATED BY liu.chong
 * AT 2024/3/20
 */
interface LogSession {
    fun chatType(chatType: Int): LogSession
    fun chatId(id: String): LogSession
    fun newIntent(): LogSession
    fun addMsg(vararg msg: String?): LogSession
    fun submit()
    fun checkEmpty(rv:RecyclerView?)
}

class LogSessionEmptyImpl : LogSession {
    override fun chatType(chatType: Int): LogSession = this
    override fun chatId(id: String): LogSession = this
    override fun newIntent(): LogSession = this
    override fun addMsg(vararg msg: String?): LogSession = this
    override fun submit() = Unit
    override fun checkEmpty(rv: RecyclerView?) = Unit
}