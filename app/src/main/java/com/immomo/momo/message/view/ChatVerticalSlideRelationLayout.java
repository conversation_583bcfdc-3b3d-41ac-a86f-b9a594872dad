package com.immomo.momo.message.view;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowInsets;
import android.widget.RelativeLayout;

import androidx.core.view.ViewCompat;
import androidx.customview.widget.ViewDragHelper;

import com.immomo.framework.utils.UIUtils;


/**
 * <AUTHOR>
 */
public class ChatVerticalSlideRelationLayout extends RelativeLayout {
    private int[] mInsets = new int[4];
    private static final int INTERNAL = 5;
    private ViewDragHelper mViewDragHelper;
    private VideoVerticalSlideCallback callback;
    private boolean settleDownSuccess;

    private int endTop;

    public ChatVerticalSlideRelationLayout(Context context) {
        super(context);
        init();
    }

    public ChatVerticalSlideRelationLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public ChatVerticalSlideRelationLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        return mViewDragHelper.shouldInterceptTouchEvent(ev);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        mViewDragHelper.processTouchEvent(event);
        return true;
    }

    @Override
    public void computeScroll() {
        if (mViewDragHelper.continueSettling(true)) {
            ViewCompat.postInvalidateOnAnimation(this);
            invalidate();
        } else if (settleDownSuccess) {
            if (callback != null) {
                callback.onSettlingDown();
            }
        }
    }

    private void init() {
        mViewDragHelper = ViewDragHelper.create(this, 0.75f, viewDragCallBack);
    }

    public void setCallback(VideoVerticalSlideCallback callback) {
        this.callback = callback;
    }

    private ViewDragHelper.Callback viewDragCallBack = new ViewDragHelper.Callback() {
        @Override
        public boolean tryCaptureView(View child, int pointerId) {
            return callback != null && callback.canSlideDown(child);
        }

        @Override
        public void onViewReleased(View releasedChild, float xvel, float yvel) {
            int finalTop = UIUtils.getScreenHeight();
            if (endTop < releasedChild.getHeight() / INTERNAL) {
                settleDownSuccess = false;
                mViewDragHelper.settleCapturedViewAt(0, 0);
            } else {
                settleDownSuccess = true;
                mViewDragHelper.settleCapturedViewAt(0, finalTop);

            }
            postInvalidate();
        }

        @Override
        public void onViewDragStateChanged(int state) {
            super.onViewDragStateChanged(state);
            if (callback != null) {
                callback.onViewDragStateChanged(state);
            }
        }


        @Override
        public int getViewHorizontalDragRange(View child) {
            return 0;
        }

        @Override
        public int getViewVerticalDragRange(View child) {
            return child.getHeight();
        }

        @Override
        public int clampViewPositionHorizontal(View child, int left, int dx) {
            return 0;
        }

        @Override
        public int clampViewPositionVertical(View child, int top, int dy) {
            endTop = top > 0 ? top : 0;
            return top > 0 ? top : 0;
        }
    };

    public interface VideoVerticalSlideCallback {
        boolean canSlideDown(View child);

        void onSettlingDown();

        void onViewDragStateChanged(int state);


    }

    @Override
    protected boolean fitSystemWindows(Rect insets) {
            mInsets[0] = insets.left;

            mInsets[1] = insets.top;

            mInsets[2] = insets.right;

            insets.left = 0;

            insets.top = 0;

            insets.right = 0;

        return super.fitSystemWindows(insets);
    }

    @Override
    public WindowInsets onApplyWindowInsets(WindowInsets insets) {

            mInsets[0] = insets.getSystemWindowInsetLeft();

            mInsets[1] = insets.getSystemWindowInsetTop();

            mInsets[2] = insets.getSystemWindowInsetRight();

            return super.onApplyWindowInsets(insets.replaceSystemWindowInsets(0, 0, 0,

                    insets.getSystemWindowInsetBottom()));

    }

}
