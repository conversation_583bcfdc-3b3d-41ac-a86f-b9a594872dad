package com.immomo.momo.message.task;

public class Dpi {
    public static final String Ldpi = "ldpi";
    public static final String Mdpi = "mdpi";
    public static final String Hdpi = "hdpi";
    public static final String Xhdpi = "xhdpi";
    public static final String XXhdpi = "xxhdpi";
    public static final String XXXhdpi = "xxxhdpi";

    public static int dpi(String dpiType) {
        switch (dpiType) {
            default:
            case Ldpi:
                return 120;
            case Mdpi:
                return 160;
            case Hdpi:
                return 240;
            case Xhdpi:
                return 320;
            case XXhdpi:
                return 480;
            case XXXhdpi:
                return 640;
        }
    }

}
