package com.immomo.momo.message.log.monitor

import android.util.Log
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.momo.messages.service.GroupMsgService
import com.immomo.momo.messages.service.GroupMsgServiceV2
import com.immomo.momo.messages.service.SingleMsgService
import com.immomo.momo.messages.service.SingleMsgServiceV2
import com.immomo.momo.mulog.MULogConstants
import com.immomo.momo.mulog.MURealtimeLog
import com.immomo.momo.mulog.pair.MUPairItem
import com.immomo.momo.service.bean.Message
import com.immomo.momo.util.DateUtil
import com.tencent.wcdb.database.SQLiteException
import java.util.Date

/**
 * CREATED BY liu.chong
 * AT 2024/9/2
 */
class MsgListMonitor {
    companion object {
        private const val BZ3_EMPTY = "MsgListMonitor"
        private const val BZ3_DB_CLOSE = "MsgListMonitor3"
    }

    var time = 0L
    var hasReported = false
    var error: Exception? = null

    /**
     * initData 开始执行InitMessageListTask时调用
     */
    fun startLoad() {
        time = System.currentTimeMillis()
        error = null
        hasReported = false
    }

    /**
     * 成功加载完数据后调用
     */
    fun onLoadFinish(chatType: Int, momoId: String?, num: Int) {
        momoId ?: return
        time = System.currentTimeMillis() - time
        if (num > 0) {
            return
        }
        if (SingleMsgServiceV2.service.getDao(momoId).db?.isOpen != true
            || GroupMsgServiceV2.service.getDao(momoId).db?.isOpen != true
        ) {
            val sms = SingleMsgServiceV2.service.getDao(momoId).db?.toString() ?: "-"
            val gms = GroupMsgServiceV2.service.getDao(momoId).db?.toString() ?: "-"
            val sms2 = SingleMsgService.getInstance().db?.toString() ?: "-"
            val gms2 = GroupMsgService.getInstance().db?.toString() ?: "-"
            MURealtimeLog.business(MULogConstants.BUSINESS_MOMO_BASIC)
                .secondLBusiness("Message")
                .thirdLBusiness(BZ3_DB_CLOSE)
                .addBodyItem(MUPairItem.title(DateUtil.formateDateTime(Date())))
                .addBodyItem(MUPairItem.id(momoId))
                .addBodyItem(MUPairItem.info("$sms-$sms2"))
                .addBodyItem(MUPairItem.msg("$gms-$gms2"))
                .addBodyItem(MUPairItem.type(chatType))
                .commit()
        }
    }

    /**
     * 加载异常后调用
     */
    fun onLoadError(chatType: Int, exception: Exception) {
        error = exception
        time = System.currentTimeMillis() - time
        if (exception is SQLiteException
            && chatType == Message.CHATTYPE_USER
            && exception.message?.contains("no such table") == true
        ) {
            error = null
        }

    }

    /**
     * 退出页面时调用，如果：
     * 1. 退出时还没加载出来，
     * 2. 或者消息记录有凭空消失，
     * 3. 或者加载超时，
     * 4. 或者加载异常的情况
     * 则会上报
     */
    fun onDestroy(momoId: String?, chatType: Int) {
        val timeOut = time > 3000
        if (timeOut || error != null) {
            if (hasReported) return
            hasReported = true
            MURealtimeLog.business(MULogConstants.BUSINESS_MOMO_BASIC)
                .secondLBusiness("Message")
                .thirdLBusiness(BZ3_EMPTY)
                .addBodyItem(MUPairItem.title(DateUtil.formateDateTime(Date())))
                .addBodyItem(MUPairItem.id(momoId.safe()))
                .addBodyItem(MUPairItem.type(chatType))
                .addBodyItem(MUPairItem.errorMsg(Log.getStackTraceString(error)))
                .addBodyItem(MUPairItem.uploadTime(time))
                .addBodyItem(MUPairItem.ticker(timeOut.toString()))
                .commit()
        }

    }
}