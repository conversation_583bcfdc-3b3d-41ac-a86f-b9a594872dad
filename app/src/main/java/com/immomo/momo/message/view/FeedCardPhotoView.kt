package com.immomo.momo.message.view

import android.content.Context
import android.util.AttributeSet
import android.widget.FrameLayout
import android.widget.ImageView
import com.immomo.momo.R

/**
 * -----------------------------------------------------------------
 * Copyright (C)  sun
 * Create: 2022/11/2 6:05 下午
 * -----------------------------------------------------------------
 */
class FeedCardPhotoView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    var imageView: ImageView? = null

    init {
        val view = inflate(context, R.layout.item_feed_card_photo, this)
        imageView = view.findViewById(R.id.imageview)
    }
}