package com.immomo.momo.message.task;

import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.discuss.bean.Discuss;
import com.immomo.momo.discuss.bean.DiscussUser;
import com.immomo.momo.maintab.sessionlist.SessionStickyHelper;
import com.immomo.momo.message.activity.MultiChatActivity;
import com.immomo.momo.protocol.imjson.IMJApi;
import com.immomo.momo.service.sessions.MessageServiceHelper;


/**
 * 多人聊天增加讨论用户的异步任务
 * <AUTHOR>
 * date 2020/8/15
 */
public class MultiChatAddDiscussUserTask extends MomoTaskExecutor.Task {

    private MultiChatActivity mActivity;


    public MultiChatAddDiscussUserTask(MultiChatActivity mActivity) {
        this.mActivity = mActivity;
    }

    @Override
    protected Object executeTask(Object[] params) {
        try {
            if (IMJApi.checkDiscussRole(mActivity.currentDiscuss.id)) {
                mActivity.discussService.addDiscussUser(mActivity.getCurrentUser().momoid,
                        mActivity.currentDiscuss.id, DiscussUser.ROLE_MEMBER);
                mActivity.discussService.updateStatus(mActivity.currentDiscuss.id, Discuss.STATUS_NORMAL);
                publishProgress();
            } else {
                try {
                    SessionStickyHelper.getInstance().removeSessionSticky(
                            mActivity.currentDiscuss.id,
                            SessionStickyHelper.ChatType.TYPE_DISCUSS);
                } catch (Throwable ignored) {
                }
            }
        } catch (Exception e) {
        }
        return null;
    }

    @Override
    protected void onProgressUpdate(Object[] values) {
        mActivity.showInputLayout();
    }
}