package com.immomo.momo.message.paper.chat.single

import android.view.View
import com.immomo.momo.R
import com.immomo.momo.message.paper.BasePaperContainerFragment
import com.immomo.momo.message.paper.PaperConfig
import com.immomo.momo.message.paper.PaperFragmentHelper
import com.immomo.momo.message.paper.chat.ITimelyPaperFragment
import com.immomo.momo.message.paper.common.ChatAudioCoverPaperFragment
import com.immomo.momo.message.paper.common.ChatSvgaEggPaperFragment
import com.immomo.momo.message.paper.common.GiftEffectPaperFragment
import com.immomo.momo.message.paper.common.timely.TimelyCameraPaperFragment

/**
 * <AUTHOR>
 * @data 2020-12-18.
 */

class SingleChatTopPaperFragment : BasePaperContainerFragment(), ITimelyPaperFragment {

    companion object {
        @JvmStatic
        fun newInstance(): SingleChatTopPaperFragment {
            return SingleChatTopPaperFragment()
        }
    }

    override fun getPagers(): MutableList<PaperConfig>? =
        mutableListOf(
            PaperConfig(ChatSvgaEggPaperFragment.newInstance()),
            PaperConfig(ChatAudioCoverPaperFragment.newInstance()),
            PaperConfig(GiftEffectPaperFragment.newInstance())
        )

    override fun getPageLayout(): Int = R.layout.paper_single_chat_top

    override fun onPageLoad() {
    }

    override fun initPageViews(contentView: View?) {
    }

    override fun addTimelyCameraPaper() {
        addPaper(PaperConfig(TimelyCameraPaperFragment.newInstance()))
    }

    override fun removeTimelyCameraPaper() {
        removePaper(TimelyCameraPaperFragment::class.java)
    }

    override fun findTimelyFragment(): TimelyCameraPaperFragment? {
        return PaperFragmentHelper.find(
            childFragmentManager,
            TimelyCameraPaperFragment::class.java
        )
    }


}