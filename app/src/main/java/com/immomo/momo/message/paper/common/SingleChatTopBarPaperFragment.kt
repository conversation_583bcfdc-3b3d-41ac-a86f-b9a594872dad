package com.immomo.momo.message.paper.common

import android.content.Context
import android.graphics.PorterDuff
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.text.TextUtils
import android.view.View
import android.view.ViewStub
import android.widget.Button
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.immomo.framework.imageloader.ImageLoaderUtil
import com.immomo.framework.imageloader.ImageType
import com.immomo.framework.utils.UIUtils
import com.immomo.mmutil.NetUtils
import com.immomo.momo.R
import com.immomo.momo.common.AppKit
import com.immomo.momo.eventbus.DataEvent
import com.immomo.momo.innergoto.helper.ActivityHandler
import com.immomo.momo.message.activity.ChatActivity
import com.immomo.momo.message.paper.event.PaperEvent
import com.immomo.momo.moment.utils.DensityUtil
import com.immomo.momo.service.bean.TopBarNotice
import com.immomo.momo.util.TopBarNoticeHelper
import de.greenrobot.event.EventBus


/**
 * 单聊通知
 * <AUTHOR>
 * @data 2020-12-18.
 */
class SingleChatTopBarPaperFragment : BaseChatTopBarPaperFragment() {

    private var isShowTopTipView = false
    private val SHOW = 1


    companion object {
        fun newInstance(): SingleChatTopBarPaperFragment {
            return SingleChatTopBarPaperFragment()
        }
    }

    override fun checkTopBarNotice(isForceRefresh: Boolean) {
        super.checkTopBarNotice(isForceRefresh)
        if (AppKit.getAccountManager().isOnline) {
            getChatActivity()?.remoteUser?.let {
                TopBarNoticeHelper.getInstance().checkNoticeUpdate(
                    getTaskTag(), TopBarNotice.TYPE_CHAT,
                    it.momoid, isForceRefresh, this
                )
            }
        }
    }

    /**
     * 显示头部通知条（举报or关注）
     *
     * @param topBarNotice
     * @return
     */
    override fun showTopBar(topBarNotice: TopBarNotice): Boolean {
        val result = showChatTopBar(topBarNotice)
        if (!result) {
            return false
        }
        getChatActivity()?.hideAddFollowLayout()
        getChatActivity()?.hideSpecialFriendNotice()
        return true
    }

    override fun hideTopBar() {
        super.hideTopBar()
        getChatActivity()?.refreshAddNoticeLayout()
        getChatActivity()?.removeSpecialFriendTipsMessage()
        getChatActivity()?.hideBottomTipsView()
        mPaperCommonViewModel?.chatWebPlaceHelper?.onTopBarVisible(false)
    }

    /**
     * 显示头部通知条（举报or关注）
     *
     * @param topBarNotice
     * @return
     */
    private fun showChatTopBar(topBarNotice: TopBarNotice): Boolean {
        if (!NetUtils.isNetworkAvailable()) {
            hideTopBar()
            return false
        }
        if (topBarNoticeLayout == null) {
            val viewStub = mContentView?.findViewById(R.id.viewstub_chat_topbar_layout) as? ViewStub
            if (null != viewStub) {
                topBarNoticeLayout = viewStub.inflate()
            }
        }

        topBarNoticeLayout?.let {
            val topBarCommonLayout: RelativeLayout? = it.findViewById(R.id.taopbar_common)

            topBarCommonLayout?.visibility = View.VISIBLE
            mPaperCommonViewModel?.chatWebPlaceHelper?.onTopBarVisible(true)
            val ivClose = topBarCommonLayout?.findViewById<ImageView>(R.id.star_chat_close)
            ivClose?.visibility = View.VISIBLE
            //点击X隐藏小蓝条布局
            ivClose?.setOnClickListener {
                topBarCommonLayout.visibility = View.GONE
                mPaperCommonViewModel?.chatWebPlaceHelper?.onTopBarVisible(false)
            }


            //举报按钮
            val topBarNoticeButton = it.findViewById<TextView>(R.id.chat_btn_notice_hongbao_send)
            val topBarNoticeButton2 = it.findViewById<TextView>(R.id.chat_btn_notice_hongbao_send2)

            //title
            val topBarTitleView = it.findViewById<TextView>(R.id.chat_tv_notice_hongbao_note)
            topBarTitleView.setText(topBarNotice.title)
            //content
            val topBarContentView = it.findViewById<TextView>(R.id.chat_tv_notice_hongbao_content)
            topBarContentView.text = topBarNotice.desc
            //控制右箭头
            if (topBarNotice.btnstyle == SHOW) {
                it.findViewById<View>(R.id.chat_btn_notice_arrow).visibility = View.VISIBLE
            } else {
                it.findViewById<View>(R.id.chat_btn_notice_arrow).visibility = View.INVISIBLE
            }
            //按钮有无边框
            if (topBarNotice.is_btn == TopBarNotice.BTN_BORDER_NONE) {
                topBarNoticeButton.setVisibility(View.INVISIBLE)
                topBarNoticeButton2.setVisibility(View.VISIBLE)
                topBarNoticeButton2.setText(topBarNotice.gotoTitle)
                topBarNoticeButton2.setTextColor(topBarNotice.btnTextColorUI)
            } else if (topBarNotice.is_btn == TopBarNotice.BTN_BORDER_SERVER) {
                topBarNoticeButton2.setVisibility(View.GONE)
                topBarNoticeButton.setText(topBarNotice.gotoTitle)
                topBarNoticeButton.setVisibility(View.VISIBLE)
                //举报按钮
                topBarNoticeButton.setOnClickListener({ v -> dealWithGotoWithOutSave(topBarNotice) })
                topBarNoticeButton.setTextColor(topBarNotice.btnTextColorUI)
                val myGrid = topBarNoticeButton.getBackground() as GradientDrawable
                myGrid.setColorFilter(topBarNotice.btnColor, PorterDuff.Mode.SRC_IN)
            }
            //头像显示
            val iconImgView = it.findViewById<ImageView>(R.id.chat_tv_notice_hongbao_icon)
            if (!TextUtils.isEmpty(topBarNotice.avatar)) {
                iconImgView.setVisibility(View.VISIBLE)
                ImageLoaderUtil.loadRoundImage(
                    topBarNotice.avatar, ImageType.IMAGE_TYPE_URL,
                    iconImgView, null, UIUtils.getPixels(20f), true, 0
                )
            } else {
                iconImgView.setVisibility(View.GONE)
            }

            //处理关注点击事件
            val starChatBtn = it.findViewById<Button>(R.id.star_chat_btn)
            starChatBtn.setOnClickListener { v -> dealWithGoto(topBarNotice) }
            it.visibility = View.VISIBLE
            it.post {
                if (topBarNoticeLayout != null) {
                    EventBus.getDefault().post(
                        DataEvent(
                            PaperEvent.PAPER_EVENT_SHOW_TOP_BAR,
                            it.height
                        )
                    )
                }
            }

            //快聊3.1新增 一些顶部提示会同时带有底部提示条
            getChatActivity()?.let { chatActivity ->
                chatActivity.showBottomTipsView(topBarNotice.footDesc)

                if (chatActivity.isHalfMode || chatActivity.isGreetHalfMode) {
                    isShowTopTipView = true
                    it.visibility = View.GONE
                    EventBus.getDefault().post(DataEvent(PaperEvent.PAPER_EVENT_HIDE_TOP_BAR, ""))
                }
            }

            //设置单聊举报的文本颜色
            getChatActivity()?.let {
                topBarNoticeButton?.setTextColor(topBarNotice.btnTextColorUI)
                val drawable = getBgDrawableWithColor(it, topBarNotice.btnColor)
                topBarNoticeButton?.background = drawable
                if (topBarNotice.isHiddenClose == 1) {
                    ivClose?.visibility = View.GONE
                } else {
                    ivClose?.visibility = View.VISIBLE
                }
            }
            return true
        }
        return false
    }

    private fun getBgDrawableWithColor(context: Context, color: Int): Drawable {
        val radius: Float = DensityUtil.dip2px(context, 25f).toFloat()
        val drawable = GradientDrawable()
        drawable.cornerRadius = radius
        drawable.setColor(color)
        return drawable
    }

    /**
     * 处理点击按钮goto跳转
     *
     * @param topBarNotice
     */
    private fun dealWithGoto(topBarNotice: TopBarNotice) {
        hideTopBar()
        dealWithGotoWithOutSave(topBarNotice)
        //保存头部notice入库
        TopBarNoticeHelper.getInstance().saveTopBarNotice(topBarNotice)
        return
    }

    /**
     * 处理点击按钮goto跳转（举报这种不保存消息的）
     *
     * @param topBarNotice
     */
    private fun dealWithGotoWithOutSave(topBarNotice: TopBarNotice) {
        getChatActivity()?.let {
            ActivityHandler.executeAction(topBarNotice.gotoString, it)

        }
        topBarNotice.closeTime = System.currentTimeMillis()
    }


    override fun onEvent(event: DataEvent<Any>) {
        super.onEvent(event)
        when (event.action) {
            PaperEvent.PAPER_EVENT_TOP_BRA_NOTICE_LAYOUT_VISIBLE -> {
                topBarNoticeLayout?.let {
                    if (isShowTopTipView && it.visibility == View.GONE) {
                        it.visibility = View.VISIBLE
                    }
                }
            }
        }
    }

    private fun getChatActivity(): ChatActivity? {
        (activity as? ChatActivity)?.let {
            return it
        }
        return null
    }

    fun getTaskTag(): Any {
        return this.javaClass.name + '@'.toString() + Integer.toHexString(this.hashCode())
    }
}