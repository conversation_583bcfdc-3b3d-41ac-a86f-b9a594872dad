package com.immomo.momo.message.paper.chat.single

import android.view.View
import com.immomo.momo.R
import com.immomo.momo.message.paper.BasePaperContainerFragment
import com.immomo.momo.message.paper.PaperConfig
import com.immomo.momo.message.paper.common.*

/**
 * <AUTHOR>
 * @data 2020-12-18.
 */

class SingleChatMidPaperFragment : BasePaperContainerFragment() {

    companion object {
        @JvmStatic
        fun newInstance(): SingleChatMidPaperFragment {
            return SingleChatMidPaperFragment()
        }
    }

    override fun getPagers(): MutableList<PaperConfig>? =
            mutableListOf(
                    PaperConfig(GreetHalfGiftPanelPaperFragment.newInstance()),
                    PaperConfig(LoveApartmentPaperFragment.newInstance()),
                    PaperConfig(SingleChatTopBarPaperFragment.newInstance()),
                    PaperConfig(SingleChatWebPaperFragment.newInstance())
            )

    override fun getPageLayout(): Int = R.layout.paper_single_chat_mid

    override fun onPageLoad() {
    }

    override fun initPageViews(contentView: View?) {
    }

}