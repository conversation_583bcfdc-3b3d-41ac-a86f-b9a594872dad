package com.immomo.momo.message.sayhi.stack;

import android.text.TextUtils;

import com.immomo.momo.likematch.slidestack.BaseSlideStackAdapter;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;


/**
 * 打招呼卡片的adapter
 * Created by lei.jialin on 2019/4/8.
 */
public class SayHiStackAdapter extends BaseSlideStackAdapter<SayHiInfo> {

    private HashSet<String> idSet = new HashSet<>();

    @Override
    public void addItem(SayHiInfo item) {
        super.addItem(item);
        idSet.add(String.valueOf(item.getMomoid()));
    }

    @Override
    public boolean notDuplication(SayHiInfo item) {
        return !idSet.contains(item.getMomoid());
    }

    @Override
    public void clearAll() {
        super.clearAll();
        idSet.clear();
    }

    /**
     * 每次加入新数据记录加入的总数量
     * <p>
     * totalCounts集合中元素的总和即为datalist的总数
     * 用于记录卡片上进度例如1/20的更新，
     * 分母取totalCounts中某个元素的值，分子取showingDataIndex减去分母所在元素之前的所有元素和
     */
    private List<Integer> totalCounts = new ArrayList<>();

    @Override
    public boolean needAnimBlock(SayHiInfo info) {
        return false;
    }

    /**
     * 只是为了回传数据给API用
     */
    @Override
    public String getUnReadIds() {
        return null;
    }

    /**
     * 手动给列表中的所有item添加likeMsg
     *
     * @param reply
     */
    public void refreshReplyMessage(String reply) {
        if (dataList == null) {
            return;
        }
        int firstChangeIndex = Math.max(0, showingDataIndex - 1);
        for (int i = firstChangeIndex; i < dataList.size(); i++) {
            if (dataList.get(i) != null) {
                dataList.get(i).mLikeMsg = reply;
            }
        }
    }

    /**
     * 用于记录卡片上进度例如1/20的更新，
     * 分母取totalCounts中某个元素的值，分子取showingDataIndex减去分母所在元素之前的所有元素和
     *
     * @param beforeDataIndexAdded 表示showingDataIndex是否完成自增
     * @return 返回
     */
    public int[] getCurrentTotal(boolean beforeDataIndexAdded) {
        int total = getSize();
        int index = showingDataIndex + 1;
        if (beforeDataIndexAdded) {
            index++;
        }
        for (int i = totalCounts.size() - 1; i >= 0; i--) {
            Integer currentTotal = totalCounts.get(i);
            if ((total - currentTotal) >= index) {
                total -= currentTotal;
            } else {
                index = index - (total - currentTotal);
                return new int[]{index, currentTotal};
            }
        }
        return new int[]{index, total};
    }

    public void addTotalCount(int currentTotal, boolean isClear) {
        if (isClear) {
            totalCounts.clear();
        }
        totalCounts.add(currentTotal);
    }

    /**
     * 1.将原数据dataList中的remoteId移除
     * 2.将totalCounts中remoteid对应的元素数做减1处理，减完若数值=0则移除该元素
     *
     * @param remoteId
     * @return
     */
    public boolean removeUnreadCard(String remoteId) {
        int size = getSize();
        for (int i = size - 1; i >= showingDataIndex; i--) {
            SayHiInfo item = getItem(i);
            if (item != null && TextUtils.equals(item.getMomoid(), remoteId)) {
                for (int totalCountsIndex = totalCounts.size() - 1; totalCountsIndex >= 0; totalCountsIndex--) {
                    Integer currentTotal = totalCounts.get(totalCountsIndex);
                    if (size - currentTotal <= i) {
                        currentTotal--;
                        if (currentTotal <= 0) {
                            totalCounts.remove(totalCountsIndex);
                        } else {
                            totalCounts.set(totalCountsIndex, currentTotal);
                        }
                        break;
                    } else {
                        size -= currentTotal;
                    }
                }
                dataList.remove(i);
                return true;
            }
        }
        return false;
    }
}