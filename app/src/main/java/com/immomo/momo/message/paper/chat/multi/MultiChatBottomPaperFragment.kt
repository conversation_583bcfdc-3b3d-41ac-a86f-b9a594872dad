package com.immomo.momo.message.paper.chat.multi

import android.view.View
import com.immomo.momo.R
import com.immomo.momo.message.paper.PaperConfig
import com.immomo.momo.message.paper.common.BackgroundPaperFragment
import com.immomo.momo.message.paper.BasePaperContainerFragment

/**
 * <AUTHOR>
 * @data 2020-12-18.
 */

class MultiChatBottomPaperFragment : BasePaperContainerFragment() {

    companion object {
        @JvmStatic
        fun newInstance(): MultiChatBottomPaperFragment {
            return MultiChatBottomPaperFragment()
        }
    }

    override fun getPagers(): MutableList<PaperConfig>? = mutableListOf(PaperConfig(BackgroundPaperFragment.newInstance()))

    override fun getPageLayout(): Int = R.layout.paper_multi_chat_bottom

    override fun onPageLoad() {
    }

    override fun initPageViews(contentView: View?) {
    }

}