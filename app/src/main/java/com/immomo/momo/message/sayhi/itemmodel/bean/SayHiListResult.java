package com.immomo.momo.message.sayhi.itemmodel.bean;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.immomo.momo.service.bean.PaginationResult;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;

/**
 * Created by lei.jialin on 2019/4/9.
 */
public class SayHiListResult extends PaginationResult<List<SayHiInfo>> {

    @Expose
    @SerializedName("surplus_count")
    public int surplusCnt;

    @Expose
    @SerializedName("limit_time")
    public long limitTime;

    public List<String> filterMoIds;    //过滤人列表，多个用逗号分割，回传给api让api不要再推荐
    private long timeSec = 0;

    @Expose
    @SerializedName("hasExposedData")
    public boolean hasExposedData;   // 当前是否有曝光过的数据，如果true直接展示分割线

    @Expose
    @SerializedName("tips")
    public TopTip tips;   // 顶部引导

    public TopTip getTips() {
        return tips;
    }

    public void setTips(TopTip tips) {
        this.tips = tips;
    }

    //<editor-fold desc="function">
    public synchronized void merge(SayHiListResult other) {
        if (other == null) return;
        if (other.getIndex() == 0) {
            copyValus(other);
        }
        appendData(other.getData());
    }

    public void copyValus(@NonNull SayHiListResult other) {
        setCount(other.getCount());
        setIndex(other.getIndex());
        setSurplusCnt(other.getSurplusCnt());
        setLimitTime(other.getLimitTime());
        setTotal(other.getTotal());
        setRemain(other.getRemain());
        setTimeSec(other.getTimeSec());
        setTips(other.getTips());
    }


    public void appendData(List<SayHiInfo> list) {
        if (getData() == null) { // merge two list
            this.setData(list);
        } else if (list != null) {
            this.getData().addAll(list);
        }
    }

    public void clearData() {
        if (getData() != null) getData().clear();
    }

    public boolean isEmpty() {
        return getData() == null || getData().isEmpty();
    }

    public void addFilterMomoids(List<String> momoids) {
        filterMoIds.addAll(momoids);
    }

    public void setFilterMoIds(List<String> filterMoIds) {
        this.filterMoIds = filterMoIds;
    }

    public List<String> getFilterMoIds() {
        return filterMoIds;
    }

    private void appendFilter(List<String> filterMoIds) {
        if (this.filterMoIds == null) {
            setFilterMoIds(filterMoIds);
        } else if (filterMoIds != null) {
            addFilterMomoids(filterMoIds);
        }
    }

    public boolean hasFilterMoIds() {
        return filterMoIds != null && !filterMoIds.isEmpty();
    }
    //</editor-fold>

    public void setSurplusCnt(int surplusCnt) {
        this.surplusCnt = surplusCnt;
    }

    public int getSurplusCnt() {
        return surplusCnt;
    }

    public void setLimitTime(long limitTime) {
        this.limitTime = limitTime;
    }

    public long getLimitTime() {
        return limitTime;  // s -> ms
    }

    public void setTimeSec(long timesec) {
        this.timeSec = timesec;
    }

    public long getTimeSec() {
        return timeSec;
    }

    public List<String> getAllMomoids() {
        List<String> momoids = new ArrayList<>();
        SayHiInfo info = null;
        if (getData() != null) {
            int size = getData().size();
            for (int i = 0; i < size; i++) {
                info = getData().get(i);
                if (info != null) {
                    momoids.add(info.getMomoid());
                }
            }
        }
        return momoids;
    }

    public int getDataCount() {
        return getData() != null ? getData().size() : 0;
    }

    public static class TopTip {
        @Expose
        @SerializedName("icon")
        public String icon;

        @Expose
        @SerializedName("text")
        public String text;

        @Expose
        @SerializedName("action")
        public String action;

    }
}
