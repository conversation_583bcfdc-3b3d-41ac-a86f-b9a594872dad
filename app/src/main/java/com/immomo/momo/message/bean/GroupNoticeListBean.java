package com.immomo.momo.message.bean;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/3/28
 * @description com.immomo.momo.message.bean
 */
public class GroupNoticeListBean {
    @Expose
    private List<NoticeItem> items;

    public List<NoticeItem> getItems() {
        return items;
    }

    public void setItems(List<NoticeItem> items) {
        this.items = items;
    }

    public class NoticeItem {
        @Expose
        private int type;// 1 代表普通入群审核 2 代表免审核开启通知
        @Expose
        private NoticeItemMeta meta;
        @Expose
        private NoticeItemActions actions;//goto

        public NoticeItemActions getActions() {
            return actions;
        }

        public void setActions(NoticeItemActions actions) {
            this.actions = actions;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public NoticeItemMeta getMeta() {
            return meta;
        }

        public void setMeta(NoticeItemMeta meta) {
            this.meta = meta;
        }
    }

    public class NoticeItemMeta {
        @Expose
        private String name;
        @Expose
        private String momoid;
        @Expose
        private String avatar;//头像url
        @Expose
        private String content;
        @Expose
        private String mark;
        @Expose
        private String time;
        @Expose
        @SerializedName(value = "expiresIn")
        private long expiresIn;//倒计时

        public long getExpiresIn() {
            return expiresIn;
        }

        public void setExpiresIn(long expiresIn) {
            this.expiresIn = expiresIn;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }


        public String getMomoid() {
            return momoid;
        }

        public void setMomoid(String momoid) {
            this.momoid = momoid;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }


        public String getMark() {
            return mark;
        }

        public void setMark(String mark) {
            this.mark = mark;
        }

        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }
    }

    public class NoticeItemActions {
        @Expose
        private String agree;
        @Expose
        private String ignore;
        @Expose
        private String refuse;

        public String getRefuse() {
            return refuse;
        }

        public void setRefuse(String refuse) {
            this.refuse = refuse;
        }

        public String getAgree() {
            return agree;
        }

        public void setAgree(String agree) {
            this.agree = agree;
        }

        public String getIgnore() {
            return ignore;
        }

        public void setIgnore(String ignore) {
            this.ignore = ignore;
        }

    }
}
