package com.immomo.momo.message.sayhi.stack;

import com.immomo.momo.likematch.slidestack.BaseSlideStackAdapter;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiStackCardInfo;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;


public class SayHiGiftStackAdapter extends BaseSlideStackAdapter<SayHiStackCardInfo> {

    private HashSet<String> idSet = new HashSet<>();

    @Override
    public void addItem(SayHiStackCardInfo item) {
        super.addItem(item);
        idSet.add(String.valueOf(item.getSayHiInfo().getMomoid()));
    }

    @Override
    public boolean notDuplication(SayHiStackCardInfo item) {
        return !idSet.contains(item.getSayHiInfo().getMomoid());
    }

    @Override
    public void clearAll() {
        super.clearAll();
        idSet.clear();
    }

    /**
     * 每次加入新数据记录加入的总数量
     * <p>
     * totalCounts集合中元素的总和即为datalist的总数
     * 用于记录卡片上进度例如1/20的更新，
     * 分母取totalCounts中某个元素的值，分子取showingDataIndex减去分母所在元素之前的所有元素和
     */
    private List<Integer> totalCounts = new ArrayList<>();

    @Override
    public boolean needAnimBlock(SayHiStackCardInfo info) {
        return false;
    }

    /**
     * 只是为了回传数据给API用
     */
    @Override
    public String getUnReadIds() {
        return null;
    }

    /**
     * 用于记录卡片上进度例如1/20的更新，
     * 分母取totalCounts中某个元素的值，分子取showingDataIndex减去分母所在元素之前的所有元素和
     *
     * @param beforeDataIndexAdded 表示showingDataIndex是否完成自增
     * @return 返回
     */
    public int[] getCurrentTotal(boolean beforeDataIndexAdded) {
        int total = getSize();
        int index = showingDataIndex + 1;
        if (beforeDataIndexAdded) {
            index++;
        }
        for (int i = totalCounts.size() - 1; i >= 0; i--) {
            Integer currentTotal = totalCounts.get(i);
            if ((total - currentTotal) >= index) {
                total -= currentTotal;
            } else {
                index = index - (total - currentTotal);
                return new int[]{index, currentTotal};
            }
        }
        return new int[]{index, total};
    }

    public void addTotalCount(int currentTotal, boolean isClear) {
        if (isClear) {
            totalCounts.clear();
        }
        totalCounts.add(currentTotal);
    }
}