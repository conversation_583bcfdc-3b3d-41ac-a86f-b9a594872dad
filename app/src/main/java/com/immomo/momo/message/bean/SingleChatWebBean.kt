package com.immomo.momo.message.bean

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

/**
 * <AUTHOR>
 * @data 2021/1/18.
 */
class SingleChatWebBean {
    companion object {
        const val OPEN = 1
    }

    @Expose
    @SerializedName("status")
    val status = 0

    @SerializedName("url")
    @Expose
    val url: String? = null

    @Expose
    @SerializedName("remoteId")
    val remoteID: String? = null
}