package com.immomo.momo.message.log

import com.immomo.lcapt.evlog.anno.ClickPoint
import com.immomo.lcapt.evlog.anno.ExposurePoint
import com.immomo.lcapt.evlog.anno.ParamMap

interface IGroupGiftBroadcastLog {

    @ExposurePoint(page = "msg.group_chat", action = "content.gift_broadcast", requireId = "19609")
    fun logShowBroadcast(@ParamMap logMap: Map<String, String?> = emptyMap())


    @ClickPoint(
        page = "msg.group_chat",
        action = "content.gift_broadcast_avatar",
        requireId = "19610"
    )
    fun logAvatarClick(@ParamMap logMap: Map<String, String?> = emptyMap())

    @ClickPoint(
        page = "msg.group_chat",
        action = "content.gift_broadcast_onlooker",
        requireId = "19611"
    )
    fun logWatchClick(@ParamMap logMap: Map<String, String?> = emptyMap())


}