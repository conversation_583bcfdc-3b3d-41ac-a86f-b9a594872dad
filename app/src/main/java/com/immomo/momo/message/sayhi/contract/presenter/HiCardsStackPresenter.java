package com.immomo.momo.message.sayhi.contract.presenter;

import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.cosmos.mdlog.MDLog;
import com.immomo.framework.rxjava.interactor.CommonSubscriber;
import com.immomo.framework.storage.kv.KV;
import com.immomo.mmstatistics.event.TaskEvent;
import com.immomo.mmutil.task.MMThreadExecutors;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoKit;
import com.immomo.momo.appconfig.model.AppConfigV2;
import com.immomo.momo.eventbus.DataEvent;
import com.immomo.momo.eventbus.EventKeys;
import com.immomo.momo.greet.GreetHelper;
import com.immomo.momo.im.GiftSayHiAppConfigV1;
import com.immomo.momo.maintab.model.SayHiListReqParam;
import com.immomo.momo.maintab.sessionlist.SessionListFragment;
import com.immomo.momo.message.sayhi.SayHiStackCache;
import com.immomo.momo.message.sayhi.contract.IHiCardsStackContract;
import com.immomo.momo.message.sayhi.itemmodel.SubmitReplySetting;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiListResult;
import com.immomo.momo.message.sayhi.repository.BlockOrReportUseCase;
import com.immomo.momo.message.sayhi.repository.BlockParams;
import com.immomo.momo.message.sayhi.repository.FilterUnshownSayhiPeopleUseCase;
import com.immomo.momo.message.sayhi.repository.SubmitAutoReplySettingUseCase;
import com.immomo.momo.message.sayhi.task.GetQuestionListTask;
import com.immomo.momo.message.sayhi.utils.SayHiMessageUtils;
import com.immomo.momo.mvp.common.model.ModelManager;
import com.immomo.momo.personalprofile.bean.PersonalProfileQuestion;
import com.immomo.momo.service.bean.SayhiSession;
import com.immomo.momo.service.bean.Session;
import com.immomo.momo.service.sessions.ISessionRepository;
import com.immomo.momo.service.sessions.SessionService;
import com.immomo.momo.statistics.EVAction;
import com.immomo.momo.statistics.EVPage;
import com.immomo.momo.util.RXUtilsKt;
import com.immomo.momo.util.StringUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

import de.greenrobot.event.EventBus;
import io.reactivex.Flowable;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by lei.jialin on 2019/4/4.
 */
public class HiCardsStackPresenter implements IHiCardsStackContract.IPresenter {
    private final FilterUnshownSayhiPeopleUseCase notInLocalUseCase;
    private IHiCardsStackContract.IView mView;
    private SayHiListResult mSayHiListResult = new SayHiListResult();
    private BlockOrReportUseCase blockOrReportUseCase;


    private ISessionRepository repository;
    private CompositeDisposable disposable = new CompositeDisposable();
    private volatile boolean isLoading;
    private SayHiListReqParam reqParam;
    //api返回可展示的所有卡片数量
    private int showTotal = -1;

    //兜底策略：api连续N次返回的都被过滤掉的话会停止请求
    private int emptyRequestCount = 0;
    //进入页时间，onpause时只更新该时间之前的数据，为了在该页面是收到的新招呼不更新未读状态
    private long inTime = System.currentTimeMillis();
    private SubmitAutoReplySettingUseCase submitDataUseCase;

    private Disposable mSayHiUserCountDisposable;
    private Disposable mSayHiUserClearGiftDisposable;


    public HiCardsStackPresenter(IHiCardsStackContract.IView iview) {
        this.mView = iview;
        notInLocalUseCase = new FilterUnshownSayhiPeopleUseCase(ModelManager.getInstance().getModel(ISessionRepository.class));
        repository = ModelManager.getInstance().getModel(ISessionRepository.class);

        reqParam = SayHiStackCache.INSTANCE.getReParam();
    }

    @Override
    public void pushNotInLocalMomoids(SayHiListResult list) {
        if (list.hasFilterMoIds()) {
            notInLocalUseCase.execute(new CommonSubscriber<Boolean>() {
                @Override
                public void onNext(Boolean aBoolean) {
                }
            }, list.getFilterMoIds());
        }
    }

    @Override
    public void onDestroy() {
        if (showTotal >= 0) {
            logCardsEvent(false, showTotal, mView.getCurrentCardInfo());
        }
        if (notInLocalUseCase != null) {
            notInLocalUseCase.dispose();
        }
        if (blockOrReportUseCase != null) {
            blockOrReportUseCase.dispose();
        }
        RXUtilsKt.dispose(mSayHiUserCountDisposable);
        RXUtilsKt.dispose(mSayHiUserClearGiftDisposable);
        cancelLoadTask();
        MomoTaskExecutor.cancleAllTasksByTag(getTaskTag());
        MomoMainThreadExecutor.cancelAllRunnables(getTaskTag());
    }

    public void cancelLoadTask() {
        if (disposable != null && !disposable.isDisposed()) {
            disposable.dispose();
        }
    }

    @Override
    public boolean onBackPressed() {
        return false;
    }

    @Override
    public void blockAndDeleteSession(String reportOptionUserId, int action, boolean deleteMsg, Map<String, String> otherParams) {
        //举报拉黑成功，更新相关数据
        if (blockOrReportUseCase == null) {
            blockOrReportUseCase = new BlockOrReportUseCase(ModelManager.getInstance().getModel(ISessionRepository.class));
        }
        blockOrReportUseCase.clear();
        blockOrReportUseCase.execute(new CommonSubscriber<BlockParams.Response>() {
            @Override
            public void onNext(BlockParams.Response response) {
                if (mView.getContext() != null) {
                    EventBus.getDefault().post(new DataEvent<>(EventKeys.Block.ADD_BLOCK, response.reportMomoid));
                    ModelManager.getInstance().getModel(ISessionRepository.class)
                            .sendFriendListBroadcastBy(mView.getContext(), response.reportMomoid, response.currentUser, response.originRelation);
                }
            }
        }, new BlockParams.Request(reportOptionUserId, deleteMsg, otherParams));
    }

    @Override
    public SayHiListReqParam getCurReqParam() {
        return reqParam;
    }


    @Override
    public SayHiListResult getSayHiList() {
        return mSayHiListResult;
    }


    /**
     * 请求卡片数据
     *
     * @param remoteId
     * @param isLoadMore false重新请求数据
     */
    @Override
    public void loadCardData(String remoteId, boolean isLoadMore) {
        //push来的不能return
        if (reqParam.useCache && !isLoadMore && TextUtils.isEmpty(remoteId)) {
            reqParam.useCache = false;
            reqParam.remoteid = null;
            if (SayHiStackCache.INSTANCE.getCacheData() != null) {
                if (GiftSayHiAppConfigV1.isOpenExp()) {
                    clearOldGiftCacheCardData();
                } else {
                    loadSuc(SayHiStackCache.INSTANCE.getCacheData());
                }
            }
            return;
        }
        if (!isLoadMore) {
            RXUtilsKt.dispose(mSayHiUserCountDisposable);
            mSayHiUserCountDisposable = Flowable
                    .fromCallable(() -> SessionService.getInstance().getAllSayHiUserNormalPartSpamCount())
                    .subscribeOn(Schedulers.from(MMThreadExecutors.INSTANCE.getUser()))
                    .observeOn(MMThreadExecutors.INSTANCE.getMain().getScheduler())
                    .subscribeWith(new CommonSubscriber<Integer>() {
                        @Override
                        public void onNext(Integer count) {
                            super.onNext(count);
                            if (count <= 0) {
                                mView.showEmptyViewOrGoHiSessionList();
                            } else {
                                loadCardDataNoCache(remoteId, false);
                            }
                        }
                    });
        } else {
            loadCardDataNoCache(remoteId, true);
        }
    }

    private void clearOldGiftCacheCardData() {
        RXUtilsKt.dispose(mSayHiUserClearGiftDisposable);
        mSayHiUserClearGiftDisposable = Flowable
                .fromCallable(() -> {
                    try {
                        SayHiListResult cacheData = SayHiStackCache.INSTANCE.getCacheData();
                        List<SayHiInfo> data = cacheData.getData();
                        if (data != null && !data.isEmpty()) {
                            Iterator<SayHiInfo> iterator = data.iterator();
                            while (iterator.hasNext()) {
                                SayHiInfo sayHiInfo = iterator.next();
                                String momoid = sayHiInfo.getMomoid();
                                if (momoid != null) {
                                    SayhiSession sayhiSession = SessionService.getInstance().getSayhiSession(momoid);
                                    if (sayhiSession != null && sayhiSession.isFromGift()) {
                                        iterator.remove();
                                        if (data.isEmpty()) {
                                            SayHiStackCache.INSTANCE.clearCache();
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    } catch (Throwable throwable) {
                        MDLog.printErrStackTrace(LogTag.COMMON, throwable);
                    }
                    return 0;
                })
                .subscribeOn(Schedulers.from(MMThreadExecutors.INSTANCE.getUser()))
                .observeOn(MMThreadExecutors.INSTANCE.getMain().getScheduler())
                .subscribeWith(new CommonSubscriber<Integer>() {
                    @Override
                    public void onNext(Integer count) {
                        loadSuc(SayHiStackCache.INSTANCE.getCacheData());
                    }
                });
    }

    /**
     * 加载卡片(如push来的)
     */
    public void loadCardDataNoCache(String remoteId, boolean isLoadMore) {
        if (isLoading) {
            return;
        }
        if (isLoadMore && !reqParam.hasMore) {
            return;
        }
        isLoading = true;
        if (!isLoadMore) {
            mSayHiListResult.clearData();
            reqParam.toInitPageByApi();
        }
        loadData(remoteId);
    }

    /**
     * 处理本地sayhi缓存列表
     *
     * @param remoteId
     * @param sayHiListResult
     */
    private void dealWithLocalCacheList(final String remoteId, @NonNull SayHiListResult sayHiListResult) {
        //获取接口返回的用户的详细信息
        SayHiInfo newPushInfo = SayHiMessageUtils.getSayHiInfo(remoteId, sayHiListResult.getData());
        //本地有用户数据，走原来的逻辑；本地没有用户数据，走强插
        List<SayHiInfo> localCacheList = SayHiStackCache.INSTANCE.getCacheData().getData();
        boolean pushUserExist = SayHiMessageUtils.getSayHiInfo(remoteId, localCacheList) != null;
        if (!pushUserExist && newPushInfo != null) {
            //需要获得对应的状态 姓名 图片信息
            if (localCacheList == null) {
                localCacheList = new ArrayList<>();
            }
            localCacheList.add(newPushInfo);
            SayHiStackCache.INSTANCE.getCacheData().setData(localCacheList);
            SayHiStackCache.INSTANCE.setHasNewHi(true);
        }
    }

    private void loadData(final String remoteId) {
        reqParam.remoteid = remoteId;
        disposable.clear();
        disposable.add(repository.sayHiStackList(reqParam)  //api
                .subscribeOn(Schedulers.from(MMThreadExecutors.INSTANCE.getUser()))
                .doOnNext(new Consumer<SayHiListResult>() {
                    @Override
                    public void accept(SayHiListResult sayHiListResult) {   //接口返回的sayhi列表
                        mSayHiListResult.setLimitTime(sayHiListResult.getLimitTime());
                        mSayHiListResult.setTimeSec(sayHiListResult.getTimeSec());
                        mSayHiListResult.setTips(sayHiListResult.getTips());
                        if (reqParam.hasMore) {
                            mSayHiListResult.setRemain(sayHiListResult.getRemain());
                            reqParam.index += reqParam.count;

                            dealWithLocalCacheList(remoteId, sayHiListResult);

                            //更新滑片卡的数据源，从本地数据库读取招呼的具体消息
                            SessionService.getInstance().addMessageForSayHiStack(sayHiListResult, remoteId);
                        }
                    }
                })
                .observeOn(MMThreadExecutors.INSTANCE.getMain().getScheduler())
                .subscribeWith(new CommonSubscriber<SayHiListResult>() {
                    @Override
                    public void onNext(SayHiListResult result) {
                        isLoading = false;
                        pushNotInLocalMomoids(result);
                        loadSuc(result);
                    }

                    @Override
                    public void onError(Throwable exception) {
                        super.onError(exception);
                        isLoading = false;
                        mView.toggleHistoryEntry(false);
                        mView.showErrorView();
                    }
                }));
    }

    private void loadSuc(SayHiListResult result) {

        //有可展示的数据
        if (!result.isEmpty()) {
            logEnter(result.getDataCount(), result.getData().get(0));
            resetEmptyDataIndex();
            mergeIntoOne(result);
            mView.loadDataSuc(result);
        } else {
            //无可展示的数据，且还可以请求更多时，直接请求分页
            if (reqParam.hasMore) {
                if (!InMaxRequestStrategy()) {
                    loadCardData(null, true);
                }
            } else {
                logEnter(result.getDataCount(), null);
                showEmpty();
            }
        }
    }

    /**
     * 是否触发兜底策略
     *
     * @return
     */
    private boolean InMaxRequestStrategy() {
        emptyRequestCount++;
        if (emptyRequestCount > KV.getUserInt(AppConfigV2.SPKeys.KEY_HI_REQUEST_MAX_COUNT, 20)) {
            reqParam.hasMore = false;
            showEmpty();
            return true;
        }
        return false;
    }

    private void showEmpty() {
        resetEmptyDataIndex();
        mView.showEmptyViewOrGoHiSessionList();
    }


    @Override
    public boolean hasMore() {
        return reqParam.hasMore;
    }

    @Override
    public void updateUnreadStatus() {
        //不在destroy中取消，防止更新不能发出
        MomoTaskExecutor.executeUserTask("updateHiUnreadStatus", new MomoTaskExecutor.Task() {
            @Override
            protected Object executeTask(Object[] objects) throws Exception {
                boolean notRemind = GreetHelper.isGreetNotRemindSettingMode();
                if (notRemind) {
                    // 招呼免打扰开关关闭时，默认所有新来的未读消息都为静默消息silent
                    GreetHelper.forceToggleAllHiMsgSilent(true);
                } else {
                    // 退出该界面时把进入intime之前的变成ignore_new状态
                    boolean isNeedFilterGift = GiftSayHiAppConfigV1.isOpenExp();
                    SessionService.getInstance().updateAllSayhiOnlyInStackIgnore(inTime, isNeedFilterGift);
                }
                return null;
            }

            @Override
            protected void onTaskSuccess(Object o) {
                Bundle bundle = new Bundle();
                bundle.putString(SessionListFragment.Key_SessionId, Session.ID.SayhiSession);
                bundle.putInt(SessionListFragment.Key_SessionType, Session.TYPE_SAYHI);
                MomoKit.getApp().dispatchMessage(bundle, SessionListFragment.Action_SessionChanged);
            }

            @Override
            protected void onTaskError(Exception e) {
            }
        });
    }

    @Override
    public void loadFemaleQuestion(boolean isFromOutSide, boolean isRedEnvelope, String sex) {
        MomoTaskExecutor.executeUserTask(getTaskTag(), new GetQuestionListTask(isFromOutSide, isRedEnvelope, sex, questions -> {
            if (questions != null && questions.size() > 0) {
                mView.showFemaleQuestionDialog((List<PersonalProfileQuestion>) questions, isFromOutSide, isRedEnvelope, sex);
            }
            return null;
        }));
    }

    @Override
    public void setReplyQuestion(String questionId, String question, boolean fromOutSide, boolean isRedEnvelope, boolean isSelect) {
        submitDataUseCase = new SubmitAutoReplySettingUseCase();
        SubmitReplySetting.Request save = new SubmitReplySetting.Request();
        save.reply = question;
        save.reply_id = questionId;
        save.iscustom = false;
        submitDataUseCase.execute(new CommonSubscriber<SubmitReplySetting.Response>() {
            @Override
            public void onNext(SubmitReplySetting.Response response) {
                super.onNext(response);
                mView.onSettingSavedSuccess(save.reply, fromOutSide, isRedEnvelope);
            }

            @Override
            public void onError(Throwable exception) {
                super.onError(exception);
                mView.onSettingError();
            }
        }, save);
    }

    private void mergeIntoOne(SayHiListResult sayHiListResult) {
        mSayHiListResult.merge(sayHiListResult);  // 分页请求汇聚
    }

    public String getTaskTag() {
        return String.valueOf(hashCode());
    }

    private void logCardsEvent(boolean isEnter, int sayHiRecommendCount, SayHiInfo cardInfo) {
        TaskEvent taskEvent = TaskEvent.create()
                .action(EVAction.Content.CardDetail)
                .page(EVPage.Msg.SayhiCard)
                .status(TaskEvent.Status.Success)
                .type("content");
        if (isEnter) {
            taskEvent.putExtra("detail_type", "start");
        } else {
            taskEvent.putExtra("detail_type", "end");
        }
        taskEvent.putExtra("sayHiRecommendCount", sayHiRecommendCount);
        if (cardInfo != null) {
            taskEvent.putExtra("cardEmpty", "0");
            taskEvent.putExtra("remoteid", cardInfo.getMomoid());
            taskEvent.putExtra("photoID", cardInfo.getUserAvatar());
            taskEvent.putExtra("distance", cardInfo.getUser().distanceString);
            taskEvent.putExtra("allmsgid", cardInfo.getAllMsgId());
            taskEvent.putExtra("online", cardInfo.isOnline() ? "1" : "0");
            taskEvent.putExtra("marks", StringUtils.join(cardInfo.getMarks(), ","));
        } else {
            taskEvent.putExtra("cardEmpty", "1");
            taskEvent.putExtra("remoteid", "");
            taskEvent.putExtra("photoID", "");
            taskEvent.putExtra("distance", "");
            taskEvent.putExtra("allmsgid", "");
            taskEvent.putExtra("online", "");
            taskEvent.putExtra("marks", "");
        }
        taskEvent.putExtra("total_card", sayHiRecommendCount);
        taskEvent.submit();
    }

    private void logEnter(int dataCount, SayHiInfo cardInfo) {
        if (showTotal < 0) {
            logCardsEvent(true, dataCount, cardInfo);
            showTotal = 0;
        }
        showTotal += dataCount;
    }

    private void resetEmptyDataIndex() {
        emptyRequestCount = 0;
    }
}
