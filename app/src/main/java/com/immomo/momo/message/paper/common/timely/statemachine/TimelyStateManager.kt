package com.immomo.momo.message.paper.common.timely.statemachine

import android.os.Parcelable
import android.view.SurfaceHolder
import androidx.lifecycle.ViewModelProvider
import com.immomo.framework.base.BaseActivity
import com.immomo.framework.statemachine.AbstractState
import com.immomo.framework.statemachine.AbstractStateMachine
import com.immomo.framework.storage.kv.KV
import com.immomo.momo.message.paper.PaperCommonViewModel
import com.immomo.momo.message.paper.common.timely.ITimelyCameraFragmentView
import com.mm.mediasdk.IMultiRecorder

class TimelyStateManager(val mView: ITimelyCameraFragmentView) : AbstractStateMachine(),
    SurfaceHolder.Callback {
    companion object {
        //记录上次摄像头方向
        const val KEY_TIMELY_CAMERA_FACE_FRONT = "KEY_TIMELY_CAMERA_FACE_FRONT"
    }

    var multiRecorder: IMultiRecorder? = null
    var mPaperCommonViewModel: PaperCommonViewModel? = null

    init {
        state.set(TimelyIdleState(this))
        (mView.getFragment().activity as? BaseActivity)?.let {
            mPaperCommonViewModel =
                ViewModelProvider(it).get(PaperCommonViewModel::class.java)
        } ?: let {
            (state.get() as? AbsBackToCloseState)?.backToCloseState()
        }

    }

    var currentHolder: SurfaceHolder? = null
    private var isFirstCreateSurface = false
    var surfaceWidth = 0
    var surfaceHeight = 0

    override fun surfaceCreated(holder: SurfaceHolder) {
        isFirstCreateSurface = true
        currentHolder = holder
    }

    override fun surfaceChanged(holder: SurfaceHolder, format: Int, width: Int, height: Int) {
        if (isFirstCreateSurface) {
            isFirstCreateSurface = false
            surfaceWidth = width
            surfaceHeight = height
            (state.get() as? AbsBackToCloseState)?.rollback2Ready()
        }
    }

    override fun surfaceDestroyed(holder: SurfaceHolder) {
        currentHolder = null
    }

    override fun hashTag() = hashCode()

    override fun onTransitionFinish() {

    }

    override fun validateMessage(type: Int, parcelable: Parcelable): Boolean {
        return false
    }

    override fun afterHandleMessage(
        state: AbstractState<*>,
        type: Int,
        parcelable: Parcelable
    ): Boolean {
        return false
    }

    override fun messageKeys() = arrayOf("nothing")

    override fun sendMessage(type: Int): Boolean {
        return false
    }

    fun switchCamera() {
        multiRecorder?.switchCamera()
        KV.saveUserValue(KEY_TIMELY_CAMERA_FACE_FRONT, multiRecorder?.isFrontCamera ?: false)
    }


    fun releaseRecord() {
        multiRecorder?.stopPreview()
        multiRecorder?.cancelRecording()
        multiRecorder?.release()
    }

    fun interruptTimely() {
        getCurrentState(AbsBackToCloseState::class.java)?.backToCloseState()
    }

    fun change2VideoState() {
        getCurrentState(TimelyReadyState::class.java)?.transition2Video()
    }

    fun recordFinish(longPress: Boolean) {
        getCurrentState(TimelyVideoState::class.java)?.recordFinish(longPress)
    }

    fun sendRealMessage(txt: String) {
        getCurrentState(TimelyReadySendState::class.java)?.sendMsg(txt)
    }

    fun isReadySendState(): Boolean {
        return getCurrentState(TimelyReadySendState::class.java) != null
    }

    fun focusOnTouch(x: Float, y: Float, width: Int, height: Int) {
        multiRecorder?.focusOnTouch(x.toDouble(), y.toDouble(), width, height)
    }


    fun setZoomLevel(percent: Float) {
        multiRecorder?.let {
            it.setZoomLevel((it.maxZoomLevel * percent).toInt())
        }
    }

    fun isCanFocus(): Boolean {
        val currentState = state.get()
        return currentState is TimelyReadyState || currentState is TimelyVideoState || currentState is TimelyImageState
    }

    fun isCanClose(): Boolean {
        val currentState = state.get()
        return currentState !is TimelyVideoState && currentState !is TimelyImageState
    }

    fun isVideoReady(): Boolean {
        return getCurrentState(TimelyReadySendState::class.java)?.microVideoModel != null
    }

    fun getRecordDuration(): Int {
        return getCurrentState(TimelyVideoState::class.java)?.passTime ?: 0
    }
}