package com.immomo.momo.message.sayhi.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.R
import com.immomo.momo.android.view.RoundCornerLinearLayout
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiUserMark

class UserMarkView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RoundCornerLinearLayout(context, attrs, defStyleAttr) {
    private var isGiftTag = false

    private var viewRoot: View
    private val imgIc: ImageView
    private val tvMark: TextView

    init {
        viewRoot = LayoutInflater.from(context).inflate(R.layout.view_sayhi_user_mark, this)
        imgIc = viewRoot.findViewById(R.id.img_ic)
        tvMark = viewRoot.findViewById(R.id.tv_mark)
    }

    fun setInfo(tag: SayHiUserMark, isGift: Boolean = false) {
        isGiftTag = isGift
        ImageLoader.load(tag.icon).into(imgIc)
        tvMark.text = tag.text
        if (isGift) {
            tvMark.setTextColor(UIUtils.getColor(R.color.color_ff7335))
        } else {
            tvMark.setTextColor(UIUtils.getColor(R.color.color_6E6E6E))
        }
    }


}