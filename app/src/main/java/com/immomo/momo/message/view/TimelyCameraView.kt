package com.immomo.momo.message.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.animation.Animation
import android.view.animation.LinearInterpolator
import android.view.animation.ScaleAnimation
import android.widget.FrameLayout
import com.immomo.momo.R

class TimelyCameraView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    companion object {
        private const val STATE_VIDEO = 0
        private const val STATE_IMAGE = 1
        private const val STATE_CANCEL = 2
    }

    var onTimelyCameraViewListen: OnTimelyCameraViewListen? = null
    var currentState = STATE_VIDEO

    private val btnVideo: View
    private val btnImage: View
    private val btnCancel: View

    private var viewRawX = -1
    private var viewRawY = -1

    init {
        inflate(getContext(), R.layout.layout_timely_camera, this)
        btnVideo = findViewById(R.id.flayout_video)
        btnCancel = findViewById(R.id.flayout_cancel)
        btnImage = findViewById(R.id.flayout_image)
        btnVideo.isSelected = true
        post {
            val location = IntArray(2)
            btnVideo.getLocationOnScreen(location)
            viewRawX = location[0]
            viewRawY = location[1]
        }
    }

    fun setMotionEvent(rawX: Float, rawY: Float) {
        if (viewRawX < 0 || viewRawY < 0) {
            return
        }

        if (rawX >= viewRawX && rawY >= viewRawY) {
            updateBtnView(STATE_VIDEO)
        } else if (rawY < viewRawY) {
            updateBtnView(STATE_IMAGE)
        } else {
            updateBtnView(STATE_CANCEL)
        }

    }

    private fun updateBtnView(state: Int) {
        if (currentState == state) {
            return
        }
        doShrinkAnim(currentState)

        currentState = state
        when (currentState) {
            STATE_VIDEO -> {
                btnVideo.isSelected = true

                btnImage.isSelected = false
                btnCancel.isSelected = false
                onTimelyCameraViewListen?.trans2Video()
            }
            STATE_IMAGE -> {
                btnVideo.isSelected = false

                btnImage.isSelected = true
                btnImage.clearAnimation()
                btnImage.startAnimation(getExpansion())

                btnCancel.isSelected = false
                onTimelyCameraViewListen?.trans2Image()
            }
            STATE_CANCEL -> {
                btnVideo.isSelected = false

                btnImage.isSelected = false

                btnCancel.isSelected = true
                btnCancel.clearAnimation()
                btnCancel.startAnimation(getExpansion())
                onTimelyCameraViewListen?.trans2Cancel()
            }
        }
    }

    private fun doShrinkAnim(state: Int) {
        when (state) {
            STATE_IMAGE -> {
                btnImage.clearAnimation()
                btnImage.startAnimation(getShinkAnim())
            }
            STATE_CANCEL -> {
                btnCancel.clearAnimation()
                btnCancel.startAnimation(getShinkAnim())
            }
        }
    }

    private fun getShinkAnim() = ScaleAnimation(
        1.3f,
        1f,
        1.3f,
        1f,
        Animation.RELATIVE_TO_SELF,
        0.5f,
        Animation.RELATIVE_TO_SELF,
        0.5f
    ).apply {
        fillAfter = true
        duration = 120
        interpolator = LinearInterpolator()
    }

    private fun getExpansion() = ScaleAnimation(
        1f,
        1.3f,
        1f,
        1.3f,
        Animation.RELATIVE_TO_SELF,
        0.5f,
        Animation.RELATIVE_TO_SELF,
        0.5f
    ).apply {
        fillAfter = true
        duration = 120
        interpolator = LinearInterpolator()
    }

    fun isVideoViewState() = currentState == STATE_VIDEO

    fun isImageViewState() = currentState == STATE_IMAGE

    fun isCancelViewState() = currentState == STATE_CANCEL

    interface OnTimelyCameraViewListen {
        fun trans2Video()
        fun trans2Image()
        fun trans2Cancel()
    }
}