package com.immomo.momo.message.view;

import android.content.Intent;
import android.view.View;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.immomo.android.module.fundamental.Badge.model.BaseBadgeModel;
import com.immomo.android.router.momo.business.message.ChatRouter;
import com.immomo.framework.imageloader.ImageLoaderX;
import com.immomo.framework.imageloader.ImageType;
import com.immomo.framework.utils.UIUtils;
import com.immomo.momo.R;
import com.immomo.momo.message.contract.ChatEditTopNoticeContract;
import com.immomo.momo.message.presenter.ChatEditTopNoticePresenter;
import com.immomo.momo.service.bean.Message;
import com.immomo.momo.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
public class GreetFeedView implements ChatEditTopNoticeContract.IChatEditTopNoticeView {
    private static final int IconWidth = UIUtils.getPixels(40);

    private View rootView;
    private View playMark, avatarLayout;
    private ImageView noticeAvatar;
    private TextView noticeTitle, noticeDesc;
    private ImageView closeBtn;
    private RelativeLayout noticeContainer;

    private ChatEditTopNoticeContract.EditTopNoticeListener editTopNoticeListener;
    private ChatEditTopNoticeContract.IChatEditTopNoticePresenter editTopNoticePresenter;

    public GreetFeedView(View rootView, Intent intent) {
        this.rootView = rootView;

        editTopNoticePresenter = new ChatEditTopNoticePresenter(this);
        editTopNoticePresenter.init(intent);
    }

    @Override
    public void initEditTopNoticeView() {
        if (!editTopNoticePresenter.hasNotice()) {
            return;
        }

        ViewStub editTopNoticeStub = (ViewStub) findViewById(R.id.view_stub_chat_edit_top_notice);
        if (editTopNoticeStub != null) {
            editTopNoticeStub.inflate();
        }

        noticeContainer = (RelativeLayout) findViewById(R.id.notice_container);
        noticeAvatar = (ImageView) findViewById(R.id.avatar);
        avatarLayout = findViewById(R.id.avatar_layout);
        playMark = findViewById(R.id.iv_play_mark);
        noticeTitle = (TextView) findViewById(R.id.notice_title);
        noticeDesc = (TextView) findViewById(R.id.notice_desc);
        closeBtn = (ImageView) findViewById(R.id.close_btn);

        closeBtn.setOnClickListener(v->{
            onRelativeMessageSend();
        });
    }

    @Override
    public void initHalfModeView() {

    }

    @Override
    public void onRelativeMessageSend() {
        if (noticeContainer != null && noticeContainer.getVisibility() != View.GONE) {
            noticeContainer.setVisibility(View.GONE);
        }

        if (editTopNoticePresenter != null && editTopNoticePresenter.hasNotice()) {
            editTopNoticePresenter.setNoticeSend();
        }
    }

    @Override
    public void onRelativeMessageSendSuccess(Message message) {
        if (editTopNoticeListener != null) {
            editTopNoticeListener.onSendSuccess(message);
        }
    }

    @Override
    public void showTopNoticeView() {
        if (!isRelativeMessageSend()) {
            if (noticeContainer != null && noticeContainer.getVisibility() != View.VISIBLE) {
                noticeContainer.setVisibility(View.VISIBLE);
            }
        }
    }

    @Override
    public void hideTopNoticeView() {
        if (!isRelativeMessageSend()) {
            if (noticeContainer != null && noticeContainer.getVisibility() != View.GONE) {
                noticeContainer.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public void setHalfTitle(CharSequence title) {
    }

    @Override
    public void setVipLabel(List<BaseBadgeModel> models) {

    }

    @Override
    public void showFullMode() {
    }

    @Override
    public void setEditTopNoticeListener(ChatEditTopNoticeContract.EditTopNoticeListener editTopNoticeListener) {
        this.editTopNoticeListener = editTopNoticeListener;
    }

    @Override
    public void destroy() {
        if (editTopNoticePresenter != null) {
            editTopNoticePresenter.destroy();
        }
    }

    @Override
    public boolean isRelativeMessageSend() {
        if (editTopNoticePresenter != null && editTopNoticePresenter.hasNotice()) {
            return editTopNoticePresenter.getNotice().isSend();
        }

        return true;
    }

    @Override
    public boolean isEditTopNoticeVisible() {
        return noticeContainer != null && noticeContainer.getVisibility() == View.VISIBLE;
    }

    @Override
    public void sendRelativeMessage(String text, String remoteId, String source, String chatSource) {
        editTopNoticePresenter.sendRelativeMessage(text, remoteId, source, chatSource);
    }

    @Override
    public String getNoticeId() {
        return editTopNoticePresenter.getNoticeId();
    }

    @Override
    public void setHalfDistance(String distance, boolean show) {
    }

    @Override
    public void setHalfTime(String time) {

    }

    @Override
    public View getHalfTimeView() {
        return null;
    }

    @Override
    public ImageView getPointView() {
        return null;
    }

    @Override
    public View getHalfToolbarView() {
        return null;
    }

    @Override
    public void refreshEditTopNoticeView() {
        ChatRouter.ChatEditNotice notice = editTopNoticePresenter.getNotice();
        if (notice == null || !notice.isValid()) {
            onRelativeMessageSend();
            return;
        }

        if (StringUtils.notEmpty(notice.getTitle())) {
            noticeTitle.setText(notice.getTitle());
            noticeTitle.setVisibility(View.VISIBLE);
        } else {
            noticeTitle.setVisibility(View.GONE);
        }
        noticeDesc.setText(notice.getDesc());
        if (StringUtils.notEmpty(notice.getImageUrl())) {
            ImageLoaderX.load(notice.getImageUrl()).type(ImageType.IMAGE_TYPE_URL).size(IconWidth, IconWidth).cornerRadius(UIUtils.getPixels(4)).showDefault(R.color.color_e6e6e6).into(noticeAvatar);
            avatarLayout.setVisibility(View.VISIBLE);

            if (notice.isVideo()) {
                playMark.setVisibility(View.VISIBLE);
            } else {
                playMark.setVisibility(View.GONE);
            }
        } else {
            avatarLayout.setVisibility(View.GONE);
            playMark.setVisibility(View.GONE);
        }
    }

    @Override
    public void setNotice(String title, String desc, String imgUrl, String feedId, boolean isVideo,String commentId) {

    }

    @Override
    public void setQuoteCloseListener(ChatEditTopNoticeContract.QuoteCloseListener listener) {

    }

    @Override
    public void reset() {
        if (editTopNoticePresenter != null) {
            editTopNoticePresenter.setNotice(null);
        }
        hideTopNoticeView();
    }

    private View findViewById(int id) {
        return rootView.findViewById(id);
    }
}
