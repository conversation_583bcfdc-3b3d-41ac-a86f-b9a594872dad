package com.immomo.momo.message.view

import android.animation.Animator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.drawable.Drawable
import android.graphics.drawable.NinePatchDrawable
import android.util.AttributeSet
import android.view.animation.LinearInterpolator
import android.widget.FrameLayout
import androidx.recyclerview.widget.RecyclerView
import com.immomo.momo.android.view.HandyListView
import com.immomo.momo.expand.allNotNull
import com.immomo.momo.message.MessageBubbleHelper
import com.immomo.momo.service.bean.Message
import com.immomo.momo.util.SimpleAnimatorListener

/**
 * <AUTHOR>
 * @data 2020-08-25.
 */

class MessageBubbleAnimView : FrameLayout {
    private var valueAnimator: ValueAnimator? = null
    private var mFirstFrame: Drawable? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attributeSet: AttributeSet) : super(context, attributeSet)

    constructor(context: Context, attributeSet: AttributeSet, defStyleAttr: Int) : super(
        context,
        attributeSet,
        defStyleAttr
    )

    fun setFirstFrame(frame: Drawable?) {
        mFirstFrame = frame
        background = mFirstFrame
    }

    fun play(message: Message, listView: RecyclerView) {
        //如果是被复用的view；或者要播放的消息，不是当前消息，停止动画
        if (MessageBubbleHelper.playAnimMsgId != message.messageId) {
            valueAnimator?.cancel()
            return
        }
        //超过时间，不播放
        if (MessageBubbleHelper.needStop()) {
            return
        }
        val drawableList = MessageBubbleHelper.getAnimNinePatchDrawableList(message, listView)
        if (drawableList.isNullOrEmpty()) {
            return
        }
        val data = MessageBubbleHelper.getConfigData(message)
        if (data?.hasAnim() != true) {
            return
        }
        allNotNull(drawableList, data.config) { list, config ->
            if (valueAnimator?.isRunning == true) {
                valueAnimator?.cancel()
            }
            valueAnimator = ValueAnimator.ofInt(config.count)
            valueAnimator?.duration = config.duration
            valueAnimator?.repeatCount = -1
            valueAnimator?.interpolator = LinearInterpolator()
            valueAnimator?.addUpdateListener {
                val index = it.animatedValue as Int
                val drawable = getDrawableByIndex(index, list)
                if (drawable != null) {
                    background = drawable
                }
            }
            valueAnimator?.addListener(object : SimpleAnimatorListener() {

                override fun onAnimationEnd(animation: Animator) {
                    super.onAnimationEnd(animation)
                    background = mFirstFrame
                }

                override fun onAnimationRepeat(animation: Animator) {
                    super.onAnimationRepeat(animation)
                    if (MessageBubbleHelper.needStop()) {
                        valueAnimator?.cancel()
                    }
                }

                override fun onAnimationCancel(animation: Animator) {
                    super.onAnimationCancel(animation)
                    background = mFirstFrame
                }

            })
            valueAnimator?.start()
        }
    }


    private fun getDrawableByIndex(index: Int, drawableList: MutableList<NinePatchDrawable>): Drawable? {
        if (drawableList.isNullOrEmpty()) {
            return null
        }
        var finalIndex = index
        if (index < 0) {
            finalIndex = 0
        }
        if (index >= drawableList.size) {
            finalIndex = drawableList.size - 1
        }
        return drawableList[finalIndex]
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        valueAnimator?.cancel()
    }

}
