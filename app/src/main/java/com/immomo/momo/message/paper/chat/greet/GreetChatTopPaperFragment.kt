package com.immomo.momo.message.paper.chat.greet

import android.view.View
import com.immomo.momo.R
import com.immomo.momo.message.paper.PaperConfig
import com.immomo.momo.message.paper.BasePaperContainerFragment
import com.immomo.momo.message.paper.common.ChatAudioCoverPaperFragment
import com.immomo.momo.message.paper.common.GiftEffectPaperFragment

/**
 * <AUTHOR>
 * @data 2020-12-18.
 */

class GreetChatTopPaperFragment : BasePaperContainerFragment() {

    companion object {
        @JvmStatic
        fun newInstance(): GreetChatTopPaperFragment {
            return GreetChatTopPaperFragment()
        }
    }

    override fun getPagers(): MutableList<PaperConfig>? =
            mutableListOf(
                    PaperConfig(ChatAudioCoverPaperFragment.newInstance()),
                    PaperConfig(GiftEffectPaperFragment.newInstance())

            )

    override fun getPageLayout(): Int = R.layout.paper_greet_chat_top

    override fun onPageLoad() {
    }

    override fun initPageViews(contentView: View?) {
    }

}