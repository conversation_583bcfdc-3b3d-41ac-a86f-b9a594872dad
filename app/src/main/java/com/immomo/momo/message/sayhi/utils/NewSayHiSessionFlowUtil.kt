package com.immomo.momo.message.sayhi.utils

import com.immomo.framework.storage.kv.KV
import com.immomo.mmutil.task.MMDispatchers
import com.immomo.momo.maintab.session2.data.manager.SessionManager
import com.immomo.momo.maintab.session2.defs.SayHiContent
import com.immomo.momo.maintab.session2.defs.SayHiSessionDefinition
import com.immomo.momo.message.NewSayHiPageConfigV2
import com.immomo.momo.message.NewSayUIConfigV1.Companion.isUserNewUI
import com.immomo.momo.util.DateUtil
import com.mln.watcher.safe
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Date

/**
 * 消息session上浮
 */
object NewSayHiSessionFlowUtil {

    /**
     * 一天内是否已经检查打招呼强制上浮
     */
    const val KEY_IS_CHECK_SAYHI_FLOW_ONE_DAY: String = "key_is_check_sayhi_flow_one_day"
    const val KEY_ALREADY_DEAL_HI_CARD_TODAY: String = "key_already_deal_hi_card_today" // 进入已经处理了卡片
    private const val TASK_SAYHI_FLOW = "task_sayhi_flow"

    private var isNeedFlowHiSession: Boolean = false
    private var curShownCnt = 0
    private var curTodayTime: String? = ""
    private var launchJob: Job? = null

    fun checkSayhiForceFlow() {
        kotlin.runCatching {
            if (!isUserNewUI()) {
                return
            }
            if (!isNeedFlowHiSession) {
                return
            }
            kotlin.runCatching {
                launchJob?.cancel()
            }
            launchJob = GlobalScope.launch(MMDispatchers.User) {
                if (isCheckSayHiFlowOneDay()) {
                    delay(100)
                    kotlin.runCatching {
                        onCheckSayHiFlowOneDay()
                        SessionManager.get().updateSession(SayHiSessionDefinition.KEY_SAYHI) {
                            if (!it.isSticky) { // 非置顶状态
                                it.recommendTime = System.currentTimeMillis()
                                return@updateSession true
                            }
                            return@updateSession false
                        }
                    }
                }
            }
        }
    }

    /**
     * 检查是否满足未读条件
     */
    fun checkNeedFlowHiSession(sessionEntity: SayHiContent?) {
        if (isUserNewUI() && sessionEntity != null) {
            val hiUserTotalCount = sessionEntity.hiUserTotalCount
            val hiUserTotalCountUsable = sessionEntity.hiUserTotalCountUsable
            isNeedFlowHiSession = hiUserTotalCount <= 0 && hiUserTotalCountUsable > 0
        }
    }

    private fun isCheckSayHiFlowOneDay(): Boolean {
        kotlin.runCatching {
            curTodayTime = getTodayTime()
            if (curTodayTime.isNullOrBlank()) return false
            val dealHiCardTime = getDealHiCardTime()
            val lastShowTimeWithCnt = KV.getUserStr(KEY_IS_CHECK_SAYHI_FLOW_ONE_DAY, "") // 是否已经展示
            if (NewSayHiPageConfigV2.configHiSessionTopDealStop && curTodayTime == dealHiCardTime) { // 当日已经处理了
                return false
            }
            if (lastShowTimeWithCnt.isNullOrBlank()) { // 首次没有展示过
                curShownCnt = 0
                return true
            } else {
                val splitTime = lastShowTimeWithCnt.split("_")
                if (splitTime.size == 2) { // 如果合法的数据存在
                    val lastShowTime = splitTime[0]
                    curShownCnt = splitTime[1].toInt()
                    if (curTodayTime != lastShowTime) { // 如果不是同一天
                        curShownCnt = 0
                        return true
                    } else if (curShownCnt < NewSayHiPageConfigV2.configHiTopCountEveryday) { // 如果是同一天，但是次数够
                        return true
                    }
                } else { // 如果存储的数据异常
                    KV.removeUserValue(KEY_IS_CHECK_SAYHI_FLOW_ONE_DAY)
                    curShownCnt = 0
                    return false
                }
            }
        }
        return false
    }

    /**
     * 展示完弹窗
     */
    private fun onCheckSayHiFlowOneDay() {
        curShownCnt++
        KV.saveUserValue(KEY_IS_CHECK_SAYHI_FLOW_ONE_DAY, "${curTodayTime}_${curShownCnt}")
    }

    private fun getDealHiCardTime() = KV.getUserStr(KEY_ALREADY_DEAL_HI_CARD_TODAY, "")

    fun onDealHiCard() {
        kotlin.runCatching {
            if (isUserNewUI()) {
                KV.saveUserValue(KEY_ALREADY_DEAL_HI_CARD_TODAY, getTodayTime())
            }
        }
    }

    private fun getTodayTime() =
        kotlin.runCatching { DateUtil.formatDate(Date()) }.getOrNull().safe()

    fun resetData() {
        curShownCnt = 0
        isNeedFlowHiSession = false
        onDestroy()
    }

    fun onDestroy() {
        kotlin.runCatching {
            launchJob?.cancel()
        }
    }

}