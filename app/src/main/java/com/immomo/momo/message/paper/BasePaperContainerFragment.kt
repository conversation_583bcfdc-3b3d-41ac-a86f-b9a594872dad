package com.immomo.momo.message.paper

import android.view.View
import androidx.fragment.app.FragmentManager
import com.immomo.momo.expand.allNotNull

/**
 * <AUTHOR>
 * @data 2020-12-18.
 */


abstract class BasePaperContainerFragment : BasePaperFragment() {
    private var pageChildFragmentManager: FragmentManager? = null

    private val paperMap = mutableMapOf<Class<BasePaperFragment>, PaperConfig>()

    override fun initViews(contentView: View?) {
        pageChildFragmentManager = childFragmentManager
        super.initViews(contentView)
        addPapers()
    }

    private fun addPapers() {
        allNotNull(pageChildFragmentManager, getPagers()) { fragmentManager, pagerConfigs ->
            for (paperConfig in pagerConfigs) {
                PaperFragmentHelper.add(
                    fragmentManager,
                    paperConfig.pager,
                    paperConfig.pager.getContainerId()
                )
            }
        }
    }


    /**
     * 动态添加PaperFragment，如果创建了PaperConfig会从缓冲中获取
     */
    fun addPaper(paperConfig: PaperConfig) {
        allNotNull(
            pageChildFragmentManager,
            paperMap.getOrPut(paperConfig.pager.javaClass) { paperConfig }) { fragmentManager, pagerConfig ->
            PaperFragmentHelper.add(
                fragmentManager,
                pagerConfig.pager,
                pagerConfig.pager.getContainerId()
            )
        }
    }

    /**
     * 移除PaperFragment
     */
    fun removePaper(paperClazz: Class<out BasePaperFragment>) {
        allNotNull(
            pageChildFragmentManager,
            paperMap.remove(paperClazz)
        ) { fragmentManager, pagerConfig ->
            PaperFragmentHelper.remove(
                fragmentManager,
                pagerConfig.pager
            )
        }
    }

    override fun getContainerId(): Int = 0


    abstract fun getPagers(): MutableList<PaperConfig>?

}