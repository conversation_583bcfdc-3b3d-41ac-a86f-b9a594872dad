package com.immomo.momo.message.iview;

import com.immomo.momo.group.bean.GroupMyHeaderWearBean;
import com.immomo.momo.message.bean.GroupActiveKitBean;

/**
 * <AUTHOR>
 * @date 2019/3/29
 * @description com.immomo.momo.message.iview
 */
public interface IGroupNoticeView {
    void handleNoReviewFinish(boolean success, int type);

    void handleApplyGroupNoticeFinish(boolean success);

    String getGroupId();

    void showDialog();

    void dismissDialog();

    void showHalfScreenMemberList(boolean isLast, boolean isNewVersion);

    void hideApplyGroupNoticeBar();

    void showTimeTickView(long expiresIn, String tips);

    boolean initApplyGroupNoticeBar();

    void showNoticeApplyView(String name, String url, String content, String btTips1, String btTips2, String mark, String time);

    void showNoticeOnView(String name, String url, String content, String btTips1, String btTips2);

    void showNoticeOffView(String name, String url, String content);

    void hideActiveKitView();

    void showWebAppKeyBoardTips(boolean showGuide);
}
