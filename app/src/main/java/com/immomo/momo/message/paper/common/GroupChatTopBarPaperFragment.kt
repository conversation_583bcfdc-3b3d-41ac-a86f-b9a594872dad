package com.immomo.momo.message.paper.common

import android.os.Bundle
import android.view.View
import com.immomo.momo.group.audio.presentation.view.GroupAudioView
import com.immomo.momo.message.activity.GroupChatActivity
import com.immomo.momo.message.paper.TopBarCallback
import com.immomo.momo.service.bean.TopBarNotice
import com.immomo.momo.util.TopBarNoticeHelper

/**
 * 群通知
 * <AUTHOR>
 * @data 2020-12-18.
 */

open class GroupChatTopBarPaperFragment : BaseChatTopBarPaperFragment() {


    companion object {
        const val KEY_TOP_BAR_NOTICE_SHOW = "key_top_bar_notice_show"
        const val KEY_TOP_BAR_NOTICE = "key_top_bar_notice"

        fun newInstance(): GroupChatTopBarPaperFragment {
            return GroupChatTopBarPaperFragment()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mPaperCommonViewModel?.noticeTopBarCallback = object : TopBarCallback {
            override fun show(hasCallbackShow: <PERSON>ole<PERSON>, bundle: Bundle): Boolean {
                if (hasCallbackShow) {
                    hideTopBar()
                    return true
                }
                if (!bundle.getBoolean(KEY_TOP_BAR_NOTICE_SHOW, false)) {
                    return topBarNoticeLayout?.visibility == View.VISIBLE
                }
                val topBarNotice = bundle.getSerializable(KEY_TOP_BAR_NOTICE) as? TopBarNotice
                return if (topBarNotice != null) {
                    return showTopBar(topBarNotice)
                } else {
                    hideTopBar()
                    false
                }
            }
        }
    }


    override fun checkTopBarNotice(isForceRefresh: Boolean) {
        super.checkTopBarNotice(isForceRefresh)

        getGroupActivity()?.let {
            if (GroupAudioView.changeHeight > 0) {
                return
            }
            it.currentGroup?.let {
                TopBarNoticeHelper.getInstance().checkNoticeUpdate(getTaskTag(),
                        TopBarNotice.TYPE_GROUP, it.gid, isForceRefresh, this)
            }
        }

    }

    override fun showTopBar(topBarNotice: TopBarNotice): Boolean {
        val result = super.showTopBar(topBarNotice)
        if (!result) {
            return false
        }
        getGroupActivity()?.removeTip()
        return true
    }


    private fun getGroupActivity(): GroupChatActivity? {
        (activity as? GroupChatActivity)?.let {
            return it
        }
        return null
    }

    fun getTaskTag(): Any {
        return this.javaClass.name + '@'.toString() + Integer.toHexString(this.hashCode())
    }

    override fun onDestroy() {
        mPaperCommonViewModel?.noticeTopBarCallback = null
        super.onDestroy()
    }

}