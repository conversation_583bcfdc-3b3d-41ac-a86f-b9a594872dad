package com.immomo.momo.message.sayhi.itemmodel.bean

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

data class SayHiRedPacket(
    @Expose @SerializedName("icon") val icon: String?,
    @Expose @SerializedName("title") val title: String?,
    @Expose @SerializedName("subTitle") val subTitle: String?,
    @Expose @SerializedName("price") val price: String?,
    @Expose @SerializedName("timeText") val timeText: String?,
    @Expose @SerializedName("clickGoto") val clickGoto:String?
)
