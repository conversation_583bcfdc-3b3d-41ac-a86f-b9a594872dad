package com.immomo.momo.message.sayhi.contract;

import android.app.Activity;
import android.view.View;

import com.immomo.framework.base.BaseFragment;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiListResult;

import java.util.List;
import java.util.Map;

/**
 * Created by lei.jialin on 2019/4/4.
 */
public interface IStackSayHiContract {
    interface IPresenter {

        void popupSlideGuide(boolean left, String him);

        boolean isCurrentUserMale();

        void onDestroy();

        void init();


        String getTaskTag();

        void postIgnoreOrLike(int likeType, String source, SayHiInfo info, CharSequence messageText,
                              int messageType, Map<String, String> toApiParams, int consumeType, String touchType);

        void setHasPlayedEnterGuide(boolean hasPlayedEnterGuide);

        boolean isHasPlayedEnterGuide();

        void updateToHiIgnore(String momoid);
    }


    interface IView {

        void toastMsg(String msg);

        boolean hideCommentLayout();

        SayHiListResult getSayHiListFormActivity();

        BaseFragment getFragment();

        Activity getActivityMayNull();

        void fillCards(SayHiListResult sayHiListResult, List<SayHiInfo> list);

        void startMainAnimGuideIfNeed();

        void appendCards(SayHiListResult cardResult, List<SayHiInfo> list);

        void startCardRotateFromLeftAnim();

        void showPopupWindow(int type, View.OnClickListener clickListener, List<CharSequence> descList, String clickGoto, String guidephoto, int logType);

        void removeCard(String removeMomoid);

        void removeCardAll();

        boolean updateSingleMessage(String remoteId, String msgId);
    }
}
