package com.immomo.momo.message.sayhi.itemmodel.bean;

import com.immomo.momo.message.sayhi.SayHiArgs;
import com.immomo.momo.service.bean.SayhiSession;

import java.util.HashMap;
import java.util.Map;

public class IgnoreOrPassParam {
    public static class Requst {
        public int like;

        public int consumeType;
        public String remoteId;
        private long lastGreetTime;
        public SayhiSession sayhiSession;
        public boolean isNeedShowToast = false; // 是否需要弹toast

        public Requst(int like, SayhiSession sayhiSession, int consumeType) {
            this.like = like;
            this.sayhiSession = sayhiSession;
            this.remoteId = sayhiSession.getMomoid();
            this.consumeType = consumeType;
        }

        public Requst(String remoteId, int consumeType) {
            this.remoteId = remoteId;
            this.consumeType = consumeType;

        }

        public Requst(int consumeType, long lastGreetTime) {
            this.consumeType = consumeType;
            this.lastGreetTime = lastGreetTime;
        }

        public void setRemoteId(String remoteId) {
            this.remoteId = remoteId;
        }

        public Map<String, String> toMap() {
            Map<String, String> params = new HashMap<>();
            params.put("remoteid", remoteId);
            params.put("like", String.valueOf(like));

            params.put("consume_type", consumeType + "");
            if (lastGreetTime > 0) {
                params.put("last_greet_time", lastGreetTime + "");
            }
            return params;
        }

        public boolean isLike() {
            return this.like == SayHiArgs.LIKE;

        }

        public boolean isIgnore() {
            return this.like == SayHiArgs.IGNORE;
        }
    }

}