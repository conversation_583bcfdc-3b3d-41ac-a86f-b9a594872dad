package com.immomo.momo.message.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ViewConfiguration
import android.widget.LinearLayout


class ClickFixLinearLayout @JvmOverloads constructor(
    context: Context,
    attributes: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attributes, defStyleAttr) {
    var mLongPressTriggered = false

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        if (ev == null) {
            return super.dispatchTouchEvent(ev)
        }
        val mask = ev.actionMasked
        if (mask == MotionEvent.ACTION_DOWN) {
            mLongPressTriggered = false
        }

        var handle = super.dispatchTouchEvent(ev)

        if (mask == MotionEvent.ACTION_DOWN && isLongClickable) {
            scheduleLongPress();
        }
        if (ev.actionMasked == MotionEvent.ACTION_UP
            || ev.actionMasked == MotionEvent.ACTION_CANCEL
        ) {
            removeLongPress()
        }
        return handle

    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (event == null) {
            return super.onTouchEvent(event)
        }
        if (mLongPressTriggered && event.actionMasked == MotionEvent.ACTION_UP) {
            return true
        }
        return super.onTouchEvent(event)
    }

    override fun onInterceptTouchEvent(ev: MotionEvent?): Boolean {
        if (ev == null) {
            return super.onInterceptTouchEvent(ev)
        }
        if (mLongPressTriggered && ev.actionMasked == MotionEvent.ACTION_UP) {
            return true
        }
        return super.onInterceptTouchEvent(ev)
    }

    private fun scheduleLongPress() {
        postDelayed(longClickRunnable, ViewConfiguration.getLongPressTimeout().toLong())
    }

    private fun removeLongPress() {
        removeCallbacks(longClickRunnable)
    }

    private val longClickRunnable = Runnable { mLongPressTriggered = performLongClick() }
}