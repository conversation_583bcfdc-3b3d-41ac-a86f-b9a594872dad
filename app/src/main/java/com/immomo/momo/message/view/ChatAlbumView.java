package com.immomo.momo.message.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;

import com.immomo.momo.R;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * 聊天发送图片
 * 相机和本地图片View
 *
 * <AUTHOR>
 * @data 2019/1/30.
 */
public class ChatAlbumView extends FrameLayout {

    private View mCameraBtn;
    private View mAlbumBtn;

    public ChatAlbumView(@NonNull Context context) {
        this(context, null);
    }

    public ChatAlbumView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ChatAlbumView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        LayoutInflater.from(getContext()).inflate(R.layout.multpic_photo_msg_camera_item, this, true);
        mCameraBtn = findViewById(R.id.multpic_take_photo);
        mAlbumBtn = findViewById(R.id.multpic_choose_photo);
    }

    public void setOnCameraClickListener(View.OnClickListener onCameraClickListener) {
        if (mCameraBtn != null) {
            mCameraBtn.setOnClickListener(onCameraClickListener);
        }
    }

    public void setOnAlbumClickListener(View.OnClickListener onAlbumClickListene) {
        if (mAlbumBtn != null) {
            mAlbumBtn.setOnClickListener(onAlbumClickListene);
        }
    }
}
