package com.immomo.momo.message.view

import android.content.Context
import android.util.AttributeSet
import com.immomo.kliaocore.widget.svga.MomoSVGAImageView
import com.immomo.kliaocore.widget.svga.SVGADrawable

class ScaleTypeSVGAImageView  @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : MomoSVGAImageView(context, attrs, defStyleAttr){
     fun stepToPercentageForScale(percentage: Double, andPlay: Boolean) {
        val drawable = drawable as? SVGADrawable
            ?: return
         drawable.scaleType = scaleType
        var frame = (drawable.videoItem.frames * percentage).toInt()
        if (frame >= drawable.videoItem.frames && frame > 0) {
            frame = drawable.videoItem.frames - 1
        }
        stepToFrame(frame, andPlay)
    }

}