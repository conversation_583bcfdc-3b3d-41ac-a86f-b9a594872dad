package com.immomo.momo.message.task;

import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.message.activity.MultiChatActivity;
import com.immomo.momo.protocol.imjson.log.IMLocalLogType;
import com.immomo.momo.protocol.imjson.log.IMLocalLogger;
import com.immomo.momo.service.bean.Message;

import java.util.List;



/**
 * 群组聊天加载更多的异步任务
 * <AUTHOR>
 * date 2020/8/15
 */
public class MultiChatLoadMoreTask extends MomoTaskExecutor.Task<Object, Object, List<Message>> {

    private MultiChatActivity mActivity;
    private String traceId;


    public MultiChatLoadMoreTask(MultiChatActivity mActivity) {
        this.mActivity = mActivity;
    }

    @Override
    protected void onPreTask() {
    }

    @Override
    protected List<Message> executeTask(Object[] params) throws Exception {

        long beginTime = System.nanoTime();
        List<Message> messages = mActivity.loadMoreMessages(false);
        //单位变成毫秒
        long executeTime = (System.nanoTime() - beginTime) / 1000000;
        if (executeTime > 0 && executeTime < 200) {
            Thread.sleep(200 - executeTime);
        }

        IMLocalLogger.logMsgIds(IMLocalLogType.MainPageLoadMoreMessages, messages);

        return messages;
    }

    @Override
    protected void onTaskSuccess(List<Message> messages) {

        if (!mActivity.hasMoreMessage) {
            mActivity.msgChatRecycler.removeOverScroll();
        } else {
            mActivity.msgChatRecycler.restoreOverScroll();
        }
        mActivity.msgChatRecycler.setRefreshing(false);
        if (messages.size() > 0) {
            mActivity.msgChatData.moreMessageComplete(messages);
        }
        mActivity.msgChatRecycler.tryEndInflateInChain(traceId);
    }

    @Override
    protected void onTaskError(Exception e) {
        super.onTaskError(e);
    }
}