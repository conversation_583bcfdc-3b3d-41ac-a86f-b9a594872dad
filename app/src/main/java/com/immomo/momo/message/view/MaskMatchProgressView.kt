package com.immomo.momo.message.view

import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Guideline
import com.cosmos.mdlog.MDLog
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.R

class MaskMatchProgressView @JvmOverloads constructor(
    context: Context,
    attributes: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attributes, defStyleAttr) {

    private var curAnimation: ValueAnimator? = null

    private var originWidth = 0

    private var lastChangeWidth = 0

    val MAX_PROGRESS = 100

    var progressContainer: CardView

    var progressView: View

    var progressTxt: TextView
    val titleView: TextView by lazy { findViewById<TextView>(R.id.title) }
    val guideline1: Guideline by lazy { findViewById<Guideline>(R.id.guideline1) }
    val guideline2: Guideline by lazy { findViewById<Guideline>(R.id.guideline2) }
    val guideline3: Guideline by lazy { findViewById<Guideline>(R.id.guideline3) }
    val guideline4: Guideline by lazy { findViewById<Guideline>(R.id.guideline4) }

    var progressContainerBg: View

    var isSetProgress = false

    private var lastProgressIndex = 0

    init {
        LayoutInflater.from(context).inflate(R.layout.layout_mask_match_progress_view, this)
        progressContainerBg = findViewById<View>(R.id.progress_view_bg)
        progressContainer = findViewById(R.id.progress_container)
        progressTxt = findViewById(R.id.progress_text)
        progressView = findViewById(R.id.progress_view)
        progressContainerBg.post {
            measureMaxWidth()
            resetProgressSize()
            resetWidthSize(0)
        }
    }

    fun setProgress(progress: Int) {
        progressTxt.text = "$progress%"
        setProgressIndex(progress)
    }

    private fun setProgressIndex(pg: Int) {
        progressContainerBg.post {
            val isFirstSet = lastProgressIndex <= 0
            lastProgressIndex = pg
            if (isFirstSet) {
                resetWidthSize(0)
            }
            var progress = pg
            progress = if (progress > MAX_PROGRESS) {
                MAX_PROGRESS
            } else {
                progress
            }
            var innerW: Int = progressContainerBg.measuredWidth * progress / MAX_PROGRESS
            if (innerW < 30 && innerW != 0) {
                innerW = 40
            }
            if (innerW < 0) {
                innerW = 0
            }
            if (isFirstSet) {
                resetWidthSize(innerW)
                if (pg == 0) {
                    progressView.visibility = GONE
                    lastChangeWidth = innerW
                    return@post
                }
            } else {
                startAnimation(lastChangeWidth, innerW)
            }
            lastChangeWidth = innerW
            progressView.visibility = VISIBLE
        }
    }

    private fun measureMaxWidth() {
        if (originWidth <= 0) {
            originWidth = progressContainerBg.measuredWidth
        }
    }

    private fun resetProgressSize() {
        if (originWidth > 0 && !isSetProgress) {
            isSetProgress = true
            progressView.layoutParams?.also {
                it.width = originWidth
                progressView.layoutParams = it
            }
        }
    }

    private fun startAnimation(start: Int, end: Int) {
        MDLog.i("MaskMatchProgressView", "start=$start  end=$end")
        curAnimation?.cancel()
        curAnimation = ValueAnimator.ofInt(start, end)
        curAnimation?.apply {
            startDelay = 100
            repeatCount = 0
            addUpdateListener(object : ValueAnimator.AnimatorUpdateListener {
                override fun onAnimationUpdate(animation: ValueAnimator) {
                    animation ?: return
                    val widthSize = animation.animatedValue as Int
                    resetWidthSize(widthSize)
                }
            })
            start()
        }
    }

    fun showSmallSize() {
        titleView.textSize = 12f
        progressTxt.minWidth = UIUtils.getPixels(20f)
        progressTxt.layoutParams?.also {
            it.width = ViewGroup.LayoutParams.WRAP_CONTENT
            progressTxt.layoutParams = it
        }
        (progressContainerBg.layoutParams as? LayoutParams?)?.also {
            it.rightMargin = UIUtils.getPixels(10f)
            progressContainerBg.layoutParams = it
        }
        (titleView.layoutParams as? LayoutParams?)?.also {
            it.leftMargin = UIUtils.getPixels(10f)
            titleView.layoutParams = it
        }
        progressTxt.setPadding(0, 0, UIUtils.getPixels(6f), 0)
        guideline1.setGuidelineEnd(0)
        guideline1.setGuidelinePercent(0.3f)
        guideline2.setGuidelinePercent(0.5f)
        guideline3.setGuidelinePercent(0.7f)
        guideline4.setGuidelinePercent(0.9f)
    }

    private fun resetWidthSize(widthSize: Int) {
        progressContainer.layoutParams?.also {
            it.width = widthSize
            progressContainer.layoutParams = it
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        curAnimation?.cancel()
    }

}