package com.immomo.momo.message.task

import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.momo.protocol.http.UserApi

/**
 * Created by <PERSON><PERSON><PERSON> on 2021/6/2.
 */
open class HarassChatTask(var remoteid: String, var block: (Boolean?) -> Unit) :
    MomoTaskExecutor.Task<Any?, Any?, Boolean>("") {


    override fun executeTask(vararg params: Any?): Boolean =
        UserApi.getInstance().getHarassImStatus(remoteid)

    override fun onTaskSuccess(result: Boolean?) {
        super.onTaskSuccess(result)
        block.invoke(result)
    }

    override fun onTaskError(e: Exception?) {
        super.onTaskError(e)
        block.invoke(false)
    }
}

class SwitchHarassChatTask(
    var remoteid: String,
    var status: Boolean,
    var block: (Bo<PERSON>an, Boolean) -> Unit
) :
    MomoTaskExecutor.Task<Any?, Any?, Any?>("") {

    override fun executeTask(vararg params: Any?): Boolean =
        UserApi.getInstance().switchHarassImStatus(remoteid, if (status) 1 else 0)

    override fun onTaskSuccess(result: Any?) {
        super.onTaskSuccess(result)
        block.invoke(status,true)
    }

    override fun onTaskError(e: Exception) {
        super.onTaskError(e)
        block.invoke(!status,false)
    }
}