package com.immomo.momo.message.sayhi.widget

import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.view.animation.Animation.AnimationListener
import android.view.animation.AnimationSet
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatDialog
import androidx.constraintlayout.widget.ConstraintLayout
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.utils.UIUtils
import com.immomo.momo.R
import com.immomo.momo.message.apt.NewSayHiPageConfigV2Getter
import com.immomo.momo.util.MomoKit
import com.immomo.svgaplayer.SVGAAnimListenerAdapter
import com.immomo.svgaplayer.view.MomoSVGAImageView

/**
 * 心动的信号弹窗
 */
class HeartbeatSvipHiDialog(context: Context, private val onClickOk: (() -> Unit)? = null) :
    AppCompatDialog(context, R.style.customDialog), View.OnClickListener {

    companion object {

        const val KEY_SHOW_HEARTBEAT_SVIP_HI_DIALOG = "KEY_SHOW_HEARTBEAT_SVIP_HI_DIALOG"

        @JvmStatic
        fun checkShow(context: Context): HeartbeatSvipHiDialog? {
            val hiDialogShow = KV.getUserBool(KEY_SHOW_HEARTBEAT_SVIP_HI_DIALOG, false)
            var dialog: HeartbeatSvipHiDialog? = null
            kotlin.runCatching {
                if (!hiDialogShow) {
                    KV.saveUserValue(KEY_SHOW_HEARTBEAT_SVIP_HI_DIALOG, true)
                    dialog = HeartbeatSvipHiDialog(context, null)
                    dialog?.show()
                }
            }
            return dialog
        }

    }

    private val animationSet = AnimationSet(true)
    var guideContainer: ConstraintLayout? = null
    var svgaIcon: MomoSVGAImageView? = null
    var tvOK: TextView? = null
    var ivClose: ImageView? = null
    var tvDesc: TextView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_heartbeat_svip_hi)
        setCanceledOnTouchOutside(true)
        window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
        guideContainer = findViewById(R.id.heart_beat_container)
        svgaIcon = findViewById(R.id.svga_icon)
        tvOK = findViewById(R.id.tv_ok)
        ivClose = findViewById(R.id.iv_close)
        tvDesc = findViewById(R.id.tv_desc)
        NewSayHiPageConfigV2Getter.get().heartbeatDialog()?.also {
            if (it.isNotBlank()) {
                tvDesc?.text = it
            }
        }
        tvOK?.setOnClickListener(this)
        ivClose?.setOnClickListener(this)
        setOnDismissListener {
            svgaIcon?.stopAnimCompletely()
            animationSet.cancel()
            guideContainer?.clearAnimation()
        }
        guideContainer?.layoutParams?.also {
            it.width = UIUtils.getScreenWidth() - UIUtils.getPixels(60f)
            guideContainer?.layoutParams = it
        }
        animationSet.duration = 500
        val alphaAnimation = AlphaAnimation(0.0f, 1.0f)
        val scaleAnimation = ScaleAnimation(0f, 1f, 0f, 1f, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f)
        alphaAnimation.duration = 500
        scaleAnimation.duration = 500
        animationSet.addAnimation(alphaAnimation)
        animationSet.addAnimation(scaleAnimation)
        animationSet.setAnimationListener(object : AnimationListener{
            override fun onAnimationStart(animation: Animation?) {
            }

            override fun onAnimationEnd(animation: Animation?) {
                startSvga()
            }

            override fun onAnimationRepeat(animation: Animation?) {
            }
        })
        guideContainer?.startAnimation(animationSet)
    }

    private fun startSvga() {
        svgaIcon?.startSVGAAnimWithListener(
            if (MomoKit.isDarkMode()) "https://s.momocdn.com/s1/u/ebcbiifjc/svip_heartbeat_card_guide_dark.svga"
            else "https://s.momocdn.com/s1/u/ebcbiifjc/svip_heartbeat_card_guide_light.svga",
            1,
            object :
                SVGAAnimListenerAdapter() {
                override fun onFinished() {
                    super.onFinished()
                    svgaIcon?.stopAnimCompletely()
                    svgaIcon?.stepToPercentage(1.0, false)
                }
            })
    }

    override fun onClick(v: View?) {
        if (v?.id == R.id.tv_ok) {
            onClickOk?.invoke()
            dismiss()
        } else if (v?.id == R.id.iv_close) {
            dismiss()
        }
    }

}