package com.immomo.momo.message.sayhi.activity;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Message;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.animation.DecelerateInterpolator;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.immomo.framework.base.BaseActivity;
import com.immomo.framework.base.BaseFragment;
import com.immomo.framework.utils.UIUtils;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.UIHandler;
import com.immomo.momo.android.view.ListEmptyView;
import com.immomo.momo.likematch.tools.BasicAnimHelper;
import com.immomo.momo.likematch.tools.RandomUtils;
import com.immomo.momo.likematch.tools.ResizeScreenHelper;
import com.immomo.momo.likematch.widget.RippleViewStroke;
import com.immomo.momo.likematch.widget.StackAnimHelper;
import com.immomo.momo.message.sayhi.utils.DianDianAnimHelper;
import com.immomo.momo.message.sayhi.widget.PopOutViewRelativeLayout;
import com.immomo.momo.service.bean.User;

import java.util.ArrayList;
import java.util.List;


public class LoadSayHiFragment extends BaseFragment {

    private ImageView userPhoto, userPhotoBg;
    private PopOutViewRelativeLayout photoLayout;
    private AnimatorSet rippleAnimatorSet;
    private AnimatorSet photoExpandAnimatorSet;
    private TextView tipTv, contentTv;
    private Button bottomBtn;
    private RelativeLayout mainLayout;
    private boolean nextShowEmptyView;
    private ListEmptyView emptyView;
    private AnimatorSet rotateAnim;
    private PopOutViewHandler handler;
    private boolean canPlayAnim;
    private boolean emptyDirectGoto;


    public static LoadSayHiFragment newInstance(Bundle bundle) {
        LoadSayHiFragment fragment = new LoadSayHiFragment();
        if (bundle == null) bundle = new Bundle();
        fragment.setArguments(bundle);
        return fragment;
    }

    public LoadSayHiFragment() {
        handler = new PopOutViewHandler(this);
    }

    public void loadData() {
        resetTitle();
        if (nextShowEmptyView) { // 直接展示数据为空，不需要loading动画
            showEmptyView();
            return;
        }
        recoveryTipAndButton();
        startInAnim();
    }


    private void parseBundle() {
        Bundle args = getArguments();
        if (args == null) return;
        nextShowEmptyView = args.getBoolean(HiCardStackActivity.SHOW_EMPTY_VIEW);
        emptyDirectGoto = args.getBoolean(HiCardStackActivity.EMPTY_DIRECT_GOTO);
    }

    public void showEmptyView() {
        nextShowEmptyView = true;
        stopAllAnim();
        toggleContainer(false);
        toggleHistoryEntry(true);
        emptyView.setContentStr(R.string.stack_has_no_say_hi_from_api);
    }

    public void showEmptyViewOrGoHiSessionList() {
        BaseActivity activityMayNull = getActivityMayNull();
        if (activityMayNull instanceof HiCardStackActivity && emptyDirectGoto) {
            ((HiCardStackActivity) activityMayNull).clearUnreadAnGoHistory();
        } else {
            showEmptyView();
        }
    }

    public void toggleHistoryEntry(boolean visible) {
        Activity act = getActivityMayNull();
        if (act instanceof HiCardStackActivity) {
            ((HiCardStackActivity) act).toggleHistoryEntry(visible);
        }
    }


    private void initAnimation() {
        initRippleAnimation();
        initPhotoExpandAnimation();
        initRotateAnim();
        initPopingOutLittlePicAnim();
    }


    private void initPopingOutLittlePicAnim() {
        photoLayout.setTargetView(userPhoto);
        photoLayout.setImgs(new String[]{
                "http://cdnst.momocdn.com/w/u/others/2019/04/19/1555641529511-ic_sayhi_stack_load_pop_pic_1.png",
                "http://cdnst.momocdn.com/w/u/others/2019/04/19/1555641529566-ic_sayhi_stack_load_pop_pic_2.png",
                "http://cdnst.momocdn.com/w/u/others/2019/04/19/1555641529490-ic_sayhi_stack_load_pop_pic_3.png",
                "http://cdnst.momocdn.com/w/u/others/2019/04/19/1555641529536-ic_sayhi_stack_load_pop_pic_4.png",
                "http://cdnst.momocdn.com/w/u/others/2019/04/19/1555641529536-ic_sayhi_stack_load_pop_pic_5.png"
        });
    }

    private void initRotateAnim() {
        List<Animator> animatorList = new ArrayList<>();
        BasicAnimHelper.Rotate(animatorList, userPhoto, 0L, 800L, null, 0f, 10f, -10f, 10f, 0f);
        rotateAnim = new AnimatorSet();
        rotateAnim.playTogether(animatorList);
    }

    private void initRippleAnimation() {
        rippleAnimatorSet = new AnimatorSet();
        AnimatorSet photoBgAnimatorSet = initPhotoBgRippleAnimator();
        rippleAnimatorSet.playTogether(photoBgAnimatorSet);
    }

    private AnimatorSet initPhotoBgRippleAnimator() {
        AnimatorSet photoBgAnimatorSet = new AnimatorSet();
        photoBgAnimatorSet.setInterpolator(new DecelerateInterpolator());
        List<Animator> animatorList = new ArrayList<>();
        View photoRippleView1 = addRippleViewToPhotoLayout();  //两个节奏不同的白色涟漪,其实一直在
        initRippleViewAnimator(photoRippleView1, 1500L, animatorList, 150L, 0f, 1f);
        View photoRippleView2 = addRippleViewToPhotoLayout();
        initRippleViewAnimator(photoRippleView2, 1500L, animatorList, 150L + 300L, 0f, 0.9f);
        photoBgAnimatorSet.playTogether(animatorList);

        return photoBgAnimatorSet;
    }

    private View addRippleViewToPhotoLayout() {
        RelativeLayout.LayoutParams lp = DianDianAnimHelper.LayoutParams.centerRelative(UIUtils.getPixels(269f), UIUtils.getPixels(269f));
        int white189 = Color.argb(100, 225, 80, 190);
        int white69 = Color.argb(30, 225, 80, 190);
        RippleViewStroke rippleView = StackAnimHelper.getRippleView(getContext(), white189, white69, UIUtils.getPixels(2f));
        photoLayout.addView(rippleView, 0, lp);
        return rippleView;
    }

    private void initRippleViewAnimator(final View rippleView, long duration, List<Animator> animatorList, long delay, float startValue, float endValue) {
        BasicAnimHelper.scaleXY(animatorList, rippleView, delay, duration, DianDianAnimHelper.Listener.showOnlyDuringAnim(rippleView), startValue, endValue);
        BasicAnimHelper.alpha(animatorList, rippleView, delay, duration, null, 1.0f, 0f);
    }

    private void initPhotoExpandAnimation() {
        photoExpandAnimatorSet = new AnimatorSet();
        long photoExpendDuration = 300L;

        List<Animator> animList = new ArrayList<>();
        BasicAnimHelper.ScaleAlphaIn(animList, userPhoto);
        BasicAnimHelper.ScaleAlphaIn(animList, userPhotoBg);
        photoExpandAnimatorSet.playTogether(animList);
        photoExpandAnimatorSet.setDuration(photoExpendDuration);
        photoExpandAnimatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(Animator animator) {
                userPhoto.setVisibility(View.VISIBLE);
                userPhotoBg.setVisibility(View.VISIBLE);
                if (getLastRemain() > 0) {
                    tipTv.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onAnimationEnd(Animator animator) {
                if (canPlayAnim) {
                    scheduleNextRipple(400L);
                    scheduleNextPopOutView(800L);
                    scheduleNextRotate(200L);
                }
            }
        });
    }

    /**
     * 搜索动画，动画结束后转入SlideMatchFrament点点页面
     * *第一次进入是卡片落入，之后是涟漪动画
     */

    private void startInAnim() {
        stopAllAnim();
        canPlayAnim = true;
        photoExpandAnimatorSet.start();
    }


    private void startRotateAnimation() {
        stopRotateAnim();
        rotateAnim.start();
    }

    /**
     * 涟漪动画：以下控件必须在显示状态
     */
    public void startRippleAnimation() {
        if (userPhoto == null || userPhotoBg == null || rippleAnimatorSet == null) return;
        userPhoto.setVisibility(View.VISIBLE);
        userPhotoBg.setVisibility(View.VISIBLE);
        rippleAnimatorSet.start();
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        parseBundle();
    }

    @Override
    public void onStop() {
        super.onStop();
        stopAllAnim();
    }

    private void stopAllAnim() {
        canPlayAnim = false;
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }
        stopPhotoExpandAnimation();
        stopRotateAnim();
        stopRippleAnimation();
        if (photoLayout != null) {
            photoLayout.stopAnim();
        }
    }

    private void resetTitle() {
        Activity act = getActivityMayNull();
        if (act instanceof HiCardStackActivity) {
            ((HiCardStackActivity) act).refreshCardCount(0, 0);
        }
    }

    @Override
    protected void onActivityResultReceived(int requestCode, int resultCode, Intent data) {
        switch (requestCode) {
            default:
                break;
        }
    }

    @Override
    protected int getLayout() {
        return R.layout.fragment_load_say_hi;
    }

    @Override
    protected void initViews(View view) {
        userPhoto = (ImageView) findViewById(R.id.match_people_photo);
        userPhotoBg = (ImageView) findViewById(R.id.match_people_photo_bg);
        photoLayout = (PopOutViewRelativeLayout) findViewById(R.id.match_people_photo_rl);
        mainLayout = (RelativeLayout) findViewById(R.id.load_say_hi_container);
        emptyView = (ListEmptyView) findViewById(R.id.empty_view);

        tipTv = (TextView) findViewById(R.id.match_people_tip);
        contentTv = (TextView) findViewById(R.id.match_people_content);
        bottomBtn = (Button) findViewById(R.id.button);
    }

    /* 宽高比3：4配比 */
    private void resetContainer() {
        User currentUser = MomoKit.getCurrentUser(); //男性为蓝色背景
        if (currentUser == null) {
            //FIX: http://crashes.to/s/8bfcd5d823b
            return;
        }
        mainLayout.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                if (Build.VERSION.SDK_INT >= 16) {
                    mainLayout.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                } else {
                    mainLayout.getViewTreeObserver().removeGlobalOnLayoutListener(this);
                }
                LinearLayout.LayoutParams params1 = (LinearLayout.LayoutParams) mainLayout.getLayoutParams();
                params1.width = mainLayout.getWidth();
                params1.height = ResizeScreenHelper.resizeByScreen(params1.width);
                mainLayout.setLayoutParams(params1);
                mainLayout.requestLayout();
            }
        });
    }

    @Override
    protected boolean isNeedLazyLoad() {
        return true;
    }


    @Override
    protected void onLoad() {
        resetContainer();
        recoveryTipAndButton();
        initAnimation();
        loadData();
    }


    public BaseActivity getActivityMayNull() {
        return (BaseActivity) getActivity();
    }

    public void stopRippleAnimation() {
        if (rippleAnimatorSet != null && rippleAnimatorSet.isRunning()) {
            rippleAnimatorSet.cancel();
        }
    }

    public void stopRotateAnim() {
        if (rotateAnim != null && rotateAnim.isRunning()) {
            rotateAnim.cancel();
        }
    }

    public void stopPhotoExpandAnimation() {
        if (photoExpandAnimatorSet != null && photoExpandAnimatorSet.isRunning()) {
            photoExpandAnimatorSet.cancel();
        }
    }


    private void recoveryTipAndButton() {
        //tipTv.setText("正为你推荐附近的人");
        toggleContainer(true);
        contentTv.setVisibility(View.INVISIBLE);
        tipTv.setVisibility(View.INVISIBLE);
        bottomBtn.setVisibility(View.INVISIBLE);
        userPhoto.setVisibility(View.INVISIBLE);
        userPhotoBg.setVisibility(View.GONE);
    }

    private void toggleContainer(boolean showLayout) {
        mainLayout.setVisibility(showLayout ? View.VISIBLE : View.GONE);
        emptyView.setVisibility(showLayout ? View.GONE : View.VISIBLE);
    }

    public int getLastRemain() {
        Activity act = getActivityMayNull();
        if (act instanceof HiCardStackActivity) {
            return ((HiCardStackActivity) act).getLastRemain();
        }
        return 0;
    }

    public static class PopOutViewHandler extends UIHandler<LoadSayHiFragment> {
        public static final int POP_NEXT_ONE = 0x881;
        public static final int START_NEXT_RIPPLE = 0x882;
        public static final int ROTATE_ANIM = 0x883;

        public PopOutViewHandler(LoadSayHiFragment cls) {
            super(cls);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            LoadSayHiFragment view = getRef();
            if (view == null || !view.isPrepared() || view.isDetached() || view.isRemoving()) {
                return;
            }
            switch (msg.what) {
                case POP_NEXT_ONE:
                    view.popNextLittleIcon();
                    view.scheduleNextPopOutView(900L);
                    break;
                case ROTATE_ANIM:
                    view.startRotateAnimation();
                    view.scheduleNextRotate(1500L);
                    break;
                case START_NEXT_RIPPLE:
                    view.startRippleAnimation();
                    view.scheduleNextRipple(2300L);
                    break;
            }
        }
    }

    private void scheduleNextRotate(long delay) {
        if (handler != null) {
            handler.sendEmptyMessageDelayed(PopOutViewHandler.ROTATE_ANIM, delay);
        }
    }


    private void scheduleNextPopOutView(long delay) {
        if (handler != null) {
            handler.sendEmptyMessageDelayed(PopOutViewHandler.POP_NEXT_ONE, delay);
        }
    }

    private void scheduleNextRipple(long delay) {
        if (handler != null) {
            handler.sendEmptyMessageDelayed(PopOutViewHandler.START_NEXT_RIPPLE, delay);
        }
    }

    private void popNextLittleIcon() {
        if (photoLayout != null) {
            photoLayout.nextPopingOutLittlePicture(RandomUtils.random(600L, 1000L), RandomUtils.random(300L, 700L));
        }
    }
}
