package com.immomo.momo.message.task;

import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.mvp.message.view.FlashChatActivity;
import com.immomo.momo.service.bean.Message;

import java.util.ArrayList;
import java.util.List;

import static com.immomo.momo.mvp.message.view.BaseMessageActivity.PAGE_SIZE;


/**
 * 闪聊页面加载更多的异步任务
 * <AUTHOR>
 * date 2020/8/15
 */
public class FlashChatLoadMoreTask extends MomoTaskExecutor.Task<Object, Object, List<Message>> {

    private FlashChatActivity mActivity;
    private String traceId;


    public FlashChatLoadMoreTask(FlashChatActivity mActivity) {
        this.mActivity = mActivity;
    }


    @Override
    protected List<Message> executeTask(Object[] params) throws Exception {

        long beginTime = System.nanoTime();
        List<Message> messages = new ArrayList<Message>();
        // 从数据库获取
        if (mActivity.hasMoreMessage) {
            messages = mActivity.loadMoreMessages(PAGE_SIZE + 1, false, false);
            mActivity.mIMLogRecorder.logLoadMoreMessages(messages);
        }
        //单位变成毫秒
        long executeTime = (System.nanoTime() - beginTime) / 1000000;
        if (executeTime > 0 && executeTime < 200) {
            Thread.sleep(200 - executeTime);
        }

        return messages;
    }

    @Override
    protected void onTaskSuccess(List<Message> messages) {
        if (!mActivity.hasMoreMessage) {
            mActivity.msgChatRecycler.removeOverScroll();
        } else {
            mActivity.msgChatRecycler.restoreOverScroll();
        }
        mActivity.msgChatRecycler.setRefreshing(false);
        if (messages.size() > 0) {
            mActivity.msgChatData.moreMessageComplete(messages);
        }
        mActivity.msgChatRecycler.tryEndInflateInChain(traceId);
    }

    @Override
    protected void onTaskError(Exception e) {
        super.onTaskError(e);
    }
}