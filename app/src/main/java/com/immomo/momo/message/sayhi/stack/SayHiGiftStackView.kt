package com.immomo.momo.message.sayhi.stack

import android.content.Context
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.GestureDetector.SimpleOnGestureListener
import android.view.MotionEvent
import com.immomo.momo.likematch.slidestack.BaseSlideStackView
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiStackCardInfo
import kotlin.math.abs
import kotlin.math.tan

class SayHiGiftStackView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : BaseSlideStackView<SayHiStackCardInfo, SayHIGiftSlideCard>(context, attrs, defStyleAttr) {

    override fun initGestureListener(): GestureDetector.OnGestureListener? {
        return object : SimpleOnGestureListener() {
            override fun onScroll(
                e1: MotionEvent,
                e2: MotionEvent,
                distanceX: Float,
                distanceY: Float
            ): Boolean {
                // dx是滑动速率，不是两次触摸事件的距离
                val deltaY = abs(e2.rawY - e1.rawY)
                val deltaX = abs(e2.rawX - e1.rawX)
                val tan30 = tan(43.0f / 180.0f * Math.PI)
                // 允许横向拖动，禁止纵向拖动，即横向滑动角度小于30degree时可以触发卡片拖拽，touch不往下传递
                return abs(deltaX) > 45 &&  // 最小滑动距离7
                        abs(deltaX) > abs(deltaY) * 1.0f / tan30
            }
        }
    }

    override fun playAnimOnCards(info: SayHiStackCardInfo?, indeXInViewList: Int) {
    }

    override fun recallCardReset(
        recallCard: SayHIGiftSlideCard?,
        indexInDataList: Int,
        info: SayHiStackCardInfo?
    ) {

    }

    override fun isFirstCardAnimCustom(): Boolean {
        return false
    }

}