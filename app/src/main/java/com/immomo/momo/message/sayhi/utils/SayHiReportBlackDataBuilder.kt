package com.immomo.momo.message.sayhi.utils

import com.alibaba.fastjson.JSON
import com.immomo.android.module.specific.data.mapper.safe
import com.immomo.momo.MomoKit
import com.immomo.momo.service.bean.Message
import com.immomo.momo.service.bean.SayhiSession
import org.json.JSONObject

object SayHiReportBlackDataBuilder {

    @JvmStatic
    fun buildGoto(sayhiSession: SayhiSession?): String {
        sayhiSession ?: return ""
        val lastMessage: Message? = sayhiSession.lastMessage
        var msgId: String? = null
        var extraDataString: String? = null
        var msgContent: String? = null
        lastMessage?.also {
            kotlin.runCatching {
                msgId = it.msgId
                msgContent = it.content
                val extraData = it.extraData
                if (extraData != null) {
                    extraDataString = JSON.toJSONString(extraData)
                }
            }
        }
        return buildGoto(
            SayHiReportBlackData(
                sayhiSession.momoid,
                MomoKit.getCurrentOrGuestMomoId(),
                msgId,
                msgContent,
                extraDataString
            )
        )
    }

    @JvmStatic
    fun buildGoto(reportData: SayHiReportBlackData): String {
        return kotlin.runCatching {
            val mainObject = JSONObject()
            // 创建 "m" JSONObject
            val mObject = JSONObject()
            mObject.put("a", "goto_anonymous_report")
            mObject.put("prm", getReportRequestPrm(reportData))
            mainObject.put("m", mObject)
            // 添加 "cb_path" 和 "cb_prm"
            mainObject.put("cb_path", "v2/user/relation/report")
            mainObject.put("cb_prm", getReportRequestCBPrm(reportData))
            // 创建 "ana_param" JSONObject
            val anaParamObject = JSONObject()
            anaParamObject.put("action", "content.report_exposure")
            anaParamObject.put("page", "msg.intercept_sayhi_list")
            anaParamObject.put("rid", "18411")
            // 创建 "param" JSONObject
            val paramObject = JSONObject()
            paramObject.put("message_id", reportData.msgId.safe())
            anaParamObject.put("param", paramObject)
            // 将 "ana_param" 添加到主JSONObject
            mainObject.put("ana_param", anaParamObject)
            mainObject.toString()
        }.getOrNull().safe()
    }

    private fun getReportRequestPrm(reportData: SayHiReportBlackData) = kotlin.runCatching {
        val jsonObject = JSONObject()
        jsonObject.put("remoteid", reportData.from.safe())
        jsonObject.put("porn_type", "api_ecological")
        jsonObject.put("text", "举报并拉黑后，将不会收到对方发来的消息")
        jsonObject.toString()
    }.getOrNull().safe()

    private fun getReportRequestCBPrm(reportData: SayHiReportBlackData) = kotlin.runCatching {
        val jsonObject = JSONObject()
        jsonObject.put("from", reportData.from.safe())
        jsonObject.put("to", reportData.to.safe())
        jsonObject.put("source", "folding_greet")
        jsonObject.put("msgId", reportData.msgId.safe())
        jsonObject.put("msg", reportData.msg.safe())
        jsonObject.put("extraData", reportData.extraData.safe())
        jsonObject.toString()
    }.getOrNull().safe()

}

data class SayHiReportBlackData(
    val from: String?,
    val to: String?,
    val msgId: String?,
    val msg: String?,
    val extraData: String?
)