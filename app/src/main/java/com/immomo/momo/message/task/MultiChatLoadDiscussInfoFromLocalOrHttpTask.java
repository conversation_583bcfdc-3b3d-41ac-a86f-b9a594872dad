package com.immomo.momo.message.task;

import com.cosmos.mdlog.MDLog;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.LogTag;
import com.immomo.momo.discuss.bean.Discuss;
import com.immomo.momo.discuss.service.DiscussService;
import com.immomo.momo.message.activity.MultiChatActivity;
import com.immomo.momo.protocol.http.DiscussApi;



/**
 * 多人聊天加载讨论信息的异步任务
 * <AUTHOR>
 * date 2020/8/15
 */
public class MultiChatLoadDiscussInfoFromLocalOrHttpTask extends MomoTaskExecutor.Task {

    private MultiChatActivity mActivity;
    private boolean needRequestFromApi;
    private boolean bothRelation = false;


    public MultiChatLoadDiscussInfoFromLocalOrHttpTask(MultiChatActivity mActivity, boolean needRequestFromApi) {
        this.mActivity = mActivity;
        this.needRequestFromApi = needRequestFromApi;
    }

    @Override
    protected Object executeTask(Object[] params) {
        // 发起网络请求
        try {
            final String discussId = mActivity.currentDiscuss.id;
            bothRelation = DiscussService.getInstance().isDiscussMember(
                     mActivity.getCurrentUser().momoid,
                    discussId
            ) && mActivity.currentDiscuss.status != Discuss.STATUS_BANDED;
            mActivity.setBothRelation(bothRelation);

            final Discuss dbDiscuss = DiscussService.getInstance().get(discussId);
            if (dbDiscuss != null) {
                mActivity.currentDiscuss = dbDiscuss;
                publishProgress();
            } else {
                if (needRequestFromApi &&
                        DiscussApi.getInstance().downloadDiscussProfile(mActivity.currentDiscuss.id, mActivity.currentDiscuss) >= 0) {
                    publishProgress();
                }
            }
        } catch (Exception e) {
            MDLog.printErrStackTrace(LogTag.Message.BaseMessageActivity, e, "downloadOtherProfile exception");
        }
        return null;
    }

    @Override
    protected void onProgressUpdate(Object[] values) {
        mActivity.refreshTitle();
        mActivity.refreshAdapterUIThread();
        mActivity.updateRelationShip();
    }
}
