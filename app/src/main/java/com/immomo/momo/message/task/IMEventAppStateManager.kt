package com.immomo.momo.message.task

import com.immomo.mmutil.task.ThreadUtils
import com.immomo.momo.MomoApplicationEvent

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/8/11.
 */
object IMEventAppStateManager: MomoApplicationEvent.ApplicationEventListener {

    fun init(){
        MomoApplicationEvent.addEventListener("IMEventAppStateManager", this)
    }

    override fun onAppEnter() {
        //
    }

    override fun onAppExit() {
        ThreadUtils.execute(ThreadUtils.TYPE_RIGHT_NOW, UnreadUploadRunnable())
    }

}