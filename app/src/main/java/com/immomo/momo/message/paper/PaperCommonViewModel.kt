package com.immomo.momo.message.paper

import android.os.Bundle
import androidx.annotation.UiThread
import androidx.lifecycle.ViewModel
import com.immomo.momo.eventbus.DataEvent
import com.immomo.momo.eventbus.PaperDataEvent
import com.immomo.momo.group.audio.presentation.view.OnGroupRoomShowListener
import com.immomo.momo.group.bean.Group
import com.immomo.momo.message.ChatWebPlaceHelper
import com.immomo.momo.message.paper.chat.AudioBackPressedListener
import com.immomo.momo.message.paper.chat.ChatPaperBooleanListener
import com.immomo.momo.message.paper.common.timely.TimelyCameraListener
import com.immomo.momo.message.paper.event.PaperEvent
import de.greenrobot.event.EventBus

/**
 * <AUTHOR>
 * @data 2020-12-23.
 */

class PaperCommonViewModel : ViewModel() {

    var chatGiftPlayingListener: ChatPaperBooleanListener? = null

    fun isChatGiftPlaying(): Boolean {
        return chatGiftPlayingListener?.onResult() ?: false
    }

    var greetGiftPanelShowListener: ChatPaperBooleanListener? = null


    fun isGreetGiftPanelShow(): Boolean {
        return greetGiftPanelShowListener?.onResult() ?: false
    }

    var forbidShowNewMsgView = false

    fun setForbidShowNewMessageView(forbid: Boolean, pageCode: Int) {
        forbidShowNewMsgView = forbid
        if (forbidShowNewMsgView) {
            EventBus.getDefault().post(PaperDataEvent(PaperEvent.PAPER_EVENT_HIDE_NEW_MESSAGE_MASK, "", pageCode))
        }
    }

    var newMessageMaskShowListener: ChatPaperBooleanListener? = null

    fun isNewMessageMaskShow(): Boolean = newMessageMaskShowListener?.onResult() ?: false

    var chatWebPlaceHelper: ChatWebPlaceHelper? = null

    // <editor-fold desc="group audio">
    // 获取群语音顶部引导的高度（未展示的话返回0）
    var currentGroup: Group? = null

    var autoHideGroupAudioFloat: Boolean = false

    var groupAudioOnBackPressedListener: AudioBackPressedListener? = null

    fun getAudioBackPressed(): Boolean? = groupAudioOnBackPressedListener?.onBackPressed()
    // </editor-fold>

    // <editor-fold desc="top bar manager">
    var audioRoomTopBarCallback: TopBarCallback? = null
    var applyGroupTopBarCallback: TopBarCallback? = null
    var audioGuideTopBarCallback: TopBarCallback? = null
    var noticeTopBarCallback: TopBarCallback? = null
    var groupRoomShowListener: OnGroupRoomShowListener? = null

    // 群组聊天页相关top bar，要显示时调用此按先后顺序更新，简化互斥逻辑的处理
    @UiThread
    fun updateTopBar(bundle: Bundle) {
        if (groupRoomShowListener?.onLookerModel() == true) { // 游客模式不展示弹窗
            return
        }
        var hasCallbackShow = false
        hasCallbackShow = audioRoomTopBarCallback?.show(hasCallbackShow, bundle) ?: hasCallbackShow
        hasCallbackShow = applyGroupTopBarCallback?.show(hasCallbackShow, bundle) ?: hasCallbackShow
        hasCallbackShow = audioGuideTopBarCallback?.show(hasCallbackShow, bundle) ?: hasCallbackShow
        noticeTopBarCallback?.show(hasCallbackShow, bundle)
    }
    // </editor-fold>

    /**
     * 存储的数据和变量都要清空
     */
    fun clear() {
        chatGiftPlayingListener = null
    }

    override fun onCleared() {
        super.onCleared()
        clear()
    }

    //实拍时刻
    var timelyCameraListener: TimelyCameraListener? = null
}

interface TopBarCallback {
    /**
     * @param hasCallbackShow 是否已有优先级更高的callback展示了topBar
     * @return 是否已有优先级更高的callback展示了topBar
     */
    fun show(hasCallbackShow: Boolean, bundle: Bundle): Boolean
}
