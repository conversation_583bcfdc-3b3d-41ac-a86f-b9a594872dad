package com.immomo.momo.message.task;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;

import com.immomo.framework.task.BaseDialogTask;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.MomoKit;
import com.immomo.momo.fullsearch.base.FullSearchHelper;
import com.immomo.momo.protocol.imjson.IMJApi;
import com.immomo.momo.protocol.imjson.handler.RetractMessageHandler;
import com.immomo.momo.protocol.imjson.receiver.MessageKeys;
import com.immomo.momo.service.bean.Message;
import com.immomo.momo.service.sessions.MessageServiceHelper;

/**
 * Created by w<PERSON><PERSON><PERSON><PERSON> on 16/4/14.
 */
public class RetractMessageTask extends BaseDialogTask<Object, Object, String> {
    Message message;
    int chatType;

    @Override
    protected boolean mayCancleOnTouchOutSide() {
        return false;
    }

    @Override
    protected boolean mayCancleOnBackPress() {
        return super.mayCancleOnBackPress();
    }

    @Override
    protected String getDispalyMessage() {
        return "正在撤回消息...";
    }

    public RetractMessageTask(Context context, Message message, int chatType) {
        super(context);
        this.message = message;
        this.chatType = chatType;
    }

    @Override
    protected String executeTask(Object... params) throws Exception {
        int resultCode = IMJApi.retractMessage(message, chatType);
        if (resultCode == 200) {
            message.contentType = Message.CONTENTTYPE_MESSAGE_NOTICE;
            message.setContent("你撤回了一条消息");
            MessageServiceHelper.getInstance().update(message);
            sysnQuoteMsgs(chatType, message);
            FullSearchHelper.getInstace().deleteMessage(message);
            return "";
        } else if (resultCode == 401) {
            return "发送超过2分钟的消息，无法被撤回";
        } else {
            return "消息撤回失败";
        }
    }

    /**
     * 更新引用"撤回"的消息
     *
     * @param chatType
     * @param message
     */
    private void sysnQuoteMsgs(int chatType, Message message) {
        String chatId = null;
        if (chatType == Message.CHATTYPE_USER) {
            chatId = message.remoteId;
        } else if (chatType == Message.CHATTYPE_GROUP) {
            chatId = message.groupId;
        }
        if (!TextUtils.isEmpty(chatId)) {
            RetractMessageHandler.dealQuoteMsgRetract(chatId, chatType, message.msgId, message.getTimestampMillis());
        }
    }

    @Override
    protected void onTaskError(Exception e) {
        Toaster.show("消息撤回失败");
    }

    @Override
    protected void onTaskSuccess(String toastMsg) {
        if (TextUtils.isEmpty(toastMsg)) {
            String action = MessageKeys.Action_UpdateMessage;
            Bundle bundle = new Bundle();
            bundle.putInt(MessageKeys.Key_ChatType, message.chatType);
            bundle.putString(MessageKeys.Key_RemoteId, message.remoteId);
            bundle.putString(MessageKeys.Key_MessageId, message.msgId);
            bundle.putBoolean(MessageKeys.Key_Withdraw, true);
            bundle.putParcelable(MessageKeys.Key_MessageObject, message);
            putGroupId(bundle);
            MomoKit.getApp().dispatchMessage(bundle, action);
        } else {
            Toaster.show(toastMsg);
        }
    }

    private void putGroupId(Bundle bundle) {
        if (message.chatType == Message.CHATTYPE_GROUP) {
            bundle.putString(MessageKeys.Key_GroupId, message.groupId);
        } else if (message.chatType == Message.CHATTYPE_DISCUSS || message.chatType == Message.CHATTYPE_LETS_CHAT) {
            bundle.putString(MessageKeys.Key_DiscussId, message.discussId);
        }
    }
}
