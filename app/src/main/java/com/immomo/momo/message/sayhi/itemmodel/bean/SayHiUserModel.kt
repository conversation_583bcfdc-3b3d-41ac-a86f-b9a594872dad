package com.immomo.momo.message.sayhi.itemmodel.bean

import com.immomo.framework.common.UniqueHash
import com.immomo.momo.message.sayhi.utils.NewSayHiConst
import com.immomo.momo.microvideo.model.WithUniqueIdentity
import com.immomo.momo.service.bean.SayhiSession
import com.mln.watcher.safe

data class SayHiUserModel(
    var sayHiSession: SayhiSession,
    val sayHiInfo: SayHiInfo? = null
) : WithUniqueIdentity<SayHiUserModel> {
    override fun uniqueId(): Long {
        return UniqueHash.id(sayHiSession.momoid)
    }

    override fun getClazz(): Class<SayHiUserModel> {
        return SayHiUserModel::class.java
    }
}

/**
 * 打招呼折叠
 */
data class SayHiSessionFoldModel(
    var fromType: Int = -1,
    var sayHiSession: SayhiSession? = null
) : WithUniqueIdentity<SayHiSessionFoldModel> {
    override fun uniqueId(): Long {
        return UniqueHash.id(hashCode())
    }

    override fun getClazz(): Class<SayHiSessionFoldModel> {
        return SayHiSessionFoldModel::class.java
    }
}

/**
 * 顶部折叠卡片聚合
 */
data class SayHiCardContainerModel(
    var unreadMsgCnt: Int = -1,
    var sayHiSession: MutableList<SayHiRecommendSubCardModel> = mutableListOf(),
    var cardShowType: Int = NewSayHiConst.CARD_TYPE_TYPE_NORMAL,
    var curModelState: Int = NewSayHiConst.CARD_MODEL_STATE_LOADING
) : WithUniqueIdentity<SayHiCardContainerModel> {

    override fun uniqueId(): Long {
        return UniqueHash.id(hashCode())
    }

    override fun getClazz(): Class<SayHiCardContainerModel> {
        return SayHiCardContainerModel::class.java
    }

    fun getSafeUnreadMsgCnt(): Int {
        return if (unreadMsgCnt < 0) {
            0
        } else {
            unreadMsgCnt
        }
    }
}

/**
 * 推荐小卡片数据
 */
class SayHiRecommendSubCardModel : WithUniqueIdentity<SayHiUserModel> {

    var cardStackCardInfo: SayHiStackCardInfo? = null

    override fun uniqueId(): Long {
        return UniqueHash.id(cardStackCardInfo?.sayHiInfo?.momoid.safe())
    }

    override fun getClazz(): Class<SayHiUserModel> {
        return SayHiUserModel::class.java
    }
}