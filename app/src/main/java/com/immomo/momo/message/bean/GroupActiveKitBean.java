package com.immomo.momo.message.bean;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2019/4/2
 * @description 活动悬浮窗
 */
public class GroupActiveKitBean {

    private int showGuide;//展示首次引导


    @Expose
    private String name;//活动名称
    @Expose
    private String action;
    @Expose
    private String theme;

    @Expose
    @SerializedName(value = "collapse_image")
    private String collapseImage;//图片地址半屏


    @Expose
    @SerializedName(value = "image")
    private String image;//图片地址全屏
    @Expose
    @SerializedName(value = "icon")
    private String headerIcon;//直播头像

    @Expose
    private String title;//活动标题
    @Expose
    private String desc;//活动内容
    @Expose
    @SerializedName(value = "is_new")
    private int isNew;//老版本是图片，新版本跑马灯文字

    public int getShowGuide() {
        return showGuide;
    }

    public void setShowGuide(int showGuide) {
        this.showGuide = showGuide;
    }

    public String getHeaderIcon() {
        return headerIcon;
    }

    public void setHeaderIcon(String headerIcon) {
        this.headerIcon = headerIcon;
    }

    public int getIsNew() {
        return isNew;
    }

    public void setIsNew(int isNew) {
        this.isNew = isNew;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getTheme() {
        return theme;
    }

    public void setTheme(String theme) {
        this.theme = theme;
    }

    public String getCollapseImage() {
        return collapseImage;
    }

    public void setCollapseImage(String collapseImage) {
        this.collapseImage = collapseImage;
    }
}
