package com.immomo.momo.message.sayhi.utils

import android.text.TextUtils
import com.immomo.android.router.momo.business.abtest.ABConfigRouter
import com.immomo.momo.abtest.config.ABConfigConstant
import com.immomo.momo.newaccount.login.bean.AbConfigBean
import info.xudshen.android.appasm.AppAsm

object SayhiPushGuideABTest {
    /**
     * 招呼push引导ab实验
     */
    fun isShowGuideTest(): Boolean {
        val bean = AppAsm.getRouter(ABConfigRouter::class.java)
            .getExperimentByKey(
                ABConfigConstant.GreetPushGuide.KEY,
                AbConfigBean::class.java
            )
        return bean != null && TextUtils.equals(
            bean.groupId,
            ABConfigConstant.GreetPushGuide.A
        )
    }
}