package com.immomo.momo.message.view;

import android.content.Context;
import android.os.Build;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.immomo.android.module.fundamental.Badge.BadgeDisplayKt;
import com.immomo.android.module.fundamental.Badge.BadgeListThemeMapperKt;
import com.immomo.framework.kotlin.ImageLoader;
import com.immomo.framework.utils.UIUtils;
import com.immomo.momo.R;
import com.immomo.momo.android.view.CircleImageView;
import com.immomo.momo.android.view.badgeview.FeedBadgeView;
import com.immomo.momo.feed.FeedHelperKt;
import com.immomo.momo.greet.GreetHelper;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.util.StringUtils;
import com.immomo.momo.util.view.BadgeView;

/**
 * 招呼半屏弹窗头部内容
 *
 * <AUTHOR>
 * @data 2019/4/11.
 */
public class ChatHalfGreetHeadView extends FrameLayout {
    private CircleImageView mHeaderView;
    private View mOnLineStatusView;
    private TextView mNameView;
    private FeedBadgeView mFeedBadgeView;
    private BadgeView mUniformBadgeView;
    private FeedBadgeView mBottomFeedBadgeView;
    private TextView mDecView;
    private User mUser;
    public View rootView;

    public ChatHalfGreetHeadView(@NonNull Context context) {
        this(context, null);
    }

    public ChatHalfGreetHeadView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ChatHalfGreetHeadView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        if (GreetHelper.showNewSayHiUI()) {
            LayoutInflater.from(getContext()).inflate(R.layout.layout_chat_new_greet_head, this, true);
        } else {
            LayoutInflater.from(getContext()).inflate(R.layout.layout_chat_greet_head, this, true);
        }
        mHeaderView = findViewById(R.id.chat_greet_head);
        mOnLineStatusView = findViewById(R.id.chat_greet_online);
        mNameView = findViewById(R.id.chat_greet_name);
        mFeedBadgeView = findViewById(R.id.chat_greet_badge);
        mUniformBadgeView = findViewById(R.id.view_badge);
        mBottomFeedBadgeView = findViewById(R.id.chat_greet_badge_new);
        mDecView = findViewById(R.id.chat_greet_dec);
        rootView = findViewById(R.id.chat_greet_head_container);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            mUniformBadgeView.setForceDarkAllowed(false);
            mBottomFeedBadgeView.setForceDarkAllowed(false);
            mHeaderView.setForceDarkAllowed(false);
            mFeedBadgeView.setForceDarkAllowed(false);
        }
    }

    public void setData(User user) {
        mUser = user;
        refresh();
    }

    private void refresh() {
        if (mUser == null) {
            return;
        }
        if (mUser.getLoadImageId() != null) {
            ImageLoader.load(mUser.getLoadImageId()).imageType(com.immomo.framework.kotlin.ImageType.ALBUM_250X250).showDefault().into(mHeaderView);
        }
        mNameView.setText(mUser.getDisplayName());
        if (mUser.isMomoVip()) {
            mNameView.setTextColor(UIUtils.getColor(R.color.font_vip_name));
        } else {
            mNameView.setTextColor(UIUtils.getColor(R.color.color_3b3b3b_to_80fff));
        }

        if (GreetHelper.showNewSayHiUI()) {
            mBottomFeedBadgeView.setFeedUser(FeedHelperKt.toFeedBadgeModel(mUser), false);
            if (mUser.uniformLabels != null) {
                BadgeDisplayKt.toDisplay(mUniformBadgeView, BadgeListThemeMapperKt.parseUniformLabelTheme2Model(mUser.uniformLabels), mBottomFeedBadgeView);
            } else {
                mUniformBadgeView.setVisibility(GONE);
                mBottomFeedBadgeView.setVisibility(VISIBLE);
            }

            mFeedBadgeView.setVisibility(GONE);
            mHeaderView.setBorderWidth(UIUtils.getPixels(2));
            mHeaderView.setBorderColor(UIUtils.getColor(R.color.color_fff_to_0000));
        } else {
            mHeaderView.setBorderWidth(UIUtils.getPixels(0));
            mHeaderView.setBorderColor(UIUtils.getColor(R.color.color_fff_to_0000));
            mBottomFeedBadgeView.setVisibility(GONE);
            mUniformBadgeView.setVisibility(GONE);
            mFeedBadgeView.setVisibility(VISIBLE);
            mFeedBadgeView.setFeedUser(FeedHelperKt.toFeedBadgeModel(mUser), false);
        }
        refreshDescView();
        refreshOnlineView();
    }

    private void refreshDescView() {
        if (GreetHelper.showNewSayHiUI()) {
            mDecView.setVisibility(GONE);
            return;
        }
        if (!StringUtils.isEmpty(mUser.gePugOrSignContent())) {
            mDecView.setVisibility(VISIBLE);
            mDecView.setText(mUser.gePugOrSignContent());
        } else {
            mDecView.setVisibility(GONE);
        }
    }

    private void refreshOnlineView() {
        if (mUser.getLocationTimestamp() != null && System.currentTimeMillis() - mUser.getLocationTimestamp().getTime() < 15 * 60 * 1000) {
            switch (mUser.hiddenmode) {
                case User.HIDDENMODE_VISIBLE:
                case User.HIDDENMODE_HIDE_NOT_NEARBY:
                    mOnLineStatusView.setVisibility(VISIBLE);
                    break;
                default:
                    mOnLineStatusView.setVisibility(GONE);
                    break;
            }
        } else {
            mOnLineStatusView.setVisibility(GONE);
        }
    }


}
