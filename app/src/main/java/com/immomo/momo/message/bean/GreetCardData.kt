package com.immomo.momo.message.bean

import com.immomo.momo.gift.bean.BaseGift
import com.immomo.momo.greet.result.GreetMessageResult.HiGiftGuide

/**
 * <AUTHOR>
 * @data 2020-08-25.
 */

open class GreetCardData {
    var pugId: String? = null
    var pugText: String? = null
    var guidanceText: String? = null
}

class GreetTextCardData : GreetCardData() {

}

class GreetImageCardData : GreetCardData() {
    var images: MutableList<String>? = null
}


class GreetGiftCardData : GreetCardData() {
    var giftDatas: MutableList<GreetGiftData>? = null
}

/**
 * 打招呼置顶礼物
 */
class GreetHiTopGiftCardData : GreetCardData() {
    var hiGiftGuide: HiGiftGuide? = null
}

class GreetGiftData {
    var id: String? = null
    var baseGift: BaseGift? = null
    var text: String? = null
}