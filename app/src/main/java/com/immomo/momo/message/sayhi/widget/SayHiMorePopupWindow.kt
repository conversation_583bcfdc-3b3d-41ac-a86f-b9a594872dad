package com.immomo.momo.message.sayhi.widget

import android.content.Context
import android.graphics.PixelFormat
import android.graphics.drawable.BitmapDrawable
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.PopupWindow
import com.immomo.momo.R


class SayHiMorePopupWindow @JvmOverloads constructor(
    val context: Context,
    clickIgnore: () -> Unit,
    clickSpam: () -> Unit
) : PopupWindow(context) {
    private var rootView: View
    private var viewIgnore: View
    private var viewSpam: View
    private var maskView: View? = null
    private var windowManager: WindowManager? = null

    init {
        windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager

        rootView = LayoutInflater.from(context).inflate(R.layout.layout_popup_sayhi_more, null)
        contentView = rootView
        viewIgnore = rootView.findViewById(R.id.tv_ignore)
        viewSpam = rootView.findViewById(R.id.tv_spam)
        viewIgnore.setOnClickListener {
            clickIgnore.invoke()
            dismiss()
        }
        viewSpam.setOnClickListener {
            clickSpam.invoke()
            dismiss()
        }

        setBackgroundDrawable(BitmapDrawable())
        isOutsideTouchable = true
        isFocusable = true
        animationStyle = R.style.Popup_Animation_Publish_Feed_Select

    }

    override fun showAsDropDown(anchor: View?, xoff: Int, yoff: Int, gravity: Int) {
        addMask()
        super.showAsDropDown(anchor, xoff, yoff, gravity)
    }

    private fun addMask() {
        val wl = WindowManager.LayoutParams()
        wl.width = WindowManager.LayoutParams.MATCH_PARENT
        wl.height = WindowManager.LayoutParams.MATCH_PARENT
        wl.format = PixelFormat.TRANSLUCENT //不设置这个弹出框的透明遮罩显示为黑色
        wl.type = WindowManager.LayoutParams.TYPE_APPLICATION_PANEL //该Type描述的是形成的窗口的层级关系
        maskView = View(context)
        maskView?.setBackgroundColor(0x18000000)
        maskView?.fitsSystemWindows = false
        maskView?.setOnKeyListener(View.OnKeyListener { v, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_BACK) {
                removeMask()
                return@OnKeyListener true
            }
            false
        })
        maskView?.setOnClickListener {
            dismiss()
        }
        windowManager?.addView(maskView, wl)
    }

    private fun removeMask() {
        if (null != maskView) {
            windowManager?.removeViewImmediate(maskView)
            maskView = null
        }
    }

    override fun dismiss() {
        super.dismiss()
        removeMask()
    }

}