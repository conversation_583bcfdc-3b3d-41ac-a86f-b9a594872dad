package com.immomo.momo.message.view;

import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.immomo.android.module.fundamental.Badge.model.BaseBadgeModel;
import com.immomo.android.router.momo.business.message.ChatRouter;
import com.immomo.framework.imageloader.ImageLoaderX;
import com.immomo.framework.imageloader.ImageType;
import com.immomo.framework.utils.UIUtils;
import com.immomo.momo.R;
import com.immomo.momo.message.contract.ChatEditTopNoticeContract;
import com.immomo.momo.message.presenter.ChatEditTopNoticePresenter;
import com.immomo.momo.performance.SimpleViewStubProxy;
import com.immomo.momo.service.bean.Message;
import com.immomo.momo.util.StringUtils;
import com.immomo.momo.util.view.BadgeView;

import java.util.List;

/**
 * Created by huang.liangjie on 2017/8/9.
 * <p>
 * Momo Tech 2011-2017 © All Rights Reserved.
 */

public class ChatEditTopNoticeView implements ChatEditTopNoticeContract.IChatEditTopNoticeView {
    private static final int IconWidth = UIUtils.getPixels(40);

    private View rootView;
    private View playMark, avatarLayout;
    private DragArrowRelativeLayout halfChatToolbar;
    private View hideHalfChatViewContainer;
    private TextView halfTitleView;
    private TextView chat_tv_distance;
    private TextView chat_tv_time;
    private ImageView iv_close;
    private ImageView chat_user_status;
    //状态栏上时间和距离中间的点
    private ImageView mImgDotView;
    private ImageView noticeAvatar;
    private TextView noticeTitle, noticeDesc;
    private ImageView closeBtn;
    private RelativeLayout noticeContainer;
    private SimpleViewStubProxy<BadgeView> badgeViewStub;

    private ChatEditTopNoticeContract.EditTopNoticeListener editTopNoticeListener;
    private ChatEditTopNoticeContract.IChatEditTopNoticePresenter editTopNoticePresenter;
    private ChatEditTopNoticeContract.QuoteCloseListener quoteCloseListener;

    public ChatEditTopNoticeView(View rootView, Intent intent) {
        this.rootView = rootView;

        editTopNoticePresenter = new ChatEditTopNoticePresenter(this);
        editTopNoticePresenter.init(intent);
    }

    @Override
    public void setQuoteCloseListener(ChatEditTopNoticeContract.QuoteCloseListener listener) {
        this.quoteCloseListener = listener;
    }

    @Override
    public void setNotice(String title, String desc, String imgUrl, String feedId, boolean isVideo, String commentId) {
        ChatRouter.ChatEditNotice notice = new ChatRouter.ChatEditNotice();
        notice.setTitle(title);
        notice.setDesc(desc);
        notice.setImageUrl(imgUrl);
        notice.setOriginId(feedId);
        notice.setVideo(isVideo);
        notice.setCommemtId(commentId);
        editTopNoticePresenter.setNotice(notice);
    }

    @Override
    public void initEditTopNoticeView() {
        if (!editTopNoticePresenter.hasNotice()) {
            return;
        }

        ViewStub editTopNoticeStub = (ViewStub) findViewById(R.id.view_stub_chat_edit_top_notice);
        if (editTopNoticeStub != null) {
            editTopNoticeStub.inflate();
        }

        noticeContainer = (RelativeLayout) findViewById(R.id.notice_container);
        noticeAvatar = (ImageView) findViewById(R.id.avatar);
        avatarLayout = findViewById(R.id.avatar_layout);
        playMark = findViewById(R.id.iv_play_mark);
        noticeTitle = (TextView) findViewById(R.id.notice_title);
        noticeDesc = (TextView) findViewById(R.id.notice_desc);
        closeBtn = (ImageView) findViewById(R.id.close_btn);

        closeBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onRelativeMessageSend();
            }
        });
    }

    private void initHalfViewStub() {
        ViewStub viewStub = (ViewStub) findViewById(R.id.viewstub_halfchat_head);
        if (null != viewStub) {
            viewStub.inflate();
        }
    }

    @Override
    public void initHalfModeView() {
        initHalfViewStub();
        if (badgeViewStub == null) {
            badgeViewStub = new SimpleViewStubProxy<>((ViewStub) findViewById(R.id.view_stub_badge));
        }
        hideHalfChatViewContainer = findViewById(R.id.hide_half_chat_view_container);
        halfChatToolbar = (DragArrowRelativeLayout) findViewById(R.id.half_chat_tootlbar);
        halfTitleView = (TextView) findViewById(R.id.half_chat_title);
        chat_tv_distance = (TextView) findViewById(R.id.chat_tv_distance);
        chat_tv_time = (TextView) findViewById(R.id.chat_tv_time);
        mImgDotView = (ImageView) findViewById(R.id.userlist_tv_timedriver);
        iv_close = (ImageView) findViewById(R.id.iv_close);
        chat_user_status = (ImageView) findViewById(R.id.chat_user_status);
        final TextView fullChatBtn = (TextView) findViewById(R.id.full_chat_btn);

        initEditTopNoticeView();

        initDragHelper();

        hideHalfChatViewContainer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (editTopNoticeListener != null) {
                    editTopNoticeListener.onHideHalfMode();
                }
            }
        });

        fullChatBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (editTopNoticeListener != null) {
                    editTopNoticeListener.onFullChatClick();
                }
            }
        });

        iv_close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (editTopNoticeListener != null) {
                    editTopNoticeListener.onCloseActivity();
                }
            }
        });

        refreshEditTopNoticeView();

        hideHalfChatViewContainer.setVisibility(View.VISIBLE);
        halfChatToolbar.setVisibility(View.VISIBLE);
    }

    @Override
    public void onRelativeMessageSend() {
        if (noticeContainer != null && noticeContainer.getVisibility() != View.GONE) {
            noticeContainer.setVisibility(View.GONE);
        }

        if (editTopNoticePresenter != null && editTopNoticePresenter.hasNotice()) {
            editTopNoticePresenter.setNoticeSend();
        }
        if (quoteCloseListener != null) {
            quoteCloseListener.onCloseClick();
        }
    }

    @Override
    public void onRelativeMessageSendSuccess(Message message) {
        if (editTopNoticeListener != null) {
            editTopNoticeListener.onSendSuccess(message);
        }
    }

    @Override
    public void showTopNoticeView() {
        if (!isRelativeMessageSend()) {
            if (noticeContainer != null && noticeContainer.getVisibility() != View.VISIBLE) {
                if (editTopNoticeListener != null && editTopNoticePresenter.hasNotice()) {
                    noticeContainer.setVisibility(View.VISIBLE);
                }
            }
        }
    }

    @Override
    public void hideTopNoticeView() {
        if (noticeContainer != null && noticeContainer.getVisibility() != View.GONE) {
            noticeContainer.setVisibility(View.GONE);
        }
    }

    @Override
    public void setHalfTitle(CharSequence title) {
        halfTitleView.setText(title);
    }

    @Override
    public void setVipLabel(List<BaseBadgeModel> models) {
        badgeViewStub.setVisibility(View.VISIBLE);
        badgeViewStub.getStubView().setData(models);
        halfTitleView.invalidate();
    }

    @Override
    public void showFullMode() {
        initHalfViewStub();
        hideHalfChatViewContainer.setVisibility(View.GONE);
        halfChatToolbar.setVisibility(View.GONE);
    }

    @Override
    public void setEditTopNoticeListener(ChatEditTopNoticeContract.EditTopNoticeListener editTopNoticeListener) {
        this.editTopNoticeListener = editTopNoticeListener;
    }

    @Override
    public void destroy() {
        if (editTopNoticePresenter != null) {
            editTopNoticePresenter.destroy();
        }
    }

    @Override
    public void reset() {
        if (editTopNoticePresenter != null) {
            editTopNoticePresenter.setNotice(null);
        }
        hideTopNoticeView();
    }

    @Override
    public boolean isRelativeMessageSend() {
        if (editTopNoticePresenter != null && editTopNoticePresenter.hasNotice()) {
            return editTopNoticePresenter.getNotice().isSend();
        }

        return true;
    }

    @Override
    public boolean isEditTopNoticeVisible() {
        return noticeContainer != null && noticeContainer.getVisibility() == View.VISIBLE;
    }

    @Override
    public void sendRelativeMessage(String text, String remoteId, String source, String chatSource) {
        editTopNoticePresenter.sendRelativeMessage(text, remoteId, source, chatSource);
    }

    @Override
    public String getNoticeId() {
        return editTopNoticePresenter.getNoticeId();
    }

    @Override
    public void setHalfDistance(String distance, boolean show) {
        if (show) {
            chat_tv_distance.setText(distance);
        } else {
            chat_tv_distance.setText("隐身");
            chat_tv_time.setVisibility(View.GONE);
            mImgDotView.setVisibility(View.GONE);
        }
    }

    @Override
    public void setHalfTime(String time) {
        if (!TextUtils.isEmpty(time)) {
            chat_tv_time.setText(time);
        } else {
            chat_tv_distance.setText("隐身");
            chat_tv_time.setVisibility(View.GONE);
            mImgDotView.setVisibility(View.GONE);
        }

    }

    @Override
    public View getHalfTimeView() {
        return chat_tv_time;
    }

    @Override
    public ImageView getPointView() {
        return chat_user_status;
    }

    @Override
    public View getHalfToolbarView() {
        return halfChatToolbar;
    }

    private void initDragHelper() {
        halfChatToolbar.setDragListener(new DragArrowRelativeLayout.DragListener() {
            @Override
            public void onDragEnd() {
                if (editTopNoticeListener != null) {
                    editTopNoticeListener.onHideHalfMode();
                }
            }
        });
    }

    @Override
    public void refreshEditTopNoticeView() {
        ChatRouter.ChatEditNotice notice = editTopNoticePresenter.getNotice();
        if (notice == null || !notice.isValid()) {
            onRelativeMessageSend();
            return;
        }

        if (StringUtils.notEmpty(notice.getTitle())) {
            noticeContainer.setVisibility(View.VISIBLE);
            noticeTitle.setText(notice.getTitle());
            noticeTitle.setVisibility(View.VISIBLE);
        } else {
            noticeContainer.setVisibility(View.GONE);
            noticeTitle.setVisibility(View.GONE);
        }
        noticeDesc.setText(notice.getDesc());
        if (StringUtils.notEmpty(notice.getImageUrl())) {
            ImageLoaderX.load(notice.getImageUrl()).type(ImageType.IMAGE_TYPE_URL).size(IconWidth, IconWidth).cornerRadius(UIUtils.getPixels(4)).showDefault(R.color.color_e6e6e6).into(noticeAvatar);
            avatarLayout.setVisibility(View.VISIBLE);

            if (notice.isVideo()) {
                playMark.setVisibility(View.VISIBLE);
            } else {
                playMark.setVisibility(View.GONE);
            }
        } else {
            avatarLayout.setVisibility(View.GONE);
            playMark.setVisibility(View.GONE);
        }
        showTopNoticeView();
    }

    private View findViewById(int id) {
        return rootView.findViewById(id);
    }

}
