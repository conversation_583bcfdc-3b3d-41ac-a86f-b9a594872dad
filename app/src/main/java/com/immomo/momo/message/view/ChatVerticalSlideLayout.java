package com.immomo.momo.message.view;

import android.content.Context;
import androidx.customview.widget.ViewDragHelper;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.LinearLayout;

import com.immomo.framework.utils.UIUtils;

/**
 * Created by jinx<PERSON><PERSON> on 2017/3/29.
 */

public class ChatVerticalSlideLayout extends LinearLayout {
    private ViewDragHelper mViewDragHelper;
    private VideoVerticalSlideCallback callback;
    private final int DragPosition = 1;

    private int initialY;
    private int lastY;

    private boolean settleDownSuccess;

    public ChatVerticalSlideLayout(Context context) {
        super(context);
        init();
    }

    public ChatVerticalSlideLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public ChatVerticalSlideLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (ev.getAction() == MotionEvent.ACTION_DOWN) {
            initialY = (int) ev.getY();
        } else {
            lastY = (int) ev.getY();
        }
        return mViewDragHelper.shouldInterceptTouchEvent(ev);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        mViewDragHelper.processTouchEvent(event);
        return true;
    }

    @Override
    public void computeScroll() {
        if (mViewDragHelper.continueSettling(true)) {
            postInvalidate();
        } else if (settleDownSuccess) {
            if (callback != null) {
                callback.onSettlingDown();
            }
        }
    }

    private void init() {
        mViewDragHelper = ViewDragHelper.create(this, 0.75f, viewDragCallBack);
    }

    public void setCallback(VideoVerticalSlideCallback callback) {
        this.callback = callback;
    }

    private ViewDragHelper.Callback viewDragCallBack = new ViewDragHelper.Callback() {
        @Override
        public boolean tryCaptureView(View child, int pointerId) {
            boolean canSlideDown = callback != null && callback.canSlideDown();
            boolean eventSlideDown = (lastY - initialY) > 0;
            return canSlideDown && eventSlideDown && indexOfChild(child) == DragPosition && mViewDragHelper.getViewDragState() != ViewDragHelper.STATE_SETTLING;
        }

        @Override
        public void onViewReleased(View releasedChild, float xvel, float yvel) {
            int finalTop = UIUtils.getScreenHeight();
            settleDownSuccess = true;
            if (mViewDragHelper.settleCapturedViewAt(0, finalTop)) {
                postInvalidate();
            }
        }

        @Override
        public void onViewDragStateChanged(int state) {
            super.onViewDragStateChanged(state);
            if (callback != null) {
                callback.onViewDragStateChanged(state);
            }
        }

        @Override
        public void onViewPositionChanged(View changedView, int left, int top, int dx, int dy) {
            int childCount = getChildCount();
            if (childCount > 0) {
                for (int i = 0; i < childCount; i++) {
                    if (i != DragPosition) {
                        View child = getChildAt(i);
                        child.setTop(i > 1 ? getChildAt(i - 1).getBottom() : 0);
                    }
                }
            }
        }

        @Override
        public int getViewHorizontalDragRange(View child) {
            return 0;
        }

        @Override
        public int getViewVerticalDragRange(View child) {
            return child.getHeight();
        }

        @Override
        public int clampViewPositionHorizontal(View child, int left, int dx) {
            return 0;
        }

        @Override
        public int clampViewPositionVertical(View child, int top, int dy) {
            return top > 0 ? top : 0;
        }
    };

    public interface VideoVerticalSlideCallback {
        boolean canSlideDown();

        void onSettlingDown();

        void onViewDragStateChanged(int state);
    }

}
