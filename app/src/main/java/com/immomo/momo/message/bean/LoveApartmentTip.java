package com.immomo.momo.message.bean;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

public class LoveApartmentTip implements Serializable, Parcelable {

    @Expose
    private String remoteid;

    @Expose
    private String momoid;

    @Expose
    private String type;

    @Expose
    private int num;

    @Expose
    private String text;

    @Expose
    @SerializedName("text_icon")
    private String textIcon;

    public String getRemoteid() {
        return remoteid;
    }

    public void setRemoteid(String remoteid) {
        this.remoteid = remoteid;
    }

    public String getMomoid() {
        return momoid;
    }

    public void setMomoid(String momoid) {
        this.momoid = momoid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getTextIcon() {
        return textIcon;
    }

    public void setTextIcon(String textIcon) {
        this.textIcon = textIcon;
    }

    public boolean isRemoved() {
        return "remove".equals(type);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.remoteid);
        dest.writeString(this.momoid);
        dest.writeString(this.type);
        dest.writeInt(this.num);
        dest.writeString(this.text);
        dest.writeString(this.textIcon);
    }

    public LoveApartmentTip() {
    }

    protected LoveApartmentTip(Parcel in) {
        this.remoteid = in.readString();
        this.momoid = in.readString();
        this.type = in.readString();
        this.num = in.readInt();
        this.text = in.readString();
        this.textIcon = in.readString();
    }

    public static final Creator<LoveApartmentTip> CREATOR = new Creator<LoveApartmentTip>() {
        @Override
        public LoveApartmentTip createFromParcel(Parcel source) {
            return new LoveApartmentTip(source);
        }

        @Override
        public LoveApartmentTip[] newArray(int size) {
            return new LoveApartmentTip[size];
        }
    };
}
