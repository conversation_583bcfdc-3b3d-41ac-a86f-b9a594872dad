package com.immomo.momo.message.view;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.os.Build;
import android.os.Message;
import android.os.VibrationEffect;
import android.os.Vibrator;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.cosmos.mdlog.MDLog;
import com.cosmos.runtime.MomoRuntime;
import com.immomo.framework.utils.UIUtils;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.UIHandler;
import com.immomo.momo.agora.mr.conflictHelper.VideoConflictConfig;
import com.immomo.momo.agora.mr.conflictHelper.VideoConflictNewHelper;
import com.immomo.svgaplayer.SVGAAnimListenerAdapter;
import com.immomo.svgaplayer.view.MomoSVGAImageView;

import org.jetbrains.annotations.NotNull;

import java.util.Locale;

import immomo.com.mklibrary.core.jsbridge.IBridge;

public class AudioRecordLayout extends RelativeLayout {
    protected static final String TAG = AudioRecordLayout.class.getSimpleName();
    private static final int STATUS_IDLE = 0;//初始状态
    private static final int STATUS_RECORDING = 1;//正在录制
    private static final int STATUS_CANCELING = 2;//准备取消录制
    private static final int STATUS_ENDING = 3; //松手后的动画

    private static final int MIN_AUDIO_LENGTH = 1000;//语音最短时长 1s
    private static final String RECORD_SVGA_ANIME = "anime_chat_record_and_animoji.svga";

    /**
     * 录制状态
     */
    private int status = STATUS_IDLE;
    /**
     * 语音长度
     */
    private long maxAudioDuration = 60 * 1000L;
    private long tooLongAudioDuration = 55 * 1000L;
    private boolean isRecordingToLong = false;

    private int btnX;
    private int btnY;
    private float endingValue;

    private int strokeColor;
    private int strokeCancelingColor;
    private int audioColorNormal, audioColorWarn, audioColorFailed;
    private int buttonColorNormal;

    private final Paint strokePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private final Paint buttonPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    private ValueAnimator recordingAnimator;
    private final Path arcPath = new Path();
    private final RectF arcRect = new RectF();

    private String audioRecordPrefix = null;

    private TextView audioRecordTip;
    private RelativeLayout audioRecordButton;
    private RelativeLayout audioPressBtn;
    private LinearLayout audioTv;
    private MomoSVGAImageView audioRecordIcon;
    private MomoSVGAImageView audioCancelIcon;
    private MomoSVGAImageView audioPlayIc;
    private ImageView ivShort;
    private View viewBc;
    private int btnWidth = UIUtils.getScreenWidth() - UIUtils.getPixels(30);
    private int btnHeight = UIUtils.getPixels(50);
    private View bcViewBlue;
    private View bcViewBluePressed;
    private ValueAnimator valueAnimator;


    private long startRecordTime = 0;

    private final MyHandler handler = new MyHandler(this);
    private OnRecordListener onRecordListener;

    public AudioRecordLayout(Context context) {
        this(context, null, 0);
    }

    public AudioRecordLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public AudioRecordLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        int strokeWidth = UIUtils.getPixels(1.5f);
        strokeColor = getResources().getColor(R.color.color_brand);
        strokeCancelingColor = getResources().getColor(R.color.color_F46161);
        audioColorNormal = UIUtils.getColor(R.color.color_aaaaaa_to_40fff);
        audioColorWarn = UIUtils.getColor(R.color.color_F46161);
        audioColorFailed = UIUtils.getColor(R.color.c_f5a623);
        buttonColorNormal = UIUtils.getColor(R.color.white);

        strokePaint.setColor(strokeColor);
        strokePaint.setStrokeWidth(strokeWidth);
        strokePaint.setStyle(Paint.Style.STROKE);
        strokePaint.setStrokeJoin(Paint.Join.ROUND);
        strokePaint.setStrokeCap(Paint.Cap.ROUND);
        buttonPaint.setColor(buttonColorNormal);
        buttonPaint.setStyle(Paint.Style.FILL);

        LayoutInflater.from(getContext()).inflate(R.layout.layout_audio_record, this, true);

        audioTv = findViewById(R.id.audio_record_text);
        audioRecordTip = findViewById(R.id.audio_record_tip);
        audioRecordButton = findViewById(R.id.audio_record_button);
        audioRecordIcon = findViewById(R.id.audio_record_icon);
        audioCancelIcon = findViewById(R.id.audio_record_icon_cancel);
        ivShort = findViewById(R.id.iv_short);
        audioPressBtn = findViewById(R.id.ll_audio_btn);
        audioPlayIc = findViewById(R.id.sv_play);
        viewBc = findViewById(R.id.background_view);
        bcViewBlue = findViewById(R.id.bc_view_blue);
        bcViewBluePressed = findViewById(R.id.bc_view_blue_press);
    }

    //<editor-fold desc="Public Methods">
    public boolean cancelRecord() {
        return onRecordCanceled();
    }

    public void setOnRecordListener(OnRecordListener onRecordListener) {
        this.onRecordListener = onRecordListener;
    }

    public void setMaxAudioDuration(long maxAudioDuration) {
        this.maxAudioDuration = maxAudioDuration;
    }

    public void setTooLongAudioDuration(long tooLongAudioDuration) {
        this.tooLongAudioDuration = tooLongAudioDuration;
    }
    //</editor-fold>

    private boolean isInsideButton(int x, int y) {
        return x > btnX && x < btnX + btnWidth && y > btnY && y < btnY + btnHeight;
    }

    private boolean isMoveInsideButton(int x, int y) {
        return y > btnY - UIUtils.getPixels(60);
    }

    //开始录制
    private final Runnable startRecordRunnable = this::onRecordStart;
    private final Runnable recordTooShortRunnable = new Runnable() {
        @Override
        public void run() {
            hideView();
        }
    };

    private void playVibrate() {
        Vibrator vibrator = (Vibrator) MomoKit.getContext().getSystemService(Context.VIBRATOR_SERVICE);
        if (getContext() != null && vibrator != null) {
            try {
                long time = 50;
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                    // 震动强度必须为 1-255 的一个值
                    int amplitude = VibrationEffect.DEFAULT_AMPLITUDE;
                    VibrationEffect vibrationEffect = VibrationEffect.createOneShot(time, amplitude);
                    vibrator.vibrate(vibrationEffect);
                } else {
                    vibrator.vibrate(time);
                }
            } catch (Exception e) {
                MDLog.printErrStackTrace(TAG, e);
            }
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN: {
                if (status != STATUS_IDLE) {
                    return false;
                }
                if (VideoConflictNewHelper.conflictWith(VideoConflictConfig.BusinessType.COMMON)) {
                    return false;
                }

                if (isInsideButton((int) event.getX(), (int) event.getY())) {
                    handler.postDelayed(startRecordRunnable, 100);
                    playVibrate();
                } else {
                    return false;
                }
                break;
            }
            case MotionEvent.ACTION_UP: {
                handler.removeMessages(MyHandler.MSG_AUDIO_TIME_OUT);
                handler.removeMessages(MyHandler.MSG_AUDIO_ALMOST_TIME_OUT);
                handler.removeCallbacks(startRecordRunnable);
                // 手指抬起
                // 移出按钮区域，取消录制
                // 在按钮区域  时长<1s  提示太短，否则发布
                if (status != STATUS_IDLE) {
                    if (isMoveInsideButton((int) event.getX(), (int) event.getY())) {
                        onRecordFinished();
                    } else {
                        onRecordCanceled();
                    }
                } else {
                    resetStatus(false);
                    performClick();
                }
                toBlueState();
                break;
            }
            case MotionEvent.ACTION_MOVE: {
                if (status == STATUS_IDLE) {
                    return false;
                }
                if (isMoveInsideButton((int) event.getX(), (int) event.getY())) {
                    onRecording();
                } else {
                    onRecordCanceling();
                }
                break;
            }
            default: {
                break;
            }
        }
        return true;
    }

    private void toBlueState() {
        audioPlayIc.stopAnimCompletely();
        audioPlayIc.clearAnimation();
        bcViewBlue.clearAnimation();
        if (bcViewBlue.getAlpha() < 1f)  {
            valueAnimator = ValueAnimator.ofFloat(0.1f, 1f);
            valueAnimator.setDuration(300);
            valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator animation) {
                    float animatedValue = (float) animation.getAnimatedValue();
                    bcViewBlue.setAlpha(animatedValue);
                }
            });
            valueAnimator.start();
        } else {
            bcViewBlue.setAlpha(1f);
        }
    }
    
    

    public void animateStartRecording() {
        buttonPaint.setColor(buttonColorNormal);

        if (!isBadModel()) {
            audioRecordIcon.setVisibility(VISIBLE);
            audioCancelIcon.setVisibility(GONE);
            audioRecordIcon.startSVGAAnim("audio_start.svga", -1);
            audioPlayIc.startSVGAAnim("audio_pressing.svga", -1);
        }

        if (recordingAnimator != null && recordingAnimator.isRunning()) {
            recordingAnimator.cancel();
        }
        recordingAnimator = ValueAnimator.ofFloat(0, 360);
        recordingAnimator.setInterpolator(new LinearInterpolator());
        recordingAnimator.addUpdateListener(animation->{
            if (status == STATUS_IDLE) return;

            if (status == STATUS_ENDING) {
                arcPath.reset();
                arcPath.addArc(arcRect, 270F, endingValue);
                invalidate();
                return;
            }
            //防止用户快速按下取消，导致hideView在showView后执行后，控件不可见
            showView();
            endingValue = (float) animation.getAnimatedValue();
            arcPath.reset();
            arcPath.addArc(arcRect, 270F, endingValue);
            invalidate();

            long passedMillsTime = animation.getCurrentPlayTime();
            boolean tooLong = passedMillsTime >= tooLongAudioDuration;
            if (tooLong) {
                long remainTime = maxAudioDuration - passedMillsTime;
                int timeRemainInSecond = (int) (remainTime / 1000);
                int second1 = timeRemainInSecond % 60;
                int minute1 = timeRemainInSecond / 60;
                audioRecordTip.setText(String.format(Locale.US, "%s %2d''", "录制时间还剩", 60 * minute1 + second1));
            } else {
                int timeInSecond = (int) (passedMillsTime / 1000);
                int second = timeInSecond % 60;
                int minute = timeInSecond / 60;
                //1分钟以60"方式展示
                audioRecordTip.setText(String.format(Locale.US, "%s %2d''", audioRecordPrefix, 60 * minute + second));
            }

            if (onRecordListener != null) {
                if (isRecordingToLong != tooLong && tooLong && status == STATUS_RECORDING) {
                    audioRecordTip.setTextColor(audioColorWarn);
                    strokePaint.setColor(strokeCancelingColor);
                }
            }
            isRecordingToLong = tooLong;
        });
        recordingAnimator.setDuration(maxAudioDuration);
        recordingAnimator.start();

        viewBc.setVisibility(VISIBLE);
        AlphaAnimation alphaAnimation = new AlphaAnimation(0f, 1f);
        alphaAnimation.setDuration(200);
        alphaAnimation.setFillAfter(true);
        viewBc.startAnimation(alphaAnimation);
    }

    private void onRecordStart() {
        audioRecordIcon.stopAnimCompletely();
        audioRecordIcon.clearAnimation();
        status = STATUS_RECORDING;
        showView();
        audioRecordPrefix = getResources().getString(R.string.audiorecod_note_scroll);
        audioRecordTip.setTextColor(audioColorNormal);
        strokePaint.setColor(strokeColor);
        animateStartRecording();

        if (onRecordListener != null) {
            onRecordListener.onStart();
        }

        startRecordTime = System.currentTimeMillis();
        //监听录制超时
        handler.removeMessages(MyHandler.MSG_AUDIO_TIME_OUT);
        handler.sendEmptyMessageDelayed(MyHandler.MSG_AUDIO_TIME_OUT, maxAudioDuration);

        //监听录制超时
        handler.removeMessages(MyHandler.MSG_AUDIO_ALMOST_TIME_OUT);
        handler.sendEmptyMessageDelayed(MyHandler.MSG_AUDIO_ALMOST_TIME_OUT, tooLongAudioDuration);
    }

    private void showTooLongToast() {
        Toaster.show("录制时间还剩5秒");
    }

    private void onRecording() {
        if (status == STATUS_RECORDING) {
            return;
        }

        status = STATUS_RECORDING;
        if (!isBadModel()) {
            audioRecordIcon.setVisibility(VISIBLE);
            audioCancelIcon.setVisibility(GONE);
            audioCancelIcon.stopAnimCompletely();
            audioCancelIcon.clearAnimation();
            audioRecordIcon.stopAnimCompletely();
            audioRecordIcon.clearAnimation();
            audioRecordIcon.startSVGAAnim("audio_start.svga", -1);
        }

        audioRecordPrefix = getResources().getString(R.string.audiorecod_note_scroll);
        if (isRecordingToLong) {
            audioRecordTip.setTextColor(audioColorWarn);
        } else {
            audioRecordTip.setTextColor(audioColorNormal);
        }
        strokePaint.setColor(isRecordingToLong ? strokeCancelingColor : strokeColor);
        if (!isRecordingToLong) {
            bcViewBluePressed.clearAnimation();
            bcViewBluePressed.setAlpha(1f);
        }
    }

    private void onRecordCanceling() {
        if (status == STATUS_CANCELING || status == STATUS_ENDING) {
            return;
        }

        status = STATUS_CANCELING;
        if (!isBadModel()) {
            audioRecordIcon.stopAnimation();
            audioRecordIcon.clearAnimation();
            audioRecordIcon.setVisibility(GONE);
            audioCancelIcon.setVisibility(VISIBLE);
            audioCancelIcon.startSVGAAnimWithListener("audio_cancel.svga", 0, new SVGAAnimListenerAdapter() {
                @Override
                public void onStep(int frame, double percentage) {
                    super.onStep(frame, percentage);
                    if (percentage > 0.15d) {
                        audioCancelIcon.stepToPercentage(0.15d, false);
                    }
                }
            });
        }
        audioRecordPrefix = getResources().getString(R.string.audiorecod_note_cancel);
        audioRecordTip.setTextColor(audioColorWarn);
        strokePaint.setColor(strokeCancelingColor);
        bcViewBluePressed.clearAnimation();
        bcViewBluePressed.setAlpha(0.1f);
    }

    public void animateStopRecording() {
        Toaster.show("录制时间过短");
        if (!isBadModel()) {
            audioRecordIcon.setVisibility(VISIBLE);
            audioRecordIcon.stopAnimation();
        }
        handler.postDelayed(recordTooShortRunnable, 500);
    }

    private void onRecordTimeout() {
        if (status == STATUS_RECORDING) {
            onRecordFinished();
        } else if (status == STATUS_CANCELING) {
            onRecordCanceled();
        } else {
            hideView();
        }
    }


    private boolean onRecordCanceled() {
        if (status == STATUS_IDLE) {
            return false;
        }

        if (onRecordListener != null) {
            status = STATUS_ENDING;
            isRecordingToLong = false;
            invalidate();
            if (!isBadModel()) {
                audioRecordIcon.setVisibility(GONE);
                audioCancelIcon.setVisibility(VISIBLE);
                audioCancelIcon.stopAnimation();
                audioCancelIcon.clearAnimation();
                audioCancelIcon.startSVGAAnimAndStepToFrame("audio_cancel.svga", 1, new SVGAAnimListenerAdapter() {
                    @Override
                    public void onFinished() {
                        super.onFinished();
                        onRecordListener.onCanceled();
                        bcViewBluePressed.clearAnimation();
                        bcViewBluePressed.setAlpha(1f);
                        audioCancelIcon.clearAnimation();
                        audioPlayIc.stopAnimCompletely();
                        audioPlayIc.clearAnimation();
                        status = STATUS_IDLE;
                        hideView();
                    }
                }, 5);
            } else {
                onRecordListener.onCanceled();
                resetStatus(false);
            }
        }
        return true;
    }


    private void onRecordFinished() {
        if (status == STATUS_IDLE) {
            hideView();
            return;
        }

        if (System.currentTimeMillis() - startRecordTime < MIN_AUDIO_LENGTH) {
            resetStatus(true);
            if (onRecordListener != null) {
                onRecordListener.onCanceled();
            }
        } else {
            resetStatus(false);
            if (onRecordListener != null) {
                onRecordListener.onFinished();
            }
            hideView();
        }
    }

    private void hideView () {
        buttonPaint.setColor(buttonColorNormal);
        audioRecordTip.setTextColor(audioColorNormal);
        audioRecordTip.setText("按住说话");
        AlphaAnimation alphaAnimation = new AlphaAnimation(1f, 0f);
        alphaAnimation.setDuration(200);
        alphaAnimation.setFillAfter(true);
        viewBc.startAnimation(alphaAnimation);
        audioRecordButton.setVisibility(INVISIBLE);
        ivShort.setVisibility(GONE);
        audioTv.setVisibility(INVISIBLE);
        bcViewBluePressed.setVisibility(GONE);
        bcViewBlue.setVisibility(VISIBLE);
    }


    private void showView() {
        if (audioRecordButton.getVisibility() != VISIBLE) {
            audioRecordButton.setVisibility(VISIBLE);
            audioTv.setVisibility(VISIBLE);
            bcViewBluePressed.setVisibility(VISIBLE);
            bcViewBlue.setVisibility(GONE);
        }
    }

    private void resetStatus(boolean isRecordingToShort) {
        status = STATUS_IDLE;
        isRecordingToLong = false;
        if (isRecordingToShort) {
            audioRecordPrefix = "录制时间太短";
            audioRecordTip.setText(audioRecordPrefix);
            strokePaint.setColor(audioColorFailed);
            audioRecordTip.setTextColor(audioColorNormal);
            ivShort.setImageResource(R.drawable.ic_audio_short);
            ivShort.setVisibility(VISIBLE);
            audioRecordIcon.setVisibility(GONE);
            animateStopRecording();
        }
        invalidate();
        audioPlayIc.stopAnimCompletely();
        audioPlayIc.clearAnimation();

    }

    @Override
    protected void onDetachedFromWindow() {
        if (recordingAnimator != null && recordingAnimator.isRunning()) {
            recordingAnimator.cancel();
        }
        audioPlayIc.clearAnimation();
        bcViewBluePressed.clearAnimation();
        bcViewBlue.clearAnimation();
        if (valueAnimator != null && valueAnimator.isRunning()) {
            valueAnimator.cancel();
        }
        handler.removeCallbacks(recordTooShortRunnable);
        super.onDetachedFromWindow();
    }

    @Override
    protected void dispatchDraw(Canvas canvas) {
        super.dispatchDraw(canvas);
        switch (status) {
            case STATUS_IDLE: {
                break;
            }
            case STATUS_ENDING:
            case STATUS_RECORDING:
            case STATUS_CANCELING: {
                canvas.drawPath(arcPath, strokePaint);
                break;
            }
            default:
                break;
        }
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);
        int buttonLeft = audioRecordButton.getLeft();
        int buttonTop = audioRecordButton.getTop();
        int buttonWidth = audioRecordButton.getWidth();
        int buttonHeight = audioRecordButton.getHeight();
        arcRect.set(buttonLeft, buttonTop, (float) buttonLeft + buttonWidth, (float) buttonTop + buttonHeight);
        btnX = audioPressBtn.getLeft() + UIUtils.getPixels(15);
        btnY = audioPressBtn.getTop() + UIUtils.getPixels(15);
    }

    public interface OnRecordListener {
        void onStart();

        void onFinished();

        void onCanceled();
    }

    private boolean isBadModel() {
        return "ALE-TL00".equals(Build.MODEL) && MomoRuntime.is64Bit();
    }

    private static class MyHandler extends UIHandler<AudioRecordLayout> {
        /**
         * 语音录制超时
         */
        private static final int MSG_AUDIO_TIME_OUT = 1;

        /**
         * 语音倒计时
         */
        private static final int MSG_AUDIO_ALMOST_TIME_OUT = 2;

        public MyHandler(AudioRecordLayout cls) {
            super(cls);
        }

        @Override
        public void handleMessage(@NotNull Message msg) {
            if (getRef() == null) {
                return;
            }
            if (msg.what == MSG_AUDIO_TIME_OUT) {
                getRef().onRecordTimeout();
                getRef().toBlueState();
            } else if (msg.what == MSG_AUDIO_ALMOST_TIME_OUT) {
                getRef().showTooLongToast();
            }
            super.handleMessage(msg);
        }
    }
}