package com.immomo.momo.message.sayhi.activity

import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.os.Message
import android.view.Gravity
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.immomo.framework.account.MessageManager
import com.immomo.framework.base.BaseActivity
import com.immomo.framework.base.IStepConfigDataProvider
import com.immomo.framework.cement.CementModel
import com.immomo.framework.cement.SimpleCementAdapter
import com.immomo.framework.cement.eventhook.OnClickEventHook
import com.immomo.framework.rxjava.interactor.CommonSubscriber
import com.immomo.framework.storage.kv.KV
import com.immomo.framework.storage.preference.SPKeys
import com.immomo.framework.utils.StatusBarUtil
import com.immomo.framework.utils.UIUtils
import com.immomo.framework.view.recyclerview.LoadMoreRecyclerView
import com.immomo.framework.view.recyclerview.itemdecoration.LinearPaddingItemDecoration
import com.immomo.framework.view.toolbar.CompatAppbarLayout
import com.immomo.lcapt.evlog.EVLog
import com.immomo.mmstatistics.event.Event
import com.immomo.mmutil.UIHandler
import com.immomo.mmutil.app.AppContext
import com.immomo.mmutil.task.MMThreadExecutors.Main
import com.immomo.mmutil.task.MMThreadExecutors.User
import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.mmutil.task.ThreadUtils
import com.immomo.mmutil.toast.Toaster
import com.immomo.momo.MomoKit
import com.immomo.momo.R
import com.immomo.momo.UrlConstant
import com.immomo.momo.android.broadcast.FriendListReceiver
import com.immomo.momo.android.view.dialog.MAlertDialog
import com.immomo.momo.businessmodel.statistics.BusinessConfig
import com.immomo.momo.businessmodel.statistics.SayHiRecListConfig
import com.immomo.momo.common.itemmodel.LoadMoreItemModel
import com.immomo.momo.eventbus.DataEvent
import com.immomo.momo.eventbus.EventKeys
import com.immomo.momo.globalevent.GlobalEventManager
import com.immomo.momo.gotologic.GotoDispatcher
import com.immomo.momo.innergoto.helper.NavigateHelper
import com.immomo.momo.maintab.sessionlist.ISessionListView2
import com.immomo.momo.message.ChatHelper
import com.immomo.momo.message.activity.ChatActivity
import com.immomo.momo.message.bean.ChatBusinessType
import com.immomo.momo.message.helper.FrequentPreferenceHelper
import com.immomo.momo.message.sayhi.FemaleSelectQuestionDialog
import com.immomo.momo.message.sayhi.FemaleSelectQuestionDialog.onClickListener
import com.immomo.momo.message.sayhi.SayHiArgs
import com.immomo.momo.message.sayhi.itemmodel.SayHiUserItemModel
import com.immomo.momo.message.sayhi.itemmodel.bean.LikeSayHi
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo
import com.immomo.momo.message.sayhi.presenter.ISayHiListPresenter
import com.immomo.momo.message.sayhi.presenter.SayHiListPresenter
import com.immomo.momo.message.sayhi.task.ReportForbiddenTask
import com.immomo.momo.message.sayhi.utils.IGreetLog
import com.immomo.momo.message.sayhi.utils.SayHiConst
import com.immomo.momo.message.sayhi.utils.SayHiQuickReplyHelperView
import com.immomo.momo.message.sayhi.utils.SayHiReportUtil
import com.immomo.momo.message.sayhi.widget.SayHiActionDialog
import com.immomo.momo.message.sayhi.widget.SayHiMorePopupWindow
import com.immomo.momo.message.task.ChatLogRunnable
import com.immomo.momo.messages.service.SingleMsgService
import com.immomo.momo.mvp.common.presenter.ITipsPresenter
import com.immomo.momo.personalprofile.bean.PersonalProfileQuestion
import com.immomo.momo.platform.utils.PlatformReportHelper
import com.immomo.momo.protocol.imjson.receiver.MessageKeys
import com.immomo.momo.service.bean.SayhiSession
import com.immomo.momo.service.bean.User
import com.immomo.momo.service.user.UserService
import com.immomo.momo.setting.BasicUserInfoUtil.getFollowerCount
import com.immomo.momo.setting.BasicUserInfoUtil.getFollowingCount
import com.immomo.momo.setting.BasicUserInfoUtil.getNewFansCount
import com.immomo.momo.setting.BasicUserInfoUtil.setFollowerCount
import com.immomo.momo.statistics.EVPage
import com.immomo.momo.statistics.logrecord.viewhelper.RecyclerViewExposureLogHelper
import com.immomo.momo.util.StringUtils
import com.immomo.momo.util.dispose
import de.greenrobot.event.EventBus
import io.reactivex.Flowable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.lang.ref.WeakReference
import java.util.Arrays
import java.util.Date

class SayHiListActivity : BaseActivity(), IStepConfigDataProvider<BusinessConfig>,
    ISayHiListView, MessageManager.MessageSubscriber, GlobalEventManager.Subscriber {
    companion object {
        private val REFRESH_COUNT_BOTTON = 7168
        var Action_Session_REPLAY = "action.session.replayed"

    }

    private var mListView: LoadMoreRecyclerView? = null
    private var mMenuItemReply: View? = null
    private var mMenuItemMore: View? = null
    private var mTitle: TextView? = null
    private var appbarLayout: CompatAppbarLayout? = null

    private val mPresenter: ISayHiListPresenter = SayHiListPresenter(this)
    private val handler = SessionHandler(this)
    private var userService = UserService.getInstance()


    private var mLayoutManager: LinearLayoutManager? = null


    private var femaleSelectQuestionDialog: FemaleSelectQuestionDialog? = null
    private var msgLike = ""

    private var mSayHiMorePopupWindow: SayHiMorePopupWindow? = null

    /**
     * 当前被举报需要处理的用户
     */
    private var reportOptionUserId: String? = null
    private var mDisposable: Disposable? = null
    private var sayHiQuickReplyHelperView: SayHiQuickReplyHelperView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        SayHiQuickReplyHelperView.init(this)
        setContentView(R.layout.activity_sayhi_rec_list)
        SayHiConst.initPage()
        window.statusBarColor = resources.getColor(R.color.color_F9F9F9)
        // 移除通知栏提示
        MomoKit.getApp().removeSayhiNotify()

        initView()
        initInternal()
        initEvents()
        loadData()
        initReceiver()
    }

    override val stepConfigData: BusinessConfig
        get() = SayHiRecListConfig

    override fun onResume() {
        super.onResume()
        sayHiQuickReplyHelperView?.onResume()
    }

    override fun onPause() {
        if (isInitialized) {
            mPresenter.updateUnreadStatus()
        }
        sayHiQuickReplyHelperView?.onPause()
        super.onPause()
    }

    override fun onDestroy() {
        mPresenter.destroy()
        MessageManager.unregisterMessageReceiver(hashCode())
        EventBus.getDefault().unregister(this)
        GlobalEventManager.getInstance().unregister(this, GlobalEventManager.EVN_NATIVE)
        SayHiConst.release()
        super.onDestroy()
    }

    override fun getPVPage(): Event.Page? {
        return EVPage.Msg.SayhiCard
    }

    override fun onSaveInstanceState(outState: Bundle) {
        outState.putString("reportOptionUserId", reportOptionUserId)
        super.onSaveInstanceState(outState)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        try {
            reportOptionUserId = savedInstanceState.getString("reportOptionUserId")
        } catch (e: Exception) {
            // nothing
        }
        super.onRestoreInstanceState(savedInstanceState)
    }

    private fun initView() {
        mTitle = findViewById(R.id.tv_title) as TextView?
        mListView = findViewById(R.id.list_hi) as LoadMoreRecyclerView?
        mLayoutManager = LinearLayoutManager(this)
        mListView?.layoutManager = mLayoutManager
        mListView?.itemAnimator = null
        mListView?.addItemDecoration(
            LinearPaddingItemDecoration(
                UIUtils.getPixels(10f),
                UIUtils.getPixels(40f), UIUtils.getPixels(20f)
            )
        )
        mListView?.setVisibleThreshold(2)
        mListView?.setOnLoadMoreListener { mPresenter.loadMore() }
        mListView?.addOnScrollListener(RecyclerViewExposureLogHelper.getOnScrollListener())
        showMessageCount()

        mMenuItemReply = findViewById(R.id.action_sayhi_reply)
        mMenuItemReply?.visibility = if (!MomoKit.isManUser()) View.VISIBLE else View.GONE
        mMenuItemMore = findViewById(R.id.action_sayhi_more)
        appbarLayout = findViewById(R.id.appbar_id) as CompatAppbarLayout?
        val statusBarHeight = StatusBarUtil.getStatusBarHeight(AppContext.sContext)
        (appbarLayout?.layoutParams as? ConstraintLayout.LayoutParams)?.also {
            it.topMargin = statusBarHeight
            appbarLayout?.layoutParams = it
        }
        sayHiQuickReplyHelperView =
            findViewById(R.id.quick_reply_view) as SayHiQuickReplyHelperView?
    }

    private fun initInternal() {
        mPresenter.init()
    }

    override fun getIntent(): Intent? {
        return super.getIntent()
    }

    private fun initEvents() {
        mMenuItemReply?.setOnClickListener {
            showReplySetView()
        }
        mMenuItemMore?.setOnClickListener {
            showMoreDialog()
        }
        GlobalEventManager.getInstance().register(this, GlobalEventManager.EVN_NATIVE)
    }

    //</editor-fold>
    fun onEvent(event: DataEvent<String>) {
        if (event.equals(EventKeys.Block.ADD_BLOCK)) {
            val momoid = event.data
            if (StringUtils.notEmpty(momoid)) {
                mPresenter.dealSessionBroadcast(momoid, true)
            }
        }
        if (event.equals(EventKeys.SayHiCard.CARD_RELOAD)) {
            loadData(true)
        }
    }

    override fun setAdapter(adapter: SimpleCementAdapter) {
        adapter.setOnItemClickListener { itemView, viewHolder, position, model ->
            if (model is LoadMoreItemModel) {
                mPresenter.loadMore()
            }

            if (model is SayHiUserItemModel) {
                EVLog.create(IGreetLog::class.java).clickSayhi(
                    model.model.sayHiSession.momoid,
                    if (model.model.sayHiSession.isFromGift) 1 else 0,
                    if (model.model.sayHiInfo?.feedImageList?.isNotEmpty() == true) 1 else 0,
                    if (model.model.sayHiInfo?.hasMarks() == true) 1 else 0
                )
                onItemClickSession(model)
            }
        }

        adapter.addEventHook(object :
            OnClickEventHook<SayHiUserItemModel.ViewHolder>(SayHiUserItemModel.ViewHolder::class.java) {
            override fun onClick(
                view: View,
                viewHolder: SayHiUserItemModel.ViewHolder,
                position: Int,
                model: CementModel<*>
            ) {
                if (model !is SayHiUserItemModel) {
                    return
                }

                if (view == viewHolder.chatBtn) { // 点击了聊天按钮
                    SayHiReportUtil.listChat(model.model.sayHiSession)
                    sayHiQuickReplyHelperView?.showComment(
                        model.model.sayHiSession,
                        model.model.sayHiInfo
                    )
                } else if (view == viewHolder.confirmBtn) { // 点击了确认按钮
                    SayHiReportUtil.listConfirm(model.model.sayHiSession)
                    mPresenter.postIgnoreOrLike(
                        SayHiArgs.LIKE,
                        model.model.sayHiSession,
                        LikeSayHi.Requst.TYPE_RIGHT
                    )
                }
            }

            override fun onBindMany(viewHolder: SayHiUserItemModel.ViewHolder): MutableList<out View>? {
                return Arrays.asList<View>(
                    viewHolder.viewFeedReply,
                    viewHolder.viewPhotoFeed,
                    viewHolder.confirmBtn,
                    viewHolder.chatBtn
                )
            }

        })

        adapter.setOnItemLongClickListener { itemView, viewHolder, position, model ->
            if (model is SayHiUserItemModel) {
                showActionDialog(model.model.sayHiSession, model.model.sayHiInfo)
            }
            true
        }
        mListView?.let {
            adapter.registerAdapterDataObserver(
                RecyclerViewExposureLogHelper.getAdapterDataObserver(
                    it
                )
            )
        }

        mListView?.adapter = adapter
    }

    override fun scrollToTop() {
        mListView?.scrollToPosition(0)
    }

    private fun loadData(isReload: Boolean = false) {
        mPresenter.loadData(isReload)
    }

    private fun initReceiver() {
        MessageManager.registerMessageReceiver(
            hashCode(), this, 700,
            MessageKeys.Action_HiMessage,
            MessageKeys.Action_UpdateMessage,
            ISessionListView2.Action_SessionChanged,
            Action_Session_REPLAY
        )
        EventBus.getDefault().register(this)
    }

    private fun showMessageCount() {
        mTitle?.text =
            if (mPresenter.getAllUnreadUserCount() > 0) {
                "招呼 (" + mPresenter.getAllUnreadUserCount() + ")"
            } else {
                "招呼"
            }
    }

    private fun onItemClickSession(model: SayHiUserItemModel) {
        val hiSession = model.model.sayHiSession
        if (StringUtils.notEmpty(model.model.sayHiInfo?.cellgoto) && !model.hasReply) {
            GotoDispatcher.action(model.model.sayHiInfo?.cellgoto, thisActivity()).execute()
        } else {
            val intent = Intent(this, ChatActivity::class.java)
            intent.putExtra(ChatActivity.REMOTE_USER_ID, hiSession.momoid)
            if (!model.hasReply) {
                intent.putExtra(ChatHelper.KEY_IS_FROM_HELLO_LIST, 1)
                intent.putExtra(ChatHelper.SHOW_GREET_REPLY, "greetFromHelloList")
            }

            startActivity(intent)
        }

        mPresenter.updateCountTitleFromDB()
        mPresenter.consumeSayhi(hiSession.momoid, LikeSayHi.Requst.TYPE_READ)
    }

    private fun showHarass() {
        NavigateHelper.startWebview(
            thisActivity(), UrlConstant.URL_HARASS_GREETING_INTRO
                    + "&annoy_status=" + if (FrequentPreferenceHelper.i().isHarassGreetingOpen) 1 else 0
        )
    }

    private fun showMoreDialog() {
        if (mSayHiMorePopupWindow == null) {
            mSayHiMorePopupWindow = SayHiMorePopupWindow(thisActivity(), {
                showClearMsgDialog()
            }, {
                showHarass()
            })
            mSayHiMorePopupWindow?.width = UIUtils.getPixels(150f)
            mSayHiMorePopupWindow?.height = UIUtils.getPixels(120f)
        }

        mMenuItemMore?.let {
            mSayHiMorePopupWindow?.showAsDropDown(
                it,
                UIUtils.getPixels(-2f),
                UIUtils.getPixels(10f),
                Gravity.END
            )
        }

    }

    private fun showClearMsgDialog() {
        val dialog = MAlertDialog.makeConfirm(
            thisActivity(),
            "本操作将清除当前未读消息提示，确认进行此操作吗?",
            { _: DialogInterface?, _: Int ->
                mPresenter.clearUnread()
                mPresenter.updateCountTitleFromDB()
                mPresenter.consumeIgnore()

            }
        ) { _: DialogInterface?, _: Int -> closeDialog() }
        dialog.setSupportDark(true)
        showDialog(dialog)
    }

    private fun showReplySetView() {
        if (MomoKit.getCurrentUser()?.isFemale == true) {
            gotoFemaleDialog()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (PlatformReportHelper.ACT_RES_REPORT == requestCode && data != null) {
            val status = data.getIntExtra(
                PlatformReportHelper.WEB_RESULT_STATUS,
                PlatformReportHelper.ACTION_FAILED
            )
            if (status == PlatformReportHelper.ACTION_SUCCESS) {
                onReportSuccess(data)
            }
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    private fun onReportSuccess(data: Intent?) {
        if (data == null) {
            return
        }
        val action = data.getIntExtra(PlatformReportHelper.WEB_RESULT_ACTION, -1)
        reportUserChat(action, reportOptionUserId)
    }

    private fun reportUserChat(action: Int, curUserid: String?) {
        if (curUserid.isNullOrBlank()) return
        mPresenter.consumeSayhi(curUserid, LikeSayHi.Requst.TYPE_REPORT)
        if (action == PlatformReportHelper.ACTION_BLOCK || action == PlatformReportHelper.ACTION_REPORT_AND_BLOCK) {
            Toaster.showInvalidate("拉黑成功")
            afterBlockAndReportSuccess(curUserid)
        } else {
            Toaster.showInvalidate("举报成功")
            SingleMsgService.getInstance().updateSayhiInnerIgnore(curUserid)
            mPresenter.dealSessionBroadcast(curUserid, true)
        }
    }

    fun afterBlockAndReportSuccess(reportUserid: String?) {
        mPresenter.deleteSession(reportUserid, true)
        blockUser()
    }

    private fun blockUser() {
        dispose(mDisposable)
        mDisposable = Flowable.fromCallable<User> {
            val user = userService.getSimpleUser(reportOptionUserId)
            if (user != null) {
                user.relation = com.immomo.momo.service.bean.User.RELATION_NONE //修改关系为陌生人
                user.blocktime = Date()
                userService.addBlackUser(user) //插入黑名单列表
                userService.saveUserSimple(user)
                updateFansList(reportOptionUserId)
                return@fromCallable user
            }
            User()
        }.subscribeOn(Schedulers.from(User))
            .observeOn(Main.scheduler)
            .subscribeWith(object : CommonSubscriber<User?>() {
                fun onNext(user: User) {
                    super.onNext(user)
                    if (StringUtils.notEmpty(user.momoid)) {
                        EventBus.getDefault()
                            .post(DataEvent(EventKeys.Block.ADD_BLOCK, user.momoid))
                    }
                }
            })
    }

    /**
     * 更新粉丝列表数目 *
     */
    protected fun updateFansList(removeId: String?) {
        val fan = userService.findFans(removeId)
        if (fan != null) {
            userService.removeFans(fan.momoid)
            if (getFollowerCount() > 0) {
                setFollowerCount(getFollowerCount() - 1)
            }
        }
        val intent = Intent(FriendListReceiver.ACTION_DELETE_FANS)
        intent.putExtra(FriendListReceiver.KEY_MOMOID, removeId)
        intent.putExtra(FriendListReceiver.KEY_NEW_FANS, getNewFansCount())
        intent.putExtra(FriendListReceiver.KEY_TOTAL_FANS, getFollowerCount())
        intent.putExtra(FriendListReceiver.KEY_TOTAL_FRIENDS, getFollowingCount())
        FriendListReceiver.send(intent) //更新粉丝列表界面
    }

    private fun showActionDialog(sayhiSession: SayhiSession, sayHiInfo: SayHiInfo?) {
        val mdialog = SayHiActionDialog(
            this,
            {
                if (MomoKit.getCurrentUser()?.isMomoVip == true) {
                    val intent = Intent(thisActivity(), ChatActivity::class.java)
                    intent.putExtra(ChatActivity.REMOTE_USER_ID, sayhiSession.momoid)
                    intent.putExtra(ChatHelper.KEY_VIEWMODEL, ChatHelper.VIEWMODEL_PEEK)
                    intent.putExtra(
                        ChatHelper.KEY_BUSINESS_TYPE,
                        if (sayhiSession.sessionBusinessType == SayhiSession.BUSINESS_TYPE_KLIAO_MATCH) ChatBusinessType.CHAT_BUSINESS_KLIAO_MATCH else ""
                    )
                    startActivity(intent)
                } else {
                    showBuyVipDialog()
                }

            }, {
                mPresenter.deleteSession(sayhiSession.momoid, true)
                mPresenter.consumeSayhi(sayhiSession.momoid, LikeSayHi.Requst.TYPE_DEL)
            }, {
                reportOptionUserId = sayhiSession.momoid
                PlatformReportHelper.startReportByMomoid(
                    thisActivity(), PlatformReportHelper.REPORT_BIZ_GREETING_CARD,
                    sayhiSession.momoid, PlatformReportHelper.REPORT_TYPE_SOURCE_FROM_NONE
                )
            }, {
                showReportDialog(sayhiSession)
            })
        mdialog.needShowReport(sayHiInfo != null)
        showDialog(mdialog)
    }

    private fun showBuyVipDialog() {
        val dialog = MAlertDialog.makeConfirm(
            thisActivity(), "会员查看消息可不标记已读",
            "取消", "开通会员",
            { dialog1: DialogInterface?, which: Int -> closeDialog() }
        ) { dialog12: DialogInterface?, which: Int ->
            NavigateHelper.startWebview(
                thisActivity(),
                UrlConstant.URL_MEMBER_CENTER
            )
        }
        dialog.setSupportDark(true)
        dialog.setTitle("提示")
        showDialog(dialog)
    }

    private fun showReportDialog(sayhiSession: SayhiSession) {
        val momoid = sayhiSession.momoid
        //拉黑并全站禁言他
        val forbiddenDialog = MAlertDialog(getActivity())
        forbiddenDialog.setTitle("提示")
        forbiddenDialog.setMessage(
            "如果该招呼让你感到被冒犯，你可以选择匿名禁言" + (if (sayhiSession.user?.isFemale == true) "她" else "他") + "并拉黑（每日一次），或者举报"
        )
        forbiddenDialog.setButton(
            MAlertDialog.INDEX_LEFT,
            "匿名禁言" + (if (sayhiSession.user?.isFemale == true) "她" else "他") + "并拉黑"
        ) { _: DialogInterface?, _: Int ->
            MomoTaskExecutor.executeUserTask(
                taskTag,
                ReportForbiddenTask(WeakReference(thisActivity()), momoid.toString())
            )
        }
        forbiddenDialog.setButton(
            MAlertDialog.INDEX_RIGHT, "举报"
        ) { _: DialogInterface?, _: Int ->
            reportUser(momoid.toString())
        }
        forbiddenDialog.setButton(
            MAlertDialog.INDEX_THIRD,
            "取消",
            forbiddenDialog.defaultButtonOnclick
        )
        forbiddenDialog.setSupportDark(true)
        showDialog(forbiddenDialog)
    }

    fun reportUser(momoId: String) {
        reportOptionUserId = momoId
        PlatformReportHelper.startReportByMomoid(
            thisActivity(), PlatformReportHelper.REPORT_BIZ_GREETING_CARD,
            momoId, PlatformReportHelper.REPORT_TYPE_SOURCE_FROM_NONE
        )
    }


    /**
     * 跳转女性选择问题弹窗
     */
    private fun gotoFemaleDialog() {
        var sex = "TA"
        mPresenter.loadFemaleQuestion(isFromOutSide = false, isRedEnvelope = false, sex = sex)
    }

    class SessionHandler(activity: SayHiListActivity?) :
        UIHandler<SayHiListActivity?>(activity) {
        override fun handleMessage(msg: Message) {
            val activity = ref ?: return
            when (msg.what) {
                REFRESH_COUNT_BOTTON -> {
                    activity.showMessageCount()
                }

                else -> {}
            }
        }
    }

    override fun getActivity(): BaseActivity? {
        return this
    }

    override fun updateLiveSession(lastSession: SayhiSession?) {

    }

    override fun updateGiftSession(lastSession: SayhiSession?) {

    }

    override fun updateTitle() {
        handler.sendEmptyMessage(REFRESH_COUNT_BOTTON)
    }

    override fun setLoadMoreVis(vis: Boolean) {

    }

    override fun loadCompleted() {
        mListView?.setLoadMoreComplete()
    }

    override fun loadFailed() {
        mListView?.setLoadMoreFailed()
    }

    override fun addTips(msg: ITipsPresenter.TipsMessage?) {

    }

    override fun removeTips(msg: ITipsPresenter.TipsMessage?) {

    }

    override fun onMessageReceive(bundle: Bundle?, action: String?): Boolean {
        return mPresenter.onMessageReceive(bundle, action);
    }

    override fun showFemaleQuestionDialog(
        questions: List<PersonalProfileQuestion>,
        isFromOutSide: Boolean,
        isRedEnvelope: Boolean,
        sex: String?
    ) {
        if (femaleSelectQuestionDialog == null) {
            femaleSelectQuestionDialog = FemaleSelectQuestionDialog(this)
        }
        femaleSelectQuestionDialog?.setData(questions, sex)
        femaleSelectQuestionDialog?.setListener(object : onClickListener {
            override fun onSureClick(questionId: String, question: String, selected: Boolean) {
                KV.saveUserValue(SPKeys.FemaleGreet.KEY_FEMALE_SELECT_STATUS, selected)
                refreshReplyMessage(question)
                mPresenter.setReplyQuestion(
                    questionId,
                    question,
                    isFromOutSide,
                    isRedEnvelope,
                    selected
                )
            }

            override fun requestNew() {
                loadFemaleQuestion(isFromOutSide = false, isRedEnvelope = false, sex = "")
            }
        })
        femaleSelectQuestionDialog?.show()
    }

    fun refreshReplyMessage(reply: String?) {
        reply?.apply {
            msgLike = reply
        }
    }

    override fun onSettingSavedSuccess(
        reply: String?,
        isFromOutSide: Boolean,
        isRedEnvelope: Boolean
    ) {

        //右滑时toast改为已通过
        if (isFromOutSide) {
            Toaster.show(if (isRedEnvelope) UIUtils.getString(R.string.sayhi_stack_pass_red_envelope) else "已通过")
        } else {
            Toaster.show("设置成功")
        }
    }

    fun loadFemaleQuestion(isFromOutSide: Boolean, isRedEnvelope: Boolean, sex: String?) {
        mPresenter.loadFemaleQuestion(isFromOutSide, isRedEnvelope, sex)
    }

    override fun onGlobalEventReceived(event: GlobalEventManager.Event?) {
        event?.also {
            when (event.name) {
                SayHiConst.EVENT_SAYHI_LIST_LOADMORE_DATA -> { // 加载更多
                    mPresenter.loadMore(true)
                }

                SayHiConst.EVENT_SAYHI_STACK_CARD_REPORT -> {
                    (event.msg["momoid"] as String?)?.also {
                        afterBlockAndReportSuccess(it)
                    }
                }

                SayHiConst.EVENT_SAYHI_STACK_CARD_STACK_LEFT -> { // 替换曝光到未曝光
                    (event.msg["momoid"] as String?)?.also {
                        mPresenter.onCardLeftStack(it)
                    }
                }

                SayHiConst.EVENT_SAYHI_STACK_CARD_WEB_REPORT -> { // 举报网页
                    (event.msg["action"] as Int?)?.also {
                        val userid = event.msg["userid"] as String?
                        reportUserChat(it, userid)
                    }
                }
            }
        }
    }
}