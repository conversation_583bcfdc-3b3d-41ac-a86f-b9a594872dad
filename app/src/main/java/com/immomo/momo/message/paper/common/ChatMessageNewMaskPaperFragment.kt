package com.immomo.momo.message.paper.common

import android.annotation.SuppressLint
import android.view.MotionEvent
import android.view.View
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import androidx.lifecycle.ViewModelProvider
import com.immomo.momo.R
import com.immomo.momo.eventbus.DataEvent
import com.immomo.momo.eventbus.PaperDataEvent
import com.immomo.momo.message.paper.BasePaperFragment
import com.immomo.momo.message.paper.PaperCommonViewModel
import com.immomo.momo.message.paper.chat.ChatPaperBooleanListener
import com.immomo.momo.message.paper.event.PaperEvent
import com.immomo.momo.mvp.message.view.BaseMessageActivity

/**
 * 新消息
 * <AUTHOR>
 * @data 2020-12-18.
 */

class ChatMessageNewMaskPaperFragment : BasePaperFragment(), View.OnTouchListener {

    private var newMessageLayout: View? = null
    private var buttombarInAnim: Animation? = null
    private var mPaperCommonViewModel: PaperCommonViewModel? = null


    companion object {
        fun newInstance(): ChatMessageNewMaskPaperFragment {
            return ChatMessageNewMaskPaperFragment()
        }
    }

    override fun getContainerId(): Int = R.id.chat_new_message_mask_paper_container

    override fun getPageLayout(): Int = R.layout.paper_chat_new_message_mask


    @SuppressLint("ClickableViewAccessibility")
    override fun initPageViews(contentView: View?) {
        newMessageLayout = contentView?.findViewById(R.id.message_layout_mask)
        newMessageLayout?.setOnTouchListener(this)
        getBaseActivity()?.let {
            mPaperCommonViewModel = ViewModelProvider(it).get(PaperCommonViewModel::class.java)
            mPaperCommonViewModel?.newMessageMaskShowListener = object : ChatPaperBooleanListener {
                override fun onResult(): Boolean {
                    return newMessageLayout?.isShown ?: false
                }
            }
        }
    }


    override fun onTouch(v: View?, event: MotionEvent?): Boolean {

        when (v?.id) {
            R.id.message_layout_mask -> {
                if (event?.action == MotionEvent.ACTION_DOWN) {
                    getBaseActivity()?.onMaskClick()
                    hideTipsBar()
                    return true
                }
            }
        }
        return false
    }

    override fun onPageLoad() {

    }

    fun showTipsBar() {
        //新加条件如果底部的输入控件没有展示出来，就不展示新消息控件 2016-8-15 dongtao
        getBaseActivity()?.let {
            if (it.isKeyboardShown || it.panelHandler.isPanelShow() || newMessageLayout?.isShown == true ||
                    !it.mMessageEditorGallery.isShown || mPaperCommonViewModel?.forbidShowNewMsgView == true) {
                return
            }
            if (buttombarInAnim == null) {
                buttombarInAnim = AnimationUtils.loadAnimation(it, R.anim.buttomtip_in)
            }
            newMessageLayout?.visibility = View.VISIBLE
            newMessageLayout?.animation = buttombarInAnim
            buttombarInAnim?.start()
        }
    }

    private fun hideTipsBar() {
        newMessageLayout?.visibility = View.GONE
    }

    override fun onEvent(event: DataEvent<Any>) {
        super.onEvent(event)
        if(activity == null || activity?.isDestroyed == true || activity?.isFinishing == true){
            return
        }
        if(event is PaperDataEvent){
            if(event.pageCode != activity.hashCode()){
                return
            }
            when (event.action) {
                PaperEvent.PAPER_EVENT_HIDE_NEW_MESSAGE_MASK -> {
                    hideTipsBar()
                }
                PaperEvent.PAPER_EVENT_SHOW_NEW_MESSAGE_MASK -> {
                    showTipsBar()
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    fun getBaseActivity(): BaseMessageActivity? {
        (activity as? BaseMessageActivity)?.let {
            return it
        }
        return null
    }

}