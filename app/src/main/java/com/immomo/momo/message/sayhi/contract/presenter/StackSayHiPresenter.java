package com.immomo.momo.message.sayhi.contract.presenter;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.framework.utils.UIUtils;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.Configs;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.businessmodel.usermodel.IUserModel;
import com.immomo.momo.gene.receiver.SayHiChangedReceiver;
import com.immomo.momo.likematch.slidestack.SlideConst;
import com.immomo.momo.message.sayhi.SayHiArgs;
import com.immomo.momo.message.sayhi.contract.IStackSayHiContract;
import com.immomo.momo.message.sayhi.itemmodel.bean.LikeSayHi;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiListResult;
import com.immomo.momo.message.sayhi.task.PostIgnoreOrLike;
import com.immomo.momo.message.sayhi.utils.SayHiMaleRedPacketHelper;
import com.immomo.momo.message.sayhi.widget.HintPopupWindow;
import com.immomo.momo.messages.service.SingleMsgService;
import com.immomo.momo.mvp.common.model.ModelManager;
import com.immomo.momo.personalprofile.activity.PersonalProfileGirlExclusiveQuestionActivity;
import com.immomo.momo.personalprofile.gotoimpl.PersonalProfileEditGreetQuestionGotoImpl;
import com.immomo.momo.personalprofile.usecase.LogProfileUtils;
import com.immomo.momo.service.bean.User;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by jinxiao on 16/7/5.
 */
public class StackSayHiPresenter implements IStackSayHiContract.IPresenter {
    private final String HI_IGNORE_TAG = "update_hiIgnore_tag";

    private IStackSayHiContract.IView mView;

    private IUserModel mUserModel;
    private boolean hasPlayedEnterGuide; // 本次若出现过左右滑引导动画，则不出现左右滑弹窗
    private int leftSlideTimes;
    private SayHiChangedReceiver sayHiChangedReceiver;
    private BaseReceiver.IBroadcastReceiveListener receiveListener = new BaseReceiver.IBroadcastReceiveListener() {

        @Override
        public void onReceive(Intent intent) {
            if (intent != null) {
                if (TextUtils.equals(intent.getAction(), SayHiChangedReceiver.ACTION_SAYHI_REMOVE_ALL)) {
                    mView.removeCardAll();
                } else if (TextUtils.equals(intent.getAction(), SayHiChangedReceiver.ACTION_SAYHI_REMOVE)) {
                    String momoid = intent.getStringExtra(SayHiChangedReceiver.KEY_MOMOID);
                    mView.removeCard(momoid);
                }
            }
        }
    };

    public StackSayHiPresenter(IStackSayHiContract.IView mView) {
        this.mView = mView;
    }

    @Override
    public void init() {
        mUserModel = ModelManager.getInstance().getModel(IUserModel.class);
        SayHiListResult list = mView.getSayHiListFormActivity();
        if (list == null) {
            return;
        }
        sayHiChangedReceiver = new SayHiChangedReceiver(mView.getFragment().getContext());
        sayHiChangedReceiver.setReceiveListener(receiveListener);
        mView.fillCards(list, list.getData());
    }

    @Override
    public void setHasPlayedEnterGuide(boolean hasPlayedEnterGuide) {
        this.hasPlayedEnterGuide = hasPlayedEnterGuide;
    }

    @Override
    public boolean isHasPlayedEnterGuide() {
        return hasPlayedEnterGuide;
    }

    @Override
    public void updateToHiIgnore(String momoid) {
        MomoTaskExecutor.executeLocalTask(HI_IGNORE_TAG, new MomoTaskExecutor.Task() {
            @Override
            protected Object executeTask(Object[] objects) throws Exception {
                SingleMsgService.getInstance().updateHiIgnore(momoid);
                return null;
            }

            @Override
            protected void onTaskError(Exception e) {
            }
        });
    }

    @Override
    public void popupSlideGuide(boolean left, String him) {
        String KeyData = left ? SPKeys.User.SayHi.KEY_POP_WINDOW_LEFT_SLIDE_LAST_DATA : SPKeys.User.SayHi.KEY_POP_WINDOW_RIGHT_SLIDE_LAST_DATA;
        String keyTimes = left ? SPKeys.User.SayHi.KEY_POP_WINDOW_LEFT_SLIDE_TIMES : SPKeys.User.SayHi.KEY_POP_WINDOW_RIGHT_SLIDE_TIMES;
        //三天后再次进入招呼页面且左右滑动卡片后，再次展示引导弹窗
        boolean threeDayLater = Math.abs(KV.getUserLong(KeyData, 0L) - System.currentTimeMillis()) >= Configs.SAYHI_POPUP_WINDOW;
        int shownTimes = KV.getUserInt(keyTimes, 0);
        if (!hasPlayedEnterGuide && threeDayLater && shownTimes < 2 && !MomoKit.isManUser()) {
            MomoMainThreadExecutor.postDelayed(getTaskTag(), () -> __popSlideGuideDelay(left, him), 300L);
            KV.saveUserValue(KeyData, System.currentTimeMillis());
            KV.saveUserValue(keyTimes, shownTimes + 1);
        }
    }

    private void __popSlideGuideDelay(boolean left, String him) {
        String leftHint = String.format("表示忽略/跳过%s，后续可在右上角收到的招呼中查看哦～", him);
        String rightHint = String.format("表示你想跟%s聊聊，会发消息告诉%s，等%s回复吧～", him, him, him);
        List<CharSequence> descList = left ? Arrays.asList("左滑！", leftHint) : Arrays.asList("右滑！", rightHint);
        String guideGoto = left ? "https://s.momocdn.com/s1/u/hfbahhidf/left.png" : "https://s.momocdn.com/s1/u/hfbahhidf/right.png";
        mView.showPopupWindow(HintPopupWindow.LEFT_OR_RIGHT_SLIDE, null, descList, null, guideGoto, -1);
    }

    @Override
    public boolean isCurrentUserMale() {
        User user = mUserModel.getCurrentUser();
        return user != null && user.isMale();
    }

    private boolean isCurrentUserFemale() {
        User user = mUserModel.getCurrentUser();
        return user != null && user.isFemale();
    }


    public String getTaskTag() {
        return String.valueOf(this.hashCode());
    }


    @Nullable
    private Activity getActivity() {
        return mView != null ? mView.getActivityMayNull() : null;
    }

    @Override
    public void onDestroy() {
        MomoTaskExecutor.cancleAllTasksByTag(getTaskTag());
        MomoMainThreadExecutor.cancelAllRunnables(getTaskTag());
        MomoTaskExecutor.cancleAllTasksByTag(HI_IGNORE_TAG);
        if (sayHiChangedReceiver != null) {
            sayHiChangedReceiver.unregister();
            sayHiChangedReceiver = null;
        }
    }

    @Override
    public void postIgnoreOrLike(int likeType, String source, SayHiInfo info, CharSequence messageText,
                                 int messageType, Map<String, String> toApiParams, int consumeType, String touchType) {
        if (info == null) {
            return;
        }
        if (TextUtils.isEmpty(messageText)) { // 手动输入的回复为空时弹出引导弹窗
            popupSlideGuide(likeType == SayHiArgs.IGNORE, info.user != null && info.user.isMale() ? "他" : "她");
        }
        if (leftSlideTimes < 5 && isCurrentUserFemale()
                && likeType == SlideConst.VANISH_TYPE_LEFT) {
            IncrementLeftSlideTimes();
        }

        LikeSayHi.Requst requst = new LikeSayHi.Requst(likeType, source, info, messageText, messageType, toApiParams, consumeType, touchType);
        requst.isRedPacket = info.maleUserRedPacket != null;
        requst.setHeartbeatSvip(info.isHeartbeat());
        PostIgnoreOrLike postIgnoreOrLike = new PostIgnoreOrLike(requst);
        postIgnoreOrLike.setShowNotiViewBlock((result) -> {
            if (getActivity() != null) {
                SayHiMaleRedPacketHelper.INSTANCE.showPacketNoticeView(getActivity(), result);
            }
            return null;
        });
        MomoTaskExecutor.executeTask(MomoTaskExecutor.EXECUTOR_TYPE_USER, getTaskTag(), postIgnoreOrLike);
    }

    private void IncrementLeftSlideTimes() {
        leftSlideTimes += 1;
        long lastDate = KV.getUserLong(SPKeys.User.SayHi.KEY_LAST_WISH_SETTING_DATE, 0L);
        boolean sevenDaysLater = System.currentTimeMillis() / 1000 - lastDate >= Configs.SAYHI_WISH_SETTING_INTERVAL;
        if (sevenDaysLater && leftSlideTimes >= 5) {
            MomoMainThreadExecutor.postDelayed(getTaskTag(), this::popLeftSlideWishDialog, 300L);
            KV.saveUserValue(SPKeys.User.SayHi.KEY_LAST_WISH_SETTING_DATE, System.currentTimeMillis() / 1000);
        }
    }

    private void popLeftSlideWishDialog() {
        String gotoStr = PersonalProfileEditGreetQuestionGotoImpl.Companion.getGotoUrl(PersonalProfileGirlExclusiveQuestionActivity.SOURCE_SAY_HI, true);
        List<CharSequence> descList = Arrays.asList(UIUtils.getString(R.string.if_unsatisfied_with_current_sayhi_msg), UIUtils.getString(R.string.set_wish_let_others_answer_your_wish));
        mView.showPopupWindow(HintPopupWindow.WISH_SETTING, null, descList, gotoStr, null, -1);
        LogProfileUtils.INSTANCE.logSayhi5TimesWishSettingExposure();
    }
}
