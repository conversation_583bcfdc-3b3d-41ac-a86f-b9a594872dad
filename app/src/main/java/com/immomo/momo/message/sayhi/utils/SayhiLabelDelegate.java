package com.immomo.momo.message.sayhi.utils;

import com.immomo.android.router.momo.bean.IUser;
import com.immomo.framework.cement.CementModel;
import com.immomo.framework.kotlin.ImageType;
import com.immomo.framework.utils.UIUtils;
import com.immomo.momo.R;
import com.immomo.momo.likematch.widget.label.Label;
import com.immomo.momo.likematch.widget.label.pagedelegate.DefaultPageLabelDelegate;
import com.immomo.momo.moment.utils.FortuneIconUtil;
import com.immomo.momo.service.bean.User;

import java.util.Collection;

public class SayhiLabelDelegate extends DefaultPageLabelDelegate {
    private boolean withFanLabel;

    public void setWithFanLabel(boolean withFanLabel) {
        this.withFanLabel = withFanLabel;
    }

    @Override
    public Collection<CementModel<?>> getLabelBy(IUser user) {
        int fortuen = user instanceof User ? ((User) user).fortuneLevel : 0;
        Label.Builder builder = Label.Builder.create(UIUtils.getPixels(12))
                .gender(user)
                .onlyVip(user)
                .imageUrl(ImageType.URL, FortuneIconUtil.getRichFortuneIconUrl(fortuen)
                        , UIUtils.getPixels(12), UIUtils.getPixels(30)
                        , false);
        if (withFanLabel) {
            builder.textDrawableLabel("你的粉丝", 0, R.drawable.bg_gender_sayhi_fans);
        }
        return builder.build();
    }
}
