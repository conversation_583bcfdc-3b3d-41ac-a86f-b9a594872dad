package com.immomo.momo.message.task;

import com.cosmos.mdlog.MDLog;
import com.immomo.momo.LogTag;
import com.immomo.momo.protocol.http.GroupApi;
import com.immomo.momo.protocol.imjson.IMJApi;

/**
 * <AUTHOR>
 * @date 2019/7/12
 * @description 页面关闭后上传服务器事件
 */
public class PostLogRunnable implements Runnable {
    private String groupId;

    public PostLogRunnable(String groupId) {
        this.groupId = groupId;
    }

    @Override
    public void run() {
        try {
            IMJApi.leaveGroupChat(groupId);
        } catch (Exception e) {
            MDLog.printErrStackTrace(LogTag.Message.GroupMessage, e);
        }

        // TODO IM接口上线覆盖量之后，删除下面接口请求。
        GroupApi.getInstance().postGroupChatFinishLog(groupId);

    }
}
