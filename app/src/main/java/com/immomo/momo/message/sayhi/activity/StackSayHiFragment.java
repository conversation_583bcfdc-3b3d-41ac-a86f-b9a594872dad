package com.immomo.momo.message.sayhi.activity;

import static com.immomo.momo.eventbus.EventKeys.SayHiCard.CARD_GET_RED_PACKET;
import static com.immomo.momo.message.sayhi.activity.HiCardStackActivity.KEY_FROM_SOURCE;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.ViewTreeObserver;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.OvershootInterpolator;
import android.view.inputmethod.EditorInfo;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.cosmos.mdlog.MDLog;
import com.immomo.framework.base.BaseFragment;
import com.immomo.framework.base.BaseTabOptionFragment;
import com.immomo.framework.kotlin.ImageLoader;
import com.immomo.framework.rxjava.executor.impl.ExecutorFactory;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.framework.utils.UIUtils;
import com.immomo.framework.view.inputpanel.impl.MomoInputPanel;
import com.immomo.framework.view.inputpanel.impl.emote.EmoteChildPanel;
import com.immomo.framework.view.inputpanel.impl.emote.EmoteConstants;
import com.immomo.game.util.DpPxUtil;
import com.immomo.mmstatistics.event.ClickEvent;
import com.immomo.mmstatistics.event.ExposureEvent;
import com.immomo.mmstatistics.event.TaskEvent;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.android.view.BindPhoneTipView;
import com.immomo.momo.android.view.MEmoteEditeText;
import com.immomo.momo.android.view.MEmoteTextView;
import com.immomo.momo.android.view.OnAnimationEndListener;
import com.immomo.momo.android.view.dialog.MAlertDialog;
import com.immomo.momo.android.view.tips.TipManager;
import com.immomo.momo.android.view.tips.tip.ITip;
import com.immomo.momo.android.view.tips.tip.OnTipHideListener;
import com.immomo.momo.android.view.util.ViewAvalableListener;
import com.immomo.momo.anim.AnimUtils;
import com.immomo.momo.appconfig.model.AppConfigV2;
import com.immomo.momo.eventbus.DataEvent;
import com.immomo.momo.eventbus.EventKeys;
import com.immomo.momo.feed.bean.CommentAtPositionBean;
import com.immomo.momo.feed.listener.CommentAtTextChangeListener;
import com.immomo.momo.gotologic.GotoDispatcher;
import com.immomo.momo.innergoto.helper.ActivityHandler;
import com.immomo.momo.likematch.slidestack.GuideAnimStatusListener;
import com.immomo.momo.likematch.slidestack.SlideConst;
import com.immomo.momo.likematch.tools.BasicAnimHelper;
import com.immomo.momo.message.sayhi.SayHiArgs;
import com.immomo.momo.message.sayhi.contract.IStackSayHiContract;
import com.immomo.momo.message.sayhi.contract.presenter.StackSayHiPresenter;
import com.immomo.momo.message.sayhi.itemmodel.bean.DetailOneSayhi;
import com.immomo.momo.message.sayhi.itemmodel.bean.LikeSayHi;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiInfo;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayHiListResult;
import com.immomo.momo.message.sayhi.itemmodel.bean.SayhiCardGuide;
import com.immomo.momo.message.sayhi.stack.ISayHiCardSwitchLisener;
import com.immomo.momo.message.sayhi.stack.SayHiSlideCard;
import com.immomo.momo.message.sayhi.stack.SayHiStackAdapter;
import com.immomo.momo.message.sayhi.stack.SayHiStackView;
import com.immomo.momo.message.sayhi.utils.FemaleRedEnvelopeHelper;
import com.immomo.momo.message.sayhi.utils.SayHiConst;
import com.immomo.momo.message.sayhi.widget.FemaleSayHiInputView;
import com.immomo.momo.message.sayhi.widget.HeartbeatSvipHiDialog;
import com.immomo.momo.message.sayhi.widget.SayHiInputView;
import com.immomo.momo.message.sayhi.widget.guideclick.MaleSayHiInputView;
import com.immomo.momo.personalprofile.module.domain.model.ProfileUserModel;
import com.immomo.momo.platform.utils.BlockUserTask;
import com.immomo.momo.router.ProfileRealAuthModel;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.service.user.ProfileModelHelper;
import com.immomo.momo.statistics.EVAction;
import com.immomo.momo.statistics.EVPage;
import com.immomo.momo.util.BindPhoneHelper;
import com.immomo.momo.util.CollectionUtils;
import com.immomo.momo.util.GsonUtils;
import com.immomo.momo.util.StringUtils;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import cn.dreamtobe.kpswitch.util.KPSwitchConflictUtil;
import cn.dreamtobe.kpswitch.util.KeyboardUtil;
import de.greenrobot.event.EventBus;
import io.reactivex.Flowable;
import io.reactivex.schedulers.Schedulers;
import io.reactivex.subscribers.DisposableSubscriber;

/**
 * "收到的招呼" - 新招呼卡片堆栈页里的Fragment
 */
public class StackSayHiFragment extends BaseTabOptionFragment implements IStackSayHiContract.IView,
        CommentAtTextChangeListener.IAtTextChange, View.OnClickListener {
    private static final long TIME_WEEK = 7 * 24 * 60 * 60 * 1000;
    private static SayHiListResult.TopTip sayhiCardsTip; // 打招呼卡片提醒
    private SayHiStackView slideStackView;
    private View viewGuideLayer, ccoverLayout;
    private AnimatorSet likeScale, dislikeScale;
    private boolean prepared;
    private IStackSayHiContract.IPresenter mPresenter;
    private WeakReference<Activity> activityRef;
    private SayHiStackAdapter stackAdapter;
    private View commentLayout;
    private BindPhoneTipView bindPhoneTipView;
    private MEmoteEditeText editText;
    private CommentAtTextChangeListener mListener;
    private MomoInputPanel mInputPanel;
    private View sendButton, emoteButton, visibleBtn;
    private KeyboardUtil.OnKeyboardShowingListener keyboardShowingListener;
    private KPSwitchConflictUtil.SwitchClickListener switchPanelListener;
    private ViewTreeObserver.OnGlobalLayoutListener onKeyboardShowingGlobalListener;
    private float scrollAccumulateY;
    private boolean hasStartFemaleAnimation = false;
    private ValueAnimator btnRecoverAnim;
    private RelativeLayout tips_layout;
    //输入框显示透明动画
    private Animation femaleGreetAlpha;
    //hi背景白色
    private ValueAnimator rlMiddleAnim;
    //输入框文字闪烁动画
    private Animation tvFlashAnim;
    //当前缩放比例
    private float currentP;
    //进场动画
    private boolean hasAnimEnd = false;
    //首张招呼detail接口是否请求完毕
    private boolean isFirstCardDetailReturn = false;
    //是否来自问题弹窗后的通过
    private boolean isFromQuestionDialog = false;
    //是否直接回复通过
    private boolean isReplyPass = false;
    //白色背景收回动画
    private ValueAnimator middleCircleDisAnimator;
    private SayHiInputView mSayHiInputView;
    private FrameLayout mSayHiInputContainer;
    private View maskView;
    private ConstraintLayout topTipContainer; // 顶部的tip
    private ImageView topTipIcon;
    private TextView topTipTitle;

    private HeartbeatSvipHiDialog heartbeatGuidDialog;

    //    <editor-fold desc="卡片点击/滑动回调">
    private ISayHiCardSwitchLisener switchListener = new ISayHiCardSwitchLisener() {

        private int curShowIndex = -1;

        @Override
        public void toggleInputLayout(boolean show, int indexInStack, MEmoteTextView editeText) {
            editText.setText(editeText.getText());
            if (show) {
                atShowCommentEditLayout();
            } else {
                hideCommentLayout();
            }
        }

        @Override
        public void onContentScrolled(RecyclerView recyclerView, int dx, int dy) {
            if (mSayHiInputView instanceof FemaleSayHiInputView) {
                scrollAccumulateY += dy;
                if (scrollAccumulateY >= 200 && !hasStartFemaleAnimation) {
                    //滚动开始时 蓝色按钮消失 输入框伸展
                    initBottomLayoutAnimation();
                    int width = ((FemaleSayHiInputView) mSayHiInputView).getRLMiddleWidth();
                    int height = ((FemaleSayHiInputView) mSayHiInputView).getRLMiddleHeight();
                    rlMiddleAnim = ValueAnimator.ofFloat(1, 2.3f, 2.2f);
                    rlMiddleAnim.addUpdateListener(animation -> {
                        currentP = (float) animation.getAnimatedValue();
                        ViewGroup.LayoutParams layoutParams = ((FemaleSayHiInputView) mSayHiInputView).getRLMiddleRLayoutParams();
                        if (layoutParams == null) {
                            return;
                        }
                        layoutParams.width = (int) (width * currentP);
                        ((FemaleSayHiInputView) mSayHiInputView).setRLMiddleRLayoutParams(layoutParams);
                        if (currentP < 1.4f) {
                            layoutParams.height = (int) (height / currentP);
                        }

                        if (currentP < 1.5f) {
                            ((FemaleSayHiInputView) mSayHiInputView).scaleLikeButton(1 / currentP);
                            mSayHiInputView.scaleDisLikeButton(1 / currentP);
                        }
                    });
                    rlMiddleAnim.setDuration(1000L);
                    rlMiddleAnim.setStartDelay(100L);
                    rlMiddleAnim.start();
                    onBottomRlMiddleItemShow();
                }
            }
        }

        @Override
        public void showGiftPanel(int index) {
            //do nothing
        }

        @Override
        public void onLoadDetail(boolean isSuccess, int index, SayHiInfo info) {
            //相等则说明当前卡片展示时，detail数据未返回。
            boolean needRefresh = index == curShowIndex;
            if (needRefresh) {
                refreshRedEnvelopeGuide(info);
                //当前在首张
                if (index == 0) {
                    isFirstCardDetailReturn = true;
                    startMainAnimGuideIfNeed();
                }
            }
        }

        @Override
        public void onShow(int index) {
            SayHiInfo info = stackAdapter.getItem(index);
            curShowIndex = index;
            if (info != null) {
                SayHiSlideCard card = slideStackView.getSlideItem(0);
                Bundle arguments = getArguments();
                String source = "msg";
                if (arguments != null && arguments.getString(KEY_FROM_SOURCE, "").equals("nearby")) {
                    source = "nearby";
                }
                if (card != null) {
                    card.setHasShow(true);
                    card.logExposure(source);
                    card.setLikeImageIcon(R.drawable.ic_sayhi_loading_btn_like_new);
                }
                refreshRedEnvelopeGuide(info);

                mPresenter.updateToHiIgnore(info.getMomoid());
                if (info.isHeartbeat()) {  // 如果是心动招呼则展示弹窗
                    Context context = getContext();
                    if (context != null) {
                        MomoMainThreadExecutor.postDelayed(hashCode(), () -> {
                            heartbeatGuidDialog = HeartbeatSvipHiDialog.checkShow(context);
                        }, 500);
                    }
                }
            }
            checkAndLoadMore();
        }

        @Override
        public void onCardVanish(int index, int type, String touchType, int picDepth, String source,
                                 boolean needRequest, Map<String, String> toApiParams) {
            scrollAccumulateY = 0f;
            switch (type) {
                case SlideConst.VANISH_TYPE_LEFT:
                    onCardSlideToLeft(source, needRequest, toApiParams, touchType);
                    break;
                case SlideConst.VANISH_TYPE_RIGHT:
                    onCardSlideToRight(source, needRequest, toApiParams, touchType);
                    break;
                default:
                    break;
            }
            showRedEnvelopToast(index, type, touchType, isFromQuestionDialog, isReplyPass);
            int nextCardIndex = index + 1;
            if (stackAdapter.isIndexValid(nextCardIndex)) {
                refreshRedEnvelopeGuide(stackAdapter.getItem(nextCardIndex));
                showFemaleInputTips(nextCardIndex);
                curShowIndex = nextCardIndex;
            }
            buttonRecoverAnim();
            refreshCardCount(stackAdapter.getCurrentTotal(true));
        }

        @Override
        public boolean canShowTips(SayhiCardGuide profileClick) {
            if (SayhiCardGuide.ScrollDown == profileClick) {
                return mPresenter != null && !mPresenter.isHasPlayedEnterGuide();
            }
            return false;
        }

        @Override
        public void onItemClick(View cardImageView, int index, int dianDianPhotoIndex) {
            Activity act = getActivityMayNull();
            if (act == null || act.isFinishing() || act.isDestroyed() || slideStackView.btnLock) { // 动画使不响应点击信息
                return;
            }
            SayHiInfo info = slideStackView.getInfoAt(index);
            if (info == null || StringUtils.isEmpty(info.getCellgoto())) {
                return;
            }
            ActivityHandler.executeAction(info.getCellgoto(), act);   // 跳转资料页
        }

        @Override
        public void onSlide(float rate) {
            //do nothing
        }

        @Override
        public void onPreventRightSlide() {
            //女性招呼优化 弹出设置问题弹窗
            if (MomoKit.getCurrentUser().isFemale() && !KV.getUserBool(SPKeys.FemaleGreet.KEY_FEMALE_SELECT_STATUS, false)) {
                FragmentActivity activity = getActivity();
                if (activity instanceof HiCardStackActivity) {
                    SayHiInfo infoAt = slideStackView.getInfoAt(slideStackView.getShowingDataIndex());
                    boolean isRedEnvelope = FemaleRedEnvelopeHelper.INSTANCE.isRedEnvelopeHi(infoAt);
                    ((HiCardStackActivity) activity).loadFemaleQuestion(true, isRedEnvelope, infoAt.user != null && infoAt.user.isMale() ? "他" : "她");
                }
            }
        }

        @Override
        public void nextCardNeedAnimBlock(int indexInDataList) {
            setBtnLock(true);
        }

        @Override
        public void setCardCanMove(boolean noMove) {
            if (slideStackView != null) {
                slideStackView.setChildCanTouch(noMove);
            }
        }

    };

    /**
     * 右滑通过、点击按钮通过（未展示问题弹窗时）展示红包toast
     */
    private void showRedEnvelopToast(int index, int type, String touchType, boolean isFromQuestionDialog, boolean isReplyPass) {
        SayHiInfo info = stackAdapter.getItem(index);
        boolean isRedEnvelope = FemaleRedEnvelopeHelper.INSTANCE.isRedEnvelopeHi(info);
        if (!isReplyPass && type == SlideConst.VANISH_TYPE_RIGHT && isRedEnvelope && (StringUtils.equalsNonNull(touchType, "slide")
                || (StringUtils.equalsNonNull(touchType, "click") && !isFromQuestionDialog))) {
            toastMsg(UIUtils.getString(R.string.sayhi_stack_pass_red_envelope));
        }
    }

    private void refreshRedEnvelopeGuide(SayHiInfo info) {
        boolean isRedEnvelop = FemaleRedEnvelopeHelper.INSTANCE.isRedEnvelopeHi(info);

        //1.回复hint
        editText.setHint(isRedEnvelop ? R.string.sayhi_stack_input_to_chat_red_envelop_hint :
                (info.maleUserRedPacket != null ? R.string.sayhi_stack_input_to_get_redpacket : R.string.sayhi_stack_input_to_chat));
        mSayHiInputView.setInputHint(isRedEnvelop ?
                UIUtils.getString(R.string.sayhi_stack_slide_input_to_chat_red_envelop)
                : (info.maleUserRedPacket != null ? UIUtils.getString(R.string.sayhi_stack_input_to_get_redpacket) : UIUtils.getString(R.string.sayhi_stack_input_hint)));

        //2.红包按钮
        mSayHiInputView.setLikeBtnRes(isRedEnvelop ? R.drawable.ic_female_like_red_envelop : R.drawable.ic_female_like);

    }

    private void initBottomLayoutAnimation() {
        //输入框动画消失后 进行闪动动画
        femaleGreetAlpha = AnimationUtils.loadAnimation(MomoKit.getContext(), R.anim.anim_female_greet_alpha);
        femaleGreetAlpha.setAnimationListener(new OnAnimationEndListener() {
            @Override
            public void onAnimationEnd(Animation animation) {
                mSayHiInputView.startFlash(tvFlashAnim);
            }
        });
        femaleGreetAlpha.setStartOffset(300);
        hasStartFemaleAnimation = true;
        //显示输入框
        mSayHiInputView.setStartChatVisible();
        mSayHiInputView.startChatAnim(femaleGreetAlpha);
        //中部hi按钮消失动画
        if (mSayHiInputView instanceof FemaleSayHiInputView) {
            ((FemaleSayHiInputView) mSayHiInputView).initMiddleBtnAnim();
        }
    }

    private DisposableSubscriber<Long> disposableSubscriber;

    private void buttonRecoverAnim() {
        if (btnRecoverAnim == null) {
            btnRecoverAnim = ValueAnimator.ofFloat(0.8f, 1f);
            btnRecoverAnim.addUpdateListener(animation -> {
                Float p = (Float) animation.getAnimatedValue();
                if (mSayHiInputView instanceof FemaleSayHiInputView) {
                    ((FemaleSayHiInputView) mSayHiInputView).scaleLikeButton(p);
                }
                mSayHiInputView.scaleDisLikeButton(p);
                if (mSayHiInputView instanceof FemaleSayHiInputView && ((FemaleSayHiInputView) mSayHiInputView).middleBanVisible()) {
                    if (middleCircleDisAnimator != null && middleCircleDisAnimator.isRunning()) {
                        return;
                    }
                    ((FemaleSayHiInputView) mSayHiInputView).scaleMiddle(p);
                }
                if (slideStackView != null) {
                    slideStackView.setChildCanTouch(1f - p < 0.01f);
                }
            });
            btnRecoverAnim.setDuration(200L);
            btnRecoverAnim.setStartDelay(200L);
        }
        if (MomoKit.getCurrentUser().isFemale()) {
            if (rlMiddleAnim != null && rlMiddleAnim.isRunning()) {
                rlMiddleAnim.cancel();
            }

            if (mSayHiInputView instanceof FemaleSayHiInputView && !((FemaleSayHiInputView) mSayHiInputView).middleBanVisible()) {
                int rl_middle_width = ((FemaleSayHiInputView) mSayHiInputView).getRLMiddleWidth();
                int rl_middle_height = ((FemaleSayHiInputView) mSayHiInputView).getRLMiddleHeight();
                middleCircleDisAnimator = ValueAnimator.ofFloat(1f, currentP);
                middleCircleDisAnimator.addUpdateListener(animation -> {
                    float p = (float) animation.getAnimatedValue();
                    ViewGroup.LayoutParams layoutParams = ((FemaleSayHiInputView) mSayHiInputView).getRLMiddleRLayoutParams();
                    if (layoutParams == null) {
                        return;
                    }
                    layoutParams.width = (int) (rl_middle_width / p);
                    if (p < 1.4f) {
                        layoutParams.height = (int) (rl_middle_height * p);
                    } else {
                        if (layoutParams.height < DpPxUtil.dp2px(MomoKit.getContext(), 70)) {
                            layoutParams.height = DpPxUtil.dp2px(MomoKit.getContext(), 70);
                        }
                    }
                    if (p == currentP && layoutParams.width != DpPxUtil.dp2px(MomoKit.getContext(), 70)) {
                        layoutParams.width = DpPxUtil.dp2px(MomoKit.getContext(), 70);
                    }
                    ((FemaleSayHiInputView) mSayHiInputView).setRLMiddleRLayoutParams(layoutParams);
                });
                middleCircleDisAnimator.setDuration(200L);
                middleCircleDisAnimator.setStartDelay(100L);
                middleCircleDisAnimator.start();
            }
            setMiddleImageAnim();
        }
        cancelBtnRecoverAnim();
        btnRecoverAnim.start();
        hasStartFemaleAnimation = false;
    }

    /**
     * 消失动画正在进行时 进行cancel操作
     */
    private void setMiddleImageAnim() {
        if (femaleGreetAlpha != null && !femaleGreetAlpha.hasEnded()) {
            femaleGreetAlpha.cancel();
        }
        if (tvFlashAnim != null && !tvFlashAnim.hasEnded()) {
            tvFlashAnim.cancel();
            mSayHiInputView.clearFlashAnim();
        }
        mSayHiInputView.clearStartChatAnim();

        if (mSayHiInputView instanceof FemaleSayHiInputView) {
            ((FemaleSayHiInputView) mSayHiInputView).cancelMiddleBtnAnim();
            ((FemaleSayHiInputView) mSayHiInputView).startMiddleBtnAnim();
        }
    }

    private void cancelBtnRecoverAnim() {
        if (btnRecoverAnim != null && !btnRecoverAnim.isRunning()) {
            btnRecoverAnim.cancel();
        }
    }

    public static StackSayHiFragment newInstance() {
        StackSayHiFragment fragment = new StackSayHiFragment();
        return fragment;
    }

    @Override
    protected int getLayout() {
        return R.layout.fragment_stack_say_hi;
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        this.activityRef = new WeakReference<>(activity);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EventBus.getDefault().register(this);
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!prepared) {
            return;
        }
        slideStackView.onResume();
        checkDataEmpty();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        prepared = false;

    }

    /**
     * 新增底部所有动画清楚
     */
    private void clearBottomAnimation() {
        if (middleCircleDisAnimator != null && !middleCircleDisAnimator.isRunning()) {
            middleCircleDisAnimator.cancel();
        }

        if (tvFlashAnim != null && !tvFlashAnim.hasEnded()) {
            tvFlashAnim.cancel();
        }

        if (femaleGreetAlpha != null && !femaleGreetAlpha.hasEnded()) {
            femaleGreetAlpha.cancel();
        }

        if (mSayHiInputView instanceof FemaleSayHiInputView) {
            ((FemaleSayHiInputView) mSayHiInputView).cancelAnim();
            ((FemaleSayHiInputView) mSayHiInputView).clearAnim();
        }

        if (disposableSubscriber != null && !disposableSubscriber.isDisposed()) {
            disposableSubscriber.dispose();
        }

        if (rlMiddleAnim != null && !rlMiddleAnim.isRunning()) {
            rlMiddleAnim.cancel();
        }

        mSayHiInputView.clearStartChatAnim();
        mSayHiInputView.clearFlashColorFilter();
    }

    @Override
    public void onDestroy() {
        MomoMainThreadExecutor.cancelAllRunnables(hashCode());
        Activity activity = getActivityMayNull();
        if (activity != null) {
            KeyboardUtil.detach(getActivityMayNull(), onKeyboardShowingGlobalListener);
            TipManager.unbindActivity(activity);
        }
        closeDialogByDestroy();
        EventBus.getDefault().unregister(this);
        super.onDestroy();
        if (mPresenter != null) {
            mPresenter.onDestroy();
        }
        if (slideStackView != null) {
            ArrayList<SayHiInfo> unReadCards = slideStackView.getUnReadCards();
            SayHiConst.checkDeleteGiftCard(unReadCards);
            slideStackView.onDestroy();
        }
        cancelBtnRecoverAnim();
        clearBottomAnimation();
        keyboardShowingListener = null;
        switchPanelListener = null;
        if (bindPhoneTipView != null) {
            bindPhoneTipView.release();
        }
    }

    private void closeDialogByDestroy() {
        try {
            if (heartbeatGuidDialog != null && heartbeatGuidDialog.isShowing()) {
                heartbeatGuidDialog.dismiss();
            }
        } catch (Throwable throwable) {
            MDLog.printErrStackTrace(LogTag.COMMON, throwable);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (!prepared) {
            return;
        }
        ccoverLayout.clearAnimation();
        slideStackView.onStop();
        hideCommentLayout();
    }

    public SayHiInfo getCurrentCardUser() {
        return slideStackView.getInfoAt(slideStackView.getShowingDataIndex());
    }

    @Override
    protected void onActivityResultReceived(int requestCode, int resultCode, Intent data) {
        //do nothing
    }

    @Override
    protected void initViews(View view) {
        slideStackView = findViewById(R.id.slideStackView);
        viewGuideLayer = findViewById(R.id.view_guide_layer);
        ccoverLayout = findViewById(R.id.cover_all);
        //女性招呼按钮引导
        tips_layout = findViewById(R.id.tips_layout);
        mSayHiInputContainer = findViewById(R.id.say_hi_input_container);

        if (MomoKit.isManUser()) {
            mSayHiInputView = new MaleSayHiInputView(getContext());
        } else {
            mSayHiInputView = new FemaleSayHiInputView(getContext());
        }
        mSayHiInputContainer.removeAllViews();
        mSayHiInputContainer.addView(mSayHiInputView);

        mSayHiInputView.setSayHiInputListener(new SayHiInputView.SayHiInputListener() {
            @Override
            public void onLikeBtnClick() {
                onRightBtnClick(false);
            }

            @Override
            public void showCommentEdit() {
                onHiClickEvent();
                atShowCommentEditLayout();
            }
        });
        initEditTextViews();
        tvFlashAnim = AnimationUtils.loadAnimation(MomoKit.getContext(), R.anim.anim_female_tv_flash);
        if (MomoKit.getCurrentUser().isFemale()) {
            setStackViewPreventStatus();
        } else {
            mSayHiInputView.startFlash(tvFlashAnim);
        }
        maskView = findViewById(R.id.view_bottom_mask);
        if (com.immomo.momo.util.MomoKit.INSTANCE.isDarkMode(getActivity())) {
            maskView.setVisibility(View.GONE);
        }
        topTipContainer = findViewById(R.id.top_tip_container);
        topTipIcon = findViewById(R.id.tip_icon);
        topTipTitle = findViewById(R.id.tip_title);
    }

    /**
     * 展示红包tip
     *
     * @param onTipHideListener
     */
    private void showRedEnvelopeTip(OnTipHideListener onTipHideListener) {
        TipManager tipManager = TipManager.bindActivity(getActivityMayNull());
        tipManager.checkViewCanShowTip(findViewById(R.id.card_right_btn), new ViewAvalableListener() {
            @Override
            public void onViewAvalable(View v) {
                KV.saveUserValue(SPKeys.User.SayHi.KEY_HAS_SHOW_RED_ENVELOP_TIP, true);
                tipManager.setTouchToHideAll(true)
                        .setMarginEdge(UIUtils.getPixels(42f))
                        .showTipView(v, UIUtils.getString(R.string.sayhi_pass_get_red_envelop),
                                0, 0, ITip.Triangle.BOTTOM)
                        .autoHide(3000L)
                        .setOnTipHideListener(onTipHideListener);
            }
        });
    }

    boolean isShowingGetRedPacketTip = false;

    /**
     * 展示回流男招呼红包tip
     */
    private void getMaleUserRedPacket() {
        vanishCardTo(SlideConst.VANISH_TYPE_RIGHT, SlideConst.SlideSource.CARD, true);
    }


    /**
     * 上下引导展示之后
     */
    private void showFemaleInputTips(int index) {
        if (getUnReadCards().size() > 1 && MomoKit.getCurrentUser().isFemale()
                && KV.getUserBool(SPKeys.User.SayHi.KEY_SAYHI_CARD_SCROLL_DOWN_GUIDE, false)
                && !KV.getUserBool(SPKeys.FemaleGreet.KEY_FEMALE_GREET_INPUT_TIPS, false)) {
            //优先展示红包tip
            if (FemaleRedEnvelopeHelper.INSTANCE.shouldShowRedEnvelopeTip(stackAdapter.getItem(index))) {
                showRedEnvelopeTip(tip -> showFemaleInputTips(index + 1));
            } else {
                tips_layout.setVisibility(View.VISIBLE);
                KV.saveUserValue(SPKeys.FemaleGreet.KEY_FEMALE_GREET_INPUT_TIPS, true);
                disposableSubscriber = Flowable.interval(3000, TimeUnit.MILLISECONDS)
                        .subscribeOn(Schedulers.from(ExecutorFactory.f().getUserExecutor()))
                        .observeOn(ExecutorFactory.f().getUIThread().getScheduler(), true)
                        .onBackpressureDrop()
                        .subscribeWith(new DisposableSubscriber<Long>() {
                            @Override
                            public void onNext(Long aLong) {
                                tips_layout.setVisibility(View.GONE);
                            }

                            @Override
                            public void onError(Throwable t) {
                                //
                            }

                            @Override
                            public void onComplete() {
                                //
                            }
                        });
            }
        }
    }

    //<editor-fold desc="输入框">
    private void initEditText() {
        mListener = new CommentAtTextChangeListener(getActivity(), editText);
        mListener.setAtTextChange(this);
        editText.addTextChangedListener(mListener);
        editText.addTextChangedListener(slideStackView);
        editText.setImeOptions(EditorInfo.IME_ACTION_SEND);
        editText.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_SEND) {
                sendMessage(editText.getText().toString());
                return true;
            }
            return false;
        });
        initInputMethod();
        hideCommentLayout();
    }

    private void initInputMethod() {
        //translucentStatusBar 为true，所以要设置为 true
        if (MomoInputPanel.isPanelInFullscreen(getActivity())) {
            mInputPanel.setFullScreenActivity(true);
        }
        //绑定面板
        onKeyboardShowingGlobalListener = KeyboardUtil.attach(getActivity(), mInputPanel, getKeyboardShowingListener());
        KPSwitchConflictUtil.attach(mInputPanel, emoteButton, editText, getSwitchPanelListener());

        //初始化表情面板
        EmoteChildPanel emotePanel = new EmoteChildPanel(getContext());
        //设定输入表情类型：除了自定义表情之外都支持
        emotePanel.setEmoteFlag(EmoteChildPanel.Emote_Comment);
        //设置支持系统暗黑资源
        emotePanel.setSupportDark(true);
        //绑定EditText
        emotePanel.setEditText(editText);
        emotePanel.setDeleteDarkStyle();
        emotePanel.setEmoteSelectedListener((adapter, model, type) -> sendEmoteComment(model.getData().toString(), type));

        mInputPanel.addPanels(emotePanel);
        // 点击发送评论按钮
        sendButton.setOnClickListener(v -> sendMessage(editText.getText().toString()));
    }

    public KeyboardUtil.OnKeyboardShowingListener getKeyboardShowingListener() {
        if (keyboardShowingListener == null) {
            keyboardShowingListener = new KeyboardUtil.OnKeyboardShowingListener() {
                @Override
                public void onKeyboardShowing(boolean isShowing) {
                    //监听到输入框收起时，隐藏评论view
                    if (!isShowing && mInputPanel.getVisibility() != View.VISIBLE) {
                        MomoMainThreadExecutor.post(hashCode(), () -> hideCommentLayout());
                    }
                }
            };
        }
        return keyboardShowingListener;
    }

    public KPSwitchConflictUtil.SwitchClickListener getSwitchPanelListener() {
        if (switchPanelListener == null) {
            switchPanelListener = new KPSwitchConflictUtil.SwitchClickListener() {
                @Override
                public void onClickSwitch(boolean switchToPanel) {
                    if (switchToPanel) {
                        editText.clearFocus();
                        mInputPanel.showPanel();
                    } else {
                        editText.requestFocus();
                    }
                }

                @Override
                public boolean onBeforeClick() {
                    return true;
                }
            };
        }
        return switchPanelListener;
    }

    public void sendMessage(String content) {
        beforeMessageAboutToSend();
        replyVanishToRight();
        postIgnoreOrLike(SayHiArgs.LIKE, SlideConst.SlideSource.CARD, content, 0, null, LikeSayHi.Requst.TYPE_RIGHT, null);
    }

    private void replyVanishToRight() {
        if (MomoKit.getCurrentUser().isFemale()) {
            slideStackView.setPreventRightSlide(false);
        }
        slideStackView.vanishOnBtnClick(SayHiArgs.LIKE, SlideConst.SlideSource.CARD, false, null); //  自动喜欢
        if (MomoKit.getCurrentUser().isFemale()) {
            setStackViewPreventStatus();
        }
    }

    @Override
    public void toastMsg(String msg) {
        Toaster.show(msg);
    }

    public void clearEditText() {
        editText.setText("");
    }

    @Override
    public boolean hideCommentLayout() {
        if (ccoverLayout.isShown()) {
            toggleCoverAnim(false);
        }
        if (commentLayout != null && commentLayout.getVisibility() == View.VISIBLE) {
            mInputPanel.hidePanelAndKeyboard();
            commentLayout.setVisibility(View.GONE);
            setBtnLock(false);
            return true;
        }
        return false;
    }

    public void showCommentLayout() {
        if (!ccoverLayout.isShown()) {
            toggleCoverAnim(true);
        }
        if (commentLayout != null && commentLayout.getVisibility() != View.VISIBLE) {
            commentLayout.setVisibility(View.VISIBLE);
            setBtnLock(true);
        }
    }

    @Override
    public void atShowCommentEditLayout() {
        showCommentLayout();
        //如果没有显示表情面板或者输入法，则显示输入法
        if (!mInputPanel.isPanelOrKeyboardShowing()) {
            mInputPanel.showKeyboard(editText);
        }
        editText.setSelection(editText.getText().toString().length());
    }

    private void onHiClickEvent() {
        Bundle arguments = getArguments();
        String source = "msg";
        if (arguments != null && arguments.getString(KEY_FROM_SOURCE, "").equals("nearby")) {
            source = "nearby";
        }
        SayHiInfo info = slideStackView.getInfoAt(slideStackView.getShowingDataIndex());
        if (info == null) return;
        User user = info.getUser();
        DetailOneSayhi.Response detail = info != null ? info.getMoreDetail() : null;
        List<String> socialCapital = detail == null ? null : detail.getSocialCapital();

        ClickEvent clickEvent = ClickEvent.create()
                .page(EVPage.Msg.SayhiCard)
                .action(EVAction.Content.Feature)
                .requireId("12917");

        clickEvent.putExtra("photo_number", info.getGreetCardPhotos() != null ? info.getGreetCardPhotos().size() : 0)
                .putExtra("time_length", info.isOnline() ? "online" : "hiding")
                .putExtra("logmap", StringUtils.isEmpty(info.getLogMapString()) ? "" : info.getLogMapString())
                .putExtra("introduce", GsonUtils.g().toJson(info.getMarks()))
                .putExtra("photoid", CollectionUtils.isEmpty(info.getGreetCardPhotos()) ? "" : info.getGreetCardPhotos().get(0))
                .putExtra("fans_sign", User.RELATION_FANS.equals(info.getRelation()) ? "1" : "0")
                .putExtra("is_gift", info.hasGiftMessage() ? "1" : "0")
                .putExtra("sayhi_source", info.sourceText)
                .putExtra("ban_type", info.getForbiddenBtnShow() == 1 ? "1" : "0")
                .putExtra("source", source)
                .putExtra("info", StringUtils.join(socialCapital, "·"))
                .putExtra("feed_pos", slideStackView.getShowingDataIndex() + "")
                .putExtra("set_photoid", info.getOriginalFirstPhoto() != null ? info.getOriginalFirstPhoto() : "");
        if (user != null) {
            clickEvent.putExtra("momo_id", user.getMomoid());
            clickEvent.putExtra("distance", StringUtils.isEmpty(info.getUser().distanceString) ? "" : info.getUser().distanceString);
            clickEvent.putExtra("msg_num", info.getMessages() == null ? "0" : info.getMessages().size() + "");
            if (user.fortuneLevel > 0) {
                clickEvent.putExtra("wealth_rank", user.fortuneLevel);
            } else {
                clickEvent.putExtra("wealth_rank", "");
            }
            clickEvent.putExtra("vip_rank", user.isMomoVip() ? user.getVipActivityLevel() : 0);
            clickEvent.putExtra("svip_rank", user.isSvip() ? user.getSvipActivityLevel() : 0);
        }
        clickEvent.submit();

    }

    @Override
    public void initSelectionChanged(List<CommentAtPositionBean> mPairList) {
        editText.initSelectionChanged(mPairList);
    }

    private void sendEmoteComment(CharSequence emote, int type) {
        //发送大表情
        if (type == EmoteConstants.TYPE_IMAGE_SINGLE) {
            beforeMessageAboutToSend();
            replyVanishToRight();//  自动喜欢
            postIgnoreOrLike(SayHiArgs.LIKE, SlideConst.SlideSource.CARD, emote, type, null, LikeSayHi.Requst.TYPE_RIGHT, null);
        }
    }

    private void beforeMessageAboutToSend() {
        boolean isRedEnvelope = false;
        if (slideStackView != null && slideStackView.getSlideItem(0) != null) {
            slideStackView.getSlideItem(0).setSlideHintVis(false); // 选择手动输入回复时，隐藏卡片上的自动回复文案
            SayHiInfo info = slideStackView.getSlideItem(0).getSayhiDataProvider().getSayhiInfo();
            isRedEnvelope = FemaleRedEnvelopeHelper.INSTANCE.isRedEnvelopeHi(info);
        }
        hideCommentLayout();
        clearEditText();
        isReplyPass = true;
        toastMsg(isRedEnvelope ? "已回复，恭喜获得缘分红包" : "已发送");
    }

    @Override
    protected void onLoad() {
        initData();
        initEvents();
        initAnim();
        startCardRotateFromLeftAnim();
        prepared = true;
    }

    private void initData() {
        slideStackView.setRemainCount(KV.getUserInt(AppConfigV2.SPKeys.KEY_HI_LOADMORE_REMAIN, 3));
        stackAdapter = new SayHiStackAdapter();
        slideStackView.setAdapter(stackAdapter);
        slideStackView.setCardSwitchListener(switchListener);
        slideStackView.setNoSlide(MomoKit.isManUser());
        mPresenter = new StackSayHiPresenter(this);
        mPresenter.init();
        refreshCardCount();
    }

    private void toggleCoverAnim(boolean visible) {
        ccoverLayout.clearAnimation();
        ccoverLayout.startAnimation(visible ? AnimUtils.Animations.newFadeInAnimation(300) :
                AnimUtils.Animations.newFadeOutAnimation(300));
        ccoverLayout.setVisibility(visible ? View.VISIBLE : View.GONE);  //复用lottie的黑色半透明背景
    }

    private void initEvents() {
        initEditText();
        ccoverLayout.setOnClickListener(this);
        mSayHiInputView.getDisLikeBtn().setOnClickListener(this);
    }

    public void onEvent(DataEvent<String> event) {
        if (event.equals(EventKeys.Block.SHOW_BLOCK)) {
            String momoid = event.getData();
            if (StringUtils.notEmpty(momoid)) {
                handleBlock(momoid);
            }
        }

        if (event.equals(EventKeys.SayHiCard.CARD_RELOAD)) {
            if (stackAdapter != null && slideStackView != null) {
                slideStackView.reloadData();
            }
        }

        if (event.equals(CARD_GET_RED_PACKET)) {
            getMaleUserRedPacket();
        }
    }

    public void handleBlock(String momoid) {
        MAlertDialog dialog = MAlertDialog.makeConfirm(getActivity(), "对方发送的消息如果让你感到不适，你可直接将他拉黑并举报。", "取消", "确定"
                , null, (tDialog, which) -> {
                    ClickEvent.create().page(EVPage.Msg.ChatPage)
                            .action(EVAction.Window.BlockPromptConfirm)
                            .putExtra("to_momo_id", momoid)
                            .putExtra("source", "saorao")
                            .submit();
                    MomoTaskExecutor.executeUserTask(hashCode(), new BlockUserTask(momoid, getActivity(), tDialog, null, false));
                }
        );
        dialog.setCancelable(false);
        dialog.setCanceledOnTouchOutside(false);
        dialog.setSupportDark(true);
        showDialog(dialog);
    }

    @Override
    public Animation onCreateAnimation(int transit, boolean enter, int nextAnim) {
        if (!enter) {
            return AnimationUtils.loadAnimation(getActivity(), R.anim.fragment_fade_out);
        } else {
            return AnimationUtils.loadAnimation(getActivity(), R.anim.fragment_fade_in);
        }
    }

    private void initEditTextViews() {
        ViewStub viewStub = findViewById(R.id.replay_input_viewstub);
        View vstub = viewStub.inflate();
        commentLayout = vstub.findViewById(R.id.feed_comment_input_layout);
        bindPhoneTipView = vstub.findViewById(R.id.tip_bind_phone);
        bindPhoneTipView.setNeedCheck(true);
        bindPhoneTipView.setMode(BindPhoneHelper.MODE_IM);
        bindPhoneTipView.updateStatus();
        editText = vstub.findViewById(R.id.tv_feed_editer);
        editText.setHint(R.string.sayhi_stack_input_to_chat);
        mInputPanel = vstub.findViewById(R.id.simple_input_panel);
        sendButton = vstub.findViewById(R.id.feed_send_layout);
        TextView tvSend = vstub.findViewById(R.id.send_comment_btn);
        if (tvSend != null) {
            tvSend.setText("发送");
        }
        visibleBtn = vstub.findViewById(R.id.iv_private_comment);
        visibleBtn.setVisibility(View.GONE);
        emoteButton = vstub.findViewById(R.id.iv_feed_emote);
    }

    private void checkAndLoadMore() {
        if (slideStackView.needMoreData()) {
            if (hasMore()) {
                Activity activityMayNull = getActivityMayNull();
                if (activityMayNull instanceof HiCardStackActivity) {
                    ((HiCardStackActivity) activityMayNull).loadMore();
                }
            } else {
                checkDataEmpty();
            }
        }
    }

    public void checkDataEmpty() {
        if (slideStackView.noDataAvailable()) {
            if (hasMore()) {
                checkAndLoadMore();
            } else {
                backToEmptyView();
                sayhiCardsTip = null;
            }
        }
    }

    private void backToEmptyView() {
        Activity act = getActivityMayNull();
        if (act instanceof HiCardStackActivity) {
            ((HiCardStackActivity) act).toLoadFragment(true);
        }
    }

    private void onButtonClick(int slideType, String source) {
        vanishCardTo(slideType, source, true);
    }

    // needRequest 是否调用/like/person/like接口
    private void vanishCardTo(int slideType, String source, boolean needRequest) {
        if (slideStackView.noDataAvailable()) {
            return;
        }
        slideStackView.orderViewStack();
        isReplyPass = false;
        slideStackView.vanishOnBtnClick(slideType, source, needRequest, null);
    }

    private void onCardSlideToLeft(String source, boolean needRequest, Map<
            String, String> toApiParams, String touchType) {
        if (needRequest) {
            postIgnoreOrLike(SayHiArgs.IGNORE, source, null, 0, toApiParams,
                    LikeSayHi.Requst.TYPE_LEFT, touchType);
        }
    }

    public void refreshCardCount() {
        if (stackAdapter == null || stackAdapter.getSize() == 0) {
            return;
        }
        refreshCardCount(stackAdapter.getCurrentTotal(false));
    }

    public void refreshCardCount(int[] currentIndex) {
        Activity act = getActivityMayNull();
        if (act instanceof HiCardStackActivity) {
            ((HiCardStackActivity) act).refreshCardCount(currentIndex[0], currentIndex[1]);
        }
    }

    private void postIgnoreOrLike(int likeType, String source, CharSequence messageText,
                                  int messageType,
                                  Map<String, String> toApiParams, int consumeType, String touchType) {
        SayHiInfo info = slideStackView.getInfoAt(slideStackView.getShowingDataIndex());
        mPresenter.postIgnoreOrLike(likeType, source, info, messageText, messageType, toApiParams, consumeType, touchType);
    }

    private void onCardSlideToRight(String source, boolean needRequest, Map<
            String, String> toApiParams, String touchType) {
        if (needRequest) {
            postIgnoreOrLike(SayHiArgs.LIKE, source, null, 0, toApiParams,
                    LikeSayHi.Requst.TYPE_RIGHT, touchType);
        }
    }

    public ArrayList<SayHiInfo> getUnReadCards() {
        if (slideStackView != null) {
            return slideStackView.getUnReadCards();
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public SayHiListResult getSayHiListFormActivity() {
        Activity activity = getActivityMayNull();
        return activity instanceof HiCardStackActivity ? ((HiCardStackActivity) activity).getSayHiList() : null;
    }

    @Override
    public BaseFragment getFragment() {
        return this;
    }

    @Override
    public Activity getActivityMayNull() {
        Activity act = null;
        if (activityRef != null && activityRef.get() != null) {
            act = activityRef.get();
        }
        return act != null ? act : getActivity();
    }

    /**
     * 准备播放动画时不允许点击及拖动卡片
     *
     * @param isAniming
     */
    public void setBtnLock(boolean isAniming) {
        slideStackView.btnLock = isAniming;
        setToolBarEnable(!isAniming);
    }

    public boolean isAnimating() {
        return slideStackView.btnLock;
    }

    @Override
    public void fillCards(SayHiListResult sayHiListResult, List<SayHiInfo> list) {
        refreshCardTip(sayHiListResult);
        resetFirstCard(list);
        stackAdapter.addTotalCount(list.size(), true);
        slideStackView.fillData(list);
    }

    @Override
    public void appendCards(SayHiListResult cardResult, List<SayHiInfo> list) {
        refreshCardTip(cardResult);
        if (stackAdapter != null && list != null && !list.isEmpty()) {
            stackAdapter.addTotalCount(list.size(), false);
            resetFirstCard(list);
        }
        slideStackView.appendData(list);
        checkAndLoadMore();
    }

    private void resetFirstCard(List<SayHiInfo> list) {
        if (list != null && !list.isEmpty()) {
            SayHiInfo sayHiInfo = list.get(0);
            if (sayHiInfo != null) {
                sayHiInfo.setFirstCard(true);
            }
        }
    }

    private void refreshCardTip(SayHiListResult cardResult) {
        if (cardResult != null) {
            SayHiListResult.TopTip tips = cardResult.tips;
            if (tips != null) {
                sayhiCardsTip = tips;
            }
        }
        ProfileUserModel currentUserModel = ProfileModelHelper.getCurrentUserModel();
        if (currentUserModel != null) {
            ProfileRealAuthModel realAuthModel = currentUserModel.getRealAuthModel().orNull();
            if (realAuthModel != null && realAuthModel.getStatus() != 1) {
                sayhiCardsTip = null;
                if (topTipContainer != null) {
                    topTipContainer.setVisibility(View.GONE);
                }
            }
        }
        if (sayhiCardsTip != null) {
            SayHiListResult.TopTip cardTips = sayhiCardsTip;
            String text = cardTips.text;
            if (StringUtils.isNotBlank(text) && topTipTitle != null) {
                topTipTitle.setText(text);
            }
            String icon = cardTips.icon;
            if (StringUtils.isNotBlank(icon) && topTipIcon != null) {
                ImageLoader.load(icon).into(topTipIcon);
            }
            if (slideStackView != null) {
                List<SayHiSlideCard> cardViewLists = slideStackView.getCardViewLists();
                if (cardViewLists != null) {
                    if (topTipContainer != null) {
                        topTipContainer.setVisibility(View.VISIBLE);
                        topTipContainer.setOnClickListener(view -> {
                            if (StringUtils.isNotBlank(cardTips.action)) {
                                GotoDispatcher.action(cardTips.action, getActivity()).execute();
                            }
                        });
                    }
                    for (int i = 0; i < cardViewLists.size(); i++) {
                        SayHiSlideCard sayHiSlideCard = cardViewLists.get(i);
                        sayHiSlideCard.setCardTipSpace(true);
                    }
                }
            }
        } else if (stackAdapter != null && stackAdapter.isEmpty()) {
            if (topTipContainer != null) {
                topTipContainer.setVisibility(View.GONE);
            }
        }
    }

    /**
     * 新安装后出现的左右滑引导动画
     */
    @Override
    public void startMainAnimGuideIfNeed() {
        if (hasAnimEnd && isFirstCardDetailReturn) {
            slideStackView.playGuideBySequence();
        }
    }

    //<editor-fold desc="调用Activity方法" >

    //是否屏蔽标题栏按钮点击
    public void setToolBarEnable(boolean enable) {
        Activity act = getActivityMayNull();
        if (act instanceof HiCardStackActivity) {
            ((HiCardStackActivity) act).setToolBarEnable(enable);
        }
    }

    private boolean hasMore() {
        Activity activityMayNull = getActivityMayNull();
        if (activityMayNull instanceof HiCardStackActivity) {
            return ((HiCardStackActivity) activityMayNull).hasMore();
        }
        return false;
    }

    @Override
    public void startCardRotateFromLeftAnim() {
        setBtnLock(true);
        slideStackView.setTipListener(() -> showRedEnvelopeTip(new OnTipHideListener() {
            @Override
            public void onHide(ITip tip) {
                slideStackView.playGuideBySequence();
            }
        }));

        slideStackView.startCardCameFromLeftAnim(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
                //do nothing
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                hasAnimEnd = true;
                slideStackView.onAnimEnd();
                startMainAnimGuideIfNeed();
            }

            @Override
            public void onAnimationRepeat(Animation animation) {
                //do nothing
            }
        });
        slideStackView.setGuideAnimStatusListener(new GuideAnimStatusListener() {
            @Override
            public void onStart() {
                setBtnLock(true);
                viewGuideLayer.setVisibility(View.VISIBLE);
                viewGuideLayer.setAlpha(0);
            }

            @Override
            public void onAnimating(float value) {
                viewGuideLayer.setAlpha(Math.abs(value));
            }

            @Override
            public void onEnd() {
                viewGuideLayer.setVisibility(View.GONE);
                viewGuideLayer.setAlpha(0);
                setBtnLock(false);
            }
        });
    }

    @Override
    public void showPopupWindow(int type, View.OnClickListener clickListener, List<
            CharSequence> descList,
                                String clickGoto, String guidePhoto, int logType) {
        Activity act = getActivityMayNull();
        if (act instanceof HiCardStackActivity) {
            ((HiCardStackActivity) act).showPopWindow(type, clickListener, descList, clickGoto, guidePhoto, logType);
        }
    }

    @Override
    public void removeCard(String removeMomoid) {
        if (stackAdapter != null && slideStackView != null && stackAdapter.removeUnreadCard(removeMomoid)) {
            slideStackView.reloadData();
            refreshCardCount();
            checkAndLoadMore();
        }
    }

    @Override
    public void removeCardAll() {
        Activity act = getActivityMayNull();
        if (act instanceof HiCardStackActivity) {
            if (slideStackView != null) {
                slideStackView.clearData();
            }
            ((HiCardStackActivity) act).refreshData();
        }
    }

    @Override
    public boolean updateSingleMessage(String remoteid, String msgId) {
        int next = slideStackView.getNextComingIndex();
        if (next < 0) {
            return false;
        }
        int total = Math.max(0, Math.min(next + 3, stackAdapter.getSize()));
        for (int i = next; i < total; i++) {
            SayHiInfo info = stackAdapter.getItem(next);
            if (info != null && StringUtils.equalsNonNull(info.getMomoid(), remoteid)) {
                int indexOfView = Math.max(0, Math.min(i - next, slideStackView.getChildCount() - 1));
                SayHiSlideCard card = slideStackView.getSlideItem(indexOfView);
                if (card == null) {
                    return false;
                }
                card.updateMessage(remoteid, msgId);
            }
        }
        return false;
    }

    private AnimatorSet initSacleXYAnim(View view) {
        AnimatorSet scale = new AnimatorSet();
        List<Animator> list = new ArrayList<>();
        BasicAnimHelper.scaleXY(list, view, 0.8f, 1.0f);
        scale.playTogether(list);
        scale.setInterpolator(new OvershootInterpolator());
        scale.setDuration(400);
        return scale;
    }

    private void initAnim() {
        dislikeScale = initSacleXYAnim(mSayHiInputView.getDisLikeBtn());
        if (mSayHiInputView instanceof FemaleSayHiInputView) {
            likeScale = initSacleXYAnim(((FemaleSayHiInputView) mSayHiInputView).getLikeButton());
        }
    }

    //</editor-fold desc="动画" >

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.card_left_btn:
                if (slideStackView.btnLock) {
                    return;
                }
                if (dislikeScale.isRunning()) {
                    dislikeScale.cancel();
                }
                dislikeScale.start();
                onButtonClick(SlideConst.VANISH_TYPE_LEFT, SlideConst.SlideSource.CARD);
                break;
            case R.id.cover_all:
                hideCommentLayout();
                break;
            default:
                break;
        }
    }

    /**
     * 点击是否来自弹窗设置问题成功
     *
     * @param isFromQuestionDialog true 来自于阻断后的弹窗设置 之后自动执行滑动操作
     */
    public void onRightBtnClick(boolean isFromQuestionDialog) {
        this.isFromQuestionDialog = isFromQuestionDialog;
        if (isFromQuestionDialog && MomoKit.getCurrentUser().isFemale()) {
            slideStackView.setPreventRightSlide(false);
        }
        if (slideStackView.btnLock) {
            if (MomoKit.getCurrentUser().isFemale()) {
                //任何情况下都要设置是否分出选择问题弹窗
                setStackViewPreventStatus();
            }
            return;
        }
        if (likeScale.isRunning()) {
            likeScale.cancel();
        }
        likeScale.start();
        onButtonClick(SlideConst.VANISH_TYPE_RIGHT, SlideConst.SlideSource.CARD);
        if (isFromQuestionDialog && MomoKit.getCurrentUser().isFemale()) {
            setStackViewPreventStatus();
        }
    }

    public void setStackViewPreventStatus() {
        //任何情况下都要设置是否分出选择问题弹窗
        boolean hasNotShowDialogRecent = false;
        long lastShowTime = KV.getUserLong(SPKeys.FemaleGreet.KEY_DIALOG_SHOW_TIME, 0L);
        if ((lastShowTime == 0 || (System.currentTimeMillis() - lastShowTime) > TIME_WEEK)) {
            hasNotShowDialogRecent = true;
        }
        slideStackView.setPreventRightSlide(!KV.getUserBool(SPKeys.FemaleGreet.KEY_FEMALE_SELECT_STATUS, false) && hasNotShowDialogRecent);
    }

    public SayHiInfo getShowingItem() {
        return slideStackView != null ? slideStackView.getShowingItem() : null;
    }

    public void refreshReplyMessage(String reply) {
        if (slideStackView != null) {
            for (int i = 0; i < slideStackView.getChildCount(); i++) {
                SayHiSlideCard card = slideStackView.getSlideItem(i);
                if (card != null) {
                    card.setSlideHint(reply);
                }
            }
        }
        if (stackAdapter != null) {
            stackAdapter.refreshReplyMessage(reply);
        }
    }

    public void onBlockSuccess(String reportOptionUserId) {
        // 举报成功后回来会左滑忽略该张卡片
        SayHiInfo info = getShowingItem();
        if (slideStackView != null && info != null && TextUtils.equals(info.getMomoid(), reportOptionUserId)) {
            vanishCardTo(SlideConst.VANISH_TYPE_LEFT, SlideConst.SlideSource.CARD, false); // session都删掉了，故不用再请求接口
            TaskEvent.create()
                    .action(EVAction.Report.Success)
                    .page(EVPage.Msg.SayhiCard)
                    .status(TaskEvent.Status.Success)
                    .type("detail")
                    .putExtra("momoid", info.getMomoid())
                    .submit();
        }
    }

    /**
     * 女性底部输入框按钮滑动后展开打点
     */
    private void onBottomRlMiddleItemShow() {
        ExposureEvent.create(ExposureEvent.Type.Normal)
                .action(EVAction.Bottom.ReplyUnfold)
                .page(EVPage.Msg.SayhiCard)
                .submit();
    }
}