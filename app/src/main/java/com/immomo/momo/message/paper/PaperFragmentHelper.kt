package com.immomo.momo.message.paper

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager

/**
 * <AUTHOR>
 * @data 2020-12-18.
 */


object PaperFragmentHelper {
    fun add(fm: FragmentManager, f: Fragment, containerId: Int) {
        kotlin.runCatching {
            if (f.isAdded) return
            val ft = fm.beginTransaction()
            val name = f::class.java.name
            val fragmentByTag = fm.findFragmentByTag(name)
            if (fragmentByTag != null && fragmentByTag.isAdded) {
                ft.remove(fragmentByTag)
            }
            ft.add(containerId, f, name)
            ft.commitAllowingStateLoss()
        }
    }

    fun <T : BasePaperFragment> find(fm: FragmentManager, clazz: Class<T>): T? {
        return fm.findFragmentByTag(clazz.name) as? T
    }

    fun remove(fm: FragmentManager, f: Fragment) {
        try {
            val ft = fm.beginTransaction()
            ft.remove(f)
            ft.commitAllowingStateLoss()
        } catch (e: Exception) {
        }
    }


}