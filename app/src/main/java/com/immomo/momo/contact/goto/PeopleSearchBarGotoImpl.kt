package com.immomo.momo.contact.goto

import android.os.Bundle
import com.immomo.momo.contact.activity.SearchContactActivity
import com.immomo.momo.gotologic.AbstractGoto
import com.immomo.momo.gotologic.GotoDispatcherParam
import com.immomo.momo.gotologic.IGotoInterceptor

// 添加联系人搜索goto
class PeopleSearchBarGotoImpl : AbstractGoto() {

    companion object {
        const val GOTO_KEY: String = "goto_people_search_bar"
    }

    override fun getGotoKey(): String = GOTO_KEY

    override fun getGotoInterceptor(): MutableList<IGotoInterceptor>? = null

    override fun executeGoto(gotoDispatcherParam: GotoDispatcherParam?): Boolean {
                startActivity(gotoDispatcherParam, Bundle().apply {
                }, SearchContactActivity::class.java)
        return true
    }

}
