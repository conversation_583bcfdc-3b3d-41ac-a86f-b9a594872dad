package com.immomo.momo.contact.activity;

import android.Manifest;
import android.annotation.TargetApi;
import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.immomo.framework.base.BaseActivity;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.framework.utils.UIUtils;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.R;
import com.immomo.momo.android.view.dialog.MAlertDialog;
import com.immomo.momo.businessmodel.usermodel.IUserModel;
import com.immomo.momo.contact.PhoneContactBlockHelper;
import com.immomo.momo.mvp.common.model.ModelManager;
import com.immomo.momo.permission.PermissionChecker;
import com.immomo.momo.permission.PermissionListener;
import com.immomo.momo.permission.PermissionUtil;

public class OpenContactActivity extends BaseActivity implements OnClickListener, PermissionListener {
    private final static int REQ_PERMISSION_OPEN_CONTACT = 1002;

    private boolean isBlockContact;
    private Button btn_open;
    private ImageView iconImg;
    private TextView titleView, descTextView;
    private PhoneContactBlockHelper contactBlockHelper;
    private IUserModel userModel = ModelManager.getInstance().getModel(IUserModel.class);


    public static void start(Activity context) {
        Intent intent = new Intent(context, OpenContactActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_opencontact);

        initViews();
        initData();
        initEvents();
    }

    protected void initEvents() {
        btn_open.setOnClickListener(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        MomoTaskExecutor.cancleAllTasksByTag(getTaskTag());
        if (contactBlockHelper != null) {
            contactBlockHelper.destroy();
        }
    }

    protected void initViews() {
        setTitle("手机通讯录");
        setStatusBarColor(Color.WHITE, true);
        getWindow().setNavigationBarColor(Color.WHITE);
        btn_open = (Button) findViewById(R.id.btn_open);

        titleView = (TextView) findViewById(R.id.title_tv);
        descTextView = (TextView) findViewById(R.id.opencontact_tv_desc);
        iconImg = (ImageView) findViewById(R.id.image_icon);
    }

    private void initData() {
        isBlockContact = (KV.getUserInt(SPKeys.User.Setting.KEY_BLOCK_PHONE_CONTACT, 0) == 1);
        if (isBlockContact) {
            btn_open.setText("取消屏蔽");
            titleView.setText("已屏蔽手机联系人");
            descTextView.setText("手机联系人当前无法在陌陌中查看到你");

            iconImg.setImageDrawable(UIUtils.getDrawable(R.drawable.ic_setting_block_phone_contact));
        } else {
            boolean hasContactPermission = PermissionUtil.getInstance().checkSelfPermission(thisActivity(), Manifest.permission.READ_CONTACTS);
            if (hasContactPermission) {
                btn_open.setText("查看");
                titleView.setText("查看通讯录好友");
                descTextView.setText("");
            } else {
                btn_open.setText("开启通讯录权限");
                titleView.setText("未开启通讯录权限");
                descTextView.setText(R.string.contact_newuseropencontact_hint);

                iconImg.setImageDrawable(UIUtils.getDrawable(R.drawable.ic_no_contact_permission));
            }
        }
    }

    private void openContact() {
        finish();
        startActivity(new Intent(OpenContactActivity.this, ContactPeopleActivity.class));
    }

    @TargetApi(Build.VERSION_CODES.M)
    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_open:
                if (isBlockContact) {
                    showBlockContactDialog();

                } else {
                    boolean hasPermission = getPermissionChecker().requestPermission(Manifest.permission.READ_CONTACTS, REQ_PERMISSION_OPEN_CONTACT,false);
                    if (hasPermission) {
                        openContact();
                    }
                }
                break;
        }
    }

    private void showBlockContactDialog() {
        MAlertDialog dialog = MAlertDialog.makeConfirm(thisActivity(),
                "取消屏蔽后，手机联系人将有可能在附近的人等位置看到你，是否继续取消屏蔽？",
                "否", "是",
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        closeDialog();
                    }
                },
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        blockContact();
                        closeDialog();
                    }
                });
        dialog.setSupportDark(true);
        showDialog(dialog);
    }

    private void blockContact() {
        contactBlockHelper = new PhoneContactBlockHelper();
        contactBlockHelper.setBlockListener(new PhoneContactBlockHelper.ContactBlockListener() {
            @Override
            public void onBlockError() {

            }

            @Override
            public void onBlockCancel() {

            }

            @Override
            public void onBlockCompleted(boolean isBlock) {
                isBlockContact = false;
                KV.saveUserValue(SPKeys.User.Setting.KEY_BLOCK_PHONE_CONTACT, 0);

                startActivity(new Intent(OpenContactActivity.this, ContactPeopleActivity.class));

                finish();
            }
        });
        contactBlockHelper.blockContact(false);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        getPermissionChecker().checkPermissionResult(requestCode, grantResults);
    }

    private PermissionChecker permissionChecker;

    private PermissionChecker getPermissionChecker() {
        if (permissionChecker == null) {
            permissionChecker = new PermissionChecker(thisActivity(), this);
        }

        return permissionChecker;
    }

    @Override
    public void onPermissionGranted(int requestCode) {
        if (requestCode == REQ_PERMISSION_OPEN_CONTACT) {
            openContact();
        }
    }

    @Override
    public void onPermissionDenied(int requestCode) {
        if (requestCode == REQ_PERMISSION_OPEN_CONTACT) {
            getPermissionChecker().showPermissionGuideDialog(Manifest.permission.READ_CONTACTS);
        }
    }

    @Override
    public void onPermissionCanceled(int requestCode) {

    }
}
