package com.immomo.momo.contact.presenter;

import android.content.Context;

import com.immomo.framework.task.BaseDialogTask;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.contact.view.ISearchContactView;
import com.immomo.momo.group.bean.Group;
import com.immomo.momo.protocol.http.GroupApi;
import com.immomo.momo.protocol.http.UserApi;
import com.immomo.momo.service.group.GroupService;
import com.immomo.momo.util.StringUtils;

/**
 * <pre>
 *   author:yangsong
 *   time:2018/10/30
 *   desc: momodev
 * </pre>
 */
public class SearchContactPresenter implements ISearchContactPresenter {

    private Context mContext;
    private ISearchContactView mSearchContactView;

    public SearchContactPresenter(Context context) {
        mContext = context;
    }

    @Override
    public void bindView(ISearchContactView searchContactView) {
        mSearchContactView = searchContactView;
    }

    @Override
    public void searchUser(String keyWord) {
        MomoTaskExecutor.executeUserTask(getTaskTag(), new GetUserTask(keyWord));
    }

    @Override
    public void searchGroup(String keyWord) {
        MomoTaskExecutor.executeUserTask(getTaskTag(), new GetGroupTask(keyWord));
    }

    @Override
    public void onDestroy() {
        MomoTaskExecutor.cancleAllTasksByTag(getTaskTag());
    }

    private class GetUserTask extends BaseDialogTask<String, Object, String> {
        private String otherId;

        public GetUserTask(String otherId) {
            this.otherId = otherId;
        }

        @Override
        protected String getDispalyMessage() {
            return "正在查找,请稍候...";
        }

        @Override
        protected String executeTask(String... params) throws Exception {
            return UserApi.getInstance().checkNiceId(otherId);
        }

        @Override
        protected void onTaskSuccess(String result) {
            if (StringUtils.isBlank(result)) {
                return;
            }
            if (mSearchContactView != null) {
                mSearchContactView.searchUserSuccess(result);
            }
        }
    }

    private class GetGroupTask extends BaseDialogTask<String, Object, Integer> {
        String groupId;
        Group group;

        public GetGroupTask(String groupId) {
            this.groupId = groupId;
        }

        @Override
        protected String getDispalyMessage() {
            return "正在查找,请稍候...";
        }

        @Override
        protected Integer executeTask(String... params) throws Exception {
            group = new Group(groupId);
            int result = GroupApi.getInstance().downloadGroupProfile(group.gid, group);
            if (result >= 0) {
                GroupService service = GroupService.getInstance();
                service.saveOrUpdate(group, false);
            }
            return result;
        }

        @Override
        protected void onTaskSuccess(Integer result) {
            if (result >= 0) {
                if (mSearchContactView != null) {
                    mSearchContactView.searchGroupSuccess(group.gid);
                }
            }
        }
    }

    public Object getTaskTag() {
        return this.getClass().getName() + '@' + Integer.toHexString(this.hashCode());
    }
}
