package com.immomo.momo.contact.presenter;

import android.app.Activity;

import com.immomo.framework.base.BaseActivity;
import com.immomo.framework.task.BaseDialogTask;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.R;
import com.immomo.momo.businessmodel.usermodel.IUserModel;
import com.immomo.momo.contact.view.IContactPeopleView;
import com.immomo.momo.mvp.common.model.ModelManager;
import com.immomo.momo.protocol.http.UserApi;
import com.immomo.momo.protocol.http.requestbean.AddContactApplyResponce;
import com.immomo.momo.service.bean.Contact;
import com.immomo.momo.service.bean.ContactGroup;
import com.immomo.momo.service.bean.LiveGuide;
import com.immomo.momo.service.contacts.ContactsService;
import com.immomo.momo.util.Cn2SpellUtil;
import com.immomo.momo.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import static com.immomo.momo.contact.activity.ContactPeopleActivity.CONTACT_TYPE;

/**
 * <pre>
 *   author:yangsong
 *   time:2018/10/25
 *   desc: momodev
 * </pre>
 */
public class ContactPeoplePresenter implements IContactPeoplePresenter {

    private IContactPeopleView mIContactPeopleView;
    private IUserModel mUserModel;
    private List<ContactGroup> mDataList = new ArrayList<>();

    private Comparator<Contact> comparator = new Comparator<Contact>() {
        @Override
        public int compare(Contact lhs, Contact rhs) {
            if (StringUtils.isEmpty(lhs.pinyin)) {
                lhs.pinyin = Cn2SpellUtil.converterToSpell(lhs.name);
            }
            if (StringUtils.isEmpty(rhs.pinyin)) {
                rhs.pinyin = Cn2SpellUtil.converterToSpell(rhs.name);
            }
            return lhs.pinyin.compareTo(rhs.pinyin);
        }
    };

    public ContactPeoplePresenter() {
        mUserModel = ModelManager.getInstance().getModel(IUserModel.class);
    }

    @Override
    public void initData() {

    }

    @Override
    public void acceptContactPeople() {
        MomoTaskExecutor.executeUserTask(getTaskTag(), new UploadContactTask());
    }

    @Override
    public void requestGuideLive() {
        MomoTaskExecutor.executeUserTask(getTaskTag(), new GetLiveGuideTask());
    }

    @Override
    public void apply(Activity context, String str, String ph, int gpos, int cpos) {
        MomoTaskExecutor.executeUserTask(getTaskTag(), new ApplyTask(context, str, ph, gpos, cpos));
    }

    public int getTaskTag() {
        return hashCode();
    }

    @Override
    public void bindView(IContactPeopleView iContactPeopleView) {
        this.mIContactPeopleView = iContactPeopleView;
    }

    @Override
    public void onDestroy() {
        MomoTaskExecutor.cancleAllTasksByTag(getTaskTag());
    }

    @Override
    public IUserModel getUserModel() {
        return mUserModel;
    }

    //fixme 改成static
    private class UploadContactTask extends BaseDialogTask<Object, Object, List<ContactGroup>> {

        @Override
        protected boolean mayCancleOnTouchOutSide() {
            return false;
        }

        @Override
        protected boolean mayCancleOnBackPress() {
            return false;
        }

        public UploadContactTask() {
            super(((BaseActivity) mIContactPeopleView.getContext()));
        }

        @Override
        protected String getDispalyMessage() {
            return "正在加载中...";
        }

        @Override
        protected List<ContactGroup> executeTask(Object... params) throws Exception {
            ContactsService.getInstance().clearCache();
            Map<String, String> map = ContactsService.getInstance().findContactMap(mIContactPeopleView.getEncode());
            if (map.keySet().size() <= 0) {
                return null;
            }
            List<Contact> list = UserApi.getInstance().uploadContacts(map.keySet(), 2);
            ContactGroup addGroup = new ContactGroup();
            ContactGroup inviteGroup = new ContactGroup();
            ContactGroup friendGroup = new ContactGroup();
            for (Contact contact : list) {
                if (contact.user != null && contact.user.momoid != null && mUserModel.getCurrentUser() != null
                        && contact.user.momoid.equals(mUserModel.getCurrentUser().momoid)) {
                    continue;
                }
                contact.name = map.get(contact.phonenumMD5);
                if (!StringUtils.isEmpty(contact.name)) {
                    switch (contact.relation) {
                        case Contact.RELATION_ADD:
                            addGroup.contacts.add(contact);
                            break;
                        case Contact.RELATION_INVITE:
                            inviteGroup.contacts.add(contact);
                            break;
                        case Contact.RELATION_FRIEND:
                            friendGroup.contacts.add(contact);
                            break;
                        default:
                            break;
                    }
                }
            }
            addGroup.groupname = addGroup.contacts.size() + mIContactPeopleView.getContext().getResources().getString(R.string.contact_grouptitle1);
            inviteGroup.groupname = inviteGroup.contacts.size() + mIContactPeopleView.getContext().getResources().getString(R.string.contact_grouptitle2);
            friendGroup.groupname = friendGroup.contacts.size() + mIContactPeopleView.getContext().getResources().getString(R.string.contact_grouptitle3);
            Collections.sort(inviteGroup.contacts, comparator);
            Collections.sort(friendGroup.contacts, comparator);
            List<ContactGroup> groupList = new ArrayList<>();
            if (addGroup.contacts != null && !addGroup.contacts.isEmpty()) {
                groupList.add(addGroup);
            }
            if (inviteGroup.contacts != null && !inviteGroup.contacts.isEmpty()) {
                groupList.add(inviteGroup);
            }
            if (friendGroup.contacts != null && !friendGroup.contacts.isEmpty()) {
                groupList.add(friendGroup);
            }
            mDataList.clear();
            mDataList.addAll(groupList);
            if (map.size() > 0) {
                ContactsService.getInstance().saveUploadedList(map.keySet());
            }
            return mDataList;
        }

        @Override
        protected void onTaskSuccess(List<ContactGroup> result) {
            if (result == null) {
                // 展示空占位
                if (mDataList.isEmpty()) {
                    notifyData();
                }
                return;
            }
            notifyData();
        }
    }

    private void notifyData() {
        if (mIContactPeopleView != null && mIContactPeopleView.getAdapter() != null) {
            mIContactPeopleView.setEmptyViewVis(mDataList.isEmpty());
            mIContactPeopleView.getAdapter().setDataList(mDataList);
        }
    }

    private class ApplyTask extends BaseDialogTask<Object, Object, AddContactApplyResponce> {

        private String reason;
        private String phone;
        private int gpos;
        private int cpos;

        public ApplyTask(Activity context, String str, String ph, int gpos, int cpos) {
            super(context);
            reason = str;
            this.phone = ph;
            this.gpos = gpos;
            this.cpos = cpos;
        }

        @Override
        protected AddContactApplyResponce executeTask(Object... params) throws Exception {
            return UserApi.getInstance().addContactApply(reason, phone);
        }

        @Override
        protected void onTaskSuccess(AddContactApplyResponce result) {
            if (mIContactPeopleView != null) {
                mIContactPeopleView.applySuccess(result, phone, gpos, cpos);
            }
        }
    }

    /**
     * 获取引导关注直播goto
     */
    private class GetLiveGuideTask extends BaseDialogTask<Void, Void, LiveGuide> {

        @Override
        protected boolean mayCancleOnBackPress() {
            return false;
        }

        @Override
        protected boolean mayCancleOnTouchOutSide() {
            return false;
        }

        @Override
        protected LiveGuide executeTask(Void... params) throws Exception {
            return UserApi.getInstance().contactGuideURText(CONTACT_TYPE);
        }

        @Override
        protected void onTaskSuccess(final LiveGuide liveGuide) {
            super.onTaskSuccess(liveGuide);
            if (mIContactPeopleView != null && liveGuide != null) {
                mIContactPeopleView.getLiveGuideSuccess(liveGuide);
            }
        }
    }
}
