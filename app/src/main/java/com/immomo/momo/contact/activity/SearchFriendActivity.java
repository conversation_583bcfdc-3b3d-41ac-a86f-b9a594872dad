package com.immomo.momo.contact.activity;

import android.database.sqlite.SQLiteException;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.AdapterView;

import com.immomo.momo.R;
import com.immomo.momo.android.activity.BaseAccountActivity;
import com.immomo.momo.android.view.ClearableEditText;
import com.immomo.momo.android.view.HandyListView;
import com.immomo.momo.router.ProfileGotoOptions;
import com.immomo.momo.router.ProfileRouter;;
import com.immomo.momo.router.RefreshTag;
import com.immomo.momo.contact.adapter.FriendsListViewAdapterV2;
import com.immomo.momo.service.bean.user.ContactUser;
import com.immomo.momo.service.user.UserService;
import com.immomo.momo.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

import info.xudshen.android.appasm.AppAsm;

public class SearchFriendActivity extends BaseAccountActivity {

    private HandyListView listview;
    private ClearableEditText searchEditText;

    private List<ContactUser> bothList;
    private FriendsListViewAdapterV2 adapter;
    private UserService userService;

    @Override
    protected void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        setContentView(R.layout.activity_search_friend);
        initViews();
        initEvents();
        initInternal();
        initData();
    }

    protected void initData() {
        initBothList();
        initBothAdapter();
    }

    private void initInternal() {
        userService = UserService.getInstance();
    }

    @Override
    protected void initEvents() {
        listview.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> arg0, View view, int position, long id) {
                ProfileGotoOptions options = new ProfileGotoOptions(adapter.getItem(position).momoid);
                options.setRequestTypeTag(RefreshTag.LOCAL);
                AppAsm.getRouter(ProfileRouter.class).gotoProfile(thisActivity(), options);
            }
        });
        searchEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String key = s.toString().trim();
                if (StringUtils.isEmpty(key)) {
                    adapter.clear();
                    searchEditText.requestFocus();
                } else {
                    try { // 一些输入法预显示效果 出现一些特殊序列字符导致数据库查询崩溃 例如： x'z
                        List<ContactUser> list = userService.searchFriendList(key);
                        adapter.replace(list);
                    } catch (SQLiteException e) {
                        log.e(e);
                    }
                }
            }
        });

        findViewById(R.id.listview_empty).setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }

    private void initBothList() {
        bothList = new ArrayList<>();
    }

    private void initBothAdapter() {
        adapter = new FriendsListViewAdapterV2(thisActivity(), bothList, listview);
        adapter.setShowSingleColor(true);
        listview.setAdapter(adapter);
    }

    @Override
    protected void initViews() {
        listview = (HandyListView) findViewById(R.id.listview);
        searchEditText = (ClearableEditText) toolbarHelper.getToolbar()
                .findViewById(R.id.toolbar_search_edittext);
        searchEditText.setHint("请输入好友名字");

        Animation animation = AnimationUtils.loadAnimation(this, R.anim.anim_slide_in_from_right);
        animation.setDuration(getResources().getInteger(R.integer.config_activity_parallax));
        toolbarHelper.getToolbar().startAnimation(animation);

        View view = getLayoutInflater().inflate(R.layout.view_searchcontact_empty, listview, false);
        listview.addEmptyView(view);
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        overridePendingTransition(R.anim.anim_fade_in, R.anim.anim_slide_out_to_right);
    }
}
