package com.immomo.momo.contact.view;

import android.content.Context;

import com.immomo.momo.feed.adapter.ContactPeopleAdapter;
import com.immomo.momo.protocol.http.requestbean.AddContactApplyResponce;
import com.immomo.momo.service.bean.LiveGuide;

/**
 * <pre>
 *   author:yangsong
 *   time:2018/10/25
 *   desc: momodev
 * </pre>
 */
public interface IContactPeopleView {

    boolean getEncode();

    Context getContext();

    ContactPeopleAdapter getAdapter();

    void closeDialog();

    void getLiveGuideSuccess(LiveGuide liveGuide);

    void setEmptyViewVis(boolean show);

    void applySuccess(AddContactApplyResponce responce, String phone, int gpos, int cpos);

}
