package com.immomo.momo.contact.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.immomo.framework.imageloader.ImageLoaderUtil;
import com.immomo.framework.imageloader.ImageType;
import com.immomo.framework.utils.UIUtils;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.android.adapter.BaseListAdapter;
import com.immomo.momo.android.view.BadgeView;
import com.immomo.momo.android.view.EmoteTextView;
import com.immomo.momo.android.view.HandyListView;
import com.immomo.momo.service.bean.ImageLoader;
import com.immomo.momo.service.bean.user.ContactUser;
import com.immomo.momo.util.LoadImageUtil;
import com.immomo.momo.util.ShowTimeAndDistanceUtils;
import com.immomo.momo.util.StringUtils;

import java.util.List;

public class FriendsListViewAdapterV2 extends BaseListAdapter<ContactUser> {

    public final static int SORT_TYPE_DISTANCE = 0; // 距离排序
    public final static int SORT_TYPE_LOGIN = 1; // 最后登录时间排序
    public final static int SORT_TYPE_ADD_TIME = 2; // 添加时间排序
    public final static int SORT_TYPE_FIRST_LETTER = 3; //首字母排序

    private HandyListView listView = null;
    private boolean showRelationIcon = false;
    private boolean showsingleColor = false;
    private int roundCorner;
    private boolean isUseOnlinStatus = false;

    public FriendsListViewAdapterV2(Context context, List<ContactUser> users, HandyListView listView) {
        this(context, users, listView, false);
    }

    public FriendsListViewAdapterV2(Context context, List<ContactUser> users, HandyListView listView, boolean showRelationIcon) {
        this(context, users, listView, showRelationIcon, false);
    }

    public FriendsListViewAdapterV2(Context context, List<ContactUser> users, HandyListView listView, boolean showRelationIcon, boolean isUseOnlinStatus) {
        super(context, users);
        this.showRelationIcon = showRelationIcon;
        this.context = context;
        this.listView = listView;
        this.roundCorner = context.getResources().getDimensionPixelSize(R.dimen.avatar_a3_corner);
        this.isUseOnlinStatus = isUseOnlinStatus;

    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if (convertView == null) {
            final ViewHolder holder = new ViewHolder();
            convertView = inflate(R.layout.listitem_user);
            holder.faceView = (ImageView) convertView.findViewById(R.id.userlist_item_iv_face);
            holder.nameView = (TextView) convertView.findViewById(R.id.userlist_item_tv_name);
            holder.distanceView = (TextView) convertView.findViewById(R.id.userlist_item_tv_distance);
            holder.timeView = (TextView) convertView.findViewById(R.id.userlist_tv_time);
            holder.signatureView = (EmoteTextView) convertView.findViewById(R.id.userlist_item_tv_sign);
            holder.badgeView = (BadgeView) convertView.findViewById(R.id.userlist_bage);
            holder.badgeView.setGenderlayoutVisable(true);
            holder.signextIconView = (ImageView) convertView.findViewById(R.id.userlist_item_pic_sign);
            holder.timeDriver = convertView.findViewById(R.id.userlist_tv_timedriver);
            convertView.setTag(R.id.tag_userlist_item, holder);
        }

        final ContactUser user = getItem(position);
        final ViewHolder holder = (ViewHolder) convertView.getTag(R.id.tag_userlist_item);

        if (isUseOnlinStatus) {
            String onlineTimeAndDistance = ShowTimeAndDistanceUtils.getOnlineTimeAndDistanceV2(user);
            if (!TextUtils.isEmpty(onlineTimeAndDistance)) {
                holder.distanceView.setVisibility(View.VISIBLE);
                holder.timeDriver.setVisibility(View.GONE);
                holder.timeView.setVisibility(View.GONE);
                holder.distanceView.setText(onlineTimeAndDistance);
            } else {
                holder.distanceView.setVisibility(View.GONE);
                holder.timeDriver.setVisibility(View.GONE);
                holder.timeView.setVisibility(View.GONE);
            }
        } else {
            holder.distanceView.setText(user.distanceString);
            holder.distanceView.setVisibility(user.showDistance() || !user.showTime() && !user.showDistance() ? View.VISIBLE : View.GONE);
            holder.timeView.setText(user.agoTime);
            holder.timeView.setVisibility(user.showTime() ? View.VISIBLE : View.GONE);
            holder.timeDriver.setVisibility(user.showTime() && user.showDistance() ? View.VISIBLE : View.GONE);
        }


        holder.nameView.setText(user.getDisplayName());
        if (user.isMomoVip()) {
            holder.nameView.setTextColor(UIUtils.getColor(R.color.font_vip_name));
        } else {
            holder.nameView.setTextColor(UIUtils.getColor(R.color.color_text_3b3b3b));
        }
        holder.signatureView.setText(user.getSignexEmoteContent());
        if (showsingleColor) {
            if (!StringUtils.isEmpty(user.signexColor)) {
                holder.signatureView.setTextColor(MomoKit.parseColor(user.signexColor));
            } else {
                holder.signatureView.setTextColor(getContext().getResources().getColor(R.color.color_text_aaaaaa));
            }
        }
        if (!StringUtils.isEmpty(user.signexIcon)) {

            holder.signextIconView.setVisibility(View.VISIBLE);
            LoadImageUtil.loadImage(new ImageLoader(user.signexIcon, true), holder.signextIconView, null, ImageType.IMAGE_TYPE_URL);
        } else {
            holder.signextIconView.setVisibility(View.GONE);
        }

        holder.badgeView.setUser(user, showRelationIcon);
        holder.badgeView.updateGenderTextState(user);
        ImageLoaderUtil.loadRoundImage(user.getLoadImageId(), ImageType.IMAGE_TYPE_ALBUM_250x250,
                holder.faceView, listView, roundCorner, true, 0);
        return convertView;
    }


    public void setShowSingleColor(boolean showsingleColor) {
        this.showsingleColor = showsingleColor;
    }

    private static class ViewHolder {
        // List显示部分
        public ImageView faceView;
        public TextView nameView;
        public TextView distanceView;
        public TextView timeView;
        public EmoteTextView signatureView;
        public ImageView signextIconView;
        public BadgeView badgeView;
        public View timeDriver;
    }
}

