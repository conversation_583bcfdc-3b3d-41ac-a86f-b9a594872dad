package com.immomo.momo.contact;

import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.framework.task.BaseDialogTask;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.protocol.http.SettingApi;
import com.immomo.momo.service.contacts.ContactsService;

import java.util.Map;

/**
 * Created by huang.liangjie on 2017/8/2.
 *
 * Momo Tech 2011-2017 © All Rights Reserved.
 */

public class PhoneContactBlockHelper {
    private ContactBlockListener blockListener;

    public void destroy() {
        MomoTaskExecutor.cancleAllTasksByTag(getTaskTag());
    }

    private Object getTaskTag() {
        return this.getClass().getName() + '@' + Integer.toHexString(this.hashCode());
    }

    public void blockContact(boolean isBlock) {
        MomoTaskExecutor.executeUserTask(getTaskTag(), new BlockContactTask(isBlock));
    }

    public void setBlockListener(ContactBlockListener listener) {
        this.blockListener = listener;
    }

    private void blockComplete(int value) {
        KV.saveUserValue(SPKeys.User.Setting.KEY_BLOCK_PHONE_CONTACT, value);
        if (blockListener != null) {
            blockListener.onBlockCompleted((value == 1));
        }
    }

    private class BlockContactTask extends BaseDialogTask<Object, Object, Object> {
        private boolean isBlock;

        public BlockContactTask(boolean isBlock) {
            this.isBlock = isBlock;
        }

        @Override
        protected Object executeTask(Object... params) throws Exception {
            Map<String, String> map = null;
            if (isBlock) {
                ContactsService.getInstance().clearCache();
                map = ContactsService.getInstance().findContactMap(true);
            }

            SettingApi.getInstance().blockContact(isBlock, map != null ? map.keySet() : null);
            return null;
        }

        @Override
        protected boolean mayCancleOnTouchOutSide() {
            return false;
        }

        @Override
        protected void onTaskSuccess(Object s) {
            super.onTaskSuccess(s);

            if (isBlock) {
                blockComplete(1);
            } else {
                blockComplete(0);
            }
        }

        @Override
        protected void onTaskError(Exception e) {
            super.onTaskError(e);
            if (blockListener != null) {
                blockListener.onBlockError();
            }
        }

        @Override
        protected void onCancelled() {
            if (blockListener != null) {
                blockListener.onBlockCancel();
            }
        }
    }

    public interface ContactBlockListener {
        void onBlockError();

        void onBlockCancel();

        void onBlockCompleted(boolean isBlock);
    }
}
