package com.immomo.momo.contact.activity;

import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.View;

import com.immomo.framework.base.IStepConfigDataProvider;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.R;
import com.immomo.momo.android.activity.BaseAccountActivity;
import com.immomo.momo.android.view.MomoRefreshListView;
import com.immomo.momo.android.view.MomoRefreshListView.OnPullToRefreshListener;
import com.immomo.momo.android.view.dialog.MAlertDialog;
import com.immomo.momo.android.view.dialog.MProcessDialog;
import com.immomo.momo.businessmodel.statistics.BusinessConfig;
import com.immomo.momo.businessmodel.statistics.RecommendUserListConfig;
import com.immomo.momo.contact.adapter.RecommendListAdapter;
import com.immomo.momo.router.ProfileRouter;;
import com.immomo.momo.protocol.http.UserApi;
import com.immomo.momo.service.bean.ContactNotice;
import com.immomo.momo.service.bean.User;

import java.util.ArrayList;
import java.util.List;

import info.xudshen.android.appasm.AppAsm;

public class RecommendUserListActivity extends BaseAccountActivity implements IStepConfigDataProvider<BusinessConfig> {
    private MomoRefreshListView listview;
    private RecommendListAdapter adapter;
    private List<ContactNotice> recommendList = null;

    @Override
    protected void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        setContentView(R.layout.activity_recommenduserlist);
        initViews();
        initEvents();
        initData();
    }

    @Override
    protected void initEvents() {
        listview.setOnPullToRefreshListener(new OnPullToRefreshListener() {
            @Override
            public void onPullToRefresh() {
                execAsyncTask(new ReflushTask(thisActivity()));
            }

            @Override
            public void onLoadingViewClick() {

            }
        });
    }

    protected void initAdapterEvents() {
        adapter.setOnItemClickListener(new RecommendListAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(View view, int position, long id) {
                ContactNotice contactNotice = adapter.getItem(position);
                if (contactNotice != null) {
                    AppAsm.getRouter(ProfileRouter.class).gotoProfile(thisActivity(), contactNotice.getRemoteMomoid());
                }
            }
        });
        adapter.setOnItemLongClickListener(new RecommendListAdapter.OnItemLongClickListener() {
            @Override
            public boolean onItemLongClick(View view, int position, long id) {
                final ContactNotice contactNotice = adapter.getItem(position);
                if (contactNotice == null) return false;

                MAlertDialog dialog = MAlertDialog.makeConfirm(RecommendUserListActivity.this, "删除推荐?",
                        new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                execAsyncTask(new RemoveRecommend(RecommendUserListActivity.this, contactNotice));
                                dialog.dismiss();
                            }
                        });
                dialog.setCancelable(true);
                dialog.setSupportDark(true);
                dialog.show();
                return true;
            }
        });
    }

    @Override
    protected void initViews() {
        listview = (MomoRefreshListView) findViewById(R.id.listview);
        listview.setFastScrollEnabled(false);
        listview.setTimeEnable(false);
        setTitle("可能认识的人");
    }

    @Override
    protected void initData() {
        super.initData();
        recommendList = new ArrayList<ContactNotice>();
        adapter = new RecommendListAdapter(this, new ArrayList<ContactNotice>(), recommendList, ((User) currentUser));
        initAdapterEvents();
        listview.setAdapter(adapter);

        listview.pullToReflush();
    }

    @Override
    public BusinessConfig getStepConfigData() {
        return RecommendUserListConfig.INSTANCE;
    }


    private class ReflushTask extends MomoTaskExecutor.Task<Object, Object, List<ContactNotice>> {

        public ReflushTask(Context context) {
            super(context);
        }

        @Override
        protected List<ContactNotice> executeTask(Object... params) throws Exception {
            List<ContactNotice> list = new ArrayList<ContactNotice>();
            UserApi.getInstance().downloadRecommendList(list);
            return list;
        }

        @Override
        protected void onTaskFinish() {
            super.onTaskFinish();
            listview.refreshComplete();
        }

        @Override
        protected void onTaskSuccess(List<ContactNotice> result) {
            super.onTaskSuccess(result);
            recommendList.clear();
            recommendList.addAll(result);
            adapter.notifyDataSetChanged();
        }

    }

    private class RemoveRecommend extends MomoTaskExecutor.Task<Object, Object, Object> {
        ContactNotice contactNotice = null;

        public RemoveRecommend(Context context, ContactNotice contactNotice) {
            super(context);
            this.contactNotice = contactNotice;
        }

        @Override
        protected void onPreTask() {
            showDialog(new MProcessDialog(RecommendUserListActivity.this));
        }

        @Override
        protected Object executeTask(Object... params) throws Exception {
            UserApi.getInstance().removeRecommend(contactNotice.getRemoteMomoid());
            recommendList.remove(contactNotice);

            return null;
        }

        @Override
        protected void onTaskFinish() {
            closeDialog();
        }

        @Override
        protected void onTaskSuccess(Object result) {
            super.onTaskSuccess(result);
            adapter.notifyDataSetChanged();
        }
    }
}
