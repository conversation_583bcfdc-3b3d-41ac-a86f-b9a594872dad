package com.immomo.momo.contact.activity;

import android.Manifest;
import android.content.Context;
import android.content.DialogInterface.OnClickListener;
import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.method.LinkMovementMethod;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.cosmos.mdlog.MDLog;
import com.immomo.framework.base.BaseActivity;
import com.immomo.framework.base.IStepConfigDataProvider;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.framework.utils.UIUtils;
import com.immomo.framework.view.pulltorefresh.MomoPtrExpandableListView;
import com.immomo.mmutil.log.Log4Android;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.Configs;
import com.immomo.momo.R;
import com.immomo.momo.android.view.EmoteEditeText;
import com.immomo.momo.android.view.ListEmptyView;
import com.immomo.momo.android.view.UnderlineTextView;
import com.immomo.momo.android.view.dialog.MAlertDialog;
import com.immomo.momo.businessmodel.statistics.BusinessConfig;
import com.immomo.momo.businessmodel.statistics.ContactPeopleConfig;
import com.immomo.momo.common.view.dialog.CommonTipDialog;
import com.immomo.momo.contact.presenter.ContactPeoplePresenter;
import com.immomo.momo.contact.presenter.IContactPeoplePresenter;
import com.immomo.momo.contact.view.IContactPeopleView;
import com.immomo.momo.feed.adapter.ContactPeopleAdapter;
import com.immomo.momo.innergoto.helper.ActivityHandler;
import com.immomo.momo.innergoto.matcher.SayHiMatcher;
import com.immomo.momo.innergoto.matcher.helper.ActivityMatcher;
import com.immomo.momo.permission.PermissionChecker;
import com.immomo.momo.permission.PermissionListener;
import com.immomo.momo.protocol.http.requestbean.AddContactApplyResponce;
import com.immomo.momo.router.ProfileGotoOptions;
import com.immomo.momo.router.ProfileRouter;
import com.immomo.momo.router.RefreshTag;
import com.immomo.momo.service.bean.Contact;
import com.immomo.momo.service.bean.ContactGroup;
import com.immomo.momo.service.bean.LiveGuide;
import com.immomo.momo.service.contacts.ContactsService;
import com.immomo.momo.util.DialogUtils;
import com.immomo.momo.util.MomoKit;
import com.immomo.momo.util.StringUtils;
import com.immomo.momo.util.TooLongValidator;

import java.util.ArrayList;
import java.util.List;

import info.xudshen.android.appasm.AppAsm;

public class ContactPeopleActivity extends BaseActivity implements PermissionListener, IContactPeopleView, IStepConfigDataProvider<BusinessConfig> {

    public static final int CONTACT_TYPE = 2;
    private final static int REQ_PERMISSION_OPEN_CONTACT = 1002;

    private ContactPeopleAdapter adapter;
    private MomoPtrExpandableListView listView;
    private List<ContactGroup> dataList = new ArrayList<>();
    private boolean encode = true;
    private LiveGuide liveGuide;
    private LinearLayout blockContactTip;
    private ListEmptyView emptyView;
    private IContactPeoplePresenter mContactPeoplePresenter;
    private PermissionChecker permissionChecker;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        boolean isBlockContact = (KV.getUserInt(SPKeys.User.Setting.KEY_BLOCK_PHONE_CONTACT, 0) == 1);
        if (isBlockContact) {
            OpenContactActivity.start(this);
            finish();
            return;
        }
        setContentView(R.layout.activity_contactpeople);
        initViews();
        initEvents();
        mContactPeoplePresenter = new ContactPeoplePresenter();
        boolean hasPermission = getPermissionChecker().checkPermission(new String[]{Manifest.permission.READ_CONTACTS});
        if (hasPermission) {
            initData();
        }
    }

    @Override
    protected boolean isLightTheme() {
        return !com.immomo.momo.util.MomoKit.INSTANCE.isDarkMode(this);
    }

    protected void initViews() {
        getWindow().getDecorView().setBackgroundColor(UIUtils.getColor(this, R.color.color_f0f0f4_to_000));
        setTitle(R.string.contact_title);
        emptyView = (ListEmptyView) findViewById(R.id.listview_emptyview);
        emptyView.setIcon(R.drawable.ic_contact_empty);
        emptyView.setContentStr("暂无通讯录好友");
        emptyView.setContentTextView(R.color.color_323333_to_80f);

        listView = (MomoPtrExpandableListView) findViewById(R.id.listview_contact);
        blockContactTip = (LinearLayout) findViewById(R.id.block_user_tip_layout);
        listView.setMMHeaderView(LayoutInflater.from(this).inflate(R.layout.listitem_contactgroup, listView, false));
        listView.setLoadMoreButtonVisible(false);

        adapter = new ContactPeopleAdapter(dataList, listView);
        listView.setAdapter(adapter);
        //重写左上角导航监听
        getToolbarHelper().getToolbar().setNavigationOnClickListener((View.OnClickListener) v -> {
            Log4Android.getInstance().i("NavigationOnClickListener");
            gotoLiveGuide();
        });
        if (MomoKit.INSTANCE.isDarkMode(this)) {
            getWindow().setNavigationBarColor(Color.BLACK);
        }
    }

    @Override
    public void setEmptyViewVis(boolean show) {
        emptyView.setVisibility(show ? View.VISIBLE : View.GONE);
    }

    protected void initEvents() {
        listView.setOnChildClickListener((parent, v, groupPosition, childPosition, id) -> {
            switch (dataList.get(groupPosition).contacts.get(childPosition).relation) {
                case Contact.RELATION_ADD:
                    if (v.getId() == R.id.btn_operate) {
                        showApplyDialog(groupPosition, childPosition);
                    } else {
                        AppAsm.getRouter(ProfileRouter.class).gotoProfile(thisActivity(), dataList.get(groupPosition).contacts.get(childPosition).momoid);
                    }
                    break;
                case Contact.RELATION_INVITE:
                    sendSms(dataList.get(groupPosition).contacts.get(childPosition).phonenumMD5, UIUtils.getString(R.string.contact_apply_smscontent, mContactPeoplePresenter.getUserModel().getCurrentUser().momoid));
                    break;
                case Contact.RELATION_FRIEND:
                    ProfileGotoOptions options = new ProfileGotoOptions(dataList.get(groupPosition).contacts.get(childPosition).momoid);
                    options.setRequestTypeTag(RefreshTag.LOCAL);
                    AppAsm.getRouter(ProfileRouter.class).gotoProfile(thisActivity(), options);
                    break;
                default:
                    break;
            }
            return true;
        });
        listView.setOnGroupClickListener((parent, v, groupPosition, id) -> true);

        blockContactTip.setOnClickListener(v -> {
            MAlertDialog dialog = MAlertDialog.makeSingleButtonDialog(thisActivity(), "可在设置-隐私设置中屏蔽手机联系人", "知道了", (OnClickListener) (dialog1, which) -> closeDialog());
            dialog.setSupportDark(true);
            showDialog(dialog);
        });
    }

    @Override
    protected void onStart() {
        super.onStart();
        checkPermission();
    }

    protected void initData() {
        mContactPeoplePresenter.bindView(this);
        mContactPeoplePresenter.acceptContactPeople();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mContactPeoplePresenter != null) {
            mContactPeoplePresenter.onDestroy();
        }
    }

    private boolean checkPermission() {
        return getPermissionChecker().requestPermission(Manifest.permission.READ_CONTACTS, REQ_PERMISSION_OPEN_CONTACT, true);
    }

    private PermissionChecker getPermissionChecker() {
        if (permissionChecker == null) {
            permissionChecker = new PermissionChecker(thisActivity(), this);
        }
        return permissionChecker;
    }

    @Override
    public void onPermissionGranted(int requestCode) {
        if (requestCode == REQ_PERMISSION_OPEN_CONTACT) {
            initData();
        }
    }

    @Override
    public void onPermissionDenied(int requestCode) {
        if (requestCode == REQ_PERMISSION_OPEN_CONTACT) {
            getPermissionChecker().showPermissionGuideDialog(Manifest.permission.READ_CONTACTS, true);
        }
    }

    @Override
    public void onPermissionCanceled(int requestCode) {
        //待实现
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        getPermissionChecker().checkPermissionResult(requestCode, grantResults);
    }

    private void showApplyDialog(final int gpos, final int cpos) {
        View view = LayoutInflater.from(this).inflate(R.layout.dialog_contactpeople_apply, null);
        final EmoteEditeText editor = view.findViewById(R.id.edittext_reason);
        editor.addTextChangedListener(new TooLongValidator(Configs.NAME_LENGTH, editor));
        MAlertDialog dialog = new MAlertDialog(ContactPeopleActivity.this);
        dialog.setTitle("好友验证");
        dialog.setContentView(view);
        dialog.setButton(MAlertDialog.INDEX_RIGHT, getString(R.string.dialog_btn_confim), (dialog1, which) -> {
            if (mContactPeoplePresenter != null) {
                mContactPeoplePresenter.apply(ContactPeopleActivity.this, editor.getText().toString().trim(), dataList.get(gpos).contacts.get(cpos).phonenumMD5, gpos, cpos);
                closeDialog();
            }

        });
        dialog.setButton(MAlertDialog.INDEX_LEFT, getString(R.string.dialog_btn_cancel), (dialog1, which) -> {
            closeDialog();
        });
        dialog.setSupportDark(true);
        showDialog(dialog);
    }

    private void sendSms(String phone, String smscontent) {
        if (!checkPermission()) {
            return;
        }
        if (StringUtils.isEmpty(phone))
            return;
        try {
            Uri uri = Uri.parse("smsto:" + ContactsService.getInstance().findContactNumberByMd5Number(phone));
            Intent it = new Intent(Intent.ACTION_SENDTO, uri);
            it.putExtra("sms_body", smscontent);
            startActivity(it);
        } catch (Exception t) {
            Toaster.show(R.string.no_sms_model);
        }
    }

    public void showSendMessageAlert(final String phone) {
        MAlertDialog dialog = MAlertDialog.makeConfirm(ContactPeopleActivity.this, "请求已发送成功，该好友可能不能及时收到添加好友消息，是否用短信立即通知他？", "取消", "短信通知", null, (OnClickListener) (dialog1, which) -> sendSms(phone, "我是" + mContactPeoplePresenter.getUserModel().getCurrentUser().name
                + "，想在陌陌上加你好友，回来跟我一起玩吧。" + "下载地址：www.immomo.com 我的陌陌号是" + mContactPeoplePresenter.getUserModel().getCurrentUser().momoid));
        dialog.setCanceledOnTouchOutside(false);
        dialog.setSupportDark(true);
        showDialog(dialog);
    }

    @Override
    public void startActivityForResult(Intent intent, int requestCode, Bundle options, String from) {
        // 把类型名字带到otherprofile界面去
        if (intent.getComponent() != null && ActivityMatcher.isUserProfileActivity(intent.getComponent().getClassName())) {
            intent.putExtra(KEY_FROM_DATA, "手机通讯录");
            intent.putExtra(KEY_SOURCE_DATA, SayHiMatcher.buildSayHiSourceByParams(ContactPeopleActivity.class.getName(), null, null));
        }
        super.startActivityForResult(intent, requestCode, options, from);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            gotoLiveGuide();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * 引导关注直播，如果获取到数据则goto，否则finish
     */
    private void gotoLiveGuide() {
        if (liveGuide != null && !StringUtils.isEmpty(liveGuide.text) && !StringUtils.isEmpty(liveGuide.contactGoto)) {
            if (thisActivity() != null && !thisActivity().isDestroyed()) {
                ActivityHandler.executeAction(liveGuide.contactGoto, thisActivity());
                finish();
            }
        } else {
            finish();
        }
    }

    @Override
    public boolean getEncode() {
        return encode;
    }

    @Override
    public Context getContext() {
        return this;
    }

    @Override
    public ContactPeopleAdapter getAdapter() {
        return adapter;
    }

    @Override
    public void getLiveGuideSuccess(LiveGuide liveGuide) {
        this.liveGuide = liveGuide;
    }

    @Override
    public void applySuccess(AddContactApplyResponce responce, String phone, int gpos, int cpos) {
        if (responce == null) {
            return;
        }
        if (responce.sendMsg) {
            showSendMessageAlert(phone);
        } else {
            Toaster.show(responce.msg);
        }
        adapter.updateSendInvite(gpos, cpos);
    }

    @Override
    public BusinessConfig getStepConfigData() {
        return ContactPeopleConfig.INSTANCE;
    }
}
