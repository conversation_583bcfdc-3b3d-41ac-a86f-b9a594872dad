package com.immomo.momo.contact.adapter;

import android.content.Context;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.View.OnLongClickListener;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.immomo.framework.imageloader.ImageLoaderUtil;
import com.immomo.framework.imageloader.ImageType;
import com.immomo.framework.utils.UIUtils;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.Configs;
import com.immomo.momo.GotoKeys;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.android.adapter.BaseListAdapter;
import com.immomo.momo.android.view.EmoteEditeText;
import com.immomo.momo.android.view.dialog.MAlertDialog;
import com.immomo.momo.android.view.dialog.MProcessDialog;
import com.immomo.momo.exception.HttpException409;
import com.immomo.momo.innergoto.helper.ActivityHandler;
import com.immomo.momo.protocol.http.AppApi;
import com.immomo.momo.protocol.http.core.HttpClient;
import com.immomo.momo.service.bean.ContactNotice;
import com.immomo.momo.service.bean.ContactNotice.Action;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.service.user.UserService;
import com.immomo.momo.util.StringUtils;
import com.immomo.momo.util.TooLongValidator;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * [同意|agree_friends_contacts|url]	-> [|agree_friends_contacts|]
 * [添加|add_friends_contacts|url]		-> [等待验证|wait_add_friends_contacts]
 */
public class RecommendListAdapter extends BaseListAdapter<ContactNotice> {
    private static final String WAIT_FOR_RESP = "wait_add_friends_contacts";

    private static final int VIEWTYPE_ITEM = 0;
    private static final int VIEWTYPE_TITLE = 1;
    private UserService userService = null;
    private List<ContactNotice> newNoticeList = null; // 待处理的消息
    private List<ContactNotice> recommendContactNotice = null; // 可能认识的人
    private User user;

    public RecommendListAdapter(Context context, List<ContactNotice> newNoticeNotice, List<ContactNotice> recommendContactNotice, User user) {
        super(context, new ArrayList<ContactNotice>());
        this.newNoticeList = newNoticeNotice;
        this.recommendContactNotice = recommendContactNotice;
        userService = UserService.getInstance();
        this.user = user;
    }

    private View getUserView(View convertView, final int position) {
        if (convertView == null) {
            final ViewHolder holder = new ViewHolder();
            convertView = LayoutInflater.from(context).inflate(R.layout.listitem_contact_toadd, null);
            holder.commandButtonLayout = convertView.findViewById(R.id.contacttoadd_layout_commandbutton);
            holder.buttons[0] = (Button) holder.commandButtonLayout.findViewById(R.id.button1);
            holder.buttons[1] = (Button) holder.commandButtonLayout.findViewById(R.id.button2);
            holder.waitForRespBtn = (Button) holder.commandButtonLayout.findViewById(R.id.button_wait_for_resp);
            holder.userImageView = (ImageView) convertView.findViewById(R.id.contacttoadd_iv_userhead);
            holder.contnetTextView = (TextView) convertView.findViewById(R.id.contacttoadd_tv_content);
            holder.containerView = convertView.findViewById(R.id.layout_content);
            holder.usernameView = (TextView) convertView.findViewById(R.id.contacttoadd_tv_username);
            holder.distanceTextView = (TextView) convertView.findViewById(R.id.contacttoadd_tv_distance);
            convertView.setTag(R.id.tag_item, holder);
        }

        final ViewHolder holder = (ViewHolder) convertView.getTag(R.id.tag_item);

        final ContactNotice contactNotice = getItem(position);
        if (contactNotice == null) {
            return convertView;
        }
        final User remoteUser = contactNotice.getRemoteUser();

        // 好友头像
        if (remoteUser != null) {
            ImageLoaderUtil.loadImage(remoteUser.getLoadImageId(), ImageType.IMAGE_TYPE_ALBUM_250x250, holder.userImageView, true, 0);
        } else {
            ImageLoaderUtil.loadImage(null, ImageType.IMAGE_TYPE_ALBUM_250x250, holder.userImageView, true, 0);
        }

        holder.usernameView.setText(contactNotice.getUserDisplayName());
        if (remoteUser != null) {
            if (remoteUser.isMomoVip()) {
                holder.usernameView.setTextColor(UIUtils.getColor(R.color.font_vip_name));
            } else {
                holder.usernameView.setTextColor(UIUtils.getColor(R.color.color_text_3b3b3b));
            }
        } else {
            holder.usernameView.setTextColor(UIUtils.getColor(R.color.color_text_3b3b3b));
        }

        if (remoteUser != null && remoteUser.getDistance() >= 0) {
            holder.distanceTextView.setVisibility(View.VISIBLE);
            holder.distanceTextView.setText("[" + remoteUser.distanceString + "]");
        } else {
            holder.distanceTextView.setVisibility(View.GONE);
        }
        holder.contnetTextView.setText(contactNotice.getMessage());

        // 显示按钮
        if (contactNotice.hasAction()) {
            List<ContactNotice.Action> actions = contactNotice.getActions();
            int actionSize = actions.size();
            // log.i("contactNotice.hasAction() actions.size="+length);
            holder.commandButtonLayout.setVisibility(View.VISIBLE);
            holder.waitForRespBtn.setVisibility(View.GONE);
            boolean merged = false;
            for (int buttonIdx = 0; buttonIdx < holder.buttons.length; buttonIdx++) {
                if (buttonIdx < actionSize) {

                    // goto_xxx
                    if (GotoKeys.isTagMatch(actions.get(buttonIdx).getName()) || matchUrlAction(actions.get(buttonIdx).getActionUrl())) {
                        // url按钮，如果notice被处理过，它们将被合并成一个按钮。显示"已添加"。
                        if (contactNotice.isProcessed() && matchUrlAction(actions.get(buttonIdx).getActionUrl())) {
                            if (merged) {
                                holder.buttons[buttonIdx].setVisibility(View.GONE);
                            } else {
                                holder.buttons[buttonIdx].setClickable(false);
                                holder.buttons[buttonIdx].setEnabled(holder.buttons[buttonIdx].isClickable());
                                holder.buttons[buttonIdx].setText("已添加");
                                holder.buttons[buttonIdx].setVisibility(View.VISIBLE);
                                merged = true;
                            }
                            continue;
                        }
                        //要是是等待验证状态，隐藏buttons，显示waitForRespBtn
                        if (actions.get(buttonIdx).getName().contains(WAIT_FOR_RESP)) {
                            if (merged) {
                                holder.buttons[buttonIdx].setVisibility(View.GONE);
                            } else {
                                holder.buttons[buttonIdx].setVisibility(View.GONE);
                                holder.waitForRespBtn.setVisibility(View.VISIBLE);
                                merged = true;
                            }
                            continue;
                        }

                        holder.buttons[buttonIdx].setClickable(!actions.get(buttonIdx).isProcessed());
                        holder.buttons[buttonIdx].setEnabled(holder.buttons[buttonIdx].isClickable());
                        holder.buttons[buttonIdx].setText(actions.get(buttonIdx).getDisplayName());
                        holder.buttons[buttonIdx].setVisibility(View.VISIBLE);
                        if (actions.get(buttonIdx).getName().contains("agree")) {
                            holder.buttons[buttonIdx].setBackgroundDrawable(context.getResources().getDrawable(R.drawable.md_button_gray_light_17_corner));
                            holder.buttons[buttonIdx].setTextColor(UIUtils.getColor(R.color.md_button_text_dark));
                        } else {
                            holder.buttons[buttonIdx].setBackgroundDrawable(context.getResources().getDrawable(R.drawable.md_button_blue));
                            holder.buttons[buttonIdx].setTextColor(UIUtils.getColor(R.color.C14));
                        }
                        holder.buttons[buttonIdx].setOnClickListener(new ActionClickListener(actions.get(buttonIdx), contactNotice));
                    } else {
                        // 未知的按钮类型，不显示
                        holder.buttons[buttonIdx].setVisibility(View.GONE);
                    }
                } else {
                    holder.buttons[buttonIdx].setVisibility(View.GONE);
                }
            }
        } else {
            holder.commandButtonLayout.setVisibility(View.GONE);
        }

        // 点击事件
        holder.containerView.setOnLongClickListener(new OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                if (onItemLongClickListener != null) {
                    return onItemLongClickListener.onItemLongClick(v, position, v.getId());
                }
                return false;
            }
        });
        holder.containerView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onItemClick(v, position, v.getId());
                }
            }
        });
        return convertView;
    }

    private View getTitleView(View convertView, String titleStr, boolean hasTopDivider) {
        if (convertView == null) {
            convertView = inflate(R.layout.listitem_light_title_with_divider);
        }
        if (hasTopDivider) {
            convertView.findViewById(R.id.listitem_title_divider).setVisibility(View.VISIBLE);
        }
        TextView textView = (TextView) convertView.findViewById(R.id.textview);
        if (textView != null) {
            if (titleStr == null) {
                textView.setText("");
            } else {
                textView.setText(titleStr);
            }
        }
        return convertView;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        int type = getItemViewType(position);
        if (type == VIEWTYPE_TITLE) {
            return getTitleView(convertView, "好友推荐", false);
        } else if (type == VIEWTYPE_ITEM) {
            return getUserView(convertView, position);
        }
        return null;
    }

    @Override
    public int getViewTypeCount() {
        return 2;
    }

    @Override
    public ContactNotice getItem(int position) {
        if (position <= 0) return null;
        position = position - 1; //减去title的位置
        if (newNoticeList.size() > 0) {
            if (position < newNoticeList.size()) {
                return newNoticeList.get(position);
            } else {
                position = position - newNoticeList.size();
            }
        }
        if (recommendContactNotice.size() > 0) {
            if (position >= 0 && position < recommendContactNotice.size()) {
                return recommendContactNotice.get(position);
            }
        }
        return null;
    }

    @Override
    public int getCount() {
        int count = 0;
        if (newNoticeList != null) {
            count += newNoticeList.size();
        }
        if (recommendContactNotice != null) {
            count += recommendContactNotice.size();
        }
        //title
        //仅当有数据时才显示title
        count = count != 0 ? count + 1 : count;

        return count;
    }

    @Override
    public boolean areAllItemsEnabled() {
        return false;
    }

    @Override
    public boolean isEnabled(int position) {
        return getItemViewType(position) != VIEWTYPE_TITLE;
    }

    @Override
    public int getItemViewType(int position) {
        if (position == 0) {
            return VIEWTYPE_TITLE;
        }
        return VIEWTYPE_ITEM;
    }

    private boolean matchUrlAction(String action) {
        return action.startsWith("/api/") || action.startsWith("/game/");
    }

    private static class ViewHolder {
        public View containerView;
        public ImageView userImageView;
        public TextView usernameView;
        public TextView contnetTextView;
        public TextView distanceTextView;
        public View commandButtonLayout;
        public Button[] buttons = new Button[2];
        public Button waitForRespBtn;
    }

    private class ActionClickListener implements View.OnClickListener {
        private Action action;
        ContactNotice notice = null;

        public ActionClickListener(Action action, ContactNotice notice) {
            this.action = action;
            this.notice = notice;
        }

        @Override
        public void onClick(View v) {
            if (GotoKeys.isTagMatch(action.getName())) {
                ActivityHandler.executeAction(action.getString(), getContext());
            } else if (action.getName().startsWith("add_friends")) {
                showApplyDialog(notice, action);
            } else if (matchUrlAction(action.getActionUrl())) {
                MomoTaskExecutor.executeTask(MomoTaskExecutor.EXECUTOR_TYPE_USER, RecommendListAdapter.this.hashCode(), new RequestHttpTask(action, notice));
            }
        }
    }

    private void showApplyDialog(final ContactNotice notice, final Action action) {
        View view = MomoKit.getLayoutInflater().inflate(R.layout.dialog_contactpeople_apply, null);
        final EmoteEditeText editor = (EmoteEditeText) view.findViewById(R.id.edittext_reason);
        editor.addTextChangedListener(new TooLongValidator(Configs.NAME_LENGTH, editor));
        MAlertDialog dialog = MAlertDialog.makeConfirm(getContext(), "", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int which) {
                MomoTaskExecutor.executeTask(MomoTaskExecutor.EXECUTOR_TYPE_USER, RecommendListAdapter.this.hashCode(), new ApplyTask(editor.getText().toString().trim(), notice, action));
            }
        });
        dialog.setTitle("好友验证");
        dialog.setContentView(view);
        editor.setText("我是" + user.getDisplayName());
        dialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
        dialog.show();
    }

    /**
     * [添加|add_friends_contacts|url]
     */
    private class ApplyTask extends MomoTaskExecutor.Task<Object, Object, String> {
        private String reason = "";
        private ContactNotice notice = null;
        private Action action = null;
        private MProcessDialog mProcessDialog;

        public ApplyTask(String str, ContactNotice notice, Action action) {
            super();
            this.notice = notice;
            this.action = action;
            reason = str;
        }

        @Override
        protected void onPreTask() {
            super.onPreTask();
            mProcessDialog = new MProcessDialog(getContext());
            mProcessDialog.show();
        }

        @Override
        protected String executeTask(Object... params) throws Exception {
            Map<String, String> map = new HashMap<String, String>();
            map.put("reason", reason);
            String result = AppApi.getInstance().doPost(HttpClient.HttpsHost + action.getActionUrl(), map);
            JSONObject resultObj = new JSONObject(result);
            Action newRespAction = null;

            if (resultObj.has("recommend_list")) {
                String newRespGoto = resultObj.getJSONObject("recommend_list").optString("goto");
                newRespAction = new Action();
                newRespAction.parse(newRespGoto);
            } else if (resultObj.has("relateduser_list")) {
                String newRespGoto = resultObj.getJSONObject("relateduser_list").optString("goto");
                newRespAction = new Action();
                newRespAction.parse(newRespGoto);
            }
            if (newRespAction != null) {
                notice.setActions(Arrays.asList(newRespAction));
                switch (notice.getType()) {
                    case ContactNotice.TYPE_NOTICE: {
                        userService.saveContactNoticeUsers(newNoticeList);
                        break;
                    }
                    case ContactNotice.TYPE_RECOMMEND: {
                        userService.saveRecommendUsers(recommendContactNotice);
                        break;
                    }
                }
            }

            return resultObj.optString("msg");
        }

        @Override
        protected void onTaskFinish() {
            try {
                mProcessDialog.dismiss();
            } catch (Throwable ignored) {
            }
        }

        @Override
        protected void onTaskError(Exception e) {
            if (e != null && e instanceof HttpException409) {
                Toaster.show(e.getMessage());
                notifyDataSetChanged();
            } else {
                super.onTaskError(e);
            }
        }

        @Override
        protected void onTaskSuccess(String result) {
            if (!StringUtils.isEmpty(result)) {
                Toaster.show(result);
            }

            notifyDataSetChanged();
        }
    }

    private class RequestHttpTask extends MomoTaskExecutor.Task<Object, Object, String> {
        Action action = null;
        ContactNotice notice = null;
        MProcessDialog mProcessDialog;

        public RequestHttpTask(Action action, ContactNotice notice) {
            super();
            this.action = action;
            this.notice = notice;
        }

        @Override
        protected void onPreTask() {
            mProcessDialog = new MProcessDialog(context);
            mProcessDialog.show();
        }

        @Override
        protected String executeTask(Object... params) throws Exception {
            String result = AppApi.getInstance().doPost(HttpClient.HttpsHost + action.getActionUrl(), null);
            if (notice.getType() == ContactNotice.TYPE_NOTICE) {
                newNoticeList.remove(notice);
                userService.saveContactNoticeUsers(newNoticeList);
            }
            return new JSONObject(result).optString("msg");
        }

        @Override
        protected void onTaskFinish() {
            mProcessDialog.dismiss();
        }

        @Override
        protected void onTaskSuccess(String result) {
            notifyDataSetChanged();
            if (!StringUtils.isEmpty(result)) {
                Toaster.show(result);
            }
        }
    }

    //<editor-fold desc="Item ClickListener">
    protected OnItemClickListener onItemClickListener;
    protected OnItemLongClickListener onItemLongClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public void setOnItemLongClickListener(OnItemLongClickListener onItemLongClickListener) {
        this.onItemLongClickListener = onItemLongClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(View view, int position, long id);
    }

    public interface OnItemLongClickListener {
        boolean onItemLongClick(View view, int position, long id);
    }
    //</editor-fold>
}
