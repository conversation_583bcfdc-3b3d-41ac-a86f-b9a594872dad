package com.immomo.momo.contact.activity;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;

import com.immomo.framework.view.pulltorefresh.MomoPtrListView;
import com.immomo.framework.view.pulltorefresh.OnPtrListener;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.R;
import com.immomo.momo.android.activity.BaseAccountActivity;
import com.immomo.momo.android.view.ListEmptyView;
import com.immomo.momo.android.view.dialog.MProcessDialog;
import com.immomo.momo.contact.adapter.NewFriendListAdapter;
import com.immomo.momo.protocol.http.UserApi;
import com.immomo.momo.router.ProfileGotoOptions;
import com.immomo.momo.router.ProfileRouter;
import com.immomo.momo.router.RefreshTag;
import com.immomo.momo.service.bean.User;

import java.util.ArrayList;
import java.util.List;

import info.xudshen.android.appasm.AppAsm;


/**
 * Created by yongqiang on 15/11/30.
 */
public class SearchOfficalListActivity extends BaseAccountActivity implements OnItemClickListener,OnPtrListener{
    public static final String SEARCH_OFFICAL_KEYWORD = "Search_Offical_Keyword";

    private final static int PAGE_SIZE = 20;

    private String searchStr = null;

    private MomoPtrListView listview = null;
    private ListEmptyView emptyView = null;

    private NewFriendListAdapter adapter;
    private List<User> friendslist=new ArrayList<User>();

    @Override
    protected void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        //跟搜索商家公用同一个xml
        setContentView(R.layout.activity_search_offical);
        initInternal();
        initViews();
        initEvents();
        initData();
    }

    private void initInternal(){
        searchStr = getIntent().getStringExtra(SEARCH_OFFICAL_KEYWORD);
    }

    @Override
    protected void initData() {
        super.initData();
        execAsyncTask(new SearchTask(thisActivity()));
    }


    @Override
    protected void initEvents() {
        listview.setOnItemClickListener(this);
        listview.setOnPtrListener(this);
    }


    @Override
    protected void initViews() {
        setTitle("搜索官方账号");
        listview = (MomoPtrListView) findViewById(R.id.listview_search);
        listview.setLoadMoreButtonVisible(false);
        emptyView = (ListEmptyView) findViewById(R.id.listview_emptyview);
        emptyView.setIcon(R.drawable.ic_vector_common_empty);
        emptyView.setContentStr("没有对应的官方账号");
        adapter = new NewFriendListAdapter(thisActivity(),new ArrayList<User>(),listview);
        adapter.setHidestatus(true);
        listview.setAdapter(adapter);
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        ProfileGotoOptions options = new ProfileGotoOptions(friendslist.get(position).momoid);
        options.setRequestTypeTag(RefreshTag.LOCAL);
        AppAsm.getRouter(ProfileRouter.class).gotoProfile(thisActivity(), options);
    }

    @Override
    public void onRefresh() {

    }

    @Override
    public void onLoadMore() {
        execAsyncTask(new SearchTask(thisActivity()));
    }

    private class SearchTask extends MomoTaskExecutor.Task<Object,Object,Boolean> {
        MProcessDialog dialog = null;

        public SearchTask(Context context){
            super(context);
            dialog = new MProcessDialog(thisActivity());
            dialog.setCancelable(true);
            showDialog(dialog);
        }

        @Override
        protected Boolean executeTask(Object... params) throws Exception {
            return UserApi.getInstance().searchOfficalList(friendslist.size(),PAGE_SIZE,searchStr,friendslist);
        }

        @Override
        protected void onTaskFinish() {
            super.onTaskFinish();
            if(friendslist!=null && friendslist.size()>0){
                emptyView.setVisibility(View.GONE);
            }else{
                emptyView.setVisibility(View.VISIBLE);
            }
            closeDialog();

        }

        @Override
        protected void onTaskSuccess(Boolean remain) {
            super.onTaskSuccess(remain);
            adapter.clear();
            adapter.addAll(friendslist);
            listview.setLoadMoreButtonVisible(remain);
        }
    }
}
