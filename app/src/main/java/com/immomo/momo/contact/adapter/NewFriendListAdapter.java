package com.immomo.momo.contact.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.immomo.framework.imageloader.ImageType;
import com.immomo.framework.imageloader.ImageLoaderUtil;
import com.immomo.framework.imageloader.ImageLoaderX;
import com.immomo.framework.utils.UIUtils;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.android.adapter.BaseListAdapter;
import com.immomo.momo.android.view.BadgeView;
import com.immomo.momo.android.view.EmoteTextView;
import com.immomo.momo.android.view.HandyListView;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 6.4A 新的好友列表
 * 头像变小 改成2行显示
 * Project momodev
 * Package com.immomo.momo.contact.adapter
 * Created by tangyuchun on 8/27/15.
 */
public class NewFriendListAdapter extends BaseListAdapter<User> {

    public final static int SORT_TYPE_ADD_TIME = 2; // 添加时间排序

    private HandyListView listView = null;

    private int roundCorner;

    private List<String> removeList = new ArrayList<String>();
    private boolean isEdit = false;
    private boolean hidestatus = false;
    private boolean showRelationIcon = false;

    public NewFriendListAdapter(Context context, List<User> users, HandyListView listView) {
        super(context, users);
        this.context = context;
        this.listView = listView;
        this.roundCorner = context.getResources().getDimensionPixelSize(R.dimen.avatar_a5_corner);
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if (convertView == null || convertView.getId() != R.id.userlist_item_rl) {
            convertView = LayoutInflater.from(context).inflate(R.layout.listitem_friend, parent, false);
        }
        if (convertView.getTag(R.id.tag_userlist_item) == null) {
            final ViewHolder holder = new ViewHolder();
            holder.itemLayout = (RelativeLayout) convertView.findViewById(R.id.userlist_item_rl);
            holder.removeView = (CheckBox) convertView.findViewById(R.id.userlist_item_checkbox);
            holder.faceView = (ImageView) convertView.findViewById(R.id.userlist_item_iv_face);
            holder.nameView = (TextView) convertView.findViewById(R.id.userlist_item_tv_name);
            holder.distanceView = (TextView) convertView.findViewById(R.id.userlist_item_tv_distance);
            holder.timeView = (TextView) convertView.findViewById(R.id.userlist_tv_time);
            holder.signatureView = (EmoteTextView) convertView.findViewById(R.id.userlist_item_tv_sign);
            holder.badgeView = (BadgeView) convertView.findViewById(R.id.userlist_bage);
            holder.signextIconView = (ImageView) convertView.findViewById(R.id.userlist_item_pic_sign);
            holder.timeDriver = convertView.findViewById(R.id.userlist_tv_timedriver);

            convertView.setTag(R.id.tag_userlist_item, holder);
        }

        final User user = getItem(position);
        final ViewHolder holder = (ViewHolder) convertView.getTag(R.id.tag_userlist_item);

        holder.badgeView.setGenderlayoutVisable(true);
        holder.badgeView.setContactUser(user, showRelationIcon);
        holder.badgeView.updateGenderTextState(user);

        holder.distanceView.setText(user.distanceString);
        //粉丝列表  编辑状态下 所有状态View 隐藏，在非编辑状态下，依照距离去显示or隐藏相关时间.距离等View
        if (isEdit) {
            holder.removeView.setVisibility(View.VISIBLE);
            holder.removeView.setChecked(removeList.contains(user.getId()));
            holder.timeDriver.setVisibility(View.GONE);
            holder.timeView.setVisibility(View.GONE);
            holder.distanceView.setVisibility(View.GONE);
        } else {
            holder.removeView.setVisibility(View.GONE);
            holder.timeView.setVisibility(user.showTime() ? View.VISIBLE : View.GONE);
            holder.timeView.setText(user.agoTime);
            holder.timeDriver.setVisibility(user.showTime() && user.showDistance() ? View.VISIBLE : View.GONE);
            holder.distanceView.setVisibility(user.showDistance() || !user.showTime() && !user.showDistance() ? View.VISIBLE : View.GONE);
            //目前仅在搜索官方帐号使用字段，不现实距离等信息，不显示好用关系
            if (hidestatus) {
                holder.timeDriver.setVisibility(View.GONE);
                holder.timeView.setVisibility(View.GONE);
                holder.distanceView.setVisibility(View.GONE);
                user.relation = User.RELATION_NONE;
            } else {
                holder.distanceView.setVisibility(View.VISIBLE);
            }
        }

        holder.nameView.setText(user.getDisplayName());
        if (user.isMomoVip()) {
            holder.nameView.setTextColor(UIUtils.getColor(R.color.font_vip_name));
        } else {
            holder.nameView.setTextColor(UIUtils.getColor(R.color.color_text_3b3b3b));
        }
        holder.signatureView.setText(user.getSignexEmoteContent());
        if (!StringUtils.isEmpty(user.signexColor)) {
            holder.signatureView.setTextColor(MomoKit.parseColor(user.signexColor));
        } else {
            holder.signatureView.setTextColor(MomoKit.getContext().getResources().getColor(R.color.color_969696));
        }

        if (!StringUtils.isEmpty(user.signexIcon)) {
            holder.signextIconView.setVisibility(View.VISIBLE);
            ImageLoaderX.loadWithReset(user.signexIcon).type(ImageType.IMAGE_TYPE_URL).showDefault().into(holder.signextIconView);
        } else {
            holder.signextIconView.setVisibility(View.GONE);
        }
        ImageLoaderUtil.loadRoundImage(user.getLoadImageId(), ImageType.IMAGE_TYPE_ALBUM_250x250, holder.faceView, listView, roundCorner, true, 0);
        return convertView;
    }

    public List<String> getRemoveList() {
        return removeList;
    }

    public void clearRemoveList() {
        this.removeList.clear();
    }

    public void setHidestatus(boolean hidestatus) {
        this.hidestatus = hidestatus;
    }

    public boolean isEdit() {
        return isEdit;
    }

    public boolean isHidestatus() {
        return hidestatus;
    }

    public void setEdit(boolean isEdit) {
        this.isEdit = isEdit;
    }

    CompoundButton.OnCheckedChangeListener onChangeListener = new CompoundButton.OnCheckedChangeListener() {
        @Override
        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {

//            mCallback.changeStatic(removeList.size());
        }
    };

    public void selectorPosition(int position, boolean isChecked) {
        String userid = getItem(position).getId();
        if (isChecked) {
            if (!removeList.contains(userid)) {
                removeList.add(userid);
            }
        } else {
            if (removeList.size() != 0 && removeList.contains(userid)) {
                removeList.remove(userid);
            }
        }
    }

    public void setShowRelationIcon(boolean isShow) {
        showRelationIcon = isShow;
    }

    private static class ViewHolder {
        // List显示部分
        public ImageView faceView;
        public TextView nameView;
        public TextView distanceView;
        public TextView timeView;
        public EmoteTextView signatureView;
        public ImageView signextIconView;
        public BadgeView badgeView;
        public View timeDriver;
        public CheckBox removeView;
        public RelativeLayout itemLayout;
    }
}

