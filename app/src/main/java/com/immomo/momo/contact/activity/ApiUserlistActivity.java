package com.immomo.momo.contact.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;

import com.immomo.framework.view.pulltorefresh.OnPtrListener;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.android.activity.BaseListActivity;
import com.immomo.momo.contact.adapter.FriendsListViewAdapter;
import com.immomo.momo.router.ProfileGotoOptions;
import com.immomo.momo.router.ProfileRouter;;
import com.immomo.momo.protocol.http.UserApi;
import com.immomo.momo.router.RefreshTag;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.util.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import info.xudshen.android.appasm.AppAsm;


public class ApiUserlistActivity extends BaseListActivity implements AdapterView.OnItemClickListener {
    public static final String KEY_APIURL = "key_apiurl";
    public static final String KEY_TITLE = "key_title";
    public static final int PAGE_COUNT = 20;
    private int currentIndex = 0;
    private FriendsListViewAdapter adapter;
    private Set<String> hashIds = new HashSet<>();
    private String apiUrl = null;
    private String title = "";

    @Override
    protected void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        initInteneranl();
        initEvents();
        initData(savedInstanceState);
    }

    private void initInteneranl() {

    }

    private void initData(Bundle savedInstanceState) {
        Intent intent = getIntent();
        apiUrl = intent.getStringExtra(KEY_APIURL);
        title = intent.getStringExtra(KEY_TITLE);
        if (StringUtils.isEmpty(apiUrl)) {
            finish();
            return;
        }
        adapter = new FriendsListViewAdapter(thisActivity(), new ArrayList<User>(), list, true);
        adapter.setShowSingleColor(true);
        list.setAdapter(adapter);
        refreshHeader(0);
        execAsyncTask(new RefreshTask(thisActivity()));
    }

    @Override
    protected void initEvents() {
        list.setOnPtrListener(new OnPtrListener() {
            @Override
            public void onRefresh() {
                execAsyncTask(new RefreshTask(thisActivity()));
            }

            @Override
            public void onLoadMore() {
                execAsyncTask(new LoadmoreTask(thisActivity()));
            }
        });
        list.setOnItemClickListener(this);
    }

    @Override
    public void onItemClick(AdapterView<?> adapterView, View view, int position, long l) {
        String momoid = adapter.getItem(position).momoid;
        ProfileGotoOptions options = new ProfileGotoOptions(momoid);
        options.setRequestTypeTag(RefreshTag.LOCAL);
        AppAsm.getRouter(ProfileRouter.class).gotoProfile(thisActivity(), options);
    }

    private void refreshHeader(int total) {
        if (total > 0) {
            setTitle(title + "(" + total + ")");
        } else {
            setTitle(title);
        }
    }

    /**
     * @param list
     */
    private void addToHashIds(List<User> list) {
        for (int i = 0; i < list.size(); i++) {
            User user = list.get(i);
            if (!hashIds.contains(user.momoid)) {
                hashIds.add(user.momoid);
            }
        }
    }

    private void clearHashIds() {
        hashIds.clear();
    }

    private class RefreshTask extends MomoTaskExecutor.Task<Object, Object, UserListResult> {

        private ArrayList<User> userArrayList = new ArrayList<>();

        public RefreshTask(Context context) {
            super(context);
        }

        @Override
        protected UserListResult executeTask(Object... params) throws Exception {
            if (StringUtils.isEmpty(apiUrl)) {
                UserListResult result = new UserListResult();
                result.isRemain = false;
                return result;
            }

            UserListResult result = UserApi.getInstance().apiUserList(apiUrl, 0, PAGE_COUNT, userArrayList);
            clearHashIds();
            addToHashIds(userArrayList);
            return result;
        }

        @Override
        protected void onTaskFinish() {
            super.onTaskFinish();
            list.refreshComplete();
        }

        @Override
        protected void onTaskSuccess(UserListResult result) {
            super.onTaskSuccess(result);
            currentIndex = 1;
            adapter = new FriendsListViewAdapter(thisActivity(), userArrayList, list, true);
            list.setAdapter(adapter);
            list.setLoadMoreButtonVisible(result.isRemain);
            refreshHeader(result.totalCount);
        }
    }

    private class LoadmoreTask extends MomoTaskExecutor.Task<Object, Object, UserListResult> {

        private ArrayList<User> userArrayList = new ArrayList<>();

        public LoadmoreTask(Context context) {
            super(context);
        }

        @Override
        protected UserListResult executeTask(Object... params) throws Exception {
            return UserApi.getInstance().apiUserList(apiUrl, currentIndex * PAGE_COUNT, PAGE_COUNT, userArrayList);
        }

        @Override
        protected void onTaskFinish() {
            super.onTaskFinish();
            list.onLoadMoreFinished();
        }

        @Override
        protected void onTaskSuccess(UserListResult result) {
            super.onTaskSuccess(result);
            for (int i = 0; i < userArrayList.size(); i++) {
                User user = userArrayList.get(i);
                if (!hashIds.contains(user.momoid)) {
                    hashIds.add(user.momoid);
                    adapter.add(user);
                }
            }
            currentIndex++;
            list.setLoadMoreButtonVisible(result.isRemain);
            refreshHeader(result.totalCount);
        }
    }

    public static class UserListResult {
        public boolean isRemain;
        public int totalCount;
    }

}
