package com.immomo.momo.contact.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ListView;

import androidx.annotation.IntDef;

import com.immomo.framework.base.BaseActivity;
import com.immomo.framework.base.IStepConfigDataProvider;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.GotoKeys;
import com.immomo.momo.R;
import com.immomo.momo.android.view.ClearableEditText;
import com.immomo.momo.businessmodel.statistics.BusinessConfig;
import com.immomo.momo.businessmodel.statistics.SearchConfig;
import com.immomo.momo.businessmodel.usermodel.IUserModel;
import com.immomo.momo.contact.adapter.SearchContactAdapter;
import com.immomo.momo.contact.presenter.ISearchContactPresenter;
import com.immomo.momo.contact.presenter.SearchContactPresenter;
import com.immomo.momo.contact.view.ISearchContactView;
import com.immomo.momo.gotologic.GotoDispatcher;
import com.immomo.momo.group.activity.GroupProfileActivity;
import com.immomo.momo.group.activity.SearchGroupActivity;
import com.immomo.momo.innergoto.log.CompleteGoto;
import com.immomo.momo.moment.utils.KeyBoardUtil;
import com.immomo.momo.mvp.common.model.ModelManager;
import com.immomo.momo.protocol.imjson.util.Debugger;
import com.immomo.momo.router.ProfileRouter;
import com.immomo.momo.util.StringUtils;
import com.immomo.momo.util.TextWatcherAdapter;
import com.immomo.momo.util.TooLongValidator;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import info.xudshen.android.appasm.AppAsm;


/**
 * Created by joel on 15/3/27.
 * <p/>
 * Momo Tech 2011-2015 © All Rights Reserved.
 */
public class SearchContactActivity extends BaseActivity implements ISearchContactView, IStepConfigDataProvider<BusinessConfig> {
    private final Pattern pattern = Pattern.compile("^[0-9]*$");
    private static final int MAX_SEARCH_LENGTH = 24;

    private boolean isNumbSearch = false;
    private String keyword;
    private ListView searchListView;
    private SearchContactAdapter adapter;
    private ClearableEditText toolbarSearchEditText = null;
    private IUserModel userModel = ModelManager.getModel(IUserModel.class);
    private ISearchContactPresenter mSearchContactPresenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_search_contact);
        mSearchContactPresenter = new SearchContactPresenter(this);
        mSearchContactPresenter.bindView(this);
        initViews();
        initEvents();
    }

    protected void initViews() {
        toolbarSearchEditText = toolbarHelper.getToolbar().findViewById(R.id.toolbar_search_edittext);
        toolbarSearchEditText.addTextChangedListener(new TooLongValidator(MAX_SEARCH_LENGTH, toolbarSearchEditText));
        toolbarSearchEditText.setHint("搜索陌陌号/群组");
        toolbarSearchEditText.requestFocus();

        adapter = new SearchContactAdapter(thisActivity());
        searchListView = (ListView) findViewById(R.id.search_contact_listview);
        searchListView.setAdapter(adapter);

//        Animation animation = AnimationUtils.loadAnimation(this, R.anim.anim_slide_in_from_right);
//        animation.setDuration(getResources().getInteger(R.integer.config_activity_parallax));
//        toolbarHelper.getToolbar().startAnimation(animation);
    }

    private boolean isNumbSearch() {
        Matcher matcher = pattern.matcher(keyword);
        return matcher.matches();
    }

    @Override
    protected void onResume() {
        super.onResume();
        toolbarSearchEditText.postDelayed(() -> KeyBoardUtil.showSoftKeyboardForce(thisActivity(), toolbarSearchEditText), 300);
    }

    protected void initEvents() {
        toolbarSearchEditText.addTextChangedListener(new TextWatcherAdapter() {
            @Override
            public void afterTextChanged(Editable s) {
                keyword = s.toString().trim();
                if (keyword.length() > 0) {
                    isNumbSearch = isNumbSearch();
                    adapter.clear();
                    if (isNumbSearch) {
                        adapter.addAll(buildNumbSearch());
                    } else {
                        adapter.addAll(buildNoNumbSearch());
                    }
                } else {
                    adapter.clear();
                }
            }
        });

        searchListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                SearchContact contact = adapter.getItem(position);
                if (contact != null) {
                    switch (contact.type) {
                        case SearchContactType.USER:
                            String momoid = keyword;
                            if (StringUtils.isEmpty(momoid)) {
                                Toaster.show(R.string.find_empty_momoid);
                                return;
                            } else if (momoid.equals(userModel.getCurrentUser().momoid)) {
                                Toaster.show(R.string.find_your_momoid);
                                return;
                            } else if (Debugger.isDebuggable() && momoid.equals(Debugger.LoggerSessionId)) {
                                // 调试模式开启
                                if (Debugger.getInstance().enableDebugger()) {
                                    return;
                                }
                            }
                            mSearchContactPresenter.searchUser(momoid);
                            break;
                        case SearchContactType.GROUP:
                            String groupKey = keyword;
                            if (StringUtils.isEmpty(groupKey)) {
                                Toaster.show(R.string.find_empty_gid);
                                return;
                            }
                            if (isNumbSearch) {
                                mSearchContactPresenter.searchGroup(groupKey);
                            } else {
                                SearchGroupActivity.start(thisActivity(), groupKey);
                            }
                            break;
                        case SearchContactType.OFFICAL:
                            String officaKey = keyword;
                            Intent intent = new Intent(thisActivity(), SearchOfficalListActivity.class);
                            intent.putExtra(SearchOfficalListActivity.SEARCH_OFFICAL_KEYWORD, officaKey);
                            startActivity(intent);
                            break;
                        default:
                            break;
                    }
                }
            }
        });
    }

    private List<SearchContact> buildNumbSearch() {
        List<SearchContact> list = new ArrayList<>();
        list.add(getUserSearchContact());
        list.add(getGroupSearchContact());
        return list;
    }

    private List<SearchContact> buildNoNumbSearch() {
        List<SearchContact> list = new ArrayList<>();
        if (StringUtils.isNiceMomoid(keyword)) {
            list.add(getUserSearchContact());
        }
        list.add(getGroupSearchContact());
        list.add(getOfficalSearchContect());
        return list;
    }

    private SearchContact getUserSearchContact() {
        return new SearchContact(SearchContactType.USER, "搜索用户：", keyword);
    }

    private SearchContact getGroupSearchContact() {
        return new SearchContact(SearchContactType.GROUP, "搜索群组：", keyword);
    }

    private SearchContact getOfficalSearchContect() {
        return new SearchContact(SearchContactType.OFFICAL, "搜索官方账号：", keyword);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mSearchContactPresenter.onDestroy();
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        KeyBoardUtil.hideSoftKeyboardNotAlways(this);
        overridePendingTransition(R.anim.anim_fade_in, R.anim.anim_slide_out_to_right);
    }

    @Override
    public void searchUserSuccess(String result) {
        AppAsm.getRouter(ProfileRouter.class).gotoProfile(thisActivity(), result);
    }

    @Override
    public void searchGroupSuccess(String gid) {
        String gotoStr = CompleteGoto.Companion.getSimpleNewGoto("", GotoKeys.GOTO_GROUP_PROFILE, new HashMap<String, String>(){{
            put(GroupProfileActivity.INTENT_KEY_GID, gid);
            put(BaseActivity.KEY_FROM, SearchContactActivity.this.getClass().getName());
        }});
        GotoDispatcher.action(gotoStr, this).execute();
    }

    @Override
    public BusinessConfig getStepConfigData() {
        return SearchConfig.INSTANCE;
    }

    public static class SearchContact {
        @SearchContentTypeAnnotations
        public int type;
        public String typeDesc;
        public String keyword;

        public SearchContact(@SearchContentTypeAnnotations int type, String desc, String keyword) {
            this.type = type;
            this.typeDesc = desc;
            this.keyword = keyword;
        }
    }

    public abstract static class SearchContactType {
        private SearchContactType() {
        }

        public static final int USER = 0x0001;
        public static final int GROUP = 0x0002;
        public static final int OFFICAL = 0x0003;
    }

    @IntDef({SearchContactType.USER, SearchContactType.GROUP, SearchContactType.OFFICAL})
    @Retention(RetentionPolicy.SOURCE)
    @interface SearchContentTypeAnnotations {
    }
}
