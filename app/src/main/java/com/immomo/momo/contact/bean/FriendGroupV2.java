package com.immomo.momo.contact.bean;

import android.text.TextUtils;

import com.immomo.mmutil.log.Log4Android;
import com.immomo.momo.service.bean.Action;
import com.immomo.momo.service.bean.user.ContactUser;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Project momodev
 * Package com.immomo.momo.contact.bean
 * Created by tangy<PERSON><PERSON> on 8/25/15.
 */
public class FriendGroupV2 {
    public static String CATEGORY_SPECIAL = "special";
    public static String CATEGORY_NORMAL = "normal";

    public String title;
    private String bargoto;

    public Set<String> userIds = new HashSet<>();
    public List<ContactUser> userList = new ArrayList<>();

    public Action gotoAction;
    public String category;

    public FriendGroupV2() {
    }

    public FriendGroupV2(FriendGroupV2 group) {
        this.title = group.title;
        this.bargoto = group.bargoto;
        this.userIds = new HashSet<>(group.userIds);
        this.userList = new ArrayList<>(group.userList);
        this.gotoAction = group.gotoAction;
        this.category = group.category;
    }

    public void setBargoto(String pBargoto) {
        bargoto = pBargoto;
        gotoAction = Action.parse(pBargoto);
    }

    public String getBargoto() {
        return bargoto;
    }

    public ContactUser getUser(int pos) {
        //修复https://fabric.io/momo6/android/apps/com.immomo.momo/issues/5874f6010aeb16625bf4ba5a
        //当超出范围时，返回一个空momoid的user，而不是null
        if (pos < 0 || pos >= userList.size()) {
            return new ContactUser("");
        }
        return userList.get(pos);
    }

    public int getUserCount() {
        return userList == null ? 0 : userList.size();
    }

    @Deprecated
    public boolean addUser(ContactUser user) {
        if (user == null) {
            return false;
        }
        if (!userList.contains(user)) {
            userList.add(user);
            return true;
        }
        return false;
    }

    @Deprecated
    public boolean addUser(int index, ContactUser user) {
        if (user == null) {
            return false;
        }
        if (!userList.contains(user)) {
            userList.add(index, user);
            return true;
        }
        return false;
    }

    public boolean addUserByMomoid(int index, ContactUser newUser) {
        if (newUser == null || userList == null) {
            return false;
        }
        for (ContactUser user : userList) {
            if (TextUtils.equals(user.momoid, newUser.momoid)) {
                return true;
            }
        }
        userList.add(index, newUser);
        return true;
    }

    public boolean removeUserByMomoid(ContactUser newUser) {
        if (newUser == null || userList == null) {
            return false;
        }
        List<ContactUser> newUserList = new ArrayList<>();
        boolean isRemoved = false;
        for (ContactUser user : userList) {
            if (TextUtils.equals(user.momoid, newUser.momoid)) {
                isRemoved = true;
            } else {
                newUserList.add(user);
            }
        }
        if (isRemoved) {
            userList.clear();
            userList.addAll(newUserList);
        }
        return isRemoved;
    }

    /**
     * 是否是特别好友这一组
     *
     * @return
     */
    public boolean isSpecialGroup() {
        return CATEGORY_SPECIAL.equals(this.category);
    }

    public boolean isNormalGroup() {
        return CATEGORY_NORMAL.equals(this.category);
    }

    @Deprecated
    public void removeUser(ContactUser user) {
        userList.remove(user);
    }

    public boolean hasUser(ContactUser user) {
        return userList != null && userList.contains(user);
    }

    public static FriendGroupV2 fromCacheJson(JSONObject json) {
        if (json == null) {
            return null;
        }
        FriendGroupV2 group = new FriendGroupV2();
        group.setBargoto(json.optString("bargoto"));
        group.title = json.optString("title", "");
        group.category = json.optString("category");
        //        log.i("tang-------解析group  " + json.toString() + "    " + group.category + "     " + group.isSpecialGroup());
        try {
            if (json.has("ids")) {//ids 是存放用于ID的索引 在缓存中使用
                JSONArray idArray = json.getJSONArray("ids");
                Set<String> ids = new HashSet<>();
                int size = idArray.length();
                String userId = null;
                for (int i = 0; i < size; i++) {
                    userId = idArray.getString(i);
                    if (!TextUtils.isEmpty(userId) && !ids.contains(userId)) {
                        ids.add(userId);
                    }
                }
                group.userIds = ids;
            }
        } catch (JSONException e) {
            Log4Android.getInstance().e(e);
        }
        return group;
    }

    @Override
    public String toString() {
        JSONObject json = toCacheJson(this);
        return json != null ? json.toString() : "";
    }

    /**
     * 转化成json数据，在缓存时使用
     *
     * @param group
     * @return
     */
    public static JSONObject toCacheJson(FriendGroupV2 group) {
        JSONObject json = new JSONObject();
        try {
            json.put("title", group.title);
            json.put("bargoto", group.bargoto);
            json.put("category", group.category);
            JSONArray idArray = new JSONArray();
            if (group.userList != null) {
                for (ContactUser user : group.userList) {
                    idArray.put(user.momoid);
                }
                json.put("ids", idArray);
            }
            return json;
        } catch (JSONException e) {
            Log4Android.getInstance().e(e);
        }
        return null;
    }

    public boolean contains(ContactUser user) {
        if (userList == null || userList.isEmpty()) {
            return false;
        }
        return userList.contains(user);
    }

    public void remove(ContactUser user) {
        if (userList != null && !userList.isEmpty()) {
            userList.remove(user);
        }
    }
}
