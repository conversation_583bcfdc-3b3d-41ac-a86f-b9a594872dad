package com.immomo.momo.contact.activity;

import android.content.Context;
import android.database.sqlite.SQLiteException;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.inputmethod.InputMethodManager;
import android.widget.AdapterView;
import android.widget.EditText;

import com.immomo.framework.base.BaseActivity;
import com.immomo.framework.view.pulltorefresh.MomoPtrListView;
import com.immomo.mmutil.log.Log4Android;
import com.immomo.momo.R;
import com.immomo.momo.android.view.ListEmptyView;
import com.immomo.momo.contact.adapter.FriendsListViewAdapterV2;
import com.immomo.momo.service.bean.user.ContactUser;
import com.immomo.momo.router.ProfileGotoOptions;
import com.immomo.momo.router.ProfileRouter;
import com.immomo.momo.router.RefreshTag;
import com.immomo.momo.service.user.UserService;
import com.immomo.momo.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

import info.xudshen.android.appasm.AppAsm;

/**
 * 现在该页面承载的是粉丝列表页面功能
 * <p>
 * 现在该页面承载的是搜索关注好友的功能 @xudong 2016-05-30
 *
 * <AUTHOR>
 */
public class SearchGuanzhuListActivity extends BaseActivity {
    private MomoPtrListView friendsListView;
    private FriendsListViewAdapterV2 friendsAdapter;

    private UserService userService;

    //used for search toolbar
    private EditText searchEditText;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_relation_friend);

        initViews();
        initInternal();
        initEvents();
        initData();
    }

    protected void initViews() {
        ListEmptyView listEmptyView = (ListEmptyView) findViewById(R.id.listview_emptyview);
        listEmptyView.setContentStr(R.string.friendlist_empty_tip);

        friendsListView = (MomoPtrListView) findViewById(R.id.friends_listview);

        friendsListView.setEmptyView(listEmptyView);
        friendsListView.setLoadMoreButtonVisible(false);

        searchEditText = (EditText) toolbarHelper.getToolbar().findViewById(R.id.toolbar_search_edittext);
        searchEditText.setHint("请输入关注人的名字");

        Animation animation = AnimationUtils.loadAnimation(this, R.anim.anim_slide_in_from_right);
        animation.setDuration(getResources().getInteger(R.integer.config_activity_parallax));
        toolbarHelper.getToolbar().startAnimation(animation);
    }

    private void initInternal() {
        userService = UserService.getInstance();
    }

    private TextWatcher watcher = new TextWatcher() {
        public void onTextChanged(CharSequence s, int start, int before, int count) {
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void afterTextChanged(final Editable s) {
            String key = s.toString().trim();
            if (StringUtils.isEmpty(key)) {
                friendsAdapter.clear(true);
            } else {
                try {
                    List<ContactUser> list = userService.searchFollow(key);
                    friendsAdapter.clear(false);
                    friendsAdapter.addAll(list);
                } catch (SQLiteException e) {
                    Log4Android.getInstance().e(e);
                }
            }
        }
    };

    protected void initEvents() {
        searchEditText.addTextChangedListener(watcher);

        friendsListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> arg0, View view, int position, long id) {
                ProfileGotoOptions options = new ProfileGotoOptions(friendsAdapter
                        .getItem(position).momoid);
                options.setRequestTypeTag(RefreshTag.LOCAL);
                AppAsm.getRouter(ProfileRouter.class).gotoProfile(thisActivity(), options);
            }
        });
    }

    private void initData() {
        friendsAdapter = new FriendsListViewAdapterV2(thisActivity(), new ArrayList<>(), friendsListView);
        friendsListView.setAdapter(friendsAdapter);
    }

    //<editor-fold desc="Input method">
    private void showInputMethod(EditText editText) {
        InputMethodManager imm = (InputMethodManager) this.getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            imm.showSoftInput(editText, InputMethodManager.RESULT_SHOWN);
            imm.toggleSoftInput(InputMethodManager.SHOW_FORCED, InputMethodManager.HIDE_IMPLICIT_ONLY);
        }
    }

    private void hideSoftInput(EditText editText) {
        InputMethodManager imm = (InputMethodManager) this.getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null && editText != null) {
            imm.hideSoftInputFromWindow(editText.getWindowToken(), 0);
        }
    }

    //</editor-fold>
    @Override
    public void onBackPressed() {
        hideSoftInput(searchEditText);

        super.onBackPressed();
        overridePendingTransition(R.anim.anim_fade_in, R.anim.anim_slide_out_to_right);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}