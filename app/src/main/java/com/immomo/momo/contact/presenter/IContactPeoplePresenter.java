package com.immomo.momo.contact.presenter;

import android.app.Activity;

import com.immomo.momo.businessmodel.usermodel.IUserModel;
import com.immomo.momo.contact.view.IContactPeopleView;

/**
 * <pre>
 *   author:yangsong
 *   time:2018/10/25
 *   desc: momodev
 * </pre>
 */
public interface IContactPeoplePresenter {

    void acceptContactPeople();

    void requestGuideLive();

    void apply(Activity context, String str, String ph, int gpos, int cpos);

    void bindView(IContactPeopleView iContactPeopleView);

    void initData();

    void onDestroy();

    IUserModel getUserModel();

}
