package com.immomo.momo.contact.adapter;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.immomo.momo.R;
import com.immomo.momo.android.adapter.BaseListAdapter;
import com.immomo.momo.android.view.EmoteTextView;
import com.immomo.momo.contact.activity.SearchContactActivity;
import com.immomo.momo.contact.activity.SearchContactActivity.SearchContactType;

/**
 * Created by joel on 15/3/27.
 * <p/>
 * Momo Tech 2011-2015 © All Rights Reserved.
 */
public class SearchContactAdapter extends BaseListAdapter<SearchContactActivity.SearchContact> {

    public SearchContactAdapter(Context context) {
        super(context);
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if (convertView == null) {
            final ViewHolder holder = new ViewHolder();
            convertView = LayoutInflater.from(context).inflate(R.layout.listitem_search_contact, parent, false);
            holder.tv_type = (TextView) convertView.findViewById(R.id.tv_search_type_desc);
            holder.tv_keyword = (EmoteTextView) convertView.findViewById(R.id.tv_search_keyword);
            holder.image = (ImageView) convertView.findViewById(R.id.search_type_image);
            convertView.setTag(R.id.tag_userlist_item, holder);
        }

        final ViewHolder holder = (ViewHolder) convertView.getTag(R.id.tag_userlist_item);
        SearchContactActivity.SearchContact contact = getItem(position);
        if (holder != null) {
            holder.tv_type.setText(contact.typeDesc);
            holder.tv_keyword.setText(contact.keyword);
            Drawable imageSource = null;
            switch (contact.type) {
                case SearchContactType.USER:
                    imageSource = context.getResources().getDrawable(R.drawable.ic_addfriend_people);
                    break;
                case SearchContactType.GROUP:
                    imageSource = context.getResources().getDrawable(R.drawable.ic_addfriend_group);
                    break;
                case SearchContactType.OFFICAL:
                    imageSource = context.getResources().getDrawable(R.drawable.ic_addfriend_offical);
                    break;
                default:
                    break;
            }
            holder.image.setImageDrawable(imageSource);
        }

        return convertView;
    }

    private class ViewHolder {
        private TextView tv_type;
        private EmoteTextView tv_keyword;
        private ImageView image;
    }
}
