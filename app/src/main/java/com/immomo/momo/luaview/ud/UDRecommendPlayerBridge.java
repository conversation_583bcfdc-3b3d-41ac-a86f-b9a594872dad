package com.immomo.momo.luaview.ud;

import android.content.Context;

import com.immomo.mls.LuaViewManager;
import com.immomo.momo.microvideo.model.MicroVideoJumpType;

import org.luaj.vm2.Globals;
import org.luaj.vm2.JavaUserdata;
import org.luaj.vm2.LuaNumber;
import org.luaj.vm2.LuaString;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

/**
 * Created by XiongFangyu on 2018/6/29.
 */
@LuaApiUsed
public class UDRecommendPlayerBridge extends JavaUserdata {
    public static final String LUA_CLASS_NAME = "RecommendPlayerBridge";

    public static final String[] methods = {
            "gotoPlayerWithItems",
            "updateBridgeItems",
            "gotoPlayer"
    };
    @LuaApiUsed
    public UDRecommendPlayerBridge(long L, LuaValue[] v) {
        super(L, v);
    }

    public UDRecommendPlayerBridge(Globals g, Object jud) {
        super(g, jud);
    }

    //do nothing
    @LuaApiUsed
    public LuaValue[] gotoPlayerWithItems(LuaValue[] values) {
        if (values.length == 3)
            gotoPlayer(varargsOf(LuaNumber.valueOf(values[2].toInt() - 1), LuaString.valueOf(""), LuaString.valueOf("recommend_index")));
        return null;
    }

    //do nothing
    @LuaApiUsed
    public LuaValue[] updateBridgeItems(LuaValue[] varargs) {
        return null;
    }

    @LuaApiUsed
    public LuaValue[] gotoPlayer(LuaValue[] values) {
        int index = values.length != 0 ? values[0].toInt() : 0;
        String requestId = values.length > 1 ? values[1].toJavaString() : null;
        String js = values.length > 2 ? values[2].toJavaString() : null;
        MicroVideoJumpType jumpType = MicroVideoJumpType.parse(js);
        return null;
    }

    private Context getContext() {
        LuaViewManager m = (LuaViewManager) globals.getJavaUserdata();
        return m != null ? m.context : null;
    }

}
