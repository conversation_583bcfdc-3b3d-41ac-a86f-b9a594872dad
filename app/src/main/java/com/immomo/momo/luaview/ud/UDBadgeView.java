package com.immomo.momo.luaview.ud;

import com.alibaba.fastjson.JSON;
import com.cosmos.mdlog.MDLog;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.immomo.android.module.fundamental.Badge.UniformLabel;
import com.immomo.android.module.fundamental.Badge.UniformLabelsBean;
import com.immomo.android.module.fundamental.Badge.model.BaseBadgeModel;
import com.immomo.android.module.nearbypeople.lua.util.NearbyPeopleHelper;
import com.immomo.mls.fun.ud.view.UDView;
import com.immomo.momo.util.GsonUtils;
import com.immomo.momo.util.view.BadgeView;

import org.jetbrains.annotations.NotNull;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import java.util.List;
import java.util.Map;

/**
 * Created by qu.jiaqi
 * on 2021/4/6
 */
@LuaApiUsed
public class UDBadgeView extends UDView<BadgeView> {
    public static final String LUA_CLASS_NAME = "UniformTagView";

    private BadgeView badgeView;

    public static final String[] methods = {
            "bindData"
    };

    @LuaApiUsed
    public UDBadgeView(long L, LuaValue[] initParams) {
        super(L, initParams);
    }

    @NotNull
    @Override
    protected BadgeView newView(LuaValue[] init) {
        badgeView = new BadgeView(getContext());
        return badgeView;
    }

    @LuaApiUsed
    public LuaValue[] bindData(LuaValue[] fun) {

        if (fun != null && fun.length > 0 && fun[0] != null) {
            String json = fun[0].toJavaString();
            try {
                List<BaseBadgeModel> list = NearbyPeopleHelper.Companion.parseBaseBadgeModel(json);
                if (list != null) {
                    badgeView.setData(list);
                }
            } catch (Exception e) {
                MDLog.printErrStackTrace(LUA_CLASS_NAME, e);
            }
        }
        return null;
    }
}
