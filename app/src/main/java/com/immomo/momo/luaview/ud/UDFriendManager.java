package com.immomo.momo.luaview.ud;

import android.content.Intent;

import com.immomo.framework.storage.kv.KV;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.contact.bean.FriendGroupV2;
import com.immomo.momo.mvp.contacts.listeners.RefreshFriendCountReceiver;
import com.immomo.momo.protocol.http.ContactApi;
import com.immomo.momo.service.bean.user.ContactUser;
import com.immomo.momo.service.user.UserService;
import com.immomo.momo.setting.BasicUserInfoUtil;
import com.immomo.push.util.AppContext;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * author: hongming.wei
 * data: 2022/11/14
 */
@LuaClass(isStatic = true)
public class UDFriendManager {

    public static final String LUA_CLASS_NAME = "FriendManager";
    private final static String SORT_TYPE_RELATION_BOTH = "sorttype_realtion_both";
    private final static int SORT_TYPE_LAST_ACTIVE_TIME = 1; // 最后活跃时间排序
    public static final String TAG_TASK_FIND = "FriendManager_TAG_TASK_FIND";


    @LuaBridge
    public static int getCurrentSortType() {
        return KV.getUserInt(SORT_TYPE_RELATION_BOTH, SORT_TYPE_LAST_ACTIVE_TIME);
    }

    @LuaBridge
    public static int getFriendCount(){
        return BasicUserInfoUtil.INSTANCE.getFriendCount();
    }

    @LuaBridge
    public static void setFriendCount(int friendCount){
        BasicUserInfoUtil.INSTANCE.setFriendCount(friendCount);
        new RefreshFriendCountReceiver(AppContext.getContext()).send(new Intent(RefreshFriendCountReceiver.ACTION_REFRESH_FRIEND_COUNT));
    }

    @LuaBridge
    public static void saveFriendList(List<com.alibaba.fastjson.JSONObject> userListJson, LVCallback callback) {
        cancelTasks();
        MomoTaskExecutor.executeUserTask(TAG_TASK_FIND, new SaveFriendDataTask(userListJson, callback));
    }

    @LuaBridge
    public static void requestLocalRefresh(int sortType, LVCallback callback) {
        cancelTasks();
        MomoTaskExecutor.executeUserTask(TAG_TASK_FIND, new LoadCachedFriendsTask(sortType, callback));
    }

    private static void cancelTasks() {
        MomoTaskExecutor.cancleAllTasksByTag(TAG_TASK_FIND);
    }

    private static class SaveFriendDataTask extends MomoTaskExecutor.Task<Object, Object, Void> {

        private LVCallback callback;
        private List<com.alibaba.fastjson.JSONObject> userListJson;

        public SaveFriendDataTask(List<com.alibaba.fastjson.JSONObject> userListJson, LVCallback callback) {
            this.userListJson = userListJson;
            this.callback = callback;
        }

        @Override
        protected Void executeTask(Object... objects) throws Exception {
            List<FriendGroupV2> list = new ArrayList<>();
            for (int i = 0; i < userListJson.size(); i++) {
                FriendGroupV2 tempGroup = ContactApi.parseFriendGroupJson(new JSONObject(userListJson.get(i).toString()));
                if (tempGroup.userList.isEmpty()) {
                    continue;
                }
                list.add(tempGroup);
            }
            List<FriendGroupV2> result = new ArrayList<>();
            for (FriendGroupV2 group : list) {
                //特别好友排在前面
                if ("special".equalsIgnoreCase(group.category)) {
                    result.add(0, group);
                } else {
                    result.add(group);
                }
            }
            UserService.getInstance().saveFriendGroupDataV2(result, true);
            UserService.getInstance().sortGroupListV2(result, getCurrentSortType());
            return null;
        }

        @Override
        protected void onTaskSuccess(Void v) {
            if (callback != null) {
                callback.call(true);
            }
        }

        @Override
        protected void onTaskError(Exception e) {
            super.onTaskError(e);
            if (callback != null) {
                callback.call(false);
            }
        }
    }

    private static class LoadCachedFriendsTask extends MomoTaskExecutor.Task<Integer, Object, ArrayList<ContactUser>> {
        private int sortType;
        private LVCallback callback;

        LoadCachedFriendsTask(int sortType, LVCallback callback) {
            this.sortType = sortType;
            this.callback = callback;
        }

        @Override
        protected ArrayList<ContactUser> executeTask(Integer[] params) throws Exception {
            List<FriendGroupV2> friendList = UserService.getInstance().getFriendGroupList(sortType);
            ArrayList<ContactUser> contactUserList = new ArrayList<>();
            for (int i = 0; i < friendList.size(); i = i + 1) {
                List<ContactUser> contactUsers = friendList.get(i).userList;
                for (ContactUser user: contactUsers) {
                    user.setCategory(friendList.get(i).category);
                }
                contactUserList.addAll(contactUsers);
            }
            return contactUserList;
        }

        @Override
        protected void onTaskSuccess(ArrayList<ContactUser> friendGroups) {
            if (callback != null) {
                KV.saveUserValue(SORT_TYPE_RELATION_BOTH, sortType);
                HashMap<String, Object> response = new HashMap<>();
                response.put("users", com.alibaba.fastjson.JSONObject.toJSONString(friendGroups));
                callback.call(true, response);
            }
        }

        @Override
        protected void onTaskError(Exception e) {
            if (callback != null) {
                callback.call(false, null);
            }
        }
    }
}
