package com.immomo.momo.luaview;

import android.content.Context;
import android.net.Uri;

import com.immomo.framework.base.BaseActivity;
import com.immomo.momo.agora.mr.conflictHelper.VideoConflictNewHelper;
import com.immomo.momo.feed.player.IMediaPlayer;
import com.immomo.momo.util.StringUtils;

/**
 * Created by <PERSON>.k<PERSON> on 2018/11/28.
 */

public class LuaRadioPlayer implements IMediaPlayer.ErrorListener, IMediaPlayer.EventListener {

    private Uri mUri;
    private Callback callback;
    private boolean started = false;
    private boolean mute = false;
    private LuaIJKPlayer mPlayer;
    private Context mContext;
    private boolean useProxyOrCache = true;//true 走代理， false 走直播流

    public LuaRadioPlayer(Context context) {
        this.mContext = context;
        mPlayer = new LuaIJKPlayer();
        mPlayer.setLoopListener(new LuaIJKPlayer.LoopListener() {
            @Override
            public void onLoopStart() {
                if (callback != null) {
                    callback.onLoopStart();
                }
            }
        });
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    public void start() {
        if (mUri == null)
            return;
        VideoConflictNewHelper.stopMusic();
        VideoConflictNewHelper.stopOutMusic();
        if (mContext instanceof BaseActivity) {
            BaseActivity activity = ((BaseActivity) mContext);
            if (!activity.isForeground()) return;
        }
        mPlayer.setUseProxyOrCache(useProxyOrCache);
        mPlayer.setErrorListener(this);
        if (!mUri.equals(mPlayer.getCurrentUri())) {
            mPlayer.updatePlayPosition();
            mPlayer.prepareAndSeek(mUri);
        }
        mPlayer.addListener(this);
        mPlayer.setSilentMode(mute);
        started = true;
        mPlayer.resume();
    }

    public void setUseProxyOrCache(boolean useProxyOrCache) {
        this.useProxyOrCache = useProxyOrCache;
    }

    public void setUri(Uri uri) {
        mUri = uri;
//        IJKMediaPreLoader.getInstance().preloadVideo(uri);
    }

    public void checkUrl(String url) {
        mPlayer.setUseProxyOrCache(StringUtils.isStartWithHttpOrHttps(url));
    }

    public void pause() {
        mPlayer.pause();
    }

    public void stopPlayback() {
        mPlayer.release();
    }

    public void resume() {
        mPlayer.resume();
    }

    public void setSilentMode(boolean silent) {
        mute = silent;
        if (started)
            mPlayer.setSilentMode(silent);
    }

    public void setLoopPlay(boolean loop) {
        mPlayer.setLoopPlay(loop);
    }

    @Override
    public void onPlayerError(int what, int extra) {
        if (callback != null) {
            callback.onError(what, extra);
        }
    }

    public boolean canSeek() {
        if (mUri == null || !started)
            return false;
        int state = mPlayer.getPlaybackState();
        return state == IMediaPlayer.STATE_READY
                || state == IMediaPlayer.STATE_ENDED;
    }

    public long getDuration() {
        if (mUri == null || !started)
            return 0;
        return mPlayer.getDuration();
    }

    public long getCurrentPosition() {
        if (mUri == null || !started)
            return 0;
        return mPlayer.getCurrentPosition();
    }

    public void seekTo(long t) {
        if (mUri == null || !started)
            return;
        mPlayer.seekTo(t);
    }

    public boolean isPlaying() {
        if (mUri == null || !started)
            return false;
        return mPlayer.getPlayWhenReady();
    }

    public boolean getUseProxyOrCache() {
        return useProxyOrCache;
    }

    @Override
    public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {
        if (callback != null) {
            callback.onPlayerStateChanged(playWhenReady, playbackState);
        }
    }

    @Override
    public void onVideoSizeChanged(int width, int height, int unappliedRotationDegrees, float pixelWidthHeightRatio) {

    }

    public interface Callback {
        void onPlayerStateChanged(boolean playWhenReady, int playbackState);

        void onError(int what, int extra);

        void onLoopStart();
    }
}
