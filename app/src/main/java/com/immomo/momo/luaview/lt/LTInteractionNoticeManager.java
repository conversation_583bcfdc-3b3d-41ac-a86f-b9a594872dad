package com.immomo.momo.luaview.lt;

import android.app.Activity;
import android.content.Context;

import androidx.annotation.CallSuper;

import com.cosmos.mdlog.MDLog;
import com.immomo.framework.base.BaseActivity;
import com.immomo.http.exception.HttpBaseException;
import com.immomo.mls.LuaViewManager;
import com.immomo.mls.MLSEngine;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoKit;
import com.immomo.momo.android.synctask.BlockTask;
import com.immomo.momo.android.synctask.ReportBlockBaseTask;
import com.immomo.momo.apt.FeedAppConfigV1Getter;
import com.immomo.momo.eventbus.DataEvent;
import com.immomo.momo.eventbus.EventKeys;
import com.immomo.momo.feed.ui.LikeListDialog;
import com.immomo.momo.platform.utils.BlockHelper;
import com.immomo.momo.protocol.http.InteractionNoticeApi;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.service.sessions.SessionUserCache;
import com.immomo.momo.universe.apt.UniAppConfigV1Getter;

import org.luaj.vm2.Globals;

import java.util.Date;
import java.util.Map;

import de.greenrobot.event.EventBus;


/**
 * Created by li.mengnan
 * on 2020/09/30
 * 互动通知的Lua桥接帮助类，桥接类不可以使用kotiln编写，不然程序运行会有问题
 */
@LuaClass(isStatic = true)
public class LTInteractionNoticeManager {
    //lua 桥接名称
    public static final String LUA_CLASS_NAME = "InteractionNoticeManager";

    private static final String ACCEPT_GROUP_VIDEO = "accept";
    private static final String DENY_GROUP_VIDEO = "deny";

    private static Context getContext() {
        return MLSEngine.getContext();
    }

    /**
     * 拉黑用户
     */
    @LuaBridge
    public static void blockUser(Globals globals, Map userData, LVCallback callback) {
        BaseActivity context = (BaseActivity) ((LuaViewManager) globals.getJavaUserdata()).context;
        String momoid = (String) userData.get("momoid");
        User user = SessionUserCache.getUser(momoid); //获取该用户资料
        if (user == null) {
            user = new User();
            user.momoid = momoid;
            user.name = (String) userData.get("name");
            user.remarkName = (String) userData.get("remarkName");
            user.nickname = (String) userData.get("name");
        }
        MomoTaskExecutor.executeUserTask(LUA_CLASS_NAME, new BlockUserTask(context, MomoKit.getCurrentUser(), user, BlockHelper.BLOCK_FROM_FEED, new
                ReportBlockBaseTask.IBlockTaskCallback() {
                    @Override
                    public void finishDo() {
                        //告知lua页面进行刷新操作
                        if (callback != null) {
                            callback.call(true);
                        }
                    }
                }));
    }

    /**
     * 加入黑名单的任务
     * 与BlockTask业务逻辑基本一致
     */
    private static class BlockUserTask extends ReportBlockBaseTask<String, Object, Boolean> {
        String source = null;

        public BlockUserTask(Activity activity, User currentUser, User remoteUser, String blockSource, IBlockTaskCallback callback) {
            super(activity, currentUser, remoteUser, callback);
            this.source = blockSource;
        }

        @Override
        @CallSuper
        protected Boolean executeTask(String... params) throws Exception {
            boolean success = InteractionNoticeApi.Companion.addBlock(remoteUser.momoid, source);
            return success;
        }

        @Override
        protected void onTaskSuccess(Boolean result) {
            if (result) {
                Toaster.showInvalidate("拉黑成功");
                User user = SessionUserCache.getUser(remoteUser.momoid); //获取该用户资料
                if (user != null) {
                    user.relation = User.RELATION_NONE;  //修改关系为陌生人
                    remoteUser.relation = User.RELATION_NONE; //修改内存中人物的关系
                    user.blocktime = new Date();
                    userService.addBlackUser(user); //插入黑名单列表
                    userService.saveUserSimple(user);
                    updateFriendsList();
                    updateFansList();
                    if (callback != null) {
                        callback.finishDo(); //回调函数
                    }
                    EventBus.getDefault().post(new DataEvent<>(EventKeys.Block.ADD_BLOCK, remoteUser.momoid));
                }
                if (remoteUser != null) {
                    BlockTask.sendBlockEvent(remoteUser.momoid);
                }
            } else {
                Toaster.showInvalidate("拉黑失败，请稍后再试");
            }
        }

        @Override
        protected void onTaskError(Exception e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
            if (e instanceof HttpBaseException) {
                Toaster.showInvalidate(e.getMessage());
            } else {
                Toaster.showInvalidate("拉黑失败，请稍后再试");
            }
        }
    }

    /**
     * 拒绝群视频邀请
     */
    @LuaBridge
    public static void denyGroupVideo(Globals globals, String groupId, String actionUrl) {
        //待处理
    }

    /**
     * 接受群视频邀请
     */
    @LuaBridge
    public static void acceptGroupVideo(Globals globals, String groupId, String actionUrl) {
        //待处理
    }

    @LuaBridge
    public static boolean isNoticeTestA() {
        return true;
    }

    @LuaBridge
    public static boolean isNoticeTestB() {
        return true;
    }

    @LuaBridge
    public static boolean isUniverseNoticeTest() {
        return UniAppConfigV1Getter.get().interactive() == 1;
    }

    @LuaBridge
    public static boolean isShowRecommand(String type) {
        if (type.equals("received")) {
            return FeedAppConfigV1Getter.get().isShowReceivedRecommand() == 1;
        } else if (type.equals("interact")) {
            return FeedAppConfigV1Getter.get().isShowInteractRecommand() == 1;
        }
        return false;
    }

    @LuaBridge
    public static void showLikeList(String action) {
        if (MomoKit.getTopActivity() != null) {
            new LikeListDialog(MomoKit.getTopActivity(), action).show();
        }
    }

    @LuaBridge
    public static boolean isNoticeTestAll() {
        return true;
    }

}
