package com.immomo.momo.luaview.ud;

import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.text.Layout;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.ViewGroup;

import com.immomo.framework.utils.UIUtils;
import com.immomo.mls.fun.constants.FontStyle;
import com.immomo.mls.fun.constants.TextAlign;
import com.immomo.mls.fun.ud.UDArray;
import com.immomo.mls.fun.ud.UDColor;
import com.immomo.mls.fun.ud.UDStyleString;
import com.immomo.mls.fun.ud.view.UDView;
import com.immomo.mls.util.AndroidUtil;
import com.immomo.mls.util.DimenUtil;
import com.immomo.mls.utils.AssertUtils;
import com.immomo.mls.utils.convert.ConvertUtils;
import com.immomo.momo.R;
import com.immomo.momo.android.view.textview.LayoutTextView;
import com.immomo.momo.android.view.textview.textshrink.TextShrinkHelper;
import com.immomo.momo.emotionstore.util.MomoEmotionUtil;
import com.immomo.momo.feed.ui.FeedTextLayoutManager;
import com.immomo.momo.feed.ui.view.FeedTextView;
import com.immomo.momo.luaview.constants.MatchLinkType;
import com.immomo.momo.util.StringUtils;

import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaNumber;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import xfy.fakeview.library.text.drawer.TextDrawer;

/**
 * Author       :   lmnrenbc
 * Date         :   2020/10/10
 * Time         :   下午3:36
 * Description  :   参考自水滴表情Label
 */
@LuaApiUsed
public class UDMultifunctionLabel<V extends FeedTextView> extends UDView<V> {
    public static final String LUA_CLASS_NAME = "MultifunctionLabel";

    public static final String[] methods = new String[]{
            "fontSize",//字体大小
            "textAlign",//文本对齐方式
            "textColor",//字体颜色
            "lines",//最大展示行数
            "setLineSpacing",//设置行间距
            "setMaxWidth",//设置最大展示宽度
            "breakMode",//设置文本截断模式（待完善）
            "setMatchLinkType",//设置文本识别类型
            "text",//设置文本
            "styleText",//设置文本样式
            "setFooterStyleText",//设置文本尾部文本及样式
            "setTextBold",//设置加粗
            "fontNameSize",//（未实现）
            "setTextFontStyle",//设置字体样式（斜体、加粗）
            "setAutoFit",//（未实现）
            "setLinksCallback",//设置富文本（表情、字体颜色、字体点击）
            "emojiScale", //表情缩放倍数
            "bgUniFmColor" //小宇宙电台专用背景色
    };

    private static final String TEXT_FINAL_KEY = "text";
    private static final String ACTION_FINAL_KEY = "action";
    private static final String COLOR_FINAL_KEY = "color";

    private static final String HINT_ELLIPSIS = "...";
    public static final String HINT_TAIL = HINT_ELLIPSIS;

    private static final String ERROR_COLOR = "Unknown color";

    private static int mEndAllTextColor = UIUtils.getColor(com.immomo.momo.android.R.color.color_BEBEBE);
    private static float mEndAllTextSize = 13;
    private static String mEndAllText = HINT_TAIL;
    private static float mLineSpacing = 0;
    private static Field ellipsesTypeField;

    private LuaFunction mLinkFunctionCallback;
    private LuaFunction mEndAllTextClickFunction;

    private int mMatchLinkType = MatchLinkType.None;
    private Map map;
    private UDArray mMapArray;

    private StaticLayout mStaticLayout;
    private float mMaxWidth = -1;
    private int mMaxLines;

    private int fontStyle = FontStyle.NORMAL;
    private TextPaint mTextPaint;
    private int color;
    private int mTextSize = UIUtils.sp2pix(16f);
    private int mTextColor;

    private double mEmojiScale = 1.5;

    private String mContentValue = "";

    private TextShrinkHelper.ShrinkClickListener mShrinkClickListener = new TextShrinkHelper.ShrinkClickListener() {
        @Override
        public void onClick(View widget) {
            if (mEndAllTextClickFunction != null) {
                mEndAllTextClickFunction.invoke(LuaValue.rString(mEndAllText));
            }
        }
    };

    @Override
    protected V newView(LuaValue[] init) {
        return (V) new FeedTextView(getContext());
    }

    @LuaApiUsed
    protected UDMultifunctionLabel(long l, LuaValue[] v) {
        super(l, v);
        initPaint();
    }

    private void initPaint() {
        mTextPaint = new TextPaint(Paint.ANTI_ALIAS_FLAG);
        mTextPaint.density = UIUtils.getResources().getDisplayMetrics().density;
        mTextPaint.setTextSize(UIUtils.sp2pix(16f));
        mTextPaint.setColor(UIUtils.getColor(R.color.C_05));
    }

    @LuaApiUsed
    public LuaValue[] fontSize(LuaValue[] var) {
        if (var.length > 0 && !var[0].isNil()) {
            int size = UIUtils.sp2pix((float) var[0].toDouble());
            mTextSize = size;
            mTextPaint.setTextSize(size);
            mEndAllTextSize = (float) var[0].toDouble();
            return null;
        }
        return LuaValue.rNumber(DimenUtil.pxToSp(mTextPaint.getTextSize()));
    }

    @LuaApiUsed
    public LuaValue[] textAlign(LuaValue[] var) {
        if (var.length > 0 && !var[0].isNil()) {

            switch (var[0].toInt()) {
                case TextAlign.LEFT:
                    mTextPaint.setTextAlign(Paint.Align.LEFT);
                    break;
                case TextAlign.CENTER:
                    mTextPaint.setTextAlign(Paint.Align.CENTER);
                    break;
                case TextAlign.RIGHT:
                    mTextPaint.setTextAlign(Paint.Align.RIGHT);
                    break;
            }
            return null;
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] textColor(LuaValue[] var) {
        if (var.length > 0 && var[0] instanceof UDColor) {
            UDColor textColor = (UDColor) var[0];
            mTextColor = textColor.getColor();
            mTextPaint.setColor(mTextColor);
            return null;
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] lines(LuaValue[] var) {
        if (var.length > 0 && !var[0].isNil()) {
            mMaxLines = var[0].toInt();

            if (mMaxLines == 0) {
                mMaxLines = Integer.MAX_VALUE;
            }

            if (StringUtils.isNotEmpty(mContentValue)) {
                setText(mContentValue + " ");
            }

            if (mMaxLines == Integer.MAX_VALUE && mMapArray != null && mLinkFunctionCallback != null) {
                parseSingleAction(mMapArray, mLinkFunctionCallback);
            }
            return null;
        }

        return null;
    }

    @LuaApiUsed
    public LuaValue[] setLineSpacing(LuaValue[] spacing) {
        if (spacing.length > 0) {
            mLineSpacing = UIUtils.getPixels(spacing[0].toInt());
            return null;
        }
        return LuaValue.rNumber(0);
    }

    @Override
    @LuaApiUsed
    public LuaValue[] setMaxWidth(LuaValue[] w) {
        mMaxWidth = DimenUtil.dpiToPx((float) w[0].toDouble());

        if (mMaxWidth > 0)
            setWidth(mMaxWidth);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] breakMode(LuaValue[] var) {
        if (var.length > 0 && !var[0].isNil()) {
            return null;
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setMatchLinkType(LuaValue[] type) {
        mMatchLinkType = type.length > 0 ? type[0].toInt() : MatchLinkType.None;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] emojiScale(LuaValue[] values) {
        if (values.length >= 1 && values[0].isNumber()) {
            mEmojiScale = values[0].toDouble();
            return null;
        }
        return varargsOf(LuaNumber.valueOf(mEmojiScale));
    }

    @LuaApiUsed
    public LuaValue[] text(LuaValue[] args) {
        String text = args[0].toJavaString();
        if (mContentValue.equals(text)) {
            return null;
        }
        setViewEllipses();
        mContentValue = text;
        CharSequence textSpan = MomoEmotionUtil.getEmoteStaticSpan(text, (int) (mTextSize * mEmojiScale));
        if ((mMatchLinkType & MatchLinkType.All) == MatchLinkType.All ||
                (mMatchLinkType & MatchLinkType.PhoneNumber) == MatchLinkType.PhoneNumber ||
                (mMatchLinkType & MatchLinkType.MOMOID) == MatchLinkType.MOMOID) {
            textSpan = FeedTextLayoutManager.addMoMoIdSpan(textSpan, FeedTextLayoutManager.MOMO_ID_PATTERN);
        }
        mTextPaint.setTextSize(mTextSize);
        mTextPaint.setColor(mTextColor);
        TextDrawer.clear(mTextPaint, Typeface.BOLD);
        TextDrawer.clear(mTextPaint, Typeface.ITALIC);
        TextDrawer.clear(mTextPaint, Typeface.BOLD_ITALIC);
        if (fontStyle != FontStyle.NORMAL) {
            TextDrawer.apply(mTextPaint, fontStyle);
        }
        //根据当前控件宽度，设置文本展示宽度！！！无法设置自适应或充满视图，此时还无法进行测量，因此必须要设置视图宽度
        ViewGroup.LayoutParams lp = getView().getLayoutParams();
        int w = ViewGroup.LayoutParams.WRAP_CONTENT;
        if (lp != null) {
            w = lp.width - getView().getPaddingLeft() - getView().getPaddingRight();
        }
        if (w < 0) {
            w = AndroidUtil.getScreenWidth(getContext());
        }

        mStaticLayout = new StaticLayout(textSpan, mTextPaint, w, Layout.Alignment.ALIGN_NORMAL,
                1f, mLineSpacing, true);
        if (mMaxLines != 0) {
            mStaticLayout = makeLayoutOfTail(w, mMaxLines, "", mStaticLayout, mShrinkClickListener);
            getView().setMaxLines(mMaxLines);
        }
        getView().setLayout(mStaticLayout);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] bgUniFmColor(LuaValue[] values) {
        getView().setBackgroundResource(R.drawable.bg_multifunction_label_uni_fm_card);
        return null;
    }

    private void setViewEllipses() {
        if (ellipsesTypeField == null) {
            try {
                ellipsesTypeField = LayoutTextView.class.getDeclaredField("ellipsesType");
                ellipsesTypeField.setAccessible(true);
            } catch (Exception ignore) {
                //无需处理
            }
        }
        if (ellipsesTypeField != null) {
            try {
                ellipsesTypeField.set(getView(), LayoutTextView.ELLIPSES);
            } catch (Exception ignore) {
                //无需处理
            }
        }
    }

    private void setText(String content) {

        if (mContentValue.equals(content)) {
            return;
        }

        mContentValue = content;
        int emojiSize = (int) (mTextSize * mEmojiScale);

        CharSequence textSpan = MomoEmotionUtil.getEmoteStaticSpan(content, emojiSize);

        if ((mMatchLinkType & MatchLinkType.All) == MatchLinkType.All ||
                (mMatchLinkType & MatchLinkType.PhoneNumber) == MatchLinkType.PhoneNumber ||
                (mMatchLinkType & MatchLinkType.MOMOID) == MatchLinkType.MOMOID) {
            textSpan = FeedTextLayoutManager.addMoMoIdSpan(textSpan, FeedTextLayoutManager.MOMO_ID_PATTERN);
        }

        mTextPaint.setTextSize(mTextSize);
        mTextPaint.setColor(mTextColor);
        mStaticLayout = new StaticLayout(textSpan, mTextPaint, FeedTextLayoutManager.getFeedTextWidth(), Layout.Alignment.ALIGN_NORMAL, 1f, mLineSpacing, true);

        if (mMaxLines != 0) {
            mStaticLayout = makeLayoutOfTail(getWidth(), mMaxLines, "", mStaticLayout, mShrinkClickListener);
        }
        resetWidth(content);
    }

    private void resetWidth(String content) {
        if (mMaxWidth > 0) {
            getView().setMaxWidth((int) mMaxWidth);
        }
        getView().setLayout(mStaticLayout);

        float value = mTextPaint.measureText(content);
        if (value > mMaxWidth) {
            value = mMaxWidth;
        }
        setWidth(value);
    }

    @LuaApiUsed
    public LuaValue[] styleText(LuaValue[] styles) {

        mStaticLayout = new StaticLayout(((UDStyleString) styles[0]).getText(), mTextPaint, FeedTextLayoutManager.getFeedTextWidth(), Layout.Alignment.ALIGN_NORMAL, 1f, mLineSpacing, true);

        if (mMaxLines != 0)
            mStaticLayout = makeLayoutOfTail(getWidth(), mMaxLines, "", mStaticLayout, mShrinkClickListener);

        if (mMaxWidth > 0)
            getView().setMaxWidth((int) mMaxWidth);

        getView().setLayout(mStaticLayout);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setFooterStyleText(LuaValue[] values) {
        UDStyleString styleString = values.length > 0 ? ((UDStyleString) values[0]) : null;

        LuaFunction endAllTextClickFunction = values.length > 1 ? values[1].toLuaFunction() : null;
        if (styleString != null) {

            if (styleString.getColor() != -1) {
                mEndAllTextColor = styleString.getColor();
            }
            if (styleString.getTextSize() != -1) {
                mEndAllTextSize = styleString.getTextSize();
            }
            mEndAllText = styleString.getText().toString();

            mEndAllTextClickFunction = endAllTextClickFunction;

            return null;
        }

        return null;
    }

    @LuaApiUsed
    public LuaValue[] setTextBold(LuaValue[] values) {
        return null;

    }

    @LuaApiUsed
    public LuaValue[] fontNameSize(LuaValue[] var) {
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setTextFontStyle(LuaValue[] style) {
        fontStyle = style[0].toInt();
        return null;

    }

    @LuaApiUsed
    public LuaValue[] setAutoFit(LuaValue[] autoFit) {
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setLinksCallback(LuaValue[] values) {
        UDArray mapArray = ((UDArray) values[0]);
        LuaFunction linkFunctionCallback = values.length > 1 ? values[1].toLuaFunction() : null;

        parseSingleAction(mapArray, linkFunctionCallback);
        return null;
    }

    private void parseSingleAction(UDArray mapArray, LuaFunction linkFunctionCallback) {
        List mapList = mapArray.getArray();

        mMapArray = mapArray;
        mLinkFunctionCallback = linkFunctionCallback;

        SpannableStringBuilder stringBuilder = new SpannableStringBuilder();

        for (int i = 0, size = mapList.size(); i < size; i++) {
            Map singleMap = (Map) mapList.get(i);
            String singleTextValue = (String) singleMap.get(TEXT_FINAL_KEY);
            stringBuilder.append(singleTextValue);
        }

        String finalValue = stringBuilder.toString();

        for (int i = 0, size = mapList.size(); i < size; i++) {
            Map singleMap = (Map) mapList.get(i);

            String singleTextValue = (String) singleMap.get(TEXT_FINAL_KEY);
            String singleActionValue = (String) singleMap.get(ACTION_FINAL_KEY);
            String singleColorValue = ((String) singleMap.get(COLOR_FINAL_KEY));

            if (StringUtils.isNotEmpty(singleColorValue)) {  // 有颜色  设置颜色
                setColor(singleColorValue);
            } else if (StringUtils.isNotEmpty(singleActionValue)) {  //  有事件  没有颜色  设置默认蓝色
                setColor("(59,179,250)");
            } else {
                this.color = UIUtils.getColor(R.color.C_05);
            }

            int start = finalValue.indexOf(singleTextValue);
            int end = start + singleTextValue.length();

            if (start >= end) {
                continue;
            }

            ForegroundColorSpan colorSpan = new ForegroundColorSpan(color);

            stringBuilder.setSpan(colorSpan, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

            if (StringUtils.isNotEmpty(singleActionValue)) {
                Clickable clickable = new Clickable();
                clickable.setTextValue(singleTextValue);
                clickable.setPosition(i + 1);
                clickable.setAction(singleActionValue);
                clickable.setColor(color);
                stringBuilder.setSpan(clickable, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
        }

        int emojiSize = (int) (mTextPaint.getTextSize() * mEmojiScale);
        CharSequence textSpan = MomoEmotionUtil.getEmoteStaticSpan(stringBuilder, emojiSize);
        int width = FeedTextLayoutManager.getFeedTextWidth();
        if (getView().getLayoutParams().width > 0) {
            width = getView().getLayoutParams().width - getView().getPaddingLeft() - getView().getPaddingRight();
        }

        mStaticLayout = new StaticLayout(textSpan, mTextPaint, width, Layout.Alignment.ALIGN_NORMAL, 1f, mLineSpacing, true);

        mContentValue = mStaticLayout.getText().toString();
        if (mMaxLines != 0) {
            mStaticLayout = makeLayoutOfTail(width, mMaxLines, "", mStaticLayout, mShrinkClickListener);
        }
        resetWidth(mStaticLayout.getText().toString());
    }

    /**
     * 文本内容点击
     */
    private class Clickable extends ClickableSpan {

        private String textValue = "";
        private String action = "";
        private int position;
        private int color;

        public void setTextValue(String textValue) {
            this.textValue = textValue;
        }

        public void setPosition(int position) {
            this.position = position;
        }

        public void setAction(String action) {
            this.action = action;
        }

        public void setColor(int color) {
            this.color = color;
        }

        @Override
        public void onClick(View v) {
            // 处理点击回调数据
            if (map == null) {
                map = new HashMap();
            }
            map.clear();
            map.put(TEXT_FINAL_KEY, textValue);
            map.put(ACTION_FINAL_KEY, action);
            map.put(COLOR_FINAL_KEY, color);

            if (mLinkFunctionCallback != null) {
                mLinkFunctionCallback.invoke(LuaValue.varargsOf(LuaNumber.valueOf(position), ConvertUtils.toLuaValue(getGlobals(), map)));
            }
        }

        @Override
        public void updateDrawState(TextPaint ds) {
            ds.setUnderlineText(false);
        }
    }

    /**
     * 设置文案颜色
     *
     * @param colorStr
     */
    public void setColor(String colorStr) {
        if (colorStr.startsWith("(") && colorStr.endsWith(")")) {
            colorStr = colorStr.substring(1, colorStr.length() - 1);
        }

        String[] rgb = colorStr.split(",");
        if (rgb.length != 3) {
            throw new IllegalArgumentException(ERROR_COLOR);
        }

        int r, g, b;
        try {
            r = Integer.valueOf(rgb[0].trim());
            g = Integer.valueOf(rgb[1].trim());
            b = Integer.valueOf(rgb[2].trim());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException(ERROR_COLOR);
        }

        if (r < 0 || r > 255 || g < 0 || g > 255 || b < 0 || b > 255) {
            throw new IllegalArgumentException(ERROR_COLOR);
        }

        this.color = Color.rgb(r, g, b);
    }

    /**
     * 处理文本末尾截断及补充文案
     *
     * @param newWidth
     * @param maxLines
     * @param topicText
     * @param contenLayout
     * @param listener
     * @return
     */
    public StaticLayout makeLayoutOfTail(int newWidth, int maxLines, @NonNull CharSequence topicText,
                                         @NonNull StaticLayout contenLayout, TextShrinkHelper.ShrinkClickListener listener) {

        if (contenLayout.getLineCount() <= maxLines) {
            return contenLayout;
        }

        CharSequence originText = contenLayout.getText();
        TextPaint textPaint = contenLayout.getPaint();
        final int spacingAdd = UIUtils.getPixels(1f);
        float hintTailLenght = textPaint.measureText(mEndAllText);

        // 限定话题宽度
        CharSequence resultTopicText = TextUtils.ellipsize(topicText, textPaint, newWidth - hintTailLenght - UIUtils.getPixels(3), TextUtils.TruncateAt.END);

        // topic + HINT_TAIL 所需要的长度
        int retainStrWidth = (int) (textPaint.measureText(resultTopicText, 0, resultTopicText.length()) + textPaint.measureText(mEndAllText));

        if (retainStrWidth > newWidth) {
            // 防止计算误差 导致结果限定宽度
            retainStrWidth = newWidth;
        }
        // 处理文本截断
        CharSequence shrinkText = contenLayout.getText();
        if (contenLayout.getLineCount() >= maxLines) {
            // 只在大于maxlines时需要截断 ， 留出最后一行给topic+HINT_TAIL
            final int line = maxLines - 1;
            final int lineStart = contenLayout.getLineStart(line);
            final int lineEnd = contenLayout.getLineEnd(line);

            final int tailWidth = retainStrWidth;
            final int lineWidth = contenLayout.getWidth();

            int lineTrim = lineEnd;
            CharSequence subText = originText.subSequence(lineStart, lineTrim);

            while (Layout.getDesiredWidth(subText, textPaint) + tailWidth > lineWidth) {
                lineTrim--;
                if (lineTrim <= lineStart) {
                    break;
                }
                subText = originText.subSequence(lineStart, lineTrim);
            }
            shrinkText = removeEndLineBreak(originText.subSequence(0, lineTrim));
        }

        // 设置尾部省略后的补充文案的颜色与点击事件
        SpannableStringBuilder mTextWithTail = new SpannableStringBuilder(shrinkText).append(mEndAllText).append(resultTopicText);
        // 计算补充文案的起始位置
        final int length = mTextWithTail.length() - mEndAllText.length() - resultTopicText.length();
        if (mEndAllTextClickFunction != null) {
            mTextWithTail.setSpan(new TouchableSpan(listener), length, mTextWithTail.length()
                    , Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }

        return new StaticLayout(mTextWithTail, 0, mTextWithTail.length(), textPaint,
                newWidth, Layout.Alignment.ALIGN_NORMAL, 1f, mLineSpacing, true);
    }

    public static class TouchableSpan extends ClickableSpan {

        private final TextShrinkHelper.ShrinkClickListener mListener;

        public TouchableSpan(TextShrinkHelper.ShrinkClickListener listener) {
            mListener = listener;
        }

        @Override
        public void onClick(View widget) {
            if (mListener != null) {
                mListener.onClick(widget);
            }
        }

        @Override
        public void updateDrawState(TextPaint ds) {
            super.updateDrawState(ds);
            ds.setColor(mEndAllTextColor);
            ds.setTextSize(UIUtils.sp2pix(mEndAllTextSize));
            ds.setUnderlineText(false);
        }
    }

    public static CharSequence removeEndLineBreak(CharSequence text) {
        if (TextUtils.isEmpty(text)) {
            return "";
        }
        final int length = text.length();
        if (length > 0 && TextUtils.equals("\n", "" + text.charAt(length - 1))) {
            text = text.subSequence(0, length - 1);
        }
        return text;
    }
}
