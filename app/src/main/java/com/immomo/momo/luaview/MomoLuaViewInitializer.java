package com.immomo.momo.luaview;

import android.content.Context;
import android.os.Looper;

import com.immomo.android.login.ud.UDBindPhoneManager;
import com.immomo.android.mvvm.luaview.lt.LTAccountCacheManager;
import com.immomo.android.mvvm.luaview.lt.LTLoginRegisterLog;
import com.immomo.android.mvvm.luaview.lt.LTLoginTool;
import com.immomo.android.mvvm.luaview.lt.LTQuickLoginManager;
import com.immomo.android.mvvm.luaview.ud.UDAccountRequestManager;
import com.immomo.android.mvvm.luaview.ud.UDChangePasswordManager;
import com.immomo.android.mvvm.luaview.ud.UDDateTimePickerController;
import com.immomo.android.mvvm.luaview.ud.UDRegisterRequestManager;
import com.immomo.mls.MLSBuilder;
import com.immomo.mls.MLSEngine;
import com.immomo.mls.wrapper.IJavaObjectGetter;
import com.immomo.mls.wrapper.ILuaValueGetter;
import com.immomo.mls.wrapper.Register;
import com.immomo.momo.BuildConfig;
import com.immomo.momo.baseroom.UDRoomHandler;
import com.immomo.momo.baseroom.im.UDAbsRoomImAdapter;
import com.immomo.momo.baseroom.media.UDAbsRoomMediaAdapter;
import com.immomo.momo.feedlist.intercept.FeedUserDataInjectInterceptor;
import com.immomo.momo.luaview.java.ShareHelper;
import com.immomo.momo.luaview.lt.LTABTestManager;
import com.immomo.momo.luaview.lt.LTFriendNoticeManager;
import com.immomo.momo.luaview.lt.LTFriendService;
import com.immomo.momo.luaview.lt.LTQQContactManager;
import com.immomo.momo.luaview.lt.LTWxContactManager;
import com.immomo.momo.luaview.lt.LTGroupSearchABHelper;
import com.immomo.momo.luaview.lt.LTHalfAcManager;
import com.immomo.momo.luaview.lt.LTInteractionNoticeManager;
import com.immomo.momo.luaview.lt.LTLocationManager;
import com.immomo.momo.luaview.lt.LTLoginSafeManager;
import com.immomo.momo.luaview.lt.LTMMUserProfile;
import com.immomo.momo.luaview.lt.LTMoreFrameHelper;
import com.immomo.momo.luaview.lt.LTNearbyViewModel;
import com.immomo.momo.luaview.lt.LTNoteManager;
import com.immomo.momo.luaview.lt.LTPrivacyManager;
import com.immomo.momo.luaview.lt.LTSecurityManager;
import com.immomo.momo.luaview.lt.LTSettingManager;
import com.immomo.momo.luaview.lt.LTSyncMsgManager;
import com.immomo.momo.luaview.lt.LTUniverseManager;
import com.immomo.momo.luaview.lt.LTVChatLocationManager;
import com.immomo.momo.luaview.lt.SISharehandler;
import com.immomo.momo.luaview.media.UDCustomSwipeRefreshLayout;
import com.immomo.momo.luaview.media.UDGlobalMediaView;
import com.immomo.momo.luaview.media.UDHorizontalScrollLabelView;
import com.immomo.momo.luaview.media.UDProgressBar;
import com.immomo.momo.luaview.media.UDVerticalScrollLabelView;
import com.immomo.momo.luaview.media.UDVerticalViewPager;
import com.immomo.momo.luaview.media.UDVerticalViewPagerAdapter;
import com.immomo.momo.luaview.media.UDVideoManager;
import com.immomo.momo.luaview.media.UDVideoSeekBar;
import com.immomo.momo.luaview.pipeline.UDIjkConferenceStreamer;
import com.immomo.momo.luaview.ud.LTUniFmChecker;
import com.immomo.momo.luaview.ud.MomoHomeHelper;
import com.immomo.momo.luaview.ud.UDAudioBgView;
import com.immomo.momo.luaview.ud.UDBadgeView;
import com.immomo.momo.luaview.ud.UDBlurLabelView;
import com.immomo.momo.luaview.ud.UDBubbleRefreshTableView;
import com.immomo.momo.luaview.ud.UDCameraHelper;
import com.immomo.momo.luaview.ud.UDCommentView;
import com.immomo.momo.luaview.ud.UDContactsManager;
import com.immomo.momo.luaview.ud.UDFeedManager;
import com.immomo.momo.luaview.ud.UDFeedPublishView;
import com.immomo.momo.luaview.ud.UDFeedVideoView;
import com.immomo.momo.luaview.ud.UDGotoManager;
import com.immomo.momo.luaview.ud.UDProfileFlexBoxLabelListView;
import com.immomo.momo.luaview.ud.UDFriendManager;
import com.immomo.momo.luaview.ud.UDInteractionNoticeCommentView;
import com.immomo.momo.luaview.ud.UDLuaImEvent;
import com.immomo.momo.luaview.ud.UDMediaView;
import com.immomo.momo.luaview.ud.UDMgsOperateView;
import com.immomo.momo.luaview.ud.UDMixLabel;
import com.immomo.momo.luaview.ud.UDMultifunctionLabel;
import com.immomo.momo.luaview.ud.UDNearbyOnLiveShineView;
import com.immomo.momo.luaview.ud.UDNearbyPeopleHandler;
import com.immomo.momo.luaview.ud.UDNearbyPeopleTableView;
import com.immomo.momo.luaview.ud.UDNearbyPlayHandler;
import com.immomo.momo.luaview.ud.UDNoteChatEmojiView;
import com.immomo.momo.luaview.ud.UDNotePublish;
import com.immomo.momo.luaview.ud.UDPersonalFeedHandler;
import com.immomo.momo.luaview.ud.UDProfileHelper;
import com.immomo.momo.luaview.ud.UDQiaoQiaoLuaManager;
import com.immomo.momo.luaview.ud.UDRadioPlayer;
import com.immomo.momo.luaview.ud.UDRecommendPlayerBridge;
import com.immomo.momo.luaview.ud.UDScrollHelper;
import com.immomo.momo.luaview.ud.UDShimmeringLabel;
import com.immomo.momo.luaview.ud.UDSimpleScrollSwitchView;
import com.immomo.momo.luaview.ud.UDSitePicker;
import com.immomo.momo.luaview.ud.UDStickyRefreshCoordinatorLayout;
import com.immomo.momo.luaview.ud.UDTagView;
import com.immomo.momo.luaview.ud.UDUniverseOpenLibraryLuaHelp;
import com.immomo.momo.luaview.ud.UDVideoGiftView;
import com.immomo.momo.luaview.ud.UDVideoLuaManager;
import com.immomo.momo.luaview.ud.UDVideoPublishView;
import com.immomo.momo.luaview.ud.UDVideoView;
import com.immomo.momo.luaview.ud.UDVipVisitorManager;
import com.immomo.momo.luaview.ud.UDWebView;
import com.immomo.momo.luaview.ud.UDWolfCountDownView;
import com.immomo.momo.luaview.ud.UnitedShareHelper;
import com.immomo.momo.luaview.ud.sliceupload.UDSliceSimpleUploadProcess;
import com.immomo.momo.luaview.weight.MDEmoteEditTextView;

import org.luaj.vm2.Globals;
import org.luaj.vm2.LuaValue;

public class MomoLuaViewInitializer {
    public static void init(Context context) {
        MLSEngine.init(context, BuildConfig.DEBUG)
                .registerUD(registerUD())  //注册UserData
                .registerSC(registerLT())  //注册静态工具类
                .registerSingleInsance(registerSingleInstance())//注册java对象，实际上是userdata，建议使用UserData
                .registerCovert(registerCovert()) //注册lua对象和java对象转换实现
                .registerConstants(registerLuaConstants())//注册枚举
                .build(Looper.myLooper() != Looper.getMainLooper());

        Register.UDHolder[] unHolder = {Register.newUDHolder(UDBadgeView.LUA_CLASS_NAME, UDBadgeView.class, true, UDBadgeView.methods)};
        Register.SHolder[] sHolder = {Register.newSHolderWithLuaClass(LTMMUserProfile.LUA_CLASS_NAME, LTMMUserProfile.class)};
        FeedUserDataInjectInterceptor.Companion.setUdArray(unHolder);
        FeedUserDataInjectInterceptor.Companion.setLTArray(sHolder);
    }

    /**
     * 两种方式：
     * 第一种，类继承自 {@link org.luaj.vm2.LuaUserdata}
     * 使用 {@link com.immomo.mls.wrapper.Register#newUDHolder(String, Class, boolean, String...)}
     * <p>
     * 第二种，类中包含 {@link com.immomo.mls.annotation.LuaClass}注解
     * 使用 {@link com.immomo.mls.wrapper.Register#newUDHolderWithLuaClass(String, Class, boolean)}
     */
    private static Register.UDHolder[] registerUD() {
        return new Register.UDHolder[]{
                Register.newUDHolder(UDCameraHelper.LUA_CLASS_NAME, UDCameraHelper.class, true, UDCameraHelper.methods),
                Register.newUDHolder(UDRecommendPlayerBridge.LUA_CLASS_NAME, UDRecommendPlayerBridge.class, true, UDRecommendPlayerBridge.methods),
                Register.newUDHolder(UDVideoView.LUA_CLASS_NAME, UDVideoView.class, true, UDVideoView.methods),
                Register.newUDHolder(UDWebView.LUA_CLASS_NAME, UDWebView.class, true, UDWebView.methods),
                Register.newUDHolder(UDFeedPublishView.LUA_CLASS_NAME, UDFeedPublishView.class, true, UDFeedPublishView.methods),
                Register.newUDHolder(UDVideoPublishView.LUA_CLASS_NAME, UDVideoPublishView.class, true, UDVideoPublishView.methods),
                Register.newUDHolder(UDTagView.LUA_CLASS_NAME, UDTagView.class, true, UDTagView.methods),
                Register.newUDHolder(UDNearbyOnLiveShineView.LUA_CLASS_NAME, UDNearbyOnLiveShineView.class, true, UDNearbyOnLiveShineView.methods),
                Register.newUDHolder(UDBubbleRefreshTableView.LUA_CLASS_NAME, UDBubbleRefreshTableView.class, true, UDBubbleRefreshTableView.methods),
                Register.newUDHolder(UDNearbyPeopleTableView.LUA_CLASS_NAME, UDNearbyPeopleTableView.class, true, UDNearbyPeopleTableView.methods),
                Register.newUDHolder(UDMgsOperateView.LUA_CLASS_NAME, UDMgsOperateView.class, true, UDMgsOperateView.methods),
                Register.newUDHolder(UDBadgeView.LUA_CLASS_NAME, UDBadgeView.class, true, UDBadgeView.methods),
                Register.newUDHolder(UDMixLabel.LUA_CLASS_NAME, UDMixLabel.class, true, UDMixLabel.methods),
                Register.newUDHolder(UDMultifunctionLabel.LUA_CLASS_NAME, UDMultifunctionLabel.class, true, UDMultifunctionLabel.methods),
                Register.newUDHolder(UDFeedVideoView.LUA_CLASS_NAME, UDFeedVideoView.class, true, UDFeedVideoView.methods),
                Register.newUDHolder(UDMediaView.LUA_CLASS_NAME, UDMediaView.class, true, UDMediaView.methods),
                Register.newUDHolder(UDSimpleScrollSwitchView.LUA_CLASS_NAME, UDSimpleScrollSwitchView.class, true, UDSimpleScrollSwitchView.methods),

                Register.newUDHolder(UDRadioPlayer.LUA_CLASS_NAME, UDRadioPlayer.class, true, UDRadioPlayer.methods),
                Register.newUDHolder(UDIjkConferenceStreamer.LUA_CLASS_NAME, UDIjkConferenceStreamer.class, true, UDIjkConferenceStreamer.methods),

                //baseroom插件
                Register.newUDHolderWithLuaClass(UDRoomHandler.LUA_CLASS_NAME, UDRoomHandler.class, true),
                Register.newUDHolderWithLuaClass(UDAbsRoomImAdapter.LUA_CLASS_NAME, UDAbsRoomImAdapter.class, true),
                Register.newUDHolderWithLuaClass(UDAbsRoomMediaAdapter.LUA_CLASS_NAME, UDAbsRoomMediaAdapter.class, true),

                Register.newUDHolderWithLuaClass(UDNearbyPlayHandler.LUA_CLASS_NAME, UDNearbyPlayHandler.class, true),
                Register.newUDHolderWithLuaClass(UDNearbyPeopleHandler.LUA_CLASS_NAME, UDNearbyPeopleHandler.class, true),
                Register.newUDHolderWithLuaClass(ShareHelper.LUA_CLASS_NAME, ShareHelper.class, true),
                Register.newUDHolderWithLuaClass(UDPersonalFeedHandler.LUA_CLASS_NAME, UDPersonalFeedHandler.class, true),

                Register.newUDHolder(UDSitePicker.LUA_CLASS_NAME, UDSitePicker.class, true, UDSitePicker.methods),

                Register.newUDHolder(UDWolfCountDownView.LUA_CLASS_NAME, UDWolfCountDownView.class, false, UDWolfCountDownView.methods),
                Register.newUDHolder(UDLuaImEvent.LUA_CLASS_NAME, UDLuaImEvent.class, true, UDLuaImEvent.methods),
                Register.newUDHolderWithLuaClass(UnitedShareHelper.LUA_CLASS_NAME, UnitedShareHelper.class, true),
                Register.newUDHolderWithLuaClass(UDSliceSimpleUploadProcess.LUA_CLASS_NAME, UDSliceSimpleUploadProcess.class, true),
                Register.newUDHolder(UDCommentView.LUA_CLASS_NAME, UDCommentView.class, true, UDCommentView.methods),
                Register.newUDHolderWithLuaClass(UDInteractionNoticeCommentView.LUA_CLASS_NAME, UDInteractionNoticeCommentView.class, true),
                Register.newUDHolder(UDShimmeringLabel.LUA_CLASS_NAME, UDShimmeringLabel.class, true, UDShimmeringLabel.methods),
                //登陆注册相关bridge
                Register.newUDHolderWithLuaClass(UDBindPhoneManager.LUA_CLASS_NAME, UDBindPhoneManager.class, true),
                //登陆注册bridge
                Register.newUDHolderWithLuaClass(UDAccountRequestManager.LUA_CLASS_NAME, UDAccountRequestManager.class, true),
                Register.newUDHolderWithLuaClass(UDChangePasswordManager.LUA_CLASS_NAME, UDChangePasswordManager.class, true),

                Register.newUDHolderWithLuaClass(UDRegisterRequestManager.LUA_CLASS_NAME, UDRegisterRequestManager.class, true),
                Register.newUDHolderWithLuaClass(UDDateTimePickerController.LUA_CLASS_NAME, UDDateTimePickerController.class, true),
                //文字模糊Label
                Register.newUDHolder(UDBlurLabelView.LUA_CLASS_NAME, UDBlurLabelView.class, true, UDBlurLabelView.methods),
                Register.newUDHolder(MDEmoteEditTextView.LUA_CLASS_NAME, MDEmoteEditTextView.class, true, MDEmoteEditTextView.methods),
                Register.newUDHolder(UDNoteChatEmojiView.LUA_CLASS_NAME, UDNoteChatEmojiView.class, true, UDNoteChatEmojiView.methods),
                Register.newUDHolderWithLuaClass(UDNotePublish.LUA_CLASS_NAME, UDNotePublish.class, true),
                Register.newUDHolderWithLuaClass(MomoHomeHelper.LUA_CLASS_NAME, MomoHomeHelper.class, true),
                Register.newUDHolder(UDCustomSwipeRefreshLayout.LUA_CLASS_NAME, UDCustomSwipeRefreshLayout.class, true, UDCustomSwipeRefreshLayout.methods),
                Register.newUDHolder(UDVerticalViewPagerAdapter.LUA_CLASS_NAME, UDVerticalViewPagerAdapter.class, true, UDVerticalViewPagerAdapter.methods),
                Register.newUDHolder(UDVerticalViewPager.LUA_CLASS_NAME, UDVerticalViewPager.class, true, UDVerticalViewPager.methods),
                Register.newUDHolder(UDGlobalMediaView.LUA_CLASS_NAME, UDGlobalMediaView.class, true, UDGlobalMediaView.methods),
                Register.newUDHolder(UDProgressBar.LUA_CLASS_NAME, UDProgressBar.class, true, UDProgressBar.methods),
                Register.newUDHolder(UDVideoSeekBar.LUA_CLASS_NAME, UDVideoSeekBar.class, true, UDVideoSeekBar.methods),
                Register.newUDHolder(UDHorizontalScrollLabelView.LUA_CLASS_NAME, UDHorizontalScrollLabelView.class, true, UDHorizontalScrollLabelView.methods),
                Register.newUDHolder(UDVerticalScrollLabelView.LUA_CLASS_NAME, UDVerticalScrollLabelView.class, true, UDVerticalScrollLabelView.methods),
                Register.newUDHolder(UDProfileFlexBoxLabelListView.LUA_CLASS_NAME, UDProfileFlexBoxLabelListView.class, true, UDProfileFlexBoxLabelListView.methods),
                Register.newUDHolder(UDStickyRefreshCoordinatorLayout.LUA_CLASS_NAME, UDStickyRefreshCoordinatorLayout.class, true, UDStickyRefreshCoordinatorLayout.methods),
                Register.newUDHolderWithLuaClass(UDUniverseOpenLibraryLuaHelp.LUA_CLASS_NAME, UDUniverseOpenLibraryLuaHelp.class, true),
                Register.newUDHolder(UDAudioBgView.LUA_CLASS_NAME, UDAudioBgView.class, true, UDAudioBgView.methods),
                Register.newUDHolder(UDVideoGiftView.LUA_CLASS_NAME, UDVideoGiftView.class, true, UDVideoGiftView.methods),
        };
    }

    /**
     * 注册lua静态类，主要是工具类
     * <p>
     * 两种方式：
     * 第一种，类中含有{@link com.immomo.mls.annotation.LuaClass}
     * 使用 {@link Register#newSHolderWithLuaClass(String, Class)}
     * <p>
     * 第二种
     * 使用 {@link Register#newSHolder(String, Class, String...)}
     * <p>
     * 若工具类中有状态，在虚拟机销毁时需要清除，则注册到 {@link #registerSingleInstance}
     */
    private static Register.SHolder[] registerLT() {
        return new Register.SHolder[]{
                Register.newSHolderWithLuaClass(LTLocationManager.LUA_CLASS_NAME, LTLocationManager.class),
                Register.newSHolderWithLuaClass(LTVChatLocationManager.LUA_CLASS_NAME, LTVChatLocationManager.class),
                Register.newSHolderWithLuaClass(UDFeedManager.LUA_CLASS_NAME, UDFeedManager.class),
                Register.newSHolderWithLuaClass(UDVideoLuaManager.LUA_CLASS_NAME, UDVideoLuaManager.class),
                Register.newSHolderWithLuaClass(UDVipVisitorManager.LUA_CLASS_NAME, UDVipVisitorManager.class),
                Register.newSHolderWithLuaClass(UDScrollHelper.LUA_CLASS_NAME, UDScrollHelper.class),


                Register.newSHolderWithLuaClass(LTMMUserProfile.LUA_CLASS_NAME, LTMMUserProfile.class),
                Register.newSHolderWithLuaClass(LTNearbyViewModel.LUA_CLASS_NAME, LTNearbyViewModel.class),
                Register.newSHolderWithLuaClass(LTFriendNoticeManager.LUA_CLASS_NAME, LTFriendNoticeManager.class),
                Register.newSHolderWithLuaClass(LTSyncMsgManager.LUA_CLASS_NAME, LTSyncMsgManager.class),
                Register.newSHolderWithLuaClass(LTInteractionNoticeManager.LUA_CLASS_NAME, LTInteractionNoticeManager.class),
                Register.newSHolderWithLuaClass(LTUniverseManager.LUA_CLASS_NAME, LTUniverseManager.class),
                Register.newSHolderWithLuaClass(LTHalfAcManager.LUA_CLASS_NAME, LTHalfAcManager.class),
                Register.newSHolderWithLuaClass(LTSecurityManager.LUA_CLASS_NAME, LTSecurityManager.class),
                Register.newSHolderWithLuaClass(LTPrivacyManager.LUA_CLASS_NAME, LTPrivacyManager.class),
                Register.newSHolderWithLuaClass(LTAccountCacheManager.LUA_CLASS_NAME, LTAccountCacheManager.class),
                Register.newSHolderWithLuaClass(LTQuickLoginManager.LUA_CLASS_NAME, LTQuickLoginManager.class),
                Register.newSHolderWithLuaClass(LTLoginRegisterLog.LUA_CLASS_NAME, LTLoginRegisterLog.class),
                Register.newSHolderWithLuaClass(LTLoginTool.LUA_CLASS_NAME, LTLoginTool.class),
                Register.newSHolderWithLuaClass(LTLoginSafeManager.LUA_CLASS_NAME, LTLoginSafeManager.class),
                Register.newSHolderWithLuaClass(LTFriendService.LUA_CLASS_NAME, LTFriendService.class),
                Register.newSHolderWithLuaClass(LTQQContactManager.LUA_CLASS_NAME, LTQQContactManager.class),
                Register.newSHolderWithLuaClass(LTWxContactManager.LUA_CLASS_NAME, LTWxContactManager.class),
                Register.newSHolderWithLuaClass(LTNoteManager.LUA_CLASS_NAME, LTNoteManager.class),
                Register.newSHolderWithLuaClass(LTGroupSearchABHelper.LUA_CLASS_NAME, LTGroupSearchABHelper.class),
                Register.newSHolderWithLuaClass(UDContactsManager.LUA_CLASS_NAME, UDContactsManager.class),
                Register.newSHolderWithLuaClass(LTSettingManager.LUA_CLASS_NAME, LTSettingManager.class),
                Register.newSHolderWithLuaClass(UDContactsManager.LUA_CLASS_NAME, UDContactsManager.class),
                Register.newSHolderWithLuaClass(UDFriendManager.LUA_CLASS_NAME, UDFriendManager.class),
                Register.newSHolderWithLuaClass(UDGotoManager.LUA_CLASS_NAME, UDGotoManager.class),
                Register.newSHolderWithLuaClass(LTABTestManager.LUA_CLASS_NAME, LTABTestManager.class),
                Register.newSHolderWithLuaClass(LTMoreFrameHelper.LUA_CLASS_NAME, LTMoreFrameHelper.class),
                Register.newSHolderWithLuaClass(UDVideoManager.LUA_CLASS_NAME, UDVideoManager.class),
                Register.newSHolderWithLuaClass(UDProfileHelper.LUA_CLASS_NAME, UDProfileHelper.class),
                Register.newSHolderWithLuaClass(LTUniFmChecker.LUA_CLASS_NAME, LTUniFmChecker.class),
                Register.newSHolderWithLuaClass(UDQiaoQiaoLuaManager.LUA_CLASS_NAME, UDQiaoQiaoLuaManager.class),
                /// Android Studio模板，快速Bridge，下载地址：https://moji.wemomo.com/attach/5da032c337b3a.zip
                /// 使用方法：https://moji.wemomo.com/doc#/detail/90081
        };
    }

    /**
     * 注册lua 单例
     */
    private static MLSBuilder.SIHolder[] registerSingleInstance() {
        return new MLSBuilder.SIHolder[]{
                new MLSBuilder.SIHolder(SISharehandler.LUA_CLASS_NAME, SISharehandler.class),

                /// Android Studio模板，快速Bridge，下载地址：https://moji.wemomo.com/attach/5da032c337b3a.zip
                /// 使用方法：https://moji.wemomo.com/doc#/detail/90081
        };
    }

    /**
     * 注册lua object和java object自动转换方法
     *
     * @see com.immomo.mls.utils.convert.ConvertUtils#toLuaValue(Globals, Object)
     * @see com.immomo.mls.utils.convert.ConvertUtils#toNativeValue(LuaValue)
     * @see com.immomo.mls.wrapper.Translator#registerJ2L(Class, ILuaValueGetter)
     * @see com.immomo.mls.wrapper.Translator#registerL2J(Class, IJavaObjectGetter)
     */
    private static MLSBuilder.CHolder[] registerCovert() {
        return new MLSBuilder.CHolder[]{
        };
    }

    /**
     * 注册lua 枚举变量
     *
     * @return
     */
    private static Class[] registerLuaConstants() {
        return new Class[]{
                /// Android Studio模板，快速Bridge，下载地址：https://moji.wemomo.com/attach/5da032c337b3a.zip
                /// 使用方法：https://moji.wemomo.com/doc#/detail/90081
        };
    }
}
