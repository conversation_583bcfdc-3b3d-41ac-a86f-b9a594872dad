package com.immomo.momo.luaview.ud;

import com.immomo.mls.fun.ud.view.UDView;
import com.immomo.mls.fun.ud.view.UDViewGroup;
import com.immomo.momo.luaview.LuaWebView;

import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;


/**
 * 和lua交互的标签视图
 * Created by <PERSON><PERSON><PERSON> on 2018/9/25.
 */
@LuaApiUsed
public class UDWebView<V extends LuaWebView> extends UDView<V> {

    public static final String LUA_CLASS_NAME = "WebView";

    public static final String[] methods = {
            "loadUrl",
            "destroy",
            "viewAppear",
            "viewDisappear"
    };


    @LuaApiUsed
    public UDWebView(long L, LuaValue[] v) {
        super(L, v);
    }

    @Override
    protected V newView(LuaValue[] init) {
        return (V) new LuaWebView(getContext());
    }


    @LuaApiUsed
    public LuaValue[] loadUrl(LuaValue[] values) {
        getView().loadUrl(values[0].toJavaString());
        return null;
    }

    @LuaApiUsed
    public LuaValue[] destroy(LuaValue[] values) {
        getView().destroy();
        return null;
    }
    @LuaApiUsed
    public LuaValue[] viewAppear(LuaValue[] values) {
        getView().onResume();
        return null;
    }
    @LuaApiUsed
    public LuaValue[] viewDisappear(LuaValue[] values) {
        getView().onPause();
        return null;
    }

    @Override
    protected void __onLuaGc() {
        super.__onLuaGc();
        getView().destroy();
    }
}
