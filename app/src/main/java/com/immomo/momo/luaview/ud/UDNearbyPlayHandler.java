package com.immomo.momo.luaview.ud;

import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.agora.mr.LiveMonitor;
import com.immomo.momo.agora.mr.conflictHelper.VideoConflictConfig;
import com.immomo.momo.agora.mr.conflictHelper.VideoConflictNewHelper;

import org.luaj.vm2.Globals;
import org.luaj.vm2.LuaValue;

@LuaClass
public class UDNearbyPlayHandler {
    public static final String LUA_CLASS_NAME = "NearbyPlayHandler";

    public UDNearbyPlayHandler(Globals g, LuaValue[] init) {
    }

    @LuaBridge
    public boolean checkConflictWithAudio(boolean showToast) {
        if (LiveMonitor.isLiveShowing) {
            if (showToast) {
                Toaster.show("需要先退出直播间，才能使用该功能");
            }
            return true;
        } else {
            return VideoConflictNewHelper.conflictWith(VideoConflictConfig.BusinessType.COMMON, showToast);
        }
    }

    @LuaBridge
    public boolean isNearbyPlayPageDisplayed() {
        return false;
    }

}
