package com.immomo.momo.luaview.ud.sliceupload.api

import com.immomo.momo.luaview.ud.sliceupload.model.SliceUploadModel
import com.immomo.momo.luaview.ud.sliceupload.model.SliceUploadResult

/**
 * Created on 2019-11-30.
 * <AUTHOR>
 */
interface IUploadApi {
    fun upload(
        sliceUploadModel: SliceUploadModel,
        bytes: ByteArray,
        dataLength: Int,
        offset: Long,
        index: Int
    ): SliceUploadResult?
}