package com.immomo.momo.luaview.ud.sliceupload.api.impl

import com.immomo.http.FormFile
import com.immomo.momo.luaview.ud.sliceupload.api.IUploadApi
import com.immomo.momo.luaview.ud.sliceupload.model.SliceUploadModel
import com.immomo.momo.luaview.ud.sliceupload.model.SliceUploadResult
import com.immomo.momo.protocol.http.core.HttpClient
import com.immomo.momoenc.config.HttpConfigs
import org.json.JSONObject
import java.util.*

/**
 * Created on 2019-11-30.
 * <AUTHOR>
 */
class SimpleUploadApiImpl(uploadUrl: String) : HttpClient(), IUploadApi {

    private var uploadUrl: String = uploadUrl

    override fun upload(
        sliceUploadModel: SliceUploadModel,
        bytes: ByteArray,
        dataLength: Int,
        offset: Long,
        index: Int
    ): SliceUploadResult? {
        val params = HashMap<String, String?>()
        sliceUploadModel.customParams?.let { params.putAll(it) }


        params["offset"] = offset.toString()
        params["length"] = sliceUploadModel.targetFile.length().toString()
        params["index"] = index.toString()

        params["uuid"] = sliceUploadModel.targetFileUuid
        params["resumable"] = "1"


        val headers = HashMap<String, String>()
        headers[HttpConfigs.HEADEP_CHECK_HISTORY] = "0"

        val formFiles = arrayOf<FormFile>(
            FormFile(
                sliceUploadModel.targetFile.name,
                bytes,
                "fileblock",
                "application/octet-stream"
            )
        )
        val result = doPost(uploadUrl, params, formFiles, headers)

        var video: SliceUploadResult? = null
        if (offset + dataLength >= sliceUploadModel.targetFile.length()) {
            val json = JSONObject(result).getJSONObject("data")
            video = SliceUploadResult()
            video.fileName = json.optString("filename")
            video.extension = json.optString("extension")
        }
        return video

    }
}