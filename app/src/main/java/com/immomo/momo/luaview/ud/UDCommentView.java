package com.immomo.momo.luaview.ud;


import android.util.ArrayMap;
import android.view.View;

import com.cosmos.mdlog.MDLog;
import com.immomo.framework.utils.UIUtils;
import com.immomo.http.exception.HttpBaseException;
import com.immomo.mls.fun.ud.UDMap;
import com.immomo.mls.fun.ud.view.UDViewGroup;
import com.immomo.mls.utils.convert.ConvertUtils;
import com.immomo.momo.LogTag;
import com.immomo.momo.feed.BaseCommentHandler;
import com.immomo.momo.feed.CommonFeedCommentHandler;
import com.immomo.momo.luaview.LuaCommentLayout;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.sessionnotice.bean.FeedCommentNotice;
import com.immomo.momo.sessionnotice.bean.FeedCommentNoticeModel;
import com.immomo.momo.sessionnotice.bean.NoticeMsg;
import com.immomo.momo.util.FoldScreenUtil;
import com.immomo.momo.util.StringUtils;

import org.luaj.vm2.LuaBoolean;
import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaNumber;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import java.util.Date;
import java.util.Map;

/**
 * Created by li.mengnan on 2020-09-21.
 * 评论的输入框视图
 */
@LuaApiUsed
public class UDCommentView<V extends LuaCommentLayout> extends UDViewGroup<V> {

    public static final String LUA_CLASS_NAME = "CommentView";
    public static final String[] methods = {
            "showPanel",
            "hidePanel",
            "resume",
            "back",
            "isPanelShowing",
            "setSendCommentCallBack",
            "setWillShowCallback",
            "pause",
            "destory",
            "setEmotionFooterAndSize",
            "setFooter",
            "showCommentLayoutOnly",
            "setVideoMode",
            "setCommentConfig",
            "atShowCommentEditLayout",
            "setKeyboardCallback",
            "setSoftInputMode",
            "setFeedType",
            "hideVideoCommentView",
            "clearInput"
    };
    private LuaCommentLayout commentLayout;
    LuaFunction luaCommentCallback;
    LuaFunction luaWillShowCallback;
    LuaFunction luaKeyboardCallback;
    private BaseCommentHandler.OnCommentListener<Object, Object> mCommentListener;

    private BaseCommentHandler.OnCommentListener<Object, Object> getCommentListener() {
        if (mCommentListener == null) {
            mCommentListener = new CommonFeedCommentHandler.OnCommentListener<Object, Object>() {
                @Override
                public void onStart() {

                }

                @Override
                public void onSuccess(Object comment, Object result) {
                    if (luaCommentCallback != null && globals != null && !globals.isDestroyed()) {
                        LuaValue luaValue = ConvertUtils.toLuaValue(globals, result);
                        if (luaValue != null)
                            //when global is destroy this value maybe null
                            luaCommentCallback.fastInvoke(luaValue);
                    }
                }

                @Override
                public void onFailed(Exception e) {
                    if (luaCommentCallback != null && globals != null && !globals.isDestroyed()) {
                        Map<String, Object> result = new ArrayMap<>(2);
                        result.put("errcode", e instanceof HttpBaseException ? ((HttpBaseException) e).errorCode : -1);
                        result.put("errmsg", e.getMessage());
                        if (e instanceof HttpBaseException && ((HttpBaseException) e).httpResultString != null) {
                            result.put("data", ((HttpBaseException) e).httpResultString);
                        }
                        LuaValue luaValue = ConvertUtils.toLuaValue(globals, result);
                        if (luaValue != null)
                            luaCommentCallback.fastInvoke(luaValue);
                    }
                }
            };
        }
        return mCommentListener;
    }

    @LuaApiUsed
    public UDCommentView(long l, LuaValue[] v) {
        super(l, v);
    }

    @Override
    protected V newView(LuaValue[] init) {
        commentLayout = new LuaCommentLayout(getContext());
        return (V) commentLayout;
    }


    @LuaApiUsed
    protected LuaValue[] setCommentConfig(LuaValue[] notice) {
        Map noticeData = ((UDMap) notice[0].toUserdata()).getMap();
        NoticeMsg noticeMsg = getNoticeMsg(noticeData);
        commentLayout.setCommentConfig(noticeMsg);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] showPanel(LuaValue[] notice) {
        Map noticeData = ((UDMap) notice[0].toUserdata()).getMap();
        NoticeMsg noticeMsg = getNoticeMsg(noticeData);
        commentLayout.showPanel(noticeMsg);
        if (commentLayout != null && commentLayout.getCommentView() != null && commentLayout.getCommentView().commentLayout != null) {
            commentLayout.getCommentView().commentLayout.postDelayed(new Runnable() {
                @Override
                public void run() {
                    setLuaWillShowCallback();
                }
            }, 30);

            if (FoldScreenUtil.INSTANCE.isWideScreen()) {
                commentLayout.getCommentView().setLuaCommentWillShowListener(() -> {
                    setLuaWillShowCallback();
                });
            }
        }
        return null;
    }

    private void setLuaWillShowCallback() {
        if (luaWillShowCallback != null) {
            int[] locationOnScreen = UIUtils.getLocationOnScreen((View) commentLayout.getParent());
            int[] commentViewOnScreen = UIUtils.getLocationOnScreen(commentLayout.getCommentView().commentLayout);
            int offset = commentViewOnScreen[1] - locationOnScreen[1];
            luaWillShowCallback.invoke(LuaNumber.rNumber(UIUtils.getDips(offset)));
        }
    }

    @LuaApiUsed
    public LuaValue[] hidePanel(LuaValue[] values) {
        commentLayout.hidePanel();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] showCommentLayoutOnly(LuaValue[] values) {
        commentLayout.showCommentLayoutOnly();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setVideoMode(LuaValue[] values) {
        commentLayout.setVideoMode();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setFeedType(LuaValue[] values) {
        commentLayout.setFeedType();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] clearInput(LuaValue[] values) {
        commentLayout.clearInput();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setEmotionFooterAndSize(LuaValue[] values) {
        int bottomHeight = values[0].toInt();
        commentLayout.setEmotionFooterAndSize(bottomHeight);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] atShowCommentEditLayout(LuaValue[] values) {
        commentLayout.getCommentView().atShowCommentEditLayout();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setSendCommentCallBack(LuaValue[] values) {
        if (luaCommentCallback != null) {
            luaCommentCallback.destroy();
        }
        luaCommentCallback = values[0].isFunction() ? values[0].toLuaFunction() : null;
        if (luaCommentCallback != null) {
            commentLayout.setSendCommentCallBack(getCommentListener());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setWillShowCallback(LuaValue[] values) {
        if (luaWillShowCallback != null) {
            luaWillShowCallback.destroy();
        }
        luaWillShowCallback = values[0].isFunction() ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] resume(LuaValue[] values) {
        commentLayout.resume();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] back(LuaValue[] values) {
        commentLayout.back();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] isPanelShowing(LuaValue[] values) {
        return LuaValue.rBoolean(commentLayout.isPanelShowing());
    }

    @LuaApiUsed
    public LuaValue[] pause(LuaValue[] values) {
        commentLayout.pause();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] destory(LuaValue[] values) {
        commentLayout.destory();
        commentLayout = null;
        luaCommentCallback = null;
        luaWillShowCallback = null;
        luaKeyboardCallback = null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setKeyboardCallback(LuaValue[] values) {
        if (luaKeyboardCallback != null) {
            luaKeyboardCallback.destroy();
        }
        luaKeyboardCallback = values[0].isFunction()? values[0].toLuaFunction() : null;
        if (luaKeyboardCallback != null) {
            commentLayout.setCommentKeyboardListener((keyboardHeight, keyboardOpen) -> {
                luaKeyboardCallback.invoke(LuaValue.varargsOf(LuaNumber.valueOf(keyboardHeight), LuaBoolean.valueOf(keyboardOpen)));
            });
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setSoftInputMode(LuaValue[] values) {
        if (commentLayout != null) {
            commentLayout.setSoftInputMode();
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] hideVideoCommentView(LuaValue[] values) {
        if (commentLayout != null) {
            commentLayout.hideVideoCommentLayout();
        }
        return null;
    }


    /**
     * 将lua层数据解析并封装为端侧数据模型
     *
     * @param noticeData
     * @return
     */
    private NoticeMsg getNoticeMsg(Map noticeData) {
        //互动通知消息体
        NoticeMsg noticeMsg = new NoticeMsg();
        Integer noticeType = (Integer) noticeData.get("noticeType");
        if (noticeType == null) {
            return null;
        }
        if (noticeData.containsKey("page_source_type")) {
            noticeMsg.microVideoSourceType = noticeData.get("page_source_type").toString();
        }
        if (noticeData.containsKey("page_source_extra")) {
            noticeMsg.pageFrom = noticeData.get("page_source_extra").toString();
        }
        noticeMsg.id = noticeData.get("noticeId").toString();
        // 评论类型1、对动态评论，2、回复评论
        if (noticeData.containsKey("srcType"))
            noticeMsg.srcType = (Integer) noticeData.get("srcType");
        if (noticeData.containsKey("hint"))
            noticeMsg.hint = noticeData.get("hint") != null ? noticeData.get("hint").toString() : "";
        if (noticeData.containsKey("privateHint"))
            noticeMsg.privateHint = noticeData.get("privateHint") != null ? noticeData.get("privateHint").toString() : "";
        if (noticeData.containsKey("sync_group"))
            noticeMsg.synGroup = noticeData.get("sync_group") != null ? noticeData.get("sync_group").toString() : "";
        //为动态类型消息
        if (noticeType == NoticeMsg.TYPE_FEED_COMMENT) {
            noticeMsg.commentId = noticeData.get("commentId").toString();
            noticeMsg.setType(NoticeMsg.TYPE_FEED_COMMENT);

            //消息内容，根据类型可分为动态评论与圈子评论消息
            FeedCommentNotice commentNotice = new FeedCommentNotice();
            noticeMsg.noticeContent = commentNotice;
            //评论所属的动态id
            commentNotice.feedId = noticeData.get("feedId").toString();
            //评论发布者id
            commentNotice.ownerUserId = noticeData.get("commentOwnerId").toString();
            //评论的模式，是否为私密模式
            commentNotice.privateComment = noticeData.get("privateComment").toString();
            //评论发送目标者id
            commentNotice.toUserid = noticeData.get("toUserid").toString();

            //动态
            commentNotice.feed = new FeedCommentNoticeModel(
                    "",
                    noticeData.get("feedOwnerId").toString(),
                    null,
                    new Date(),
                    null
            );
        } //圈子业务已经下线，else不存在
        try {
            if (noticeData.containsKey("extendsData")) {
                String anExtends = (String) noticeData.get("extendsData");
                if (StringUtils.isNotBlank(anExtends)) {
                    noticeMsg.extandsData = anExtends;
                }
            }
        } catch (Throwable throwable) {
            MDLog.printErrStackTrace(LogTag.COMMON, throwable);
        }

        //发送的目标用户
        User user = new User();
        noticeMsg.noticeContent.sendUser = user;
        //用户名
        user.name = noticeData.get("name").toString();
        //备注名
        user.remarkName = noticeData.get("remarkName").toString();
        //关系
        user.relation = noticeData.get("relation").toString();

        return noticeMsg;
    }

}
