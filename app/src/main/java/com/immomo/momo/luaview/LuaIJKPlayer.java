package com.immomo.momo.luaview;

import android.net.Uri;

import com.cosmos.mdlog.MDLog;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.mmutil.StringUtils;
import com.immomo.momo.LogTag;
import com.immomo.momo.feed.MicroVideoPlayLogger;
import com.immomo.momo.feed.player.TextureReusableIJKPlayer;
import com.immomo.momo.feed.player.preload.IJKMediaPreLoader;

import tv.danmaku.ijk.media.momoplayer.IMediaPlayer;
import tv.danmaku.ijk.media.momoplayer.IjkMediaMeta;
import tv.danmaku.ijk.media.momoplayer.pullDetect;

/**
 * Created by zhang.ke on 2019/3/9.
 */

public class LuaIJKPlayer extends TextureReusableIJKPlayer {
    private boolean useSmartCache;

    public LuaIJKPlayer(){
        useSmartCache = KV.getUserBool(SPKeys.User.MicroVideo.KEY_SMART_CACHE, true);
    }

    @Override
    public void onCompletion(IMediaPlayer mp) {
        if (completionListener != null) {
            completionListener.onCompletion();
        }
        super.onCompletion(mp);
        if (isLoopPlay && loopListener != null) {
            loopListener.onLoopStart();
        }
    }

    @Override
    public boolean onInfo(IMediaPlayer mp, int what, int extra) {
        if (IMediaPlayer.MEDIA_INFO_VIDEO_RENDERING_START == what){
            long[] mediaParams = getMediaParams();
            if (IJKMediaPreLoader.getInstance().getPreloadMethod() == IJKMediaPreLoader.PRELOAD_METHOD_PROXY) {
                //代理模式更新cache命中数据
                long isCacheExist = 0;
                if (currentUri != null) {
                    if (StringUtils.notEmpty(currentUri.getPath()) && cacheMap.containsKey(currentUri.getPath())) {
                        isCacheExist = cacheMap.get(currentUri.getPath()).longValue();
                        cacheMap.remove(currentUri.getPath());
                        MDLog.d("LuaIJKPlayer", String.format("isCacheExist:%d, path:%s, Map size:%d", isCacheExist, currentUri.getPath(), cacheMap.size()));
                    } else {
                        MDLog.d("LuaIJKPlayer", String.format("path:%s is not exit, cacheMap size:%d", currentUri.getPath(), cacheMap.size()));
                    }
                }
                mediaParams[7] = isCacheExist;
            }

            MicroVideoPlayLogger.getInstance().logPrepareEnd(getCurrentPosition(), mediaParams);
        }
        return super.onInfo(mp, what, extra);
    }

    public long[] getMediaParams() {
        long ret[] = new long[17];
        if (validPlayer()) {
            try {
                long audioBitrate = player.getMediaInfo().mMeta.mAudioStream.mBitrate;
                ret[0] = audioBitrate;
            } catch (Exception ignore) {
            }
            try {
                IjkMediaMeta videoMeta = player.getMediaInfo().mMeta;
                long videoBitrate = videoMeta.mVideoStream.mBitrate;
                ret[1] = videoBitrate;
                long width = videoMeta.mVideoStream.mWidth;
                ret[2] = width;
                long height = videoMeta.mVideoStream.mHeight;
                ret[3] = height;
                long fileSize = videoMeta.getInt(IjkMediaMeta.IJKM_KEY_DATA_SIZE);
                ret[4] = fileSize;
                ret[7] = videoMeta.isCached;
                ret[8] = currentMediaCodec ? 1 : 0;
                ret[9] = h265Playing ? 1 : 0;
                ret[10] = useSmartCache ? 1 : 0;
                ret[11] = player.getOpenInputCost();
                ret[12] = player.getFindStreamInfoCost();
                ret[13] = player.getFirstVideoPacketCost();
                if (!currentMediaCodec) {
                    ret[14] = player.getVideoFFPlayCost();
                } else {
                    ret[14] = player.getVideoMediaCodecCost();
                }
                ret[15] = player.getVideoDecodeCost();
                ret[16] = player.getFirstVideoRenderCost();
            } catch (Exception ignore) {
            }
            try {
                pullDetect[] detects = player.getPullDetectStatus();
                if (null != detects && detects.length > 0) {
                    ret[5] = detects[0].timestamp;
                    ret[6] = detects[0].firstPacketTime + detects[0].timestamp;
                }
            } catch (Exception e) {
                MDLog.printErrStackTrace(LogTag.Player.IJK, e);
            }
        }
        return ret;
    }

    private LoopListener loopListener;
    private CompletionListener completionListener;

    public void setLoopListener(LoopListener loopListener) {
        this.loopListener = loopListener;
    }

    public void setCompletionListener(CompletionListener completionListener) {
        this.completionListener = completionListener;
    }

    public boolean isLoopPlay() {
        return isLoopPlay;
    }

    public interface LoopListener {
        void onLoopStart();
    }

    public interface CompletionListener {
        void onCompletion();
    }

    @Override
    public void release() {
        final Uri current = getCurrentUri();
        super.release();
        if (current != null)
            playPositionMap.remove(current);
    }
}
