package com.immomo.momo.luaview.media;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.view.View;

import com.alibaba.fastjson.JSONObject;
import com.immomo.android.module.feedlist.domain.model.style.AbstractFeedModel;
import com.immomo.android.module.feedlist.domain.model.style.common.BasicFeedModelUtilsKt;
import com.immomo.android.module.feedlist.domain.model.style.common.MicroVideoFeedLuaModel;
import com.immomo.android.module.feedlist.domain.model.style.common.MicroVideoFeedModel;
import com.immomo.android.router.momo.business.pay.FastChargeParam;
import com.immomo.android.router.momo.business.pay.FastReChargeRouter;
import com.immomo.android.router.momo.business.pay.PayRouter;
import com.immomo.android.router.momo.util.VideoConflictRouter;
import com.immomo.android.router.pay.IPayCallback;
import com.immomo.android.router.pay.model.PayResult;
import com.immomo.framework.SPKeys;
import com.immomo.framework.base.BaseActivity;
import com.immomo.framework.model.businessmodel.microvideo.IMicroVideoRepository;
import com.immomo.framework.rxjava.interactor.CommonSubscriber;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.utils.TimeUtils;
import com.immomo.mls.LuaViewManager;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmutil.app.AppContext;
import com.immomo.mmutil.task.MMThreadExecutors;
import com.immomo.momo.apt.FeedAppConfigV2Getter;
import com.immomo.momo.businessmodel.statistics.PageStepHelper;
import com.immomo.momo.exception.HttpException508;
import com.immomo.momo.feed.FeedStepHelper;
import com.immomo.momo.feed.MicroVideoPlayLogger;
import com.immomo.momo.feed.player.performance.H265Utils;
import com.immomo.momo.feed.player.preload.IJKMediaPreLoader;
import com.immomo.momo.feed.util.VideoPlaySelectUtil;
import com.immomo.momo.innergoto.matcher.MicroVideoMatcher;
import com.immomo.momo.microvideo.interactor.VideoPayUnlockUseCase;
import com.immomo.momo.moment.utils.VolumeHelper;
import com.immomo.momo.mvp.common.model.ModelManager;
import com.immomo.momo.util.GsonUtils;
import com.immomo.momo.util.MicroVideoCache;
import com.immomo.momo.util.StringUtils;

import org.luaj.vm2.Globals;

import java.util.List;
import java.util.concurrent.TimeUnit;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import info.xudshen.android.appasm.AppAsm;
import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * author: hongming.wei
 * data: 2023/2/15
 */
@LuaClass(isStatic = true)
public class UDVideoManager {

    public static final String LUA_CLASS_NAME = "VideoManager";
    private static VideoPayUnlockUseCase request;
    private static LVCallback mCallback;
    private static Disposable disposable;
    private static int currentVolume = -1;

    @LuaBridge
    public static void preloadModelVideo(List<String> param) {
        if (param != null && param.size() > 0) {
            IJKMediaPreLoader.getInstance()
                    .preloadUrlVideo(param);
        }
    }

    @LuaBridge
    public static String obtainFeedModel(String feedId) {
        String feedModel = (String) MicroVideoCache.get(MicroVideoCache.LuaSingleMicroVideo);
        if (StringUtils.isNotEmpty(feedModel)) {
            if (StringUtils.isEmpty(feedId)) {
                return feedModel;
            }
            MicroVideoFeedModel videoFeedModel = FeedVideoThemeAdapter.INSTANCE.parseFeedStringToModel(feedModel);
            if (videoFeedModel != null && StringUtils.equalsNonNull(feedId, videoFeedModel.getFeedId())) {
                return feedModel;
            }
        }
        AbstractFeedModel<?> feed = (AbstractFeedModel<?>) MicroVideoCache.get(MicroVideoCache.SingleMicroVideo);
        if (feed instanceof MicroVideoFeedModel && StringUtils.equalsNonNull(feed.getFeedId(), feedId)) {
            MicroVideoFeedLuaModel feedLuaModel = ((MicroVideoFeedModel) feed).toLuaModel();
            return JSONObject.toJSONString(feedLuaModel);
        } else {
            feed = BasicFeedModelUtilsKt.generateEmptyBasicFeedModel(feedId);
        }
        MicroVideoFeedLuaModel videoFeedLuaModel = ((MicroVideoFeedModel) feed).toLuaModel();
        return JSONObject.toJSONString(videoFeedLuaModel);
    }

    @LuaBridge
    public static void saveFeedModel(String feedModel, String feedId) {
        if (StringUtils.isNotEmpty(feedModel)) {
            MicroVideoCache.save(MicroVideoCache.LuaSingleMicroVideo, feedModel);
        }
    }

    @LuaBridge
    public static boolean isVideoConflict(){
        VideoConflictRouter router = AppAsm.getRouter(VideoConflictRouter.class);
        return router.isRunning();
    }

    @LuaBridge
    public static String obtainVideoIsH265() {
        return H265Utils.isSupportH265() ? "1" : "0";
    }

    @LuaBridge
    public static String obtainVideoH265Level() {
        return String.valueOf(H265Utils.isSupportH265Level());
    }

    @LuaBridge
    public static void payVideoRequest(String feedId, long price, Globals globals, LVCallback callback) {
        if (request == null) {
            request = new VideoPayUnlockUseCase(ModelManager.getModel(IMicroVideoRepository.class));
        }
        BaseActivity context = (BaseActivity) ((LuaViewManager) globals.getJavaUserdata()).context;

        request.execute(new CommonSubscriber<String>() {
            @Override
            public void onNext(String s) {
                super.onNext(s);
                callback.call(true);
            }

            @Override
            public void onError(@Nullable Throwable exception) {
                if (exception instanceof HttpException508) {
                    FastChargeParam param = new FastChargeParam.Builder(context, 16777, "short-video")
                            .needCoin(price)
                            .showPaySuccess(true).build();
                    AppAsm.getRouter(PayRouter.class).startFastRechargeActivity(param);
                } else {
                    super.onError(exception);
                }
                callback.call(false);
            }
        }, feedId);
    }

    @LuaBridge
    public static void requestReadLook(String params) {
        MediaVideoReadLookModel lookModel = GsonUtils.g().fromJson(params, MediaVideoReadLookModel.class);
        if (lookModel != null) {
            MicroVideoPlayLogger.getInstance().logVideoPlayed(
                    lookModel.getForwardFeedId(),
                    lookModel.isAutoPlay(),
                    lookModel.getMediaVideoSource(),
                    lookModel.getVideoEventId(),
                    lookModel.getVideoCurrentIndex(),
                    lookModel.getVideoEventId(),
                    lookModel.getLogMap(),
                    lookModel.getBusinesstype(),
                    lookModel.getSecondType(),
                    lookModel.getVideoTagDesc(),
                    lookModel.getParams());
        }
    }

    @LuaBridge
    public static String microVideoSource(Globals globals, String extraJumpType) {
        //        if (StringUtils.equalsNonNull(extraJumpType, "followVideoFeed.lua")) {
        //            new MainTabVideoConfig(MicroVideoJumpType.FOLLOW_TAB_LUA_VIDEO_FEED);
        //        }

        BaseActivity context = (BaseActivity) ((LuaViewManager) globals.getJavaUserdata()).context;
        return MicroVideoMatcher.buildApiMicroVideoSource(getMicroVideoSource(), FeedStepHelper.INSTANCE.getFeedUpStepConfig().getLogSource(), getWebSource(context));
    }

    @LuaBridge
    public static String getLogSource() {
        return PageStepHelper.INSTANCE.getFeedUpStepConfig().getLogSource();
    }

    @LuaBridge
    public static int getMediaVideoRequestRed() {
        return FeedAppConfigV2Getter.get().mediaVideoRecommendWhichFrame();
    }

    @LuaBridge
    public static boolean isVideoPayTest() {
        return VideoPlaySelectUtil.INSTANCE.isTestA();
    }

    @LuaBridge
    public static void startPayActivity(int price, Globals globals, LVCallback callback) {
        BaseActivity context = (BaseActivity) ((LuaViewManager) globals.getJavaUserdata()).context;
        if (mCallback == null) {
            registerBroadcast(context);
        }
        mCallback = callback;
        FastChargeParam param = new FastChargeParam.Builder(context, 16779, "short-video")
                .needCoin(price)
                .showPaySuccess(true).build();
        AppAsm.getRouter(PayRouter.class).startFastRechargeActivity(param);
    }

    @LuaBridge
    public static void startFastPayActivity(String reqJson, Globals globals, LVCallback callback) {
        BaseActivity context = (BaseActivity) ((LuaViewManager) globals.getJavaUserdata()).context;
        AppAsm.getRouter(PayRouter.class).startPayActivity(context, new IPayCallback() {
            @Override
            public void onActivityResult(@NonNull Activity activity, @Nullable PayResult payResult) {
                if (payResult != null && payResult.isSuccess()) {
                    callback.call(true);
                }
            }
        }, reqJson, false);
    }

    private static void registerBroadcast(Context context) {
        AppAsm.getRouter(FastReChargeRouter.class).registerBroadcast(context, new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (intent == null) {
                    return;
                }
                if (intent.getAction() == "action_fast_recharge_success" && mCallback != null) {
                    mCallback.call(true);
                }
            }
        });
    }

    @LuaBridge
    public static void saveMediaVideoFollowTime(String owner) {
        if (StringUtils.isNotEmpty(owner)) {
            KV.saveUserValue(SPKeys.User.MicroVideo.KEY_MEDIA_VIDEO_FOLLOW_TIME + owner, System.currentTimeMillis());
        }
    }

    @LuaBridge
    public static void saveMediaVideoFollowUser(String owner) {
        if (StringUtils.isNotEmpty(owner)) {
            KV.saveUserValue(SPKeys.User.MicroVideo.Key_MEDIA_VIDEO_FOLLOW_USER + owner, true);
        }
    }

    @LuaBridge
    public static boolean isCurrentTodayFollow(String owner) {
        long oldTime = getMediaVideoFollowTime(owner);
        return !TimeUtils.isCurrentToday(oldTime) && !getMediaVideoFollowUser(owner);
    }

    @LuaBridge
    public static boolean checkShowHotListHalfView() {
        return !KV.getUserBool(VideoPlaySelectUtil.KEY_VIDEO_SELECT_FIRST_SHOW, false);
    }

    @LuaBridge
    public static void saveShowHotListHalfView() {
        KV.saveUserValue(VideoPlaySelectUtil.KEY_VIDEO_SELECT_FIRST_SHOW, true);
    }

    @LuaBridge
    public static void adjustRaiseVoiceSystem(Globals globals) {
        BaseActivity context = (BaseActivity) ((LuaViewManager) globals.getJavaUserdata()).context;
        if (context == null) {
            return;
        }
        VolumeHelper volumeHelper = new VolumeHelper(AppContext.getContext());
        if (disposable != null) {
            disposable.dispose();
            disposable = null;
        }
        currentVolume = volumeHelper.get100CurrentVolume();
        disposable = Observable
                .intervalRange(1, currentVolume, 0, 50, TimeUnit.MILLISECONDS)
                .map(aLong -> aLong)
                .subscribeOn(Schedulers.from(MMThreadExecutors.INSTANCE.getUser()))
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(aLong -> {
                    volumeHelper.setVoice100(Math.toIntExact(aLong));
                }, throwable -> {
                }, () -> currentVolume = -1);
    }

    @LuaBridge
    public static void closeVoiceSystem() {
        closeDisposable();
        if (currentVolume > 0) {
            VolumeHelper volumeHelper = new VolumeHelper(AppContext.getContext());
            volumeHelper.setVoice100(Math.toIntExact(currentVolume));
        }
        currentVolume = -1;
    }

    /**
     * 获取当前音量
     * @param type music / ring
     * @return 音乐音量or铃声音量，0->100
     */
    @LuaBridge
    public static int getVolume100(String type) {
        VolumeHelper volumeHelper = new VolumeHelper(AppContext.getContext());
        switch (type) {
            case "music":
                volumeHelper.setAudioType(VolumeHelper.TYPE_MUSIC);
            case "ring":
                volumeHelper.setAudioType(VolumeHelper.TYPE_RING);
        }
        return volumeHelper.get100CurrentVolume();
    }

    @LuaBridge
    public static void showVideoMusicPopup(View view, Globals globals) {
        BaseActivity context = (BaseActivity) ((LuaViewManager) globals.getJavaUserdata()).context;
    }

    @LuaBridge
    public static void setVideoSeekBar() {
        MicroVideoPlayLogger.getInstance().setVideoSeekedByUser(true);
    }

    private static boolean getMediaVideoFollowUser(String owner) {
        if (StringUtils.isNotEmpty(owner)) {
            return KV.getUserBool(SPKeys.User.MicroVideo.Key_MEDIA_VIDEO_FOLLOW_USER + owner, false);
        }
        return false;
    }

    private static Long getMediaVideoFollowTime(String owner) {
        if (StringUtils.isNotEmpty(owner)) {
            return KV.getUserLong(SPKeys.User.MicroVideo.KEY_MEDIA_VIDEO_FOLLOW_TIME + owner, 0L);
        }
        return 0L;
    }

    private static String getMicroVideoSource() {
        return FeedStepHelper.INSTANCE.getFeedUpStepConfig().getMicroVideoSourceType();
    }

    private static String getWebSource(BaseActivity activity) {
        return getViewIntent(activity) == null ? "" : getViewIntent(activity).getStringExtra(BaseActivity.KEY_WEB_SOURCE);
    }

    private static Intent getViewIntent(BaseActivity activity) {
        return activity.getIntent();
    }

    private static void closeDisposable() {
        if (disposable != null) {
            disposable.dispose();
            disposable = null;
        }
    }
}
