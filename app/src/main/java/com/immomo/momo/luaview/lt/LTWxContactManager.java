package com.immomo.momo.luaview.lt;

import android.text.TextUtils;

import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.plugin.weixin.WeixinApi;
import com.immomo.momo.util.WebShareParams;

@LuaClass(isStatic = true)
public class LTWxContactManager {
    public static final String LUA_CLASS_NAME = "AddWxContactManager";
    public static final int SORT_TYPE_ADD_TIME = 2; // 添加时间排序
    public static WebShareParams params;

    private LTWxContactManager() {
    }

    public static void getWebShareParams(String title, String imgUrl, String desc, String gotoUrl){
        params = new WebShareParams();
        params.shareText = desc;
        params.shareUrl = gotoUrl;
        params.sharePicUrl = imgUrl;
        params.shareTitle = title;
    }


    @LuaBridge
    public static void shareToWexinFriend(String title, String imgUrl, String desc, String gotoUrl) {
        getWebShareParams(title, imgUrl, desc, gotoUrl);
        if(params.isShareToWXMiniProgram() && WeixinApi.getInstance().canShareMiniProgram()){
            // 是分享到微信小程序的
            WeixinApi.getInstance().shareMiniProgramToFriendSession(params);
        } else if (WeixinApi.getInstance().canShare()) {
            if (TextUtils.isEmpty(params.shareText)) {
                WeixinApi.getInstance().shareWebpageToFriendSessionWithUrl(params.shareUrl, params.shareUrl, params.sharePicUrl, params.shareTitle);
            } else {
                WeixinApi.getInstance().shareWebpageToFriendSessionWithUrl(params.shareUrl, params.shareText, params.sharePicUrl, params.shareTitle);
            }

        } else if (!WeixinApi.getInstance().isWXAppInstalled()) {
            Toaster.show("您还没有安装微信", Toaster.LENGTH_SHORT);
        } else {
            Toaster.show("您的微信不是最新版本", Toaster.LENGTH_SHORT);
        }
    }

    @LuaBridge
    public static void shareToWeixinQuan(String title, String imgUrl, String desc, String gotoUrl) {
        getWebShareParams(title, imgUrl, desc, gotoUrl);
        if (WeixinApi.getInstance().canShare()) {
            if (TextUtils.isEmpty(params.shareTitle)) {
                WeixinApi.getInstance().shareWebpageToTimelineWithUrl(params.shareUrl, params.shareUrl, params.sharePicUrl);
            } else {
                WeixinApi.getInstance().shareWebpageToTimelineWithUrl(params.shareUrl, params.shareTitle, params.sharePicUrl);
            }
        } else if (!WeixinApi.getInstance().isWXAppInstalled()) {
            Toaster.show("您还没有安装微信", Toaster.LENGTH_SHORT);
        } else {
            Toaster.show("您的微信不是最新版本", Toaster.LENGTH_SHORT);
        }
    }

}
