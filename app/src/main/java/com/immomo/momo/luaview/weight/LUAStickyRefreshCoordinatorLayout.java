package com.immomo.momo.luaview.weight;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.cosmos.mdlog.MDLog;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.immomo.framework.utils.UIUtils;
import com.immomo.momo.R;

public class LUAStickyRefreshCoordinatorLayout extends FrameLayout {

    public static final String TAG = "LUAStickyRefreshCoordinatorLayout";

    SwipeRefreshLayout refreshLayout;
    CoordinatorLayoutExtends mCoordinatorLayout;
    AppBarLayout mAppBarLayout;
    CollapsingToolbarLayout mCollapsingToolbarLayout;
    LinearLayout topContainer;
    LinearLayout stickyTabContainer;
    FrameLayout bottomContainer;

    private boolean isExpandAppBar = true;

    public LUAStickyRefreshCoordinatorLayout(@NonNull Context context) {
        super(context);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.lua_coordinator_stiky_refresh_layout, this);
        refreshLayout = findViewById(R.id.swipe_refresh_layout);
        mCoordinatorLayout = findViewById(R.id.coordinator_layout);
        mAppBarLayout = findViewById(R.id.appbar_layout);
        mCollapsingToolbarLayout = findViewById(R.id.lv_collapsing_toolbar_layout);
        topContainer = findViewById(R.id.top_container);
        stickyTabContainer = findViewById(R.id.sticky_tab_container);
        bottomContainer = findViewById(R.id.bottom_container);
        refreshLayout.setColorSchemeResources(R.color.colorAccent);
        refreshLayout.setProgressViewEndTarget(true, UIUtils.getPixels(64));
        mAppBarLayout.addOnOffsetChangedListener((AppBarLayout.BaseOnOffsetChangedListener) (appBarLayout, i) -> {
            isExpandAppBar = i >= -10;
            // MDLog.i(TAG, "addOnOffsetChangedListener=" + i);
            setRefreshEnable(isExpandAppBar);
        });
        mAppBarLayout.post(() -> {
            CoordinatorLayout.LayoutParams layoutParams = (CoordinatorLayout.LayoutParams) mAppBarLayout.getLayoutParams();
            AppBarLayout.Behavior behavior = (AppBarLayout.Behavior) layoutParams.getBehavior();
            if (behavior != null) {
                behavior.setDragCallback(new AppBarLayout.Behavior.DragCallback() {
                    @Override
                    public boolean canDrag(@NonNull AppBarLayout appBarLayout) {
                        return true;
                    }
                });
            }
        });
    }

    // 最下面View, eg: ViewPager
    public void addContentView(View contentView) {
        if (contentView != null) {
            mCoordinatorLayout.addView(contentView);
        }
    }

    // 中间View, eg: TabLayou, TabSegment 滑动到悬停位置
    public void addAppBarView(View appbarView) {
        if (appbarView != null) {
            topContainer.addView(appbarView);
        }
    }

    public void addStickyTabView(View stickyTabView) {
        if (stickyTabView != null) {
            stickyTabContainer.addView(stickyTabView);
        }
    }

    public CollapsingToolbarLayout getCollapsingToolbarLayout() {
        return mCollapsingToolbarLayout;
    }

    public AppBarLayout getAppBarLayout() {
        return mAppBarLayout;
    }

    public CoordinatorLayoutExtends getCoordinatorLayout() {
        return mCoordinatorLayout;
    }

    public SwipeRefreshLayout getRefreshLayout() {
        return refreshLayout;
    }

    public void setRefreshEnable(boolean isEnable) {
        if (refreshLayout != null) {
            boolean enabled = refreshLayout.isEnabled();
            if (enabled != isEnable) {
                if (!refreshLayout.isRefreshing()) {
                    if (isEnable && isExpandAppBar) {
                        refreshLayout.setEnabled(true);
                    } else {
                        refreshLayout.setEnabled(false);
                    }
                    MDLog.i(TAG, "isEnable=" + isEnable + "  isExpandAppBar=" + isExpandAppBar);
                }
            }
        }
    }

    public void setRefreshing(boolean isEnable) {
        if (refreshLayout != null) {
            refreshLayout.setRefreshing(isEnable);
            MDLog.i(TAG, "setRefreshing=" + isEnable);
        }
    }

    public boolean isExpandAppBar() {
        return isExpandAppBar;
    }
}
