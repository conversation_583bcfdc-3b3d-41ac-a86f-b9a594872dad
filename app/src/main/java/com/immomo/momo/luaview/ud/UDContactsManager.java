package com.immomo.momo.luaview.ud;

import android.content.Intent;

import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.view.recyclerview.helper.ItemModelFilter;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.contact.bean.FriendGroupV2;
import com.immomo.momo.mvp.contacts.listeners.RefreshFollowCountReceiver;
import com.immomo.momo.protocol.http.ContactApi;
import com.immomo.momo.protocol.http.UserApi;
import com.immomo.momo.router.ProfileRouter;
import com.immomo.momo.service.bean.user.ContactUser;
import com.immomo.momo.service.user.UserService;
import com.immomo.momo.setting.BasicUserInfoUtil;
import com.immomo.momo.util.StringUtils;
import com.immomo.push.util.AppContext;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import info.xudshen.android.appasm.AppAsm;

@LuaClass(isStatic = true)
public class UDContactsManager {
    public static final String LUA_CLASS_NAME = "ContactsManager";
    public static final String TAG_TASK_FIND = "ContactsManager_TAG_TASK_FIND";
    public static final String TAG_TASK_ADD = "ContactsManager_TAG_TASK_ADD";
    public final static String SORT_TYPE_RELATION_FRIEND = "sorttype_realtion_friend";
    public final static int DEFAULT_SORT_TYPE_ADD_TIME = 2; // 添加时间排序
    private final static String TASKTAG_FOLLOW_FRIEND = "TASKTAG_FOLLOW_FRIEND";

    @LuaBridge
    public static void findLocalFollows(int sortType, int pageCount, int offset, LVCallback callback) {
        MomoTaskExecutor.executeUserTask(TAG_TASK_FIND, new RequestLocalTask(sortType, pageCount, offset, callback));
    }

    @LuaBridge
    public static void saveFollows(List<com.alibaba.fastjson.JSONObject> userListJson, LVCallback callback) {
        MomoTaskExecutor.executeUserTask(TAG_TASK_ADD, new AddFollowsTask(userListJson, callback));
    }

    @LuaBridge
    public static int getFollowingCount() {
        return BasicUserInfoUtil.INSTANCE.getFollowingCount();
    }

    /**
     * 保存好友数量
     *
     * @param followingCount
     */
    @LuaBridge
    public static void setFriendCount(int followingCount) {
        BasicUserInfoUtil.INSTANCE.setFriendCount(followingCount);
    }

    @LuaBridge
    public static void setFollowingCount(int followingCount) {
        BasicUserInfoUtil.INSTANCE.setFollowingCount(followingCount);
        new RefreshFollowCountReceiver(AppContext.getContext()).send(new Intent(RefreshFollowCountReceiver.ACTION_REFRESH_FOLLOW_COUNT));
    }

    @LuaBridge
    public static void setFansCount(int followingCount) {
        BasicUserInfoUtil.INSTANCE.setFollowerCount(followingCount);
    }

    @LuaBridge
    public static void setGroupCount(int followingCount) {
        BasicUserInfoUtil.INSTANCE.updateCurUserGroupCount(followingCount);
    }

    @LuaBridge
    public static int getFollowsSortType() {
        return KV.getUserInt(SORT_TYPE_RELATION_FRIEND, DEFAULT_SORT_TYPE_ADD_TIME);
    }

    @LuaBridge
    public static void saveFollowsSortType(int sortType) {
        KV.saveUserValue(SORT_TYPE_RELATION_FRIEND, sortType);
    }

    /**
     * 是否有用户被关注了，需要写入到搜索数据库
     *
     * @return
     */
    @LuaBridge
    public static void followedWithMomoid(String momoid) {
        if (StringUtils.isBlank(momoid)) return;
        MomoTaskExecutor.executeUserTask(TASKTAG_FOLLOW_FRIEND, new FollowContactUserTask(momoid));
    }

    private static class RequestLocalTask extends MomoTaskExecutor.Task<Object, Object, List<ContactUser>> {
        private int sortType;
        private int pageCount;
        private int offset;
        private LVCallback callback;

        public RequestLocalTask(int sortType, int pageCount, int offset, LVCallback callback) {
            this.pageCount = pageCount;
            this.sortType = sortType;
            this.offset = offset;
            this.callback = callback;
        }

        @Override
        protected void onPreTask() {
        }

        @Override
        protected List<ContactUser> executeTask(Object... params) throws Exception {
            return UserService.getInstance().findAllFollows(sortType, pageCount, offset);
        }

        @Override
        protected void onTaskSuccess(List<ContactUser> users) {
            if (callback != null) {
                HashMap<String, Object> response = new HashMap<>();
                response.put("users", com.alibaba.fastjson.JSONObject.toJSONString(users));
                callback.call(true, response);
            }
        }

        @Override
        protected void onTaskError(Exception e) {
            super.onTaskError(e);
            if (callback != null) {
                callback.call(false, null);
            }
        }
    }

    private static class AddFollowsTask extends MomoTaskExecutor.Task<Object, Object, Void> {
        private List<com.alibaba.fastjson.JSONObject> userListJson;
        private LVCallback callback;

        public AddFollowsTask(List<com.alibaba.fastjson.JSONObject> userListJson, LVCallback callback) {
            this.userListJson = userListJson;
            this.callback = callback;
        }

        @Override
        protected void onPreTask() {
        }

        @Override
        protected Void executeTask(Object... params) throws Exception {
            AppAsm.getRouter(ProfileRouter.class).saveOrUpdateUser(userListJson.toString());
            List<ContactUser> list = new ArrayList<>();
            for (int i = 0; i < userListJson.size(); i++) {
                ContactUser user = ContactApi.parseUserSimple(new JSONObject(userListJson.get(i).toString()));
                list.add(user);
            }
            new ItemModelFilter().doFilter(list);
            UserService.getInstance().addFollow(list);
            return null;
        }

        @Override
        protected void onTaskSuccess(Void v) {
            if (callback != null) {
                callback.call(true);
            }
        }

        @Override
        protected void onTaskError(Exception e) {
            super.onTaskError(e);
            if (callback != null) {
                callback.call(false);
            }
        }
    }

    private static class FollowContactUserTask extends MomoTaskExecutor.Task<Object, Object, List<FriendGroupV2>> {
        private String momoid;

        FollowContactUserTask(String momoid) {
            this.momoid = momoid;
        }

        @Override
        protected List<FriendGroupV2> executeTask(Object... params) throws Exception {
            JSONObject myProfileResp = UserApi.getInstance().downloadSimpleUsersJson(new String[]{momoid});
            if (myProfileResp != null) {
                JSONObject profileJson = myProfileResp.optJSONObject(momoid);
                ContactUser contactUser = ContactApi.parseUserSimple(profileJson);
                ArrayList<ContactUser> contactUsers = new ArrayList<>();
                contactUsers.add(contactUser);
                UserService.getInstance().addFriendList(contactUsers);
            }
            return null;
        }

        @Override
        protected void onTaskSuccess(List<FriendGroupV2> groups) {}

        @Override
        protected void onTaskError(Exception e) {}
    }

}
