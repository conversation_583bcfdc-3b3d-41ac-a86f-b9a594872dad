package com.immomo.momo.luaview

import android.content.Context
import android.graphics.Typeface
import android.view.LayoutInflater
import android.view.View
import com.immomo.framework.view.widget.ShimmeringLabel
import com.immomo.mls.`fun`.weight.BorderRadiusFrameLayout
import com.immomo.momo.R

/**
 * 扫光控件桥接
 * Created by li.mengnan on 2021/2/16.
 */
class LuaShimmeringLabel(activity: Context) : BorderRadiusFrameLayout(activity) {

    private var mSlText: ShimmeringLabel? = null

    private fun init(context: Context) {
        LayoutInflater.from(context).inflate(R.layout.lua_shimmering_label, this)
        mSlText = findViewById<View>(R.id.sl_text) as ShimmeringLabel?
    }

    fun setText(text: String?) {
        mSlText?.text = text
    }

    fun setTextSize(size: Float) {
        mSlText?.textSize = size
    }

    fun setTextColor(color: Int) {
        mSlText?.setTextColor(color)
    }

    fun setFontStyle(isBold: Boolean) {
        if (isBold) {
            mSlText?.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
        } else {
            mSlText?.typeface = Typeface.defaultFromStyle(Typeface.NORMAL)
        }
    }

    fun setFont(typeface: Typeface?) {
        mSlText?.typeface = typeface
    }

    fun setShimmerColors(vararg colors: Int) {
        mSlText?.setShimmerColors(*colors)
    }

    fun setShimmeringInterval(interval: Float) {
        mSlText?.setShimmeringInterval(interval)
    }

    fun setShimmeringAngle(angle: Float) {
        mSlText?.setShimmeringAngle(angle)
    }

    fun setShimmeringDuration(duration: Float) {
        mSlText?.setShimmeringDuration(duration)
    }

    fun setShimmeringSpeed(speed: Float) {
        mSlText?.setShimmeringSpeed(speed)
    }

    fun setShimmeringPercent(percent: Float) {
        mSlText?.setShimmeringPercent(percent)
    }

    fun setShimmeringSpace(space: Float) {
        mSlText?.setShimmeringSpace(space)
    }

    fun setShimmeringRepeat(times: Int) {
        mSlText?.setShimmeringRepeat(times)
    }

    fun startShimmering() {
        mSlText?.startShimmering()
    }

    fun stopShimmering() {
        mSlText?.stopShimmering()
    }

    fun refreshView() {
        mSlText?.invalidate()
    }

    init {
        init(activity)
    }
}