package com.immomo.momo.luaview.ud;


import android.graphics.SurfaceTexture;
import android.view.Surface;
import android.view.TextureView;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;

import com.cosmos.mdlog.MDLog;
import com.immomo.mls.fun.ud.view.UDView;
import com.immomo.momo.gift.GiftVideoEffectResourceHelper;
import com.immomo.momo.gift.player.MLIjkPlayer;
import com.immomo.momo.util.BaseDownloadResourceHelper;
import com.immomo.momo.util.StringUtils;
import com.immomo.velib.player.EffectConfig;
import com.immomo.velib.player.EffectPlayer;
import com.immomo.velib.player.IEffectPlayer;

import org.json.JSONException;
import org.json.JSONObject;
import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import java.io.File;

/**
 * author: hongming.wei
 * data: 2024/6/17
 */
@LuaApiUsed
public class UDVideoGiftView<V extends FrameLayout> extends UDView<V> {

    public static final String LUA_CLASS_NAME = "VideoGiftView";

    public static final String[] methods = {
            "startPlay",
            "animateCompletion",
            "videoDestroy"
    };

    private IEffectPlayer player;
    private TextureView mTextureView;
    private LuaFunction completionFunction;

    @LuaApiUsed
    protected UDVideoGiftView(long L, LuaValue[] v) {
        super(L, v);
    }
    @NonNull
    @Override
    protected V newView(@NonNull LuaValue[] init) {
        return (V) new FrameLayout(getContext());
    }

    @LuaApiUsed
    public LuaValue[] startPlay(LuaValue[] values) {
        if (values != null && values.length > 0) {
            String videoUrl = values[0].toJavaString();
            if (StringUtils.isNotEmpty(videoUrl)) {
                showGiftAnim(videoUrl, StringUtils.md5(videoUrl));
            }
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] animateCompletion(LuaValue[] values) {
        if (completionFunction != null) {
            completionFunction.destroy();
        }
        completionFunction = values[0].isFunction() ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] videoDestroy(LuaValue[] values) {
        releaseVideo();
        completionFunction = null;
        return null;
    }


    public void showGiftAnim(String videoUrl, String resourceId) {
        if (StringUtils.isEmpty(videoUrl) || StringUtils.isEmpty(resourceId)) {
            return;
        }
        if (GiftVideoEffectResourceHelper.getInstance().isResourceReady(resourceId)) {
            readConfigJsonFile(new File(GiftVideoEffectResourceHelper.getInstance().getBaseFile(), resourceId));
        } else {//去下载资源再播
            GiftVideoEffectResourceHelper.getInstance().downloadResource(resourceId, videoUrl, new BaseDownloadResourceHelper.OnPrepareListener() {
                @Override
                public void prepared(File file) {
                    readConfigJsonFile(file);
                }

                @Override
                public void onFail() {
                }
            });
        }
    }


    private void readConfigJsonFile(final File file) {
        File configFile = new File(file, "config.json");
        if (configFile.exists()) {
            try {
                GiftVideoEffectResourceHelper.getInstance().getConfigJson(file.getName(), configFile, new GiftVideoEffectResourceHelper.OnGetConfigJsonListener() {

                    @Override
                    public void success(JSONObject jsonObject) {
                        if (getView() != null && getView().isAttachedToWindow()) {
                            parseVideoInfoAndPlay(jsonObject, file);
                        }
                    }

                    @Override
                    public void fail() {

                    }
                });
            } catch (Exception e) {
                MDLog.printErrStackTrace("PersonalProfileHeaderView", e);
            }
        }
    }

    private void parseVideoInfoAndPlay(JSONObject configJson, File file) {
        try {
            String fileName = configJson.getString("video");
            File mp4 = new File(file, fileName);
            if (mp4.exists()) {
                setTextureStats(mp4.getAbsolutePath());
            }
        } catch (JSONException e) {
            MDLog.printErrStackTrace("PersonalProfileHeaderView", e);
        }
    }


    public void setTextureStats(String videoUrl) {
        if (getView() != null) {
            mTextureView = null;
            mTextureView = new TextureView(getContext());
            mTextureView.setOpaque(false);
            getView().addView(mTextureView, 0, new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT));
            playMp4Video(videoUrl);
        }
    }


    private void playMp4Video(String videoUrl) {
        if (player == null) {
            player = new EffectPlayer(getContext());
        }

        try {
            player.setOnVideoCompleteListener(() -> {
                releaseVideo();
                if (completionFunction != null) {
                    completionFunction.fastInvoke();
                }
            });
            player.setRenderSize(750, 1280);
            player.setDataSource(videoUrl, IEffectPlayer.MERGE_ALPHA);
            addTextureViewListener();
        } catch (Exception e) {
            MDLog.printErrStackTrace("PersonalProfileHeaderView", e);
        }
    }

    private void addTextureViewListener() {
        if (mTextureView == null) {
            return;
        }
        mTextureView.setSurfaceTextureListener(new TextureView.SurfaceTextureListener() {
            @Override
            public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height) {
                if (player != null) {
                    EffectConfig.Builder builder = new EffectConfig.Builder().setVisualSize(width, height);
                    builder.setMLVideoPlayer(new MLIjkPlayer());
                    player.setEffectConfig(builder.build());
                    player.prepare();
                    player.startPlay(new Surface(surface));
                }
            }

            @Override
            public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) {

            }

            @Override
            public boolean onSurfaceTextureDestroyed(SurfaceTexture surface) {
                return false;
            }

            @Override
            public void onSurfaceTextureUpdated(SurfaceTexture surface) {

            }
        });
    }

    public void releaseVideo() {
        if (getView() != null) {
            getView().removeAllViews();
        }
        if (mTextureView != null) {
            mTextureView = null;
        }
        if (player != null) {
            player.stopPlay();
            player = null;
        }
    }
}
