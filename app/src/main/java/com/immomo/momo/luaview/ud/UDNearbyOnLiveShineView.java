package com.immomo.momo.luaview.ud;

import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.immomo.framework.view.widget.ShimmerFrameLayout;
import com.immomo.mls.fun.ud.view.UDViewGroup;
import com.immomo.mls.util.DimenUtil;
import com.immomo.momo.R;
import com.immomo.momo.service.bean.User;

import org.luaj.vm2.Globals;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

/**
 * Created by fanqiang on 2018/9/20.
 */
@LuaApiUsed
public class UDNearbyOnLiveShineView<L extends ShimmerFrameLayout> extends UDViewGroup<L> {
    public static final String LUA_CLASS_NAME = "NearbyOnLiveShineView";

    public static final String[] methods = new String[]{
            "updateStyleWithLivingType",
            "startAnimation"
    };

    private ImageView imageView;

    @LuaApiUsed
    public UDNearbyOnLiveShineView(long L, LuaValue[] v) {
        super(L, v);
    }

    @LuaApiUsed
    public UDNearbyOnLiveShineView(Globals g, L jud) {
        super(g, jud);
    }

    public UDNearbyOnLiveShineView(Globals g) {
        super(g);
    }

    @Override
    protected L newView(LuaValue[] init) {
        return (L) new ShimmerFrameLayout(getContext());
    }

    @LuaApiUsed
    public LuaValue[] updateStyleWithLivingType(LuaValue [] values) {

        Integer type = values[0].toInt();

        if (imageView == null) {
            getView().setAutoStart(true);
            getView().setDuration(1600);
            getView().setRelativeWidth(1);
            getView().setRepeatDelay(0);
            getView().setMaskShape(ShimmerFrameLayout.MaskShape.LINEAR_CUSTOM);
            getView().setPadding(DimenUtil.dpiToPx(7), DimenUtil.dpiToPx(3), DimenUtil.dpiToPx(6), DimenUtil.dpiToPx(3));
            getView().setBackgroundResource(R.drawable.bg_nearby_live_cornered);

            getView().removeAllViews();

            imageView = new ImageView(getView().getContext());
            FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            lp.gravity = Gravity.CENTER;
            imageView.setLayoutParams(lp);
            getView().addView(imageView);
        }

        if (type == User.ON_VIDEO_LIVE) {
            getView().setBackgroundResource(R.drawable.bg_nearby_live_cornered);
            imageView.setImageResource(R.drawable.icon_live_text);
        } else if (type == User.ON_RADIO_LIVE) {
            getView().setBackgroundResource(R.drawable.bg_nearby_radio_cornered);
            imageView.setImageResource(R.drawable.ic_nearby_user_radio);
        } else if (type == User.ON_NEARBY_PEOPLE_LIVE) {
            getView().setAutoStart(false);
            getView().setRepeatCount(0);
            getView().setMaskShape(ShimmerFrameLayout.MaskShape.LINEAR_NEARBY_PEOPLE);
            getView().setPadding(DimenUtil.dpiToPx(0), DimenUtil.dpiToPx(0), DimenUtil.dpiToPx(0), DimenUtil.dpiToPx(0));
            getView().setRepeatDelay(1500);
            getView().setBackgroundResource(R.drawable.transparent);
            imageView.setImageResource(R.drawable.ic_nearby_people_live_shimmer);
        }
        getView().setVisibility(View.VISIBLE);

        return null;
    }

    @LuaApiUsed
    public LuaValue[] startAnimation(LuaValue[] values) {
        return null;
    }
}
