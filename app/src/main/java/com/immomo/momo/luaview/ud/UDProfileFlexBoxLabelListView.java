package com.immomo.momo.luaview.ud;

import android.view.ViewGroup;

import com.cosmos.mdlog.MDLog;
import com.google.android.flexbox.AlignContent;
import com.google.android.flexbox.AlignItems;
import com.google.android.flexbox.FlexDirection;
import com.google.android.flexbox.FlexWrap;
import com.google.android.flexbox.FlexboxLayout;
import com.google.android.flexbox.JustifyContent;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.immomo.mls.fun.ud.view.UDView;
import com.immomo.mls.util.RelativePathUtils;
import com.immomo.momo.luaview.weight.FlexBoxImgThemeData;
import com.immomo.momo.luaview.weight.FlexBoxLabelData;
import com.immomo.momo.luaview.weight.FlexBoxLabelThemeData;
import com.immomo.momo.luaview.weight.LuaFlexBoxLabelListItemView;
import com.immomo.momo.util.StringUtils;

import org.jetbrains.annotations.NotNull;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@LuaApiUsed
public class UDProfileFlexBoxLabelListView extends UDView<FlexboxLayout> {

    public static final String LUA_CLASS_NAME = "basicInfoView";

    private FlexboxLayout flexLabelView;

    private String lastListData = null;
    private String lastThemeData = null;

    public static final String[] methods = {
            "bindData",
            "fillData"
    };

    @LuaApiUsed
    public UDProfileFlexBoxLabelListView(long L, LuaValue[] initParams) {
        super(L, initParams);
    }

    @NotNull
    @Override
    protected FlexboxLayout newView(LuaValue[] init) {
        flexLabelView = new FlexboxLayout(getContext());
        flexLabelView.setFlexDirection(FlexDirection.ROW);
        flexLabelView.setFlexWrap(FlexWrap.WRAP);
        flexLabelView.setJustifyContent(JustifyContent.FLEX_START);
        flexLabelView.setAlignItems(AlignItems.FLEX_START);
        flexLabelView.setAlignContent(AlignContent.FLEX_START);
        return flexLabelView;
    }

    @LuaApiUsed
    public LuaValue[] bindData(LuaValue[] fun) {
        if (fun != null && fun.length > 0 && fun[0] != null) {
            String jsonData = fun[0].toJavaString();
            if (jsonData != null && jsonData.equals(lastListData)) return null;
            try {
                List<FlexBoxLabelData> list = new Gson().fromJson(jsonData,
                        new TypeToken<List<FlexBoxLabelData>>() {
                        }.getType());
                if (list != null) {
                    lastListData = jsonData;
                    if (flexLabelView.getFlexItemCount() > 0) {
                        flexLabelView.removeAllViews();
                    }
                    for (int i = 0; i < list.size(); i++) {
                        FlexBoxLabelData flexBoxLabelData = list.get(i);
                        LuaFlexBoxLabelListItemView itemView = new LuaFlexBoxLabelListItemView(getContext());
                        itemView.bindData(flexBoxLabelData);
                        FlexboxLayout.LayoutParams layoutParams = new FlexboxLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                        layoutParams.topMargin = LuaFlexBoxLabelListItemView.getTagSpaceMargin();
                        layoutParams.bottomMargin = LuaFlexBoxLabelListItemView.getTagSpaceMargin();
                        layoutParams.rightMargin = LuaFlexBoxLabelListItemView.getTagSpaceMargin();
                        layoutParams.leftMargin = LuaFlexBoxLabelListItemView.getTagSpaceMargin();
                        flexLabelView.addView(itemView, layoutParams);
                    }
                }
            } catch (Exception e) {
                MDLog.printErrStackTrace(LUA_CLASS_NAME, e);
            }
        }
        return null;
    }


    @LuaApiUsed
    public LuaValue[] fillData(LuaValue[] fun) {
        if (fun != null && fun.length > 0 && fun[0] != null) {
            String jsonData = fun[0].toJavaString();
            String iconPath = "";
            if (fun.length > 1 && fun[1].isString()) {
                iconPath = fun[1].toJavaString();
                if (RelativePathUtils.isLocalUrl(iconPath)) {
                    iconPath = RelativePathUtils.getAbsoluteUrl(iconPath);
                }
            }
            String jsonTheme = "";
            if (fun.length > 2 && fun[2].isString()) {
                jsonTheme = fun[2].toJavaString();
            }
            if (jsonData != null && jsonData.equals(lastListData) && StringUtils.isNotEmpty(jsonTheme) && StringUtils.equalsNonNull(jsonTheme, lastThemeData)) return null;
            try {
                List<FlexBoxLabelData> list = new Gson().fromJson(jsonData,
                        new TypeToken<List<FlexBoxLabelData>>() {
                        }.getType());
                FlexBoxLabelThemeData themeData = getLabelThemeData(jsonTheme);
                String contentColor = "";
                String borderColor = "";
                Map<String, FlexBoxImgThemeData> themeDataMap = null;
                if (themeData != null && themeData.getInfoLists() != null && themeData.getInfoLists().size() > 0) {
                    themeDataMap = getImgThemeData(themeData.getInfoLists());
                }
                if (themeData != null) {
                    contentColor = themeData.getContentColor();
                    borderColor = themeData.getBorderColor();
                }
                if (list != null) {
                    lastListData = jsonData;
                    lastThemeData = jsonTheme;
                    if (flexLabelView.getFlexItemCount() > 0) {
                        flexLabelView.removeAllViews();
                    }
                    for (int i = 0; i < list.size(); i++) {
                        FlexBoxLabelData flexBoxLabelData = list.get(i);
                        LuaFlexBoxLabelListItemView itemView = new LuaFlexBoxLabelListItemView(getContext());
                        itemView.bindData(flexBoxLabelData, themeDataMap, contentColor, borderColor, iconPath);
                        FlexboxLayout.LayoutParams layoutParams = new FlexboxLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                        layoutParams.topMargin = LuaFlexBoxLabelListItemView.getTagSpaceMargin();
                        layoutParams.bottomMargin = LuaFlexBoxLabelListItemView.getTagSpaceMargin();
                        layoutParams.rightMargin = LuaFlexBoxLabelListItemView.getTagSpaceMargin();
                        layoutParams.leftMargin = LuaFlexBoxLabelListItemView.getTagSpaceMargin();
                        flexLabelView.addView(itemView, layoutParams);
                    }
                }
            } catch (Exception e) {
                MDLog.printErrStackTrace(LUA_CLASS_NAME, e);
            }
        }
        return null;
    }


    private FlexBoxLabelThemeData getLabelThemeData(String jsonTheme) {
        try {
            if (StringUtils.isNotEmpty(jsonTheme)) {
                FlexBoxLabelThemeData themeData = new Gson().fromJson(jsonTheme,
                        new TypeToken<FlexBoxLabelThemeData>() {
                        }.getType());
                return themeData;
            }
        } catch (Exception e) {
            MDLog.printErrStackTrace("UDProfileFlexBoxLabelListView", e);
        }
        return null;
    }

    private Map<String, FlexBoxImgThemeData> getImgThemeData(List<FlexBoxImgThemeData> imgTheme) {
        Map<String, FlexBoxImgThemeData> themeDataMap = new HashMap<>();
        try {
            for (FlexBoxImgThemeData themeData : imgTheme) {
                if (themeData != null) {
                    themeDataMap.put(themeData.getCategory(), themeData);
                }
            }
        } catch (Exception e) {
            MDLog.printErrStackTrace("UDProfileFlexBoxLabelListView", e);
        }
        return themeDataMap;
    }
}
