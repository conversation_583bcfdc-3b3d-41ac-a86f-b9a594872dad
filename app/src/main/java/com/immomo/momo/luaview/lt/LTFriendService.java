package com.immomo.momo.luaview.lt;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.cosmos.mdlog.MDLog;
import com.immomo.framework.model.businessmodel.contact.IRelationRepository;
import com.immomo.framework.storage.kv.KV;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmutil.StringUtils;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.task.ThreadUtils;
import com.immomo.momo.Configs;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoKit;
import com.immomo.momo.android.broadcast.ReflushMyGroupListReceiver;
import com.immomo.momo.android.broadcast.ReflushUserProfileReceiver;
import com.immomo.momo.contact.bean.FriendGroupV2;
import com.immomo.momo.eventbus.group.GroupEvent;
import com.immomo.momo.gotologic.GotoDispatcher;
import com.immomo.momo.messages.service.GroupMsgService;
import com.immomo.momo.mvp.common.model.ModelManager;
import com.immomo.momo.service.bean.PaginationResult;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.service.bean.user.ContactUser;
import com.immomo.momo.service.group.GroupService;
import com.immomo.momo.service.sessions.SessionService;
import com.immomo.momo.service.user.UserService;
import com.immomo.momo.setting.BasicUserInfoUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import de.greenrobot.event.EventBus;

@LuaClass(isStatic = true)
public class LTFriendService {
    public static final String LUA_CLASS_NAME = "FriendService";
    public static final int SORT_TYPE_ADD_TIME = 2; // 添加时间排序
    private static final String PRE_TIME_SUCCESS_BOTHLIST = "lasttime_bothlist_success";

    //申请结果
    public static final int SUCCESS = 0;
    public static final int SUCCESS_NO_REVIEW = 2;
    public static final int ERROR = 1;

    private static InitDataTask initDataTask;

    private LTFriendService() {

    }

    @LuaBridge
    public static void cancelCreateGroup(String gid) {
        MomoTaskExecutor.executeUserTask(LUA_CLASS_NAME, new MomoTaskExecutor.Task() {
            @Override
            protected Object executeTask(Object[] objects) throws Exception {
                GroupService service = GroupService.getInstance();
                try {
                    User currentUser = MomoKit.getCurrentUser();
                    service.removeGroupUser(currentUser.momoid, gid);
                } catch (NullPointerException e) {
                }
                GroupMsgService.getInstance().deleteByGroupId(gid, true);
                return null;
            }
        });

         Context context = MomoKit.getTopActivity();
        Intent it = new Intent(ReflushMyGroupListReceiver.ACTION_DELETE);
        it.putExtra(ReflushMyGroupListReceiver.KEY_GID, gid);
        context.sendBroadcast(it); // 更新群组列表广播

        Intent refreshUserIntent = new Intent(ReflushUserProfileReceiver.ACTION_REFRESHGROUP);
        context.sendBroadcast(refreshUserIntent); // 更新个人信息广播
    }

    @LuaBridge
    public static void handleJoinGroup(boolean success, boolean isNoReview, String action,
                                       String gid, String source) {
        if(TextUtils.isEmpty(gid)) {
            return;
        }
        if (success) {
            if (isNoReview) {
                sendJoinResult(SUCCESS_NO_REVIEW, gid, action, source);
            } else {
                sendJoinResult(SUCCESS, gid, null, source);
            }
        } else {
            sendJoinResult(ERROR, gid, null, source);
        }
    }

    private static void sendJoinResult(int status, String gid, String action, String from) {
        if (StringUtils.isEmpty(gid)) {
            return;
        }
        //成功后发送事件
        GroupEvent groupEvent = new GroupEvent();
        groupEvent.from = from;
        switch (status) {
            case SUCCESS:
                groupEvent.status = GroupEvent.SUCCESS;
                break;
            case ERROR:
                groupEvent.status = GroupEvent.FAILED;
                break;
            case SUCCESS_NO_REVIEW:
                groupEvent.status = GroupEvent.SUCCESS_NO_REVIEW;
                GotoDispatcher.action(action, MomoKit.getTopActivity()).execute();
                break;
            default:
                break;
        }
        EventBus.getDefault().post(groupEvent);
    }


    @LuaBridge
    public static void getRecentContact(LVCallback callback) {
        MomoTaskExecutor.executeUserTask(LTFriendService.class.getSimpleName(), new MomoTaskExecutor.Task<Object, Object, Map<String, Object>>() {

            @Override
            protected Map<String, Object> executeTask(Object... objects) throws Exception {
                FriendGroupV2 group = SessionService.getInstance().getContactedUserGroupV2();
                return groupToMap(group);
            }

            @Override
            protected void onTaskSuccess(Map<String, Object> groupMap) {
                callback.call(true, groupMap);
            }

            @Override
            protected void onTaskError(Exception e) {
                super.onTaskError(e);
                callback.call(false);
            }

        });
    }

    @LuaBridge
    public static void getAllFriendUserGroup(LVCallback callback) {
        if (initDataTask != null && !initDataTask.isCancelled()) {
            initDataTask.cancel(true);
        }
        initDataTask = new InitDataTask(callback);
        MomoTaskExecutor.executeTask(ThreadUtils.TYPE_RIGHT_NOW,
                LTFriendService.class.getSimpleName(), initDataTask);
    }

    @LuaBridge
    public static void searchFriendsByName(String userName, LVCallback callback) {
        MomoTaskExecutor.executeUserTask(LTFriendService.class.getSimpleName(), new MomoTaskExecutor.Task<Object, Object, List<Map<String, Object>>>() {

            @Override
            protected List<Map<String, Object>> executeTask(Object... objects) throws Exception {
                List<ContactUser> resultUserList = new ArrayList<>();
                try {
                    resultUserList = UserService.getInstance().searchFriendList(userName);
                } catch (Exception e) {
                    //do nothing
                }

                return usersToMap(resultUserList);
            }

            @Override
            protected void onTaskSuccess(List<Map<String, Object>> users) {
                super.onTaskSuccess(users);
                if (callback != null) {
                    callback.call(true, users);
                }
            }

            @Override
            protected void onTaskError(Exception e) {
                super.onTaskError(e);
                callback.call(false);
            }
        });
    }

    private static class InitDataTask extends MomoTaskExecutor.Task<Object, Object, Object> {

        private FlushTask flushTask;
        private LoadLocalDataTask localDataTask;
        private final LVCallback callback;

        public InitDataTask(LVCallback callback) {
            this.callback = callback;
        }

        @Override
        protected Object executeTask(Object... objects) throws Exception {
            startLocalTask(callback);
            return null;
        }

        @Override
        protected void onTaskError(Exception e) {
            super.onTaskError(e);
            if (callback != null) {
                callback.call(false);
            }
        }

        private void startLocalTask(LVCallback callback) {
            if (localDataTask != null && !localDataTask.isCancelled()) {
                localDataTask.cancel(true);
            }
            localDataTask = new LoadLocalDataTask(callback);
            MomoTaskExecutor.executeTask(ThreadUtils.TYPE_RIGHT_NOW,
                    LTFriendService.class.getSimpleName(), localDataTask);
        }

        /**
         * 将获取的原生用户列表数据，转成map
         */
        private static List<Map<String, Object>> parseGroupsToMaps(List<FriendGroupV2> list) {
            List<Map<String, Object>> result = new ArrayList<>(list.size());
            for (FriendGroupV2 group : list) {
                result.add(groupToMap(group));
            }
            return result;
        }

        private class LoadLocalDataTask extends MomoTaskExecutor.Task<Object, Object, List<FriendGroupV2>> {

            private final LVCallback callback;

            public LoadLocalDataTask(LVCallback callback) {
                this.callback = callback;
            }

            @Override
            protected List<FriendGroupV2> executeTask(Object... objects) throws Exception {
                return loadFromLocal();
            }

            @Override
            protected void onTaskSuccess(List<FriendGroupV2> friendGroups) {
                super.onTaskSuccess(friendGroups);
                if (isNeedRefreshFromApi(friendGroups)) {
                    startRefreshTask(callback);
                } else {
                    if (callback != null) {
                        callback.call(true, parseGroupsToMaps(friendGroups));
                    }
                }
            }

            @Override
            protected void onTaskError(Exception e) {
                super.onTaskError(e);
                MDLog.e(LogTag.RecentContactHandler.allFriendHandler, e.getMessage());
                startRefreshTask(callback);
            }

            private boolean isNeedRefreshFromApi(List<FriendGroupV2> friendGroups) {
                return (friendGroups == null || friendGroups.isEmpty())
                        || System.currentTimeMillis() -
                        KV.getUserLong(PRE_TIME_SUCCESS_BOTHLIST, 0L) > Configs.FRIEND_FLUSH_TIME;
            }

            private List<FriendGroupV2> loadFromLocal() {
                return new ArrayList<>(getUsersFromDB());
            }

            private List<FriendGroupV2> getUsersFromDB() {
                return UserService.getInstance().getFriendGroupList(SORT_TYPE_ADD_TIME);
            }

            private void startRefreshTask(LVCallback callback) {
                if (flushTask != null && !flushTask.isCancelled()) {
                    flushTask.cancel(true);
                }
                flushTask = new FlushTask(callback);
                MomoTaskExecutor.executeTask(ThreadUtils.TYPE_RIGHT_NOW,
                        LTFriendService.class.getSimpleName(), flushTask);
            }

        }

        private static class FlushTask extends MomoTaskExecutor.Task<Object, Object, List<FriendGroupV2>> {

            private final LVCallback callback;

            public FlushTask(LVCallback callback) {
                this.callback = callback;
            }

            @Override
            protected List<FriendGroupV2> executeTask(Object... params) throws Exception {
                return loadFromApi();
            }

            @Override
            protected void onTaskSuccess(List<FriendGroupV2> list) {
                KV.saveUserValue(PRE_TIME_SUCCESS_BOTHLIST, System.currentTimeMillis());
                if (callback != null) {
                    callback.call(true, parseGroupsToMaps(list));
                }
            }

            @Override
            protected void onTaskError(Exception e) {
                super.onTaskError(e);
                if (callback != null) {
                    callback.call(false);
                }
            }

            private List<FriendGroupV2> loadFromApi() throws Exception {
                PaginationResult<List<FriendGroupV2>> result = ModelManager.getModel(IRelationRepository.class).getFriendListV2(200);
                UserService.getInstance().saveFriendGroupDataV2(result.getData(), true);  // 缓存好友分组数据到文件
                BasicUserInfoUtil.INSTANCE.setFriendCount(result.getTotal());
                return UserService.getInstance().getFriendGroupList(SORT_TYPE_ADD_TIME);
            }
        }

    }

    public static List<Map<String, Object>> usersToMap(List<ContactUser> users) {
        List<Map<String, Object>> resultList = new ArrayList<>(users.size());
        for (ContactUser user : users) {
            Map<String, Object> userMap = new HashMap<>(4);
            userMap.put("name", user.name);
            userMap.put("remarkname", user.remarkName);
            userMap.put("momoid", user.momoid);
            userMap.put("avatar", user.getAvatar());
            resultList.add(userMap);
        }
        return resultList;
    }

    public static Map<String, Object> groupToMap(FriendGroupV2 group) {
        HashMap<String, Object> groupMap = new HashMap<>();
        groupMap.put("bargoto", group.getBargoto());
        groupMap.put("title", group.title);
        groupMap.put("category", group.category);
        List<Map<String, Object>> userList = new ArrayList<>();
        for (int i = 0; i < group.userList.size(); i++) {
            ContactUser user = group.userList.get(i);
            HashMap<String, Object> userMap = new HashMap<>();
            userMap.put("name", user.name);
            userMap.put("remarkname", user.remarkName);
            userMap.put("momoid", user.momoid);
            userMap.put("avatar", user.getAvatar());
            userMap.put("distance", user.getDistance());
            userMap.put("loc_timesec", user.getLocTimesec());
            userMap.put("followtime", user.getFollow_time());
            userList.add(userMap);
        }
        groupMap.put("data", userList);
        return groupMap;
    }

}
