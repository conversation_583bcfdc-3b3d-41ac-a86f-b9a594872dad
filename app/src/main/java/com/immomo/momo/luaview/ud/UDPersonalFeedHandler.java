package com.immomo.momo.luaview.ud;

import android.database.Cursor;
import android.text.TextUtils;

import androidx.fragment.app.FragmentActivity;

import com.immomo.android.mm.kobalt.domain.fx.Option;
import com.immomo.android.module.feed.FeedSPKeys;
import com.immomo.android.module.feedlist.data.api.GetGuideConfigApi;
import com.immomo.android.module.feedlist.data.api.response.bean.GuideConfig;
import com.immomo.android.module.feedlist.data.repository.GetGuideConfigRepository;
import com.immomo.android.module.feedlist.domain.model.style.inner.NewPhotoMediaHeaderModel;
import com.immomo.android.module.feedlist.presentation.feedUtils.MediaLatLonHelper;
import com.immomo.android.router.momo.business.VideoRecordParam;
import com.immomo.android.router.momo.business.VideoRecordRouter;
import com.immomo.android.router.momo.util.LoggerUtilRouter;
import com.immomo.framework.storage.kv.KV;
import com.immomo.mls.LuaViewManager;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.moment.MomentConstants;
import com.immomo.momo.multpic.entity.LatLonPhotoList;
import com.immomo.momo.multpic.utils.MediaStoreHelper;
import com.immomo.momo.publish.view.PublishFeedActivity;
import com.immomo.momo.statistics.LoggerKeys;

import org.luaj.vm2.Globals;
import org.luaj.vm2.LuaValue;

import java.util.HashMap;

import info.xudshen.android.appasm.AppAsm;

@LuaClass
public class UDPersonalFeedHandler {
    public static final String LUA_CLASS_NAME = "UserFeedGuideManager";
    private FragmentActivity topActivity;
    private PhotoTask photoTask;
    private ConfigTask configTask;
    private LatLonPhotoList currentLatLonPhotoList;


    public UDPersonalFeedHandler(Globals globals, LuaValue[] init) {
        LuaViewManager m = (LuaViewManager) globals.getJavaUserdata();
        if (m != null && m.context instanceof FragmentActivity ) {
            topActivity = (FragmentActivity) m.context;
        }
    }

    @LuaBridge
    public void showMediaOrGuideHeader(LVCallback callback) {
        if (MediaLatLonHelper.needCheckNewPhoto()) {
            if (this.topActivity != null) {
                MediaStoreHelper.getNewPositionImage(this.topActivity, cursor -> {
                    if (cursor == null) {
                        MediaStoreHelper.cancelImageGuideScanLoadSupport(this.topActivity);
                        return null;
                    } else {
                        cancelTask(photoTask);
                        photoTask = new PhotoTask(cursor, callback);
                        MomoTaskExecutor.executeUserTask("UDPersonalFeedHandler", photoTask);
                    }
                    return null;
                });
            }

        } else {
            cancelTask(configTask);
            configTask = new ConfigTask(1, callback);
            MomoTaskExecutor.executeUserTask("UDPersonalFeedHandler", configTask);
        }
    }

    public void getJustOneImageOrVideo(String stat_key, LVCallback callback, String icon, String title, String description, String goto_url) {

        if (this.topActivity != null) {
            MediaStoreHelper.getJustOneImageOrVideo(this.topActivity, null, 5, false, (MediaStoreHelper.MediaScanJustOneCallback) (path, type, id) -> {
                MediaStoreHelper.cancelFirstImageAndVideoLoad(this.topActivity);
                HashMap<String, Object> map = new HashMap<>();
                map.put("photoPath",path);
                map.put("icon",icon);
                map.put("title",title);
                map.put("description",description);
                map.put("gotoUrl",goto_url);
                callback.call(true,map);
                if (!TextUtils.isEmpty(path)) {
                    AppAsm.getRouter(LoggerUtilRouter.class).saveGotoLog(
                            String.format(
                                    LoggerKeys.LOCAL_LOG_KEY_FRIEND_FEED_LIST_GUIDE_PHOTO_NEW_PHOTO_SHOW,
                                    stat_key
                            )
                    );
                }
            });
        }
    }

    @LuaBridge
    public void gotoUserMediaGuide(){
        KV.saveUserValue(
                FeedSPKeys.User.UserFeedList.KEY_USER_FEEDS_LIST_RECENT_NEW_POSITION_PHOTO_CLICK_TIME,
                System.currentTimeMillis() / 1000
        );
        KV.saveUserValue(
                FeedSPKeys.User.UserFeedList.KEY_USER_FEEDS_LIST_NEW_POSITION_PHOTO_CHECK_COUNT,
                2
        );
        AppAsm.getRouter(LoggerUtilRouter.class).saveGotoLog(
                String.format(
                        LoggerKeys.LOCAL_LOG_KEY_FRIEND_FEED_LIST_GUIDE_PHOTO_NEW_PHOTO_CLICK,
                        MomentConstants.SCHEME_ID_NEW_PHOTO
                )
        );
        VideoRecordParam videoRecordInfo = new  VideoRecordParam();
        videoRecordInfo.setState(VideoRecordParam.State.ChooseMedia);
        videoRecordInfo.setShowAlbumTabs( VideoRecordParam.AlbumTabs.PictureAlbumVideoCollect);
        videoRecordInfo.setInitAlbumIndex(VideoRecordParam.AlbumTabs.Album);
        videoRecordInfo.setStatKey(MomentConstants.SCHEME_ID_NEW_PHOTO);
        videoRecordInfo.setGuideFromType( MomentConstants.SOURCE_PAGE_NEW_PHOTO_PROFILE);
        videoRecordInfo.setGotoActivityName(PublishFeedActivity.class.getName());
        videoRecordInfo.setSendText(VideoRecordParam.SendText.Complete);
        videoRecordInfo.setHasLatLonPhotos(true);
        videoRecordInfo.setLatLonMedias(currentLatLonPhotoList);
        currentLatLonPhotoList = null;
        if (this.topActivity != null) {
            AppAsm.getRouter(VideoRecordRouter.class).openVideoRecord(this.topActivity, videoRecordInfo, -1);
        }
    }

    private void cancelTask(MomoTaskExecutor.Task task) {
        if (task != null && !task.isCancelled()) {
            task.cancel(true);
        }
    }


     class PhotoTask extends MomoTaskExecutor.Task<Object, Object, Option<NewPhotoMediaHeaderModel>> {
        Cursor cursor;
        LVCallback callback;

        public PhotoTask(Cursor cursor, LVCallback callback) {
            this.cursor = cursor;
            this.callback = callback;
        }

        @Override
        protected Option<NewPhotoMediaHeaderModel> executeTask(Object... objects) throws Exception {
            GetGuideConfigApi api = new GetGuideConfigApi();
            GetGuideConfigRepository configRepository = new GetGuideConfigRepository(api);
            return configRepository.scanning(cursor);
        }

        @Override
        protected void onTaskSuccess(Option<NewPhotoMediaHeaderModel> modelOption) {
            super.onTaskSuccess(modelOption);

            if (modelOption == null || modelOption.isEmpty()) {
                //请求接口
                cancelTask(configTask);
                configTask = new ConfigTask(1, callback);
                MomoTaskExecutor.executeUserTask("UDPersonalFeedHandler", configTask);
            } else {
                modelOption.map(model -> {
                    HashMap<String, Object> response = new HashMap<>();
                    response.put("isLocal", true);
                    response.put("site", model.getSite());
                    response.put("photoPath", model.getPhotoPath());
                    currentLatLonPhotoList = model.getLatLonPhotoList();
                    callback.call(true, response);
                    return null;
                });
            }
            if (topActivity != null) {
                MediaStoreHelper.cancelImageGuideScanLoadSupport(topActivity);
            }
        }

        @Override
        protected void onTaskError(Exception e) {
            //请求接口
            cancelTask(configTask);
            configTask = new ConfigTask(1, callback);
            MomoTaskExecutor.executeUserTask("UDPersonalFeedHandler", configTask);
            if (topActivity != null){
                MediaStoreHelper.cancelImageGuideScanLoadSupport(topActivity);
            }
        }
    }

    public class ConfigTask extends MomoTaskExecutor.Task<Object, Object, GuideConfig> {
        private int type;
        LVCallback callback;

        public ConfigTask(int type, LVCallback callback) {
            super();
            this.type = type;
            this.callback = callback;

        }

        @Override
        protected GuideConfig executeTask(Object... params) throws Exception {
            GetGuideConfigApi api = new GetGuideConfigApi();
            return api.getGuideConfig(this.type);
        }


        @Override
        protected void onTaskSuccess(GuideConfig result) {
            if (result != null) {
                getJustOneImageOrVideo(result.getStat_key(),callback,result.getIcon(),result.getTitle(),result.getDescription(),result.getGoto_url());
            }
        }

    }


    @LuaBridge
    public void onDestroy() {
        MediaStoreHelper.cancelImageGuideScanLoadSupport(this.topActivity);
        MediaStoreHelper.cancelFirstImageAndVideoLoad(this.topActivity);
        MomoTaskExecutor.cancleAllTasksByTag("UDPersonalFeedHandler");
        this.topActivity = null;
    }


}
