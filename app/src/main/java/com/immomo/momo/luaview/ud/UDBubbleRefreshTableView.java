package com.immomo.momo.luaview.ud;

import android.text.TextUtils;
import android.view.ViewGroup;

import com.immomo.mls.fun.ud.UDColor;
import com.immomo.mls.fun.ud.UDMap;
import com.immomo.mls.fun.ud.view.recycler.UDBaseRecyclerAdapter;
import com.immomo.mls.fun.ud.view.recycler.UDBaseRecyclerLayout;
import com.immomo.mls.fun.ud.view.recycler.UDRecyclerView;
import com.immomo.mls.fun.ui.OnLoadListener;
import com.immomo.mls.utils.AssertUtils;
import com.immomo.mls.utils.ErrorUtils;
import com.immomo.momo.luaview.LuaBubbleRefreshTableView;
import com.immomo.momo.luaview.weight.IBubbleRefreshRecyclerView;

import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaNumber;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import java.util.Map;

/**
 * Created by zhang.ke
 * on 2019/3/15
 */
@LuaApiUsed
public class UDBubbleRefreshTableView<T extends ViewGroup & IBubbleRefreshRecyclerView & OnLoadListener,
        A extends UDBaseRecyclerAdapter, L extends UDBaseRecyclerLayout> extends UDRecyclerView<T, A, L> {
    public static final String LUA_CLASS_NAME = "BubbleRefreshTableView";
    public static final String[] methods = {
            "refreshBubbleEnable",
            "setBubbleRefreshingCallback",
            "setCanBubbleRefreshCallback",
            "setRefreshingCallback",
            "setRefreshBgColor",
            "setRefreshSvgaPath",
            "setUpdatePercentCallback",
            "setSwitchBequietCallback",
            "showSwitchBequietGuide"
    };

    private LuaFunction bubbleRefreshingCallback;
    private LuaFunction canBubbleRefreshCallback;
    private LuaFunction updatePercentCallback;
    private LuaFunction switchBequietCallback;

    protected LuaBubbleRefreshTableView refreshTableView;

    @LuaApiUsed
    public UDBubbleRefreshTableView(long L, LuaValue[] initParams) {
        super(L, initParams);
    }

    @Override
    protected T newView(LuaValue[] init) {
        boolean loadEnable = false;
        boolean bubbleEnable = false;
        if (init.length > 0) {
            mRefreshEnabled = init[0].toBoolean();
        }
        if (init.length > 1) {
            bubbleEnable = init[1].toBoolean();
        }

        if (init.length > 2) {
            loadEnable = init[2].toBoolean();
        }
        refreshTableView = new LuaBubbleRefreshTableView(getContext(), this, mRefreshEnabled, bubbleEnable, loadEnable);
        return (T) refreshTableView;
    }

    @LuaApiUsed
    public LuaValue[] refreshBubbleEnable(LuaValue[] enable) {
        getView().refreshBubbleEnable(enable.length > 0 && enable[0].toBoolean());
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setRefreshBgColor(LuaValue[] var) {
        if (var.length == 1 && AssertUtils.assertUserData(var[0], UDColor.class, "bgColor", getGlobals())) {
            getView().setRefreshBgColor(((UDColor) var[0]).getColor());
            var[0].destroy();
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setRefreshSvgaPath(LuaValue[] var) {
        String enterPath = null;
        String loopPath = null;
        if (var.length == 2) {
            enterPath = var[0].isNil() ? "" : var[0].toJavaString();
            loopPath = var[1].isNil() ? "" : var[1].toJavaString();
        }
        if (!TextUtils.isEmpty(enterPath) && !TextUtils.isEmpty(loopPath)) {
            getView().setRefreshSvgaPath(enterPath, loopPath);
            return null;
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setBubbleRefreshingCallback(LuaValue[] fun) {
        bubbleRefreshingCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setCanBubbleRefreshCallback(LuaValue[] fun) {
        canBubbleRefreshCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setRefreshingCallback(LuaValue[] fun) {
        super.setRefreshingCallback(fun);
        ErrorUtils.debugUnsupportError("BubbleRefreshTableView use setBubbleRefreshingCallback instead of setRefreshingCallback");
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setUpdatePercentCallback(LuaValue[] fun) {
        updatePercentCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setSwitchBequietCallback(LuaValue[] fun) {
        switchBequietCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] showSwitchBequietGuide(LuaValue[] fun) {
        if (refreshTableView != null) {
            refreshTableView.showGuide(fun.length > 0 ? fun[0].toInt() : 1);
        }
        return null;
    }

    public void callOnRefresh(int var1, Map bubbleParams) {
        if (bubbleRefreshingCallback != null) {
            bubbleRefreshingCallback.invoke(LuaValue.varargsOf(LuaNumber.valueOf(var1), UDMap.G.newInstance(globals, bubbleParams)));
        }
    }

    public boolean callCanBubbleRefresh() {
        if (canBubbleRefreshCallback != null) {
            LuaValue[] luaValues = canBubbleRefreshCallback.invoke(null);
            return luaValues[0].isBoolean() ? luaValues[0].toBoolean() : true;
        }
        return true;
    }

    public void callUpdatePercent(float percent, int animType) {
        if (updatePercentCallback != null) {
            updatePercentCallback.invoke(LuaValue.varargsOf(LuaNumber.valueOf(percent), LuaNumber.valueOf(animType)));
        }
    }

    public void callSwitchBequiet() {
        if (switchBequietCallback != null) {
            switchBequietCallback.invoke(null);
        }
    }
}
