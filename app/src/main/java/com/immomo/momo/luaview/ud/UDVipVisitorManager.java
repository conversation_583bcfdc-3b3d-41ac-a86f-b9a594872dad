package com.immomo.momo.luaview.ud;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import com.alibaba.fastjson.JSONObject;
import com.alipay.sdk.app.PayTask;
import com.immomo.android.router.pay.PayConst;
import com.immomo.framework.rxjava.interactor.CommonSubscriber;
import com.immomo.mls.InitData;
import com.immomo.mls.MLSBundleUtils;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmutil.task.MMThreadExecutors;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.MomoKit;
import com.immomo.momo.UrlConstant;
import com.immomo.momo.feed.VipSeeHelper;
import com.immomo.momo.feed.service.FeedReadService;
import com.immomo.momo.feed.util.VideoPlaySelectUtil;
import com.immomo.momo.feedlist.helper.GotoLuaFeedDetailHelper;
import com.immomo.momo.innergoto.helper.NavigateHelper;
import com.immomo.momo.luaview.LuaViewVideoActivity;
import com.immomo.momo.mvp.visitme.VipVisitorLuaHelper;
import com.immomo.momo.mvp.visitme.apt.VipVisitorV2ConfigGetter;
import com.immomo.momo.mvp.visitme.services.VideoVisitorService;
import com.immomo.momo.pay.PayVipBootHelper;
import com.immomo.momo.plugin.alipay.AlipayStatusCode;
import com.immomo.momo.router.ProfileGotoOptions;
import com.immomo.momo.router.ProfileRouter;
import com.immomo.momo.service.user.UserService;

import java.util.HashMap;
import java.util.Map;

import info.xudshen.android.appasm.AppAsm;
import io.reactivex.Flowable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

@LuaClass(isStatic = true)
public class UDVipVisitorManager {
    public static final String LUA_CLASS_NAME = "VipVisitorManager";

    /**
     * 获取我看过谁AB实验分组
     */
    @LuaBridge
    public static int getSeePayType() {
       return VipVisitorV2ConfigGetter.get().vipSeePayType();
    }


    /**
     * 获取我看过谁AB实验分组
     */
    @LuaBridge
    public static void gotoProfile(String momoId,String type) {
        VipVisitorLuaHelper.INSTANCE.setCurrentType(type);
        ProfileGotoOptions options = new ProfileGotoOptions(momoId);
        options.setFrom("1003403");
        AppAsm.getRouter(ProfileRouter.class).gotoProfile(MomoKit.getTopActivity(), options);
    }

    /**
     * 从那个cell进入
     */
    @LuaBridge
    public static void setCurrentType(String type) {
        VipVisitorLuaHelper.INSTANCE.setCurrentType(type);
    }

    /**
     * 获取动态支付分组
     */
    @LuaBridge
    public static int getFeedPayType() {
        return VipVisitorV2ConfigGetter.get().vipFeedPayType();
    }

    /**
     * 获取装扮支付
     */
    @LuaBridge
    public static void gotoDressPay() {
        NavigateHelper.startPassportWebview(MomoKit.getTopActivity(), UrlConstant.URL_BUY_VIP);
    }

    /**
     * 跳转支付
     */
    @LuaBridge
    public static void gotoPay(int source) {
        PayVipBootHelper.startPayVipPage(MomoKit.getTopActivity(), PayConst.VipType.SVIP_TYPE, source);
    }

    /**
     * 唤起支付宝弹窗
     */
    @LuaBridge
    public static void signAliPayWithhold(String signStr, LVCallback callback) {
        try {
            Activity context = MomoKit.getTopActivity();
            Disposable payDisposable = Flowable.fromCallable(() -> {
                        PayTask payTask = new PayTask(context);
                        return payTask.payV2(signStr, true);
                    })
                    .subscribeOn(Schedulers.from(MMThreadExecutors.INSTANCE.getUser()))
                    .observeOn(MMThreadExecutors.INSTANCE.getMain().getScheduler())
                    .subscribeWith(new CommonSubscriber<Map<String, String>>() {
                        @Override
                        public void onNext(Map<String, String> payResult) {
                            super.onNext(payResult);
                            if (payResult != null) {
                                if (payResult.containsKey("resultStatus")) {
                                    String resultStatus = payResult.get("resultStatus");
                                    if (AlipayStatusCode.PAY_STATUS_SUCCESS.equals(resultStatus)) {
                                        Toaster.show("支付中，结果请留意通知");
                                    }
                                    callback.call(resultStatus);
                                }
                            }
                        }
                    });
        } catch (Exception e) {
        }
    }


    /**
     * 跳转动态详情
     *
     * @param feedId
     */
    @LuaBridge
    public static void gotoFeedProfileCommonFeedActivity(String feedId) {
        GotoLuaFeedDetailHelper.INSTANCE.gotoFeeDetailLuaActivity(MomoKit.getTopActivity(), feedId);
    }

    /**
     * 视频播放页
     *
     * @param feedId
     */
    @LuaBridge
    public static void gotoVideoPlay(String feedId) {
        isVideoLuaPage(MomoKit.getTopActivity(), feedId);
    }

    private static void isVideoLuaPage(Context context, String feedId) {
        Intent intent = new Intent(context, LuaViewVideoActivity.class);
        Bundle data = new Bundle();
        Map<String, Object> map = new HashMap<>();
        HashMap<String, Object> param = new HashMap<>();

        map.put("extraJumpType", "WHO_LOOK_ME_FEED");
        map.put("feedId", feedId);
        param.put("param", JSONObject.toJSONString(map));
        InitData initData = MLSBundleUtils.createInitData(VideoPlaySelectUtil.INSTANCE.getLuaVideoUrl());
        data.putParcelable(MLSBundleUtils.KEY_INIT_DATA, initData);
        initData.extras = param;
        intent.putExtras(data);
        LuaViewVideoActivity.startActivityFromBottom(context, initData, intent);
    }



    /**
     * 根据type消除一个条目
     * @param type
     * @param feedId
     * @param momoId
     */
    @LuaBridge
    public static void removeSingleByType(String type,String feedId, String momoId) {
        switch (type) {
            case "profile":
                UserService.getInstance().clearSingleVisitor(momoId);
                int count = UserService.getInstance().getVisitorsCount();
                UserService.getInstance().saveVisitorsCount(--count);
                break;
            case "feed":
                FeedReadService.getInstance().clearSingleVisitor(feedId);
                break;
            case "video":
                VideoVisitorService.getInstance().removeSingeVistor(feedId);
                break;
            case "see":
                int seeCount = VipSeeHelper.INSTANCE.getSeeCount();
                if (seeCount > 0) {
                    seeCount = seeCount - 1;
                }
                VipSeeHelper.INSTANCE.saveSeeCount(seeCount);
                break;
            case "chat":
                int chatFansCount = VipSeeHelper.INSTANCE.getChatFansCount();
                if (chatFansCount > 0) {
                    chatFansCount = chatFansCount - 1;
                }
                VipSeeHelper.INSTANCE.saveChatFansCount(chatFansCount);
                break;
            case "match":
                int matchUserCount = VipSeeHelper.INSTANCE.getMatchUserCount();
                if (matchUserCount > 0) {
                    matchUserCount = matchUserCount - 1;
                }
                VipSeeHelper.INSTANCE.saveMatchUserCount(matchUserCount);
                break;
        }

    }

    /**
     * 根据type跟新访客数量
     * @param count
     * @param type
     */
    @LuaBridge
    public static void updateCountByType(int count, String type) {
        switch (type) {
            case "profile":
                UserService.getInstance().updateVisitorCount(count);
                break;
            case "feed":
                FeedReadService.getInstance().updateVisitorCount(count);
                break;
            case "video":
                VideoVisitorService.getInstance().updateVisitorCount(count);
                break;
            case "see":
                VipSeeHelper.INSTANCE.saveSeeCount(count);
                break;
            case "chat":
                VipSeeHelper.INSTANCE.saveChatFansCount(count);
                break;
            case "match":
                VipSeeHelper.INSTANCE.saveMatchUserCount(count);
                break;
        }
    }

    /**
     * 根据type清除访客数量
     * @param type
     */
    @LuaBridge
    public static void clearCountByType(String type) {
        switch (type) {
            case "profile":
                UserService.getInstance().clearVisitors();
                UserService.getInstance().saveVisitorsCount(0);
                UserService.getInstance().saveNewVisitorsCount(0);
                break;
            case "feed":
                FeedReadService.getInstance().clearAllFeedRead();
                break;
            case "video":
                VideoVisitorService.getInstance().clearVideoUserAll();
                break;
            case "see":
                VipSeeHelper.INSTANCE.saveSeeCount(0);
                break;
            case "chat":
                VipSeeHelper.INSTANCE.saveChatFansCount(0);
                VipSeeHelper.INSTANCE.saveNewChatFansCount(0);
                break;
            case "match":
                VipSeeHelper.INSTANCE.saveMatchUserCount(0);
                VipSeeHelper.INSTANCE.saveNewMatchUserCount(0);
                break;
        }
    }

    /**
     * 根据type获取访客熟练
     * @param type
     * @return
     */
    @LuaBridge
    public static int getVisitorCountByType(String type) {
        switch (type) {
            case "profile":
                return UserService.getInstance().getVisitorsCount();
            case "feed":
                return FeedReadService.getInstance().getVisitorsCount();
            case "video":
                return VideoVisitorService.getInstance().getVisitorsCount();
            case "see":
                return VipSeeHelper.INSTANCE.getSeeCount();
            case "chat":
                return VipSeeHelper.INSTANCE.getChatFansCount();
            case "match":
                return VipSeeHelper.INSTANCE.getMatchUserCount();
        }
        return 0;
    }

    /**
     * 根据type获取最新的访客数量
     * @param type
     * @return
     */
    @LuaBridge
    public static int getNewVisitorCountByType(String type) {
        switch (type) {
            case "profile":
                return UserService.getInstance().getNewVisitorsCount();
            case "feed":
                return FeedReadService.getInstance().getNewVisitorsCount();
            case "video":
                return VideoVisitorService.getInstance().getNewVistorsCount();
            case "chat":
                return VipSeeHelper.INSTANCE.getNewChatFansCount();
            case "match":
                return VipSeeHelper.INSTANCE.getNewMatchUserCount();
        }
        return 0;
    }

    /**
     * 根据type清除新访客数量
     * @param type
     */
    @LuaBridge
    public static void clearNewVisitorCountByType(String type) {
        switch (type) {
            case "profile":
                UserService.getInstance().saveNewVisitorsCount(0);
                break;
            case "feed":
                FeedReadService.getInstance().saveNewVisitorsCount(0);
                break;
            case "video":
                VideoVisitorService.getInstance().saveNewVistorsCount(0);
                break;
            case "chat":
                VipSeeHelper.INSTANCE.saveNewChatFansCount(0);
                break;
            case "match":
                VipSeeHelper.INSTANCE.saveNewMatchUserCount(0);
                break;
        }
    }

}