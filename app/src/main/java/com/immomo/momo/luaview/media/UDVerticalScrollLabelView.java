package com.immomo.momo.luaview.media;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.immomo.framework.utils.UIUtils;
import com.immomo.mls.fun.ud.UDArray;
import com.immomo.mls.fun.ud.UDColor;
import com.immomo.mls.fun.ud.view.UDView;
import com.immomo.mls.util.DimenUtil;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.momo.R;
import com.immomo.momo.android.view.scrolllayout.ScrollLayout;

import org.luaj.vm2.LuaNumber;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import java.util.List;

/**
 * author: hongming.wei
 * data: 2023/3/16
 */
@LuaApiUsed
public class UDVerticalScrollLabelView <V extends ScrollLayout> extends UDView<V> implements Runnable {

    public static final String LUA_CLASS_NAME = "VerticalScrollLabelView";

    public static final String[] methods = {
            "setFontSize",
            "setTextColor",
            "setDelay",
            "setEdgeMargin",
            "setTextShowColor",
            "setTextArrayAndTimes",
            "startAnimation",
            "stopAnimation",
            "setMaxWidth"
    };

    private int postTag = this.hashCode();
    private float textSize = 12f;
    private UDColor textColor;
    private long delayMill = 1000;
    private List<String> descList;
    private int descIndex = 0;
    private int mMaxViewWidth = 0;
    @LuaApiUsed
    protected UDVerticalScrollLabelView(long L, LuaValue[] v) {
        super(L, v);
    }

    @NonNull
    @Override
    protected V newView(@NonNull LuaValue[] init) {
        return (V) LayoutInflater.from(getContext()).inflate(R.layout.layout_vertical_scroll_label_view, null);
    }

    @LuaApiUsed
    public LuaValue[] setFontSize(LuaValue[] values){
        if (values != null && values.length > 0) {
            textSize = values[0].toFloat();
            return null;
        }
        return varargsOf(LuaNumber.valueOf(DimenUtil.pxToSp(textSize)));
    }

    @LuaApiUsed
    public LuaValue[] setTextColor(LuaValue[] values){
        if (values != null && values.length > 0) {
            textColor = (UDColor) values[0];
            return null;
        }
        UDColor ret = new UDColor(getGlobals(), 0);
        return varargsOf(ret);
    }

    @LuaApiUsed
    public LuaValue[] setDelay(LuaValue[] values){
        if (values != null && values.length > 0) {
            delayMill = (long) (values[0].toFloat() * 1000);
            return null;
        }
        return varargsOf(LuaNumber.valueOf(delayMill));
    }

    @LuaApiUsed
    public LuaValue[] setEdgeMargin(LuaValue[] values) {
        return null;
    }    


    @LuaApiUsed
    public LuaValue[] setTextShowColor(LuaValue[] values) {
        return null;
    }


    @LuaApiUsed
    public LuaValue[] setTextArrayAndTimes(LuaValue[] values){
        cancelDescRunnable();
        if (values != null && values.length > 0) {
            if (values[0] instanceof UDArray) {
                descList = ((UDArray) values[0]).getArray();
                if (descList.isEmpty()) return null;
                resetVideoDescWidth(descList);
                if (descList.size() == 1) {
                    setSingleDesc(descList.get(0));
                } else {
                    setSingleDesc(descList.get(0));
                    descIndex = (descIndex + 1) % descList.size();
                }
            }
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] startAnimation(LuaValue[] values){
        MomoMainThreadExecutor.postDelayed(postTag, this, delayMill);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] stopAnimation(LuaValue[] values) {
        cancelDescRunnable();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setMaxWidth(LuaValue[] values) {
        if (values != null && values.length > 0) {
            mMaxViewWidth = UIUtils.sp2pix(values[0].toInt());
        }
        return null;
    }

    private void cancelDescRunnable(){
        MomoMainThreadExecutor.cancelSpecificRunnable(postTag, this);
    }

    private void resetVideoDescWidth(List<String> descList) {
        int maxWidth = 0;
        for (String desc: descList){
            int width = UIUtils.calculateTextWidth(desc, textSize) + UIUtils.getPixels(6f);
            if (width > maxWidth){
                maxWidth = width;
            }
            if (mMaxViewWidth > 0 && maxWidth > mMaxViewWidth) {
                maxWidth = mMaxViewWidth;
            }
        }
        ViewGroup.LayoutParams layoutParams = getView().getLayoutParams();
        layoutParams.width = maxWidth;
        getView().setLayoutParams(layoutParams);
    }

    private void setSingleDesc(String desc) {
        for (int i = 0; i < 3; i++) {
            TextView bottomText = (TextView) getView().getChildAt(i);
            if (bottomText != null) {
                bottomText.setTextSize(textSize);
                bottomText.setText(desc);
                if (textColor != null) {
                    bottomText.setTextColor(textColor.getColor());
                }
            }
        }
    }


    @Override
    public void run() {
        cancelDescRunnable();
        if (descList != null && !descList.isEmpty()) {
            if (descIndex < 0 || descIndex >= descList.size()) {
                return;
            }
            String desc = descList.get(descIndex);
            flipToNextBottomText(desc);
            descIndex = (descIndex + 1) % descList.size();
            MomoMainThreadExecutor.postDelayed(postTag, this, delayMill);
        }
    }

    private void flipToNextBottomText(CharSequence text) {
        int cur = getView().getCurrentIndex();
        int now = (cur + 1) % 4;
        View childViewAt = getView().getChildViewAt(now);
        if (childViewAt instanceof TextView) {
            ((TextView) childViewAt).setText(text);
        }
        getView().toNext(true);
    }
}
