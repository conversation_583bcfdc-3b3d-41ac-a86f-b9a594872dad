package com.immomo.momo.luaview;

import com.immomo.annotations.appconfig.AppConfigField;
import com.immomo.annotations.appconfig.appconfigv2.AppConfigV2;

@AppConfigV2
public class LuaViewConfigV2 {
    @AppConfigField(
            mark = "387",
            key = "mln_list_detect",
            defValue = "0",
            isSysValue = true
    )
    int checkWhiteScreenEnable = 0;

    @AppConfigField(
            mark = "387",
            key = "mln_list_detect_timeinterval",
            defValue = "0",
            isSysValue = true
    )
    int checkWhiteScreenInterval = 0;

    @AppConfigField(
            mark = "387",
            key = "mln_list_detect_time_limite",
            defValue = "0",
            isSysValue = true
    )
    int checkWhiteScreenLimit = 0;

}