package com.immomo.momo.luaview.lt;

import android.content.Context;
import android.content.Intent;

import com.immomo.framework.task.BaseDialogTask;
import com.immomo.mls.MLSEngine;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmutil.StringUtils;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.MomoKit;
import com.immomo.momo.android.broadcast.FriendNoticeReceiver;
import com.immomo.momo.globalevent.GlobalEventManager;
import com.immomo.momo.protocol.http.FriendNoticeApi;
import com.immomo.momo.service.sessions.SessionService;

import java.util.HashMap;
import java.util.Map;

import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;

import static com.immomo.momo.globalevent.GlobalEventManager.EVN_LUA;


/**
 * Created by li.mengnan
 * on 2020/07/21
 * 好友提醒的Lua桥接帮助类，桥接类不可以使用kotiln编写，不然程序运行会有问题
 */
@LuaClass(isStatic = true)
public class LTFriendNoticeManager {
    //lua 桥接名称
    public static final String LUA_CLASS_NAME = "FriendNoticeManager";
    //lua广播协议 - 关注视频红人
    public static final String ACTION_FOLLOW = "NTF_FRIEND_NOTICE_FOLLOW";
    //lua广播协议 - 取消关注视频红人
    public static final String ACTION_UNFOLLOW = "NTF_FRIEND_NOTICE_UNFOLLOW";
    //操作状态key
    public static final String KEY_ACTION_STATUS = "action_status";//0成功   1失败
    //红人id
    public static final String KEY_HOT_ID = "id";

    private static Context getContext() {
        return MLSEngine.getContext();
    }

    /**
     * 通知端侧，全部新好友提醒已读，清除提醒
     * todo
     */
    @LuaBridge
    public static void readAll() {
        /*
         * step 1    清空friendNoticeDao里未读状态
         * step 2    处理消息帧好友入口红点或者气泡
         * */
        MomoTaskExecutor.executeUserTask(LUA_CLASS_NAME, new ClearAllNoticeTask());
    }

    /**
     * 删除好友提醒
     *
     * @param noticeIds  消息ids
     * @param isLast     是否为最后一条消息数据
     * @param nextNotice 当删除首条消息时，返回第二条消息数据
     * @param callback
     */
    @LuaBridge
    public static void delRemind(String noticeIds, boolean isLast, Map nextNotice, LVCallback callback) {
        MomoTaskExecutor.executeUserTask(LUA_CLASS_NAME, new DelRemindTask(noticeIds, isLast, nextNotice, callback));
    }

    /**
     * 删除好友提醒的任务
     */
    static class DelRemindTask extends BaseDialogTask<Void, Void, Boolean> {
        @NonNull
        private String noticeIds;
        @NonNull
        private LVCallback callback;
        private boolean isLast;
        private Map nextNotice;

        public DelRemindTask(String noticeIds, boolean isLast, Map nextNotice, LVCallback callback) {
            this.noticeIds = noticeIds;
            this.callback = callback;
            this.isLast = isLast;
            this.nextNotice = nextNotice;
        }

        @CallSuper
        @Override
        protected Boolean executeTask(Void... params) throws Exception {
            boolean success = FriendNoticeApi.getInstance().delRemind(noticeIds, 0);

            if (success) {
                if (nextNotice != null && !nextNotice.isEmpty()) {
                    // 消息内容
                    String content = (String) nextNotice.get("content");
                    SessionService.getInstance().updateFriendNoticeSession(content);
                }
                //最后一条数据，删除好友提醒入口
                if (isLast) {
                    SessionService.getInstance().clearFriendNotice();
                }
            }
            return success;
        }

        @Override
        protected void onTaskSuccess(Boolean success) {
            //删除成功回调
            callback.call(true);

            if (success) {
                Intent intent = new Intent(FriendNoticeReceiver.ACTION_FRIEND_NOTICE_REFRESH);
                getContext().sendBroadcast(intent);
            }
        }

        @Override
        protected void onTaskError(Exception e) {
            //删除失败回调
            callback.call(false);
            super.onTaskError(e);
        }

        @Override
        protected boolean mayCancleOnTouchOutSide() {
            return false;
        }
    }

    static class ClearAllNoticeTask extends MomoTaskExecutor.Task<Void, Void, Void> {

        @Override
        protected Void executeTask(Void... voids) throws Exception {
            //清理未读数
            SessionService.getInstance().clearFriendNoticeCount();
            //移除notification
            MomoKit.getApp().removeFrendNoticeNotify();
            return null;
        }

        @Override
        protected void onTaskError(Exception e) {
            // note: 不提示给用户
        }

        @Override
        protected void onTaskSuccess(Void aVoid) {
            super.onTaskSuccess(aVoid);
            //清除好友提醒未读数
            Intent intent = new Intent(FriendNoticeReceiver.ACTION_FRIEND_NOTICE_REFRESH);
            getContext().sendBroadcast(intent);
        }
    }

    /**
     * 向lua发送，关注成功的广播
     *
     * @param status
     * @param id
     */
    public static void sendLuaFollowResult(int status, String id) {
        if (StringUtils.isEmpty(id)) {
            return;
        }
        //lua广播
        GlobalEventManager.Event event = new GlobalEventManager.Event(ACTION_FOLLOW);
        event.dst(EVN_LUA);
        HashMap<String, Object> map = new HashMap<>();
        map.put(KEY_ACTION_STATUS, status);
        map.put(KEY_HOT_ID, id);
        event.msg(map);
        GlobalEventManager.getInstance().sendEvent(event);
    }

    /**
     * 向lua发送，取消关注成功的广播
     *
     * @param status
     * @param id
     */
    public static void sendLuaUnfollowResult(int status, String id) {
        if (StringUtils.isEmpty(id)) {
            return;
        }
        //lua广播
        GlobalEventManager.Event event = new GlobalEventManager.Event(ACTION_UNFOLLOW);
        event.dst(EVN_LUA);
        HashMap<String, Object> map = new HashMap<>();
        map.put(KEY_ACTION_STATUS, status);
        map.put(KEY_HOT_ID, id);
        event.msg(map);
        GlobalEventManager.getInstance().sendEvent(event);
    }

}
