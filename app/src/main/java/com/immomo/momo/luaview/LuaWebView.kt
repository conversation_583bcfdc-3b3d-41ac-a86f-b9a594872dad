package com.immomo.momo.luaview

import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.view.ViewGroup
import com.immomo.android.mm.kobalt.domain.extention.castOrNull
import com.immomo.mls.`fun`.weight.BorderRadiusFrameLayout
import com.immomo.momo.MomoKit
import com.immomo.momo.mk.MomoMKWebViewHelper
import com.immomo.momo.mk.bridges.GlobalEventBridge
import immomo.com.mklibrary.core.base.ui.MKWebView
import immomo.com.mklibrary.core.ui.SetUIBtnParams
import immomo.com.mklibrary.core.ui.SetUIParams
import immomo.com.mklibrary.mwc.MWCHolderBundle
import immomo.com.mklibrary.prerender.MKCachePoolManager

/**
 * CREATED BY liu.chong
 * AT 2025/7/9
 */
class LuaWebView @JvmOverloads constructor(
    context: Context
) : BorderRadiusFrameLayout(context) {
    var momoMKWebViewHelper: MomoMKWebViewHelper? = null
    var webView: MKWebView? = null
    fun loadUrl(url: String) {
        val ac = context.castOrNull<Activity>()
            ?: throw IllegalStateException("LuaWebView must be attached to an Activity.url:$url")
        if (webView != null) {
            webView?.loadUrl(url)
            return
        }
        val webView = MKCachePoolManager.pop(ac, url)
            ?: MKWebView(ac, MWCHolderBundle.create(url))
        webView.setBackgroundColor(Color.TRANSPARENT)
        addView(
            webView,
            ViewGroup.LayoutParams(
                LayoutParams.MATCH_PARENT,
                LayoutParams.MATCH_PARENT
            )
        )
        this.webView = webView

        momoMKWebViewHelper =
            object : MomoMKWebViewHelper() {
                override fun uiGoBack() {
                }

                override fun uiSetTitle(title: String?) {
                }

                override fun uiShowHeaderBar(show: Boolean) {
                }

                override fun uiSetUI(uiParams: SetUIParams?) {
                }

                override fun uiSetUIButton(params: SetUIBtnParams?) {
                }

                override fun clearRightButton() {
                }

                override fun closePage() {
                }

            }
        momoMKWebViewHelper?.bindActivity(ac, webView)
        momoMKWebViewHelper?.initWebView(MomoKit.getMKUserAgent(), url)
        momoMKWebViewHelper?.setCustomBridge(GlobalEventBridge(webView))
        webView.loadUrl(url)
    }

    fun onResume() {
        momoMKWebViewHelper?.onPageResume()
    }

    fun onPause() {
        momoMKWebViewHelper?.onPagePause()
    }
    fun destroy() {
        momoMKWebViewHelper?.onPageDestroy()
        webView = null
        momoMKWebViewHelper = null
    }
}