package com.immomo.momo.luaview.lt;

import android.content.Intent;

import com.immomo.android.login.auth.view.AuthDevicePhoneActivity;
import com.immomo.android.login.router.LoginRouter;
import com.immomo.framework.location.UserLocationUtils;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.momo.MomoKit;
import com.immomo.momo.util.device.DeviceIdHelper;

import java.util.HashMap;
import java.util.Map;

import info.xudshen.android.appasm.AppAsm;

/**
 * des 登陆保护桥接
 * author zs
 * date 2022/3/4
 */
@LuaClass(isStatic = true)
public class LTLoginSafeManager {

    public static final String LUA_CLASS_NAME = "LoginSafeManager";

    @LuaBridge
    public static Map<String, String> getSecurityParams() {
        Map<String, String> map = new HashMap<>();
        map.put("lat", UserLocationUtils.getLatitude() + "");
        map.put("lng", UserLocationUtils.getLongitude() + "");
        map.put("acc", UserLocationUtils.getAccuracy() + "");
        map.put("uid", DeviceIdHelper.getDeviceId());
        if (UserLocationUtils.getLocationCacheTime() > 0) {
            map.put("locationCacheTime", UserLocationUtils.getLocationCacheTime() / 1000 + ""); // 新增定位时间
        }
        return map;
    }

    @LuaBridge
    public static Map<String, Double> getSecurityDoubleParams() {
        Map<String, Double> map = new HashMap<>();
        map.put("lat", UserLocationUtils.getLatitude());
        map.put("lng", UserLocationUtils.getLongitude());
        return map;
    }

    @LuaBridge
    public static void setAccreditDevice(int enable) {
        AppAsm.getRouter(LoginRouter.class).setAccreditDevice(enable);
    }

    @LuaBridge
    public static void verifyDevice(String phoneNumber) {
        Intent it = new Intent(MomoKit.getContext(), AuthDevicePhoneActivity.class);
        it.putExtra(AuthDevicePhoneActivity.KEY_PHONE, phoneNumber);
        it.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        MomoKit.getContext().startActivity(it);
    }

}
