package com.immomo.momo.luaview.lt;

import android.location.Location;

import com.immomo.framework.location.LocaterType;
import com.immomo.framework.location.LocationCallBack;
import com.immomo.framework.location.LocationClient;
import com.immomo.framework.location.LocationResultCode;
import com.immomo.framework.location.LocationUtil;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.momo.LocationClientSetter;

import java.util.Map;

/**
 * Created by zhang.ke on 2018/9/20.
 */
@LuaClass(isStatic = true)
public class LTVChatLocationManager {
    public static final String LUA_CLASS_NAME = "VChatLocationManager";

    //<editor-fold desc="API">
    @LuaBridge
    public static void getLocation(LVCallback callback) {
        try {
            LocationClient.getLocationInQueue(LocationClientSetter.LocationMemoryCache.TYPE_OTHER, new C(callback));
        } catch (Exception e) {
            callback.call(-1, -1);
        }
    }

    @LuaBridge
    public static void getLocationWithCache(Map option, LVCallback callback) {
        if (option == null || option.isEmpty())
            return;
        int type = (int) option.get("type");

        try {
            LocationClient.getLocationInQueue(type, new C(callback));
        } catch (Exception e) {
            callback.call(-1, -1);
        }
    }

    private static final class C implements LocationCallBack {
        final LVCallback callback;

        C(LVCallback callback) {
            this.callback = callback;
        }

        @Override
        public void callback(final Location loc, boolean correctLocType, final LocationResultCode resultCode, LocaterType locaterType) {
            MomoMainThreadExecutor.post(new Runnable() {
                @Override
                public void run() {
                    if (resultCode == LocationResultCode.RESULT_CODE_CANCEL) {
                        callback.call(-1, -1);
                        return;
                    }
                    if (LocationUtil.isAvailableLocation(loc)) {
                        callback.call((float) loc.getLatitude(), (float) loc.getLongitude());
                    } else {
                        callback.call(-1, -1);
                    }
                }
            });
        }
    }
}
