package com.immomo.momo.luaview.lt;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.cosmos.mdlog.MDLog;
import com.immomo.android.router.share.ShareDialogConfig;
import com.immomo.android.router.share.ShareRouter;
import com.immomo.android.router.share.model.PageConfig;
import com.immomo.mls.LuaViewManager;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.fun.ud.UDMap;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmstatistics.BuildConfig;
import com.immomo.momo.LogTag;
import com.immomo.momo.mk.share.bean.MKSharePannel;
import com.immomo.momo.share2.IShareDialog;
import com.immomo.momo.share2.listeners.MKShareClickListener;
import com.immomo.momo.util.BroadcastHelper;
import com.immomo.momo.util.GsonUtils;
import com.immomo.momo.util.WebShareParams;
import com.immomo.momo.webview.util.WebShareReceiver;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.luaj.vm2.Globals;
import org.luaj.vm2.LuaValue;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import info.xudshen.android.appasm.AppAsm;

/**
 * Created by guo.lei on 2019-07-22.
 */
@LuaClass
public class SISharehandler {

    public static final String LUA_CLASS_NAME = "LuaShareHandler";
    protected Globals globals;
    protected Context context;

    private MKSharePannel pannel;
    private IShareDialog dialog;
    private LVCallback shareCallback;

    public SISharehandler(Globals globals, LuaValue[] init) {
        this.globals = globals;
        this.context = ((LuaViewManager) globals.getJavaUserdata()).context;
    }

    public void __onLuaGc() {
        BroadcastHelper.unregisterBroadcast(context, broadcastReceiver);
    }

    @LuaBridge
    public void shareWithParam(Map map, LVCallback shareCallback) {
        if ((map == null || map.isEmpty())
                && BuildConfig.DEBUG) {
            MDLog.w(SISharehandler.class.getSimpleName(), "table can't be null or empty.");
            return;
        }

        this.shareCallback = shareCallback;
        // 获取分享参数
        try {
            showSharePanel(new JSONObject(GsonUtils.g().toJson(map)));
        } catch (JSONException e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
    }

    private BroadcastReceiver broadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (WebShareReceiver.ACTION.endsWith(intent.getAction())) {
                if (shareCallback != null) {
                    Map<String,String> result = new HashMap();
                    try {
                        result.put("app", intent.getStringExtra(WebShareReceiver.KEY_CALLBACK_APP));
                        result.put("status", intent.getStringExtra(WebShareReceiver.KEY_CALLBACK_STATUS));
                        result.put("message", intent.getStringExtra(WebShareReceiver.KEY_CALLBACK_MESSAGE));
                    } catch (Exception e) {
                        MDLog.printErrStackTrace(LogTag.COMMON, e);
                    }

                    shareCallback.call(new UDMap(globals,result));
                }
            }
        }
    };

    private void showSharePanel(JSONObject jsonObject) throws JSONException {
        if (jsonObject == null) {
            return;
        }

        BroadcastHelper.registerBroadcast(context,
                broadcastReceiver,
                WebShareReceiver.ACTION
        );

        String linkUrl = jsonObject.optString("url");
        String textContent = jsonObject.optString("text");
        String picUrl = jsonObject.optString("pic");
        String shareTitle = jsonObject.optString("title");

        String callBack = jsonObject.optString("callback");
        String web_source = jsonObject.optString("web_source");

        JSONArray pics_array = jsonObject.optJSONArray("pics");
        List<String> pics = new ArrayList<>();
        if ((pics_array != null) && (pics_array.length() > 0)) {
            for (int i = 0; i < pics_array.length(); i++) {
                pics.add(pics_array.getString(i));
            }
        }

        JSONArray jarray = jsonObject.optJSONArray("apps");
        List<String> apps = new ArrayList<>();
        if ((jarray != null) && (jarray.length() > 0)) {
            for (int i = 0; i < jarray.length(); i++) {
                apps.add(jarray.getString(i));
            }
        }

        Map<String, WebShareParams> shareParamsMap = new HashMap<>();
        if (apps.size() > 0) {
            JSONObject appConifgsJson = jsonObject.optJSONObject("configs");
            if (null != appConifgsJson) {
                for (String appStr : apps) {
                    JSONObject appConfig = appConifgsJson.optJSONObject(appStr);
                    if (appConfig != null) {
                        WebShareParams params = new WebShareParams();
                        params.shareUrl = appConfig.optString("url");
                        params.shareText = appConfig.optString("text");
                        params.sharePicUrl = appConfig.optString("pic");
                        params.shareTitle = appConfig.optString("title");
                        if (appConfig.has("resource")) {
                            params.resource = appConfig.optJSONObject("resource").toString();
                        }
                        params.shareMode = appConfig.optInt("sdk");
                        params.defaultContent = appConfig.optString("sdk_text");
                        params.callBack = callBack;
                        params.webSource = web_source;
                        params.shareType = appConfig.optString("share_type");
                        shareParamsMap.put(appStr, params);
                    }
                }
            }
        }

        WebShareParams defaultParams = new WebShareParams();
        defaultParams.shareUrl = linkUrl;
        defaultParams.callBack = callBack;
        defaultParams.sharePicUrl = picUrl;
        defaultParams.shareText = textContent;
        defaultParams.shareTitle = shareTitle;
        defaultParams.sharePicUrls = pics;
        defaultParams.shareMode = jsonObject.optInt("sdk");
        defaultParams.defaultContent = jsonObject.optString("sdk_text");
        defaultParams.webSource = web_source;

        pannel = new MKSharePannel();
        pannel.apps = apps;
        pannel.defaultMkShareParams = defaultParams;
        pannel.shareParamsMap = shareParamsMap;
        if (null != dialog && dialog.isShowing()) {
            dialog.dismiss();
        }

        MKShareClickListener listener = new MKShareClickListener((Activity) context, pannel);
        listener.setOnCheckResultListener((callback, em) -> {
        });

        ShareDialogConfig config = new ShareDialogConfig.Builder(context)
                .mShareListener(listener)
                .mPageConfig(new PageConfig.Builder().supportDark(true).apps(pannel.apps).build())
                .build();

        dialog = AppAsm.getRouter(ShareRouter.class).showShareDialog(config);
    }
}
