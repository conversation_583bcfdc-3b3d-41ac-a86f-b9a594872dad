package com.immomo.momo.luaview.ud;

import android.os.Bundle;

import com.cosmos.mdlog.MDLog;
import com.immomo.android.module.luaview.LogTag;
import com.immomo.android.module.luaview.MessageKeys;
import com.immomo.framework.account.MessageManager;
import com.immomo.mls.util.JsonUtil;
import com.immomo.momo.util.StringUtils;

import org.json.JSONObject;
import org.luaj.vm2.Globals;
import org.luaj.vm2.JavaUserdata;
import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import java.util.HashMap;

// 桥接  平台im , wiki: https://moji.wemomo.com/doc#/detail/80189
@LuaApiUsed
public class UDLuaImEvent extends JavaUserdata implements MessageManager.MessageSubscriber {

    public static final String LUA_CLASS_NAME = "LuaImEvent";

    public static final String[] methods = {
            "registerHandler",
            "unregisterHandler",
    };

    //已经废弃imjManager，请使用IMJRouter进行相关操作
//    ImjManager imjManager;

    //只有是同一个 LuaImEvent 对象的 lua Action会放在这里
    private HashMap<String, LuaFunction> mActionfunctionMap = new HashMap<>();

    @LuaApiUsed
    protected UDLuaImEvent(long L, LuaValue[] v) {
        super(L, v);
//        imjManager = MomoKit.getApp().getImjManager();

        MessageManager.registerMessageReceiver(
                this.hashCode(),
                this,
                800,
                MessageKeys.Action_LUA_IM_MESSAGE
        );
    }

    @LuaApiUsed
    public UDLuaImEvent(Globals g, Object jud) {
        super(g, jud);
    }


    //<editor-fold desc="API">
    @LuaApiUsed
    public LuaValue[] registerHandler(LuaValue[] values) {
        if (values.length >= 2) {

            String action = null;
            LuaFunction function = null;

            if (values[0].isString())
                action = values[0].toJavaString();
            if (values[1].isFunction())
                function = values[1].toLuaFunction();


            if (StringUtils.isNotEmpty(action)) {
                mActionfunctionMap.put(action, function);
            }
        }

        return null;
    }

    @LuaApiUsed
    public LuaValue[] unregisterHandler(LuaValue[] values) {
        if (StringUtils.isNotEmpty(values[0].toJavaString())) {
            mActionfunctionMap.remove(values[0].toJavaString());
        }
        return null;
    }
    //</editor-fold>


    @Override
    public boolean onMessageReceive(Bundle bundle, String action) {
        switch (action) {
            case MessageKeys.Action_LUA_IM_MESSAGE:
                if (bundle != null) {
                    String packetMsg = bundle.getString(MessageKeys.Key_Lua_IM);

                    String luaAction = bundle.getString(MessageKeys.Key_Lua_IM_ACTION);

                    if (StringUtils.isEmpty(packetMsg))
                        break;

                    for (String key : mActionfunctionMap.keySet()) {
                        if (luaAction != null && luaAction.equals(key)) {
                            // 从当前对象持有的 action 获取对应的 function
                            LuaFunction luaFunction = mActionfunctionMap.get(key);

                            JSONObject jsonObject = null;
                            try {
                                jsonObject = new JSONObject(packetMsg);
                            } catch (Exception e) {
                                MDLog.e(LogTag.LuaViewLog.LuaImEvent, "im数据异常：" + e.toString());
                            }

                            if (luaFunction != null && jsonObject != null)
                                luaFunction.invoke(varargsOf(JsonUtil.toLuaTable(globals, jsonObject)));

                        }
                    }
                }
                break;
        }
        return false;
    }

    @Override
    protected void __onLuaGc() {
        super.__onLuaGc();
        MessageManager.unregisterMessageReceiver(hashCode());
    }

}
