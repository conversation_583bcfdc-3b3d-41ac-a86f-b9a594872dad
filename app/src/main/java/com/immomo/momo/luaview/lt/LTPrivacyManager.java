package com.immomo.momo.luaview.lt;

import android.Manifest;
import android.content.Context;

import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.framework.task.BaseDialogTask;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.MomoKit;
import com.immomo.momo.luaview.LuaViewActivity;
import com.immomo.momo.mvp.nearby.PrivacyHelper;
import com.immomo.momo.permission.PermissionListener;
import com.immomo.momo.protocol.http.SettingApi;
import com.immomo.momo.router.ProfileRouter;
import com.immomo.momo.service.bean.user.BlackUser;
import com.immomo.momo.service.bean.user.ContactUser;
import com.immomo.momo.service.contacts.ContactsService;
import com.immomo.momo.service.user.UserService;
import com.immomo.momo.util.DateUtil;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import info.xudshen.android.appasm.AppAsm;

import static com.immomo.momo.protocol.http.ContactApi.parseUserSimple;

/**
 * -----------------------------------------------------------------
 * Copyright (C)  sun
 * Create: 2022/8/30 5:48 PM
 * -----------------------------------------------------------------
 */
@LuaClass(isStatic = true)
public class LTPrivacyManager {
    public static final String LUA_CLASS_NAME = "PrivacyManager";
    private static final String[] permissions = {
        Manifest.permission.READ_CONTACTS
    };

    private static Context getContext() {
        return MomoKit.getTopActivity();
    }

    @LuaBridge
    public static boolean isNewPrivacyVersionImmediate() {
        return PrivacyHelper.isNewPrivacyVersionImmediate();
    }

    @LuaBridge
    public static boolean isHideInNearbyPeople() {
        return PrivacyHelper.isHideInNearbyPeopleForbiddenImmediate();
    }

    @LuaBridge
    public static void phoneContactBlock(boolean isBlock, LVCallback callback) {
        if (hasPhonePermission()) {
            MomoTaskExecutor.executeUserTask(LUA_CLASS_NAME, new BlockContactTask(isBlock, callback));
        } else {
            checkPhonePermission(isBlock, callback);
        }
    }

    @LuaBridge
    public static void saveBlackList(Map map) {
        try{
            if (!map.containsKey("list") || map.get("list") == null) {
                return;
            }
            String strJson = map.get("list").toString();
            JSONArray jsonList = new JSONArray(strJson);
            AppAsm.getRouter(ProfileRouter.class).saveOrUpdateUser(jsonList.toString());
            List<BlackUser> list = new ArrayList<>();
            for (int i = 0; i < jsonList.length(); i++) {
                JSONObject userjson = jsonList.getJSONObject(i);
                ContactUser user = parseUserSimple(userjson);
                long blocktime = -1;
                try {
                    blocktime = DateUtil.parseStringToDateTime(userjson.optString("blocktime")).getTime();
                } catch (Exception e) {
                }
                BlackUser blackUser = new BlackUser(user.momoid, blocktime);
                blackUser.setUser(user);
                list.add(blackUser);
            }
            UserService.getInstance().addBlackUser(list);
        }catch (Exception e) {
            String aa = e.getMessage();
        }

    }

    @LuaBridge
    public static void removeBlackList(String momoId) {
        UserService.getInstance().removeBlackUser(momoId);
    }

    private static void checkPhonePermission(boolean isBlock, LVCallback callback) {
        if (!(getContext() instanceof LuaViewActivity))
            return;

        LuaViewActivity lva = (LuaViewActivity) getContext();
        lva.hasPermission(Manifest.permission.READ_CONTACTS, new PermissionListener() {

            @Override
            public void onPermissionGranted(int requestCode) {
                MomoTaskExecutor.executeUserTask(LUA_CLASS_NAME, new BlockContactTask(isBlock, callback));
            }

            @Override
            public void onPermissionDenied(int requestCode) {
                callback.call(false);
            }

            @Override
            public void onPermissionCanceled(int requestCode) {
                callback.call(false);
            }
        });
    }

    private static boolean hasPhonePermission() {
        if (!(getContext() instanceof LuaViewActivity))
            return false;

        LuaViewActivity lva = (LuaViewActivity) getContext();
        return lva.checkPermission(Manifest.permission.READ_CONTACTS);
    }

    static class BlockContactTask extends BaseDialogTask<Object, Object, Object> {
        private boolean isBlock;
        private LVCallback callback;

        public BlockContactTask(boolean isBlock, LVCallback callback) {
            this.isBlock = isBlock;
            this.callback = callback;
        }

        @Override
        protected Object executeTask(Object... params) throws Exception {
            Map<String, String> map = null;
            if (isBlock) {
                ContactsService.getInstance().clearCache();
                map = ContactsService.getInstance().findContactMap(true);
            }

            SettingApi.getInstance().blockContact(isBlock, map != null ? map.keySet() : null);
            return null;
        }

        @Override
        protected boolean mayCancleOnTouchOutSide() {
            return false;
        }

        @Override
        protected void onTaskSuccess(Object s) {
            super.onTaskSuccess(s);
            KV.saveUserValue(SPKeys.User.Setting.KEY_BLOCK_PHONE_CONTACT, isBlock ? 1 : 0);
            callback.call(true);
        }

        @Override
        protected void onTaskError(Exception e) {
            super.onTaskError(e);
            callback.call(false);
        }
    }

}
