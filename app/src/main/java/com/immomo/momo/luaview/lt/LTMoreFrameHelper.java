package com.immomo.momo.luaview.lt;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;

import com.cosmos.mdlog.MDLog;
import com.immomo.android.login.multi.util.MultiAccountConfig;
import com.immomo.mls.MLSEngine;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.util.JsonUtil;
import com.immomo.moarch.account.AccountKit;
import com.immomo.moarch.account.AccountUser;
import com.immomo.momo.MomoKit;
import com.immomo.momo.feed.VipSeeHelper;
import com.immomo.momo.feed.service.FeedReadService;
import com.immomo.momo.feed.util.BasePublishUtil;
import com.immomo.momo.maingroup.manager.FrameConfigConst;
import com.immomo.momo.mvp.maintab.mainbubble.MainBubbleHelper;
import com.immomo.momo.mvp.visitme.services.VideoVisitorService;
import com.immomo.momo.service.user.UserService;

import org.json.JSONArray;
import org.json.JSONException;
import org.luaj.vm2.LuaFunction;

import java.util.HashMap;
import java.util.Map;

import androidx.annotation.NonNull;


/**
 * <AUTHOR>
 * @date 2022/12/15 19:35
 * @desc 更多帧bridge
 * @modified
 */
@LuaClass(isStatic = true)
public class LTMoreFrameHelper {
    public static final String LUA_CLASS_NAME = "MoreFrameHelper";

    private static Context getContext() {
        return MLSEngine.getContext();
    }

    @LuaBridge
    public static Map<String,Object> moreAcountInfo() {
        try {
            org.json.JSONObject dataJson = new org.json.JSONObject();
            JSONArray accountInfoListJson = new JSONArray();
            String moreAccountAvatar = "";
            int moreAccountNums = MultiAccountConfig.INSTANCE.getPopRedPoint();
            String currentMomoid = AccountKit.getAccountManager().getCurrentAccountUserId();
            for (AccountUser user : AccountKit.getAccountManager().getAccountList()) {
                if (user != null
                        && (user.hasLogin() || !TextUtils.isEmpty(user.getLoginWithoutPwdToken()))
                        && user.getUnReadMessage() == 1
                        && user.isReceiptNotification()
                        && MomoKit.getCurrentUser() != null
                        && !TextUtils.equals(user.getId(), currentMomoid)) {
                    if (TextUtils.isEmpty(moreAccountAvatar)) {
                        moreAccountAvatar = user.getAvatar();
                    }
                    org.json.JSONObject userJson = new org.json.JSONObject();
                    userJson.put("momoId",user.getId());
                    userJson.put("userName",user.getAccountName());
                    userJson.put("avtarUrl",user.getAvatar());
                    userJson.put("alertStatus",user.isReceiptNotification() ? 1 : 0);
                    userJson.put("unreadMsgState",user.getUnReadMessage());
                    accountInfoListJson.put(userJson);
                }
            }
            dataJson.put("moreAccountAvatar",moreAccountAvatar);
            dataJson.put("moreAccountNums",moreAccountNums);
            dataJson.put("accountInfoList",accountInfoListJson);
            return JsonUtil.toMap(dataJson);
        } catch (JSONException e) {
            MDLog.printErrStackTrace(LUA_CLASS_NAME,e);
        }
        return new HashMap<>();
    }

    //谁看过我气泡数
    @LuaBridge
    public static int getVisitorsCount() {
        int newCount = UserService.getInstance().getNewVisitorsCount()
                + FeedReadService.getInstance().getNewVisitorsCount()
                + VideoVisitorService.getInstance().getNewVistorsCount()
                + VipSeeHelper.INSTANCE.getNewChatFansCount()
                + VipSeeHelper.INSTANCE.getNewMatchUserCount();
        return newCount;
    }

    @LuaBridge
    public static void clearVisitorsCount() {
        UserService.getInstance().saveNewVisitorsCount(0);
        FeedReadService.getInstance().saveNewVisitorsCount(0);
        VideoVisitorService.getInstance().saveNewVistorsCount(0);
        VipSeeHelper.INSTANCE.saveNewChatFansCount(0);
        VipSeeHelper.INSTANCE.saveNewMatchUserCount(0);
    }

    @LuaBridge
    public static void updateUnreadOnPerson() {
        MainBubbleHelper.INSTANCE.checkBubbles(FrameConfigConst.KEY_HOME_MORE);
    }

    @LuaBridge
    public static void checkFeedRelease(LuaFunction function) {
        Activity activity = MomoKit.getTopActivity();
        if (null != activity) {
            BasePublishUtil.checkFeedDraftBeforePublish(activity, new Handler.Callback() {
                @Override
                public boolean handleMessage(@NonNull Message msg) {
                    function.fastInvoke(true);
                    return false;
                }
            });
        }
    }
}
