package com.immomo.momo.luaview.ud;

import android.net.Uri;
import android.text.TextUtils;
import android.view.ViewGroup;

import com.immomo.mls.fun.ud.view.UDViewGroup;
import com.immomo.momo.android.videoview.ScalableType;
import com.immomo.momo.feed.player.ExoTextureLayout;
import com.immomo.momo.feed.player.IMediaPlayer;
import com.immomo.momo.luaview.LuaIJKPlayer;
import com.immomo.momo.luaview.LuaMediaViewNew;
import com.immomo.momo.luaview.constants.PlayStatus;
import com.immomo.momo.mvp.maintab.mainimpl.appconfig.HttpsAppConfigV1;

import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaNumber;
import org.luaj.vm2.LuaString;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

/**
 * Created by zhang.ke on 2019/11/21.
 */

@LuaApiUsed
public class UDMediaView<V extends ExoTextureLayout> extends UDViewGroup<V> implements LuaMediaViewNew.Callback, LuaMediaViewNew.ProgressUpdateCallback, LuaIJKPlayer.CompletionListener {
    public static final String LUA_CLASS_NAME = "MediaView";
    private String feedid;

    public static final String[] methods = {
            "src",
            "setFeedId",
            "mute",
            "repeatCount",
            "offScreen",
            "totalDuration",
            "play",
            "stop",
            "pause",
            "seek",
            "setDidStartCallback",
            "setStartStallingCallback",
            "setEndStallingCallback",
            "setFinishCallback",
            "setFailCallback",
            "setProgressCallback",
            "setVideoSizeChangedCallback",
            "setWillRepeatCallback",
            "getPlayStatus",
            "directAccess",
            "scaleMode",
            "setToInitialCallback",
            "currentPosition"
    };

    @Override
    protected void __onLuaGc() {
        super.__onLuaGc();
        stopPlayVideo();
    }

    @LuaApiUsed
    public UDMediaView(long L, LuaValue[] v) {
        super(L, v);
        if (v.length > 0) {
            uri = Uri.parse(v[0].toJavaString());
        }
        initView();
    }

    @LuaApiUsed
    public LuaValue[] setFeedId(LuaValue[] vars) {
        this.feedid = vars.length > 0 ? vars[0].toJavaString() : null;
        getVideoView().setFeedid(feedid);
        return null;
    }

    @Override
    protected V newView(LuaValue[] init) {
        return (V) new LuaMediaViewNew(getContext());
    }

    private Uri uri;

    private boolean mAutoPlay;
    private int repeatCount = 1;//循环播放次数 ,默认1次
    private boolean isMute;
    private long totalDuration;

    private long stopPoint = 0;
    private LuaFunction didStartFun;
    private LuaFunction startStallingFun;
    private LuaFunction endStallingFun;
    private LuaFunction finishFun;
    private LuaFunction failFun;
    private LuaFunction progressFun;
    private LuaFunction sizeChangedFun;
    private LuaFunction willRepeatFun;
    private LuaFunction setToInitialFun;
    private int playStatus = PlayStatus.IDEL;
    private boolean hasStalling = false;
    private boolean isStop = true;

    @LuaApiUsed
    public LuaValue[] src(LuaValue[] values) {
        if (values.length == 0) {
            return LuaValue.rString(uri.toString());
        }
        LuaValue value = values[0];
        String src = value.toJavaString();
        boolean useMediaCodec = false;
        if (values.length > 1) {
            useMediaCodec = values[1].toBoolean();
        }
        if (!TextUtils.isEmpty(src)) {
            src = HttpsAppConfigV1.INSTANCE.replaceVideoCdn(src);
            uri = Uri.parse(src);
            if (getVideoView() != null) {
                getVideoView().checkUrl(src);
                getVideoView().setUri(uri, useMediaCodec);
                getVideoView().setLoopPlay(repeatCount != 1);//两端统一，在加载prepare和设置src时设置loop，播完一轮之后失效
            }
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] mute(LuaValue[] values) {
        if (values.length == 0) {
            return LuaValue.rBoolean(this.isMute);
        }
        LuaValue value = values[0];
        this.isMute = value.toBoolean();
        if (getVideoView() != null) {
            getVideoView().setSilentMode(isMute);
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] repeatCount(LuaValue[] values) {
        if (values.length == 0) {
            return LuaValue.rNumber(repeatCount);
        }
        LuaValue value = values[0];
        repeatCount = value.toInt();
        if (getVideoView() != null) {
            getVideoView().setRepeatCount(repeatCount);
        }
        return null;
    }

    @Deprecated
    @LuaApiUsed
    public LuaValue[] offScreen(LuaValue[] values) {
        if (values.length == 0) {
            return LuaValue.rBoolean(this.mAutoPlay);
        }

        LuaValue value = values[0];
        this.mAutoPlay = value.toBoolean();
        if (getVideoView() != null) {
            getVideoView().setAutoPlayForbidden(!this.mAutoPlay);
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] totalDuration(LuaValue[] values) {
        if (values.length == 0) {
            return LuaValue.rNumber(getVideoView().getDuration() * 1.0 / 1000);
        }
        LuaValue value = values[0];
        totalDuration = value.toInt();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] play(LuaValue[] values) {
        startPlayVideo();
        if (isStop) {
            getVideoView().setLoopPlay(repeatCount != 1);//两端统一，在加载prepare和设置src时设置loop，播完一轮之后失效
        }
        isStop = false;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] directAccess(LuaValue[] values) {
        boolean directAccess = values.length > 0 && values[0].toBoolean();
        getVideoView().directAccess(directAccess);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] stop(LuaValue[] values) {
        stopPlayVideo();
        isStop = true;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] pause(LuaValue[] values) {
        pausePlayVideo();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] seek(LuaValue[] values) {
        if (values.length > 0) {
            long point = values[0].toInt();
            seekPlayVideo(point * 1000);
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] currentPosition(LuaValue[] values) {
        long posi = 0L;
        if (getVideoView() != null) {
            posi = getVideoView().getCurrentPosition();
        }
        return LuaValue.rNumber(posi);
    }

    @LuaApiUsed
    public LuaValue[] setDidStartCallback(LuaValue[] values) {
        didStartFun = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setStartStallingCallback(LuaValue[] values) {
        startStallingFun = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setEndStallingCallback(LuaValue[] values) {
        endStallingFun = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setFinishCallback(LuaValue[] values) {
        finishFun = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setFailCallback(LuaValue[] values) {
        failFun = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setProgressCallback(LuaValue[] values) {
        progressFun = values.length != 0 ? values[0].toLuaFunction() : null;
        getVideoView().setProgressCallback(progressFun == null ? null : this);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setVideoSizeChangedCallback(LuaValue[] values) {
        sizeChangedFun = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setWillRepeatCallback(LuaValue[] values) {
        willRepeatFun = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setToInitialCallback(LuaValue[] values) {
        setToInitialFun = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] getPlayStatus(LuaValue[] values) {
        if (isStop) {
            return LuaValue.rNumber(PlayStatus.IDEL);
        }
        switch (playStatus) {
            case PlayStatus.READY:
            case PlayStatus.PREPARING:
                return LuaValue.rNumber(getVideoView().isPlaying() ? PlayStatus.PLAYING : PlayStatus.PAUSE);
        }
        return LuaValue.rNumber(playStatus);
    }

    @LuaApiUsed
    public LuaValue[] scaleMode(LuaValue[] values) {
        if (values.length == 0) {
            return null;
        }
        LuaValue value = values[0];
        int type = value.toInt();
        if (getVideoView() != null) {
            if (type == 1) {
                getVideoView().setScalableType(ScalableType.FIT_WIDTH);
            } else if (type == 2) {
                getVideoView().setScalableType(ScalableType.CENTER_CROP);
            } else if (type == 3) {
                getVideoView().setScalableType(ScalableType.FIT_CENTER);
            }
        }
        return null;
    }

    protected void initView() {
        getVideoView().setCompletionListener(this);
        getVideoView().setCallback(this);
        ViewGroup.LayoutParams videoLayoutParams =
                new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                                           ViewGroup.LayoutParams.MATCH_PARENT);
        getVideoView().setLayoutParams(videoLayoutParams);
        getVideoView().setSilentMode(isMute);
        getVideoView().setRepeatCount(repeatCount);
        getVideoView().setAutoPlayForbidden(!mAutoPlay);
        if (uri != null)
            getVideoView().setUri(uri);
    }

    private void pausePlayVideo() {
        if (getVideoView() != null) {
            //            stopPoint = getVideoView().getCurrentPosition();
            getVideoView().pause();
        }
    }

    private void stopPlayVideo() {
        if (getVideoView() != null) {
            stopPoint = getVideoView().getCurrentPosition();
            getVideoView().stopPlayback();
        }
    }

    private void startPlayVideo() {
        if (getVideoView() != null) {
            //            if (totalDuration > 0 && stopPoint >= totalDuration)
            //                stopPoint = 0;
            //            if (stopPoint > 0) {
            //                getVideoView().seekTo(stopPoint);
            //                stopPoint = 0;
            //            }
            getVideoView().start();
        }
    }

    private void seekPlayVideo(long point) {
        if (getVideoView() != null) {
            getVideoView().seekTo(point);
        }
    }

    @Override
    public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {
        switch (playbackState) {
            case IMediaPlayer.STATE_IDLE:
                onSetToInitialState();
                break;
            case IMediaPlayer.STATE_ENDED:
                playStatus = PlayStatus.IDEL;
                break;
            case IMediaPlayer.STATE_BUFFERING:
                playStatus = PlayStatus.PREPARING;
                if (startStallingFun != null) {
                    startStallingFun.invoke(LuaValue.rString(uri.toString()));
                }
                hasStalling = true;
                break;
            case IMediaPlayer.STATE_READY:
                if (hasStalling && endStallingFun != null) {
                    endStallingFun.invoke(LuaValue.rString(uri.toString()));
                    hasStalling = false;
                }
                playStatus = PlayStatus.READY;
                totalDuration = getVideoView() != null ? getVideoView().getDuration() : -1;
                break;
        }
    }

    @Override
    public void onError(int what, int extra) {
        playStatus = PlayStatus.ERROR;
        if (failFun != null) {
            failFun.invoke(LuaValue.rString(uri.toString()));
        }
    }

    @Override
    public void onStartRendering() {
        playStatus = PlayStatus.PLAYING;
        if (didStartFun != null) {
            didStartFun.invoke(varargsOf(LuaString.valueOf(uri.toString()), LuaNumber.valueOf(getVideoView().getDuration() * 1.0 / 1000)));
        }
    }

    @Override
    public void onVideoSizeChanged(int width, int height, int unappliedRotationDegrees, float pixelWidthHeightRatio) {
        if (sizeChangedFun != null) {
            sizeChangedFun.invoke(varargsOf(LuaString.valueOf(uri.toString()), LuaNumber.valueOf(width), LuaNumber.valueOf(height)));
        }
    }

    @Override
    public void onLoopStart() {
        if (willRepeatFun != null) {
            willRepeatFun.invoke(varargsOf(LuaString.valueOf(uri.toString()), LuaNumber.valueOf(getVideoView().getDuration() * 1.0 / 1000)));
        }
    }

    private LuaMediaViewNew getVideoView() {
        return (LuaMediaViewNew) getView();
    }

    @Override
    public void onProgressUpdate(long progress) {
        if (progressFun != null) {
            progressFun.invoke(varargsOf(LuaString.valueOf(uri.toString()), LuaNumber.valueOf(progress * 1.0 / 1000), LuaNumber.valueOf(getVideoView().getDuration() * 1.0 / 1000)));
        }
    }

    @Override
    public void onCompletion() {
        if (finishFun != null) {
            finishFun.invoke(LuaValue.rString(uri.toString()));
        }
    }

    @Override
    public void onSetToInitialState() {
        if (setToInitialFun != null) {
            setToInitialFun.invoke(LuaValue.rString(uri.toString()));
        }
    }
}
