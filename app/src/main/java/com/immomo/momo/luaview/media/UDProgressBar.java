package com.immomo.momo.luaview.media;

import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.os.Build;
import android.view.LayoutInflater;
import android.widget.ProgressBar;

import com.immomo.mls.fun.ud.UDColor;
import com.immomo.mls.fun.ud.view.UDView;
import com.immomo.momo.R;

import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

/**
 * author: hongming.wei
 * data: 2023/2/20
 */

@LuaApiUsed
public class UDProgressBar<V extends ProgressBar> extends UDView<V> {

    public static final String LUA_CLASS_NAME = "VideoProgressBar";

    public static final String[] methods = {
            "setProgress",
            "setMaxHeight",
            "setMax",
            "setProgressColor",
            "setProgressBackgroundColor",
    };

    @LuaApiUsed
    protected UDProgressBar(long L, LuaValue[] v) {
        super(L, v);
    }

    @Override
    protected V newView(LuaValue[] init) {
        return (V) LayoutInflater.from(getContext()).inflate(R.layout.layout_video_progress_bar, null);
    }

    @LuaApiUsed
    public LuaValue[] setProgress(LuaValue[] values) {
        if (values != null && values.length > 0) {
            getView().setProgress(values[0].toInt());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setMaxHeight(LuaValue[] values) {
        if (values != null && values.length > 0 && Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            getView().setMaxHeight(values[0].toInt());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setMax(LuaValue[] values) {
        if (values != null && values.length > 0) {
            getView().setMax(values[0].toInt());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setProgressColor(LuaValue[] values) {
        if (values == null || values.length == 0 || !(values[0] instanceof UDColor)) {
            return null;
        }
        UDColor color = (UDColor) values[0];
        getView().setProgressTintList(ColorStateList.valueOf(color.getColor()));
        getView().setProgressTintMode(PorterDuff.Mode.SRC);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setProgressBackgroundColor(LuaValue[] values) {
        if (values == null || values.length == 0 || !(values[0] instanceof UDColor)) {
            return null;
        }
        UDColor color = (UDColor) values[0];
        getView().setProgressBackgroundTintList(ColorStateList.valueOf(color.getColor()));
        getView().setProgressBackgroundTintMode(PorterDuff.Mode.SRC);
        return null;
    }

}
