package com.immomo.momo.luaview.ud;

import android.graphics.BlurMaskFilter;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.MaskFilterSpan;
import android.widget.TextView;

import com.immomo.mls.fun.ud.view.UDLabel;
import com.immomo.momo.util.StringUtils;

import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

/**
 * 模糊视图的Lable，仅安卓支持
 *
 * @param <V>
 */
@LuaApiUsed
public class UDBlurLabelView extends UDLabel<TextView> {
    public static final String LUA_CLASS_NAME = "BlurLabel";

    public static final String[] methods = {
            "text",
            "textAlign",
            "fontSize",
            "textColor",
            "lines",
            "breakMode",
            "styleText",
            "setTextBold",
            "fontNameSize",
            "setLineSpacing",
            "setTextFontStyle",
            "addTapTexts",
            "setAutoFit",
            "setMaxWidth",
            "setMaxHeight",
            "setMinWidth",
            "setMinHeight",
            "a_setIncludeFontPadding",
            "setBlurRadius",
    };

    private float blurRadius = 10f;

    @LuaApiUsed
    public UDBlurLabelView(long L, LuaValue[] v) {
        super(L, v);
    }

    @LuaApiUsed
    public LuaValue[] setBlurRadius(LuaValue[] var) {
        //ios新增参数2，Android不处理
        if (var.length > 0 && !var[0].isNil()) {
            blurRadius = var[0].toFloat();
        }
        return null;
    }

    protected void setText(String text) {
        super.setText(text);
        if (StringUtils.isNotBlank(text)) {
            SpannableString stringBuilder = new SpannableString(text);
            stringBuilder.setSpan(new MaskFilterSpan(new BlurMaskFilter(blurRadius, BlurMaskFilter.Blur.NORMAL)),
                    0, stringBuilder.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            getView().setText(stringBuilder);
        }
    }

}
