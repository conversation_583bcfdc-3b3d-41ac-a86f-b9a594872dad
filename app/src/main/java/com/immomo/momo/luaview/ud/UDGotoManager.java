package com.immomo.momo.luaview.ud;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Vibrator;

import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.momo.MomoKit;
import com.immomo.momo.home.manager.FrameConfigManager;
import com.immomo.momo.homepage.fragment.HomePageFragment;
import com.immomo.momo.maintab.MaintabActivity;
import com.immomo.momo.maingroup.manager.FrameConfigConst;


@LuaClass(isStatic = true)
public class UDGotoManager {
    public static final String LUA_CLASS_NAME = "MaskLuaBridge";

    @LuaBridge
    public static void popToHomeSquare() {
        Intent intent = new Intent(MomoKit.getTopActivity(), MaintabActivity.class);
        Bundle data = new Bundle();
        if (!FrameConfigManager.INSTANCE.containsFrame(FrameConfigConst.FRAME_FLASH_CHAT)) {
            return;
        }
        data.putString( MaintabActivity.KEY_SON_BUSINESS, FrameConfigConst.FRAME_FLASH_CHAT);
        data.putInt(MaintabActivity.KEY_TABINDEX, MaintabActivity.TAB_FEED);
        data.putString(MaintabActivity.KEY_SOURCE, HomePageFragment.HOMEPAGE_FRAGMENT);
        intent.putExtras(data);
        if (MomoKit.getTopActivity() == null) {
            return;
        }
        MomoKit.getTopActivity().startActivity(intent);
    }

    @LuaBridge
    public static void doImpactFeedback() {
        Vibrator vibrator = (Vibrator) MomoKit.getContext().getSystemService(Context.VIBRATOR_SERVICE);
        if (vibrator != null) {
            vibrator.vibrate(100);
        }
    }
}
