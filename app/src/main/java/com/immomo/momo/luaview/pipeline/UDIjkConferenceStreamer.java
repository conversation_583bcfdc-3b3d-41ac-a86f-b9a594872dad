package com.immomo.momo.luaview.pipeline;

import android.app.Activity;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.core.glcore.cv.MMCVInfo;
import com.cosmos.mdlog.MDLog;
import com.immomo.framework.utils.UIUtils;
import com.immomo.game.view.GameVideoSurfaceView;
import com.immomo.mediacore.audio.AudioVolumeWeight;
import com.immomo.medialog.MediaConfigsForIJK;
import com.immomo.mls.fun.other.Size;
import com.immomo.mls.fun.ud.UDSize;
import com.immomo.mls.fun.ud.view.UDViewGroup;
import com.immomo.mls.utils.MainThreadExecutor;
import com.immomo.molive.gui.common.filter.JniEngineAdjustFilter;
import com.immomo.molive.gui.common.filter.JniFilterSwitch;
import com.immomo.molive.gui.common.filter.MLAdjustFilter;
import com.immomo.momo.LogTag;
import com.immomo.momo.luaview.pipeline.base.ILuaMRtcEventHandler;
import com.immomo.momo.luaview.pipeline.entity.UDDataBuffer;
import com.immomo.momo.luaview.pipeline.view.GameLuaLivePreviewDialog;
import com.momo.piplineext.MLocalRtcStats;
import com.momo.piplineext.MRemoteRtcStats;

import org.luaj.vm2.Globals;
import org.luaj.vm2.LuaBoolean;
import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaNumber;
import org.luaj.vm2.LuaString;
import org.luaj.vm2.LuaTable;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import java.util.Arrays;

import tv.danmaku.ijk.media.streamer.ijkMediaStreamer;

/**
 * Created by zhang.ke
 * on 2019/5/21
 */
@LuaApiUsed
public class UDIjkConferenceStreamer extends BaseLuaIJKConferenceStreamer implements ILuaMRtcEventHandler {
    public static final String LUA_CLASS_NAME = "RTCEngine";
    public static final String[] methods = {
            "rtcType",
            "appId",
            "channelKey",
            "channelId",
            "userId",
            "role",
            "roomMode",
            "musicVolume",
            "enableVideo",
            "videoBitRate",
            "videoFrameRate",
            "videoResolution",
            "audioSampleRate",
            "audioChannels",
            "audioProfile",
            "rtcLogPath",
            "audioVolumeReportInterval",
            "audioRouteToSpeakerphone",
            "enableV3Log",
            "hardwareDecode",
            "preferFrameRate",
            "audioMixable",
            "userSign",
            "businessType",
            "videoInitBitRate",
            "disableIOAudioUnitBus1",
            "IOAudioUnitMode",
            "onJoinChannel",
            "onLeaveChannel",
            "onExitRoom",
            "onJoinChannelfail",
            "onUserOffline",
            "onRtcEngineError",
            "onLostConnection",
            "onLostConnectionThorough",
            "onAudioDidMute",
            "onVideoDidMute",
            "onAudioVolumeReport",
            "onVideoLocalSentStatus",
            "onMusicPlayFinish",
            "onPcmDataCallback",
            "onVideoRemoteSentStatus",
            "onVideoFirstFrame",
            "onReceiveStreamMessage",
            "setSimpleMediaLogsUpload",
            "onRequestChannelKey",
            "showBeautyFace",
            "dismissBeautyFace",
            "switchCamera",
            "showSurfaceView",
            "hideSurfaceView",
            "showPreviewSurfaceView",
            "showPreviewView",
            "closePreviewView",
            "showFace",
            "onVideoChannelAddedCallBack",
            "onPreviewViewCountEndCallBack",
            "onPreviewViewCancelCallBack",

    };

    //通用
    private LuaFunction joinChannelCallback;                //  加入频道成功
    private LuaFunction leaveChannelCallback;               //  用户离开频道
    private LuaFunction onExitRoomCallback;               //  新接口，离开房间回调
    private LuaFunction joinChannelFailCallback;               //  加入频道失败
    private LuaFunction onUserOfflineCallback;               //  用户离线
    private LuaFunction rtcEngineErrorCallback;             //  SDK发生错误回调
    private LuaFunction lostConnectionCallback;             //  与服务器断开连接, 同时SDK会自动尝试重连
    private LuaFunction lostConnectionThorouhgCallback;     //  与服务器彻底断开连接且SDK不再重连
    private LuaFunction requestChannelKeyCallback;          //  channelKey 过期回调
    //音频
    private LuaFunction audioDidMuteCallback;               //  音频mute/unmute回调
    private LuaFunction audioVolumeReportCallback;          //  用户音量回调
    private LuaFunction musicPlayFinishCallback;            //  音乐播放完成或者失败
    private LuaFunction pcmDataCallback;                    //  原始声音的回调, 通过enableRecordAudioReport(Bool enable)方法来开启
    //视频
    private LuaFunction videoDidMuteCallback;               //  视频mute/unmute回调
    private LuaFunction videoLocalSentStatusCallback;       //  视频上行状态回调 (该方法每2s回调一次)
    private LuaFunction videoRemoteSentStatusCallback;      //  远端视频状态回调 (该方法每2s回调一次)
    private LuaFunction videoFirstFrameCallback;            //  拉取到第一帧视频的回调,
    private LuaFunction receiveStreamMessageCallback;       //  收到数据流的回调
    private LuaFunction onVideoChannelAddedCallBack;           //  收到视频view
    private LuaFunction onPreviewViewCountEndCallBack;           //  收到视频view
    private LuaFunction onPreviewViewCancelCallBack;           //  收到视频view

    //
    private GameLuaLiveManager mGameLuaVideoManager;
    private MLAdjustFilter mMLAdjustFilter;

    @LuaApiUsed
    public UDIjkConferenceStreamer(long L, LuaValue[] v) {
        super(L, v);
    }

    public UDIjkConferenceStreamer(Globals g, Object jud) {
        super(g, jud);
    }

    //<editor-fold desc="Property">
    @LuaApiUsed
    public LuaValue[] rtcType(LuaValue[] values) {
        return LuaValue.rNumber(getRtcType());//只读，构造赋值
    }

    @LuaApiUsed
    public LuaValue[] appId(LuaValue[] appId) {
        if (appId.length > 0) {
            setAppId(appId[0].toJavaString());
            if (mMediaStreamer != null)
                mMediaStreamer.setAppID(getAppId());
        }
        return LuaValue.rString(getAppId());
    }

    @LuaApiUsed
    public LuaValue[] channelKey(LuaValue[] channelKey) {
        if (channelKey.length > 0) {
            putChannelkey(channelKey[0].toJavaString());
            if (mMediaStreamer != null)
                mMediaStreamer.setChannelkey(getChannelkey());
        }
        return LuaValue.rString(getChannelkey());
    }

    @LuaApiUsed
    public LuaValue[] channelId(LuaValue[] channelId) {
        if (channelId.length > 0) {
            setVid(channelId[0].toJavaString());
            if (mMediaStreamer != null)
                mMediaStreamer.setChannalName(getVid());
        }
        return LuaValue.rString(getVid());
    }

    @LuaApiUsed
    public LuaValue[] userId(LuaValue[] userId) {
        if (userId.length > 0) {
            setUid(userId[0].toInt());
            if (mMediaStreamer != null)
                mMediaStreamer.setUserID(getUid());
        }
        return LuaValue.rNumber(getUid());
    }

    @LuaApiUsed
    public LuaValue[] role(LuaValue[] role) {
        if (role.length > 0) {
            setClientRole(role[0].toInt());
            if (mMediaStreamer != null)
                mMediaStreamer.setRole(getClientRole());
        }
        return LuaValue.rNumber(getClientRole());
    }

    @LuaApiUsed
    public LuaValue[] roomMode(LuaValue[] role) {
        if (role.length > 0) {
            setRoomMode(role[0].toInt());
            if (mMediaStreamer != null)
                mMediaStreamer.setRoomMode(getRoomMode());
        }
        return LuaValue.rNumber(getRoomMode());
    }

    @LuaApiUsed
    public LuaValue[] musicVolume(LuaValue[] musicVolume) {
        if (musicVolume.length > 0) {
            setCurrentVolume((float) musicVolume[0].toDouble());
            if (mMediaStreamer != null)
                mMediaStreamer.setSlaveAudioLevel(getCurrentVolume());
        }
        return LuaValue.rNumber(getCurrentVolume());
    }

    @LuaApiUsed
    public LuaValue[] enableVideo(LuaValue[] enable) {
        if (enable.length > 0) {
            setEnableVideo(enable[0].toBoolean());
            if (mMediaStreamer != null)
                mMediaStreamer.enableVideo(isEnableVideo());
        }
        return LuaValue.rBoolean(isEnableVideo());
    }

    @LuaApiUsed
    public LuaValue[] videoBitRate(LuaValue[] videoBitRate) {
        if (videoBitRate.length > 0) {
            setVideoBitRate(videoBitRate[0].toInt());
            if (mMediaStreamer != null)
                mMediaStreamer.setVideoEncodingBitRate(getVideoBitRate());
        }
        return LuaValue.rNumber(getVideoBitRate());
    }

    @LuaApiUsed
    public LuaValue[] videoFrameRate(LuaValue[] videoFrameRate) {
        if (videoFrameRate.length > 0) {
            setVideoCodecRate(videoFrameRate[0].toInt());
            if (mMediaStreamer != null)
                mMediaStreamer.setVideoCodeFrameRate(getVideoCodecRate());
        }
        return LuaValue.rNumber(getVideoCodecRate());
    }

    @LuaApiUsed
    public LuaValue[] videoResolution(LuaValue[] values) {
        if (values.length > 0) {
            UDSize resolution = (UDSize) values[0].toUserdata();
            if (resolution != null && resolution.getSize() != null) {
                MDLog.i(LogTag.GameWolf.GameWolf,"videoResolution:"+resolution.toString());
                setEncoderSize(resolution.getSize());
                if (mMediaStreamer != null)
                    mMediaStreamer.setEncoderSize((int) getEncodeSize().getWidth(), (int) getEncodeSize().getHeight());
            }
        }
        return LuaValue.varargsOf(new UDSize(getGlobals(), getEncodeSize()));
    }

    @LuaApiUsed
    public LuaValue[] audioSampleRate(LuaValue[] audioSampleRate) {
        if (audioSampleRate.length > 0) {
            setAudioSampleRate(audioSampleRate[0].toInt());
            if (mMediaStreamer != null)
                mMediaStreamer.setAudioSampleRate(getAudioSampleRate());
        }
        return LuaValue.rNumber(getAudioSampleRate());
    }

    @LuaApiUsed
    public LuaValue[] audioChannels(LuaValue[] audioChannels) {
        return null;
    }

    @LuaApiUsed
    public LuaValue[] audioProfile(LuaValue[] vars) {
        setAudioProfile(vars.length > 0 ? vars[0].toInt() : -1);
        setAudioScenario(vars.length > 1 ? vars[1].toInt() : -1);
        if (mMediaStreamer != null)
            mMediaStreamer.setAudioProfile(getAudioProfile(), getAudioScenario());
        return null;
    }

    @LuaApiUsed
    public LuaValue[] rtcLogPath(LuaValue[] rtcLogPath) {
        if (rtcLogPath.length > 0) {
            setLinkMicLogPath(rtcLogPath[0].toJavaString());
        }
        return LuaValue.rString(getLinkMicLogPath());
    }

    @LuaApiUsed
    public LuaValue[] audioVolumeReportInterval(LuaValue[] vars) {
        setAudioInterval(vars.length > 0 ? vars[0].toInt() : -1);
        setAudiosmooth(vars.length > 1 ? vars[1].toInt() : -1);
        if (mMediaStreamer != null)
            mMediaStreamer.enableAudioVolumeIndication(getAudioInterval(), getAudiosmooth());
        return null;
    }

    @LuaApiUsed
    public LuaValue[] audioRouteToSpeakerphone(LuaValue[] enable) {
        if (enable.length > 0) {
            setSpeakerphone(enable[0].toBoolean());
            if (mMediaStreamer != null)
                mMediaStreamer.setEnableSpeakerphone(isSpeakerphone());
        }
        return LuaValue.rBoolean(isSpeakerphone());
    }

    @LuaApiUsed
    public LuaValue[] enableV3Log(LuaValue[] enableV3Log) {
        if (enableV3Log.length > 0) {
            setEnableV3Log(enableV3Log[0].toBoolean());
            MediaConfigsForIJK.getInstance().setEnableV3LogReport(isEnableV3Log());
        }
        return LuaValue.rBoolean(isEnableV3Log());
    }

    @LuaApiUsed
    public LuaValue[] hardwareDecode(LuaValue[] enableMedia) {
        if (enableMedia.length > 0) {
            setEnableHardDecode(enableMedia[0].toBoolean());
            if (mMediaStreamer != null)
                mMediaStreamer.setMediaCodecEnable(isEnableHardDecode());
        }
        return LuaValue.rBoolean(isEnableHardDecode());
    }

    @LuaApiUsed
    public LuaValue[] preferFrameRate(LuaValue[] preferFrameRate) {
        if (preferFrameRate.length > 0) {
            setVideoQualityFlg(preferFrameRate[0].toBoolean());
            if (mMediaStreamer != null)
                mMediaStreamer.setVideoQualityParameters(isVideoQualityFlg());
        }
        return LuaValue.rBoolean(isVideoQualityFlg());
    }

    @LuaApiUsed
    public LuaValue[] audioMixable(LuaValue[] audioMixable) {
        if (audioMixable.length > 0) {
            setMixOutput(audioMixable[0].toBoolean());
        }
        return LuaValue.rBoolean(isMixOutput());
    }

    @LuaApiUsed
    public LuaValue[] userSign(LuaValue[] userSign) {
        if (userSign.length > 0) {
            setUserSign(userSign[0].toJavaString());
            if (mMediaStreamer != null)
                mMediaStreamer.setUserSig(getUserSign());
        }
        return LuaString.rString(getUserSign());
    }

    @LuaApiUsed
    public LuaValue[] businessType(LuaValue[] businessType) {
        if (businessType.length > 0) {
            setBusinessType(businessType[0].toInt());
            if (mMediaStreamer != null)
                mMediaStreamer.setBusinessType(getBusinessType());
            return null;
        }
        return LuaNumber.rNumber(getBusinessType());
    }

    @LuaApiUsed
    public LuaValue[] videoInitBitRate(LuaValue[] values) {
        return null;//iOS Only
    }

    @LuaApiUsed
    public LuaValue[] disableIOAudioUnitBus1(LuaValue[] values) {
        return null;//iOS Only
    }

    @LuaApiUsed
    public LuaValue[] IOAudioUnitMode(LuaValue[] values) {
        return null;//iOS Only
    }

    //<editor-fold>

    //<editor-fold desc="Callback">
    @Override
    @LuaApiUsed
    public LuaValue[] onJoinChannel(LuaValue[] fun) {
        this.joinChannelCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @Override
    @LuaApiUsed
    public LuaValue[] onLeaveChannel(LuaValue[] fun) {
        this.leaveChannelCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    //新接口，离开房间回调
    @LuaApiUsed
    public LuaValue[] onExitRoom(LuaValue[] fun) {
        this.onExitRoomCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @Override
    @LuaApiUsed
    public LuaValue[] onJoinChannelfail(LuaValue[] fun) {
        this.joinChannelFailCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @Override
    @LuaApiUsed
    public LuaValue[] onUserOffline(LuaValue[] fun) {
        this.onUserOfflineCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @Override
    @LuaApiUsed
    public LuaValue[] onRtcEngineError(LuaValue[] fun) {
        this.rtcEngineErrorCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @Override
    @LuaApiUsed
    public LuaValue[] onLostConnection(LuaValue[] fun) {
        this.lostConnectionCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @Override
    @LuaApiUsed
    public LuaValue[] onLostConnectionThorough(LuaValue[] fun) {
        this.lostConnectionThorouhgCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @Override
    @LuaApiUsed
    public LuaValue[] onAudioDidMute(LuaValue[] fun) {
        this.audioDidMuteCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @Override
    @LuaApiUsed
    public LuaValue[] onVideoDidMute(LuaValue[] fun) {
        this.videoDidMuteCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @Override
    @LuaApiUsed
    public LuaValue[] onAudioVolumeReport(LuaValue[] fun) {
        this.audioVolumeReportCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @Override
    @LuaApiUsed
    public LuaValue[] onVideoLocalSentStatus(LuaValue[] fun) {
        this.videoLocalSentStatusCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @Override
    @LuaApiUsed
    public LuaValue[] onMusicPlayFinish(LuaValue[] fun) {
        this.musicPlayFinishCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @Override
    @LuaApiUsed
    public LuaValue[] onPcmDataCallback(LuaValue[] fun) {
        this.pcmDataCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @Override
    @LuaApiUsed
    public LuaValue[] onVideoRemoteSentStatus(LuaValue[] fun) {
        this.videoRemoteSentStatusCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @Override
    @LuaApiUsed
    public LuaValue[] onVideoFirstFrame(LuaValue[] fun) {
        this.videoFirstFrameCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @Override
    @LuaApiUsed
    public LuaValue[] onReceiveStreamMessage(LuaValue[] fun) {
        this.receiveStreamMessageCallback = fun.length > 0 ? fun[0].toLuaFunction() : null;
        return null;
    }

    @Override
    @LuaApiUsed
    public LuaValue[] setSimpleMediaLogsUpload(LuaValue[] values) {
        logInterval = values.length > 0 && values[0].isNumber() ? values[0].toInt() : 0;
        logCount = values.length > 1 && values[1].isNumber() ? values[1].toInt() : 0;
        mediaLogsUploadCallback = values.length > 2 && values[2].isFunction() ? values[2].toLuaFunction() : null;
        return null;
    }

    @Override
    @LuaApiUsed
    public LuaValue[] onRequestChannelKey(LuaValue[] values) {
        requestChannelKeyCallback = values.length > 0 && values[0].isFunction() ? values[0].toLuaFunction() : null;
        return null;
    }

    //</editor-fold>


    //<editor-fold desc="RtcEngine Callback">
    @Override
    public void onJoinChannelSuccess(final String channel, final long uid, final int elapsed) {
        MDLog.i(LogTag.GameWolf.GameWolf,"onJoinChannelSuccess:uid="+uid);
        if (joinChannelCallback != null) {
            MainThreadExecutor.post(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    joinChannelCallback.invoke(LuaValue.varargsOf(LuaString.valueOf(channel), LuaNumber.valueOf(uid), LuaNumber.valueOf(elapsed)));
                }
            });
        }
    }

    @Override
    public void onJoinChannelfail(String channel, long uid, int elapsed) {
        MDLog.i(LogTag.GameWolf.GameWolf,"onJoinChannelfail:uid="+uid);
        if (joinChannelFailCallback != null) {
            MainThreadExecutor.post(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    joinChannelFailCallback.invoke(LuaValue.varargsOf(LuaString.valueOf(channel), LuaNumber.valueOf(uid), LuaNumber.valueOf(elapsed)));
                }
            });
        }
    }

    @Override
    public void onVideoChannelRemove(final long userId, final int reason) {
        MDLog.i(LogTag.GameWolf.GameWolf,"onVideoChannelRemove:userId="+userId+",reason="+reason);
        videoMap.remove((int) userId);
        if(mGameLuaVideoManager != null){
            mGameLuaVideoManager.onVideoChannelRemove((int) userId,false);
        }
        if (leaveChannelCallback != null) {
            MainThreadExecutor.post(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    leaveChannelCallback.invoke(LuaValue.varargsOf(LuaNumber.valueOf(userId), LuaNumber.valueOf(reason)));
                }
            });
        }
    }

    @Override
    public void onPcmDateCallback(final long uid, final byte[] data, int samplingFreq, boolean isStereo) {
        if (pcmDataCallback != null)
            pcmDataCallback.invoke(LuaValue.varargsOf(new UDDataBuffer(pcmDataCallback.getGlobals(), data), LuaNumber.valueOf(uid)));
    }

    @Override
    public void onAudioVolumeIndication(AudioVolumeWeight[] speakers, int totalVolume) {
        if (audioVolumeReportCallback != null) {
            MainThreadExecutor.post(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    audioVolumeReportCallback.invoke(LuaValue.varargsOf(speakersToTable(getGlobals(), speakers)));
                }
            });
        }
    }

    //转化为LuaTable，传给lua层
    private LuaTable speakersToTable(Globals global, AudioVolumeWeight[] speakers) {
        if (global.isDestroyed()) {
            return null;
        }

        LuaTable table = LuaTable.create(global);
        if (speakers != null && !table.isDestroyed()) {
            for (AudioVolumeWeight item :
                    speakers) {
                table.set(item.uid, item.volume);
            }
        }
        return table;
    }

    /**
     * what 1:prepared;2:completed;3:seeked;-1:erroe
     * 直播K歌 业务层方便不加新的状态接口,借用这个回调，增加几个状态值
     * what  10 ~ 30 含义参考 KtvPlayerStatusCode
     **/
    @Override
    public void OnSurroundMusicStatus(ijkMediaStreamer mp, final int what, int extra) {
        if (musicPlayFinishCallback != null) {
            MainThreadExecutor.post(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    switch (what) {
                        case 2:
                            musicPlayFinishCallback.invoke(LuaValue.varargsOf(LuaValue.Nil()));
                            break;
                    }
                }
            });
        }
    }

    @Override
    public void onLocalRtcStats(final MLocalRtcStats stats) {
        if (videoLocalSentStatusCallback != null && stats != null) {
            MainThreadExecutor.post(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    videoLocalSentStatusCallback.invoke(LuaValue.varargsOf(LuaNumber.valueOf(stats.sentFrameRate),
                            LuaNumber.valueOf(stats.sentBitrate)));
                }
            });
        }
    }

    @Override
    public void onRemoteRtcStats(final MRemoteRtcStats stats) {
        if (videoRemoteSentStatusCallback != null && stats != null) {
            MainThreadExecutor.post(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    videoRemoteSentStatusCallback.invoke(LuaValue.varargsOf(LuaNumber.valueOf(stats.receivedFrameRate),
                            LuaNumber.valueOf(stats.receivedBitrate)));
                }
            });
        }
    }

    @Override
    public void onFirstRemoteVideoDecoded(final long uid, final int width, final int height, final int elapsed) {
        if (videoFirstFrameCallback != null) {
            MainThreadExecutor.post(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    //注意检查，是否频繁调用，创建对象
                    Globals globals = videoFirstFrameCallback.getGlobals();
                    videoFirstFrameCallback.invoke(LuaValue.varargsOf(LuaNumber.valueOf(uid),
                            new UDSize(globals, new Size(width, height)), LuaNumber.valueOf(elapsed)));
                }
            });
        }
    }

    @Override
    public void onRequestChannelKey() {
//         channelKey 过期回调
//                    changeChannelKey();
        if (requestChannelKeyCallback != null) {
            MainThreadExecutor.post(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    requestChannelKeyCallback.invoke(null);
                }
            });
        }
    }

    @Override
    public void onStreamMessage(final int uid, int streamId, final byte[] data) {
//                    BaseMediaChatHelper.this.onStreamMessage(uid, streamId, data);
        if (receiveStreamMessageCallback != null) {
            MainThreadExecutor.post(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    receiveStreamMessageCallback.invoke(LuaValue.varargsOf(LuaString.valueOf(Arrays.toString(data)),
                            LuaNumber.valueOf(uid)));
                }
            });
        }
    }

    @Override
    public void onStreamMessageError(int uid, int streamId, int error, int missed, int cached) {
//                    MDLog.e(LogTag.VoiceChat.VChatKTV, "推流错误：" + uid + "," + error);
    }

    @Override
    public void onUserOffline(long uid, int reason) {
        if (onUserOfflineCallback != null) {
            MainThreadExecutor.post(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    onUserOfflineCallback.invoke(LuaValue.varargsOf(LuaNumber.valueOf(uid), LuaNumber.valueOf(reason)));
                }
            });
        }
    }

    @Override
    public void onConnectionLost() {
        if (lostConnectionCallback != null) {
            MainThreadExecutor.post(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    lostConnectionCallback.invoke(null);
                }
            });
        }
    }

    @Override
    public void onReconnectTimeout() {
        if (lostConnectionThorouhgCallback != null) {
            MainThreadExecutor.post(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    lostConnectionThorouhgCallback.invoke(null);
                }
            });
        }
    }


    @Override
    public void onAudioMixingFinished() {

    }

    @Override
    public void onWarning(int warn) {

    }

    @Override
    public void onError(final int err) {
        if (rtcEngineErrorCallback != null) {
            MainThreadExecutor.post(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    rtcEngineErrorCallback.invoke(LuaValue.varargsOf(LuaNumber.valueOf(err), LuaValue.Nil()));
                }
            });
        }
        MDLog.i(LogTag.GameWolf.GameWolf,"UDIJK onError:err="+err);
        //声网的channel过期回调
        if(getRtcType() == 1 && err == 109){
            if (requestChannelKeyCallback != null) {
                MainThreadExecutor.post(getTaskTag(), new Runnable() {
                    @Override
                    public void run() {
                        requestChannelKeyCallback.invoke(null);
                    }
                });
            }
        }
    }

    @Override
    public void onUserMuteVideo(final int uid, final boolean muted) {
        MDLog.i(LogTag.GameWolf.GameWolf,"<<<<<<<<<onUserMuteVideo,uid="+uid +",muted="+muted);
        if (videoDidMuteCallback != null) {
            MainThreadExecutor.post(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    videoDidMuteCallback.invoke(LuaValue.varargsOf(LuaBoolean.valueOf(muted), LuaNumber.valueOf(uid)));
                }
            });
        }

        SurfaceView surfaceView = getRemoteView(uid);
        if (surfaceView == null) {
            return;
        }
        initGameLuaVideoManager();

        if (muted) {
            if (mGameLuaVideoManager != null) {
                mGameLuaVideoManager.onVideoChannelRemove(uid,true);
            }
        } else {
            if (mGameLuaVideoManager != null) {
                mGameLuaVideoManager.onVideoChannelAdded(uid, surfaceView, 0, 0);
            }
        }
    }

    @Override
    public void onUserMuteAudio(final int uid, final boolean muted) {
        if (audioDidMuteCallback != null) {
            MainThreadExecutor.post(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    audioDidMuteCallback.invoke(LuaValue.varargsOf(LuaBoolean.valueOf(muted), LuaNumber.valueOf(uid)));
                }
            });
        }
    }

    @Override
    public void onExitRoom() {
        if (onExitRoomCallback != null) {
            MainThreadExecutor.post(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    if (onExitRoomCallback != null) {
                        onExitRoomCallback.invoke(null);
                    }
                }
            });
        }
    }

    @Override
    protected void initVideo(ViewGroup container) {
        super.initVideo(container);
        initGameLuaVideoManager();

        if (JniFilterSwitch.isJniFilterOpen()) {
            mMLAdjustFilter = new JniEngineAdjustFilter(getContext(), mMediaStreamer.getMomoProcessPipeline());
        } else {
            mMLAdjustFilter = new MLAdjustFilter(getContext(), mMediaStreamer.getMomoProcessPipeline());
        }
        mGameLuaVideoManager.setmMLAdjustFilter(mMLAdjustFilter);

        mGameLuaVideoManager.resetFaceSettings();
        mMediaStreamer.selectFaceDetectFilter(getContext(), mMLAdjustFilter);

        GameVideoSurfaceView surface = new GameVideoSurfaceView(getContext());
        surface.setCallback(new GameVideoSurfaceView.SurfcaeHolderCreatedCallback() {
            @Override
            public void created(final SurfaceHolder surfaceHolder) {
                MDLog.i(LogTag.GameWolf.GameWolf,"surfaceCreated");
                mGameLuaVideoManager.setPreivew(surfaceHolder);

                mGameLuaVideoManager.loadModle(new MLAdjustFilter.FaceDetectedListener(){
                    @Override
                    public void faceDetectd(MMCVInfo mmcvInfo) {

                    }
                });

            }

            @Override
            public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
                int encodeW = (int) getEncodeSize().getWidth();
                int encodeH = (int) getEncodeSize().getHeight();
                MDLog.i(LogTag.GameWolf.GameWolf,"surfaceChanged:width="+width+",height="+height
                        +",encodeW="+encodeW+ ",encodeH="+encodeH);
                if(encodeW == 0 || encodeH ==0){
                    mMediaStreamer.setPreviewSize(width, height);
                    mMediaStreamer.setEncoderSize(176,176);
                    mMediaStreamer.changeVideoEncodeSize();
                }else{
                    mMediaStreamer.setPreviewSize(width, height);
                    mMediaStreamer.setEncoderSize(encodeW,encodeH);
                    mMediaStreamer.changeVideoEncodeSize();
                }

            }

            @Override
            public void surfaceDestroyed(SurfaceHolder holder) {
                MDLog.i(LogTag.GameWolf.GameWolf,"surfaceDestroyed");
                if (mMediaStreamer != null) {
                    mMediaStreamer.setPreviewDisplay(null);
                }

            }
        });
        ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT);
        surface.setLayoutParams(layoutParams);
        videoMap.put(getUid(), surface);
        mMediaStreamer.setAvFlag(1);
    }
    //</editor-fold>


    private boolean isMainThread() {
        return MainThreadExecutor.isMainThread();
    }

    //展示美颜面板
    @LuaApiUsed
    public LuaValue[] showBeautyFace(LuaValue[] values) {
        initGameLuaVideoManager();
        mGameLuaVideoManager.showFaceSettingViewV2();
        return null;
    }

    //隐藏美颜面板
    @LuaApiUsed
    public LuaValue[] dismissBeautyFace(LuaValue[] values) {
        if (mGameLuaVideoManager != null){
            mGameLuaVideoManager.dimissFaceSettingViewV2();
        }
        return null;
    }

    /**
     * 切换摄像头
     * @param values
     * @return
     */
    @LuaApiUsed
    public LuaValue[] switchCamera(LuaValue[] values){
        if (mMediaStreamer != null){
            mMediaStreamer.switchCamera();
        }
        return null;
    }

    @Override
    @LuaApiUsed
    public LuaValue[] setRemoteVideoCanvas(LuaValue[] values) {
        UDViewGroup container = values.length > 0 ? (UDViewGroup) values[0].toUserdata() : null;
        int uid = values.length > 1 ? values[1].toInt() : 0;

        SurfaceView surfaceView = getRemoteView(uid);
        if (surfaceView != null && container != null && container.getView() != null) {

            ViewGroup containerView = ((ViewGroup) container.getView());
            containerView.removeAllViews();
            containerView.addView(surfaceView, new ViewGroup.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        }
        return null;
    }

    public void initGameLuaVideoManager(){
        if (mGameLuaVideoManager == null){
            mGameLuaVideoManager = new GameLuaLiveManager((Activity) getContext());
            mGameLuaVideoManager.setUDIJKConferenceStreamer(this);
        }
    }

    /**
     * 显示视频view 大小
     * @param values
     * @return
     */
    @LuaApiUsed
    public LuaValue[] showSurfaceView(LuaValue[] values){
        initGameLuaVideoManager();
        if (mGameLuaVideoManager != null){
            UDViewGroup udViewGroup = values.length > 0 ? (UDViewGroup) values[0].toUserdata() : null;
            ViewGroup container = (udViewGroup == null)? null : ((ViewGroup) udViewGroup.getView());
            int uid = values.length > 1 ? values[1].toInt() : 0;
            int radius = values.length>2? UIUtils.getPixels(values[2].toInt()):0;

            MDLog.i(LogTag.GameWolf.GameWolf,"showSurfaceView:uid="+uid);
            mGameLuaVideoManager.showSurfaceView(container,uid,radius);
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] showPreviewSurfaceView(LuaValue[] values){
        initGameLuaVideoManager();
        if (mGameLuaVideoManager != null){
            UDViewGroup udViewGroup = values.length > 0 ? (UDViewGroup) values[0].toUserdata() : null;
            ViewGroup container = (udViewGroup == null)? null : ((ViewGroup) udViewGroup.getView());
            int uid = values.length > 1 ? values[1].toInt() : 0;
            mGameLuaVideoManager.showPreviewSurfaceView(container, uid);
        }
        return null;
    }
    @LuaApiUsed
    public LuaValue[] showPreviewView(LuaValue[] values){
        MDLog.i(LogTag.GameWolf.GameWolf,"showPreviewView");
        initGameLuaVideoManager();
        if (mGameLuaVideoManager != null){

            mGameLuaVideoManager.showPreviewViewAndCheckPermission(new GameLuaLivePreviewDialog.GameLuaPreviewCallback() {
                @Override
                public void onCountDownEnd() {
                    if (onPreviewViewCountEndCallBack != null){
                        onPreviewViewCountEndCallBack.invoke(null);
                    }
                }

                @Override
                public void onCancelPreview() {
                    if (onPreviewViewCancelCallBack != null){
                        onPreviewViewCancelCallBack.invoke(null);
                    }
                }
            });
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] closePreviewView(LuaValue[] values){
        initGameLuaVideoManager();
        if (mGameLuaVideoManager != null){
            mGameLuaVideoManager.closePreviewView();
        }
        return null;
    }

    /**
     * 隐藏视频view
     * @param values
     * @return
     */
    @LuaApiUsed
    public LuaValue[] hideSurfaceView(LuaValue[] values){

        if (mGameLuaVideoManager != null){
            int uid = values.length > 0 ? values[0].toInt() : 0;
            MDLog.i(LogTag.GameWolf.GameWolf,"hideSurfaceView:" + uid);
            mGameLuaVideoManager.hideSurfaceView(uid,false);
        }
        return null;
    }


    @LuaApiUsed
    public LuaValue[] showFace(LuaValue[] values){
        initGameLuaVideoManager();
        if (mGameLuaVideoManager != null){
            mGameLuaVideoManager.showFace(values);
        }
        return null;
    }

    /**
     * 预留，未同步ios
     * @param values
     * @return
     */
    @LuaApiUsed
    public LuaValue[] resetCodec(LuaValue[] values){

        int encodeW = values.length>0? UIUtils.getPixels(values[0].toInt()):0;
        int encodeH = values.length>1? UIUtils.getPixels(values[1].toInt()):0;
        mMediaStreamer.resetCodec(encodeW, encodeH);
        return null;
    }
    @LuaApiUsed
    public LuaValue[] onVideoChannelAddedCallBack(LuaValue[] values){
        this.onVideoChannelAddedCallBack = values.length > 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] onPreviewViewCountEndCallBack(LuaValue[] values){
        this.onPreviewViewCountEndCallBack = values.length > 0 ? values[0].toLuaFunction() : null;
        return null;
    }
    @LuaApiUsed
    public LuaValue[] onPreviewViewCancelCallBack(LuaValue[] values){
        this.onPreviewViewCancelCallBack = values.length > 0 ? values[0].toLuaFunction() : null;
        return null;
    }
    @Override
    public void onVideoChannelAdded(long uid, SurfaceView surfaceView, int width, int height) {
        super.onVideoChannelAdded(uid, surfaceView, width, height);
        initGameLuaVideoManager();
        if (mGameLuaVideoManager != null) {
            mGameLuaVideoManager.onVideoChannelAdded((int) uid, surfaceView, 0, 0);
        }
        if (this.onVideoChannelAddedCallBack != null){

            MainThreadExecutor.post(getTaskTag(), new Runnable() {
                @Override
                public void run() {
                    onVideoChannelAddedCallBack.invoke(LuaValue.varargsOf( LuaNumber.valueOf(uid)));
                }
            });
        }
    }

    @Override
    public LuaValue[] joinChannel(LuaValue[] values) {
        LuaValue[] ret = super.joinChannel(values);
        initGameLuaVideoManager();
        if (mGameLuaVideoManager != null) {
            mGameLuaVideoManager.joinChannel();
        }
        return ret;
    }

    @Override
    public LuaValue[] leaveChannel(LuaValue[] values) {
        if (mGameLuaVideoManager != null) {
            mGameLuaVideoManager.leaveChannel();
        }
        return super.leaveChannel(values);
    }
}
