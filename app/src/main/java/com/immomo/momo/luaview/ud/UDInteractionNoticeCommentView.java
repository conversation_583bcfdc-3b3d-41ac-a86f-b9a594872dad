package com.immomo.momo.luaview.ud;


import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.fun.globals.UDLuaView;
import com.immomo.momo.luaview.LuaCommentLayout;

import org.luaj.vm2.Globals;
import org.luaj.vm2.LuaValue;

@LuaClass
public class UDInteractionNoticeCommentView {
    public static final String LUA_CLASS_NAME = "InteractionNoticeCommentView";

    private UDCommentView<LuaCommentLayout> commentView;

    public UDInteractionNoticeCommentView(Globals g, LuaValue[] v) {
        UDLuaView luaWindow = (UDLuaView) v[0].toUserdata();
        commentView = new UDCommentView<LuaCommentLayout>(g.getL_State(), new LuaValue[0]);
        LuaValue[] luaView = new LuaValue[]{commentView};
        luaWindow.addView(luaView);
    }

    @LuaBridge
    public LuaValue[] showPanel(LuaValue[] notice) {
        commentView.showPanel(notice);
        return null;
    }

    @LuaBridge
    public LuaValue[] setSendCommentCallBack(LuaValue[] values) {
        commentView.setSendCommentCallBack(values);
        return null;
    }

    @LuaBridge
    public LuaValue[] hidePanel(LuaValue[] values) {
        commentView.hidePanel(values);
        return null;
    }

    @LuaBridge
    public LuaValue[] resume(LuaValue[] values) {
        commentView.resume(values);
        return null;
    }

    @LuaBridge
    public LuaValue[] back(LuaValue[] values) {
        commentView.back(values);
        return null;
    }

    @LuaBridge
    public LuaValue[] isPanelShowing(LuaValue[] values) {
        return commentView.isPanelShowing(values);
    }

    @LuaBridge
    public LuaValue[] pause(LuaValue[] values) {
        commentView.pause(values);
        return null;
    }

    @LuaBridge
    public LuaValue[] destory(LuaValue[] values) {
        commentView.destory(values);
        commentView = null;
        return null;
    }

}
