package com.immomo.momo.luaview.lt;


import androidx.annotation.Nullable;

import com.alibaba.fastjson.JSONObject;
import com.cosmos.mdlog.MDLog;
import com.immomo.android.module.fundamental.Badge.UniformLabelsBean;
import com.immomo.framework.imageloader.ImageType;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.fun.ud.UDArray;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.task.ThreadUtils;
import com.immomo.moarch.account.AccountKit;
import com.immomo.moarch.account.AccountUser;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoKit;
import com.immomo.momo.common.AppKit;
import com.immomo.momo.guest.GuestConfig;
import com.immomo.momo.imageloader.MomoImageHandler;
import com.immomo.momo.messages.service.GroupMsgService;
import com.immomo.momo.mvp.maintab.task.UpdateBasicUserInfoTask;
import com.immomo.momo.personalprofile.data.api.response.ProfileAppendInfo;
import com.immomo.momo.personalprofile.data.api.response.ProfileUser;
import com.immomo.momo.personalprofile.module.domain.model.ExquisitePicModel;
import com.immomo.momo.personalprofile.module.domain.model.ExquisitePicTagModel;
import com.immomo.momo.personalprofile.module.domain.model.PersonalProfilePhotoModel;
import com.immomo.momo.personalprofile.module.domain.model.ProfileUserModel;
import com.immomo.momo.personalprofile.utils.LoadProfileLiveAvatarGifUtil;
import com.immomo.momo.profile.ConvertHelper;
import com.immomo.momo.protocol.http.UserApi;
import com.immomo.momo.protocol.http.core.HttpClient;
import com.immomo.momo.router.IProfileAppendInfo;
import com.immomo.momo.router.IProfileUser;
import com.immomo.momo.router.IProfileUserMood;
import com.immomo.momo.router.ProfileRealAuth;
import com.immomo.momo.router.ProfileRouter;
import com.immomo.momo.service.bean.AvatarFrame;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.service.sessions.MessageServiceHelper;
import com.immomo.momo.service.user.ProfileModelHelper;
import com.immomo.momo.service.user.ProfileUserService;
import com.immomo.momo.service.user.UserService;
import com.immomo.momo.setting.BasicUserInfoUtil;
import com.immomo.momo.user.UserInfoRequest;
import com.immomo.momo.util.ProfileImageBrowserUtils;
import com.immomo.momo.util.StringUtils;

import org.luaj.vm2.LuaTable;
import org.luaj.vm2.LuaValue;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import info.xudshen.android.appasm.AppAsm;

/**
 * Created by XiongFangyu on 2018/9/14.
 */
@LuaClass(isStatic = true)
public class LTMMUserProfile {
    public static final String LUA_CLASS_NAME = "MMUserProfile";

    private static boolean isFristCheckStatus = true;
    private static boolean allowShowOnlineStatus = false;

    //<editor-fold desc="API">
    @LuaBridge
    public static String getMmId() {
        return AppKit.getAccountManager().getCurrentAccountUserId();
    }

    @LuaBridge
    public static String userMoodImage() {
        String moodUrl = "";
        IProfileUser profileUser = AppAsm.getRouter(ProfileRouter.class).getProfileUser(MomoKit.getCurrentOrGuestMomoId());
        IProfileAppendInfo appendInfo = profileUser.getProfileAppendInfo();
        if (appendInfo != null) {
            IProfileUserMood userMood = appendInfo.getUserMood();
            if (userMood != null) {
                moodUrl = userMood.getMoodUrl();
            }
        }
        return moodUrl;
    }

    @LuaBridge
    public static boolean isGuest() {
        AccountUser user = AppKit.getAccountManager().getCurrentAccountUser();

        return user == null || user.isGuest() || GuestConfig.getInstance().isGuestMode();
    }

    @LuaBridge
    public static String getNickName() {
        AccountUser user = AppKit.getAccountManager().getCurrentAccountUser();
        if (user != null) {
            return user.getName();
        }
        return "";
    }

    /**
     * 获取当前用户头像框
     *
     * @return
     */
    @LuaBridge
    public static String getAvatarFrame(String momoid) {
        String avatarFrameData = "";
        if (StringUtils.isBlank(momoid)) return avatarFrameData;
        AvatarFrame avatarFrame = null;
        if (MomoKit.getCurrentOrGuestMomoId().equals(momoid)) {
            User currentUser = MomoKit.getCurrentUser();
            if (currentUser != null) {
                avatarFrame = currentUser.avatarFrame;
            }
        }
        if (avatarFrame != null) {
            avatarFrameData = avatarFrame.toJson().toString();
        }
        return avatarFrameData;
    }

    @LuaBridge
    public static void getAvatarFrameWithCallback(String momoid, LVCallback callback) {
        if (StringUtils.isBlank(momoid) || callback == null) return;
        MomoTaskExecutor.executeTask(ThreadUtils.TYPE_RIGHT_NOW, LUA_CLASS_NAME, new MomoTaskExecutor.Task<Object, Object, String>() {
            @Override
            protected String executeTask(Object[] objects) throws Exception {
                String avatarFrameData = "";
                AvatarFrame avatarFrame = null;
                if (MomoKit.getCurrentOrGuestMomoId().equals(momoid)) {
                    User currentUser = MomoKit.getCurrentUser();
                    if (currentUser != null) {
                        avatarFrame = currentUser.avatarFrame;
                    }
                } else {
                    ProfileUser profileUser = ProfileUserService.getInstance().get(momoid);
                    if (profileUser != null) {
                        avatarFrame = profileUser.avatarFrame;
                    } else {
                        UserInfoRequest.INSTANCE.fetchOtherProfileAsync(new User(momoid), null, null, null, null);
                    }
                }
                if (avatarFrame != null) {
                    avatarFrameData = avatarFrame.toJson().toString();
                }
                return avatarFrameData;
            }

            @Override
            protected void onTaskSuccess(String avatarFrameData) {
                super.onTaskSuccess(avatarFrameData);
                callback.call(avatarFrameData);
            }
        });
    }

    @LuaBridge
    public static String getSessionId() {
        String session = AppKit.getAccountManager().getCurrentAccountSession();
        return session != null ? session : "";
    }

    @LuaBridge
    public static String getAvatarURL(@Nullable String shorLink) {
        if (MomoKit.isVipUser() || MomoKit.isSVipUser()) {
            ProfileUserModel currentUserModel = ProfileModelHelper.getCurrentUserModel();
            if (currentUserModel != null) {
                List<PersonalProfilePhotoModel> photoList = currentUserModel.getPhotoList();
                if (photoList.size() > 0) {
                    String liveAvatarImg = LoadProfileLiveAvatarGifUtil.getLiveAvatarImg(photoList);
                    if (StringUtils.isNotBlank(liveAvatarImg)) {
                        return liveAvatarImg;
                    }
                }
            }
        }
        AccountUser user = AppKit.getAccountManager().getCurrentAccountUser();

        if (StringUtils.isEmpty(shorLink) && user != null && StringUtils.isNotEmpty(user.getAvatar()))
            return HttpClient.getImageHost() + MomoImageHandler.buildImageUrlAppend(user.getAvatar(), ImageType.IMAGE_TYPE_ALBUM_LARGE);

        if (StringUtils.isNotEmpty(shorLink) && shorLink.startsWith("http"))
            return shorLink;

        if (StringUtils.isNotEmpty(shorLink))
            return HttpClient.getImageHost() + MomoImageHandler.buildImageUrlAppend(shorLink, ImageType.IMAGE_TYPE_ALBUM_LARGE);

        return "";
    }

    /**
     * lua请求用户数据后，同步本地user数据库
     *
     * @param udArray
     */
    @LuaBridge
    public static void synchronizeUserProfileDataFromNetwork(final UDArray udArray) {
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                if (udArray == null || udArray.getArray() == null) {
                    return;
                }
                List source = udArray.getArray();

                List<User> userList = new ArrayList<>();
                for (int i = 0; i < source.size(); i++) {
                    try {
                        JSONObject fastJb = (JSONObject) source.get(i);
                        org.json.JSONObject sourceJb = new org.json.JSONObject(fastJb.toJSONString());

                        User user = new User();
                        UserApi.parseUserSample(user, sourceJb);

                        userList.add(user);

                    } catch (Exception e) {
                        MDLog.printErrStackTrace(LogTag.COMMON, e);
                    }
                }
                if (userList == null || userList.size() <= 0) {
                    return;
                }
                UserService.getInstance().saveUserSimple(userList);
            }
        };

        ThreadUtils.execute(ThreadUtils.TYPE_RIGHT_NOW, runnable);
    }

    /**
     * 是否是打招呼
     *
     * @param momoid
     * @return
     */
    @LuaBridge
    public static boolean isShowGreet(String momoid) {
        User user = UserService.getInstance().getSimpleUser(momoid);
        if (user != null) {
            return user.getShowGreet() == 1;
        }
        return true;
    }

    /**
     * @return 是否显示在线状态（用户前三次登录）
     */
    @LuaBridge
    public static boolean allowShowOnlineStatus() {

        return false;
    }

    /**
     * @return 是否打开用户在线 AB 测试开关
     */
    @LuaBridge
    public static boolean isUserOnlineABTestOn() {
        return true;
    }

    /**
     * @return 用户在线时长阈值
     */
    @LuaBridge
    public static int onlineTimeIntervalThreshhold() {
        return KV.getUserInt(SPKeys.User.InitTask.KEY_ONLINE_TIME, 600);
    }

    /**
     * @return 获取用户性别，男：M，女：F
     */
    @LuaBridge
    public static String getGender() {
        AccountUser user = AppKit.getAccountManager().getCurrentAccountUser();
        String gender = "";
        if (user != null) {
            switch (user.getGender()) {
                case 1:
                    gender = "M";
                    break;
                case 2:
                    gender = "F";
                    break;
                default:
                    break;
            }
        }
        return gender;
    }

    @LuaBridge
    public static int getUserAge() {
        AccountUser user = AppKit.getAccountManager().getCurrentAccountUser();
        if (user != null) {
            return user.getAge();
        }
        return 0;
    }

    @LuaBridge
    public static long getBalance() {
        return BasicUserInfoUtil.INSTANCE.getBalance();
    }

    @LuaBridge
    public static void setBalance(long balance) {
        BasicUserInfoUtil.INSTANCE.setBalance(balance);
    }

    /**
     * 年费svip4，不是年费3，普通年费2，非年费1，什么都不是就是0
     *
     * @return
     */
    @LuaBridge
    public static int getVipType() {
        if (UserService.getInstance().getCurrentUserIsVipOrSvip()) {
            if (ProfileModelHelper.getCurrentUserModel().isSVip()) {
                if (ProfileModelHelper.getCurrentUserModel().isYearVip()) {
                    return 4;
                } else {
                    return 3;
                }
            } else if (ProfileModelHelper.getCurrentUserModel().isVip()) {
                if (ProfileModelHelper.getCurrentUserModel().isYearVip()) {
                    return 2;
                } else {
                    return 1;

                }
            }

        }
        return 0;
    }


    @LuaBridge
    public static boolean couldShowAboutMeAlbums(String momoid) {
        if (momoid.equals(ProfileModelHelper.getCurrentId())) {
            return true;
        }
        if (StringUtils.isEmpty(ProfileModelHelper.getCurrentId())) {
            return false;
        }
        ProfileUserModel currentProfileUser = ProfileModelHelper.getUserModel(ProfileModelHelper.getCurrentId());
        if (currentProfileUser != null) {
            if (currentProfileUser.isFemale()) {
                return true;
            }
            ProfileUserModel profileUser = ProfileModelHelper.getUserModel(momoid);
            if (profileUser != null && profileUser.getPrivilegeModel() != null
                    && profileUser.getPrivilegeModel().orNull() != null
                    && StringUtils.isNotEmpty(Objects.requireNonNull(profileUser.getPrivilegeModel().orNull()).getNewUserPrompt())) {
                return true;
            }
            if (currentProfileUser.getPhotos() != null && currentProfileUser.getPhotos().length > 0) {
                return currentProfileUser.getPhotos().length >= 3;
            }
        }
        return false;
    }

    @LuaBridge
    public static boolean userIsQAEffect(String momoid) {
        ProfileUserModel profileUser = ProfileModelHelper.getUserModel(momoid);
        if (profileUser != null) {
            return (profileUser.getAnswerGuide() != null && !profileUser.getAnswerGuide().isEmpty());
        }
        return false;
    }

    @LuaBridge
    public static void openUserAlbum(LuaTable params) {
        if (params == null) {
            return;
        }
        int index = Integer.parseInt(valueOf(params, "index"));
        String momoid = valueOf(params, "momoid");

        ProfileUserModel profileUser = ProfileModelHelper.getUserModel(momoid);
        if (profileUser == null) {
            return;
        }
        List<ExquisitePicModel> picModels = profileUser.getExquisitePics();
        List<String> thumbList = new ArrayList<>();
        List<String> originUrls = new ArrayList<>();
        List<ProfileAppendInfo.PicLabel> picLabels = new ArrayList<>();

        for (ExquisitePicModel model : picModels) {
            thumbList.add(model.getThumbUrl());
            originUrls.add(model.getOriginUrl());
            ExquisitePicTagModel picTagModel = model.getPicTag().orNull();
            ProfileAppendInfo.PicLabel picLabel = new ProfileAppendInfo.PicLabel();
            picLabel.id = picTagModel.getId();
            picLabel.name = picTagModel.getName();
            picLabel.bgColor = picTagModel.getBgColor();
            picLabel.iconSelected = picTagModel.getIconBig();
            picLabel.fontColorBig = picTagModel.getFontColorBig();
            picLabel.bigIcon = picTagModel.getIconBig();
            picLabel.iconNormal = picTagModel.getIconNormal();
            picLabels.add(picLabel);
        }

        ProfileImageBrowserUtils.openAlbumPicBrowser(index, MomoKit.getTopActivity(),
                thumbList, originUrls, picLabels, null, momoid, false, true);
    }

    private static String valueOf(LuaTable params, String key) {
        if (params == null) {
            return "";
        }

        LuaValue value = params.get(key);
        if (value.isString() || value.isNumber()) {
            return value.toJavaString();
        } else if (value.isBoolean()) {
            return value.toBoolean() ? "1" : "0";
        } else {
            return "";
        }
    }

    public static User getOrCreateUser() {
        AccountUser accountUser = AccountKit.getAccountManager().getCurrentAccountUser();
        User user = null;
        if (accountUser != null) {
            user = accountUser.getAdaptiveUser();
        }
        if (user == null && AccountKit.getAccountManager().getCurrentAccountUserId() != null) {
            user = new User(AccountKit.getAccountManager().getCurrentAccountUserId());
        }
        return user;
    }

    //获取真人认证信息
    @LuaBridge
    public static Map<String, String> getRealAuthInfo() {
        Map<String, String> info = new HashMap<>();
        User user = getOrCreateUser();
        if (null != user) {
            ProfileRealAuth auth = user.realAuth;
            if (null != auth) {
                info.put("gotoStr", auth.gotoStr);
                info.put("icon", auth.icon);
                info.put("status", String.valueOf(auth.status));
            }
        }
        return info;
    }


    @LuaBridge
    public static int getFriendCount() {
        return BasicUserInfoUtil.INSTANCE.getFriendCount();
    }

    @LuaBridge
    public static int getFollowCount() {
        return BasicUserInfoUtil.INSTANCE.getFollowingCount();
    }

    @LuaBridge
    public static int getFansCount() {
        return BasicUserInfoUtil.INSTANCE.getFollowerCount();
    }

    @LuaBridge
    public static int getGroupCount() {
        return BasicUserInfoUtil.INSTANCE.getCurUserGroupCount();
    }

    @LuaBridge
    public static int getDiscussCount() {
        return BasicUserInfoUtil.INSTANCE.getMyDiscussCount();
    }

    @LuaBridge
    public static int getGroupUnreadCount() {
        return _getGroupUnreadMsgCount();
    }

    private static int _getGroupUnreadMsgCount() {
        return MessageServiceHelper.getInstance().getGroupMessageUnread()
                + MessageServiceHelper.getInstance().getDiscussMessageUnread();
    }

    @LuaBridge
    public static void getGroupUnreadCountAsync(final LVCallback callback) {
        ThreadUtils.execute(ThreadUtils.TYPE_MESSAGE, new Runnable() {
            @Override
            public void run() {
                int count = _getGroupUnreadMsgCount();
                MomoMainThreadExecutor.post(() -> {
                    if (callback != null) {
                        callback.call(count);
                    }
                });
            }
        });
    }

    @LuaBridge
    public static void getTargetGroupsUnreadCount(UDArray udArray, LVCallback callback) {
        if (udArray == null || udArray.getArray() == null) {
            callback.call(new HashMap<String, Integer>());
            return;
        }
        List ids = udArray.getArray();

        List<String> groupIds = new ArrayList<>();
        for (int i = 0; i < ids.size(); i++) {
            groupIds.add(ids.get(i).toString());
        }
        MomoTaskExecutor.executeUserTask("MMUserProfile", new MomoTaskExecutor.Task() {
            @Override
            protected Object executeTask(Object[] objects) throws Exception {
                return GroupMsgService.getInstance().getUnreadCountsByGroupIds(groupIds);
            }

            @Override
            protected void onTaskSuccess(Object o) {
                if (o instanceof Map) {
                    Map<String, Integer> map = (Map<String, Integer>) o;
                    if (callback != null) {
                        callback.call(map);
                    }
                }
            }

            @Override
            protected void onTaskError(Exception e) {

            }
        });
    }

    @LuaBridge
    public static void fetchPerfonalInfo() {
        MomoTaskExecutor.executeTask(ThreadUtils.TYPE_INNER, LUA_CLASS_NAME, new UpdateBasicUserInfoTask());
    }

    @LuaBridge
    public static boolean getMiniVipState() {
        return ConvertHelper.Companion.isMiniVip();
    }
    //</editor-fold>

    /**
     * 谷歌是否在审核中
     *
     * @return
     */
    @LuaBridge
    public static boolean isGoogleReviewing() {
        return MomoKit.isGoogleReviewing();
    }


    @LuaBridge
    public static String getP2pUniformInfo() {
        String badgeStr = "";
        try {
            IProfileUser profileUser = ProfileUserService.getInstance().getMemProfileUser(MomoKit.getCurrentOrGuestMomoId());
            if (profileUser != null) {
                UniformLabelsBean badgeModels = profileUser.getP2PUniformLabels();
                badgeStr = JSONObject.toJSONString(badgeModels);
            }
        } catch (Exception e) {
            MDLog.printErrStackTrace("LTMMUserProfile", e);
        }
        return badgeStr;
    }

    @LuaBridge
    public static String getNameColor() {
        String nameColor = "";
        ProfileUserModel userModel = ProfileModelHelper.getCurrentUserModel();
        if (userModel != null) {
            nameColor = userModel.getNameColor();
        }
        return nameColor;
    }

}
