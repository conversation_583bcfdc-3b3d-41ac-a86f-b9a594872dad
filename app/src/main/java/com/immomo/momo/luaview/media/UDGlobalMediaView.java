package com.immomo.momo.luaview.media;

import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;

import com.immomo.mls.fun.ud.view.UDViewGroup;
import com.immomo.mls.utils.MainThreadExecutor;
import com.immomo.mmutil.StringUtils;
import com.immomo.momo.R;
import com.immomo.momo.luaview.weight.IGlobalMediaEventListener;
import com.immomo.momo.luaview.weight.LuaGlobalMediaView;
import com.immomo.momo.mvp.maintab.mainimpl.appconfig.HttpsAppConfigV1;

import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaNumber;
import org.luaj.vm2.LuaString;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

/**
 * author: hongming.wei
 * data: 2023/2/2
 */

@LuaApiUsed
public class UDGlobalMediaView<V extends LuaGlobalMediaView> extends UDViewGroup<V> implements IGlobalMediaEventListener {

    public static final String LUA_CLASS_NAME = "GlobalIJKPlayer";

    private LuaGlobalMediaView globalMediaView;
    private Uri mUri;

    private LuaFunction singleClick;
    private LuaFunction doubleClick;
    private LuaFunction playerState;
    private LuaFunction playPosition;
    private LuaFunction idleState;
    private LuaFunction stateEnded;
    private LuaFunction bufferState;
    private LuaFunction playState;
    private LuaFunction surfaceUpdated;

    public static final String[] methods = {
            "setLoopPlay",
            "setScalableType",
            "audioPlay",
            "videoPlay",
            "resume",
            "pause",
            "release",
            "getCurrentUri",
            "clearPlayPosition",
            "onSingleClick",
            "onDoubleClick",
            "onPlayerStateChanged",
            "onPlayPosition",
            "releaseVideoTexture",
            "isPlaying",
            "silenceMode",
            "seekTo",
            "getDuration",
            "getCurrentPosition",
            "setToIdleState",
            "setToStateEnded",
            "setToBufferState",
            "setToPlayState",
            "onSurfaceUpdated",
            "setMediaViewSize"
    };


    @LuaApiUsed
    public UDGlobalMediaView(long L, LuaValue[] v) {
        super(L, v);
    }


    @Override
    protected V newView(LuaValue[] init) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.layout_global_media_video_view, null);
        globalMediaView = view.findViewById(R.id.lua_global_media_view);
        globalMediaView.setMediaEventListener(this);
        return (V) globalMediaView;
    }

    @LuaApiUsed
    public LuaValue[] setMediaViewSize(LuaValue[] values) {
        if (values.length > 0 && globalMediaView != null) {
            globalMediaView.setMediaViewSize(values[0].toFloat());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setLoopPlay(LuaValue[] values) {
        if (values.length > 0 && globalMediaView != null) {
            globalMediaView.setLoopPlay(values[0].toBoolean());
        }
        return null;
    }


    @LuaApiUsed
    public LuaValue[] setScalableType(LuaValue[] values) {
        if (values.length > 0 && globalMediaView != null) {
            globalMediaView.setScalableType(values[0].toInt());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] audioPlay(LuaValue[] values) {
        if (values.length == 0) {
            return LuaValue.rString(mUri.toString());
        }
        String url = values[0].toJavaString();
        String feedId = values[1].toJavaString();
        boolean isAutoPlay = values[2].toBoolean();
        String videoSource = values[3].toJavaString();
        String eventId = values[4].toJavaString();
        String secondType = values[5].toJavaString();
        String tagDesc = values[6].toJavaString();
        String luaParams = values[7].toJavaString();
        if (StringUtils.isNotEmpty(url)) {
            url = HttpsAppConfigV1.INSTANCE.replaceVideoCdn(url);
            mUri = Uri.parse(url);
            if (globalMediaView != null) {
                globalMediaView.audioPlay(mUri,
                        StringUtils.isNotEmpty(feedId) ? feedId : "",
                        isAutoPlay,
                        StringUtils.isNotEmpty(videoSource) ? videoSource : "",
                        StringUtils.isNotEmpty(eventId) ? eventId : "",
                        StringUtils.isNotEmpty(secondType) ? secondType : "",
                        StringUtils.isNotEmpty(tagDesc) ? tagDesc : "",
                        StringUtils.isNotEmpty(luaParams) ? luaParams : "");
            }
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] silenceMode(LuaValue[] values) {
        if (values != null && values.length >= 1) {
            boolean muteMode = values[0].toBoolean();
            if (globalMediaView != null) {
                globalMediaView.silenceMode(muteMode);
            }
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] videoPlay(LuaValue[] values) {
        if (values.length == 0) {
            return LuaValue.rString(mUri.toString());
        }
        String url = values[0].toJavaString();
        String feedId = values[1].toJavaString();
        boolean isAutoPlay = values[2].toBoolean();
        String videoSource = values[3].toJavaString();
        String eventId = values[4].toJavaString();
        String secondType = values[5].toJavaString();
        String tagDesc = values[6].toJavaString();
        String luaParams = values[7].toJavaString();
        if (StringUtils.isNotEmpty(url)) {
            url = HttpsAppConfigV1.INSTANCE.replaceVideoCdn(url);
            mUri = Uri.parse(url);
            if (globalMediaView != null) {
                globalMediaView.videoPlay(mUri,
                        StringUtils.isNotEmpty(feedId) ? feedId : "",
                        isAutoPlay,
                        StringUtils.isNotEmpty(videoSource) ? videoSource : "",
                        StringUtils.isNotEmpty(eventId) ? eventId : "",
                        StringUtils.isNotEmpty(secondType) ? secondType : "",
                        StringUtils.isNotEmpty(tagDesc) ? tagDesc : "",
                        StringUtils.isNotEmpty(luaParams) ? luaParams : "");
            }
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] resume(LuaValue[] values) {
        if (globalMediaView != null) {
            globalMediaView.resume();
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] pause(LuaValue[] values) {
        if (globalMediaView != null) {
            globalMediaView.pause();
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] release(LuaValue[] values) {
        if (globalMediaView != null) {
            globalMediaView.release();
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] getCurrentUri(LuaValue[] values) {
        if (globalMediaView != null && globalMediaView.getCurrentUri() != null) {
            return LuaValue.varargsOf(LuaString.valueOf(String.valueOf(globalMediaView.getCurrentUri())));
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] clearPlayPosition(LuaValue[] values) {
        if (globalMediaView != null) {
            globalMediaView.clearPlayPosition();
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] onSingleClick(LuaValue[] values) {
        singleClick = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] onDoubleClick(LuaValue[] values) {
        doubleClick = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] onPlayerStateChanged(LuaValue[] values) {
        playerState = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] onPlayPosition(LuaValue[] values) {
        playPosition = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] releaseVideoTexture(LuaValue[] values) {
        if (globalMediaView != null) {
            globalMediaView.releaseVideoTexture();
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] isPlaying(LuaValue[] values) {
        if (globalMediaView != null) {
            return LuaValue.varargsOf(globalMediaView.isPlaying() ? LuaValue.True() : LuaValue.False());
        }
        return LuaValue.varargsOf(LuaValue.False());
    }

    @LuaApiUsed
    public LuaValue[] seekTo(LuaValue[] values) {
        if (globalMediaView != null && values.length > 0) {
            globalMediaView.seekTo(values[0].toLong());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] getDuration(LuaValue[] values) {
        if (globalMediaView != null) {
            return LuaValue.varargsOf(LuaNumber.valueOf(globalMediaView.getDuration()));
        }
        return LuaValue.varargsOf(LuaNumber.valueOf(0));
    }

    @LuaApiUsed
    public LuaValue[] getCurrentPosition(LuaValue[] values) {
        if (globalMediaView != null) {
            return LuaValue.varargsOf(LuaNumber.valueOf(globalMediaView.getCurrentPosition()));
        }
        return LuaValue.varargsOf(LuaNumber.valueOf(0));
    }

    @LuaApiUsed
    public LuaValue[] setToIdleState(LuaValue[] values) {
        idleState = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setToStateEnded(LuaValue[] values) {
        stateEnded = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setToBufferState(LuaValue[] values) {
        bufferState = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setToPlayState(LuaValue[] values) {
        playState = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }


    @LuaApiUsed
    public LuaValue[] onSurfaceUpdated(LuaValue[] values) {
        surfaceUpdated = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }


    @Override
    public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {

        if (playerState != null) {
            if (MainThreadExecutor.isMainThread()) {
                playerState.invoke(LuaValue.varargsOf(playWhenReady ? LuaValue.True() : LuaValue.False(), LuaNumber.valueOf(playbackState)));

            } else {
                MainThreadExecutor.post(() -> playerState.invoke(LuaValue.varargsOf(playWhenReady ? LuaValue.True() : LuaValue.False(), LuaNumber.valueOf(playbackState))));
            }
        }
    }

    @Override
    public void onSingleTapConfirmed() {
        if (singleClick != null) {
            singleClick.fastInvoke();
        }
    }

    @Override
    public void onDoubleTap() {
        if (doubleClick != null) {
            doubleClick.fastInvoke();
        }
    }


    @Override
    public void onPlayPosition(long position, long duration) {
        if (playPosition != null) {
            if (MainThreadExecutor.isMainThread()) {
                playPosition.invoke(LuaValue.varargsOf(LuaNumber.valueOf(position), LuaNumber.valueOf(duration)));
            } else {
                MainThreadExecutor.post(() -> playPosition.invoke(LuaValue.varargsOf(LuaNumber.valueOf(position), LuaNumber.valueOf(duration))));
            }
        }
    }

    @Override
    public void setToIdleState() {
        if (idleState != null) {
            if (MainThreadExecutor.isMainThread()) {
                idleState.fastInvoke();
            } else {
                MainThreadExecutor.post(() -> idleState.fastInvoke());
            }
        }
    }

    @Override
    public void setToStateEnded(int playCount) {
        if (stateEnded != null) {
            if (MainThreadExecutor.isMainThread()) {
                stateEnded.invoke(LuaValue.varargsOf(LuaNumber.valueOf(playCount)));
            } else {
                MainThreadExecutor.post(() -> stateEnded.invoke(LuaValue.varargsOf(LuaNumber.valueOf(playCount))));
            }
        }
    }

    @Override
    public void setToBufferState() {
        if (bufferState != null) {
            if (MainThreadExecutor.isMainThread()) {
                bufferState.fastInvoke();
            } else {
                MainThreadExecutor.post(() -> bufferState.fastInvoke());
            }
        }
    }

    @Override
    public void setToPlayState() {
        if (playState != null) {
            if (MainThreadExecutor.isMainThread()) {
                playState.fastInvoke();
            } else {
                MainThreadExecutor.post(() -> playState.fastInvoke());
            }
        }
    }

    @Override
    public void onSurfaceUpdated() {
        if (surfaceUpdated != null) {
            if (MainThreadExecutor.isMainThread()) {
                surfaceUpdated.fastInvoke();
            } else {
                MainThreadExecutor.post(() -> surfaceUpdated.fastInvoke());
            }
        }
    }
}
