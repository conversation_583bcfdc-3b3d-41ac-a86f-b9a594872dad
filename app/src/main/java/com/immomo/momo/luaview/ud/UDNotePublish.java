package com.immomo.momo.luaview.ud;

import com.immomo.framework.base.BaseActivity;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.MainThreadExecutor;
import com.immomo.momo.MomoKit;
import com.immomo.momo.mk.bridge.PublishFeedBridgeData;
import com.immomo.momo.mk.bridge.PublishFeedHelper;
import com.immomo.momo.util.GsonUtils;

import org.jetbrains.annotations.Nullable;
import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaValue;

import java.util.HashMap;

/**
 * -----------------------------------------------------------------
 * Copyright (C)  sun
 * Create: 2022/5/30 11:59 AM
 * -----------------------------------------------------------------
 */
@LuaClass
public class UDNotePublish {
    public static final String LUA_CLASS_NAME = "NotePublish";
    private PublishFeedHelper publishFeedHelper = new PublishFeedHelper();
    public static boolean flag = false;
    private BaseActivity activity;

    public UDNotePublish() {
        if (MomoKit.getTopActivity() instanceof BaseActivity) {
            activity = (BaseActivity) MomoKit.getTopActivity();
        }
    }

    public void __onLuaGc() {
        if (publishFeedHelper != null) {
            publishFeedHelper.release();
        }
    }

    @LuaBridge
    public void publish(LuaValue[] values) {
        if (values == null || values.length <= 1) {
            return;
        }
        try {
            if (activity == null) {
                return;
            }
            String json = values[0].toJavaString();
            LuaFunction function = values[1].toLuaFunction();
            PublishFeedBridgeData publishFeedBridgeData = GsonUtils.g().fromJson(json, PublishFeedBridgeData.class);
            HashMap<String, String> publishInfo = publishFeedBridgeData.getPublishInfo();
            if (publishInfo != null && publishInfo.containsKey("src")) {
                publishFeedHelper.setFromNote("noteFeed".equals(publishInfo.get("src")));
            }
            publishFeedHelper.setPublishListener(new PublishFeedHelper.IPublishCallBackListener() {
                @Override
                public void publishCallBack(int status, @Nullable String message) {
                    MainThreadExecutor.post(() -> {
                        function.fastInvoke(status == 0);
                    });
                }
            });
            publishFeedHelper.upload(publishFeedBridgeData, activity, new PublishFeedHelper.IPublishResultListener() {
                @Override
                public void checkSuccess() {
                }

                @Override
                public void checkError() {
                    MainThreadExecutor.post(() -> {
                        function.fastInvoke(false);
                    });
                }
            });

            publishFeedHelper.setNoteErrorListener(() -> MainThreadExecutor.post(() -> function.fastInvoke(false)));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
