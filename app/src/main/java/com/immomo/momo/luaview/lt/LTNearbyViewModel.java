package com.immomo.momo.luaview.lt;

import static com.immomo.momo.pay.PayConst.VipSource.SOURCE_SVIP_FREE_ADVERTISING;
import static com.immomo.momo.protocol.http.core.HttpClient.Imei;
import static com.immomo.momo.protocol.http.core.HttpClient.Net;

import android.content.Context;
import android.text.TextUtils;

import com.immomo.android.module.nearbypeople.lua.util.NearbyPeopleHelper;
import com.immomo.android.router.momo.GotoRouter;
import com.immomo.android.router.pay.PayConst;
import com.immomo.framework.imageloader.ImageType;
import com.immomo.mls.MLSEngine;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.fun.ud.UDArray;
import com.immomo.mmutil.NetUtils;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.task.ThreadUtils;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.UrlConstant;
import com.immomo.momo.android.synctask.ClosePeopleTask;
import com.immomo.momo.android.view.dialog.MAlertListDialog;
import com.immomo.momo.android.view.dialog.OnItemSelectedListener;
import com.immomo.momo.feed.FeedModelUtils;
import com.immomo.momo.imageloader.MomoImageHandler;
import com.immomo.momo.innergoto.helper.ActivityHandler;
import com.immomo.momo.innergoto.helper.NavigateHelper;
import com.immomo.momo.innergoto.log.CompleteGoto;
import com.immomo.momo.mvp.nearby.NearbyFilterHelper;
import com.immomo.momo.mvp.nearby.tasks.LoadPopupAdTask;
import com.immomo.momo.pay.PayVipBootHelper;
import com.immomo.momo.protocol.http.core.HttpClient;
import com.immomo.momo.router.ProfileGotoOptions;
import com.immomo.momo.router.ProfileRouter;
import com.immomo.momo.router.RefreshTag;
import com.immomo.momo.util.DeviceUtils;
import com.immomo.momo.util.StringUtils;
import com.immomo.momo.util.device.DeviceIdHelper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import info.xudshen.android.appasm.AppAsm;

/**
 * Created by zhang.ke
 * on 2018/10/10
 */
@LuaClass(isStatic = true)
public class LTNearbyViewModel {
    public static final String LUA_CLASS_NAME = "NearbyViewModel";

    private static Context getContext() {
        return MLSEngine.getContext();
    }

    /**
     * 获取附近的人列表网络请求的post参数（不用包含经纬度！），
     * 包括filter信息，首页入口是否显示，广告商业化要求的一些参数)等。
     *
     * @return
     */
    @LuaBridge
    public static Map getNearbyListHttpPostParams(boolean isFirstPage) {
        Map<String, String> map = NearbyPeopleHelper.Companion.buildRequestParam(NearbyFilterHelper.getFilter());
        appendExtraInfo(map);
        return map;
    }

    /**
     * 附近在玩（同城）帧获取广告相关参数
     */
    @LuaBridge
    public static Map getNearbyHttpADParams() {
        Map<String, String> map = new HashMap<>();
        appendExtraInfo(map);
        return map;
    }

    /**
     * 请求参附附加额外设备信息(net\imei...)
     * 代码来自 @link com.immomo.momo.protocol.http.core.HttpClient#appendExtraInfo()
     */
    protected static void appendExtraInfo(Map<String, String> formData) {
        if (formData == null)
            return;

        formData.put(Net, String.valueOf(NetUtils.getNetWorkStatus()));

        String imei = DeviceIdHelper.getIMEI();
        if (!TextUtils.isEmpty(imei)) {
            formData.put(Imei, imei);
        }

        formData.putAll(DeviceUtils.getDeviceInfo());
    }

    /**
     * 点击theme 40 的cell的avatar后的事件
     *
     * @param momoid
     */
    @LuaBridge
    public static void onclickNearbyMatchRecommendCellAvatar(String momoid) {
        Map<String, String> m = new HashMap<>();
        m.put("likemeid", momoid);
        String gotoStr = CompleteGoto.Companion.getSimpleNewGoto("", "goto_nearby_match_list", m);
        AppAsm.getRouter(GotoRouter.class).executeAction(gotoStr, MomoKit.getTopActivity());
    }

    /**
     * 点击theme 33 的cell的arrow后的事件; 点击theme 35, theme 36 delete button的事件
     */
    @LuaBridge
    public static void onclickAdvertiseCellDeleteOrArrowButton(final Map option) {
        final String adId = (String) option.get("id");
        final int index = (int) option.get("index");
        final String logId = (String) option.get("logid");
        final int theme = (int) option.get("theme");

        final ArrayList<String> options = new ArrayList<>();
        options.add("开通SVIP屏蔽广告");
        options.add("不感兴趣");
        options.add("取消");
        Context context = MomoKit.getTopActivity();
        final MAlertListDialog moreDialog = new MAlertListDialog(context, options);
        moreDialog.setTitle(R.string.dialog_title_option);
        moreDialog.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int logIndex) {
                String option = options.get(logIndex);
                if ("不感兴趣".equals(option)) {
                    if (adId != null) {
                        MomoTaskExecutor.executeUserTask("closead", new ClosePeopleTask(adId, index));
                    }
                } else if ("取消".equals(option)) {
                    moreDialog.dismiss();
                } else if ("开通SVIP屏蔽广告".equals(option)) {
                    //NearbyPeopleItem.API_THEME_AD 历史遗留
                    if (theme == 33) {
                        if (!MomoKit.isSVipUser()) {
                            PayVipBootHelper.startPayVipPage(context, PayConst.VipType.SVIP_TYPE, SOURCE_SVIP_FREE_ADVERTISING);
                        } else {
                            NavigateHelper.startPassportWebview(context, UrlConstant.URL_MEMBER_CENTER);
                        }
                    } else {
                        NavigateHelper.startPassportWebview(context, UrlConstant.URL_BUY_VIP);
                    }
                }
            }
        });
        moreDialog.setSupportDark(true);
        moreDialog.show();
    }

    /**
     * theme 33 如果有浮层gif，则原生需要去下载
     *
     * @param layerADAddress
     */
    @LuaBridge
    public static void downloadThirdPartyADCellLayerAD(String layerADAddress) {
        if (!TextUtils.isEmpty(layerADAddress)) {
            LoadPopupAdTask adTask = new LoadPopupAdTask(layerADAddress);
            MomoTaskExecutor.cancleAllTasksByTag(adTask.getTaskTag());
            MomoTaskExecutor.executeInnerTask(adTask.getTaskTag(), adTask);
        }
    }

    @LuaBridge
    public static void handleThirdPartyADClickLog(UDArray clickLog) {
        // 附近人直播中点击日志回传
        if (clickLog != null && clickLog.getArray() != null) {
            runLogAction(getContext(), clickLog.getArray());
        }
    }

    @LuaBridge
    public static void pushToUserProfilePage(String momoid) {
        ProfileGotoOptions options = new ProfileGotoOptions(momoid);
        options.setRequestTypeTag(RefreshTag.LOCAL);
        AppAsm.getRouter(ProfileRouter.class).gotoProfile(getContext(), options);
    }

    /**
     * 根据guid获取avatar完整图片地址
     *
     * @param guid 图片链接
     * @return
     */
    @LuaBridge
    public static String getAvatarFullUrlStringWithGuid(String guid) {
        return MomoImageHandler.generateImageUrl(guid, ImageType.IMAGE_TYPE_ALBUM_250x250);
    }

    /**
     * 去除字符串中的换行符/r, /n等
     *
     * @param guid 图片链接
     * @return
     */
    @LuaBridge
    public static String removeWrapForText(String guid) {
        if (guid == null) {
            return guid;
        }

        return guid.replaceAll("\r|\n", "");
    }

    /**
     * 获取tableView的顶部offset，iOS中返回>0；android 返回0
     *
     * @return
     */
    @LuaBridge
    public static int getTableViewTopPadding() {
        return 0;
    }

    /**
     * 执行统计log
     *
     * @param logs
     */
    private static void runLogAction(Context context, final List<String> logs) {
        if (logs == null || logs.size() == 0) {
            return;
        }
        for (final String logstr : logs) {
            if (TextUtils.isEmpty(logstr)) {
                continue;
            }

            final String tempLogStr = FeedModelUtils.INSTANCE.parseStatisticGoto(logstr);
            if (TextUtils.isEmpty(tempLogStr)) continue;

            if (StringUtils.isStartWithHttpOrHttps(tempLogStr)) {
                Runnable thread = new Runnable() {
                    @Override
                    public void run() {
                        try {
                            HttpClient.doThirdPartGet(tempLogStr, null, null);
                        } catch (Exception ignore) {
                        }
                    }
                };
                ThreadUtils.execute(ThreadUtils.TYPE_INNER, thread);
            } else {
                ActivityHandler.executeAction(tempLogStr, context);
            }
        }
    }

    @LuaBridge
    public static boolean shouldShowNearbyGuideBubble() {
        return false;
    }

    @LuaBridge
    public static void setHasShownNearbyGuideBubble() {
    }

    /**
     * 目前只有：新版小蓝框曝光打点 使用
     *
     * @param action
     * @param source
     */
    @LuaBridge
    public static void uploadV1Log(String action, String source) {

    }

    /**
     * iOS中tableView滑动回调；android空实现
     */
    @LuaBridge
    public static void tableViewScrollBegin(double x, double y) {

    }

    /**
     * iOS中tableView滑动回调；android空实现
     */
    @LuaBridge
    public static void tableViewScrolling(double x, double y) {

    }

    /**
     * iOS中tableView滑动回调；android空实现
     */
    @LuaBridge
    public static void tableViewEndDragging(double x, double y, boolean decelerate) {

    }

    /**
     * iOS中tableView滑动回调；android空实现
     */
    @LuaBridge
    public static void tableViewEndScroll(double x, double y) {

    }
}
