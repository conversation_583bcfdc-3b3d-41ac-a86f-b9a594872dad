package com.immomo.momo.luaview.weight;

import android.content.Context;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.ProgressBar;

import com.immomo.momo.R;

/**
 * author: hongming.wei
 * data: 2023/2/20
 */
public class LuaVideoProgressBar extends LinearLayout {

    private ProgressBar progressBar;

    public LuaVideoProgressBar(Context context) {
        super(context);
        initView();
    }


    private void initView(){
        LayoutInflater.from(getContext()).inflate(R.layout.layout_video_progress_bar, this, true);
        progressBar = findViewById(R.id.progressBar);
    }

    public void setMax(int max) {
        if (progressBar != null) {
            progressBar.setMax(max);
        }
    }

    public void setProgressDrawable(){
        if (progressBar != null) {
            progressBar.setProgressDrawable(getContext().getDrawable(R.drawable.video_play_progressbar));
        }
    }

    public void setMaxHeight(int height) {
        if (progressBar != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                progressBar.setMaxHeight(height);
            }
        }
    }

    public void setProgress(int progress) {
        if (progressBar != null) {
            progressBar.setProgress(progress);
        }
    }



}
