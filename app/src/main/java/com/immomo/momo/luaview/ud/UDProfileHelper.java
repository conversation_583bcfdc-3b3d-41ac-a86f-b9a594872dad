package com.immomo.momo.luaview.ud;

import android.Manifest;
import android.app.Activity;
import android.database.Cursor;
import android.provider.MediaStore;

import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmutil.app.AppContext;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.MomoKit;
import com.immomo.momo.permission.PermissionUtil;
import com.immomo.momo.personalprofile.presentation.activity.PersonalProfileActivityK;
import com.immomo.momo.personalprofile.utils.ProfileNewUIManager;

import java.util.Calendar;

@LuaClass(isStatic = true)
public class UDProfileHelper {
    public static final String LUA_CLASS_NAME = "ProfileHelper";

    @LuaBridge
    public static String getNewUIExp() {
        return ProfileNewUIManager.getNewUIExp();
    }

    @LuaBridge
    public static void getLastYearPhoto(LVCallback callback) {
        Activity topActivity = MomoKit.getTopActivity();
        if (topActivity != null) {
            boolean hasPermission = PermissionUtil.getInstance().checkSelfPermission(topActivity, Manifest.permission.READ_EXTERNAL_STORAGE);
            if (hasPermission) {
                MomoTaskExecutor.executeLocalTask(topActivity.hashCode(), new MomoTaskExecutor.Task() {
                    @Override
                    protected Object executeTask(Object[] objects) throws Exception {
                        String lastYearPhotoPath = getLastYearPhotoPath(true, 1);
                        MomoMainThreadExecutor.post(() -> {
                            if (callback != null) {
                                if (lastYearPhotoPath == null) {
                                    callback.call("");
                                } else {
                                    callback.call(lastYearPhotoPath);
                                }
                            }
                        });
                        return null;
                    }
                });
            } else {
                callPhotoEmpty(callback);
            }
        } else {
            callPhotoEmpty(callback);
        }
    }

    @LuaBridge
    public static void getPhotoByLastDay(int lastDayTime, LVCallback callback) {
        Activity topActivity = MomoKit.getTopActivity();
        if (topActivity != null) {
            boolean hasPermission = PermissionUtil.getInstance().checkSelfPermission(topActivity, Manifest.permission.READ_EXTERNAL_STORAGE);
            if (hasPermission) {
                MomoTaskExecutor.executeLocalTask(topActivity.hashCode(), new MomoTaskExecutor.Task() {
                    @Override
                    protected Object executeTask(Object[] objects) throws Exception {
                        String lastYearPhotoPath = getLastYearPhotoPath(false, lastDayTime);
                        MomoMainThreadExecutor.post(() -> {
                            if (callback != null) {
                                if (lastYearPhotoPath == null) {
                                    callback.call("");
                                } else {
                                    callback.call(lastYearPhotoPath);
                                }
                            }
                        });
                        return null;
                    }
                });
            } else {
                callPhotoEmpty(callback);
            }
        } else {
            callPhotoEmpty(callback);
        }
    }

    public static String getLastYearPhotoPath(boolean isLastYear, int lastLimitTime) {
        Cursor cursor = null;
        try {
            Calendar cal = Calendar.getInstance();
            if (isLastYear) {
                cal.add(Calendar.YEAR, -lastLimitTime);
            } else {
                cal.add(Calendar.DATE, -lastLimitTime);
            }
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            long currentTime = cal.getTime().getTime() / 1000;
            long lastTime = currentTime + 24 * 60 * 60;
            cursor = AppContext.getContext().getContentResolver().query(MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                    new String[]{MediaStore.Images.Media._ID, MediaStore.Images.Media.DATA},
                    MediaStore.Images.Media.DATE_ADDED + " >= ? and " + MediaStore.Images.Media.DATE_ADDED + " < ?",
                    new String[]{currentTime + "", lastTime + ""},
                    MediaStore.Images.Media.DATE_ADDED + " DESC");
            if (cursor != null) {
                if (cursor.moveToFirst()) {
                    return cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return null;
    }

    private static void callPhotoEmpty(LVCallback callback) {
        if (callback != null) {
            callback.call("");
        }
    }

}
