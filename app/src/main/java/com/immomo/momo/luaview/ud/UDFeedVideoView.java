package com.immomo.momo.luaview.ud;

import android.net.Uri;
import android.text.TextUtils;
import android.view.ViewGroup;

import com.immomo.mls.fun.ud.view.UDViewGroup;
import com.immomo.momo.feed.player.ExoTextureLayout;
import com.immomo.momo.feed.player.IMediaPlayer;
import com.immomo.momo.luaview.LuaNearbyMediaView;
import com.immomo.momo.luaview.constants.PlayStatus;

import org.luaj.vm2.Globals;
import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaNumber;
import org.luaj.vm2.LuaString;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

/**
 * Created by <PERSON>.yahao 2019/3/30.
 * 9.0 附近的人视频续播业务实现
 */

@LuaApiUsed
public class UDFeedVideoView<V extends ExoTextureLayout> extends UDViewGroup<V> implements LuaNearbyMediaView.Callback, LuaNearbyMediaView.ProgressUpdateCallback {
    public static final String LUA_CLASS_NAME = "FeedVideoView";
    public static final String[] methods = {
            "src",
            "setFeedId",
            "setSource",
            "mute",
            "repeatCount",
            "offScreen",
            "totalDuration",
            "play",
            "stop",
            "pause",
            "setDidStartCallback",
            "setStartStallingCallback",
            "setEndStallingCallback",
            "setFinishCallback",
            "setFailCallback",
            "setProgressCallback",
            "setVideoSizeChangedCallback",
            "setWillRepeatCallback",
            "getPlayStatus",

    };


    private Uri uri;

    private boolean mAutoPlay;
    private int repeatCount;
    private boolean isMute;
    private long totalDuration;

    private long stopPoint = 0;
    private LuaFunction didStartFun;
    private LuaFunction startStallingFun;
    private LuaFunction endStallingFun;
    private LuaFunction finishFun;
    private LuaFunction failFun;
    private LuaFunction progressFun;
    private LuaFunction sizeChangedFun;
    private LuaFunction willRepeatFun;
    private int playStatus = PlayStatus.IDEL;
    private boolean hasStalling = false;
    private boolean isStop = true;

    private String feedid;
    private String eventId;
    private String type;
    private String from;
    @LuaApiUsed
    protected UDFeedVideoView(long L, LuaValue[] v) {
        super(L, v);
        if (v != null && v.length > 0 && v[0].isString()) {
            uri = Uri.parse(v[0].toJavaString());
        }
        initView();
    }

    public UDFeedVideoView(Globals g, V jud) {
        super(g, jud);
    }

    @Override
    protected V newView(LuaValue[] init) {
        return (V) new LuaNearbyMediaView(getContext());
    }
    @LuaApiUsed
    public LuaValue[] src(LuaValue[] vars) {
        String src = vars.length > 0 ? vars[0].toJavaString() : null;
        if (src == null) {
            return LuaValue.rString(uri.toString());
        }
        if (!TextUtils.isEmpty(src)) {
            uri = Uri.parse(src);
            if (getVideoView() != null) {
                getVideoView().setUri(uri);
                getVideoView().checkUrl(src);
            }
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setFeedId(LuaValue[] vars) {
        this.feedid = vars.length > 0 ? vars[0].toJavaString() : null;
        // FIXME chenwangwang 已经确认不用了，这个eventId如果要用的话，自己传递，不走缓存
        eventId = "";
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setSource(LuaValue[] vars) {
        this.type = vars.length > 0 ? vars[0].toJavaString() : null;
        this.from = vars.length > 1 ? vars[1].toJavaString() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] mute(LuaValue[] vars) {
        if (vars == null || vars.length == 0) {
            return LuaValue.rBoolean(this.isMute);
        }
        this.isMute = vars[0].toBoolean();
        if (getVideoView() != null) {
            getVideoView().setSilentMode(this.isMute);
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] repeatCount(LuaValue[] vars) {
        if (vars == null || vars.length == 0) {
            return LuaValue.rNumber(repeatCount);
        }
        repeatCount = vars[0].toInt();
        if (getVideoView() != null) {
//            getVideoView().setRepeatCount(repeatCount);
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] offScreen(LuaValue[] vars) {
        if (vars == null || vars.length == 0) {
            return LuaValue.rBoolean(this.mAutoPlay);
        }

        this.mAutoPlay = vars[0].toBoolean();
        if (getVideoView() != null) {
            getVideoView().setAutoPlayForbidden(!mAutoPlay);
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] totalDuration(LuaValue[] vars) {
        if (vars == null || vars.length == 0) {
            return LuaValue.rNumber(getVideoView().getDuration() * 1.0 / 1000);
        }
        totalDuration = vars[0].toInt();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] play(LuaValue[] vars) {
        startPlayVideo();
        isStop = false;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] stop(LuaValue[] vars) {
        stopPlayVideo();
        isStop = true;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] pause(LuaValue[] vars) {
        pausePlayVideo();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setDidStartCallback(LuaValue[] callback) {
        didStartFun = callback.length > 0 ? callback[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setStartStallingCallback(LuaValue[] callback) {
        startStallingFun = callback.length > 0 ? callback[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setEndStallingCallback(LuaValue[] callback) {
        endStallingFun = callback.length > 0 ? callback[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setFinishCallback(LuaValue[] callback) {
        finishFun = callback.length > 0 ? callback[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setFailCallback(LuaValue[] callback) {
        failFun = callback.length > 0 ? callback[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setProgressCallback(LuaValue[] callback) {
        progressFun = callback.length > 0 ? callback[0].toLuaFunction() : null;
        getVideoView().setProgressCallback(progressFun == null ? null : this);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setVideoSizeChangedCallback(LuaValue[] callback) {
        sizeChangedFun = callback.length > 0 ? callback[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setWillRepeatCallback(LuaValue[] callback) {
        willRepeatFun = callback.length > 0 ? callback[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] getPlayStatus(LuaValue[] values) {
        if (isStop) {
            return LuaValue.rNumber(PlayStatus.IDEL);
        }
        switch (playStatus) {
            case PlayStatus.READY:
            case PlayStatus.PREPARING:
                return LuaValue.rNumber(getVideoView().isPlaying() ? PlayStatus.PLAYING : PlayStatus.PAUSE);
        }
        return LuaValue.rNumber(playStatus);
    }

    protected void initView() {
        getVideoView().setCallback(this);
        ViewGroup.LayoutParams videoLayoutParams =
                new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT);
        getVideoView().setLayoutParams(videoLayoutParams);
        getVideoView().setSilentMode(isMute);
//        getVideoView().setRepeatCount(repeatCount);
        getVideoView().setAutoPlayForbidden(!mAutoPlay);

        if (uri != null)
            getVideoView().setUri(uri);
    }

    private void pausePlayVideo() {
        if (getVideoView() != null) {
            stopPoint = getVideoView().getCurrentPosition();
            getVideoView().pause();
        }
    }

    private void stopPlayVideo() {
        if (getVideoView() != null) {
            stopPoint = getVideoView().getCurrentPosition();
            getVideoView().stopPlayback();
        }
    }

    private void startPlayVideo() {
        if (getVideoView() != null) {
            if (totalDuration > 0 && stopPoint >= totalDuration)
                stopPoint = 0;
            if (stopPoint > 0) {
                getVideoView().seekTo(stopPoint);
                stopPoint = 0;
            }
            getVideoView().start(feedid, eventId, type, from);
        }
    }

    @Override
    public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {
        switch (playbackState) {
            case IMediaPlayer.STATE_ENDED:
                playStatus = PlayStatus.IDEL;
                if (finishFun != null) {
                    finishFun.invoke(LuaValue.rString(uri.toString()));
                }
                break;
            case IMediaPlayer.STATE_BUFFERING:
                playStatus = PlayStatus.PREPARING;
                if (startStallingFun != null) {
                    startStallingFun.invoke(LuaValue.rString(uri.toString()));
                }
                hasStalling = true;
                break;
            case IMediaPlayer.STATE_READY:
                if (hasStalling && endStallingFun != null) {
                    endStallingFun.invoke(LuaValue.rString(uri.toString()));
                    hasStalling = false;
                }
                playStatus = PlayStatus.READY;
                totalDuration = getVideoView() != null ? getVideoView().getDuration() : -1;
                break;
        }
    }

    @Override
    public void onError(int what, int extra) {
        playStatus = PlayStatus.ERROR;
        if (failFun != null) {
            failFun.invoke(LuaValue.rString(uri.toString()));
        }
    }

    @Override
    public void onStartRendering() {
        playStatus = PlayStatus.PLAYING;
        if (didStartFun != null) {
            didStartFun.invoke(varargsOf(LuaString.valueOf(uri.toString()), LuaNumber.valueOf(getVideoView().getDuration() * 1.0 / 1000)));
        }
    }

    @Override
    public void onVideoSizeChanged(int width, int height, int unappliedRotationDegrees, float pixelWidthHeightRatio) {
        if (sizeChangedFun != null) {
            sizeChangedFun.invoke(varargsOf(LuaString.valueOf(uri.toString()), LuaNumber.valueOf(width), LuaNumber.valueOf(height)));
        }
    }

    @Override
    public void onLoopStart() {
        if (willRepeatFun != null) {
            willRepeatFun.invoke(varargsOf(LuaString.valueOf(uri.toString()), LuaNumber.valueOf(getVideoView().getDuration() * 1.0 / 1000)));
        }
    }

    private LuaNearbyMediaView getVideoView() {
        return (LuaNearbyMediaView) getView();
    }

    @Override
    public void onProgressUpdate(long progress) {
        if (progressFun != null) {
            progressFun.invoke(varargsOf(LuaString.valueOf(uri.toString()), LuaNumber.valueOf(progress * 1.0 / 1000), LuaNumber.valueOf(getVideoView().getDuration() * 1.0 / 1000)));
        }
    }
}
