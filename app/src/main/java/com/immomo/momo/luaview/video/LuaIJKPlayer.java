package com.immomo.momo.luaview.video;

import android.net.Uri;

import com.immomo.momo.feed.player.TextureReusableIJKPlayer;

import tv.danmaku.ijk.media.momoplayer.IMediaPlayer;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 2018/3/8.
 */

public class LuaIJKPlayer extends TextureReusableIJKPlayer {
    @Override
    public void onCompletion(IMediaPlayer mp) {
        super.onCompletion(mp);
        if (isLoopPlay && loopListener != null) {
            loopListener.onLoopStart();
        }
    }

    private LoopListener loopListener;

    public void setLoopListener(LoopListener loopListener) {
        this.loopListener = loopListener;
    }

    public interface  LoopListener {
        void onLoopStart();
    }

    @Override
    public void release() {
        final Uri current = getCurrentUri();
        super.release();
        if (current != null)
            playPositionMap.remove(current);
    }
}
