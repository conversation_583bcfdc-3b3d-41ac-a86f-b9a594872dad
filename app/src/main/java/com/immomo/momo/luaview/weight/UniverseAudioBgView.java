package com.immomo.momo.luaview.weight;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.cosmos.mdlog.MDLog;
import com.immomo.android.router.momo.util.VideoConflictRouter;
import com.immomo.framework.utils.UIUtils;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.mmutil.task.ThreadUtils;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.R;
import com.immomo.momo.agora.mr.conflictHelper.VideoConflictConfig;
import com.immomo.momo.audio.IAudioPlayer;
import com.immomo.momo.globalevent.GlobalEventManager;
import com.immomo.momo.universe.phonograph.PhonographManager;
import com.immomo.momo.util.StringUtils;
import com.immomo.svgaplayer.SVGAAnimListenerAdapter;
import com.immomo.svgaplayer.view.MomoSVGAImageView;

import java.io.File;

import info.xudshen.android.appasm.AppAsm;

/**
 * author: hongming.wei
 * data: 2023/12/15
 */
public class UniverseAudioBgView extends RelativeLayout {

    private static final String TAG = "UniverseAudioBgView";
    private RelativeLayout mRlVoice;
    private ImageView mIvVoice;
    private TextView mTvDuration;
    private MomoSVGAImageView mSivVoicePlay;
    private ImageView mIvDel;

    public String mFilePath;

    private final String PLAYING_SVGA =
            "https://s.momocdn.com/s1/u/iiefhaija/UniverseCreateContex_light.svga";
    private IAudioPlayer mAudioPlayer = null;
    private IAudioPlayer.OnStateChangedListener mOnPlayStateChangedListener = null;
    private boolean isPlayingFinish = true; //音频播放是否结束
    private boolean isPlayingPause = false; //音频播放是否被暂停

    public UniverseAudioBgView(Context context) {
        super(context);
        initView(context);
    }

    public UniverseAudioBgView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    public UniverseAudioBgView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }
    
    
    private void initView(Context context){
        LayoutInflater.from(context).inflate(R.layout.item_voice, this, true);
        mRlVoice = findViewById(R.id.rl_voice);
        mIvVoice = findViewById(R.id.iv_voice);
        mTvDuration = findViewById(R.id.tv_duration);
        mSivVoicePlay = findViewById(R.id.siv_voice_play);
        mIvDel = findViewById(R.id.iv_del);
        setVisibility(View.GONE);
        initListener();
    }



    public void bindData(String filePath, long duration) {
        mFilePath = filePath;
        int second = Math.round(duration / 1000f);
        if (mTvDuration != null) {
            mTvDuration.setText(String.valueOf(second));
        }
        if (mRlVoice != null) {
            mRlVoice.setBackgroundResource(R.drawable.bg_7dp_round_corner_grey);
        }
        if (mIvVoice != null) {
            mIvVoice.setImageResource(R.drawable.ic_uni_publish_audio);
        }

        if (mTvDuration != null) {
            mTvDuration.setTextColor(UIUtils.getColor(R.color.color_323333_to_80f));
        }
    }

    private void initListener() {
        mRlVoice.setOnClickListener(v -> {
            if (PhonographManager.Companion.getInstance() != null
                    && PhonographManager.Companion.getInstance().isPhonographShowing()) {
                PhonographManager.Companion.getInstance().quitAudio();
            }
            if (StringUtils.isNotEmpty(mFilePath)) {
                dealWithVoiceClick(mFilePath);
            }
        });
        mIvDel.setOnClickListener(v -> {
            removeVoice();
        });
    }


    private void dealWithVoiceClick(String path) {
        //播放或暂停音频
        if (isPlayingFinish) {
            //播放
            play(path);
        } else {
            if (isPlaying()) {
                if (isPlayingPause) {
                    if (mAudioPlayer != null) {
                        mAudioPlayer.resume();
                    }
                    playAnim();
                    isPlayingPause = false;
                } else {
                    if (mAudioPlayer != null) {
                        mAudioPlayer.pause();
                    }
                    isPlayingFinish = false;
                    stopPlayAnim();
                    isPlayingPause = true;
                }
            }
        }
    }

    private boolean isPlaying() {
        if (mAudioPlayer != null) {
            return mAudioPlayer.isPlaying();
        } else {
            return false;
        }
    }

    private void play(String voicePath) {
        if (TextUtils.isEmpty(voicePath)) {
            Toaster.show(UIUtils.getString(R.string.get_voice_error));
            return;
        }
        try {
            File audioFile = new File(voicePath);
            if (audioFile.exists() && audioFile.length() > 0) {
                playAudio(audioFile, "opus");
                playAnim();
            } else {
                Toaster.show(UIUtils.getString(R.string.get_voice_error));
            }
        } catch (Exception e) {
            MDLog.printErrStackTrace(TAG, e);
        }
    }

    private void playAudio(File audioFile, String audioExtension) {
        if (AppAsm.getRouter(VideoConflictRouter.class)
                .conflictWith(VideoConflictConfig.BusinessType.COMMON)
        ) {
            return;
        }
        if (mAudioPlayer != null && mAudioPlayer.isPlaying()) {
            mAudioPlayer.stop();
        }

        mAudioPlayer = IAudioPlayer.create("opus" == audioExtension, null);
        mAudioPlayer.setAudioFile(audioFile);
        if (mOnPlayStateChangedListener == null) {
            mOnPlayStateChangedListener = new OnPlayStateChangedListener();
        }
        mAudioPlayer.setOnStateChangedListener(mOnPlayStateChangedListener);
        mAudioPlayer.start();
    }


    public void playAnim(){
        if (mSivVoicePlay != null) {
            mSivVoicePlay.startSVGAAnimWithListener(
                    PLAYING_SVGA,
                    0,
                    new SVGAAnimListenerAdapter() {
                        @Override
                        public void onStart() {
                            super.onStart();
                            mSivVoicePlay.setVisibility(View.VISIBLE);
                            if (mIvVoice != null) {
                                mIvVoice.setVisibility(View.INVISIBLE);
                            }
                        }

                        @Override
                        public void onFinished() {
                            super.onFinished();
                            mSivVoicePlay.setVisibility(View.INVISIBLE);
                            if (mIvVoice != null) {
                                mIvVoice.setVisibility(View.VISIBLE);
                            }
                        }
                    }
            );
        }
    }


    public void showBgView() {
        setVisibility(View.VISIBLE);
    }

    public void destroy(){
        stopPlay();
        if (mAudioPlayer != null) {
            mAudioPlayer.setOnStateChangedListener(null);
            mAudioPlayer = null;
        }
        mOnPlayStateChangedListener = null;
        stopPlayAnim();
    }


    private class OnPlayStateChangedListener implements IAudioPlayer.OnStateChangedListener {
        @Override
        public void onStart() {
            isPlayingFinish = false;
            isPlayingPause = false;
        }

        @Override
        public void onStop() {
            isPlayingFinish = true;
            stopPlayAnim();
        }

        @Override
        public void onFinish() {
            isPlayingFinish = true;
            stopPlayAnim();
        }

        @Override
        public void onComplete() {
            isPlayingFinish = true;
            stopPlayAnim();
        }

        @Override
        public void onError(int errCode) {
            isPlayingFinish = true;
            stopPlayAnim();
        }
    }


    private void stopPlayAnim() {
        MomoMainThreadExecutor.post(() -> {
            if (mSivVoicePlay != null && mSivVoicePlay.isAnimating()) {
                mSivVoicePlay.stopAnimCompletely();
            }
        });
    }

    private void stopPlay() {
        if (mAudioPlayer != null && isPlaying()) {
            mAudioPlayer.stop();
        }
    }


    private void removeVoice() {
        if (mAudioPlayer != null && mAudioPlayer.isPlaying()) {
            mAudioPlayer.stop();
        }

        clearFiles();
    }

    private void clearFiles() {
        //删除音频文件
        if (!TextUtils.isEmpty(mFilePath)) {
            ThreadUtils.execute(ThreadUtils.TYPE_RIGHT_NOW, () -> {
                try {
                    File file = new File(mFilePath);
                    if (file.exists()) {
                        file.delete();
                    }
                } catch (Exception e) {
                    MDLog.printErrStackTrace(TAG, e);
                }
            });
        }
        mFilePath = "";
        GlobalEventManager.getInstance().sendEvent(
                new GlobalEventManager.Event("EVENT_UNIVERSE_DELETE_VOICE_VIEW")
                        .src(GlobalEventManager.EVN_NATIVE)
                        .dst(GlobalEventManager.EVN_LUA));
    }

}
