package com.immomo.momo.luaview.lt;


import android.text.TextUtils;

import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.momo.group.apt.GroupConfigV1Getter;

@LuaClass(isStatic = true)
public class LTGroupSearchABHelper {
    public static final String LUA_CLASS_NAME = "GroupSearchABHelper";

    @LuaBridge
    public static String searchPlaceHolder() {
        if (TextUtils.isEmpty(GroupConfigV1Getter.get().groupSearchTips())) {
            return "搜索群号/群名称";
        } else {
            return GroupConfigV1Getter.get().groupSearchTips();
        }
    }

}
