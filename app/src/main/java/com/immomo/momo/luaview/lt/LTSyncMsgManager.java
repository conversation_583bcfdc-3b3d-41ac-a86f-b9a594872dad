package com.immomo.momo.luaview.lt;

import android.content.Context;
import android.content.Intent;

import com.immomo.android.router.momo.EventKeys;
import com.immomo.mls.MLSEngine;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.LVCallback;
import com.immomo.momo.android.broadcast.ReflushSessionUnreadReceiver;
import com.immomo.momo.eventbus.DataEvent;

import java.util.HashMap;
import java.util.Map;

import de.greenrobot.event.EventBus;


/**
 * Created by li.mengnan
 * on 2020/10/16
 * Lua桥接帮助类，用于消息同步，目前已处理的业务同步如下：
 * 1.互动通知消息已读
 */
@LuaClass(isStatic = true)
public class LTSyncMsgManager {
    //lua 桥接名称
    public static final String LUA_CLASS_NAME = "SyncMsgManager";

    //互动通知业务类型
    private static final int TYPE_INTERACTION_NOTICE = 1;
    //小宇宙流星消息同步
    private static final int TYPE_MICROCOSM_METEOR_NOTICE = 2;
    //小宇宙互动消息同步
    private static final int TYPE_MICROCOSM_INTERACT_NOTICE = 3;

    private static final int TYPE_MICROCOSM_VOTE_NOTICE = 4;

    private static Context getContext() {
        return MLSEngine.getContext();
    }

    /**
     * 通知端侧，消息已读，清除提醒
     *
     * @param businessType 业务类型
     * @param callback     处理lua层回调
     */
    @LuaBridge
    public static void readAll(int businessType, LVCallback callback) {
        switch (businessType) {
            case TYPE_INTERACTION_NOTICE:
                dealInteractionMsgAllRead();
                break;
            case TYPE_MICROCOSM_METEOR_NOTICE:
                dealMicrocosmMeteorMsgAllRead();
                break;
            case TYPE_MICROCOSM_INTERACT_NOTICE:
                dealMicrocosmInteractMsgAllRead();
                break;
            case TYPE_MICROCOSM_VOTE_NOTICE:
                dealMicrocosmVoteMsgAllRead();
                break;
            default:
                break;
        }
    }

    /**
     * 处理互动通知的消息已读
     */
    private static void dealInteractionMsgAllRead() {
        /*
         * step 1    清空互动通知数据库里未读状态
         * step 2    处理消息帧好友入口红点或者气泡
         * */
        Intent intent = new Intent(ReflushSessionUnreadReceiver.ReflushNotice);
        getContext().sendBroadcast(intent);
    }

    /**
     * 小宇宙流星类消息已读
     */
    private static void dealMicrocosmMeteorMsgAllRead() {
        Map<String, Object> params = new HashMap();
        params.put("count", 0);
        EventBus.getDefault().post(new DataEvent<Map<String, Object>>(EventKeys.Microcosm.METEOR_MSG, params));
    }

    /**
     * 小宇宙互动类消息已读
     */
    private static void dealMicrocosmInteractMsgAllRead() {
        Map<String, Object> params = new HashMap();
        params.put("count", 0);
        EventBus.getDefault().post(new DataEvent<Map<String, Object>>(EventKeys.Microcosm.INTERACT_MSG, params));
    }

    private static void dealMicrocosmVoteMsgAllRead() {
        Map<String, Object> params = new HashMap<>();
        params.put("count", 0);
        EventBus.getDefault().post(new DataEvent<Map<String, Object>>(EventKeys.Microcosm.VOTE_MSG, params));
    }

}
