package com.immomo.momo.luaview.ud;

import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;

import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.fun.ud.view.UDViewGroup;
import com.immomo.mls.utils.LVCallback;
import com.immomo.momo.home.busines.BaseLuaBusiFragment;

import org.luaj.vm2.Globals;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;

@LuaClass
public class MomoHomeHelper {
    public static final String LUA_CLASS_NAME = "__MomoHomeHelper";
    public static final String LUA_SINGLE_NAME = "MomoHomeHelper";

    public LVCallback singleClickTabCallBack;
    public LVCallback clearUnreadCallBack;
    public LVCallback openPageCallBack;

    private WeakReference<BaseLuaBusiFragment> iView;

    private int count;
    private UDViewGroup rightMenusView;

    private boolean isRedBadgeShow;

    /**
     * 跳转携带的数据
     */
    private Map<String, String> openParams = new HashMap<>();

    public void setView(BaseLuaBusiFragment view) {
        this.iView = new WeakReference(view);
    }

    @LuaBridge
    public void setSingleClickTabBarItem(LVCallback callback) {
        singleClickTabCallBack = callback;
    }

    @LuaBridge
    public void setNeedDestroyAllBadge(LVCallback callback) {
        clearUnreadCallBack = callback;
    }

    @LuaBridge
    public void setOpenWithParam(LVCallback callback) {
        openPageCallBack = callback;
        if (this.openParams != null && this.openParams.size() > 0 && openPageCallBack != null) {
            openPageCallBack.call(this.openParams);
        }
    }

    /**
     * 设置未读数
     *
     * @param count
     */
    @LuaBridge
    public void setBadgeCount(int count) {
        this.count = count;
        if (iView != null && iView.get() != null) {
            iView.get().setBadgeCount(count);
        }
    }

    @LuaBridge
    public int getBadgeCount() {
        return this.count;
    }

    @LuaBridge
    public void showBadgeRedDot(boolean isRedBadgeShow) {
        this.isRedBadgeShow = isRedBadgeShow;
        if (iView != null && iView.get() != null) {
            iView.get().setBadgeRedDot(isRedBadgeShow);
        }
    }

    @LuaBridge
    public boolean getBadgeRedDot() {
        return this.isRedBadgeShow;
    }

    public void setRightMenusView(@NonNull ViewGroup rightView, @NonNull Globals globals) {
        if (rightMenusView == null) {
            rightMenusView = new UDViewGroup(globals);
        }
        View view = rightMenusView.getView();
        if (view != null) {
            FrameLayout.LayoutParams params =
                    new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.MATCH_PARENT);
            params.gravity = Gravity.END;
            view.setLayoutParams(params);
            rightView.removeAllViews();
            rightView.addView(view);
        }
    }

    @LuaBridge
    public UDViewGroup rightNavigationView() {
        return rightMenusView;
    }

    @LuaBridge
    public void setRightNavigationViewWidth(int width) {
        if (iView != null && iView.get() != null) {
            iView.get().setRightNavigationViewWidth(width);
        }

    }

    @LuaBridge
    public int topBarHeight() {
        int top = 0;
        if (iView != null && iView.get() != null) {
            top = iView.get().getTopBarHeight();
        }
        return top;
    }

    @LuaBridge
    public int tabBarHeight() {
        return 0;
    }

    public void setOpenParams(Map<String, String> tableData) {
        if (tableData != null) {
            openParams.clear();
            openParams.putAll(tableData);
        }
    }
}

