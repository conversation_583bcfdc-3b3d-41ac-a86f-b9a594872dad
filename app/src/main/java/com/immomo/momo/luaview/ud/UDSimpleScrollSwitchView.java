package com.immomo.momo.luaview.ud;

import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;

import com.immomo.framework.utils.UIUtils;
import com.immomo.mls.fun.ud.view.UDView;
import com.immomo.mls.fun.ud.view.UDViewGroup;
import com.immomo.mls.fun.ui.LuaRecyclerView;
import com.immomo.momo.feed.ui.view.BaseScrollSwitchView;
import com.immomo.momo.feed.ui.view.LuaScrollSwitchView;

import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

@LuaApiUsed
public class UDSimpleScrollSwitchView<V extends LuaScrollSwitchView> extends UDViewGroup<V> {

    public static final String LUA_CLASS_NAME = "UDSimpleScrollSwitchView";

    public static final String[] methods = {
            "setTopHeight",
            "setMiddleHeight",
            "setBottomHeight",
            "setUserPaddingScroll",
            "setScrollView",
            "setOpenStatusAllowClick",
            "setSubScrollScrollEnableWhenTop",
            "setBackgroundAlpha",
            "setBackgroundColor",
            "setContentView",
            "setInterceptMiddleScroll",
            "changeToFull",
            "setDefaultStatus"
    };

    private LuaScrollSwitchView switchView;

    @LuaApiUsed
    protected UDSimpleScrollSwitchView(long L, LuaValue[] v) {
        super(L, v);
    }

    @NonNull
    @Override
    protected V newView(@NonNull LuaValue[] init) {
        boolean showShadowView = true;
        if (init.length > 1) {
            showShadowView = init[1].toBoolean();
        }
        switchView = new LuaScrollSwitchView(getContext());
        switchView.addBgView(showShadowView);
        return (V) switchView;
    }


    @LuaApiUsed
    public LuaValue[] setTopHeight(LuaValue[] fun) {
        int topHeight = fun[0].toInt();
        switchView.setTopHeight(UIUtils.getPixels(topHeight));
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setMiddleHeight(LuaValue[] fun) {
        int middleHeight = fun[0].toInt();
        switchView.setMiddleHeight(UIUtils.getPixels(middleHeight));
        return null;

    }

    @LuaApiUsed
    public LuaValue[] setBottomHeight(LuaValue[] fun) {
        int bottomHeight = fun[0].toInt();
        switchView.setBottomHeight(UIUtils.getPixels(bottomHeight));
        return null;

    }

    @LuaApiUsed
    public LuaValue[] setUserPaddingScroll(LuaValue[] fun) {
        boolean userPaddingScroll = fun[0].toBoolean();
        switchView.setUserPaddingScroll(userPaddingScroll);
        return null;

    }

    @LuaApiUsed
    public LuaValue[] setScrollView(LuaValue[] fun) {
        UDView udView = (UDView) fun[0];
        View view = udView.getView();
        if (view instanceof LuaRecyclerView) {
            switchView.setScrollView(((LuaRecyclerView<?, ?>) view).getRecyclerView());
        }
        return null;

    }

    @Override
    public LuaValue[] addView(LuaValue[] var) {
        LuaValue[] values = super.addView(var);
        View childAt = switchView.getChildAt(1);
        FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) childAt.getLayoutParams();
        params.setMargins(0, UIUtils.getPixels(22f) + switchView.getTopHeight(), 0, 0);
        childAt.setLayoutParams(params);
        return values;
    }

    @LuaApiUsed
    public LuaValue[] setOpenStatusAllowClick(LuaValue[] fun) {
        boolean openStatusAllowClick = fun[0].toBoolean();
        switchView.setOpenStatusAllowClick(openStatusAllowClick);
        return null;

    }

    @LuaApiUsed
    public LuaValue[] setInterceptMiddleScroll(LuaValue[] fun) {
        boolean interceptMiddleScroll = fun[0].toBoolean();
        switchView.setInterceptMiddleScroll(interceptMiddleScroll);
        return null;

    }

    @LuaApiUsed
    public LuaValue[] setDefaultStatus(LuaValue[] fun) {
        int status = fun[0].toInt();
        BaseScrollSwitchView.Status localStatus = BaseScrollSwitchView.Status.NONE;
        switch (status) {
            case 0:
                localStatus = BaseScrollSwitchView.Status.NONE;
                break;
            case 1:
                localStatus = BaseScrollSwitchView.Status.CLOSE;
                break;
            case 2:
                localStatus = BaseScrollSwitchView.Status.MIDDLE;
                break;
            case 3:
                localStatus = BaseScrollSwitchView.Status.OPEN;
                break;
        }
        switchView.setDefaultStatus(localStatus, true);
        return null;
    }

    /**
     * ios需要 android空实现
     *
     * @param fun
     * @return
     */
    @LuaApiUsed
    public LuaValue[] setSubScrollScrollEnableWhenTop(LuaValue[] fun) {
        return null;
    }


    /**
     * ios需要 android空实现
     *
     * @param fun
     * @return
     */
    @LuaApiUsed
    public LuaValue[] setContentView(LuaValue[] fun) {
        return null;
    }

    @LuaApiUsed
    public LuaValue[] changeToFull(LuaValue[] fun) {
        LuaFunction function = fun[0].toLuaFunction();
        if (switchView != null) {
            switchView.setOnViewListener(new BaseScrollSwitchView.OnScrollSwitchViewListener() {
                @Override
                public void onViewScrolled(float openOffset) {

                }

                @Override
                public void onSwitchChanged(BaseScrollSwitchView.REASON reason, BaseScrollSwitchView.Status status) {
                    if (function != null) {
                        function.fastInvoke(status == BaseScrollSwitchView.Status.OPEN ? 1 : 0);
                    }
                }
            });
        }

        return null;
    }

    public LuaValue[] setBackgroundAlpha(LuaValue[] fun) {
        float alpha = fun[0].toFloat();
        switchView.setAlpha(alpha);
        return null;

    }

    public LuaValue[] setBackgroundColor(LuaValue[] fun) {
        String alpha = fun[0].toString();
        switchView.setARGB(alpha);
        return null;

    }


}
