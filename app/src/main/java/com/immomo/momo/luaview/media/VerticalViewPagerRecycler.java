package com.immomo.momo.luaview.media;

import com.immomo.mls.fun.ud.view.viewpager.ViewPagerContent;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by XiongFangyu on 2018/9/27.
 */
final class VerticalViewPagerRecycler {

    private final Map<String, ViewPagerContent> pools;

    VerticalViewPagerRecycler() {
        pools = new HashMap<>();
    }

    ViewPagerContent getViewFromPoolByReuseId(String id) {
        return pools.remove(id);
    }

    void saveViewToPoolByReuseId(String id, ViewPagerContent v) {
        pools.put(id, v);
    }

    void release() {
        pools.clear();
    }
}
