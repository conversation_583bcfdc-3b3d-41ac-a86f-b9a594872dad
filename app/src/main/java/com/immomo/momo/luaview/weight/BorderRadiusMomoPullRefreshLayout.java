//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.immomo.momo.luaview.weight;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.NonNull;

import com.immomo.framework.view.pulltorefresh.MomoPullRefreshLayout;
import com.immomo.mls.fun.other.Size;
import com.immomo.mls.fun.ud.view.IBorderRadiusView;
import com.immomo.mls.fun.ud.view.IClipRadius;
import com.immomo.mls.fun.weight.BorderBackgroundDrawable;
import com.immomo.mls.util.LuaViewUtil;
import com.immomo.mls.utils.ViewClipHelper;
import com.immomo.mls.utils.ViewClipHelper.SuperDrawAction;
import com.immomo.mls.utils.ViewShadowHelper;

public class BorderRadiusMomoPullRefreshLayout extends MomoPullRefreshLayout implements IBorderRadiusView, IClipRadius, SuperDrawAction {
    @NonNull
    private final BorderBackgroundDrawable backgroundDrawable = new BorderBackgroundDrawable();
    @NonNull
    private final ViewClipHelper viewClipHelper = new ViewClipHelper();
    private final @NonNull
    ViewShadowHelper viewShadowHelper = new ViewShadowHelper();

    public BorderRadiusMomoPullRefreshLayout(Context context) {
        super(context);
    }

    public BorderRadiusMomoPullRefreshLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public BorderRadiusMomoPullRefreshLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setBgColor(int color) {
        this.backgroundDrawable.setBgColor(color);
        LuaViewUtil.setBackground(this, this.backgroundDrawable);
    }

    @Override
    public void setViewDarkAllowed(boolean allowed) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            setForceDarkAllowed(allowed);
        }
    }

    @Override
    public void setBgDrawable(Drawable drawable) {

    }

    public void setDrawRadiusBackground(boolean draw) {
        this.viewClipHelper.setDrawRadiusBackground(draw);
        this.backgroundDrawable.setDrawRadiusBackground(draw);
    }

    public int getBgColor() {
        return this.backgroundDrawable.getBgColor();
    }

    public void setGradientColor(int start, int end, int type) {
        this.backgroundDrawable.setGradientColor(start, end, type);
        LuaViewUtil.setBackground(this, this.backgroundDrawable);
    }

    public void setRadiusColor(int color) {
        this.viewClipHelper.setRadiusColor(color);
    }

    @Override
    public void setAddShadow(int color, Size offset, float shadowRadius, float alpha) {
        if (Build.VERSION.SDK_INT >= 21) {
            // 这个是加外边框，通过 setRoundRect 添加
            viewShadowHelper.setShadowData(color, offset, shadowRadius, alpha);
            viewShadowHelper.setOutlineProvider(this);
        }
    }

    public void setStrokeWidth(float width) {
        this.backgroundDrawable.setStrokeWidth(width);
        LuaViewUtil.setBackground(this, this.backgroundDrawable);
    }

    public void setStrokeColor(int color) {
        this.backgroundDrawable.setStrokeColor(color);
        LuaViewUtil.setBackground(this, this.backgroundDrawable);
    }

    public void setCornerRadius(float radius) {
        this.backgroundDrawable.setCornerRadius(radius);
        LuaViewUtil.setBackground(this, this.backgroundDrawable);
        this.viewClipHelper.setRadius(radius);
        viewShadowHelper.setRadius(radius);
        viewShadowHelper.setError(false);
        viewClipHelper.setCornerType(TYPE_CORNER_RADIUS);
    }

    public void setRadius(float topLeft, float topRight, float bottomLeft, float bottomRight) {
        this.backgroundDrawable.setRadius(topLeft, topRight, bottomLeft, bottomRight);
        LuaViewUtil.setBackground(this, this.backgroundDrawable);
        this.viewClipHelper.setRadius(topLeft, topRight, bottomLeft, bottomRight);
        viewClipHelper.setCornerType(TYPE_CORNER_RADIUS);
    }

    public void setRadius(int direction, float radius) {
        this.backgroundDrawable.setRadius(direction, radius);
        LuaViewUtil.setBackground(this, this.backgroundDrawable);
        this.viewClipHelper.setRadius(this.backgroundDrawable);
        viewClipHelper.setCornerType(TYPE_CORNER_DIRECTION);
        viewShadowHelper.setError(true);//阴影禁止和setCornerRadiusWithDirection()连用
    }

    @Override
    public void setMaskRadius(int direction, float radius) {
        backgroundDrawable.setMaskRadius(direction, radius);
        LuaViewUtil.setBackground(this, backgroundDrawable);
        viewClipHelper.setRadius(backgroundDrawable);
        viewShadowHelper.setError(false);//阴影可以和addCornerMask()连用
    }

    @Override
    public void initCornerManager(boolean open) {
        viewClipHelper.openDefaultClip(open);
    }

    @Override
    public void forceClipLevel(int clipLevel) {
        viewClipHelper.setForceClipLevel(clipLevel);
    }

    public float getStrokeWidth() {
        return (float) ((int) this.backgroundDrawable.getStrokeWidth());
    }

    public int getStrokeColor() {
        return this.backgroundDrawable.getStrokeColor();
    }

    @Override
    public float getCornerRadiusWithDirection(int direction) {
        return backgroundDrawable.getCornerRadiusWithDirection(direction);
    }

    public float getRadius(int direction) {
        return this.backgroundDrawable.getRadius(direction);
    }

    public float[] getRadii() {
        return this.backgroundDrawable.getRadii();
    }

    public void drawBorder(Canvas canvas) {
        this.backgroundDrawable.drawBorder(canvas);
    }

    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        this.viewClipHelper.updatePath(w, h, this.backgroundDrawable.getStrokeWidth());
    }

    public void draw(Canvas canvas) {
        if (this.viewClipHelper.needClicp()) {
            this.viewClipHelper.clip(canvas, this);
        } else {
            super.draw(canvas);
        }
        drawBorder(canvas);
    }

    public void innerDraw(Canvas canvas) {
        super.draw(canvas);
    }

    public boolean onTouchEvent(MotionEvent event) {
        if (isEnabled())
            this.backgroundDrawable.onRippleTouchEvent(event);
        return super.onTouchEvent(event);
    }

    public void setDrawRipple(boolean drawRipple) {
        if (drawRipple) {
            this.setClickable(true);
        }

        this.backgroundDrawable.setDrawRipple(drawRipple);
        LuaViewUtil.setBackground(this, this.backgroundDrawable);
    }
}
