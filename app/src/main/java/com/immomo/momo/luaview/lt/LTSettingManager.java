package com.immomo.momo.luaview.lt;

import android.app.Activity;
import android.app.Dialog;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;

import com.cosmos.runtime.MomoRuntime;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.framework.task.BaseDialogTask;
import com.immomo.framework.utils.mfrpermission.Manufacturer;
import com.immomo.framework.utils.mfrpermission.MfrPermission;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.BuildConfig;
import com.immomo.momo.MomoKit;
import com.immomo.momo.android.synctask.CheckNewVersionTask;
import com.immomo.momo.eventbus.SimpleEvent;
import com.immomo.momo.knock.task.NearbyAudioEnterTask;
import com.immomo.momo.maintab.sessionlist.SessionListFragment;
import com.immomo.momo.protocol.imjson.receiver.MessageKeys;
import com.immomo.momo.protocol.imjson.util.Debugger;
import com.immomo.momo.service.bean.Session;
import com.immomo.momo.setting.tools.CleanCacheUtil;
import com.immomo.momo.util.market.MarketUtils;
import com.meituan.robust.manager.RobustManager;

import java.util.Objects;

import de.greenrobot.event.EventBus;

/**
 * -----------------------------------------------------------------
 * Copyright (C)  sun
 * Create: 2022/11/23 2:57 下午
 * 设置页桥接
 * -----------------------------------------------------------------
 */
@LuaClass(isStatic = true)
public class LTSettingManager {
    public static final String LUA_CLASS_NAME = "SettingManager";

    @LuaBridge
    public static void clearCache() {
        Activity activity = MomoKit.getTopActivity();
        if (activity == null) return;
        Dialog alertDialog = CleanCacheUtil.createCleanDialog(activity, () -> {
            MomoTaskExecutor.executeUserTask(LUA_CLASS_NAME, new ClearCacheTask(activity, ""));
            return null;
        });
        alertDialog.show();
    }

    /**
     * 检查更新
     */
    @LuaBridge
    public static void checkNewVersion() {
        Activity activity = MomoKit.getTopActivity();
        if (activity == null) return;
        new CheckNewVersionTask(activity, true).execute();
    }

    @LuaBridge
    public static void sendNearbyAudioEnterEvent() {
        EventBus.getDefault().post(new SimpleEvent(NearbyAudioEnterTask.ACTION_UPDATE_FLOAT));
    }

    /**
     * 给陌陌评分
     */
    @LuaBridge
    public static void markMomo() {
        Activity activity = MomoKit.getTopActivity();
        if (activity == null) return;
        String str = "market://details?id=" + activity.getPackageName();
        try {
            Intent mark = new Intent(Intent.ACTION_VIEW);
            mark.setData(Uri.parse(str));
            activity.startActivity(mark);
        } catch (Exception e) {
            Toaster.show("找不到应用市场，无法对陌陌评分");
        }
    }

    @LuaBridge
    public static void showDebugToast() {
        boolean isTestEnvOpen = KV.getSysInt(SPKeys.User.Test.KEY_TEST_ENV_SWITCH, 0) == 1;
        Toaster.show("debug:" + Debugger.isDebuggable() + ";\n"
            + "inner:" + MomoKit.getVersionCode() + ";\n"
            + "outer:" + BuildConfig.VERSION_CODE + ";\n"
            + "channel:" + MarketUtils.getMarketSource() + ";\n"
            + "is64bit:" + MomoRuntime.is64Bit() + ";\n"
            + "test open:" + isTestEnvOpen+ ";\n"
            + "robustPatchVer:" + RobustManager.INSTANCE.getPatchVersion() + ";\n"
            + "uniquesign:" + BuildConfig.UNIQUE_SIGN
        );
    }

    /**
     * 是否可以查询到通知状态
     * @return
     */
    @LuaBridge
    public static boolean notificationCheckable() {
        Activity activity = MomoKit.getTopActivity();
        if (activity == null) return false;
        return MfrPermission.Notification.isCheckable(activity);
    }

    /**
     * 是否打开到通知提醒
     * @return
     */
    @LuaBridge
    public static boolean notificationCheck() {
        Activity activity = MomoKit.getTopActivity();
        if (activity == null) return false;
        return MfrPermission.Notification.check(activity);
    }

    /**
     * 是否是可以跳转到系统设置的机型
     * @return
     */
    @LuaBridge
    public static boolean isSupportGoSetting() {
        for (Manufacturer manufacturer : Manufacturer.values()) {
            if (manufacturer.isCurrentMfr()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 跳转系统设置
     */
    @LuaBridge
    public static void gotoSystemSetting() {
        MfrPermission.Notification.gotoSetting(MomoKit.getContext());
    }

    /**
     * 修改陌生人招呼通知
     */
    @LuaBridge
    public static void changeSayHiNotice() {
        Bundle bundle = new Bundle();
        bundle.putString(SessionListFragment.Key_SessionId, Session.ID.SayhiSession);
        MomoKit.getApp().dispatchMessage(bundle, SessionListFragment.Action_SessionChanged);
    }

    /**
     * 修改群组通知
     */
    @LuaBridge
    public static void changeGroupNotice() {
        MomoKit.getApp().dispatchMessage(new Bundle(), MessageKeys.Action_GroupActionStatusChanged);
    }

    @LuaBridge
    public static void onDestroy() {
        MomoTaskExecutor.cancleAllTasksByTag(LUA_CLASS_NAME);
    }


    @LuaBridge
    public static boolean isDebug() {
        return BuildConfig.DEBUG;
    }

    @LuaBridge
    public static boolean isDebuggable() {
        return Debugger.isDebuggable();
    }

    @LuaBridge
    public static String getAppVersionName() {
        try {
            Activity activity = MomoKit.getTopActivity();
            PackageInfo packageInfo = activity.getApplicationContext()
                .getPackageManager()
                .getPackageInfo(activity.getPackageName(), 0);
            return packageInfo.versionName;
        } catch (PackageManager.NameNotFoundException e) {
        }
        return "";
    }

    private static class ClearCacheTask extends BaseDialogTask<Object, Object, Objects> {

        public ClearCacheTask(Activity context, String traceId) {
            super(context);
        }

        @Override
        protected String getDispalyMessage() {
            return "请稍候...";
        }

        @Override
        protected Objects executeTask(Object... params) throws Exception {
            CleanCacheUtil.exeClean();
            return null;
        }

        @Override
        protected void onTaskFinish() {
            super.onTaskFinish();
            Toaster.show("缓存清理完成");
        }

    }
}
