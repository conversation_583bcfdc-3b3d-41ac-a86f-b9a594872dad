package com.immomo.momo.luaview.lt;

import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.MomoKit;
import com.immomo.momo.plugin.qq.QQApi;
import com.tencent.tauth.IUiListener;
import com.tencent.tauth.UiError;

@LuaClass(isStatic = true)
public class LTQQContactManager {
    public static final String LUA_CLASS_NAME = "AddQQContactManager";
    public static final int SORT_TYPE_ADD_TIME = 2; // 添加时间排序

    private LTQQContactManager() {
    }


    @LuaBridge
    public static void executeGetQQContactQZone(String title, String imgUrl, String desc, String gotoUrl) {
        QQApi.getInstance().shareToQQZoneWithUrlImg(title, imgUrl, desc, gotoUrl, MomoKit.getTopActivity(), new IUiListener() {

            @Override
            public void onError(UiError arg0) {
                Toaster.show(arg0.errorMessage);
            }

            @Override
            public void onComplete(Object arg0) {
            }

            @Override
            public void onCancel() {

            }

            @Override
            public void onWarning(int i) {

            }
        });

    }

    @LuaBridge
    public static void executeGetQQContactFriend(String title, String imgUrl, String desc, String gotoUrl) {
        QQApi.getInstance().shareToQQWithUrlImg(title, imgUrl, desc, gotoUrl, MomoKit.getTopActivity(), new IUiListener() {

            @Override
            public void onError(UiError arg0) {
                Toaster.show(arg0.errorMessage);

            }

            @Override
            public void onComplete(Object arg0) {
            }

            @Override
            public void onCancel() {

            }

            @Override
            public void onWarning(int i) {

            }
        });
    }

}

