package com.immomo.momo.luaview.ud;

import com.immomo.game.view.CountDownView;
import com.immomo.mls.fun.ud.view.UDView;

import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

/**
 * 321倒计时
 * Created by wang.lichen on 2019-09-26.
 */
@LuaApiUsed
public class UDWolfCountDownView<V extends CountDownView> extends UDView<V> implements CountDownView.CountdownEndListener {
    public static final String LUA_CLASS_NAME = "WolfCountDownView";

    public static final String[] methods = {
            "setOnCountDownEndCallback",
            "startCountdown",
    };

    private LuaFunction onCountDownEndCallback;

    @LuaApiUsed
    protected UDWolfCountDownView(long L, LuaValue[] v) {
        super(L, v);
    }

    @Override
    protected V newView(LuaValue[] init) {
        return (V)new CountDownView(getContext());
    }

    @LuaApiUsed
    public LuaValue[] setOnCountDownEndCallback(LuaValue[] values) {
        this.onCountDownEndCallback = values.length > 0 ? values[0].toLuaFunction() : null;
        if (this.onCountDownEndCallback != null){
            getView().setOnCountdownListener(this);
        }else{
            getView().setOnCountdownListener(null);
        }

        return null;
    }
    @LuaApiUsed
    public LuaValue[] startCountdown(LuaValue[] values) {
        getView().startCountdown();
        return null;
    }



    @Override
    public void onCountDownEnd() {
        if (this.onCountDownEndCallback != null){
            this.onCountDownEndCallback.invoke(null);
        }
    }
}
