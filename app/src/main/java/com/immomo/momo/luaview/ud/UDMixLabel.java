package com.immomo.momo.luaview.ud;

import android.annotation.SuppressLint;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.text.Layout;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.immomo.framework.utils.UIUtils;
import com.immomo.mls.fun.constants.FontStyle;
import com.immomo.mls.fun.constants.TextAlign;
import com.immomo.mls.fun.ud.UDArray;
import com.immomo.mls.fun.ud.UDColor;
import com.immomo.mls.fun.ud.UDStyleString;
import com.immomo.mls.fun.ud.view.UDView;
import com.immomo.mls.util.AndroidUtil;
import com.immomo.mls.util.DimenUtil;
import com.immomo.mls.utils.convert.ConvertUtils;
import com.immomo.momo.R;
import com.immomo.momo.android.view.textview.LayoutTextView;
import com.immomo.momo.android.view.textview.textshrink.TextShrinkHelper;
import com.immomo.momo.emotionstore.util.MomoEmotionUtil;
import com.immomo.momo.feed.ui.FeedTextLayoutManager;
import com.immomo.momo.feed.ui.view.FeedTextView;
import com.immomo.momo.luaview.constants.MatchLinkType;
import com.immomo.momo.util.StringUtils;

import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaNumber;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import xfy.fakeview.library.text.drawer.TextDrawer;

/**
 * Author       :   <EMAIL>
 * Date         :   2019/3/7
 * Time         :   下午2:59
 * Description  :   水滴表情Label
 */

@LuaApiUsed
public class UDMixLabel<V extends FeedTextView> extends UDView<V> {
    public static final String LUA_CLASS_NAME = "MixLabel";
    public static final String[] methods = new String[]{
            "fontSize",
            "textAlign",
            "textColor",
            "lines",
            "setLineSpacing",
            "setLineSpacingExtra",
            "setMaxWidth",
            "breakMode",
            "setMatchLinkType",
            "text",
            "styleText",
            "setFooterStyleText",
            "setTextBold",
            "fontNameSize",
            "setTextFontStyle",
            "setAutoFit",
            "setLinksCallback",
            "emojiScale",
            "newText",
            "getFirstLineWidth",
            "setMergeWidth",
            "getLines"

    };

    private static final String HINT_ELLIPSIS = " ...";
    private static final String HINT_EXPAND = "全文";
    public static final String HINT_TAIL = HINT_ELLIPSIS + HINT_EXPAND;

    private static final String TEXT_FINAL_KEY = "text";
    private static final String ACTION_FINAL_KEY = "action";
    private static final String COLOR_FINAL_KEY = "color";

    private LuaFunction mLinkFunctionCallback;
    private LuaFunction mEndAllTextClickFunction;
    private UDStyleString mStyleString;
    private int color;
    private float mLineSpacing = 0;
    private int mMatchLinkType = MatchLinkType.None;
    private float mEndAllTextSize = 13;
    private String mEndAllText = HINT_TAIL;
    private Map<String, Object> map;
    private String mContentValue = "";
    private float contentLength = -1f;
    private int mEndAllTextColor = UIUtils.getColor(com.immomo.momo.android.R.color.color_BEBEBE);
    private boolean fixLineSpacingExtraForEmoj;//用新api修复带表情的内容 以兼容老版本
    private int mMergeWidth = 0;

    TextPaint mTextPaint;
    StaticLayout mStaticLayout;

    TextShrinkHelper.ShrinkClickListener mShrinkClickListener = new TextShrinkHelper.ShrinkClickListener() {
        @Override
        public void onClick(View widget) {
            // Toast.makeText(widget.getContext(),"click all text",Toast.LENGTH_LONG).show();

            if (mEndAllTextClickFunction != null)
                mEndAllTextClickFunction.invoke(LuaValue.rString(mEndAllText));
        }
    };

    @LuaApiUsed
    protected UDMixLabel(long L, LuaValue[] v) {
        super(L, v);
        initPaint();
    }

    private void initPaint() {
        mTextPaint = new TextPaint(Paint.ANTI_ALIAS_FLAG);
        mTextPaint.density = UIUtils.getResources().getDisplayMetrics().density;
        mTextPaint.setTextSize(UIUtils.sp2pix(16f));
        mTextPaint.setColor(UIUtils.getColor(R.color.C_05));
    }

    private int mTextSize = UIUtils.sp2pix(16f);

    @LuaApiUsed
    public LuaValue[] fontSize(LuaValue[] var) {
        if (var.length > 0 && !var[0].isNil()) {
            int size = UIUtils.sp2pix((float) var[0].toDouble());
            mTextSize = size;
            mTextPaint.setTextSize(size);
            mEndAllTextSize = (float) var[0].toDouble();
            return null;
        }
        return LuaValue.rNumber(DimenUtil.pxToSp(mTextPaint.getTextSize()));
    }

    @LuaApiUsed
    public LuaValue[] textAlign(LuaValue[] var) {
        if (var.length > 0 && !var[0].isNil()) {

            switch (var[0].toInt()) {
                case TextAlign.LEFT:
                    mTextPaint.setTextAlign(Paint.Align.LEFT);

                    break;
                case TextAlign.CENTER:
                    mTextPaint.setTextAlign(Paint.Align.CENTER);

                    break;

                case TextAlign.RIGHT:
                    mTextPaint.setTextAlign(Paint.Align.RIGHT);
                    break;
            }

            return null;
        }

        return null;
    }

    int mTextColor = UIUtils.getColor(R.color.C_05);

    @LuaApiUsed
    public LuaValue[] textColor(LuaValue[] var) {
        if (var.length > 0 && var[0] instanceof UDColor) {
            UDColor color = (UDColor) var[0];
            mTextColor = color.getColor();
            mTextPaint.setColor(mTextColor);

            return null;
        }
        return null;
    }

    int mMaxLines;

    @LuaApiUsed
    public LuaValue[] lines(LuaValue[] var) {
        if (var.length > 0 && !var[0].isNil()) {
            mMaxLines = var[0].toInt();

            if (mMaxLines == 0)
                mMaxLines = Integer.MAX_VALUE;
            /*if (mMaxLines >= 1)
                --mMaxLines;*/

            if (StringUtils.isNotEmpty(mContentValue))
                setText(mContentValue + " ");

            if (mMaxLines == Integer.MAX_VALUE && mMapArray != null && mLinkFunctionCallback != null)
                parseSingleAction(mMapArray, mLinkFunctionCallback);
            return null;
        }

        return null;
    }

    @LuaApiUsed
    public LuaValue[] setMergeWidth(LuaValue[] values) {
        if (values.length > 0 && !values[0].isNil()) {
            int merge = values[0].toInt();
            mMergeWidth = DimenUtil.dpiToPx((float) merge);
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] getLines(LuaValue[] values) {
        if (mStaticLayout != null) {
            return LuaValue.rNumber(mStaticLayout.getLineCount());
        }
        return LuaValue.rNumber(0);
    }


    /**
     * @deprecated Use
     * * {@link UDMixLabel#setLineSpacingExtra(LuaValue[])}
     */
    @Deprecated
    @LuaApiUsed
    public LuaValue[] setLineSpacing(LuaValue[] spacing) {
        if (spacing.length > 0) {
            mLineSpacing = UIUtils.getPixels(spacing[0].toInt());//无效的代码 因为LayoutTextView的spacingAdd是默认设置的1
            return null;
        }
        return LuaValue.rNumber(0);
    }

    @LuaApiUsed
    public LuaValue[] setLineSpacingExtra(LuaValue[] spacing) {
        if (spacing.length > 0) {
            mLineSpacing = UIUtils.getPixels(spacing[0].toInt());//无效的代码 因为LayoutTextView的spacingAdd是默认设置的1
            getView().setSpacingAdd(spacing[0].toInt());//内部会转为pixel
            fixLineSpacingExtraForEmoj = true;
            return null;
        }
        return LuaValue.rNumber(0);
    }


    float mMaxWidth = -1;

    @LuaApiUsed
    public LuaValue[] setMaxWidth(LuaValue[] w) {
        mMaxWidth = DimenUtil.dpiToPx((float) w[0].toDouble());

        if (mMaxWidth > 0)
            setWidth(mMaxWidth);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] breakMode(LuaValue[] var) {
        if (var.length > 0 && !var[0].isNil()) {
            // getView().getLayoutOfTail(getWidth(), i, mStaticLayout, null);

            return null;
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setMatchLinkType(LuaValue[] type) {
        mMatchLinkType = type.length > 0 ? type[0].toInt() : MatchLinkType.None;
        return null;
    }

    double mEmojiScale = 1.5;

    @LuaApiUsed
    public LuaValue[] emojiScale(LuaValue[] values) {
        if (values.length >= 1 && values[0].isNumber()) {
            mEmojiScale = values[0].toDouble();
            return null;
        }
        return varargsOf(LuaNumber.valueOf(mEmojiScale));
    }

    @LuaApiUsed
    public LuaValue[] text(LuaValue[] varargs) {

        String content = "";

        if (varargs.length > 0 && !varargs[0].isNil()) {
            content = varargs[0].toJavaString();
        }

        if (StringUtils.isEmpty(content))
            return null;

        setText(content);
        return null;
    }

    private int fontStyle = FontStyle.NORMAL;
    private static Field ellipsesTypeField;

    private void setViewEllipses() {
        if (ellipsesTypeField == null) {
            try {
                ellipsesTypeField = LayoutTextView.class.getDeclaredField("ellipsesType");
                ellipsesTypeField.setAccessible(true);
            } catch (Throwable ignore) {
            }
        }
        if (ellipsesTypeField != null) {
            try {
                ellipsesTypeField.set(getView(), LayoutTextView.ELLIPSES);
            } catch (Throwable ignore) {
            }
        }
    }

    @LuaApiUsed
    public LuaValue[] newText(LuaValue[] args) {
        String text = args[0].toJavaString();
        if (mContentValue.equals(text))
            return null;
        setViewEllipses();
        mContentValue = text;
        CharSequence textSpan = fixLineSpacingExtraForEmoj ? MomoEmotionUtil.getEmoteStaticSpanWithLineSpace(text, (int) (mTextSize * mEmojiScale)) : MomoEmotionUtil.getEmoteStaticSpan(text, (int) (mTextSize * mEmojiScale));
        if ((mMatchLinkType & MatchLinkType.All) == MatchLinkType.All ||
                (mMatchLinkType & MatchLinkType.PhoneNumber) == MatchLinkType.PhoneNumber ||
                (mMatchLinkType & MatchLinkType.MOMOID) == MatchLinkType.MOMOID)
            textSpan = FeedTextLayoutManager.addMoMoIdSpan(textSpan, FeedTextLayoutManager.MOMO_ID_PATTERN);
        mTextPaint.setTextSize(mTextSize);
        mTextPaint.setColor(mTextColor);
        TextDrawer.clear(mTextPaint, Typeface.BOLD);
        TextDrawer.clear(mTextPaint, Typeface.ITALIC);
        TextDrawer.clear(mTextPaint, Typeface.BOLD_ITALIC);
        if (fontStyle != FontStyle.NORMAL)
            TextDrawer.apply(mTextPaint, fontStyle);

        ViewGroup.LayoutParams lp = getView().getLayoutParams();
        int w = ViewGroup.LayoutParams.WRAP_CONTENT;
        if (lp != null) {
            w = lp.width;
        }
        if (w < 0) {
            w = AndroidUtil.getScreenWidth(getContext());
        }

        mStaticLayout = new StaticLayout(textSpan,
                mTextPaint,
                w,
                Layout.Alignment.ALIGN_NORMAL,
                1f,
                mLineSpacing,
                true);
        if (mMaxLines != 0) {
            mStaticLayout = makeLayoutOfTail(w, mMaxLines, "", mStaticLayout, mShrinkClickListener);
            getView().setMaxLines(mMaxLines);
        }
        getView().setLayout(mStaticLayout);
        return null;
    }


    @SuppressLint("DeprecatedAPI")
    private void setText(String content) {

        if (mContentValue.equals(content))
            return;

        mContentValue = content;
        int emojiSize = (int) (mTextSize * mEmojiScale);

        CharSequence textSpan = fixLineSpacingExtraForEmoj ? MomoEmotionUtil.getEmoteStaticSpanWithLineSpace(content, emojiSize) : MomoEmotionUtil.getEmoteStaticSpan(content, emojiSize);

        if ((mMatchLinkType & MatchLinkType.All) == MatchLinkType.All ||
                (mMatchLinkType & MatchLinkType.PhoneNumber) == MatchLinkType.PhoneNumber ||
                (mMatchLinkType & MatchLinkType.MOMOID) == MatchLinkType.MOMOID)
            textSpan = FeedTextLayoutManager.addMoMoIdSpan(textSpan, FeedTextLayoutManager.MOMO_ID_PATTERN);

        mTextPaint.setTextSize(mTextSize);
        mTextPaint.setColor(mTextColor);
        //根据当前控件宽度，设置文本展示宽度！！！无法设置自适应或充满视图，此时还无法进行测量，因此必须要设置视图宽度
        ViewGroup.LayoutParams lp = getView().getLayoutParams();
        int viewWidth = ViewGroup.LayoutParams.WRAP_CONTENT;
        if (lp != null) {
            viewWidth = lp.width - getView().getPaddingLeft() - getView().getPaddingRight();
        }
        if (viewWidth <= 0) {
            viewWidth = FeedTextLayoutManager.getFeedTextWidthChange();
        }
        mStaticLayout = new StaticLayout(textSpan, mTextPaint, viewWidth, Layout.Alignment.ALIGN_NORMAL, 1f, mLineSpacing, true);

        if (mMaxLines != 0)
            mStaticLayout = makeLayoutOfTail(viewWidth, mMaxLines, "", mStaticLayout, mShrinkClickListener);

        resetWidth(content);
    }

    private void resetWidth(String content) {
        if (mMaxWidth > 0)
            getView().setMaxWidth((int) mMaxWidth);

        getView().setLayout(mStaticLayout);

        float value = mTextPaint.measureText(content);
        contentLength = value;
        if (value > mMaxWidth)
            value = mMaxWidth;

        setWidth(value);
    }

    @LuaApiUsed
    public LuaValue[] getFirstLineWidth(LuaValue[] value) {
        if (mStaticLayout != null) {
            return LuaValue.rNumber(UIUtils.getDips((int) mStaticLayout.getLineWidth(0)));
        } else {
            return LuaValue.rNumber(getWidth());
        }
    }

    @LuaApiUsed
    public LuaValue[] styleText(LuaValue[] styles) {

        mStaticLayout = new StaticLayout(((UDStyleString) styles[0]).getText(), mTextPaint, FeedTextLayoutManager.getFeedTextWidthChange(), Layout.Alignment.ALIGN_NORMAL, 1f, mLineSpacing, true);

        if (mMaxLines != 0)
            mStaticLayout = makeLayoutOfTail(getWidth(), mMaxLines, "", mStaticLayout, mShrinkClickListener);

        if (mMaxWidth > 0)
            getView().setMaxWidth((int) mMaxWidth);

        getView().setLayout(mStaticLayout);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setFooterStyleText(LuaValue[] values) {
        mStyleString = values.length > 0 ? ((UDStyleString) values[0]) : null;

        LuaFunction endAllTextClickFunction = values.length > 1 ? values[1].toLuaFunction() : null;
        if (mStyleString != null) {

            if (mStyleString.getColor() != -1)
                mEndAllTextColor = mStyleString.getColor();

            if (mStyleString.getTextSize() != -1)
                mEndAllTextSize = mStyleString.getTextSize();

            mEndAllText = mStyleString.getText().toString();

            mEndAllTextClickFunction = endAllTextClickFunction;

            return null;
        }

        return null;
    }

    @LuaApiUsed
    public LuaValue[] setTextBold(LuaValue[] values) {
        return null;

    }

    @LuaApiUsed
    public LuaValue[] fontNameSize(LuaValue[] var) {
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setTextFontStyle(LuaValue[] style) {
        fontStyle = style[0].toInt();
        return null;

    }

    @LuaApiUsed
    public LuaValue[] setAutoFit(LuaValue[] autoFit) {
        return null;
    }


    UDArray mMapArray;

    @LuaApiUsed
    public LuaValue[] setLinksCallback(LuaValue[] values) {
        UDArray mapArray = ((UDArray) values[0]);
        LuaFunction linkFunctionCallback = values.length > 1 ? values[1].toLuaFunction() : null;

        parseSingleAction(mapArray, linkFunctionCallback);
        return null;
    }

    private void parseSingleAction(UDArray mapArray, LuaFunction linkFunctionCallback) {
        List mapList = mapArray.getArray();

        mMapArray = mapArray;
        mLinkFunctionCallback = linkFunctionCallback;

        // String currentString = mStaticLayout.getText().toString();
        SpannableStringBuilder stringBuilder = new SpannableStringBuilder();


        for (int i = 0, size = mapList.size(); i < size; i++) {
            Map singleMap = (Map) mapList.get(i);
            String singleTextValue = (String) singleMap.get(TEXT_FINAL_KEY);
            stringBuilder.append(singleTextValue);
        }

        String finalValue = stringBuilder.toString();

        for (int i = 0, size = mapList.size(); i < size; i++) {
            Map singleMap = (Map) mapList.get(i);

            String singleTextValue = (String) singleMap.get(TEXT_FINAL_KEY);
            String singleActionValue = (String) singleMap.get(ACTION_FINAL_KEY);
            String singleColorValue = ((String) singleMap.get(COLOR_FINAL_KEY));


            if (StringUtils.isNotEmpty(singleColorValue))  // 有颜色  设置颜色
                setColor(singleColorValue);
            else if (StringUtils.isNotEmpty(singleActionValue))  //  有事件  没有颜色  设置默认蓝色
                setColor("(59,179,250)");
            else
                this.color = mTextColor;

            int start = finalValue.indexOf(singleTextValue);
            int end = start + singleTextValue.length();

            if (start >= end)
                continue;


            ForegroundColorSpan colorSpan = new ForegroundColorSpan(color);

            stringBuilder.setSpan(colorSpan, start, end,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);


            if (StringUtils.isNotEmpty(singleActionValue)) {
                UDMixLabelClickable clickable = new UDMixLabelClickable();
                clickable.setTextValue(singleTextValue);
                clickable.setPosition(i + 1);
                clickable.setAction(singleActionValue);
                clickable.setColor(color);

                stringBuilder.setSpan(clickable, start, end,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
        }

        int emojiSize = (int) (mTextPaint.getTextSize() * mEmojiScale);
        CharSequence textSpan = fixLineSpacingExtraForEmoj ? MomoEmotionUtil.getEmoteStaticSpanWithLineSpace(stringBuilder, emojiSize) : MomoEmotionUtil.getEmoteStaticSpan(stringBuilder, emojiSize);

        mStaticLayout = new StaticLayout(textSpan, mTextPaint, FeedTextLayoutManager.getFeedTextWidthChange() - mMergeWidth, Layout.Alignment.ALIGN_NORMAL, 1f, mLineSpacing, true);


        mContentValue = mStaticLayout.getText().toString();
        if (mMaxLines != 0)
            mStaticLayout = makeLayoutOfTail(getWidth(), mMaxLines, "", mStaticLayout, mShrinkClickListener);

        resetWidth(mStaticLayout.getText().toString());
    }

    @Override
    protected V newView(LuaValue[] init) {
        return (V) new FeedTextView(getContext());
    }


    public class UDMixLabelClickable extends ClickableSpan {

        String textValue = "";
        int position;

        String action = "";
        int color;

        public UDMixLabelClickable() {
        }

        public void setTextValue(String textValue) {
            this.textValue = textValue;
        }

        public void setPosition(int position) {
            this.position = position;
        }

        public void setAction(String action) {
            this.action = action;
        }

        public void setColor(int color) {
            this.color = color;
        }


        @Override
        public void onClick(View v) {

            if (map == null)
                map = new HashMap();

            map.clear();

            map.put(TEXT_FINAL_KEY, textValue);
            map.put(ACTION_FINAL_KEY, action);
            map.put(COLOR_FINAL_KEY, color);


            if (mLinkFunctionCallback != null) {
                mLinkFunctionCallback.invoke(LuaValue.varargsOf(LuaNumber.valueOf(position), ConvertUtils.toLuaValue(getGlobals(), map)));
            }
        }

        @Override
        public void updateDrawState(TextPaint ds) {
            ds.setUnderlineText(false);
        }
    }

    public void setColor(String colorStr) {

        if (colorStr.startsWith("(") && colorStr.endsWith(")")) {
            colorStr = colorStr.substring(1, colorStr.length() - 1);
        }

        String[] rgb = colorStr.split(",");
        if (rgb.length < 3 || rgb.length > 4) {
            throw new IllegalArgumentException("Unknown color");
        }

        int r, g, b, a = 255;
        try {
            r = Integer.valueOf(rgb[0].trim());
            g = Integer.valueOf(rgb[1].trim());
            b = Integer.valueOf(rgb[2].trim());
            if (rgb.length > 3) {
                a = (int) (Float.parseFloat(rgb[3].trim()) * 255);
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Unknown color");
        }

        if (r < 0 || r > 255 || g < 0 || g > 255 || b < 0 || b > 255) {
            throw new IllegalArgumentException("Unknown color");
        }
        if (a < 0 || a > 255) {
            a = 255;
        }

        this.color = Color.argb(a, r, g, b);
    }


    public StaticLayout makeLayoutOfTail(int newWidth, int maxLines, @NonNull CharSequence topicText, @NonNull StaticLayout contenLayout, TextShrinkHelper.ShrinkClickListener listener) {

        if (contenLayout.getLineCount() <= maxLines) {
            return contenLayout;
        }

        CharSequence originText = contenLayout.getText();

        TextPaint textPaint = contenLayout.getPaint();

        final int spacingAdd = UIUtils.getPixels(1f);

        float hintTailLenght = textPaint.measureText(mEndAllText);

        // 限定话题宽度
        CharSequence resultTopicText = TextUtils.ellipsize(topicText, textPaint, newWidth - hintTailLenght - UIUtils.getPixels(3), TextUtils.TruncateAt.END);

        // topic + HINT_TAIL 所需要的长度
        int retainStrWidth = (int) (textPaint.measureText(resultTopicText, 0, resultTopicText.length()) + textPaint.measureText(mEndAllText));

        if (retainStrWidth > newWidth) {
            // 防止计算误差 导致结果限定宽度
            retainStrWidth = newWidth;
        }

        CharSequence shrinkText = contenLayout.getText();
        if (contenLayout.getLineCount() >= maxLines) {
            // 只在大于maxlines时需要截断 ， 留出最后一行给topic+HINT_TAIL
            final int line = maxLines - 1;
            final int lineStart = contenLayout.getLineStart(line);
            final int lineEnd = contenLayout.getLineEnd(line);

            final int tailWidth = retainStrWidth;
            final int lineWidth = contenLayout.getWidth();

            int lineTrim = lineEnd;
            CharSequence subText = originText.subSequence(lineStart, lineTrim);

            while (Layout.getDesiredWidth(subText, textPaint) + tailWidth > lineWidth) {
                lineTrim--;
                if (lineTrim <= lineStart) {
                    break;
                }
                subText = originText.subSequence(lineStart, lineTrim);
            }
            shrinkText = removeEndLineBreak(originText.subSequence(0, lineTrim));
        }

        CharSequence endText = mEndAllText;
        if (mStyleString != null) {
            endText = mStyleString.getText();
        }
        SpannableStringBuilder mTextWithTail = new SpannableStringBuilder(shrinkText).append(endText).append(resultTopicText);

        final int length = mTextWithTail.length() - mEndAllText.length() - resultTopicText.length();

        if (mEndAllTextClickFunction != null) {
            mTextWithTail.setSpan(new TouchableSpan(listener, mEndAllTextSize, mEndAllTextColor)
                    , length
                    , mTextWithTail.length() - resultTopicText.length()
                    , Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        } else if (mStyleString == null || mStyleString.getColor() != -1) {
            ForegroundColorSpan colorSpan = new ForegroundColorSpan(mEndAllTextColor);
            mTextWithTail.setSpan(colorSpan
                    , length
                    , mTextWithTail.length() - resultTopicText.length()
                    , Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }


        return new StaticLayout(mTextWithTail, 0, mTextWithTail.length()
                , textPaint, newWidth
                , Layout.Alignment.ALIGN_NORMAL
                , 1f, mLineSpacing
                , true);
    }

    public static class TouchableSpan extends ClickableSpan {


        private final TextShrinkHelper.ShrinkClickListener mListener;
        int mEndAllTextColor = UIUtils.getColor(com.immomo.momo.android.R.color.color_BEBEBE);
        float mEndAllTextSize = 13;

        public TouchableSpan(TextShrinkHelper.ShrinkClickListener listener, float mEndAllTextSize, int mEndAllTextColor) {
            mListener = listener;
            this.mEndAllTextSize = mEndAllTextSize;
            this.mEndAllTextColor = mEndAllTextColor;
        }


        @Override
        public void onClick(View widget) {
            if (mListener != null) {
                mListener.onClick(widget);
            }
        }

        @Override
        public void updateDrawState(TextPaint ds) {
            super.updateDrawState(ds);
            ds.setColor(mEndAllTextColor);
            ds.setTextSize(UIUtils.sp2pix(mEndAllTextSize));
            ds.setUnderlineText(false);
        }
    }

    public static CharSequence removeEndLineBreak(CharSequence text) {
        if (TextUtils.isEmpty(text)) {
            return "";
        }
        final int length = text.length();
        if (length > 0 && TextUtils.equals("\n", "" + text.charAt(length - 1))) {
            text = text.subSequence(0, length - 1);
        }
        return text;
    }
}
