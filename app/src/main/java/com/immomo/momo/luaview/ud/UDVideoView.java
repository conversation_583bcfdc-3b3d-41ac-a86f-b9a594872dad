package com.immomo.momo.luaview.ud;

import android.net.Uri;
import android.text.TextUtils;
import android.view.ViewGroup;

import com.immomo.mls.fun.ud.view.UDViewGroup;
import com.immomo.momo.feed.player.ExoTextureLayout;
import com.immomo.momo.feed.player.IMediaPlayer;
import com.immomo.momo.luaview.LuaMediaView;
import com.immomo.momo.luaview.constants.PlayStatus;

import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaNumber;
import org.luaj.vm2.LuaString;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

/**
 * Created by fanqiang on 2018/9/5.
 */

@LuaApiUsed
public class UDVideoView<V extends ExoTextureLayout> extends UDViewGroup<V> implements LuaMediaView.Callback, LuaMediaView.ProgressUpdateCallback {
    public static final String LUA_CLASS_NAME = "VideoView";

    public static final String[] methods = {
            "src",
            "mute",
            "repeatCount",
            "offScreen",
            "totalDuration",
            "play",
            "stop",
            "pause",
            "setDidStartCallback",
            "setStartStallingCallback",
            "setEndStallingCallback",
            "setFinishCallback",
            "setFailCallback",
            "setProgressCallback",
            "setVideoSizeChangedCallback",
            "setWillRepeatCallback",
            "getPlayStatus",
            "directAccess",
    };

    @LuaApiUsed
    public UDVideoView(long L, LuaValue[] v) {
        super(L, v);
        if (v.length > 0) {
            uri = Uri.parse(v[0].toJavaString());
        }
        initView();
    }

    @Override
    protected V newView(LuaValue[] init) {
        return (V) new LuaMediaView(getContext());
    }


    private Uri uri;

    private boolean mAutoPlay;
    private int repeatCount;
    private boolean isMute;
    private long totalDuration;

    private long stopPoint = 0;
    private LuaFunction didStartFun;
    private LuaFunction startStallingFun;
    private LuaFunction endStallingFun;
    private LuaFunction finishFun;
    private LuaFunction failFun;
    private LuaFunction progressFun;
    private LuaFunction sizeChangedFun;
    private LuaFunction willRepeatFun;
    private int playStatus = PlayStatus.IDEL;
    private boolean hasStalling = false;
    private boolean isStop = true;

    @LuaApiUsed
    public LuaValue[] src(LuaValue[] values) {
        if (values.length == 0) {
            return LuaValue.rString(uri.toString());
        }
        LuaValue value = values[0];
        String src = value.toJavaString();
        if (!TextUtils.isEmpty(src)) {
            uri = Uri.parse(src);
            if (getVideoView() != null) {
                getVideoView().setUri(uri);
                getVideoView().checkUrl(src);
            }
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] mute(LuaValue[] values) {
        if (values.length == 0) {
            return LuaValue.rBoolean(this.isMute);
        }
        LuaValue value = values[0];
        this.isMute = value.toBoolean();
        if (getVideoView() != null) {
            getVideoView().setSilentMode(isMute);
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] repeatCount(LuaValue[] values) {
        if (values.length == 0) {
            return LuaValue.rNumber(repeatCount);
        }
        LuaValue value = values[0];
        repeatCount = value.toInt();
        if (getVideoView() != null) {
//            getVideoView().setRepeatCount(repeatCount);
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] offScreen(LuaValue[] values) {
        if (values.length == 0) {
            return LuaValue.rBoolean(this.mAutoPlay);
        }

        LuaValue value = values[0];
        this.mAutoPlay = value.toBoolean();
        if (getVideoView() != null) {
            getVideoView().setAutoPlayForbidden(!this.mAutoPlay);
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] totalDuration(LuaValue[] values) {
        if (values.length == 0) {
            return LuaValue.rNumber(getVideoView().getDuration() * 1.0 / 1000);
        }
        LuaValue value = values[0];
        totalDuration = value.toInt();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] play(LuaValue[] values) {
        startPlayVideo();
        isStop = false;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] directAccess(LuaValue[] values) {
        boolean directAccess = values.length > 0 && values[0].toBoolean();
        getVideoView().directAccess(directAccess);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] stop(LuaValue[] values) {
        stopPlayVideo();
        isStop = true;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] pause(LuaValue[] values) {
        pausePlayVideo();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setDidStartCallback(LuaValue[] values) {
        didStartFun = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setStartStallingCallback(LuaValue[] values) {
        startStallingFun = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setEndStallingCallback(LuaValue[] values) {
        endStallingFun = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setFinishCallback(LuaValue[] values) {
        finishFun = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setFailCallback(LuaValue[] values) {
        failFun = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setProgressCallback(LuaValue[] values) {
        progressFun = values.length != 0 ? values[0].toLuaFunction() : null;
        getVideoView().setProgressCallback(progressFun == null ? null : this);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setVideoSizeChangedCallback(LuaValue[] values) {
        sizeChangedFun = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setWillRepeatCallback(LuaValue[] values) {
        willRepeatFun = values.length != 0 ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] getPlayStatus(LuaValue[] values) {
        if (isStop) {
            return LuaValue.rNumber(PlayStatus.IDEL);
        }
        switch (playStatus) {
            case PlayStatus.READY:
            case PlayStatus.PREPARING:
                return LuaValue.rNumber(getVideoView().isPlaying() ? PlayStatus.PLAYING : PlayStatus.PAUSE);
        }
        return LuaValue.rNumber(playStatus);
    }

    protected void initView() {
        getVideoView().setCallback(this);
        ViewGroup.LayoutParams videoLayoutParams =
                new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT);
        getVideoView().setLayoutParams(videoLayoutParams);
        getVideoView().setSilentMode(isMute);
//        getVideoView().setRepeatCount(repeatCount);
        getVideoView().setAutoPlayForbidden(!mAutoPlay);

        if (uri != null)
            getVideoView().setUri(uri);
    }

    private void pausePlayVideo() {
        if (getVideoView() != null) {
            stopPoint = getVideoView().getCurrentPosition();
            getVideoView().pause();
        }
    }

    private void stopPlayVideo() {
        if (getVideoView() != null) {
            stopPoint = getVideoView().getCurrentPosition();
            getVideoView().stopPlayback();
        }
    }

    private void startPlayVideo() {
        if (getVideoView() != null) {
            if (totalDuration > 0 && stopPoint >= totalDuration)
                stopPoint = 0;
            if (stopPoint > 0) {
                getVideoView().seekTo(stopPoint);
                stopPoint = 0;
            }
            getVideoView().start();
        }
    }

    @Override
    public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {
        switch (playbackState) {
            case IMediaPlayer.STATE_ENDED:
                playStatus = PlayStatus.IDEL;
                if (finishFun != null) {
                    finishFun.invoke(LuaValue.rString(uri.toString()));
                }
                break;
            case IMediaPlayer.STATE_BUFFERING:
                playStatus = PlayStatus.PREPARING;
                if (startStallingFun != null) {
                    startStallingFun.invoke(LuaValue.rString(uri.toString()));
                }
                hasStalling = true;
                break;
            case IMediaPlayer.STATE_READY:
                if (hasStalling && endStallingFun != null) {
                    endStallingFun.invoke(LuaValue.rString(uri.toString()));
                    hasStalling = false;
                }
                playStatus = PlayStatus.READY;
                totalDuration = getVideoView() != null ? getVideoView().getDuration() : -1;
                break;
        }
    }

    @Override
    public void onError(int what, int extra) {
        playStatus = PlayStatus.ERROR;
        if (failFun != null) {
            failFun.invoke(LuaValue.rString(uri.toString()));
        }
    }

    @Override
    public void onStartRendering() {
        playStatus = PlayStatus.PLAYING;
        if (didStartFun != null) {
            didStartFun.invoke(varargsOf(LuaString.valueOf(uri.toString()), LuaNumber.valueOf(getVideoView().getDuration() * 1.0 / 1000)));
        }
    }

    @Override
    public void onVideoSizeChanged(int width, int height, int unappliedRotationDegrees, float pixelWidthHeightRatio) {
        if (sizeChangedFun != null) {
            sizeChangedFun.invoke(varargsOf(LuaString.valueOf(uri.toString()), LuaNumber.valueOf(width), LuaNumber.valueOf(height)));
        }
    }

    @Override
    public void onLoopStart() {
        if (willRepeatFun != null) {
            willRepeatFun.invoke(varargsOf(LuaString.valueOf(uri.toString()), LuaNumber.valueOf(getVideoView().getDuration() * 1.0 / 1000)));
        }
    }

    private LuaMediaView getVideoView() {
        return (LuaMediaView) getView();
    }

    @Override
    public void onProgressUpdate(long progress) {
        if (progressFun != null) {
            progressFun.invoke(varargsOf(LuaString.valueOf(uri.toString()), LuaNumber.valueOf(progress * 1.0 / 1000), LuaNumber.valueOf(getVideoView().getDuration() * 1.0 / 1000)));
        }
    }
}
