package com.immomo.momo.luaview.lt;

import android.view.View;

import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;


/**
 * <AUTHOR>
 */
@LuaClass(isStatic = true)
public class LTHalfAcManager {
    /**
     * lua 桥接名称
     */
    public static final String LUA_CLASS_NAME = "CurrentHalfViewManager";

    @LuaBridge
    public static void halfLuaScrollBaseView(View view) {
        if (view != null) {
            HalfAcViewManager.INSTANCE.changeScrollView(view);
        }
    }

    @LuaBridge
    public static void closeHalfLuaViewController() {
        HalfAcViewManager.INSTANCE.closeActivity();
    }

    @LuaBridge
    public static void addCurrentControllerToGroup() {
        //待实现
    }

    @LuaBridge
    public static void closeAllHalfLuaViewController(String key) {
        //待实现
    }

    @LuaBridge
    public static void setBackgroundColor(String color) {
        //待实现
    }
    @LuaBridge
    public static void setBackgroundAlpha(float alpha) {
        //待实现
    }
    @LuaBridge
    public static void setSubScrollScrollEnableWhenTop(float alpha) {
        //待实现
    }
}
