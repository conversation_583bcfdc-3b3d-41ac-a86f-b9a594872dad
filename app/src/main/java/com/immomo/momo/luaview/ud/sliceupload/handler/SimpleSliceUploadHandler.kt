package com.immomo.momo.luaview.ud.sliceupload.handler

import android.text.TextUtils
import android.util.Pair
import com.immomo.momo.luaview.ud.sliceupload.api.IUploadApi
import com.immomo.momo.luaview.ud.sliceupload.callback.SliceUploadListener
import com.immomo.momo.luaview.ud.sliceupload.model.SliceUploadModel
import com.immomo.momo.luaview.ud.sliceupload.model.SliceUploadResult
import com.immomo.momo.util.uploadtask.UploadProgressDbHelper
import com.immomo.momo.util.uploadtask.UploadProgressManager
import java.io.BufferedInputStream
import java.io.ByteArrayOutputStream
import java.io.FileInputStream
import java.io.InputStream

/**
 * Created on 2019-11-30.
 *
 * <AUTHOR>
 */
class SimpleSliceUploadHandler(private val uploadApi: IUploadApi) {


    private var isInterrupted = false
    private var isNeedSlice: Boolean = true

    constructor(uploadApi: IUploadApi, isNeedSlice: Boolean = true) : this(uploadApi) {
        this.isNeedSlice = isNeedSlice
    }


    fun getBlockLength(fileLength: Long): Long {
        return if (isNeedSlice) UploadProgressManager.getBlockSizeByNetStatus() else fileLength
    }

    fun interrupt() {
        isInterrupted = true
    }


    @Throws(Exception::class)
    fun upload(
        uploadModel: SliceUploadModel,
        uploadListener: SliceUploadListener?
    ): SliceUploadResult? {
        isInterrupted = false
        var inputStream: InputStream? = null
        var bos: ByteArrayOutputStream? = null
        var uploadResultData: SliceUploadResult? = null
        val file = uploadModel.targetFile
        var offset = 0L
        val uuid = uploadModel.targetFileUuid


        if (UploadProgressManager.isCompleted(uuid)) {
            val entity = UploadProgressDbHelper.findEntityByUUID(uuid)
            uploadResultData = SliceUploadResult()
            uploadResultData.extension = entity?.extension

            return uploadResultData
        } else if (offset == uploadModel.targetFileLength) {
            offset = 0
        }


        try {
            val fileLength = file.length() // 文件总大小

            /*在wifi情况下不切片，直接整个文件上传*/
            val blockSize: Int

            val blockSizeInDb = UploadProgressManager.getBlockSize(uuid)
            if (blockSizeInDb > 1) {
                //分片大小以第一次的为准，否则服务器无法接受完整文件。
                blockSize = blockSizeInDb.toInt()
            } else {
                blockSize = getBlockLength(fileLength).toInt()
            }




            inputStream = BufferedInputStream(FileInputStream(file))

            /*
             * 跳过已上传的字节
			 */
            if (offset > 0 && offset <= fileLength) {
                val skipBytes = inputStream.skip(offset)
                if (skipBytes < 0 || skipBytes != offset) {
                    offset = 0
                    inputStream.skip(0)
                }
            } else {
                offset = 0
            }

            uploadListener?.onProcess(
                Pair(offset, uploadModel.targetFileLength),
                uploadModel.targetFileUuid
            )

            bos = ByteArrayOutputStream(blockSize) // 一次要上传的数据缓冲区 10240
            val data = ByteArray(2048)
            var len = -1
            while (true) {
                if (isInterrupted) {
                    return null
                }
                len = inputStream.read(data)
                if (len <= 0) {
                    break
                }
                bos!!.write(data, 0, len)

                val bufferSize = bos.size()

                /*
                 * 当缓冲区的数据大于等于 10KB 时，上传数据到服务器
				 */
                if (bufferSize >= blockSize) {
                    val video = realUpload(bos, offset, uploadModel)

                    if (offset == 0L && bufferSize >= fileLength) {
                        uploadResultData = video
                        return uploadResultData
                    }
                    offset += bufferSize.toLong()
                    uploadListener?.onProcess(
                        Pair(offset, uploadModel.targetFileLength),
                        uploadModel.targetFileUuid
                    )

                    bos.close()
                    bos = ByteArrayOutputStream(blockSize)
                }
            }

            /*
             * 如果缓冲区还有残余数据，上传它们
			 */
            val bufferSize = bos!!.size()
            if (bufferSize > 0) {
                uploadResultData = realUpload(bos, offset, uploadModel)
                offset += bufferSize.toLong()

//                todo 改改，憋和 feed 共用一个类型
                UploadProgressManager.onUploadProgress(uuid, offset)

                uploadListener?.onProcess(
                    Pair(offset, uploadModel.targetFileLength),
                    uploadModel.targetFileUuid
                )
            }
        } catch (e: Exception) {
            throw e
        } finally {
            if (bos != null) {
                bos.close()
                bos = null
            }

            if (inputStream != null) {
                inputStream.close()
                inputStream = null
            }
        }

        //容错逻辑：有时候续传文件，服务器不会返回filename。
        if (TextUtils.equals(uploadResultData?.fileName, "")) {
            if (uploadListener != null) {
                uploadListener.onFailed()
                uploadResultData = null
            }
        }

        return uploadResultData
    }

    @Throws(Exception::class)
    protected fun realUpload(
        bos: ByteArrayOutputStream,
        offset: Long,
        uploadVideoModel: SliceUploadModel
    ): SliceUploadResult? {

        val index = (offset / getBlockLength(uploadVideoModel.targetFileLength)).toInt()

        return uploadApi.upload(
            uploadVideoModel,
            bos.toByteArray(),
            bos.size(),
            offset,
            index
        )
    }
}
