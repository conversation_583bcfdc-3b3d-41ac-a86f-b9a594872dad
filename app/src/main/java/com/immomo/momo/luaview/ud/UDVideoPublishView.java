package com.immomo.momo.luaview.ud;

import android.app.Activity;
import android.os.Handler;
import android.os.Message;

import androidx.annotation.NonNull;

import com.immomo.android.module.feed.statistics.EVAction;
import com.immomo.android.module.feed.statistics.EVPage;
import com.immomo.android.router.momo.util.LoggerUtilRouter;
import com.immomo.mls.fun.ud.view.UDView;
import com.immomo.mmstatistics.event.ClickEvent;
import com.immomo.momo.MomoKit;
import com.immomo.momo.businessmodel.statistics.PageStepHelper;
import com.immomo.momo.feed.ui.CachePublishType;
import com.immomo.momo.feed.ui.PublishFeedButton;
import com.immomo.momo.feed.ui.PublishFeedButtonKt;
import com.immomo.momo.feed.util.BasePublishUtil;
import com.immomo.momo.feed.util.PublishLuaClickManager;
import com.immomo.momo.statistics.LoggerKeys;

import org.jetbrains.annotations.NotNull;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import info.xudshen.android.appasm.AppAsm;


@LuaApiUsed
public class UDVideoPublishView extends UDView<PublishFeedButton> {
    public static final String LUA_CLASS_NAME = "VideoPublishView";

    private PublishFeedButton publishFeedButton;

    public static final String[] methods = {
            "setCustomStyle",
            "resetPageState",
            "release"
    };

    @LuaApiUsed
    public UDVideoPublishView(long L, LuaValue[] initParams) {
        super(L, initParams);
    }

    @NotNull
    @Override
    protected PublishFeedButton newView(LuaValue[] init) {
        publishFeedButton = new PublishFeedButton(getContext());
        publishFeedButton.setOnClickListener(v -> {
            AppAsm.getRouter(LoggerUtilRouter.class).saveGotoLog(LoggerKeys.Record.FEED_RELEASE_BUTTON + LoggerKeys.Record.MINE);

            Activity activity = MomoKit.getTopActivity();
            BasePublishUtil.checkDraftBeforePublish(activity, new Handler.Callback() {
                @Override
                public boolean handleMessage(@NonNull Message msg) {
                    if (activity != null) {
                        PublishLuaClickManager.gotoTakeMediaToVideoRecord(activity, LoggerKeys.Record.VIDEO
                                , PageStepHelper.INSTANCE.getFeedUpStepConfig().getLogSource(), null);
                    }

                    return false;
                }
            });
        });
        return publishFeedButton;
    }

    @LuaApiUsed
    public LuaValue[] setCustomStyle(LuaValue[] vars) {
        PublishFeedButtonKt.customStyle(publishFeedButton, CachePublishType.PageFrom.Lua);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] resetPageState(LuaValue[] vars) {
        publishFeedButton.resetPageState(true);
        return  null;
    }

    @LuaApiUsed
    public LuaValue[] release(LuaValue[] vars) {
        publishFeedButton.release();
        return null;
    }

}
