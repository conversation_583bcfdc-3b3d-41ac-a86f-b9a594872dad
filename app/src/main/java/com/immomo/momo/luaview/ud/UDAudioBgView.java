package com.immomo.momo.luaview.ud;

import androidx.annotation.NonNull;

import com.immomo.mls.fun.ud.view.UDView;
import com.immomo.momo.luaview.weight.UniverseAudioBgView;
import com.immomo.momo.util.StringUtils;

import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

/**
 * author: hongming.wei
 * data: 2023/12/15
 */
@LuaApiUsed
public class UDAudioBgView<V extends UniverseAudioBgView> extends UDView<V> {

    public static final String LUA_CLASS_NAME = "audioBgView";

    public static final String[] methods = {
            "bindData",
            "showBgView",
            "audioBgDestroy"
    };

    @LuaApiUsed
    protected UDAudioBgView(long L, LuaValue[] v) {
        super(L, v);
    }

    @NonNull
    @Override
    @LuaApiUsed
    protected V newView(@NonNull LuaValue[] init) {
        return (V) new UniverseAudioBgView(getContext());
    }



    @LuaApiUsed
    public LuaValue[] bindData(LuaValue[] values) {
        if (values == null || values.length == 0){
            return null;
        }

        String filePath = values[0].toJavaString();
        long duration = 0;
        if (values.length >= 2) {
            duration = values[1].toLong();
        }
        if (StringUtils.isNotEmpty(filePath)) {
            getView().bindData(filePath, duration);
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] showBgView(LuaValue[] values) {
        getView().showBgView();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] audioBgDestroy(LuaValue[] values) {
        getView().destroy();
        return null;
    }


}
