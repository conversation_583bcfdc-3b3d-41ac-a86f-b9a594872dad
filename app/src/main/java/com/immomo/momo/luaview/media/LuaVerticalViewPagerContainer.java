package com.immomo.momo.luaview.media;

import android.content.Context;
import android.graphics.Canvas;

import com.immomo.mls.fun.weight.BorderRadiusFrameLayout;
import com.immomo.mls.util.LuaViewUtil;

import androidx.annotation.NonNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/9/9.
 * LuaViewPager替换成LuaVerticalViewPager
 */
public class LuaVerticalViewPagerContainer extends BorderRadiusFrameLayout implements IVerticalViewPager<UDVerticalViewPager> {
    private UDVerticalViewPager udViewPager;
    private LuaVerticalViewPager luaVerticalViewPager;

    public LuaVerticalViewPagerContainer(@NonNull Context context, UDVerticalViewPager userdata) {
        super(context);
        udViewPager = userdata;
        luaVerticalViewPager = new LuaVerticalViewPager(context, (UDVerticalViewPager) userdata);
        addView(luaVerticalViewPager, LuaViewUtil.createRelativeLayoutParamsMM());
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        getUserdata().measureOverLayout(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        getUserdata().layoutOverLayout(left, top, right, bottom);
    }

    @Override
    protected void dispatchDraw(Canvas canvas) {
        super.dispatchDraw(canvas);
        getUserdata().drawOverLayout(canvas);
    }

    @Override
    public UDVerticalViewPager getUserdata() {
        return udViewPager;
    }

    @Override
    public LuaVerticalViewPager getViewPager() {
        return luaVerticalViewPager;
    }

    @Override
    public void setViewLifeCycleCallback(ViewLifeCycleCallback cycleCallback) {
    }

    @Override
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        // animHelper.startIfNeed();
        luaVerticalViewPager.callOnAttachedToWindow();
    }

    @Override
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        // animHelper.stopAnim();
        luaVerticalViewPager.callOnDetachedFromWindow();
    }

    @Override
    public boolean isAutoScroll() {
        return luaVerticalViewPager.isAutoScroll();
    }

    @Override
    public void setAutoScroll(boolean autoScroll) {
        luaVerticalViewPager.setAutoScroll(autoScroll);
    }

    @Override
    public boolean isRepeat() {
        return luaVerticalViewPager.isRepeat();
    }

    @Override
    public void setRepeat(boolean repeat) {
        luaVerticalViewPager.setRepeat(repeat);
    }

    @Override
    public float getFrameInterval() {
        return luaVerticalViewPager.getFrameInterval();
    }

    @Override
    public void setFrameInterval(float frameInterval) {
        luaVerticalViewPager.setFrameInterval(frameInterval);
    }

    @Override
    public void setPageIndicator(PageIndicator pageIndicator) {
        luaVerticalViewPager.setPageIndicator(pageIndicator);
    }

    @Override
    public PageIndicator getPageIndicator() {
        return luaVerticalViewPager.getPageIndicator();
    }

    @Override
    public void addCallback(Callback c) {
        luaVerticalViewPager.addCallback(c);
    }

    @Override
    public void removeCallback(Callback c) {
        luaVerticalViewPager.removeCallback(c);
    }

}
