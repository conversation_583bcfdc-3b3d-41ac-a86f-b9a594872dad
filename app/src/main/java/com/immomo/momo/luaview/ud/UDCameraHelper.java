package com.immomo.momo.luaview.ud;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;

import com.immomo.framework.SPKeys;
import com.immomo.framework.imageloader.ImageType;
import com.immomo.framework.imjson.client.util.UniqueIDentity;
import com.immomo.mls.LuaViewManager;
import com.immomo.mls.MLSEngine;
import com.immomo.mls.fun.ud.UDArray;
import com.immomo.mls.fun.ud.UDMap;
import com.immomo.mmutil.task.ThreadUtils;
import com.immomo.momo.Configs;
import com.immomo.momo.MomoKit;
import com.immomo.momo.album.util.AlbumConstant;
import com.immomo.momo.album.util.AlbumHelper;
import com.immomo.momo.businessmodel.statistics.PageStepHelper;
import com.immomo.momo.dynamicresources.ResourceChecker;
import com.immomo.momo.dynamicresources.ResourceLoadCallback;
import com.immomo.momo.feed.bean.BasePublishConstant;
import com.immomo.momo.globalevent.GlobalEventManager;
import com.immomo.momo.imagefactory.docorate.ImageDecorateActivity;
import com.immomo.momo.moment.MomentConstants;
import com.immomo.momo.moment.activity.AlbumHomeFragment;
import com.immomo.momo.moment.activity.VideoRecordAndEditActivity;
import com.immomo.momo.moment.mvp.VideoInfoTransBean;
import com.immomo.momo.multpic.entity.Photo;
import com.immomo.momo.multpic.entity.PhotoDirectory;
import com.immomo.momo.multpic.utils.MediaStoreHelper;
import com.immomo.momo.util.ImageUtil;
import com.immomo.momo.util.MediaFileUtil;
import com.immomo.momo.util.StringUtils;

import org.luaj.vm2.Globals;
import org.luaj.vm2.JavaUserdata;
import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaString;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author       :   <EMAIL>
 * Date         :   2019/5/15
 * Time         :   上午10:53
 * Description  :   选取相册 或 拍照
 */

@LuaApiUsed
public class UDCameraHelper extends JavaUserdata {

    public static final String LUA_CLASS_NAME = "HonorCameraHelper";

    public static final String[] methods = {
            "openAlbum",
            "openCamera",
            "takePhoto",
            "loadPhotoAddressPunchLibrary",
            "setDealWithImageCallback",
            "openAddressPunchAlbum",
            "openAlbumWithLimit",
            "openPhotoAlbum",
            "setDealWithAlbumCallback"
    };

    public static final String PICTURE_RESULT_LOCAL_PATH_EVENT = "picture_result_local_path_event";
    public static final String PICTURE_RESULT_LOCAL_PATH = "picture_result_local_path";
    public static final String ALBUM_LOCAL_PATH_EVENT = "album_local_path_event";

    public static final String PICTURE_RESULT_MULTI_IMAGE_SELECT_ENVENT = "picture_result_multi_image_select_envent";
    public static final String PICTURE_RESULT_MULTI_IMAGE_LOADING_EVENT = "picture_result_multi_image_loading_event";
    public static final String PICTURE_RESULT_MULTI_IMAGE_SELECT_LIST = "picture_result_multi_image_select_list";
    public static final String KEY_NAIL_IMAGE = "nailImage";
    public static final String KEY_ORIGIN_IMAGE = "originImage";
    public static final String KEY_ID = "id";
    public static final String KEY_MAP_PATH = "path";
    public static final String KEY_MAP_TYPE = "mediaType";
    public static final String KEY_MAP_HEIGHT = "height";
    public static final String KEY_MAP_WIDTH = "width";
    public static final String KEY_MAP_DURATION = "duration";
    public static final String KEY_MAP_LENGTH = "length";
    public static final String KEY_MAP_COVER = "cover";
    public static final String KEY_MAP_VIDEO = "video";
    private String currentSource = "";
    private LuaFunction pictureLocalUrlCallBack;
    private LuaFunction albumLocalUrlCallBack;
    private GlobalEventManager.Subscriber subscriber;

    @LuaApiUsed
    protected UDCameraHelper(long L, LuaValue[] v) {
        super(L, v);
        initBridge();
    }

    @LuaApiUsed
    public UDCameraHelper(Globals g, Object jud) {
        super(g, jud);
        initBridge();
    }

    @Override
    protected void __onLuaGc() {
        super.__onLuaGc();
        GlobalEventManager.getInstance().unregister(subscriber, "native");

    }

    //<editor-fold desc="API">
    @LuaApiUsed
    public LuaValue[] openAlbum(LuaValue[] values) {
        boolean isBlockBusiness = values.length != 0 ? values[0].toBoolean() : true;
        selectPictureActivity(VideoInfoTransBean.STATE_CHOOSE_MEDIA, isBlockBusiness);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] openCamera(LuaValue[] values) {
        boolean isBlockBusiness = values.length != 0 ? values[0].toBoolean() : true;
        selectPictureActivity(VideoInfoTransBean.STATE_DEFAULT_RECORD, isBlockBusiness);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setDealWithImageCallback(LuaValue[] values) {
        if (pictureLocalUrlCallBack != null)
            pictureLocalUrlCallBack.destroy();
        if (values.length >= 1)
            pictureLocalUrlCallBack = values[0].toLuaFunction();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setDealWithAlbumCallback(LuaValue[] values) {
        if (albumLocalUrlCallBack != null)
            albumLocalUrlCallBack.destroy();
        if (values.length >= 1)
            albumLocalUrlCallBack = values[0].toLuaFunction();
        return null;
    }

    /**
     * 打开拍照，返回图片和缩略图，不裁剪
     *
     * @param values
     * @return
     */
    @LuaApiUsed
    public LuaValue[] takePhoto(LuaValue[] values) {
        VideoInfoTransBean videoRecordInfo = getCommonVideoRecordInfo(VideoInfoTransBean.STATE_DEFAULT_RECORD, true);
        videoRecordInfo.luaInvokeMultiImage = true;
        videoRecordInfo.luaInvokeActivity = false;
        videoRecordInfo.extraBundle = null;
        startRecordForResult(MLSEngine.getContext(), videoRecordInfo, 303);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] loadPhotoAddressPunchLibrary(LuaValue[] values) {
        int maxCount = values[0].toInt();
        LuaFunction callback = values[1].toLuaFunction();
        Bundle mediaStoreArgs = new Bundle();
        mediaStoreArgs.putBoolean(MediaStoreHelper.KEY_GIF_ENABLE, false);
        FragmentActivity activity = (FragmentActivity) getContext();
        MediaStoreHelper.getPhotoDirs(activity, mediaStoreArgs, new MediaStoreHelper.PhotosResultCallback() {
            @Override
            public void onResultCallback(List<PhotoDirectory> directories) {
                if (directories != null && directories.size() > 0) {
                    MediaStoreHelper.cancelPhotoDirs(activity);
                    ArrayList<Map> photos = new ArrayList<>();
                    int count = Math.min(maxCount, directories.get(0).getPhotos().size());
                    for (Photo p : directories.get(0).getPhotos().subList(0, count < 0 ? 0 : count)) {
                        photos.add(getPunchMap(String.valueOf(p.id), p.path));
                    }
                    callback.invoke(varargsOf(new UDArray(globals, photos)));
                }
            }
        });
        return null;
    }

    private HashMap getPunchMap(String picId, String path) {
        HashMap<String, String> map = getImageByPath(path);
        map.put(KEY_ID, picId);
        return map;
    }

    public Context getContext() {
        LuaViewManager m = (LuaViewManager) globals.getJavaUserdata();
        return m != null ? m.context : null;
    }

    public static void postMultImageSelectEvent2Lua(List<Photo> result) {
        if (result == null || result.isEmpty()) {
            return;
        }

        ThreadUtils.execute(ThreadUtils.TYPE_INNER, new Runnable() {
            @Override
            public void run() {
                postMultImageEvent2LuaInner(result);
            }
        });
    }

    private synchronized static void postMultImageEvent2LuaInner(List<Photo> result) {
        GlobalEventManager.getInstance().sendEvent(new GlobalEventManager.Event(UDCameraHelper.PICTURE_RESULT_MULTI_IMAGE_LOADING_EVENT).dst("lua").src("native"));
        List<Map> array = new ArrayList<>();
        String photoPath = "";
        for (int i = 0; i < result.size(); i++) {
            photoPath = result.get(i).getTempPath();
            HashMap<String, String> map = getImageByPath(photoPath);
            map.put(KEY_ID, String.valueOf(result.get(i).id));
            array.add(map);
        }
        HashMap hashMap = new HashMap();
        hashMap.put(UDCameraHelper.PICTURE_RESULT_MULTI_IMAGE_SELECT_LIST, array);
        GlobalEventManager.getInstance().sendEvent(new GlobalEventManager.Event(UDCameraHelper.PICTURE_RESULT_MULTI_IMAGE_SELECT_ENVENT).dst("native").src("native").msg(hashMap));
    }

    //</editor-fold>

    private void selectPictureActivity(int state) {
        selectPictureActivity(state, true);
    }

    private void selectPictureActivity(int state, boolean isBlockBusiness) {
        if (MomoKit.getCurrentUser() == null) {
            return;
        }

        VideoInfoTransBean videoRecordInfo = getCommonVideoRecordInfo(state, isBlockBusiness);

        startRecordForResult(MLSEngine.getContext(), videoRecordInfo, 303);
    }

    /**
     * 获取跳转拍照界面的参数
     *
     * @param state
     * @param isBlockBusiness
     * @return
     */
    private VideoInfoTransBean getCommonVideoRecordInfo(int state, boolean isBlockBusiness) {
        VideoInfoTransBean videoRecordInfo = new VideoInfoTransBean();
        videoRecordInfo.mediaType = AlbumConstant.MEDIA_TYPE_IMAGE;

        // 控制 选取相片  还是  拍照
        videoRecordInfo.state = state;

        //选取头像时 并可以编辑
        videoRecordInfo.mode = VideoInfoTransBean.MODE_STYLE_ONE;

        // 不可以编辑  并设置最多选取一张照片
        /*videoRecordInfo.mode = VideoInfoTransBean.MODE_BACK_RESULT_IMMEDIATLY;
        videoRecordInfo.maxSelectedCount = 1;*/

        // 控制 顶部三个 Tab  : 影集，相册，视频
        // videoRecordInfo.showAlbumTabs = AlbumHomeFragment.STATE_PICTURE_ALBUM | AlbumHomeFragment.STATE_ALBUM | AlbumHomeFragment.STATE_VIDEO;

        videoRecordInfo.minDuration = 3 * 1000L;
        videoRecordInfo.setMaxDuration(10 * 1000L);
        Bundle bundle = new Bundle();
        bundle.putInt(ImageDecorateActivity.EXTRA_ASPECT_Y, 1);
        bundle.putInt(ImageDecorateActivity.EXTRA_ASPECT_X, 1);
        bundle.putInt(ImageDecorateActivity.EXTAR_MIN_SIZE, Configs.IMAGE_CROP_DEFAULT_WIDTH);
        videoRecordInfo.extraBundle = bundle;
        videoRecordInfo.showTopic = false;
        videoRecordInfo.sendText = VideoInfoTransBean.BTN_TEXT_COMPLETE;
        videoRecordInfo.luaInvokeActivity = true;
        videoRecordInfo.blockBusiness = isBlockBusiness;

        return videoRecordInfo;
    }

    public static HashMap<String, String> getImageByPath(String photoPath) {
        File thumbFile = null;
        File file = new File(photoPath);
        if (!TextUtils.isEmpty(photoPath) && file.exists()) {
            String newAvatarName = UniqueIDentity.nextId();
            Bitmap largeBitmap = ImageUtil.decodeFile(file.getPath(), 480, 480);
            if (largeBitmap != null) {
                Bitmap bitmap = ImageUtil.getSquaredBitmap(largeBitmap, Configs.IMAGE_MIN_PHOTO_SIZE, false);
                thumbFile = MediaFileUtil.storeImage(newAvatarName, bitmap, ImageType.IMAGE_TYPE_ALBUM_SMALL, false);
                if (!bitmap.isRecycled()) {
                    bitmap.recycle();
                }
                if (!largeBitmap.isRecycled()) {
                    largeBitmap.recycle();
                }
            }
        }
        return getThumbMap(photoPath, thumbFile != null ? thumbFile.getAbsolutePath() : "");
    }

    private static HashMap<String, String> getThumbMap(String origin, String thumb) {
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put(KEY_ORIGIN_IMAGE, origin);
        hashMap.put(KEY_NAIL_IMAGE, thumb);
        return hashMap;
    }

    @LuaApiUsed
    public LuaValue[] openAddressPunchAlbum(LuaValue[] values) {
        int limitCnt = values.length != 0 ? values[0].toInt() : 0;
        VideoInfoTransBean videoRecordInfo = new VideoInfoTransBean();
        videoRecordInfo.maxSelectedCount = limitCnt;
        videoRecordInfo.state = VideoInfoTransBean.STATE_CHOOSE_MEDIA;
        videoRecordInfo.showTopic = true;
        videoRecordInfo.logKey = SPKeys.VideoInfoTrans.PUBLISH;
        videoRecordInfo.canChangeTopic = true;
        videoRecordInfo.from = PageStepHelper.INSTANCE.getFeedUpStepConfig().getLogSource();
        videoRecordInfo.mediaType = AlbumConstant.MEDIA_TYPE_IMAGE;
        videoRecordInfo.showTabs = AlbumHomeFragment.STATE_ALBUM;
        videoRecordInfo.sendText = VideoInfoTransBean.BTN_TEXT_COMPLETE;
        videoRecordInfo.luaInvokeMultiImage = true;
        startRecordForResult(MLSEngine.getContext(), videoRecordInfo, BasePublishConstant.RESULT_CODE_TAKE_MEDIA);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] openAlbumWithLimit(LuaValue[] values) {
        int limit = values.length > 0 ? values[0].toInt() : 0;

        VideoInfoTransBean videoRecordInfo = new VideoInfoTransBean();
        videoRecordInfo.maxSelectedCount = limit;
        videoRecordInfo.state = VideoInfoTransBean.STATE_CHOOSE_MEDIA;
        videoRecordInfo.showTopic = false;
        videoRecordInfo.mode = VideoInfoTransBean.MODE_BACK_RESULT_IMMEDIATLY;
        videoRecordInfo.mediaType = AlbumConstant.MEDIA_TYPE_IMAGE;
        videoRecordInfo.showTabs = AlbumHomeFragment.STATE_ALBUM;
        videoRecordInfo.sendText = VideoInfoTransBean.BTN_TEXT_COMPLETE;
        videoRecordInfo.luaInvokeMultiImage = true;
        startRecordForResult(MLSEngine.getContext(), videoRecordInfo, BasePublishConstant.RESULT_CODE_TAKE_MEDIA);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] openPhotoAlbum(LuaValue[] values) {
        VideoInfoTransBean videoRecordInfo = new VideoInfoTransBean();
        int state = values.length > 0 ? values[0].toInt() : 0;
        currentSource = values.length > 1 ? values[1].toJavaString() : "";
        videoRecordInfo.maxSelectedCount = 1;
        if (state == 0) {
            videoRecordInfo.state = VideoInfoTransBean.STATE_CHOOSE_MEDIA;
        } else if (state == 1) {
            videoRecordInfo.state = VideoInfoTransBean.STATE_DEFAULT_RECORD;
        } else if (state == 2) {
            videoRecordInfo.state = VideoInfoTransBean.STATE_ADVANCED_RECORD;
        }
        videoRecordInfo.showTopic = true;
        videoRecordInfo.logKey = SPKeys.VideoInfoTrans.PUBLISH;
        videoRecordInfo.canChangeTopic = true;
        videoRecordInfo.from = PageStepHelper.INSTANCE.getFeedUpStepConfig().getLogSource();
        videoRecordInfo.mediaType = AlbumConstant.MEDIA_TYPE_MIXED;
        videoRecordInfo.showTabs = AlbumHomeFragment.STATE_ALBUM;
        videoRecordInfo.sendText = VideoInfoTransBean.BTN_TEXT_COMPLETE;
        videoRecordInfo.luaInvokeActivity = true;
        startRecordForResult(MLSEngine.getContext(), videoRecordInfo, BasePublishConstant.RESULT_CODE_TAKE_MEDIA);
        return null;
    }

    private void initBridge() {
        if (subscriber != null)
            return;

        subscriber = new GlobalEventManager.Subscriber() {
            @Override
            public void onGlobalEventReceived(GlobalEventManager.Event event) {
                if (event == null) {
                    return;
                }

                if (PICTURE_RESULT_MULTI_IMAGE_SELECT_ENVENT.equals(event.getName())) {
                    Object multList = event.getMsg().get(PICTURE_RESULT_MULTI_IMAGE_SELECT_LIST);
                    if (pictureLocalUrlCallBack != null && multList instanceof Collection) {
                        pictureLocalUrlCallBack.invoke(varargsOf(new UDArray(globals, (Collection) multList)));
                    }
                } else if (PICTURE_RESULT_LOCAL_PATH_EVENT.equals(event.getName())) {
                    String pictureLocalUrl = (String) event.getMsg().get(PICTURE_RESULT_LOCAL_PATH);
                    if (!TextUtils.isEmpty(pictureLocalUrl) && pictureLocalUrlCallBack != null) {
                        pictureLocalUrlCallBack.invoke(varargsOf(LuaString.valueOf(pictureLocalUrl)));
                    }
                }else if (ALBUM_LOCAL_PATH_EVENT.equals(event.getName())) {
                    Map map = event.getMsg();
                    if (map!= null && !map.isEmpty() && albumLocalUrlCallBack != null) {
                        albumLocalUrlCallBack.invoke(varargsOf(new UDMap(globals,map), LuaString.valueOf(currentSource)));
                    }
                }
            }
        };
        GlobalEventManager.getInstance().register(subscriber, "native");
    }

    public static void postEvent2Lua(String photoPath) {
        if (StringUtils.isEmpty(photoPath))
            return;

        File file = new File(photoPath);
        if (file != null && file.exists()) {
            String newAvatarName = UniqueIDentity.nextId();
            Bitmap largeBitmap = ImageUtil.decodeFile(file.getPath());
            if (largeBitmap != null) {
                MediaFileUtil.storeImage(newAvatarName, largeBitmap, ImageType.IMAGE_TYPE_ALBUM_LARGE, true);
                largeBitmap.recycle();

                HashMap hashMap = new HashMap();
                hashMap.put(UDCameraHelper.PICTURE_RESULT_LOCAL_PATH, photoPath);
                GlobalEventManager.getInstance().sendEvent(new GlobalEventManager.Event(UDCameraHelper.PICTURE_RESULT_LOCAL_PATH_EVENT).dst("native").src("native").msg(hashMap));
            }
        }
    }

    public static void postEvent(Map<String, Object> map) {
        if (map == null || map.isEmpty())
            return;
        GlobalEventManager.getInstance().sendEvent(new GlobalEventManager.Event(UDCameraHelper.ALBUM_LOCAL_PATH_EVENT).dst("native").src("native").msg(map));
    }

    private static boolean startRecordForResult(@NonNull final Context context, final VideoInfoTransBean info, final int requestCode) {
        if (context == null || info == null)
            return false;

        boolean result = (ResourceChecker.needBlockService(ResourceChecker.BUSINESS_TYPE_PHOTO, ResourceChecker.TYPE_RECORDER, new ResourceLoadCallback() {
            @Override
            public void onSuccess() {
                startRecordForResult(context, info, requestCode);
            }

            @Override
            public void onFailed(String errorMsg) {

            }

            @Override
            public void onProcess(int percent, double speed) {

            }

            @Override
            public void onProcessDialogClose() {

            }
        }));

        if (result)
            return false;

        AlbumHelper.isOnlyAlbum(info);
        Intent intent = new Intent(context, VideoRecordAndEditActivity.class);
        intent.putExtra(MomentConstants.KEY_RECORD_FROM, info.from);
//        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        Bundle bundle = new Bundle();
        bundle.putParcelable(MomentConstants.EXTRA_KEY_VIDEO_TRANS_INFO, info);
        bundle.putInt(MomentConstants.EXTRA_KEY_VIDEO_STATE, info.state);
        intent.putExtras(bundle);

        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

        if (context instanceof Activity) {
            ((Activity) context).startActivityForResult(intent, requestCode);
        } else {
            context.startActivity(intent);
        }
//        addAnimationInFromBottom(context);
        return true;
    }

}