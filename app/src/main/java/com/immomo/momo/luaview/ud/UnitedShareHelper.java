package com.immomo.momo.luaview.ud;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.cosmos.mdlog.MDLog;
import com.immomo.android.module.feedlist.data.api.mapper.theme.FeedListThemeMapperKt;
import com.immomo.android.module.feedlist.data.api.response.theme.FeedListTheme;
import com.immomo.android.module.feedlist.domain.model.style.recommend.BaseRecommendLiveInfo;
import com.immomo.android.module.feedlist.domain.model.style.recommend.RecommendLivePicsInfoModel;
import com.immomo.android.module.feedlist.domain.model.style.recommend.RecommendLivingMicroVideoModel;
import com.immomo.android.router.share.ShareDialogConfig;
import com.immomo.android.router.share.ShareRouter;
import com.immomo.android.router.share.model.PageConfig;
import com.immomo.android.router.share.model.ShareData;
import com.immomo.mls.LuaViewManager;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.fun.ud.UDMap;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoKit;
import com.immomo.momo.publish.receiver.PublishReceiver;
import com.immomo.momo.share2.listeners.RecommendLivingShareClickListener;
import com.immomo.momo.share2.listeners.ShareListenerAdapter;
import com.immomo.momo.share3.data.CustomApp;
import com.immomo.momo.util.BroadcastHelper;
import com.immomo.momo.util.IMShareHelper;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.luaj.vm2.Globals;
import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaString;
import org.luaj.vm2.LuaValue;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import info.xudshen.android.appasm.AppAsm;

/**
 * Created by chenxin on 2019-10-15.
 */
@LuaClass
public class UnitedShareHelper {

    public static final String LUA_CLASS_NAME = "UnitedShareHelper";
    private static final String TAG = "UnitSharedHelper_";


    private LuaFunction shareCallback;
    private Globals globals;
    private BroadcastReceiver publishReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                if (PublishReceiver.ACTION.endsWith(intent.getAction())) {
                    String app = intent.getStringExtra(PublishReceiver.KEY_CALLBACK_APP);
                    int status = intent.getIntExtra(PublishReceiver.KEY_CALLBACK_STATUS, -1);
                    String message = intent.getStringExtra(PublishReceiver.KEY_CALLBACK_MESSAGE);
                    String extra = intent.getStringExtra(PublishReceiver.KEY_CALLBACK_EXTRA);
                    JSONObject obj = new JSONObject();
                    obj.put("app", app);
                    obj.put("status", status);
                    obj.put("message", message);
                    obj.put("extra", extra);
                    if (shareCallback != null) {
                        shareCallback.invoke(LuaValue.varargsOf(LuaString.valueOf(obj.toString())));
                    }
                    MDLog.i(LogTag.CommonShare.CommonShare, "分享回调 -> " + status);
                }
            } catch (JSONException e) {
                MDLog.printErrStackTrace(TAG, e);
            }
        }
    };

    public UnitedShareHelper(Globals globals) {
        this.globals = globals;
        BroadcastHelper.registerBroadcast(getContext(),
                publishReceiver,
                PublishReceiver.ACTION
        );
    }

    public void __onLuaGc() {
        if (shareCallback != null) {
            shareCallback.destroy();
        }
        BroadcastHelper.unregisterBroadcast(getContext(), publishReceiver);
    }

    @LuaBridge
    public void showSharePanel(LuaValue[] values) {
        UDMap map = values.length != 0 ? (UDMap) values[0].toUserdata() : null;
        shareCallback = values.length >= 1 ? values[1].toLuaFunction() : null;
        if (map == null) {
            return;
        }

        try {
            String from_type = (String) map.getMap().get("from_type");
            String scene_id = (String) map.getMap().get("scene_id");
            String feed_pic = (String) map.getMap().get("feed_pic");
            String extra = (String) map.getMap().get("extra");
            String ignore_tip = (String) map.getMap().get("ignore_tip");
            String callBack = (String) map.getMap().get("callback");
            String tip_name = (String) map.getMap().get("tip_name");
            boolean disableNet = false;
            if (map.getMap().containsKey("disableNet")) {
                disableNet = (boolean) map.getMap().get("disableNet");
            }

            boolean supportDark = false;
            if (map.getMap().containsKey("supportDark")) {
                supportDark = (boolean) map.getMap().get("supportDark");
            }

            JSONArray jsonArray = new JSONArray(map.getMap().get("apps").toString());
            List<String> apps = new ArrayList<>();
            if (jsonArray != null && jsonArray.length() > 0) {
                for (int i = 0; i < jsonArray.length(); i++) {
                    String app = (String) jsonArray.get(i);
                    if (!TextUtils.isEmpty(app)) {
                        apps.add(app);
                    }
                }
            }


            ShareData shareParams = new ShareData();
            shareParams.fromType = from_type;
            shareParams.sceneId = scene_id;
            shareParams.feedPic = feed_pic;
            shareParams.extra = extra;
            shareParams.ignoreTip = ignore_tip;
            shareParams.callback = callBack;
            shareParams.shareDialogMsg = "你将把" + (TextUtils.isEmpty(tip_name) ? "内容" : tip_name) + "分享给 %s?";
            shareParams.supportDark = supportDark;

            ShareListenerAdapter clickListener = new ShareListenerAdapter() {
                @Override
                public void onClick(String appName) {
                    try {
                        JSONObject obj = new JSONObject();
                        obj.put("platformString", appName);
                        if (shareCallback != null) {
                            shareCallback.invoke(LuaValue.varargsOf(LuaString.valueOf(obj.toString())));
                        }
                    } catch (Exception e) {
                        MDLog.printErrStackTrace(TAG, e);
                    }
                }
            };
            ShareDialogConfig config = new ShareDialogConfig.Builder(getContext())
                    .mShareListener(clickListener)
                    .useCommonShare(true)
                    .mShareData(shareParams)
                    .mPageConfig(new PageConfig.Builder()
                            .apps(apps)
                            .supportDark(supportDark)
                            .build())
                    .loadShareAppFromServer(!disableNet)
                    .build();
            AppAsm.getRouter(ShareRouter.class).showShareDialog(config);

        } catch (JSONException e) {
            MDLog.printErrStackTrace(TAG, e);
        }
    }

    @LuaBridge
    public void shareToIMWithParams(LuaValue[] values) {
        UDMap map = values.length != 0 ? (UDMap) values[0].toUserdata() : null;
        shareCallback = values.length >= 1 ? values[1].toLuaFunction() : null;

        IMShareHelper.IMShareCallback callback = new IMShareHelper.IMShareCallback() {
            @Override
            public void onSuccess(String result) {
                if (shareCallback != null) {
                    shareCallback.invoke(LuaValue.varargsOf(LuaString.valueOf(result)));
                }
            }
        };
        if (map == null) {
            MDLog.e(TAG, "the params from lua is null");
            return;
        }
        String from_type = (String) map.getMap().get("fromType");
        String scene_id = (String) map.getMap().get("sceneId");
        String extra = (String) map.getMap().get("extra");
        String businessName = (String) map.getMap().get("businessName");
        boolean showContacts = (int) map.getMap().get("showContacts") == 1;
        String sendType = (String) map.getMap().get("sendType");
        String to = (String) map.getMap().get("to");
        String msgType = (String) map.getMap().get("msgType");
        String toName = (String) map.getMap().get("toName");
        IMShareHelper.shareToIMWithParams(getContext(), callback, from_type,
                scene_id, extra, businessName,
                showContacts, sendType,
                to, msgType, toName);
    }

    @LuaBridge
    public void showShareAndFuncPanel(LuaValue[] values) {
        UDMap udMap = values.length != 0 ? (UDMap) values[0].toUserdata() : null;
        shareCallback = values.length >= 1 ? values[1].toLuaFunction() : null;
        if (udMap == null) {
            return;
        }

        try {
            Map map = udMap.getMap();
            String from_type = (String) map.get("from_type");
            String scene_id = (String) map.get("scene_id");
            String feed_pic = (String) map.get("feed_pic");
            String extra = (String) map.get("extra");
            String ignore_tip = (String) map.get("ignore_tip");
            String callBack = (String) map.get("callback");
            String tip_name = (String) map.get("tip_name");
            boolean supportDark = false;
            if (map.containsKey("supportDark")) {
                supportDark = (boolean) map.get("supportDark");
            }
            boolean disableNet = false;
            if (map.containsKey("disableNet")) {
                disableNet = (boolean) map.get("disableNet");
            }
            //分享列表,lua层传Array
            List<String> appList = (List<String>) map.get("apps");
            if (appList == null) {
                appList = new ArrayList<>();
            }

            //遍历功能区，lua层传Array<Map<String,String>>
            List<CustomApp> customAppList = new ArrayList<>();
            List<Map<String, String>> funcList = (List) map.get("funcs");
            if (funcList != null && funcList.size() > 0) {
                CustomApp item;
                for (Map<String, String> mapItem : funcList) {
                    item = new CustomApp();
                    item.app = mapItem.get("platformString");
                    item.appName = mapItem.get("title");
                    item.icon = mapItem.get("icon");
                    item.darkIcon = mapItem.get("darkIcon");
                    customAppList.add(item);
                }
            }

            ShareData shareParams = new ShareData();
            shareParams.fromType = from_type;
            shareParams.sceneId = scene_id;
            shareParams.feedPic = feed_pic;
            shareParams.extra = extra;
            shareParams.ignoreTip = ignore_tip;
            shareParams.callback = callBack;
            shareParams.shareDialogMsg = "你将把" + (TextUtils.isEmpty(tip_name) ? "内容" : tip_name) + "分享给 %s?";
            shareParams.supportDark = supportDark;

            ShareListenerAdapter clickListener = new ShareListenerAdapter() {
                @Override
                public void onClick(String appName) {
                    super.onClick(appName);
                    try {
                        JSONObject obj = new JSONObject();
                        obj.put("platformString", appName);
                        if (shareCallback != null) {
                            shareCallback.invoke(LuaValue.varargsOf(LuaString.valueOf(obj.toString())));
                        }
                    } catch (Exception e) {
                        MDLog.printErrStackTrace(TAG, e);
                    }
                }
            };

            ShareDialogConfig config = new ShareDialogConfig.Builder(getContext())
                    .mShareListener(clickListener)
                    .useCommonShare(true)
                    .mShareData(shareParams)
                    .mPageConfig(new PageConfig.Builder()
                            .apps(appList)
                            .customApps(customAppList)
                            .supportDark(supportDark)
                            .build())
                    .loadShareAppFromServer(!disableNet)
                    .build();
            AppAsm.getRouter(ShareRouter.class).showShareDialog(config);

        } catch (Exception e) {
            MDLog.printErrStackTrace(TAG, e);
        }
    }

    @LuaBridge
    public void showShareAndFuncPanelByLiving(LuaValue[] values) {
        UDMap udMap = values.length != 0 ? (UDMap) values[0].toUserdata() : null;
        shareCallback = values.length >= 1 ? values[1].toLuaFunction() : null;
        if (udMap == null) {
            return;
        }
        Map map = udMap.getMap();
        com.alibaba.fastjson.JSONObject feedJson = (com.alibaba.fastjson.JSONObject) map.get("feed");
        try {
            FeedListTheme<?> theme = FeedListTheme.Companion.getMoshi().adapter(FeedListTheme.class).fromJson(String.valueOf(feedJson));
            BaseRecommendLiveInfo<?> feed = (BaseRecommendLiveInfo) FeedListThemeMapperKt.parseFeedTheme2Model(theme);
            RecommendLivingShareClickListener shareClickListener = new RecommendLivingShareClickListener(MomoKit.getTopActivity()) {
                @Override
                public void onClick(String appName) {
                    super.onClick(appName);
                    try {
                        JSONObject obj = new JSONObject();
                        obj.put("platformString", appName);
                        if (shareCallback != null) {
                            shareCallback.invoke(LuaValue.varargsOf(LuaString.valueOf(obj.toString())));
                        }
                    } catch (Exception e) {
                        MDLog.printErrStackTrace(TAG, e);
                    }
                }
            };
            if (feed instanceof RecommendLivingMicroVideoModel) {
                shareClickListener.setFeed((RecommendLivingMicroVideoModel) feed);
            } else if (feed instanceof RecommendLivePicsInfoModel) {
                shareClickListener.setFeed((RecommendLivePicsInfoModel) feed);
            }
            //分享列表,lua层传Array
            List<String> appList = (List<String>) map.get("apps");
            if (appList == null) {
                appList = new ArrayList<>();
            }
            //遍历功能区，lua层传Array<Map<String,String>>
            List<CustomApp> customAppList = new ArrayList<>();
            List<Map<String, String>> funcList = (List) map.get("funcs");
            if (funcList != null && funcList.size() > 0) {
                CustomApp item;
                for (Map<String, String> mapItem : funcList) {
                    item = new CustomApp();
                    item.app = mapItem.get("platformString");
                    item.appName = mapItem.get("title");
                    item.icon = mapItem.get("icon");
                    item.darkIcon = mapItem.get("darkIcon");
                    customAppList.add(item);
                }
            }
            ShareDialogConfig config = new ShareDialogConfig.Builder(MomoKit.getTopActivity())
                    .mShareListener(shareClickListener)
                    .mPageConfig(new PageConfig.Builder().apps(appList).supportDark(true).customApps(customAppList).build())
                    .build();
            AppAsm.getRouter(ShareRouter.class).showShareDialog(config);
        } catch (IOException e) {
            MDLog.printErrStackTrace(TAG, e);
        }


    }

    @LuaBridge
    public void closeSharePanel(LuaValue[] values) {
        //todo @chenxin 关闭分享面板
    }

    public Context getContext() {
        LuaViewManager m = (LuaViewManager) globals.getJavaUserdata();
        return m != null ? m.context : null;
    }

}
