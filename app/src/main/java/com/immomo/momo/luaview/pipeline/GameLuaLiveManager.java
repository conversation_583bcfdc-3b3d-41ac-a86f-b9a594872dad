package com.immomo.momo.luaview.pipeline;

import android.Manifest;
import android.app.Activity;
import android.content.DialogInterface;
import android.os.Build;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.view.ViewGroup;

import com.core.glcore.cv.MMCVInfo;
import com.cosmos.mdlog.MDLog;
import com.immomo.android.module.newgame.views.facev2.bean.BeautyFunctionUtil;
import com.immomo.android.module.newgame.views.facev2.bean.PublishSettings;
import com.immomo.android.module.newgame.views.facev2.bean.UserBeautyConfig;
import com.immomo.framework.utils.UIUtils;
import com.immomo.game.faceV2.FaceTagsBean;
import com.immomo.game.faceV2.FaceTagsManager;
import com.immomo.game.faceV2.IPublishSettingsable;
import com.immomo.game.faceV2.view.FaceSettingDialogV2;
import com.immomo.game.view.GameTextureOutlineProvider;
import com.immomo.ijkConferenceStreamer;
import com.immomo.mls.util.DimenUtil;
import com.immomo.mls.utils.MainThreadExecutor;
import com.immomo.mmutil.BaseDeviceUtils;
import com.immomo.mmutil.StringUtils;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.molive.foundation.eventcenter.event.FilterDownloadCompleteEvent;
import com.immomo.molive.foundation.eventcenter.eventsubscriber.FilterDownloadCompleteSubscriber;
import com.immomo.molive.gui.common.filter.MLAdjustFilter;
import com.immomo.molive.media.ext.input.common.Filter;
import com.immomo.molive.media.ext.input.common.FilterDownloadHelper;
import com.immomo.momo.Configs;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoApplicationEvent;
import com.immomo.momo.MomoKit;
import com.immomo.momo.dynamicresources.DynamicResourceConstants;
import com.immomo.momo.dynamicresources.DynamicResourceManager;
import com.immomo.momo.dynamicresources.ResourceChecker;
import com.immomo.momo.dynamicresources.ResourceLoadCallback;
import com.immomo.momo.luaview.LuaViewActivity;
import com.immomo.momo.luaview.pipeline.view.GameLuaLivePreviewDialog;
import com.immomo.momo.permission.PermissionListener;
import com.momo.mcamera.mask.MaskModel;
import com.momo.mcamera.mask.facewarp.FaceBeautyID;

import org.json.JSONException;
import org.json.JSONObject;
import org.luaj.vm2.LuaValue;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;

import static android.view.ViewGroup.LayoutParams.MATCH_PARENT;

/**
 * Created by wang.lichen on 2019-09-11.
 */
public class GameLuaLiveManager implements MomoApplicationEvent.ApplicationEventListener {

    private WeakReference<Activity> mContextRef;
    private FaceSettingDialogV2 mFaceSettingDialogV2;
    private GameLuaLivePreviewDialog mGameLuaLivePreviewDialog;
    private MLAdjustFilter mMLAdjustFilter;
    public ArrayList<String> faceDetecModelPath = new ArrayList<>();
    private HashMap<Integer, ViewInfo> viewInfoMaps = new HashMap<>();
    private UDIjkConferenceStreamer mUDIjkConferenceStreamer;
    private FilterDownloadCompleteSubscriber mFilterDownloadCompleteSubscriber;

    public GameLuaLiveManager(Activity activity){
        this.mContextRef = new WeakReference<>(activity);
    }

    public ijkConferenceStreamer getmMediaStreamer() {
        if(mUDIjkConferenceStreamer != null){
            return mUDIjkConferenceStreamer.mMediaStreamer;
        }
        return null;
    }

    public void setmMLAdjustFilter(MLAdjustFilter mMLAdjustFilter) {
        this.mMLAdjustFilter = mMLAdjustFilter;
    }

    public void setUDIJKConferenceStreamer(UDIjkConferenceStreamer udIjkConferenceStreamer) {
        this.mUDIjkConferenceStreamer = udIjkConferenceStreamer;
    }

    private Activity getActivity(){
        if(mContextRef.get() != null){
            return mContextRef.get();
        }
        return MomoKit.getTopActivity();
    }

    public void release(){
        mContextRef.clear();
        mContextRef = null;
        mMLAdjustFilter = null;
    }

    /**
     * 切换摄像头
     * @return
     */
    public LuaValue[] switchCamera(){
        ijkConferenceStreamer mediaStreamer = getmMediaStreamer();
        if (mediaStreamer != null){
            mediaStreamer.switchCamera();
        }
        return null;
    }

    /**
     * 隐藏美颜、滤镜等人脸设置的视图
     */
    public void dimissFaceSettingViewV2(){
        if (mFaceSettingDialogV2 != null && mFaceSettingDialogV2.isShowing()){
            mFaceSettingDialogV2.dismiss();
        }
    }

    public void unRegisterFilterDownloadCompleteSubscriber(){
        //监听的事件取消掉
        if (mFilterDownloadCompleteSubscriber != null){
            mFilterDownloadCompleteSubscriber.unregister();
            mFilterDownloadCompleteSubscriber = null;
        }
    }

    public void loadNetFilterInfo(){
        mFilterDownloadCompleteSubscriber = new FilterDownloadCompleteSubscriber() {
            @Override
            public void onEventMainThread(FilterDownloadCompleteEvent param) {

                MDLog.i(LogTag.GameWolf.GameWolf,"onEventMainThread:lookup download succ");
                if (mFaceSettingDialogV2 != null && mFaceSettingDialogV2.isShowing()){
                    mFaceSettingDialogV2.updateLookupFilter();
                }
                if (mMLAdjustFilter != null){
                    PublishSettings publishSettings = PublishSettings.obtain(PublishSettings.KEY_OWNER_SETTINGS);
                    mMLAdjustFilter.selectFilter(Filter.getFilterRes(publishSettings.getFilterName()));
                    mMLAdjustFilter.setLookupIntensity(publishSettings.getFilterValue());
                }


                if (mFilterDownloadCompleteSubscriber != null){
                    mFilterDownloadCompleteSubscriber.unregister();
                    mFilterDownloadCompleteSubscriber = null;
                }
            }
        };
        mFilterDownloadCompleteSubscriber.registerSticky();
        FilterDownloadHelper.getInstance().loadFilterInfo();
    }
    /**
     * 展示美颜、滤镜等人脸设置的视图
     */
    public void showFaceSettingViewV2(){
        Activity activity = getActivity();
        if (activity != null){
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (mFaceSettingDialogV2 == null) {
                        initFaceSettingViewV2();
                    }
                    if (mFaceSettingDialogV2.isShowing()) {
                        return;
                    }
                    PublishSettings settings = PublishSettings.obtain(PublishSettings.KEY_OWNER_SETTINGS);
                    mFaceSettingDialogV2.setData(settings);
                    mFaceSettingDialogV2.show();
                }
            });
        }
    }
    /**
     * 初始化美颜设置面板
     */
    public void initFaceSettingViewV2(){
        mFaceSettingDialogV2 = new FaceSettingDialogV2(MomoKit.getTopActivity());
        mFaceSettingDialogV2.setDelegatePublishSettingsable(new IPublishSettingsable() {

            @Override
            public void setFaceBeautyValue(String id, float value) {
                if (mMLAdjustFilter != null){
                    mMLAdjustFilter.setFaceBeautyValue(id,value);
                }
            }

            @Override
            public void selectFilter(String name, float intensity) {
                if (mMLAdjustFilter != null){
                    mMLAdjustFilter.selectFilter(Filter.getFilterRes(name));
                    mMLAdjustFilter.setLookupIntensity(intensity);
                }
            }

            @Override
            public void setFilterIntensity(float intensity) {
                if (mMLAdjustFilter != null){
                    mMLAdjustFilter.setLookupIntensity(intensity);
                }
            }

            @Override
            public void switchSmoothFilter(boolean isOpen) {
                if (mMLAdjustFilter != null){
                    mMLAdjustFilter.switchSmoothFilter(isOpen);
                }
            }

            @Override
            public void changeDokiBeautyFilter(boolean isOpen) {
                if (mMLAdjustFilter != null){
                    mMLAdjustFilter.changeDokiBeautyFilter(isOpen);
                }
            }

            @Override
            public void changeOrigin(boolean isOpen) {
                mMLAdjustFilter.changeOrigin(isOpen);
            }
        });
        mFaceSettingDialogV2.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                if(mFaceSettingDialogV2 != null){
                    mFaceSettingDialogV2.dismiss();
                    mFaceSettingDialogV2 = null;
                }
            }
        });
    }
    public void resetFaceSettings() {
        if (!PublishSettings.isSettingExistInDisk(PublishSettings.KEY_OWNER_SETTINGS)){
            UserBeautyConfig defaultConfig = BeautyFunctionUtil.getDefaultConfig(MomoKit.getCurrentUser().isFemale());
            PublishSettings publishSettings = PublishSettings.obtain(PublishSettings.KEY_OWNER_SETTINGS);
            publishSettings.setSkinSmoothLevel(defaultConfig.getSmoothSkin());
            publishSettings.setSkinLightLevel(defaultConfig.getSkinWhitenAmount());
            publishSettings.setFaceEyeScale(defaultConfig.getEyeSize());
            publishSettings.setFaceThinScale(defaultConfig.getThinFace());
            publishSettings.setFaceWidthLevel(defaultConfig.getFaceWidth());
            publishSettings.setChinLengthLevel(defaultConfig.getChinLength());
            publishSettings.setNoseSizeLevel(defaultConfig.getNoseSize());
            publishSettings.setLipThicknessLevel(defaultConfig.getLipThickness());
            publishSettings.setFilterName(defaultConfig.getFilterName());
            publishSettings.setFilterValue(defaultConfig.getFilterValue());




            //1.判断是否支持doki美颜，是否强制使用doki美颜
            // 1.1 如果支持再判断默认使用doki还是旧版美颜
            // 1.2 如果不支持doki美颜,判断默认使用新版磨皮还是旧版磨皮
            if (BeautyFunctionUtil.isDokiBeautyForce() || BeautyFunctionUtil.isDokiBeautyEnable()) {
                publishSettings.setUseDokiBeauty(BeautyFunctionUtil.isDoki() ? 1 : 0);
            } else {
                publishSettings.setUseDokiBeauty(0);
                publishSettings.setUseNewSmooth(BeautyFunctionUtil.isNewSmooth() ? 1 : 0);
            }

            publishSettings.save();

            initBeautyWithUserConfig(PublishSettings.obtain(PublishSettings.KEY_OWNER_SETTINGS));
        }else{
            initBeautyWithUserConfig(PublishSettings.obtain(PublishSettings.KEY_OWNER_SETTINGS));
        }
    }

    /**
     * 打开预览时，使用之前配置的美颜数据
     * @param publishSettings
     */
    public void initBeautyWithUserConfig(PublishSettings publishSettings){
        if (publishSettings != null){
            if (mMLAdjustFilter != null){
                if (BeautyFunctionUtil.isDokiBeautyForce() || BeautyFunctionUtil.isDokiBeautyEnable()) {
                    mMLAdjustFilter.changeDokiBeautyFilter(publishSettings.isUseDokiBeauty());
                } else {
                    mMLAdjustFilter.switchSmoothFilter(publishSettings.isUseNewSmooth());
                }

                mMLAdjustFilter.setFaceBeautyValue(FaceBeautyID.SKIN_SMOOTH, publishSettings.getSkinSmoothLevel());
                mMLAdjustFilter.setFaceBeautyValue(FaceBeautyID.SKIN_WHITENING, publishSettings.getSkinLightLevel());
                mMLAdjustFilter.setFaceBeautyValue(FaceBeautyID.BIG_EYE, publishSettings.getFaceEyeScale());
                mMLAdjustFilter.setFaceBeautyValue(FaceBeautyID.THIN_FACE, publishSettings.getFaceThinScale());
                if (publishSettings.isUseDokiBeauty()) {
                    mMLAdjustFilter.setFaceBeautyValue(FaceBeautyID.FACE_WIDTH, publishSettings.getFaceWidthLevel());
                    mMLAdjustFilter.setFaceBeautyValue(FaceBeautyID.CHIN_LENGTH, publishSettings.getChinLengthLevel());
                    mMLAdjustFilter.setFaceBeautyValue(FaceBeautyID.NOSE_SIZE, publishSettings.getNoseSizeLevel());
                    mMLAdjustFilter.setFaceBeautyValue(FaceBeautyID.LIP_THICKNESS, publishSettings.getLipThicknessLevel());
                }

                mMLAdjustFilter.selectFilter(Filter.getFilterRes(publishSettings.getFilterName()));
                mMLAdjustFilter.setLookupIntensity(publishSettings.getFilterValue());
            }
        }
    }

    public void setMaskModel(MaskModel maskModel) {
        if (mMLAdjustFilter == null) {
            return;
        }
        ijkConferenceStreamer mediaStreamer = getmMediaStreamer();
        if (mediaStreamer == null){
            return;
        }
        if (maskModel != null) {
            mMLAdjustFilter.clearMaskWithModelType(MaskModel.TYPE_VIDEO_EFFECT);
            maskModel.setModelType(MaskModel.TYPE_VIDEO_EFFECT);
            maskModel.setDuration(999999999);
            mMLAdjustFilter.addMaskModel(maskModel, false);
            mediaStreamer.setDoFaceDetect(true);
        } else {
            mMLAdjustFilter.clearMaskWithModelType(MaskModel.TYPE_VIDEO_EFFECT);
        }
    }

    public void setMaskMkModel(MaskModel maskModel,  FaceTagsBean face) {
        if (mMLAdjustFilter == null) {
            return;
        }
        ijkConferenceStreamer mediaStreamer = getmMediaStreamer();
        if (mediaStreamer == null){
            return;
        }

        if (maskModel != null) {
            mMLAdjustFilter.clearMaskWithModelType(MaskModel.TYPE_VIDEO_EFFECT);
            maskModel.setModelType(MaskModel.TYPE_VIDEO_EFFECT);
            maskModel.setDuration(face.getExpire());

            for(int i =0; i< maskModel.getStickers().size(); i ++){
                com.momo.mcamera.mask.Sticker sticker = maskModel.getStickers().get(i);
                sticker.setDuration(1000L * face.getExpire());
            }
            mMLAdjustFilter.addMaskModel(maskModel, false);
            mediaStreamer.setDoFaceDetect(true);
        } else {
            mMLAdjustFilter.clearMaskWithModelType(MaskModel.TYPE_VIDEO_EFFECT);
        }
    }
    /**
     * 加载面部识别模型
     * @param listener
     */
    public void loadModle(final MLAdjustFilter.FaceDetectedListener listener){
        ijkConferenceStreamer mediaStreamer = getmMediaStreamer();
        if (mediaStreamer == null){
            return;
        }
        try {
            File modelFdFilePath = DynamicResourceManager.getInstance().getResource(DynamicResourceConstants.ITEM_NAME_MMCV_FD_MODEL);
            File modelFaFilePath = DynamicResourceManager.getInstance().getResource(DynamicResourceConstants.ITEM_NAME_MMCV_FA_MODEL);

            if (modelFdFilePath != null && modelFdFilePath.exists() && modelFaFilePath != null && modelFaFilePath.exists()) {
                faceDetecModelPath.add(modelFdFilePath.getAbsolutePath());
                faceDetecModelPath.add(modelFaFilePath.getAbsolutePath());
                mediaStreamer.setFaceDetectModelPath(faceDetecModelPath);
                mediaStreamer.setDoFaceDetect(true);
                mMLAdjustFilter.setFaceDetectedListener(listener);
            } else {
                Toaster.show("正在下载资源，请稍等");
                ResourceChecker.checkMmcvFaFdResource(false, false, new ResourceLoadCallback() {
                    @Override
                    public void onSuccess() {
                        File modelFdFilePath = DynamicResourceManager.getInstance().getResource(DynamicResourceConstants.ITEM_NAME_MMCV_FD_MODEL);
                        File modelFaFilePath = DynamicResourceManager.getInstance().getResource(DynamicResourceConstants.ITEM_NAME_MMCV_FA_MODEL);

                        if (modelFdFilePath != null && modelFdFilePath.exists() && modelFaFilePath != null && modelFaFilePath.exists()) {
                            faceDetecModelPath.add(modelFdFilePath.getAbsolutePath());
                            faceDetecModelPath.add(modelFaFilePath.getAbsolutePath());
                            mediaStreamer.setFaceDetectModelPath(faceDetecModelPath);
                            mediaStreamer.setDoFaceDetect(true);
                            mMLAdjustFilter.setFaceDetectedListener(listener);
                        }
                    }

                    @Override
                    public void onFailed(String errorMsg) {

                    }

                    @Override
                    public void onProcess(int percent, double speed) {

                    }

                    @Override
                    public void onProcessDialogClose() {

                    }
                });
            }
        } catch (Throwable ex) {
            MDLog.printErrStackTrace(LogTag.Mmcv.MmcvModel, ex, "load MMCV_OD_MODEL file error");
        }
    }


    public void selectCamera(Activity activity) {
        ijkConferenceStreamer mediaStreamer = getmMediaStreamer();
        if (null != mediaStreamer) {
            mediaStreamer.selectCamera(activity, 1);
            resetFaceSettings();
            mediaStreamer.setVideoEncodingBitRate(500000);
            mediaStreamer.setDoFaceDetect(true);
            mediaStreamer.selectFaceDetectFilter(activity, mMLAdjustFilter);
            mediaStreamer.startRecording();
            if (faceDetecModelPath.size() > 0) {
                mediaStreamer.setFaceDetectModelPath(faceDetecModelPath);
            }
            mediaStreamer.setFaceDetectTimeoutSwitch(false);
        }
    }
    public void setPreivew(SurfaceHolder surfaceHolder) {
        ijkConferenceStreamer mediaStreamer = getmMediaStreamer();
        if (mediaStreamer != null) {
//            mijkMediaStreamer.setPreviewDisplay(null);  8.4.1版本放开会黑屏卡死
            mediaStreamer.setPreviewDisplay(surfaceHolder);
            mediaStreamer.startPreview(1, surfaceHolder);
        }
    }

    /**
     * 展示某人的视频视图
     * @param container
     * @param uid
     */
    public void showSurfaceView(ViewGroup container, int uid, int radius) {
        if(this.mUDIjkConferenceStreamer == null){
            return;
        }
        Activity activity = getActivity();
        if (activity == null) {
            return;
        }

        final ViewInfo info = new ViewInfo();
        info.container = container;
        info.width = getViewWidth(info.container);
        info.height = getViewHeight(info.container);
        info.radius = radius;
        MDLog.i(LogTag.GameWolf.GameWolf,"showSurfaceView:uid="+uid +",width="+info.width+",height="+info.height
                + ",radius="+radius);

        ViewInfo oldViewInfo = viewInfoMaps.get(uid);
        if (oldViewInfo != null && oldViewInfo.container == container
                && oldViewInfo.width == info.width
                && oldViewInfo.height == info.height
                && oldViewInfo.radius == info.radius){
            //没有有变化，不需要重新add
            MDLog.i(LogTag.GameWolf.GameWolf,"showSurfaceView 视图未发生变化，重复add:uid="+uid);
            return;
        }
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                viewInfoMaps.put(uid, info);
                SurfaceView surfaceView = mUDIjkConferenceStreamer.getRemoteView(uid);
                MDLog.i(LogTag.GameWolf.GameWolf,
                        ">>>>showSurfaceView:uid="+uid +",surfaceview Exist="+(surfaceView != null));
                if (surfaceView != null) {
                    showSurfaceView(info, surfaceView);
                }
            }
        });
    }

    /**
     * 捕捉移除view的异常
     * @param parentView
     * @param childView
     */
    private void securityRemoveView(ViewGroup parentView,View childView){
        if (parentView != null && childView != null){
            try {
                parentView.removeView(childView);
            } catch (RuntimeException e1){

            }catch (Exception e) { }
        }
    }
    private void showSurfaceView(ViewInfo info, SurfaceView surfaceView) {
        if (surfaceView == null) {
            return;
        }
        //是否有移除surfaceview的操作
        boolean isHaveRemoveViewOperate = false;
        if (info != null && info.view != null && info.container != null) {

            securityRemoveView(info.container,info.view);
            info.view = null;
            isHaveRemoveViewOperate = true;
        }

        if (info != null && info.container != null && info.view == null) {

            if (null != surfaceView.getParent()) {
                ViewGroup group = (ViewGroup) surfaceView.getParent();
                securityRemoveView(group,surfaceView);
                isHaveRemoveViewOperate = true;
            }
            MDLog.i(LogTag.GameWolf.GameWolf,"showSurfaceView:isHaveRemoveViewOperate="+isHaveRemoveViewOperate);

            if (isHaveRemoveViewOperate && isNeedDelayAddPhone()){
                MainThreadExecutor.post(hashCode(),new Runnable() {
                    @Override
                    public void run() {
                        if (null == surfaceView.getParent()) {
                            addViewToContainer(info,surfaceView);
                        }
                    }
                });
            }else{
                addViewToContainer(info,surfaceView);
            }

        }
    }

    /**
     * 是否视图需要延迟add
     * @return
     */
    private boolean isNeedDelayAddPhone(){
        if (BaseDeviceUtils.isNotchScreenForMiUi() && Build.VERSION.SDK_INT >= 26){
            return true;
        }
        return false;
    }

    /**
     * 将视图重新添加到容器中
     * @param info
     * @param surfaceView
     */
    private void addViewToContainer(ViewInfo info, SurfaceView surfaceView){
        if (info == null || surfaceView == null){
            return;
        }

        MDLog.i(LogTag.GameWolf.GameWolf,"addViewToContainer:radius:"+info.radius);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            surfaceView.setOutlineProvider(new GameTextureOutlineProvider(info.radius));
            surfaceView.setClipToOutline(true);
        }
        info.view = surfaceView;
        surfaceView.setVisibility(View.GONE);
        info.container.addView(surfaceView);

        ViewGroup.LayoutParams layoutParams = surfaceView.getLayoutParams();
        layoutParams.height = info.height;
        layoutParams.width = info.width;
        surfaceView.setLayoutParams(layoutParams);
        surfaceView.setVisibility(View.VISIBLE);

        surfaceView.getHolder().setFixedSize(info.width , info.height);

        surfaceView.setZOrderMediaOverlay(false);
        info.container.requestLayout();
    }

    public void hideSurfaceView(final int uid,final boolean isOnlyRemoveSurfaceView) {
        Activity activity = getActivity();
        if (activity == null) {
            return;
        }
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                removeSurfaceView(uid,isOnlyRemoveSurfaceView);
            }
        });
    }

    private void removeSurfaceView(int uid,boolean isOnlyRemoveSurfaceView) {
        if (uid == 0) {
            return;
        }
        ViewInfo info;
        if (isOnlyRemoveSurfaceView){
            info = viewInfoMaps.get(uid);
        }else{
            info = viewInfoMaps.remove(uid);
        }

        if (info != null && info.container != null && info.view != null) {
            info.view.setVisibility(View.GONE);
            securityRemoveView(info.container, info.view);
            info.view = null;
        }
    }

    public void onVideoChannelAdded(int uid, SurfaceView surfaceView, int width, int height) {
        Activity activity = getActivity();
        if (activity == null) {
            return;
        }
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                ViewInfo info = viewInfoMaps.get(uid);
                if (info != null && surfaceView != null) {
                    showSurfaceView(info, surfaceView);
                }
            }
        });
    }

    public void onVideoChannelRemove(int uid,final boolean isOnlyRemoveSurfaceView) {
        Activity activity = getActivity();
        if (activity == null) {
            return;
        }
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                removeSurfaceView(uid,isOnlyRemoveSurfaceView);
            }
        });
    }

    public void showFace(LuaValue[] values) {
        String jsonStr = values.length > 0 ? values[0].toString() : "";
        if(StringUtils.notEmpty(jsonStr)){
            JSONObject params = null;
            try {
                params = new JSONObject(jsonStr);
            } catch (JSONException e) {}

            if(params != null){
                FaceTagsBean face = FaceTagsBean.fromJson(params);

                File file = new File(Configs.getAppHome().getAbsolutePath()+face.getZip_url());

                if(file.exists()){
                    setMaskMkModel(FaceTagsManager.getMaskMKModelByface(MomoKit.getContext(), face, file.getAbsolutePath()), face);
                }
            }
        }
    }

    /**
     * 关闭预览界面
     */
    public void closePreviewView() {
        if (mGameLuaLivePreviewDialog != null && mGameLuaLivePreviewDialog.isShowing()){
            mGameLuaLivePreviewDialog.dismiss();
        }
    }

    public void showPreviewView(Activity activity,GameLuaLivePreviewDialog.GameLuaPreviewCallback callback) {
        if (activity == null || (mGameLuaLivePreviewDialog != null && mGameLuaLivePreviewDialog.isShowing())){
            return;
        }
        mGameLuaLivePreviewDialog  = new GameLuaLivePreviewDialog(activity,GameLuaLiveManager.this);
        mGameLuaLivePreviewDialog.setPrevieweCallback(callback);
        mGameLuaLivePreviewDialog.show();
    }
    /**
     * 展示预览页面，提供默认的UI
     *
     */
    public void showPreviewViewAndCheckPermission(GameLuaLivePreviewDialog.GameLuaPreviewCallback callback) {
        Activity activity = getActivity();
        if (activity != null && (getActivity() instanceof LuaViewActivity)){
            LuaViewActivity lva = (LuaViewActivity) getActivity();
            boolean hasPermission = lva.hasPermission(Manifest.permission.CAMERA, new PermissionListener() {
                @Override
                public void onPermissionGranted(int requestCode) {
                    showPreviewView(activity,callback);
                }

                @Override
                public void onPermissionDenied(int requestCode) {}

                @Override
                public void onPermissionCanceled(int requestCode) {}
            });
            if (hasPermission){
                showPreviewView(activity,callback);
            }
        }else{
            showPreviewView(activity,callback);
        }


    }
    /**
     * 展示预览视图
     * @param container
     * @param uid
     */
    public void showPreviewSurfaceView(ViewGroup container, int uid) {

        Activity activity = getActivity();
        if (activity == null) {
            return;
        }
        ijkConferenceStreamer mediaStreamer = getmMediaStreamer();
        if (mediaStreamer == null){
            MDLog.i(LogTag.GameWolf.GameWolf,"showPreviewSurfaceView:ijkConferenceStreamer=null");
            return;
        }
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                loadModle(new MLAdjustFilter.FaceDetectedListener(){
                    @Override
                    public void faceDetectd(MMCVInfo mmcvInfo) {

                    }
                });

                int videoWidth = getViewWidth(container);
                int videoHeight = getViewHeight(container);

                SurfaceView surfaceView = mUDIjkConferenceStreamer.getRemoteView(uid);
                if (surfaceView != null) {

                    mediaStreamer.resetCodec(176,176);
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                        surfaceView.setOutlineProvider(new GameTextureOutlineProvider(UIUtils.getPixels(0)));
                        surfaceView.setClipToOutline(true);
                    }

                    if (null != surfaceView.getParent()) {
                        ViewGroup group = (ViewGroup) surfaceView.getParent();
                        group.removeView(surfaceView);
                    }

                    ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(DimenUtil.check(videoWidth),
                            DimenUtil.check(videoHeight));
                    surfaceView.setLayoutParams(layoutParams);

                    container.addView(surfaceView);
                    surfaceView.setVisibility(View.VISIBLE);

                    surfaceView.getHolder().setFixedSize(videoWidth, videoHeight);
                    surfaceView.setZOrderMediaOverlay(true);
                }
            }
        });


    }

    /**
     * 获取当前用户的uid
     */
    public int getUID(){
        if (mUDIjkConferenceStreamer != null){
            return mUDIjkConferenceStreamer.getUid();
        }
        return 0;
    }

    public void leaveChannel() {
        MomoApplicationEvent.removeEventListener(this.getClass().getName());
    }

    public void joinChannel() {
        MomoApplicationEvent.addEventListener(this.getClass().getName(), this);
    }

    @Override
    public void onAppExit() {
        MDLog.i(LogTag.GameWolf.GameWolf,"onAppExit");
        ijkConferenceStreamer mediaStreamer = getmMediaStreamer();
        if (mediaStreamer != null){
            MDLog.i(LogTag.GameWolf.GameWolf,"muteLocalVideoStream:true");
            mediaStreamer.muteLocalVideoStream(true);
        }
    }

    @Override
    public void onAppEnter() {
        MDLog.i(LogTag.GameWolf.GameWolf,"onAppEnter");
        ViewInfo viewInfo = viewInfoMaps.get(getUID());
        ijkConferenceStreamer mediaStreamer = getmMediaStreamer();
        if (viewInfo != null) {
            if (mediaStreamer != null){
                MDLog.i(LogTag.GameWolf.GameWolf,"muteLocalVideoStream:false");
                mediaStreamer.muteLocalVideoStream(false);
            }
        }
        if (mediaStreamer != null){
            mediaStreamer.resetCamera();
        }
    }
    class ViewInfo {
        //存储view的位置和宽高
        int width, height,radius;
        SurfaceView view;
        ViewGroup container;
    }

    /**
     * 获取视图的宽度
     * @param view
     * @return
     */
    public int getViewWidth(View view) {
        if (view != null) {
            ViewGroup.LayoutParams params = view.getLayoutParams();
            if (params != null) {
                int pw = params.width;
                if (pw >= 0)
                    return pw;
                int mw = view.getMeasuredWidth();
                if (mw > 0)
                    return mw;
                if (pw == MATCH_PARENT) {
                    if (view.getParent() instanceof ViewGroup) {
                        return ((ViewGroup) view.getParent()).getMeasuredWidth();
                    }
                }
                return mw;
            }
        }
        return 0;
    }
    /**
     * 获取视图的高度
     * @param view
     * @return
     */
    public int getViewHeight(View view) {
        if (view != null) {
            ViewGroup.LayoutParams params = view.getLayoutParams();
            if (params != null) {
                int ph = params.height;
                if (ph >= 0)
                    return ph;
                int mh = view.getMeasuredHeight();
                if (mh > 0)
                    return mh;
                if (ph == MATCH_PARENT) {
                    if (view.getParent() instanceof ViewGroup) {
                        return ((ViewGroup) view.getParent()).getMeasuredHeight();
                    }
                }
                return mh;
            }
        }
        return 0;
    }
}
