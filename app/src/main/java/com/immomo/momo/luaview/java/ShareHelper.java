package com.immomo.momo.luaview.java;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.cosmos.mdlog.MDLog;
import com.immomo.android.module.feed.share.CommonFeedShareInfo;
import com.immomo.android.module.feed.share.MicroVideoFeedShareInfo;
import com.immomo.android.router.share.ShareDialogConfig;
import com.immomo.android.router.share.ShareRouter;
import com.immomo.android.router.share.model.PageConfig;
import com.immomo.mls.LuaViewManager;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.util.FileUtil;
import com.immomo.mls.utils.LVCallback;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoKit;
import com.immomo.momo.feed.bean.PublishFeedNewRouter;
import com.immomo.momo.feed.bean.PublishFeedOptionsLocalPic;
import com.immomo.momo.mk.share.bean.MKSharePannel;
import com.immomo.momo.share2.IShareDialog;
import com.immomo.momo.share2.listeners.FeedShareClickListener;
import com.immomo.momo.share2.listeners.MKShareClickListener;
import com.immomo.momo.share3.listeners.OnCheckResultListener;
import com.immomo.momo.util.WebShareParams;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.luaj.vm2.Globals;
import org.luaj.vm2.LuaValue;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import info.xudshen.android.appasm.AppAsm;

// extends WXModule

@LuaClass
public class ShareHelper {
    public static final String LUA_CLASS_NAME = "ShareHelper";

    private static final String TAG_ = ShareHelper.class.getSimpleName();

    protected Globals globals;
    private Context mContext;

    public ShareHelper(Globals g, LuaValue[] titles) {
        globals = g;
        init();
    }

    public ShareHelper(Globals globals) {
        this.globals = globals;
        init();
    }

    private void init() {
        LuaViewManager m = (LuaViewManager) globals.getJavaUserdata();
        mContext = (m != null ? m.context : null);
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (shareClickListener != null) {
            shareClickListener.onActivityResult(requestCode, resultCode, data);
        }
    }

    @LuaBridge
    public void share(Map map, LVCallback lvCallback) {
        //        Toaster.show("share");
        // String options, JSCallback callback

        try {
            // JSONObject jsonObject = new JSONObject(options);
            JSONObject jsonObject = new JSONObject(map);
            if (null != jsonObject) {
                showSharePanel(jsonObject, new WeakReference<LVCallback>(lvCallback));
            }
        } catch (JSONException e) {
            MDLog.printErrStackTrace(TAG_, e, "");
        }
    }

    private MKSharePannel pannel;
    private IShareDialog dialog;
    private MKShareClickListener shareClickListener;

    private void showSharePanel(JSONObject jsonObject, final WeakReference<LVCallback> callbackRef) throws JSONException {
        Context context = mContext;

        String linkUrl = jsonObject.optString("url");
        String textContent = jsonObject.optString("text");
        String picUrl = jsonObject.optString("pic");
        String shareTitle = jsonObject.optString("title");

        String callBack = jsonObject.optString("callback");
        String web_source = jsonObject.optString("web_source");

        JSONArray pics_array = jsonObject.optJSONArray("pics");
        List<String> pics = new ArrayList<>();
        if ((pics_array != null) && (pics_array.length() > 0)) {
            for (int i = 0, length = pics_array.length(); i < length; i++) {
                pics.add(pics_array.getString(i));
            }
        }

        JSONArray jarray = jsonObject.optJSONArray("apps");
        List<String> apps = new ArrayList<String>();
        if ((jarray != null) && (jarray.length() > 0)) {
            for (int i = 0, length = jarray.length(); i < length; i++) {
                apps.add(jarray.getString(i));
            }
        }

        Map<String, WebShareParams> shareParamsMap = new HashMap<>();
        if (apps.size() > 0) {
            JSONObject appConifgsJson = jsonObject.optJSONObject("configs");
            if (null != appConifgsJson) {
                for (String appStr : apps) {
                    JSONObject appConfig = appConifgsJson.optJSONObject(appStr);
                    if (appConfig != null) {
                        WebShareParams params = new WebShareParams();
                        params.shareUrl = appConfig.optString("url");
                        params.shareText = appConfig.optString("text");
                        params.sharePicUrl = appConfig.optString("pic");
                        params.shareTitle = appConfig.optString("title");
                        if (appConfig.has("resource")) {
                            params.resource = appConfig.optJSONObject("resource").toString();
                        }
                        params.shareMode = appConfig.optInt("sdk");
                        params.defaultContent = appConfig.optString("sdk_text");
                        params.callBack = callBack;
                        params.webSource = web_source;
                        params.shareType = appConfig.optString("share_type");
                        shareParamsMap.put(appStr, params);
                    }
                }
            }
        }

        WebShareParams defaultParams = new WebShareParams();
        defaultParams.shareUrl = linkUrl;
        defaultParams.callBack = callBack;
        defaultParams.sharePicUrl = picUrl;
        defaultParams.shareText = textContent;
        defaultParams.shareTitle = shareTitle;
        defaultParams.sharePicUrls = pics;
        defaultParams.shareMode = jsonObject.optInt("sdk");
        defaultParams.defaultContent = jsonObject.optString("sdk_text");
        defaultParams.webSource = web_source;

        pannel = new MKSharePannel();
        pannel.apps = apps;
        pannel.defaultMkShareParams = defaultParams;
        pannel.shareParamsMap = shareParamsMap;
        if (null != dialog && dialog.isShowing()) {
            dialog.dismiss();
        }

        shareClickListener = new MKShareClickListener((Activity) context, pannel);
        shareClickListener.setOnCheckResultListener(new OnCheckResultListener() {
            @Override
            public void onCheckResult(String callback, String em) {

                try {
                    JSONObject obj = new JSONObject(em);
                    callbackRef.get().call(obj.optString("platform"), obj.optString("status"), obj.optString("message"));
                } catch (Exception e) {

                }
            }
        });

        ShareDialogConfig config = new ShareDialogConfig.Builder(context)
                .mShareListener(shareClickListener)
                .mPageConfig(new PageConfig.Builder().supportDark(true).apps(pannel.apps).build())
                .build();

        dialog = AppAsm.getRouter(ShareRouter.class).showShareDialog(config);

    }

    private void shareNative(CommonFeedShareInfo feed, List<String> shareArray, String fromType, String source, final LVCallback callback) {
        Activity activity = getSafeActivity();
        if (feed == null || feed.getFeedId() == null) {
            callback.call("feed has no id");
            return;
        }
        if (shareArray == null || shareArray.isEmpty()) {
            callback.call("share array is emtpy");
            return;
        }
        FeedShareClickListener shareClickListener = new Listener(activity, source, callback);
        shareClickListener.setCommonFeedShareInfo(feed);

        ShareDialogConfig config = new ShareDialogConfig.Builder(activity)
                .mShareListener(shareClickListener)
                .mPageConfig(new PageConfig.Builder().supportDark(true).apps(shareArray).build())
                .build();

        AppAsm.getRouter(ShareRouter.class).showShareDialog(config);

    }

    @LuaBridge
    public void shareNative(Map<String, Object> params, LVCallback callback) {
        List<String> shareArray = new ArrayList<>();
        String feedId = getString(params, "feedid");
        String fromType = getString(params, "from_type");
        String source = getString(params, "source");

        Object obj = params.get("shareArray");
        if (obj instanceof com.alibaba.fastjson.JSONArray) {
            com.alibaba.fastjson.JSONArray ja = (com.alibaba.fastjson.JSONArray) obj;
            for (int i = 0, l = ja.size(); i < l; i++) {
                Object a = ja.get(i);
                if (a != null)
                    shareArray.add(a.toString());
            }
        }
        obj = params.get("funcArray");
        if (obj instanceof com.alibaba.fastjson.JSONArray) {
            com.alibaba.fastjson.JSONArray ja = (com.alibaba.fastjson.JSONArray) obj;
            for (int i = 0, l = ja.size(); i < l; i++) {
                Object a = ja.get(i);
                if (a != null)
                    shareArray.add(a.toString());
            }
        }

        CommonFeedShareInfo shareInfo = parseCommonFeedShareInfo((Map<String, Object>) params.get("info"), feedId, fromType);

        shareNative(shareInfo, shareArray, fromType, source, callback);
    }

    @LuaBridge
    public void sharePlatform(Map map, LVCallback lvCallback) {
        if (map != null) {
            try {
                JSONObject js = new JSONObject(map);
                sharePlatform(js, lvCallback);
            } catch (JSONException e) {
                MDLog.printErrStackTrace(LogTag.COMMON, e);
            }
        }
    }

    private void sharePlatform(JSONObject jsonObject, LVCallback callback) throws JSONException {
        Context context = mContext;
        Activity activity = null;
        if (context instanceof Activity) {
            activity = (Activity) context;
        }
        if (activity == null) {
            return;
        }
        String platform = jsonObject.optString("platform");
        String linkUrl = jsonObject.optString("url");
        String textContent = jsonObject.optString("text");
        String picUrl = jsonObject.optString("pic");
        String shareTitle = jsonObject.optString("title");

        String web_source = jsonObject.optString("web_source");

        JSONArray pics_array = jsonObject.optJSONArray("pics");
        List<String> pics = new ArrayList<>();
        if ((pics_array != null) && (pics_array.length() > 0)) {
            for (int i = 0, length = pics_array.length(); i < length; i++) {
                pics.add(pics_array.getString(i));
            }
        }

        Map<String, WebShareParams> shareParamsMap = new HashMap<>();
        if (!TextUtils.isEmpty(platform)) {
            JSONObject appConifgsJson = jsonObject.optJSONObject("configs");
            if (null != appConifgsJson) {
                JSONObject appConfig = appConifgsJson.optJSONObject(platform);
                if (appConfig != null) {
                    WebShareParams params = new WebShareParams();
                    params.shareUrl = appConfig.optString("url");
                    params.shareText = appConfig.optString("text");
                    params.sharePicUrl = appConfig.optString("pic");
                    params.shareTitle = appConfig.optString("title");
                    if (appConfig.has("resource")) {
                        params.resource = appConfig.optJSONObject("resource").toString();
                    }
                    params.shareMode = appConfig.optInt("sdk");
                    params.defaultContent = appConfig.optString("sdk_text");
                    params.webSource = web_source;
                    params.shareType = appConfig.optString("share_type");
                    shareParamsMap.put(platform, params);
                }
            }
        }

        WebShareParams defaultParams = new WebShareParams();
        defaultParams.shareUrl = linkUrl;
        defaultParams.sharePicUrl = picUrl;
        defaultParams.shareText = textContent;
        defaultParams.shareTitle = shareTitle;
        defaultParams.sharePicUrls = pics;
        defaultParams.shareMode = jsonObject.optInt("sdk");
        defaultParams.defaultContent = jsonObject.optString("sdk_text");
        defaultParams.webSource = web_source;

        MKSharePannel pannel = new MKSharePannel();
        pannel.apps = new ArrayList<>();
        pannel.apps.add(platform);
        pannel.defaultMkShareParams = defaultParams;
        pannel.shareParamsMap = shareParamsMap;
        MKShareClickListener shareClickListener = new MKShareClickListener(activity, pannel);
        final WeakReference<LVCallback> callbackRef = new WeakReference<LVCallback>(callback);
        shareClickListener.setOnCheckResultListener(new OnCheckResultListener() {
            @Override
            public void onCheckResult(String callback, String em) {
                if (callbackRef != null && callbackRef.get() != null) {

                    try {
                        JSONObject obj = new JSONObject(em);
                        callbackRef.get().call(obj.optString("platform"), obj.optString("status"), obj.optString("message"));
                    } catch (Exception e) {

                    }
                }
            }
        });
        directMKShare(platform, shareClickListener);
    }

    @LuaBridge
    public void shareLocalImageToFeed(Map<String, Object> option, LVCallback callback) {
        String path = getString(option.get("path"), "");
        if (TextUtils.isEmpty(path)) {
            shareLocalImageFailed(callback, "路径不能为空");
            return;
        }
        if (FileUtil.isLocalUrl(path)) {
            path = FileUtil.getAbsoluteUrl(path);
        }
        if (TextUtils.isEmpty(path) || !new File(path).exists()) {
            shareLocalImageFailed(callback, "该路径下的文件不存在");
            return;
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("status", 0);
        map.put("message", "success");
        callback.call(map);
        Activity activity = getSafeActivity();

        AppAsm.getRouter(PublishFeedNewRouter.class).publishFeed(activity, new PublishFeedOptionsLocalPic(path, null,false));

    }

    private void shareLocalImageFailed(LVCallback callback, String msg) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("status", 1);
        map.put("message", msg);
        callback.call(map);
    }

    private CommonFeedShareInfo parseCommonFeedShareInfo(Map<String, Object> info, String feedId, String fromType) {
        String microVideoId = getString(info, "microvideoid");
        return new MicroVideoFeedShareInfo(
                "",                 // userId
                feedId,
                false,
                getInt(info, "permission", 1) == 1,
                "",                // eventId
                microVideoId != null ? microVideoId : ""
        );
    }

    private static final String getString(Map<String, Object> map, String key) {
        Object obj = map.get(key);
        return obj != null ? obj.toString() : null;
    }

    private static final int getInt(Map<String, Object> map, String key, int def) {
        Object obj = map.get(key);
        if (obj == null)
            return def;
        if (obj instanceof Integer)
            return (Integer) obj;
        try {
            return Integer.parseInt(obj.toString());
        } catch (Exception e) {
        }
        return def;
    }

    private static class Listener extends FeedShareClickListener {
        private String source;
        private LVCallback jsCallback;

        public Listener(Activity activity, String source, LVCallback callback) {
            super(activity);
            this.source = source;
            jsCallback = callback;
        }

        @Override
        public void onClick(String appName) {
            if (jsCallback != null)
                jsCallback.call(appName);
        }
    }

    private void directMKShare(String type, MKShareClickListener shareClickListener) {
        switch (type) {
            case "momo_contacts":
                shareClickListener.share2Friend();
                break;
            case "momo_feed":
                shareClickListener.shareToPublishFeedActivity();
                break;
            case "qq":
                shareClickListener.shareToQQFriend();
                break;
            case "weixin":
                shareClickListener.shareToWeixinQuan();
                break;
            case "weixin_friend":
                shareClickListener.shareToWexinFriend();
                break;
            case "qzone":
                shareClickListener.shareToQQZone();
                break;
            default:
                break;
        }
    }

    private String getString(@Nullable Object value, @Nullable String df) {

        if (value == null)
            return df;

        String originValue;
        if (value instanceof String) {
            originValue = (String) value;
        } else {
            originValue = value.toString();
        }

        return originValue;
    }

    private Activity getSafeActivity() {
        if (mContext instanceof Activity)
            return (Activity) mContext;
        return MomoKit.getTopActivity();
    }
}
