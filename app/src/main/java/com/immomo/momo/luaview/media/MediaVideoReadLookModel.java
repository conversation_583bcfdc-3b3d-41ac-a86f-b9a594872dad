package com.immomo.momo.luaview.media;

import com.cosmos.mdlog.MDLog;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * author: hongming.wei
 * data: 2023/3/3
 */
public class MediaVideoReadLookModel {

    @Expose
    @SerializedName("forwardFeedId")
    private String forwardFeedId;

    @Expose
    @SerializedName("isAutoPlay")
    private boolean isAutoPlay;

    @Expose
    @SerializedName("mediaVideoSource")
    private String mediaVideoSource;

    @Expose
    @SerializedName("videoEventId")
    private String videoEventId;

    @Expose
    @SerializedName("videoCurrentIndex")
    private String videoCurrentIndex;

    @Expose
    @SerializedName("videoLogMap")
    private Map<String, Object> videoLogMap;

    @Expose
    @SerializedName("videoTagDesc")
    private String videoTagDesc;

    @Expose
    @SerializedName("businesstype")
    private String businesstype;

    @Expose
    @SerializedName("secondType")
    private String secondType;

    @Expose
    @SerializedName("params")
    private String params;


    public String getForwardFeedId() {
        return forwardFeedId;
    }

    public void setForwardFeedId(String forwardFeedId) {
        this.forwardFeedId = forwardFeedId;
    }

    public boolean isAutoPlay() {
        return isAutoPlay;
    }

    public void setAutoPlay(boolean autoPlay) {
        isAutoPlay = autoPlay;
    }

    public String getMediaVideoSource() {
        return mediaVideoSource;
    }

    public void setMediaVideoSource(String mediaVideoSource) {
        this.mediaVideoSource = mediaVideoSource;
    }

    public String getVideoEventId() {
        return videoEventId;
    }

    public void setVideoEventId(String videoEventId) {
        this.videoEventId = videoEventId;
    }

    public String getVideoCurrentIndex() {
        return videoCurrentIndex;
    }

    public void setVideoCurrentIndex(String videoCurrentIndex) {
        this.videoCurrentIndex = videoCurrentIndex;
    }

    public Map<String, Object> getVideoLogMap() {
        return videoLogMap;
    }

    public void setVideoLogMap(Map<String, Object> videoLogMap) {
        this.videoLogMap = videoLogMap;
    }

    public String getVideoTagDesc() {
        return videoTagDesc;
    }

    public void setVideoTagDesc(String videoTagDesc) {
        this.videoTagDesc = videoTagDesc;
    }

    public String getBusinesstype() {
        return businesstype;
    }

    public void setBusinesstype(String businesstype) {
        this.businesstype = businesstype;
    }

    public String getSecondType() {
        return secondType;
    }

    public void setSecondType(String secondType) {
        this.secondType = secondType;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public Map<String, String> getLogMap() {
        Map<String, String> logMap = new HashMap<>();
        if (getVideoLogMap() == null || getVideoLogMap().isEmpty()) {
            return logMap;
        }
        Set<String> setKey = getVideoLogMap().keySet();
        for (String key: setKey){
            logMap.put(key, getVideoLogMap().get(key) != null ? String.valueOf(getVideoLogMap().get(key)) : "");
        }
        return logMap;
    }


    @Override
    public String toString() {
        return "MediaVideoReadLookModel{" +
                "forwardFeedId='" + forwardFeedId + '\'' +
                ", isAutoPlay=" + isAutoPlay +
                ", mediaVideoSource='" + mediaVideoSource + '\'' +
                ", videoEventId='" + videoEventId + '\'' +
                ", videoCurrentIndex='" + videoCurrentIndex + '\'' +
                ", videoLogMap=" + videoLogMap +
                ", videoTagDesc='" + videoTagDesc + '\'' +
                ", businesstype='" + businesstype + '\'' +
                ", secondType='" + secondType + '\'' +
                ", params='" + params + '\'' +
                '}';
    }
}
