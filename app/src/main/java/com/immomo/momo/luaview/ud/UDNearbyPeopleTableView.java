package com.immomo.momo.luaview.ud;

import android.view.ViewGroup;

import com.immomo.android.module.nearbypeople.lua.view.LuaNearbyPeopleTableView;
import com.immomo.mls.fun.ud.view.recycler.UDBaseRecyclerAdapter;
import com.immomo.mls.fun.ud.view.recycler.UDBaseRecyclerLayout;
import com.immomo.mls.fun.ui.OnLoadListener;
import com.immomo.momo.luaview.weight.IBubbleRefreshRecyclerView;

import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

/**
 * Created by qu.jiaqi on 2021/3/12.
 * <p>
 * 小宇宙使用UDBubbleRefreshTableView设置了LuaBubbleRefreshTableView RefreshStyle.LUA 禁用了二段下拉
 * 附近的人使用NearbyPeopleTableView bridge根据隐私 设置二段下拉
 */
@LuaApiUsed
public class UDNearbyPeopleTableView<T extends ViewGroup & IBubbleRefreshRecyclerView & OnLoadListener,
        A extends UDBaseRecyclerAdapter, L extends UDBaseRecyclerLayout> extends UDBubbleRefreshTableView<T, A, L> {
    public static final String LUA_CLASS_NAME = "NearbyPeopleTableView";

    @LuaApiUsed
    public UDNearbyPeopleTableView(long L, LuaValue[] initParams) {
        super(L, initParams);
    }

    @Override
    protected T newView(LuaValue[] init) {
        boolean loadEnable = false;
        boolean bubbleEnable = false;
        if (init.length > 0) {
            mRefreshEnabled = init[0].toBoolean();
        }
        if (init.length > 1) {
            bubbleEnable = init[1].toBoolean();
        }

        if (init.length > 2) {
            loadEnable = init[2].toBoolean();
        }
        refreshTableView = new LuaNearbyPeopleTableView(getContext(), this, mRefreshEnabled, bubbleEnable, loadEnable);
        return (T) refreshTableView;
    }
}
