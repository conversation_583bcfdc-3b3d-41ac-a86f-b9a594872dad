package com.immomo.momo.luaview.ud;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;

import com.cosmos.mdlog.MDLog;
import com.immomo.framework.base.BaseActivity;
import com.immomo.framework.utils.UIUtils;
import com.immomo.mls.LuaViewManager;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.R;
import com.immomo.momo.album.util.AlbumConstant;
import com.immomo.momo.moment.MomentConstants;
import com.immomo.momo.moment.activity.VideoRecordAndEditActivity;
import com.immomo.momo.moment.mvp.VideoInfoTransBean;
import com.immomo.momo.universe.audio.view.UniverseAudioRecordActivity;
import com.immomo.momo.universe.util.UniverseLuaUtils;
import com.immomo.momo.util.StringUtils;

import org.json.JSONException;
import org.json.JSONObject;
import org.luaj.vm2.Globals;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;


/**
 * author: hongming.wei
 * data: 2023/6/15
 */
@LuaClass
public class UDUniverseOpenLibraryLuaHelp {

    public static final String LUA_CLASS_NAME = "UniverseOpenLibraryLuaHelp";
    private static final String TAG = "UDUniverseOpenLibraryLuaHelp";
    private static LVCallback mCallback;
    private static LVCallback mAudioCallback;
    private static String albumUniversePath;


    @LuaBridge
    public void showLibrary(Globals globals, LVCallback callback) {
        BaseActivity context = (BaseActivity) ((LuaViewManager) globals.getJavaUserdata()).context;
        mCallback = callback;
        VideoInfoTransBean videoInfoTransBean = new VideoInfoTransBean();
        videoInfoTransBean.mediaType = AlbumConstant.MEDIA_TYPE_IMAGE;
        videoInfoTransBean.state = VideoInfoTransBean.STATE_CHOOSE_MEDIA;
        videoInfoTransBean.mode = VideoInfoTransBean.MODE_BACK_RESULT_IMMEDIATLY_CHOOSE;
        videoInfoTransBean.navigationText = UIUtils.getString(R.string.cancel);
        videoInfoTransBean.albumThemeStyle = VideoInfoTransBean.THEME_STYLE_LIGHT;
        videoInfoTransBean.albumTabTitleSize = 18;
        videoInfoTransBean.albumIndicatorHide = true;
        videoInfoTransBean.albumTabScaleEnable = false;
        videoInfoTransBean.albumTabInCenter = true;
        videoInfoTransBean.arrowWidthDip = 10;
        videoInfoTransBean.arrowHeightDip = 5;
        videoInfoTransBean.blockBusiness = false;
        videoInfoTransBean.luaInvokeActivity = true;
        Intent intent = new Intent(context, VideoRecordAndEditActivity.class);
        Bundle bundle = new Bundle();
        bundle.putParcelable(MomentConstants.EXTRA_KEY_VIDEO_TRANS_INFO, videoInfoTransBean);
        intent.putExtras(bundle);
        if (context != null) {
            ((Activity) context).startActivity(intent);
        }
    }

    @LuaBridge
    public void deleteImage() {
        albumUniversePath = "";
        mCallback = null;
    }


    @LuaBridge
    public void showAudio(Globals globals, LVCallback callback) {
        BaseActivity context = (BaseActivity) ((LuaViewManager) globals.getJavaUserdata()).context;
        mAudioCallback = callback;
        if (context != null) {
            Intent intent = new Intent(context, UniverseAudioRecordActivity.class);
            context.startActivity(intent);
        }
    }


    public static void obtainUniverseAlbum(String albumPath) {
        if (mCallback != null && StringUtils.isNotEmpty(albumPath)) {
            albumUniversePath = albumPath;
            mCallback.call(albumUniversePath);
        }
    }


    public static void obtainUniverseAudio(@Nullable  String audioName, @Nullable String voicePath, long time) {
        if (mAudioCallback != null && StringUtils.isNotEmpty(audioName) && StringUtils.isNotEmpty(voicePath)) {
            Map<String, Object> map = new HashMap<>();
            map.put("voicePath", voicePath);
            map.put("time", time);
            map.put("audioName", audioName);
            mAudioCallback.call(map);
        } else {
            if (mAudioCallback != null ) {
                Toaster.show(UIUtils.getString(R.string.get_voice_error));
            }
        }
    }

    @LuaBridge
    public void upload(Globals globals, String jsonStr) {
        BaseActivity context = (BaseActivity) ((LuaViewManager) globals.getJavaUserdata()).context;
        try {
            if (context == null) {
                return;
            }
            int fileType = -1;
            String filePath = "";
            String voiceName = "";
            long voiceDuration = 0;

            JSONObject object = new JSONObject(jsonStr);
            if (object.has("type")) {
                fileType = object.optInt("type", -1); // 0, 图片， 1，音频
            }
            if (object.has("filePath")) {
                filePath = object.optString("filePath", "");  //文件地址
            }
            if (object.has("voiceName")) {
                voiceName = object.optString("voiceName", ""); //音频名称
            }
            if (object.has("voiceDuration")) {
                voiceDuration = object.optLong("voiceDuration", 0);  //音频时长
            }
            if (fileType == 1) {
                UniverseLuaUtils.INSTANCE.savePic(jsonStr, filePath);
            } else if (fileType == 2) {
                UniverseLuaUtils.INSTANCE.saveAudio(jsonStr, filePath, voiceName, voiceDuration);
            } else {
                UniverseLuaUtils.INSTANCE.sendFeedSuccessLua("");
            }
        } catch (JSONException e) {
            MDLog.printErrStackTrace(TAG, e);
        }
    }


    @LuaBridge
    public void clearDraft(){
        UniverseLuaUtils.INSTANCE.deleteLocal();
    }


    @LuaBridge
    public void readDraft(LVCallback callback){
        UniverseLuaUtils.INSTANCE.readDraft(callback);
    }



    @LuaBridge
    public void getImgSpamInfo(String fileName, LVCallback callback) {
        UniverseLuaUtils.INSTANCE.checkImgSpam(fileName, callback);
    }

    @LuaBridge
    public void saveDraft(String jsonStr) {
        UniverseLuaUtils.INSTANCE.cacheLocal(jsonStr);
    }

    @LuaBridge
    public void uploadImages(ArrayList<String> pics) {
        UniverseLuaUtils.INSTANCE.savePics(pics);
    }



}
