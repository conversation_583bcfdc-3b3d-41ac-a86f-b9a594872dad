package com.immomo.momo.luaview;

import android.content.Context;
import android.view.LayoutInflater;

import androidx.recyclerview.widget.RecyclerView;

import com.cosmos.mdlog.MDLog;
import com.immomo.framework.view.pulltorefresh.MomoPullRefreshLayout;
import com.immomo.framework.view.pulltorefresh.refreshview.AbstractRefreshView;
import com.immomo.framework.view.pulltorefresh.refreshview.LuaRefreshView;
import com.immomo.mls.MLSAdapterContainer;
import com.immomo.mls.base.ud.lv.ILView;
import com.immomo.mls.fun.other.Point;
import com.immomo.mls.fun.ud.view.recycler.UDBaseRecyclerAdapter;
import com.immomo.mls.fun.ud.view.recycler.UDBaseRecyclerLayout;
import com.immomo.mls.fun.ui.OnLoadListener;
import com.immomo.mls.fun.ui.SizeChangedListener;
import com.immomo.mls.fun.weight.MLSRecyclerView;
import com.immomo.mls.util.DimenUtil;
import com.immomo.mls.util.LuaViewUtil;
import com.immomo.mls.utils.MainThreadExecutor;
import com.immomo.mls.weight.load.ILoadViewDelegete;
import com.immomo.momo.LogTag;
import com.immomo.momo.R;
import com.immomo.momo.luaview.ud.UDBubbleRefreshTableView;
import com.immomo.momo.luaview.weight.BorderRadiusMomoPullRefreshLayout;
import com.immomo.momo.luaview.weight.IBubbleRefreshRecyclerView;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by zhang.ke
 * on 2019/3/15
 */
public class LuaBubbleRefreshTableView<A extends UDBaseRecyclerAdapter, L extends UDBaseRecyclerLayout>
        extends BorderRadiusMomoPullRefreshLayout implements ILView<UDBubbleRefreshTableView>, IBubbleRefreshRecyclerView, OnLoadListener, MomoPullRefreshLayout.OnRefreshListener {
    private final MLSRecyclerView recyclerView;
    private final UDBubbleRefreshTableView userdata;
    private final ILoadViewDelegete loadViewDelegete;
    private SizeChangedListener sizeChangedListener;
    private boolean loadEnable = false;
    private boolean isLoading = false;
    private ViewLifeCycleCallback cycleCallback;

    public LuaBubbleRefreshTableView(Context globals, UDBubbleRefreshTableView javaUserData, boolean refreshEnable, boolean refreshBubbleEnable, boolean loadEnable) {
        super(globals);
        userdata = javaUserData;
        recyclerView = (MLSRecyclerView) LayoutInflater.from(globals).inflate(R.layout.default_layout_recycler_view, null);
        loadViewDelegete = MLSAdapterContainer.getLoadViewAdapter().newLoadViewDelegate(globals, recyclerView);
        recyclerView.setLoadViewDelegete(loadViewDelegete);
        recyclerView.setOnLoadListener(this);
        setViewLifeCycleCallback(userdata);
        userdata.setLoadViewDelegete(loadViewDelegete);
//        setDistanceToTriggerSync(MLSFlag.getRefreshEndPx());
        addView(recyclerView, LuaViewUtil.createRelativeLayoutParamsMM());
        setRefreshEnable(refreshEnable);
        refreshBubbleEnable(refreshBubbleEnable);
        setLoadEnable(loadEnable);
        if (mRefreshView != null
                && (getRefreshStyle() == RefreshStyle.HOME_PAGE || getRefreshStyle() == RefreshStyle.HOME_PAGE_PRIVACY)) {
            mRefreshView.setOnRefreshViewListener(new AbstractRefreshView.OnRefreshViewListener() {

                @Override
                public void switchBequietStatus() {
                    if (userdata != null) {
                        userdata.callSwitchBequiet();
                    }
                }

                @Override
                public void onRefreshViewStop(int action, int data) {
                    if (onRefreshListener != null && mCurrentPhase > 0) {
                        if (action == AbstractRefreshView.REFRESH_ACTION) {
                            //只需要执行刷新
                            onRefreshListener.onRefresh(0);
                        } else if (action == AbstractRefreshView.BUBBLE_ACTION) {
                            //回调给lua 执行冒泡
                            onRefreshWithParams(1, data);
                        }
                    }

                    //头部点击，需要滑动到第一阶段
                    if (mCurrentPhase > 0 && mRefreshing) {
                        animateOffsetStartToCorrectPosition();
                    }
                }

                @Override
                public void onScrollEnd() {
                    //滑动到头部
                    if (mCurrentPhase > 0) {
                        animateOffsetEndToCorrectPosition();
                    }
                }

                @Override
                public boolean isCanBubble() {
                    //是否能冒泡
                    boolean canBubble = canDragNextPhrase();
                    MDLog.d(LogTag.Bubble.Bubble, "isCanBubble:" + canBubble);
                    return canBubble;
                }

                @Override
                public void onOffset(int offset) {
                    //设置头部距离
                    mPhaseOffset = offset;
                }

                @Override
                public void stopRefresh() {
                    //停止刷新状态
                    stopRefreshing();
                }

                @Override
                public int currentPhase() {
                    return mCurrentPhase;
                }

                @Override
                public void onUpdatePercent(float percent, int animType) {
                    if (userdata != null) {
                        userdata.callUpdatePercent(percent, animType);
                    }
                }

            });
        }
    }

    @Override
    protected RefreshStyle initRefreshStyle() {
        return RefreshStyle.LUA;
    }

    @Override
    public UDBubbleRefreshTableView getUserdata() {
        return userdata;
    }

    @Override
    public void setViewLifeCycleCallback(ViewLifeCycleCallback cycleCallback) {
        this.cycleCallback = cycleCallback;
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (cycleCallback != null) {
            cycleCallback.onAttached();
        }
    }

    @Override
    public int getCurrentState() {
        return loadViewDelegete.getCurrentState();
    }

    @Override
    public void setContentOffset(Point p) {
        getRecyclerView().scrollBy((int) p.getXPx() - getRecyclerView().computeHorizontalScrollOffset(), (int) p.getYPx() - getRecyclerView().computeVerticalScrollOffset());
    }

    @Override
    public Point getContentOffset() {
        return new Point(DimenUtil.pxToDpi(getRecyclerView().computeHorizontalScrollOffset()), DimenUtil.pxToDpi(getRecyclerView().computeVerticalScrollOffset()));
    }

    @Override
    public void smoothScrollTo(Point p) {
        getRecyclerView().smoothScrollBy((int) p.getXPx() - getRecyclerView().computeHorizontalScrollOffset(), (int) p.getYPx() - getRecyclerView().computeVerticalScrollOffset());
    }

    @Override
    public void startLoading() {
        loadViewDelegete.startLoading();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (cycleCallback != null) {
            cycleCallback.onDetached();
        }
    }
    //</editor-fold>

    //<editor-fold desc="IBubbleRefreshRecyclerView">
    @Override
    public RecyclerView getRecyclerView() {
        return recyclerView;
    }

    @Override
    public void setRefreshEnable(boolean enable) {
        setEnabled(enable);
        if (enable) {
            setOnRefreshListener(this);
        }
    }

    @Override
    public boolean isRefreshEnable() {
        return isEnabled();
    }

    @Override
    public boolean isRefreshing() {
        return false;
    }

    @Override
    public void startRefreshing() {
        recyclerView.scrollToPosition(0);
        setRefreshing(true, 0);
        MainThreadExecutor.post(new Runnable() {
            @Override
            public void run() {
                onRefresh(0);
            }
        });
    }

    @Override
    public void stopRefreshing() {
        setRefreshing(false);
        loadViewDelegete.setEnable(loadEnable);
    }

    @Override
    public void setLoadEnable(boolean enable) {
        if (loadEnable == enable)
            return;
        loadEnable = enable;
        loadViewDelegete.setEnable(enable);
    }

    @Override
    public boolean isLoadEnable() {
        return loadEnable;
    }

    @Override
    public boolean isLoading() {
        return isLoading;
    }

    @Override
    public void stopLoading() {
        isLoading = false;
        loadViewDelegete.getLoadView().stopAnim();
    }

    @Override
    public void noMoreData() {
        loadViewDelegete.noMoreData();
    }

    @Override
    public void resetLoading() {
        loadViewDelegete.resetLoading();
    }

    @Override
    public void loadError() {
        loadViewDelegete.loadError();
    }

    public void setSizeChangedListener(SizeChangedListener sizeChangedListener) {
        this.sizeChangedListener = sizeChangedListener;
    }
    //</editor-fold>

    //<editor-fold desc="OnLoadListener">

    @Override
    public void onLoad() {
        if (isLoading)
            return;
        isLoading = true;
        userdata.callbackLoading();
    }
    //</editor-fold>

    //<editor-fold desc="OnRefreshListener">

    @Override
    public void onRefresh(int currentPhase) {
        MDLog.d(LogTag.Bubble.Bubble, "onRefresh:" + currentPhase);
        loadViewDelegete.setEnable(false);
        userdata.callbackRefresh();
        Map params = new HashMap();
        userdata.callOnRefresh(currentPhase, params);//0表示是1段即普通刷新，1表示2段即拉高的刷新
    }

    private void onRefreshWithParams(int currentPhase, int data) {
        MDLog.d(LogTag.Bubble.Bubble, "onRefreshWithParams:" + currentPhase + ",data:" + data);
        loadViewDelegete.setEnable(false);
        userdata.callbackRefresh();
        Map params = new HashMap();
        params.put("selectedId", data);
        userdata.callOnRefresh(currentPhase, params);//0表示是1段即普通刷新，1表示2段即拉高的刷新
    }
    //</editor-fold>

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        if (sizeChangedListener != null) {
            sizeChangedListener.onSizeChanged(w, h);
        }
    }

    @Override
    public void refreshBubbleEnable(boolean enable) {
        //TODO 客户端二段功能，待实现 9.0———2019.3.15
        if (enable) {
            setMaxSpinnerPhase(1);
        } else {
            setMaxSpinnerPhase(0);
        }
    }

    @Override
    public void setRefreshBgColor(int color) {
        if (mRefreshView instanceof LuaRefreshView) {
            ((LuaRefreshView) mRefreshView).setBgColor(color);
        }
    }

    @Override
    public void setRefreshSvgaPath(String enterPath, String loopPath) {
        if (mRefreshView instanceof LuaRefreshView) {
            ((LuaRefreshView) mRefreshView).setSvgaPath(enterPath, loopPath);
        }
    }

    public boolean canDragNextPhrase() {
        return userdata.callCanBubbleRefresh();
    }
}
