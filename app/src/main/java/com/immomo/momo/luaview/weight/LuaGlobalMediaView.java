package com.immomo.momo.luaview.weight;

import android.content.Context;
import android.graphics.SurfaceTexture;
import android.net.Uri;
import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.ViewParent;

import com.immomo.android.module.fundamental.FundamentalInitializer;
import com.immomo.framework.utils.UIUtils;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.momo.R;
import com.immomo.momo.feed.player.ExoTextureLayout;
import com.immomo.momo.feed.player.IGlobalIJKPlayer;
import com.immomo.momo.feed.player.IMediaPlayer;
import com.immomo.momo.feed.player.ITextureReusablePlayer;
import com.immomo.momo.util.FoldScreenUtil;


/**
 * author: hongming.wei
 * data: 2023/2/2
 */
public class LuaGlobalMediaView extends ExoTextureLayout {

    private int playBackSate = IMediaPlayer.STATE_IDLE;
    private boolean playWhenReady = false;
    private int mInitialX;
    private int mInitialY;
    private IGlobalIJKPlayer ijkPlayer;
    private IGlobalMediaEventListener mMediaEventListener;
    private boolean isAttachedToWindow;
    private Object taskTag = hashCode();
    private int playCount = 0;
    private final Runnable updateProgressAction = this::updateProgress;


    public LuaGlobalMediaView(Context context) {
        super(context);
    }

    public LuaGlobalMediaView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public LuaGlobalMediaView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void videoPlay(Uri uri, String feedId, boolean isAutoPlay, String videoSource, String eventId, String secondType, String tagDesc, String luaParams) {
        initIjkPlayer();
        ijkPlayer.setLoopPlay(true);
        ijkPlayer.prepareAndSeek(uri, feedId, isAutoPlay, videoSource, eventId, secondType, tagDesc, luaParams, true);
        acquireVideoTexture(getContext(), ijkPlayer);
        ijkPlayer.resume();
        ijkPlayer.setSilentMode(false);
    }

    public void audioPlay(Uri uri, String feedId, boolean isAutoPlay, String videoSource, String eventId, String secondType, String tagDesc, String luaParams) {
        initIjkPlayer();
        ijkPlayer.setLoopPlay(true);
        ijkPlayer.prepareAndSeek(uri, feedId, isAutoPlay, videoSource, eventId, secondType, tagDesc, luaParams, false);
        ijkPlayer.resume();
        ijkPlayer.setSilentMode(false);
    }

    public void silenceMode(boolean b) {
        initIjkPlayer();
        ijkPlayer.setSilentMode(b);
    }

    public void setMediaViewSize(float screenRatio) {
        if (screenRatio > 0.8f) {
            int w = UIUtils.getScreenWidth();
            setTextureSize(w, (int) (w / screenRatio));
        } else if (FoldScreenUtil.INSTANCE.isWideScreen()) {
            int h = UIUtils.getScreenHeight() - UIUtils.getPixels(getContext().getResources().getDimension(R.dimen.maintabbottomtabbar));
            setTextureSize((int) (h * screenRatio), h);
        } else {
            setTextureSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        }
    }


    public void setLoopPlay(boolean loopPlay) {
        initIjkPlayer();
        ijkPlayer.setLoopPlay(loopPlay);
    }


    public void resume() {
        initIjkPlayer();
        ijkPlayer.resume();
    }

    public void pause() {
        initIjkPlayer();
        ijkPlayer.pause();
    }

    public void release() {
        initIjkPlayer();
        ijkPlayer.release();
        ijkPlayer = null;
    }

    public Uri getCurrentUri(){
        initIjkPlayer();
        return ijkPlayer.getCurrentUri();
    }

    public void clearPlayPosition(){
        initIjkPlayer();
        ijkPlayer.clearPlayPosition();
    }

    public void seekTo(long positionMs) {
        initIjkPlayer();
        ijkPlayer.seekTo(positionMs);
    }

    public long getDuration() {
        initIjkPlayer();
        return ijkPlayer.getDuration();
    }

    public long getCurrentPosition(){
        initIjkPlayer();
        return ijkPlayer.getCurrentPosition();
    }


    private void initIjkPlayer(){
        if (ijkPlayer == null) {
            ijkPlayer = FundamentalInitializer.Companion.getGlobalIjkPlayer();
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        ViewParent parentView = getParent();
        ViewConfiguration viewConfiguration = ViewConfiguration.get(getContext());
        int touchSlop = viewConfiguration.getScaledTouchSlop();

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mInitialX = (int) event.getX();
                mInitialY = (int) event.getY();
                if (parentView != null) {
                    parentView.requestDisallowInterceptTouchEvent(true);
                }
                break;
            case MotionEvent.ACTION_MOVE:
                int actionX = (int) event.getX();
                int actionY = (int) event.getY();
                int deltaX = Math.abs(actionX - mInitialX);
                int deltaY = Math.abs(actionY - mInitialY);

                if ((deltaY > touchSlop) && parentView != null) {
                    parentView.requestDisallowInterceptTouchEvent(false);
                }
                break;
            default:
                break;
        }
        gestureDetector.onTouchEvent(event);
        return true;
    }

    GestureDetector gestureDetector = new GestureDetector(getContext(), new GestureDetector.SimpleOnGestureListener() {

        @Override
        public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
            return false;
        }

        @Override
        public boolean onSingleTapConfirmed(MotionEvent e) {
            if (mMediaEventListener != null) {
                mMediaEventListener.onSingleTapConfirmed();
            }
            return true;
        }

        @Override
        public boolean onDoubleTap(MotionEvent e) {
            if (mMediaEventListener != null) {
                mMediaEventListener.onDoubleTap();
            }
            return true;
        }
    });

    @Override
    public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {
        this.playBackSate = playbackState;
        this.playWhenReady = playWhenReady;
        switch (playbackState) {
            case IMediaPlayer.STATE_IDLE:
                if (mMediaEventListener != null) {
                    mMediaEventListener.setToIdleState();
                }
                break;
            case IMediaPlayer.STATE_ENDED:
                ++playCount;
                if (mMediaEventListener != null) {
                    mMediaEventListener.setToStateEnded(playCount);
                }
                break;
            case IMediaPlayer.STATE_BUFFERING:
                if (mMediaEventListener != null) {
                    mMediaEventListener.setToBufferState();
                }
                break;
            case IMediaPlayer.STATE_READY:
                videoReady = true;
                if (mMediaEventListener != null) {
                    mMediaEventListener.setToPlayState();
                }
                break;
            default:
                break;
        }

        if (mMediaEventListener != null) {
            mMediaEventListener.onPlayerStateChanged(playWhenReady, playbackState);
        }
        updateProgress();
    }

    @Override
    public void acquireVideoTexture(Context context, ITextureReusablePlayer exoPlayer) {
        super.acquireVideoTexture(context, exoPlayer);
        isAttachedToWindow = true;
        updateProgress();
    }

    @Override
    public void onSurfaceTextureUpdated(SurfaceTexture surface) {
        super.onSurfaceTextureUpdated(surface);
        if (mMediaEventListener != null) {
            mMediaEventListener.onSurfaceUpdated();
        }
    }

    @Override
    public void releaseVideoTexture() {
        super.releaseVideoTexture();
        isAttachedToWindow = false;
        MomoMainThreadExecutor.cancelSpecificRunnable(taskTag, updateProgressAction);
    }

    public void setMediaEventListener(IGlobalMediaEventListener listener) {
        mMediaEventListener = listener;
    }


    private void updateProgress() {
        if (!isAttachedToWindow) {
            return;
        }
        long position = player == null ? 0 : player.getCurrentPosition();
        long duration = player == null ? 0 : player.getDuration();
        if (ijkPlayer != null && ijkPlayer.getCurrentUri() != null && ijkPlayer.getCurrentUri().toString().contains("privacy.momocdn.com") && position > 2000) {
            ijkPlayer.putPlayPosition(ijkPlayer.getCurrentUri(), position);
        }

        MomoMainThreadExecutor.cancelSpecificRunnable(taskTag, updateProgressAction);
        int playbackState = player == null ? IMediaPlayer.STATE_IDLE : player.getPlaybackState();
        if (playbackState != IMediaPlayer.STATE_IDLE) {
            long delayMs;
            if (player.getPlayWhenReady() && playbackState == IMediaPlayer.STATE_READY) {
                delayMs = 16;
            } else {
                delayMs = 1000;
            }
            MomoMainThreadExecutor.postDelayed(taskTag, updateProgressAction, delayMs);
        }
        if (mMediaEventListener != null) {
            mMediaEventListener.onPlayPosition(position, duration);
        }
    }

    public boolean isPlaying() {
        return playBackSate == IMediaPlayer.STATE_READY && playWhenReady;
    }
}
