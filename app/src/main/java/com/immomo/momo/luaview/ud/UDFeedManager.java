package com.immomo.momo.luaview.ud;

import static com.immomo.android.module.feedlist.presentation.fragment.FriendFeedListLuaFragment.FRIEND_REFRESH_FEED_LIST;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LifecycleOwnerKt;

import com.cosmos.mdlog.MDLog;
import com.immomo.android.module.feed.share.UnFollowTask;
import com.immomo.android.module.feed.statistics.EVAction;
import com.immomo.android.module.feed.statistics.EVPage;
import com.immomo.android.module.feedlist.data.api.mapper.theme.FeedListThemeMapperKt;
import com.immomo.android.module.feedlist.data.api.response.theme.FeedListTheme;
import com.immomo.android.module.feedlist.domain.model.style.common.MicroVideoFeedModel;
import com.immomo.android.module.feedlist.presentation.feedUtils.FeedListMessageHelper;
import com.immomo.android.module.fundamental.FundamentalInitializer;
import com.immomo.android.router.momo.MomoRouter;
import com.immomo.android.router.momo.business.GuestLogParams;
import com.immomo.android.router.momo.business.GuestRouter;
import com.immomo.android.router.momo.business.VideoPlayerRouter;
import com.immomo.android.router.momo.business.feed.FeedConfigs;
import com.immomo.android.router.momo.business.statistics.SayHiSourceRouter;
import com.immomo.android.router.momo.util.LogActionUtilRouter;
import com.immomo.mls.LuaViewManager;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmstatistics.event.ClickEvent;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoKit;
import com.immomo.momo.android.view.dialog.MAlertDialog;
import com.immomo.momo.apt.FeedAppConfigV1Getter;
import com.immomo.momo.eventbus.DataEvent;
import com.immomo.momo.feed.FeedStepHelper;
import com.immomo.momo.feed.MicroVideoPlayLogger;
import com.immomo.momo.feed.bean.BasePublishConstant;
import com.immomo.momo.feed.player.IGlobalIJKPlayer;
import com.immomo.momo.feed.service.VideoService;
import com.immomo.momo.feed.util.LuaFeedParamHelper;
import com.immomo.momo.homepage.HomeUniverseGuideUtils;
import com.immomo.momo.homepage.helper.LikeSettingHelper;
import com.immomo.momo.homepage.model.LikeSettingInfo;
import com.immomo.momo.imagefactory.presenter.FollowUtils;
import com.immomo.momo.imagefactory.presenter.FollowUtils2;
import com.immomo.momo.innergoto.matcher.MicroVideoMatcher;
import com.immomo.momo.luaview.LuaViewActivity;
import com.immomo.momo.maintab.MaintabActivity;
import com.immomo.momo.publish.view.PublishGroupFeedActivity;
import com.immomo.momo.service.feeddraft.DraftPublishService;

import org.json.JSONObject;
import org.luaj.vm2.Globals;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import de.greenrobot.event.EventBus;
import info.xudshen.android.appasm.AppAsm;

@LuaClass(isStatic = true)
public class UDFeedManager {
    public static final String LUA_CLASS_NAME = "FeedManager";
    private static LocalUnFollowTask unFollowTask;

    @LuaBridge
    public static boolean followFeedListInMessageType() {
        return HomeUniverseGuideUtils.INSTANCE.universeTab4User();
    }

    @LuaBridge
    public static Map<String,String> getProfileFeedPublishGuideConfig() {
        HashMap<String, String> map = new HashMap<>();
        map.put("title",FeedAppConfigV1Getter.get().title());
        map.put("subTitle",FeedAppConfigV1Getter.get().subTitle());
        map.put("pic",FeedAppConfigV1Getter.get().guidePic());
        map.put("showTimes",FeedAppConfigV1Getter.get().showTimes());
        map.put("showTime",FeedAppConfigV1Getter.get().showTime());
        map.put("source",FeedAppConfigV1Getter.get().source());
        map.put("gotoUrl",FeedAppConfigV1Getter.get().gotoUrl());
        map.put("svga", FeedAppConfigV1Getter.get().svga());
        return map;
    }

    @LuaBridge
    public static boolean needAutoPlayVideo() {
        return AppAsm.getRouter(VideoPlayerRouter.class).readVideoPlayStatus();
    }

    @LuaBridge
    public static String getRealAction(String action) {
        if (!TextUtils.isEmpty(action) && action.startsWith("[") && action.endsWith("\"]")){
           return action.substring(2, action.indexOf("]") + 1);
        }
        return action;
    }

    @LuaBridge
    public static boolean followHaveNoRead() {
        return FeedListMessageHelper.INSTANCE.getFeedUnreadCount() > 0;
    }

    @LuaBridge
    public static void releaseVideo() {
        MomoMainThreadExecutor.cancelAllRunnables("releaseVideo");

        MomoMainThreadExecutor.postDelayed("releaseVideo", new Runnable() {
            @Override
            public void run() {
                IGlobalIJKPlayer exoPlayer = FundamentalInitializer.Companion.getGlobalIjkPlayer();
                exoPlayer.release();
            }
        },100);
    }

    @LuaBridge
    public static Map<String,String> nearbyFeedParam(Boolean isFirstRequest) {
        return LuaFeedParamHelper.Companion.buildRequestParam(isFirstRequest);
    }

    @LuaBridge
    public static int feedListAutoRefreshInterval() {
        return FeedAppConfigV1Getter.get().frontPageNearbyFeedRefreshTime();
    }

    @LuaBridge
    public static String getSourceInfo() {
        return MicroVideoMatcher.getMicroVideoLogSourceForLike(FeedStepHelper.INSTANCE.getPreFeedStepExcludeProfile());
    }

    @LuaBridge
    public static void showVideoPlayHintDialog(boolean isClick) {
        VideoService.videoPlay();
    }

    @LuaBridge
    public static boolean isGuestMode() {
        return AppAsm.getRouter(MomoRouter.class).isGuestMode();
    }

    @LuaBridge
    public static boolean gotoLogin(Map<String, String> param) {
        Context context = MomoKit.getTopActivity();
        if (context != null && !MomoKit.getTopActivity().isFinishing()) {
            if (param != null) {
                GuestLogParams guestLogParams = new GuestLogParams();
                guestLogParams.feedId = param.getOrDefault("feedId", "");
                guestLogParams.userId = param.getOrDefault("momoId", "");
                String pageSource = param.getOrDefault("pageSource", "");
                String loginSource = param.getOrDefault("loginSource", "");
                AppAsm.getRouter(GuestRouter.class).handleNewGuest(context, "", guestLogParams, loginSource);
                return true;
            }
        }
        return false;
    }

    @LuaBridge
    public static ArrayList<Map> getLikeConfigArr() {
        ArrayList<Map> maps = new ArrayList<>();

        List<LikeSettingInfo> infos = LikeSettingHelper.Companion.getInstance().getLikeSetting();
        for (LikeSettingInfo info : infos) {
            Map<String, String> map = new HashMap<>();
            map.put("animaion", info.getAnimUrl());
            map.put("cancel_animation", info.getCancelAnimUrl());
            map.put("icon_after", info.getIcLike());
            map.put("icon_before", info.getIcUnLike());
            map.put("icon_text", info.getZanText());
            map.put("id", info.getId());
            maps.add(map);
        }
        return maps;
    }


    @LuaBridge
    public static boolean shouldShowFollowGuide(String feedId) {
        boolean showGuideByFeedId = FollowUtils.isFollowShowGuideByFeedId(feedId);
        if (showGuideByFeedId) {
            FollowUtils.saveFollowGuideShowedByFeedId(
                    feedId
            );
        }
        return showGuideByFeedId;
    }

    /**
     * 广告theme打点
     *
     * @param index
     * @param actions
     */
    @LuaBridge
    public static void feedListAdLog(int index, List<String> actions) {
        Map<String, String> params = new HashMap<>();
        if (index != -1) {
            params.put("_cpos", String.valueOf(index));
        }
        AppAsm.getRouter(LogActionUtilRouter.class).executeLogs(MomoKit.getContext(), actions, params);

    }

    @LuaBridge
    public static void follow(String momoId, Map<String, String> map, LVCallback callback) {
        if (map != null) {
            ClickEvent.create().page(EVPage.NearbyFeed.Detail).action(EVAction.Top.Follow)
                    .putExtra("avatar_id", momoId)
                    .putExtra("doc_id", map.get("feedId"))
                    .requireId("5136")
                    .submit();
        }
        HashMap<String, String> response = new HashMap<>();
        response.put("follow", momoId);
        String sourceInfo = AppAsm.getRouter(SayHiSourceRouter.class)
                .buildSayHiSourceByParams(FeedStepHelper.INSTANCE.getFeedUpStepConfig().getLogSource(), "", "");
        Activity topActivity = MomoKit.getTopActivity();
        if (topActivity instanceof LuaViewActivity || topActivity instanceof MaintabActivity) {
            Activity activity = topActivity;
            FollowUtils2.followCurrentUser(LifecycleOwnerKt.getLifecycleScope((LifecycleOwner) activity),
                    momoId, "", "", sourceInfo, relation -> {
                        response.put("relation", relation);
                        MomoMainThreadExecutor.post(() -> callback.call(true, response));
                        return null;
                    });
        }

    }

    @LuaBridge
    public static void unfollow(String momoId, LVCallback callback) {
        cancelTask(unFollowTask);
        unFollowTask = new LocalUnFollowTask(momoId, callback);
        MomoTaskExecutor.executeUserTask("UD_FEED_MANAGER", unFollowTask);
    }

    private static void cancelTask(MomoTaskExecutor.Task task) {
        if (task != null && !task.isCancelled()) {
            task.cancel(true);
        }
    }

    static class LocalUnFollowTask extends UnFollowTask {
        LVCallback callback = null;

        public LocalUnFollowTask(String userid, LVCallback callback) {
            super(userid);
            this.callback = callback;
        }

        @Override
        protected void onTaskSuccess(Object result) {
            super.onTaskSuccess(result);
            if (callback != null) {
                callback.call(true);
            }
        }
    }


    @LuaBridge
    public static void logFeedReadLook(String jsonFeed) {
        try {
            JSONObject feedObj = new JSONObject(jsonFeed);
            FeedListTheme<?> theme = FeedListTheme.Companion.getMoshi().adapter(FeedListTheme.class).fromJson(jsonFeed);
            MicroVideoFeedModel feed = (MicroVideoFeedModel) FeedListThemeMapperKt.parseFeedTheme2Model(theme);
            if (feed != null) {
                MicroVideoPlayLogger.getInstance().logVideoPlayed(feed.getFeedId(), true, MicroVideoMatcher.getMicroVideoLogSource(), feed.getLogMap());
            }

        } catch (Exception e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
    }

    @LuaBridge
    public static void refreshFollowFeed(Map<String, Object> param) {
        if (param != null) {
            EventBus.getDefault().post(new DataEvent<>(FRIEND_REFRESH_FEED_LIST, param));
        }
    }

    @LuaBridge
    public static float getFontScale(Globals globals) {
        Context context = ((LuaViewManager) globals.getJavaUserdata()).context;
        if (context != null
                && context.getResources() != null
                && context.getResources().getConfiguration() != null
                && context.getResources().getConfiguration().fontScale >= 1) {
            return context.getResources().getConfiguration().fontScale;
        }
        return 1.0f;
    }

    @LuaBridge
    public static void gotoReleaseGroupPostWithGroupId(String groupId) {
        checkDraft(groupId);
    }

    private static void checkDraft(String groupId) {
        Activity activity = MomoKit.getTopActivity();
        if(activity == null) {return;}
        if (checkHasGroupSpaceFeedDraftSendFailed(groupId)) {
            MAlertDialog dialog = MAlertDialog.makeConfirm(
                    activity, "你之前有一条动态未发布成功，是否重新发布？",
                    (dialog1, which) -> resendDraft(activity, groupId), (dialog12, which) -> cancelResendDraft(groupId));
            dialog.setSupportDark(true);
            dialog.show();
        } else {
            Intent intent = new Intent(activity, PublishGroupFeedActivity.class);
            intent.putExtra("gid", groupId);
            intent.putExtra(BasePublishConstant.KEY_MORE_ONCREATE, true);
            intent.putExtra(PublishGroupFeedActivity.KEY_PUBLISH_GROUP_FEED_FROM_SOURCE,
                    FeedConfigs.PUBLISH_GROUP_FEED_FROM.GROUP_FEEDS);
            activity.startActivity(intent);
        }
    }

    private static boolean checkHasGroupSpaceFeedDraftSendFailed(String gid) {
        return DraftPublishService.getInstance().getFailCountByType(
                DraftPublishService.PublishDraft.TYPE_GROUPFEED, gid) != 0;
    }

    private static void cancelResendDraft(String gid) {
        DraftPublishService.PublishDraft draft = DraftPublishService.getInstance().getFailDraftByType(
                DraftPublishService.PublishDraft.TYPE_GROUPFEED, gid);
        if (draft != null) {
            DraftPublishService.getInstance().removeNotify(draft.id);
            DraftPublishService.getInstance().deletedDraftById(draft.id);
        }
    }

    private static void resendDraft(Activity activity, String gid) {
        DraftPublishService.PublishDraft draft = DraftPublishService.getInstance().getFailDraftByType(
                DraftPublishService.PublishDraft.TYPE_GROUPFEED, gid);
        if (draft != null && draft.type == DraftPublishService.PublishDraft.TYPE_GROUPFEED) {
            Intent intent = new Intent(activity, PublishGroupFeedActivity.class);
            intent.putExtra(DraftPublishService.PublishDraft.KEY_DRAFTID, String.valueOf(draft.id));
            intent.putExtra(DraftPublishService.PublishDraft.KEY_DRAFT, draft.draft);
            intent.putExtra(PublishGroupFeedActivity.KEY_PUBLISH_GROUP_FEED_FROM_SOURCE,
                    FeedConfigs.PUBLISH_GROUP_FEED_FROM.GROUP_FEED_DARF);
            activity.startActivity(intent);
        }
    }
}
