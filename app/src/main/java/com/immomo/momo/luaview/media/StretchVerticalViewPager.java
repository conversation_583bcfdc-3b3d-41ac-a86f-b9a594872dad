package com.immomo.momo.luaview.media;

import android.animation.ValueAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.Interpolator;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public class StretchVerticalViewPager extends VerticalViewPager implements ValueAnimator.AnimatorUpdateListener {
    public static final int STRETCH_NONE = 0x00;
    /**
     * top stretch
     */
    public static final int STRETCH_TOP = 0x01;
    /**
     * bottom stretch
     */
    public static final int STRETCH_BOTTOM = 0x10;
    /**
     * both stretch
     */
    public static final int STRETCH_BOTH = 0x11;
    /**
     * refresh priority GT stretch
     */
    private int refreshModel = STRETCH_NONE;
    private int stretchModel = STRETCH_BOTH;
    private int directionModel = STRETCH_NONE;
    private int lastX = 0;
    private int lastY = 0;
    private int distanceY = 0;
    private int expectDistance;
    private boolean stretchStatus = false;
    private OnStretchListener listener;
    private final ValueAnimator anim = ValueAnimator.ofInt(0, 1);
    private int activePointerId;
    /**
     * first touch down,current scrollY vaule
     */
    private int firstScrollY = 0;
    private int lastTotalDistance = 0;
    /**
     * 回弹动画正在播放
     */
    private boolean isAnimalRunning = false;
    /**
     * 触摸在边界位置合法
     */
    private boolean validTouch = false;
    private View topView, bottomView;
    private boolean isFirstMove, isMoveY;

    public StretchVerticalViewPager(@NonNull Context context) {
        this(context, null);
    }

    public StretchVerticalViewPager(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        anim.setInterpolator(new AccelerateDecelerateInterpolator());
        anim.setDuration(100);
    }

    public void setRefreshView(View topView, View bottomView) {
        this.topView = topView;
        this.bottomView = bottomView;
        if (topView != null) {
            refreshModel |= STRETCH_TOP;
        }
        if (bottomView != null) {
            refreshModel |= STRETCH_BOTTOM;
        }
    }

    public int getRefreshModel() {
        return refreshModel;
    }

    /**
     * set Stretch model，default is: opened
     *
     * @param model one of {@link #STRETCH_BOTH},{@link #STRETCH_TOP},{@link #STRETCH_BOTTOM},{@link #STRETCH_NONE}
     */
    public void setStretchModel(int model) {
        this.stretchModel = model;
    }

    public int getStretchModel() {
        return stretchModel;
    }

    public void setOnStretchListener(OnStretchListener l) {
        listener = l;
    }

    public void setAnimInterpolator(Interpolator interpolator) {
        this.anim.setInterpolator(interpolator);
    }

    public void setAnimDuration(int duration) {
        this.anim.setDuration(duration);
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);
        final int count = getChildCount();//重新放置位置
        View child = getChildAt(count - 1);
        if (child != null && (topView == child || bottomView == child)) {
            int height = getMeasuredHeight();
            int top = expectDistance + (child == topView ? 0 : height);
            int bottom = top + height;
            child.layout(0, top, getMeasuredWidth(), bottom);
        }
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        int actionId = ev.getAction() & MotionEvent.ACTION_MASK;
        switch (actionId) {
            case MotionEvent.ACTION_DOWN://0
                validTouch = !isAnimalRunning;
                if (validTouch) {
                    firstScrollY = getScrollY();
                    int height = getHeight();
                    int round = (int) Math.round(1.0 * firstScrollY / height);//fixed scrollY distance
                    expectDistance = round * height;
                }
                isFirstMove = true;
                isMoveY = false;
                lastX = (int) ev.getX();
                lastY = (int) ev.getY();
                activePointerId = ev.getPointerId(0);
                break;
            case MotionEvent.ACTION_MOVE:
                final int pointerIndex = ev.findPointerIndex(activePointerId);
                if (null == getAdapter() || -1 == pointerIndex) {
                    break;
                }
                int currentY = (int) ev.getY(pointerIndex);
                distanceY = currentY - lastY;
                /** swip by horizontal*/
                if (isFirstMove) {
                    int distanceX = (int) ev.getX(pointerIndex) - lastX;
                    if (distanceY != 0 && distanceY != distanceX) {
                        isFirstMove = false;
                        isMoveY = Math.abs(distanceY) > Math.abs(distanceX);
                    }
                }
                if (isMoveY) {
                    lastY = currentY;
                    if (!stretchStatus) {
                        stretchStatus = validTouch && getStretchEnable(distanceY);
                    }
                }
                break;
        }
        return super.dispatchTouchEvent(ev);
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        if (stretchStatus) {
            int actionId = ev.getAction() & MotionEvent.ACTION_MASK;
            switch (actionId) {
                case MotionEvent.ACTION_MOVE://2
                    if (null != getAdapter() && -1 != ev.findPointerIndex(activePointerId)) {
                        scrollByMove(distanceY);
                    }
                    return true;
                case MotionEvent.ACTION_UP://1
                case MotionEvent.ACTION_CANCEL://3
                    if (validTouch) {
                        validTouch = false;
                        scrollEndMove();
                        return true;
                    }
                    break;
                case MotionEvent.ACTION_POINTER_DOWN://5
                    final int index = ev.getActionIndex();
                    lastX = (int) ev.getX(index);//multi-touch
                    lastY = (int) ev.getY(index);//multi-touch
                    activePointerId = ev.getPointerId(index);
                    return true;
            }
        }
        return super.onTouchEvent(ev);
    }

    private boolean getStretchEnable(int distanceY) {
        boolean enable = true;
        boolean refreshTop = (STRETCH_TOP & refreshModel) > 0;
        boolean refreshBottom = (STRETCH_BOTTOM & refreshModel) > 0;
        boolean stretchTop = (STRETCH_TOP & stretchModel) > 0;
        boolean stretchBottom = (STRETCH_BOTTOM & stretchModel) > 0;
        if ((stretchTop || refreshTop) && 0 == getCurrentItem() && distanceY > 0) {
            directionModel = STRETCH_TOP;//left edge and distanceX GT 0
        } else if ((stretchBottom || refreshBottom) && getAdapter().getCount() == getCurrentItem() + 1 && distanceY < 0) {
            directionModel = STRETCH_BOTTOM;//right edge and distanceX LT 0
        } else {
            directionModel = STRETCH_NONE;
            enable = false;
        }
        return enable;
    }

    private void scrollByMove(int y) {
        addTopBottomEdge();
        int total = 2 * getHeight() / 10;
        int scroll = Math.abs(getScrollY() - firstScrollY);
        double dy = Math.signum(-y) * (scroll > 0.9 * total ? (scroll > total ? 0 : 1) : 0.25 * Math.abs(y));
        scrollBy(0, (int) dy);
        if (null != listener) {
            listener.onScrolled(directionModel, getScrollDistance());
        }
    }

    private void addTopBottomEdge() {
        if (directionModel == STRETCH_TOP && topView != null && topView.getParent() == null) {
            addEdgeView(topView);
        } else if (directionModel == STRETCH_BOTTOM && bottomView != null && bottomView.getParent() == null) {
            addEdgeView(bottomView);
        }
    }

    private void addEdgeView(View view) {
        LayoutParams p = new LayoutParams();
        p.isDecor = true;
        addView(view, p);
    }

    private void scrollEndMove() {
        final int scrollDistance = getScrollDistance();
        if (null != listener) {
            listener.onRefresh(directionModel, Math.abs(scrollDistance));
        }
        refreshDoneAnim();
    }

    private void refreshDoneAnim() {
        isAnimalRunning = true;
        anim.addUpdateListener(this);
        anim.start();
    }

    @Override
    public void onAnimationUpdate(ValueAnimator animation) {
        float percent = animation.getAnimatedFraction();
        int distance = getScrollDistance();
        int firstTotalDistance = distance + lastTotalDistance;
        int dy = (int) ((percent > 1.0f ? 1.0 : percent) * firstTotalDistance) - lastTotalDistance;
        lastTotalDistance += dy;
        scrollBy(0, dy);
        if (1.0f <= percent || distance == 0) {
            anim.removeAllUpdateListeners();
            if (null != listener) listener.onRelease(directionModel);
            removeView(topView);
            removeView(bottomView);
            lastTotalDistance = 0;
            isAnimalRunning = false;
            stretchStatus = false;
        }
    }

    private int getScrollDistance() {
        return expectDistance - getScrollY();
    }
}