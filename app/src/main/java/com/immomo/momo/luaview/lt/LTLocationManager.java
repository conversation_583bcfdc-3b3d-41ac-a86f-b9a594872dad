package com.immomo.momo.luaview.lt;

import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.location.Location;
import android.net.Uri;
import android.provider.Settings;

import com.immomo.android.module.feed.util.LocationFuncCheckUtils;
import com.immomo.android.router.momo.PrivacyRouter;
import com.immomo.framework.location.ILocationMemoryCache;
import com.immomo.framework.location.LocaterType;
import com.immomo.framework.location.LocationCallBack;
import com.immomo.framework.location.LocationClient;
import com.immomo.framework.location.LocationResultCode;
import com.immomo.framework.location.LocationUtil;
import com.immomo.framework.location.UserLocationUtils;
import com.immomo.mls.MLSEngine;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.fun.globals.UDLuaView;
import com.immomo.mls.fun.other.Point;
import com.immomo.mls.fun.ud.UDMap;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmutil.app.AppContext;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.moarch.account.AccountKit;
import com.immomo.momo.LocationClientSetter;
import com.immomo.momo.MomoKit;
import com.immomo.momo.android.view.dialog.MAlertDialog;
import com.immomo.momo.mvp.maintab.mainimpl.appconfig.apt.LocationAppConfigGetter;
import com.immomo.momo.permission.PermissionUtil;
import com.immomo.momo.util.DeviceUtils;
import com.immomo.momo.util.StringUtils;
import com.immomo.momo.util.rom.MiuiUtils;

import java.util.HashMap;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import info.xudshen.android.appasm.AppAsm;

/**
 * Created by XiongFangyu on 2018/7/6.
 */
@LuaClass(isStatic = true)
public class LTLocationManager {
    public static final String LUA_CLASS_NAME = "LocationManager";

    //<editor-fold desc="API">
    @LuaBridge
    public static Point userLocation() {
        Point point = new Point();

        if (LocationAppConfigGetter.get().luaUserLocationSwitch() == 1) {
            point.setX((float) UserLocationUtils.getLatitude());
            point.setY((float) UserLocationUtils.getLongitude());
        } else {
            Location location = LocationClient.getLastknownLocation();
            if (location != null) {
                point.setX((float) location.getLatitude());
                point.setY((float) location.getLongitude());
            }
        }
        return point;
    }
    //</editor-fold>

    /**
     * 重新获取当前位置，后返回回调
     *
     * @param callback
     */
    @LuaBridge
    public static void updateLocation(final LVCallback callback) {
        getLocationCacheWithType(LocationClientSetter.LocationMemoryCache.TYPE_OTHER, callback);
    }

    /**
     * 使用缓存策略，获取当前位置，并返回回调，如果超过缓存间隔时间，则重新获取当前位置
     *
     * @param type
     * @param callback
     */
    @LuaBridge
    public static void getLocationCacheWithType(int type, final LVCallback callback) {
        if (!checkPrivacyValid()) {
            return;
        }
        final Map<String, Object> map = new HashMap<>();
        try {
            LocationClient.getLocationInQueue(type, new LocationCallBack() {
                @Override
                public void callback(final Location loc, final boolean correctLocType, final LocationResultCode resultCode, final LocaterType locaterType) {
                    MomoMainThreadExecutor.post(new Runnable() {
                        @Override
                        public void run() {
                            if (resultCode == LocationResultCode.RESULT_CODE_CANCEL) {
                                map.put("state", resultCode.value());
                            } else if (resultCode == LocationResultCode.RESULT_CODE_NET_DISCONNECTED) {
                                map.put("state", resultCode.value());
                            } else if (LocationUtil.isAvailableLocation(loc)) {
                                map.put("state", 1);
                                setCallBackParam(map, loc, correctLocType, resultCode, locaterType);
                            } else {
                                map.put("state", 0);
                            }
                            callback.call(map);
                        }
                    });
                }
            });
        } catch (SecurityException e) {
            map.put("state", -1);
            callback.call(map);
        } catch (Exception e) {
            map.put("state", 0);
            callback.call(map);
        }
    }

    /**
     * 定位整改实验，避免对旧版本传入新版本才支持的type，新增V2桥接。
     * 传入旧版本不认识的type，会导致每次都是事实定位，没有缓存逻辑。
     * @param type
     * @param callback
     */
    @LuaBridge
    public static void getLocationCacheWithTypeV2(int type, final LVCallback callback) {
        getLocationCacheWithType(type, callback);
    }

    /**
     *
     * @param timeInterval 单位s
     * @param callback
     */
    @Deprecated
    @LuaBridge
    public static void getLocationCacheWithTimeInterval(int timeInterval, final LVCallback callback) {
    }

    @LuaBridge
    public static void getLocationCacheWithTimeIntervalV2(int timeInterval, final LVCallback callback) {
        if (!checkPrivacyValid()) {
            return;
        }
        final Map<String, Object> map = new HashMap<>();
        try {
            LocationClient.getLocationWithTimeInQueue(ILocationMemoryCache.TYPE_DEFAULT, timeInterval, (loc, correctLocType, resultCode, locaterType)->
                    MomoMainThreadExecutor.post(()->{
                        if (resultCode == LocationResultCode.RESULT_CODE_CANCEL) {
                            map.put("state", resultCode.value());
                        } else if (resultCode == LocationResultCode.RESULT_CODE_NET_DISCONNECTED) {
                            map.put("state", resultCode.value());
                        } else if (LocationUtil.isAvailableLocation(loc)) {
                            map.put("state", 1);
                            setCallBackParam(map, loc, correctLocType, resultCode, locaterType);
                        } else {
                            map.put("state", 0);
                        }
                        callback.call(map);
                    }));
        } catch (SecurityException e) {
            map.put("state", -1);
            callback.call(map);
        } catch (Exception e) {
            map.put("state", 0);
            callback.call(map);
        }
    }

    public static void setCallBackParam(@NonNull Map<String, Object> map, Location loc, boolean correctLocType, LocationResultCode resultCode, LocaterType locaterType) {
        double latitude = loc.getLatitude();
        double longitude = loc.getLongitude();
        float accuracy = loc.getAccuracy();
        long locationTime = loc.getTime();
        int locaterTypeValue = 0;
        if (locaterType != null) {
            locaterTypeValue = locaterType.value();
        }
        map.put("lat", latitude);
        map.put("lng", longitude);
        map.put("acc", accuracy);
        map.put("time", System.currentTimeMillis());
        if (LocationClient.getConfiguration().getLocationMemoryCache() instanceof LocationClientSetter.LocationMemoryCache) {
            map.put("lastCacheTime", ((LocationClientSetter.LocationMemoryCache) LocationClient.getConfiguration().getLocationMemoryCache()).lastLocationTime);
        }
        if (locationTime > 0) {
            map.put("locationCacheTime", locationTime + "");
        }
        map.put("correctLocType", correctLocType);
        map.put("resultCode", resultCode.value());
        map.put("locaterType", locaterTypeValue);
        boolean fuzzyLocPermission = PermissionUtil.checkFuzzyLocationPermission(AppContext.getContext());
        if (fuzzyLocPermission) {
            map.put("locPermissionType", "1"); // 如果是模糊定位状态
        }
        if (resultCode.value() == com.immomo.framework.location.LocationResultCode.RESULT_CODE_MONI_LOCATIONSET.value()) { // 是否是模拟定位
            map.put("virtualLocation", "1");
        }
    }

    @LuaBridge
    public static boolean authorized() {
        return PermissionUtil.getInstance().checkSelfPermission(MLSEngine.getContext(), android.Manifest.permission.ACCESS_FINE_LOCATION);
    }

    @LuaBridge
    public static void showLocationErrorAlert(Map resultMap) {
        if (resultMap == null) {
            return;
        }

        Object resultCode = resultMap.get("state");
        if (resultCode instanceof Integer) {
            showErrorToast((Integer) resultCode);
        }
    }

    @LuaBridge
    public static void showLocationErrorAlertWithMap(Map resultMap) {
        if (resultMap == null) {
            return;
        }

        Object resultCode = resultMap.get("state");
        if (resultCode instanceof Integer) {
            showErrorToast((Integer) resultCode);
        }
    }

    private static void showErrorToast(int resultCode) {
        String errormsg = getString(resultCode);

        Toaster.showInvalidate(errormsg);
    }

    private static String getString(int resultCode) {
        String errormsg;
        switch (resultCode) {
            case 104:
                errormsg = "当前网络不可用，请检查";
                break;

            case 105:
                errormsg = "检测到设备开启[允许模拟位置]。必须关闭才能继续使用陌陌，现在去设置吗？";
                break;

            /*
             case 101:
                errormsg = " RESULT_CODE_FAILED ";
                break;

            case 106:
                errormsg = "RESULT_CODE_CONVERT";
                break;

            case 107:
                errormsg = " RESULT_CODE_CANCEL";
                break;
                */

            default:
                errormsg = "定位失败，请检查定位设置或稍后重试";
        }
        return errormsg;
    }

    /**
     * 是否开启了定位服务
     *
     * @return
     */
    @LuaBridge
    public static boolean openLocationService() {
        return LocationFuncCheckUtils.isLocationEnabled();
    }

    @LuaBridge
    public static void showPermissionAlert(final UDLuaView window, @Nullable UDMap udmap) {
        if (window == null)
            return;

        String title = null, message = null, cancelString = null, rightButtonString = null;

        if (udmap != null) {
            try {
                int state = (int) udmap.getMap().get("state");
                title = (String) udmap.getMap().get("title");
                message = (String) udmap.getMap().get("message");
                cancelString = (String) udmap.getMap().get("cancelString");
                rightButtonString = (String) udmap.getMap().get("rightButtonString");
            } catch (Exception e) {

            }
        }

        if (StringUtils.isEmpty(title))
            title = "定位服务未开启";

        if (StringUtils.isEmpty(message))
            message = "请在手机设置中开启定位服务";

        if (StringUtils.isEmpty(cancelString))
            cancelString = "取消";

        if (StringUtils.isEmpty(rightButtonString))
            rightButtonString = "开启定位";

        MAlertDialog dialog = MAlertDialog.makeConfirm(window.getContext(), message, cancelString, rightButtonString,
                                                       new DialogInterface.OnClickListener() {
                                                           @Override
                                                           public void onClick(DialogInterface dialog, int which) {
                                                               dialog.dismiss();
                                                           }
                                                       },
                                                       new DialogInterface.OnClickListener() {
                                                           @Override
                                                           public void onClick(DialogInterface dialog, int which) {
                                                               // getPermissionChecker().requestPermission(Manifest.permission.READ_CONTACTS, REQ_PERMISSION_BLOCK_CONTACT);
                                                               gotoAppDetailSetting(window.getContext());
                                                           }
                                                       });
        dialog.setTitle(title);
        dialog.setSupportDark(true);
        dialog.show();
    }

    private static void gotoAppDetailSetting(Context context) {
        try {
            if (DeviceUtils.isMIUI()) {
                MiuiUtils.goToPermissionsEditorActivity(MomoKit.getContext());
            } else {
                Intent settingIntent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                Uri uri = Uri.fromParts("package", MomoKit.getPackageName(), null);
                settingIntent.setData(uri);
                context.startActivity(settingIntent);
            }
        } catch (Exception ex) {
            gotoInstalledAppList(context);
        }
    }

    private static void gotoInstalledAppList(Context context) {
        try {
            context.startActivity(new Intent(Settings.ACTION_MANAGE_ALL_APPLICATIONS_SETTINGS));
        } catch (Exception ex) {

        }
    }

    /**
     * 是否同意隐私协议
     * @return
     */
    private static boolean checkPrivacyValid() {
        if (!AccountKit.getAccountManager().isOnline()) {
            return AppAsm.getRouter(PrivacyRouter.class).isAgreePrivacy();
        }
        return true;
    }
}