package com.immomo.momo.luaview.ud;

import android.content.Context;

import com.immomo.android.router.momo.business.music.MusicStateListener;
import com.immomo.framework.storage.kv.KV;
import com.immomo.mls.LuaViewManager;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.fun.other.Rect;
import com.immomo.momo.businessmodel.statistics.ILogRecordHelper;
import com.immomo.momo.feed.service.VideoService;
import com.immomo.momo.luaview.lt.SettingKVUtils;
import com.immomo.momo.music.MusicManager;
import com.immomo.momo.mvp.nearby.NearbyBubbleHelper;
import com.immomo.momo.mvp.nearby.PrivacyHelper;
import com.immomo.momo.util.StringUtils;

import org.luaj.vm2.Globals;
import org.luaj.vm2.LuaBoolean;
import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaValue;

@LuaClass
public class UDNearbyPeopleHandler {
    public static final String LUA_CLASS_NAME = "NearbyPeopleHandler";
    private static final int MUSIC = 2;
    private String feedSource;

    private static final int NEARBY_TYPE_FEED = 1;// 动态
    private static final int NEARBY_TYPE_LIVE = 101;// 直播推荐

    private Globals globals;

    public UDNearbyPeopleHandler(Globals g, LuaValue[] v) {
        this.globals = g;
        if (v.length > 0) {
            String source = v[0].toJavaString();
            feedSource = source;
        } else {
            feedSource = ILogRecordHelper.FeedSource.NEARBY_LIST;
        }
    }

    public Context getContext() {
        LuaViewManager m = (LuaViewManager) globals.getJavaUserdata();
        return m != null ? m.context : null;
    }

    @LuaBridge
    public void setNearbyBubbleRefreshEmotionId(int mid) {
        NearbyBubbleHelper.saveLastMoodId(mid);
    }

    @LuaBridge
    public void setNearbyBubbleRefreshTime(long timeSecond) {
        NearbyBubbleHelper.saveLastBubbleTime(timeSecond);
    }

    @LuaBridge
    public long nearbyBubbleRefreshTime() {
        //        return  0;
        return NearbyBubbleHelper.getLastBubbleTime();
    }

    @LuaBridge
    public void setNearbyBubbleRefreshTimeInterval(int timeInterval) {
        NearbyBubbleHelper.saveNextBubbleTimeFrame(timeInterval);
    }

    @LuaBridge
    public int nearbyBubbleRefreshTimeInterval() {
        return NearbyBubbleHelper.getNextBubbleTimeFrame();
    }

    @LuaBridge
    public boolean isShowHomeGuideView() {
        return false;
    }

    /**
     * 获取用户的视频自动播放设置
     *
     * @return
     */
    @LuaBridge
    public boolean checkAutoPlaySetting() {
        int videoPlayStatus = SettingKVUtils.getAutoPlayType();
        return VideoService.getInstance().needAutoPlayVideo(videoPlayStatus);
    }

    /**
     * 设置冒泡状态
     *
     * @param hasShowed
     */
    @LuaBridge
    public void setHasShowNearbyPeopleBubbleAlert(boolean hasShowed) {
        KV.saveUserValue(com.immomo.framework.SPKeys.User.PublishFeed.KEY_HAS_SHOW_NEARBY_BUBBLE, hasShowed);
    }

    /**
     * 获取冒泡状态
     *
     * @return
     */
    @LuaBridge
    public boolean hasShowNearbyPeopleBubbleAlert() {
        return KV.getUserBool(com.immomo.framework.SPKeys.User.PublishFeed.KEY_HAS_SHOW_NEARBY_BUBBLE, false);
    }

    /**
     * 动态的位置功能区跳转
     *
     * @param item
     */
    @LuaBridge
    public void showSiteListPageWithSite(com.alibaba.fastjson.JSONObject item) {
    }

    @LuaBridge
    public boolean isHideInNearbyPeopleForbidden() {
        return PrivacyHelper.isHideInNearbyPeopleForbidden();
    }

    @LuaBridge
    public void saveBeQuiet(boolean beQuiet) {
        PrivacyHelper.saveBequiet(beQuiet ? 1 : 0);
    }

    @LuaBridge
    public boolean getBeQuiet() {
        return PrivacyHelper.getBequiet();
    }

    /**
     * 音乐动态跳转到音乐播放界面
     *
     * @param data
     * @param object
     * @param callback
     */
    @LuaBridge
    public void gotoMusicPlayActivity(String data, com.alibaba.fastjson.JSONObject object, LuaFunction callback) {
        //        CommonFeed feed = new CommonFeed();
        //        final String playKey;
        //        String listenerKey;
        //        try {
        //            JSONObject feedJson = new JSONObject(data);
        //            FeedApi.parseCommonFeed(feedJson, feed);
        //            playKey = "FEED" + feed.getFeedId();
        //            listenerKey = "FEED" + feed.getFeedId() + config.getFeedSource();
        //        } catch (Exception e) {
        //            // 数据异常
        //            return;
        //        }
        //        LoggerUtilX.getInstance().saveGotoLog(com.immomo.momo.statistics.LoggerKeys.LOCAL_LOG_KEY_FEEDINFO_MUSIC_CLICK);
        //        if (feed.music != null && feed.music.frType == 1) {
        //            logFeedRead(feed);
        //            MusicManager.getInstance().gotoMusicPlayerProfile(playKey, listenerKey, feed.music.id, feed.music.webUrl, new MusicPlayListener(playKey, listenerKey, callback));
        //        } else {
        //            gotoWebPage(feed);
        //        }
    }

    //    private void gotoWebPage(CommonFeed feed) {
    //        if (feed.music == null) {
    //            return;
    //        }
    //        WebRouterConfig config = new WebRouterConfig.Builder().url(feed.music.webUrl).build();
    //        WebRouterUtils.startOpenWebPage(getContext(), config);
    //
    //        MusicManager.getInstance().stop();
    //    }

    /**
     * 音乐动态 播放音乐
     *
     * @param data
     * @param type
     * @param object
     * @param callback
     */
    @LuaBridge
    public void playMusicWithFeed(String data, int type, com.alibaba.fastjson.JSONObject object, final LuaFunction callback) {
        switch (type) {
            case MUSIC:
                musicPlay(data, callback);
                break;
            default:

                break;
        }
    }

    private boolean isPlayingMusic(String playKey) {
        return MusicManager.getInstance().isPlaying(playKey);
    }

    /**
     * 音乐动态播放
     *
     * @param data
     * @param callback
     */
    private void musicPlay(String data, LuaFunction callback) {
        //        CommonFeed feed = new CommonFeed();
        //        final String playKey;
        //        String listenerKey;
        //        try {
        //            JSONObject feedJson = new JSONObject(data);
        //            FeedApi.parseCommonFeed(feedJson, feed);
        //            playKey = "FEED" + feed.getFeedId();
        //            listenerKey = "FEED" + feed.getFeedId() + config.getFeedSource();
        //        } catch (Exception e) {
        //            // 数据异常
        //            return;
        //        }
        //        // TODO by MUSIC 后续加回
        //        if (feed.music != null && feed.music.frType == 1) {
        ////            if (!isPlayingMusic(playKey)) {
        ////                LoggerUtilX.getInstance().saveGotoLog(LoggerKeys.LOCAL_LOG_KEY_FEED_MUSCI_CLICK);
        ////                logFeedRead(feed);
        ////                MusicManager.getInstance().startByXiami(playKey, listenerKey, feed.music.id, feed.music.webUrl, new MusicPlayListener(playKey, listenerKey, callback));
        ////            } else {
        ////                MusicManager.getInstance().stop();
        ////            }
        //        } else {
        //            if (feed.music != null && !isPlayingMusic(playKey)) {
        //                LoggerUtilX.getInstance().saveGotoLog(com.immomo.momo.statistics.LoggerKeys.LOCAL_LOG_KEY_FEED_MUSCI_CLICK);
        //                MusicManager.getInstance().startByUrl(playKey, listenerKey, feed.music.songUrl, feed.music.webUrl, feed.music.picUrl, new MusicPlayListener(playKey, listenerKey, callback));
        //            } else {
        //                MusicManager.getInstance().stop();
        //            }
        //        }
    }

    /**
     * 图片动态跳转到大图界面--含交叉推广
     *
     * @param feedId
     * @param data
     * @param clickIndex
     * @param rect
     */
    @LuaBridge
    public void showBigFeedPhotoWithFeedId(String feedId, String data, int clickIndex, Rect rect) {
        // FIXME 删除

        //        CommonFeed feed = new CommonFeed();
        //        try {
        //            JSONObject feedJson = new JSONObject(data);
        //            FeedApi.parseCommonFeed(feedJson, feed);
        //        } catch (Exception e) {
        //
        //        }
        //        if (config == null) {
        //            config = FeedModelConfig.build(ILogRecordHelper.FeedSource.NEARBY_LIST);
        //        }
        //        Context context = getContext();
        //
        //        android.graphics.Rect[] imageBounds = new android.graphics.Rect[1];
        //        imageBounds[0] = new android.graphics.Rect(UIUtils.getScreenWidth() / 2, UIUtils.getScreenHeight() / 2, UIUtils.getScreenWidth() / 2, UIUtils.getScreenHeight() / 2);
        //
        //        Intent intent = new Intent(context, FeedImageBrowserActivity.class);
        //        JSONObject extra = new JSONObject();
        //        try {
        //            extra.put(FeedImageBrowserActivity.KEY_FEED_SOURCE, config.getFeedSourceOrPre());
        //            extra.put(FeedImageBrowserActivity.KEY_HEADER_AUTOHIDE, true);
        //            extra.put(FeedImageBrowserActivity.KEY_FEED_ID, feed.getFeedId());
        //            extra.put(FeedImageBrowserActivity.KEY_FROM_GID, "");
        //            extra.put(FeedImageBrowserActivity.KEY_REMOTE_ID, feed.user == null ? "" : feed.user.momoid);
        //            extra.put(FeedImageBrowserActivity.KEY_IS_FROM_RECOMMEND, true);
        //            if ((NEARBY_LIST.equals(config.getFeedSource()) || NEARBY_MIX.equals(config.getFeedSource()) || NEARBY_FEED.equals(config.getFeedSource()) || NEARBY_PEOPLE.equals(config.getFeedSource()))
        //                    && !feed.isForwardFeed()) {
        //                extra.put(FeedImageBrowserActivity.KEY_IS_SHOW_RECOMMEND_IMAGE, true);
        //            }
        //            if (null != feed.user) {
        //                extra.put(FeedImageBrowserActivity.KEY_USER_AVATAR, feed.user.getAvatar());
        //            }
        //
        //        } catch (JSONException e) {
        //            MDLog.printErrStackTrace(LogTag.COMMON, e);
        //        }
        //
        //        ImageBrowserConfig config = new ImageBrowserConfig.Builder()
        //                .imageType(ImageBrowserConfig.Type.TYPE_FEED)
        //                .startIndex(clickIndex)
        //                .imageBounds(imageBounds)
        //                .thumbImageType(ImageType.IMAGE_TYPE_FEEDIMG_400X400)
        //                .imageThumbUrls(feed.feedimgs)
        //                .imageLargeUrls(feed.originalfeedimgs)
        //                .extraData(extra.toString())
        //                .imageMode(ImageBrowserConfig.Mode.IMAGE_URL)
        //                .build();
        //        intent.putExtra(FeedImageBrowserActivity.KEY_CONFIG, config);
        //

        //        Intent intent = new Intent(context, ImageBrowserActivity.class);
        //        intent.putExtra(ImageBrowserActivity.KEY_FEED_SOURCE, config.getFeedSourceOrPre());
        //        intent.putExtra(ImageBrowserActivity.KEY_IMAGETYPE, ImageBrowserActivity.TYPE_FEED);
        //        intent.putExtra(ImageBrowserActivity.KEY_THUMB_IMAGETYPE, ImageType.IMAGE_TYPE_FEEDIMG_400X400);
        //        intent.putExtra(ImageBrowserActivity.KEY_HEADER_AUTOHIDE, true);
        //        intent.putExtra(ImageBrowserActivity.KEY_INDEX, clickIndex);
        //        intent.putExtra(ImageBrowserActivity.KEY_FEED_ID, feed.getFeedId());
        //        intent.putExtra(ImageBrowserActivity.KEY_IMAGE_BOUNDS, imageBounds);
        //        intent.putExtra(ImageBrowserActivity.KEY_FROM_GID, "");
        //        intent.putExtra(ImageBrowserActivity.KEY_IMAGEARRAY_THUMB_URL, feed.feedimgs);
        //        intent.putExtra(ImageBrowserActivity.KEY_IMAGEARRAY_LARGE_URL, feed.originalfeedimgs);
        //        intent.putExtra(ImageBrowserActivity.KEY_MODEL, ImageBrowserActivity.MODEL_URLIMAGE);
        //        intent.putExtra(ImageBrowserActivity.KEY_REMOTE_ID, feed.user == null ? "" : feed.user.momoid);
        //            if ((NEARBY_LIST.equals(config.getFeedSource()) || NEARBY_MIX.equals(config.getFeedSource()) || NEARBY_FEED.equals(config.getFeedSource()) || NEARBY_PEOPLE.equals(config.getFeedSource()))
        //                    && !feed.isForwardFeed()) {
        //                extra.put(FeedImageBrowserActivity.KEY_IS_SHOW_RECOMMEND_IMAGE, true);
        //            }
        //            if (null != feed.user) {
        //                extra.put(FeedImageBrowserActivity.KEY_USER_AVATAR, feed.user.getAvatar());
        //            }
        //
        //        } catch (JSONException e) {
        //            e.printStackTrace();
        //        }
        //
        //        ImageBrowserConfig config = new ImageBrowserConfig.Builder()
        //                .imageType(ImageBrowserConfig.Type.TYPE_FEED)
        //                .startIndex(clickIndex)
        //                .imageBounds(imageBounds)
        //                .thumbImageType(ImageType.IMAGE_TYPE_FEEDIMG_400X400)
        //                .imageThumbUrls(feed.feedimgs)
        //                .imageLargeUrls(feed.originalfeedimgs)
        //                .extraData(extra.toString())
        //                .imageMode(ImageBrowserConfig.Mode.IMAGE_URL)
        //                .build();
        //        intent.putExtra(FeedImageBrowserActivity.KEY_CONFIG, config);
        //
        //
        ////        Intent intent = new Intent(context, ImageBrowserActivity.class);
        ////        intent.putExtra(ImageBrowserActivity.KEY_FEED_SOURCE, config.getFeedSourceOrPre());
        ////        intent.putExtra(ImageBrowserActivity.KEY_IMAGETYPE, ImageBrowserActivity.TYPE_FEED);
        ////        intent.putExtra(ImageBrowserActivity.KEY_THUMB_IMAGETYPE, ImageType.IMAGE_TYPE_FEEDIMG_400X400);
        ////        intent.putExtra(ImageBrowserActivity.KEY_HEADER_AUTOHIDE, true);
        ////        intent.putExtra(ImageBrowserActivity.KEY_INDEX, clickIndex);
        ////        intent.putExtra(ImageBrowserActivity.KEY_FEED_ID, feed.getFeedId());
        ////        intent.putExtra(ImageBrowserActivity.KEY_IMAGE_BOUNDS, imageBounds);
        ////        intent.putExtra(ImageBrowserActivity.KEY_FROM_GID, "");
        ////        intent.putExtra(ImageBrowserActivity.KEY_IMAGEARRAY_THUMB_URL, feed.feedimgs);
        ////        intent.putExtra(ImageBrowserActivity.KEY_IMAGEARRAY_LARGE_URL, feed.originalfeedimgs);
        ////        intent.putExtra(ImageBrowserActivity.KEY_MODEL, ImageBrowserActivity.MODEL_URLIMAGE);
        ////        intent.putExtra(ImageBrowserActivity.KEY_REMOTE_ID, feed.user == null ? "" : feed.user.momoid);
        ////        if ((NEARBY_LIST.equals(config.getFeedSource()) || NEARBY_MIX.equals(config.getFeedSource()) || NEARBY_FEED.equals(config.getFeedSource()) || NEARBY_PEOPLE.equals(config.getFeedSource()))
        ////                && !feed.isForwardFeed()) {
        ////            intent.putExtra(ImageBrowserActivity.KEY_IS_SHOW_RECOMMEND_IMAGE, true);
        ////        }
        ////        if (null != feed.user) {
        ////            intent.putExtra(ImageBrowserActivity.KEY_USER_AVATAR, feed.user.getAvatar());
        ////        }
        //
        //        context.startActivity(intent);
        //        if (context instanceof Activity) {
        //            Activity activity = (Activity) context;
        //            if (activity.getParent() != null) {
        //                activity.getParent().overridePendingTransition(0, 0);
        //            } else {
        //                activity.overridePendingTransition(R.anim.feed_image_enter, 0);
        //            }
        //        }
        //        if (GuestConfig.getInstance().isGuestMode()) {
        //            return;
        //        }
        //        logFeedRead(feed);
    }

    //    private void logFeedRead(CommonFeed feed) {
    //        //访客四期阻断
    //        if (GuestConfig.getInstance().isGuestMode()) {
    //            return;
    //        }
    ////        MomoTaskExecutor.executeUserTask(config.getTaskTag(), new ReadFeedTask(feed));
    //    }

    /**
     * 图片印记跳转到大图界面--不含交叉推广，自动加载下一个feed
     *
     * @param momoid
     * @param data
     * @param arr
     * @param position
     * @param rect
     */
    @LuaBridge
    public void showBigImpressPhotoWithMomoId(String momoid, String data, com.alibaba.fastjson.JSONArray arr, int position, Rect rect) {
        //        List<BaseFeed> feeds = new ArrayList<>();
        //        List<String> feedimgList = new ArrayList<>();
        //        List<String> originalFeedimgList = new ArrayList<>();
        //        List<String> feedGuids = new ArrayList<>();
        //        ArrayList<String> feedIdList = new ArrayList<>();
        //        try {
        //            JSONObject json = new JSONObject(data);
        //            JSONArray feedArr = json.optJSONArray("albums");
        //
        //            for (int i = 0; i < feedArr.length(); i++) {
        //                CommonFeed feed = new CommonFeed();
        //                JSONObject feedJson = feedArr.getJSONObject(i);
        //                FeedApi.parseCommonFeed(feedJson, feed);
        //
        //                feedIdList.add(feed.getFeedId());
        //                if (StringUtils.isNotEmpty(feed.feedImg)) {
        //                    feedimgList.add(feed.feedImg);
        //                }
        //                if (StringUtils.isNotEmpty(feed.originalFeedImg)) {
        //                    originalFeedimgList.add(feed.originalFeedImg);
        //                }
        //                if (StringUtils.isNotEmpty(feed.cross_promotion_guid)) {
        //                    feedGuids.add(feed.cross_promotion_guid);
        //                }
        //                feeds.add(feed);
        //            }
        //        } catch (Exception e) {
        //
        //        }
        //        UserFeedService.getInstance().saveUserFeed(feeds);
        //        String feedId = feeds.get(position).getFeedId();
        //
        //        Context context = getContext();
        //        android.graphics.Rect[] imageBounds = new android.graphics.Rect[1];
        //        imageBounds[0] = new android.graphics.Rect(UIUtils.getScreenWidth() / 2, UIUtils.getScreenHeight() / 2, UIUtils.getScreenWidth() / 2, UIUtils.getScreenHeight() / 2);
        //
        //
        //        Intent intent = new Intent(context, FeedImageBrowserActivity.class);
        //        JSONObject extra = new JSONObject();
        //        try {
        //            extra.put(FeedImageBrowserActivity.KEY_FEED_SOURCE, config.getFeedSourceOrPre());
        //            extra.put(FeedImageBrowserActivity.KEY_HEADER_AUTOHIDE, true);
        //            extra.put(FeedImageBrowserActivity.KEY_FEED_ID, feedId);
        ////            extra.put(FeedImageBrowserActivity.KEY_FROM_GID, false);
        //            extra.put(FeedImageBrowserActivity.KEY_REMOTE_ID, feedId);
        //            extra.put(FeedImageBrowserActivity.KEY_IS_FROM_RECOMMEND, true);
        //            extra.put(FeedImageBrowserActivity.KEY_LAST_IMAGE_GUID, feedGuids.get(feedGuids.size() - 1));
        //            extra.put(FeedImageBrowserActivity.KEY_IS_FROM_IMPRESSION_PHOTO, true);
        //            intent.putStringArrayListExtra(FeedImageBrowserActivity.KEY_FEED_ID_LIST, feedIdList);
        //        } catch (JSONException e) {
        //            MDLog.printErrStackTrace(LogTag.COMMON, e);
        //        }
        //
        //        ImageBrowserConfig config = new ImageBrowserConfig.Builder()
        //                .imageType(ImageBrowserConfig.Type.TYPE_FEED)
        //                .startIndex(position)
        //                .imageBounds(imageBounds)
        //                .thumbImageType(ImageType.IMAGE_TYPE_FEEDIMG_400X400)
        //                .imageThumbUrls(feedimgList.toArray(new String[feedimgList.size()]))
        //                .imageLargeUrls(originalFeedimgList.toArray(new String[originalFeedimgList.size()]))
        //                .extraData(extra.toString())
        //                .imageMode(ImageBrowserConfig.Mode.IMAGE_URL)
        //                .build();
        //        intent.putExtra(FeedImageBrowserActivity.KEY_CONFIG, config);
        //

        //        Intent intent = new Intent(context, ImageBrowserActivity.class);
        //        intent.putExtra(ImageBrowserActivity.KEY_FEED_SOURCE, config.getFeedSourceOrPre());
        //        intent.putExtra(ImageBrowserActivity.KEY_IMAGETYPE, ImageBrowserActivity.TYPE_FEED);
        //        intent.putExtra(ImageBrowserActivity.KEY_THUMB_IMAGETYPE, ImageType.IMAGE_TYPE_FEEDIMG_400X400);
        //        intent.putExtra(ImageBrowserActivity.KEY_HEADER_AUTOHIDE, true);
        //        intent.putExtra(ImageBrowserActivity.KEY_INDEX, position);
        //        intent.putExtra(ImageBrowserActivity.KEY_FEED_ID, feedId);
        //        intent.putExtra(ImageBrowserActivity.KEY_IMAGE_BOUNDS, imageBounds);
        //        intent.putExtra(ImageBrowserActivity.KEY_FROM_GID, false);
        //        intent.putExtra(ImageBrowserActivity.KEY_IMAGEARRAY_THUMB_URL, feedimgList.toArray(new String[feedimgList.size()]));
        //        intent.putExtra(ImageBrowserActivity.KEY_IMAGEARRAY_LARGE_URL, originalFeedimgList.toArray(new String[originalFeedimgList.size()]));
        //        intent.putExtra(ImageBrowserActivity.KEY_MODEL, ImageBrowserActivity.MODEL_URLIMAGE);
        //        intent.putExtra(ImageBrowserActivity.KEY_REMOTE_ID, momoid);
        //        intent.putExtra(ImageBrowserActivity.KEY_IS_FROM_IMPRESSION_PHOTO, true);// 相册印记添加新类型
        //        intent.putExtra(ImageBrowserActivity.KEY_LAST_IMAGE_GUID, feedGuids.get(feedGuids.size() - 1));
        //        intent.putStringArrayListExtra(ImageBrowserActivity.KEY_FEED_ID_LIST, feedIdList);
        //        context.startActivity(intent);
        //        if (context instanceof Activity) {
        //            Activity activity = (Activity) context;
        //            if (activity.getParent() != null) {
        //                activity.getParent().overridePendingTransition(0, 0);
        //            } else {
        //                activity.overridePendingTransition(R.anim.feed_image_enter, 0);
        //            }
        //        }
    }

    private class MusicPlayListener implements MusicStateListener {

        private String playKey;
        private String listenerKey;
        private LuaFunction callback;

        public MusicPlayListener(String playKey, String listenerKey, LuaFunction callback) {
            this.playKey = playKey;
            this.listenerKey = listenerKey;
            this.callback = callback;
        }

        @Override
        public void onStateChanged(String typeKey, int state) {
            if (listenerKey != null && !listenerKey.equals(typeKey)) {
                return;
            }
            if (callback == null) {
                return;
            }

            if (MusicManager.getInstance().getCurrentPlayId() != null && !MusicManager.getInstance().getCurrentPlayId().equals(playKey)) {
                return;
            }

            switch (state) {
                case STATE_START:
                    callback.invoke(LuaBoolean.rBoolean(true));
                    break;
                case STATE_STOP:
                case STATE_COMPLETE:
                case STATE_ERROR:
                    callback.invoke(LuaBoolean.rBoolean(false));
                    break;
            }

        }

    }
}
