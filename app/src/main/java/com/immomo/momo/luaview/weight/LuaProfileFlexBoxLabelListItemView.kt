package com.immomo.momo.luaview.weight

import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.Keep
import com.cosmos.mdlog.MDLog
import com.google.gson.annotations.SerializedName
import com.immomo.framework.kotlin.ImageLoader
import com.immomo.framework.utils.UIUtils
import com.immomo.game.util.DpPxUtil
import com.immomo.mmutil.StringUtils
import com.immomo.mmutil.toast.Toaster
import com.immomo.momo.MomoKit
import com.immomo.momo.R
import com.immomo.momo.gotologic.GotoDispatcher
import com.immomo.momo.util.ColorUtils
import java.io.File

class LuaFlexBoxLabelListItemView @JvmOverloads constructor(
    context: Context,
    attributes: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attributes, defStyleAttr) {

    companion object {
        @JvmStatic
        val tagSpaceMargin = UIUtils.getPixels(5F)
    }

    private var iconView: ImageView

    private var nameView: TextView

    private val tagHeight = UIUtils.getPixels(12F)

    private val tagMargin = UIUtils.getPixels(3F)

    init {
        LayoutInflater.from(context).inflate(R.layout.layout_lua_flex_label_list_item, this)
        gravity = Gravity.CENTER_VERTICAL
        orientation = HORIZONTAL
        background = resources.getDrawable(R.drawable.bg_10dp_round_corner_ebebeb)
        val leftRightSize = UIUtils.getPixels(15F)
        val topBottomSize = UIUtils.getPixels(10F)
        setPadding(leftRightSize, topBottomSize, leftRightSize, topBottomSize)
        iconView = findViewById(R.id.icon)
        nameView = findViewById(R.id.name)
    }

    fun bindData(data: FlexBoxLabelData?, imgThemeMap: Map<String, FlexBoxImgThemeData>?, contentColor: String?, borderColor: String?, iconPath: String?) {
        data ?: return
        val img = data.icon
        val name: String? = data.name
        val tagIcons = data.tagIcons
        bindLabelData(name)
        var imageWidth = 0

        if (StringUtils.isNotEmpty(borderColor)) {
            val shapeDrawable = background as? GradientDrawable
            shapeDrawable?.mutate()
            shapeDrawable?.setStroke(DpPxUtil.dp2px(context,1f), ColorUtils.parseColor(borderColor, R.color.color_ebebeb))
        } else {
            background = resources.getDrawable(R.drawable.bg_10dp_round_corner_ebebeb)
        }
        if (StringUtils.isNotEmpty(contentColor)) {
            nameView?.setTextColor(ColorUtils.parseColor(contentColor, R.color.color_6e6e6e))
        }

        if (imgThemeMap?.containsKey(data?.category) == true) {
            val imgTheme: FlexBoxImgThemeData? = imgThemeMap?.get(data?.category)
            try {
                val iconFile = File("$iconPath/${imgTheme?.img}")
                if (iconFile.exists()) {
                    iconView.visibility = View.VISIBLE
                    ImageLoader.load(iconFile).into(iconView)
                } else {
                    bindIconView(img)
                }
            } catch (e: Exception) {
                MDLog.printErrStackTrace("LuaFlexBoxLabelListItemView", e)
            }
            imageWidth = bindRightIconView(tagIcons, imgTheme, iconPath)
        } else {
            bindIconView(img)
            imageWidth = bindRightIconView(tagIcons, null, "")
        }

        setLabelMaxWidth(imageWidth)
        onGotoClick(data)
    }

    private fun bindRightIconView(tagIcons: List<FlexBoxLabelImgData>?,imgTheme: FlexBoxImgThemeData?, iconPath: String?) : Int {
        var imageWidth = 0
        tagIcons?.forEach {
            try {
                val tagView = ImageView(context)
                val layoutParams =
                    LayoutParams((tagHeight * (it.aspectRatio ?: 1f)).toInt(), tagHeight)
                layoutParams.marginStart = tagMargin
                addView(tagView, layoutParams)
                if (imgTheme != null) {
                    when {
                        StringUtils.equalsNonNull("arrow", it.key) && StringUtils.isNotEmpty(imgTheme.arrow) -> {
                            loadImageView(tagView, it.imgUrl, "$iconPath/${imgTheme.arrow}")
                        }
                        StringUtils.equalsNonNull("liang", it.key) && StringUtils.isNotEmpty(imgTheme.liang) -> {
                            loadImageView(tagView, it.imgUrl, "$iconPath/${imgTheme.liang}")
                        }
                        StringUtils.equalsNonNull("individuality", it.key) && StringUtils.isNotEmpty(imgTheme.individuality) -> {
                            loadImageView(tagView, it.imgUrl, "$iconPath/${imgTheme.individuality}")
                        }
                        else -> {
                            ImageLoader.load(it.imgUrl).into(tagView)
                        }
                    }
                } else {
                    ImageLoader.load(it.imgUrl).into(tagView)
                }
                imageWidth += tagMargin + layoutParams.width
            } catch (e: Exception) {
                MDLog.printErrStackTrace("LuaFlexBoxLabelListItemView", e)
            }
        }
        return imageWidth
    }

    private fun loadImageView(tagView: ImageView, imgUrl: String?, fileStr: String) {
        try {
            val iconFile = File(fileStr)
            if (iconFile.exists()) {
                ImageLoader.load(iconFile).into(tagView)
            } else {
                ImageLoader.load(imgUrl).into(tagView)
            }
        } catch (e: Exception) {
            MDLog.printErrStackTrace("LuaFlexBoxLabelListItemView", e)
        }
    }

    private fun bindLabelData(name: String?) {
        nameView.visibility = if (name.isNullOrBlank()) {
            View.GONE
        } else {
            nameView.text = name
            View.VISIBLE
        }
    }

    private fun bindIconView(img: String?) {
        iconView.visibility = if (img.isNullOrBlank()) {
            View.GONE
        } else {
            ImageLoader.load(img).into(iconView)
            View.VISIBLE
        }
    }

    private fun onGotoClick(data: FlexBoxLabelData?) {
        val name: String? = data?.name
        val gotoStr = data?.gotoStr
        val ismomoid = data?.ismomoid
        gotoStr?.also {
            setOnClickListener {
                GotoDispatcher.action(gotoStr, context).execute()
            }
        }
        ismomoid?.also {
            if (it > 0 && StringUtils.isNotBlank(name)) {
                setOnLongClickListener {
                    //获取剪贴板管理器：
                    MomoKit.setClipboardText(name)
                    Toaster.show("已成功复制文本")
                    return@setOnLongClickListener true
                }
            }
        }
    }

    fun bindData(data: FlexBoxLabelData?) {
        data ?: return
        val img = data.icon
        val name: String? = data.name
        val tagIcons = data.tagIcons
        bindIconView(img)
        bindLabelData(name)
        var imageWidth = 0
        tagIcons?.forEach {
            val tagView = ImageView(context)
            val layoutParams = LayoutParams((tagHeight * (it.aspectRatio ?: 1f)).toInt(), tagHeight)
            layoutParams.marginStart = tagMargin
            addView(tagView, layoutParams)
            ImageLoader.load(it.imgUrl).into(tagView)
            imageWidth += tagMargin + layoutParams.width
        }
        setLabelMaxWidth(imageWidth)
        onGotoClick(data)
    }

    private fun setLabelMaxWidth(width: Int){
        if (nameView?.visibility == View.VISIBLE) {
            val maxWidth = UIUtils.getScreenWidth() - width - UIUtils.getPixels(85f)
            nameView?.maxWidth = maxWidth
        }
    }

}

/**
 * 标签数据
 */
@Keep
class FlexBoxLabelData {
    @SerializedName("text")
    var name: String? = null

    @SerializedName("icon")
    var icon: String? = null

    @SerializedName("gotoStr")
    var gotoStr: String? = null

    @SerializedName("ismomoid")
    var ismomoid: Int? = null

    @SerializedName("category")
    var category: String? = null

    @SerializedName("tag_icons")
    var tagIcons: List<FlexBoxLabelImgData>? = null
}

@Keep
class FlexBoxLabelImgData {

    @SerializedName("img_url")
    var imgUrl: String? = null

    @SerializedName("aspect_ratio")
    var aspectRatio: Float? = null

    @SerializedName("key")
    var key: String? = null
}

@Keep
class FlexBoxLabelThemeData {
    @SerializedName("contentColor")
    var contentColor: String? = null

    @SerializedName("borderColor")
    var borderColor: String? = null

    @SerializedName("rightIcon")
    var rightIcon: String? = null

    @SerializedName("infoLists")
    var infoLists: List<FlexBoxImgThemeData>? = null
}

@Keep
class FlexBoxImgThemeData {
    @SerializedName("category")
    var category: String? = null

    @SerializedName("img")
    var img: String? = null

    @SerializedName("arrow")
    var arrow: String? = null

    @SerializedName("liang")
    var liang: String? = null

    @SerializedName("individuality")
    var individuality: String? = null


}