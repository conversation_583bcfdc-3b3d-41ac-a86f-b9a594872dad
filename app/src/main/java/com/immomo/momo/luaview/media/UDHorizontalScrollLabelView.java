package com.immomo.momo.luaview.media;

import android.util.TypedValue;
import android.view.LayoutInflater;

import androidx.annotation.NonNull;

import org.luaj.vm2.LuaNumber;
import org.luaj.vm2.LuaString;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import com.immomo.mls.fun.ud.UDColor;
import com.immomo.mls.util.DimenUtil;
import com.immomo.mls.util.LogUtil;
import com.immomo.momo.R;
import com.immomo.momo.feed.ui.view.MarqueeTextVIew;
import com.immomo.mls.fun.ud.view.UDView;
import com.immomo.momo.util.StringUtils;

/**
 * author: hongming.wei
 * data: 2023/3/16
 * 左右滚动Label
 */
@LuaApiUsed
public class UDHorizontalScrollLabelView<V extends MarqueeTextVIew> extends UDView<V> {

    public static final String LUA_CLASS_NAME = "HorizontalScrollLabelView";

    public static final String[] methods = {
            "setFontSize",
            "setText",
            "setTextColor"
    };


    @LuaApiUsed
    protected UDHorizontalScrollLabelView(long L, LuaValue[] v) {
        super(L, v);
    }

    @NonNull
    @Override
    protected V newView(@NonNull LuaValue[] init) {
        return (V) LayoutInflater.from(getContext()).inflate(R.layout.layout_horizontal_scroll_label_view, null);
    }


    @LuaApiUsed
    public LuaValue[] setFontSize(LuaValue[] values){
        if (values != null && values.length > 0) {
            getView().setTextSize(TypedValue.COMPLEX_UNIT_SP, (float) values[0].toDouble());
            return null;
        }
        return varargsOf(LuaNumber.valueOf(DimenUtil.pxToSp(getView().getTextSize())));
    }

    @LuaApiUsed
    public LuaValue[] setText(LuaValue[] values) {
        String text = null;
        if (values.length == 1) {
            text = values[0].toJavaString();

            if (values[0].isNil())
                text = "";
        }
        if (text != null) {
            try {
                getView().setText(text);
                getView().start();
            } catch (Exception e) {
                LogUtil.w("Label text()  bridge   Exception ", e);
            }
            return null;
        }
        return varargsOf(LuaString.valueOf(getView().getText().toString()));
    }

    @LuaApiUsed
    public LuaValue[] setTextColor(LuaValue[] values) {
        if (values.length == 1 && values[0] instanceof UDColor) {
            UDColor color = (UDColor) values[0];
            getView().setTextColor(color.getColor());
            return null;
        }

        UDColor ret = new UDColor(getGlobals(), 0);
        ret.setColor(getView().getTextColors().getDefaultColor());
        return varargsOf(ret);
    }
}
