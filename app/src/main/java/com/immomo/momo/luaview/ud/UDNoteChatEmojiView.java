package com.immomo.momo.luaview.ud;

import android.graphics.Color;

import com.immomo.framework.utils.UIUtils;
import com.immomo.framework.view.inputpanel.impl.emote.OnEmoteSelectedListener;
import com.immomo.mls.fun.ud.view.UDView;
import com.immomo.momo.android.view.EmoteEditeText;
import com.immomo.momo.android.view.ResizableEmoteInputView;

import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

/**
 * -----------------------------------------------------------------
 * Copyright (C)  sun
 * Create: 2022/5/27 2:28 PM
 * 表情面板视图
 * -----------------------------------------------------------------
 */
@LuaApiUsed
public class UDNoteChatEmojiView extends UDView<ResizableEmoteInputView> {
    public static final String LUA_CLASS_NAME = "NoteChatEmojiView";

    public static final String[] methods = {
        "setEditText",
        "show",
        "hide",
        "setEmoteFlag",
        "clickEmoji",
        "willShow",
        "willHidden",
        "hideTab"
    };

    private ResizableEmoteInputView emoteInputView;

    LuaFunction luaClickEmoteCallback;
    LuaFunction luaShowCallback;
    LuaFunction luaHideCallback;

    @LuaApiUsed
    public UDNoteChatEmojiView(long L, LuaValue[] v) {
        super(L, v);
    }

    @Override
    protected ResizableEmoteInputView newView(LuaValue[] init) {
        emoteInputView = new ResizableEmoteInputView(getContext());
        emoteInputView.setBackgroundColor(Color.parseColor("#FFF7F7F7"));
        emoteInputView.setPadding(0, UIUtils.getPixels(10),0,0);
        return emoteInputView;
    }

    @LuaApiUsed
    public LuaValue[] setEditText(LuaValue[] values) {
        UDView view = (UDView) values[0];
        emoteInputView.setEditText((EmoteEditeText) view.getView());
        return null;
    }

    @LuaApiUsed
    public LuaValue[] show(LuaValue[] values) {
        emoteInputView.show();
        if (luaShowCallback != null) {
            luaShowCallback.fastInvoke(emoteInputView.getHeight());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] hide(LuaValue[] values) {
        emoteInputView.hide();
        if (luaHideCallback != null) {
            luaHideCallback.fastInvoke(emoteInputView.getHeight());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setEmoteFlag(LuaValue[] values) {
        emoteInputView.setEmoteFlag(values[0].toInt());
        return null;
    }

    @LuaApiUsed
    public LuaValue[] clickEmoji(LuaValue[] values) {
        if (luaClickEmoteCallback != null) {
            luaClickEmoteCallback.destroy();
        }
        luaClickEmoteCallback = values[0].isFunction() ? values[0].toLuaFunction() : null;
        emoteInputView.setOnEmoteSelectedListener(new OnEmoteSelectedListener() {
            @Override
            public void onEmoteSelected(CharSequence emoteString, int type) {
                luaClickEmoteCallback.fastInvoke(emoteString.toString());
            }
        });
        return null;
    }

    @LuaApiUsed
    public LuaValue[] willShow(LuaValue[] values) {
        if (luaShowCallback != null) {
            luaShowCallback.destroy();
        }
        luaShowCallback = values[0].isFunction() ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] willHidden(LuaValue[] values) {
        if (luaHideCallback != null) {
            luaHideCallback.destroy();
        }
        luaHideCallback = values[0].isFunction() ? values[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] hideTab(LuaValue[] values) {
        emoteInputView.showLetschatStyle();
        return null;
    }


}
