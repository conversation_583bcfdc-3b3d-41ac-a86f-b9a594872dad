package com.immomo.momo.luaview.lt;

import android.content.Context;
import android.text.TextUtils;

import com.immomo.android.router.momo.business.abtest.ABConfigRouter;
import com.immomo.mls.MLSEngine;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.momo.newaccount.login.bean.AbConfigBean;

import info.xudshen.android.appasm.AppAsm;

/**
 * <AUTHOR>
 * @date 2022/11/29 18:36
 * @desc ab实验桥接类
 * @modified
 */
@LuaClass(isStatic = true)
public class LTABTestManager {
    public static final String LUA_CLASS_NAME = "ABTestManager";

    private static Context getContext() {
        return MLSEngine.getContext();
    }

    /**
     * 通用实验桥接方法
     * @return
     */
    @LuaBridge
    public static boolean inABTestGroup(String key,String abName) {
        AbConfigBean bean =
                AppAsm.getRouter(ABConfigRouter.class).getExperimentByKey(key,AbConfigBean.class);
        return bean != null && TextUtils.equals(bean.getGroupId(),abName);
    }

}
