package com.immomo.momo.luaview.pipeline.view;

import android.app.Activity;
import android.app.Dialog;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;

import com.core.glcore.cv.MMCVInfo;
import com.immomo.framework.utils.UIUtils;
import com.immomo.game.view.CountDownView;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.molive.gui.common.filter.MLAdjustFilter;
import com.immomo.momo.MomoApplicationEvent;
import com.immomo.momo.R;
import com.immomo.momo.luaview.pipeline.GameLuaLiveManager;

import androidx.annotation.NonNull;

public class GameLuaLivePreviewDialog extends Dialog implements View.OnClickListener, DialogInterface.OnCancelListener, DialogInterface.OnDismissListener {

    private Activity mActivity;
    private GameLuaLiveManager mGameLuaLiveManager;
    private GameLuaPreviewCallback mLuaPrevieweCallback;
    private FrameLayout surfaceLayout;
    private CountDownView countdownView;

    public GameLuaLivePreviewDialog(@NonNull Activity activity, GameLuaLiveManager mGameLuaLiveManager) {
        super(activity, R.style.AnchorToolDialog);
        this.mGameLuaLiveManager = mGameLuaLiveManager;

        Window window = this.getWindow();
        WindowManager.LayoutParams lp = window.getAttributes();
        window.getDecorView().setPadding(0, 0, 0, 0);
        lp.width = WindowManager.LayoutParams.MATCH_PARENT;
        //设置窗口高度为包裹内容
        lp.height = WindowManager.LayoutParams.MATCH_PARENT;//UIUtils.getPixels(175);
        window.setWindowAnimations(R.style.LiveSlideNormalAnimation);
        window.setAttributes(lp);
        setCancelable(true);

        mActivity = activity;
        init();
    }

    private void init() {
        initView();
        initEvent();
    }

    private void initView() {
        ViewGroup contentView = (ViewGroup) LayoutInflater.from(mActivity).inflate(R.layout.game_view_web_preview, null);
        surfaceLayout = new FrameLayout(getContext());

        int width = UIUtils.getScreenWidth();
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(width,width);
        params.addRule(RelativeLayout.CENTER_IN_PARENT);
        surfaceLayout.setLayoutParams(params);
        contentView.addView(surfaceLayout,0);

        setContentView(contentView);

    }

    private void initEvent() {
        findViewById(R.id.game_view_web_preview_back).setOnClickListener(this);
        findViewById(R.id.game_view_preview_refresh).setOnClickListener(this);
        findViewById(R.id.game_view_web_preview_beauty).setOnClickListener(this);
        findViewById(R.id.game_view_web_preview_rectangle).setOnClickListener(this);
        countdownView = findViewById(R.id.game_webvideo_countdown);

        setOnCancelListener(this);
        setOnDismissListener(this);
    }

    private void showPreviewView(){
        if (this.mGameLuaLiveManager != null){
            //加载模型数据
            this.mGameLuaLiveManager.loadModle(new MLAdjustFilter.FaceDetectedListener(){
                @Override
                public void faceDetectd(MMCVInfo mmcvInfo) {

                }
            });

            this.mGameLuaLiveManager.showPreviewSurfaceView(surfaceLayout,mGameLuaLiveManager.getUID());
        }


    }

    @Override
    public void dismiss() {
        try {
            if (isShowing()){
                super.dismiss();
            }
        } catch (IllegalArgumentException e) {
        } catch (Exception e) {
        }

    }

    @Override
    public void show() {
        super.show();
        showPreviewView();
        if (mGameLuaLiveManager != null){
            mGameLuaLiveManager.loadNetFilterInfo();
        }
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.game_view_web_preview_back:
                dismiss();
                if (GameLuaLivePreviewDialog.this.mLuaPrevieweCallback != null){
                    GameLuaLivePreviewDialog.this.mLuaPrevieweCallback.onCancelPreview();
                }
                break;
            case R.id.game_view_preview_refresh:
                if (countdownView != null && countdownView.isPlaying()){
                    Toaster.show("视频准备开启时无法切换");
                }else{
                    if (this.mGameLuaLiveManager != null){
                        this.mGameLuaLiveManager.switchCamera();
                    }
                }
                break;
            case R.id.game_view_web_preview_beauty:
                if (this.mGameLuaLiveManager != null){
                    this.mGameLuaLiveManager.showFaceSettingViewV2();
                }
                break;
            case R.id.game_view_web_preview_rectangle:

                CountDownView countdownView = findViewById(R.id.game_webvideo_countdown);
                countdownView.setOnCountdownListener(new CountDownView.CountdownEndListener() {
                    @Override
                    public void onCountDownEnd() {
                        dismiss();
                        //判断下应用是否在前台，非前台不回调
                        boolean isAppForeground = MomoApplicationEvent.isForeground();
                        if (isAppForeground){
                            if (GameLuaLivePreviewDialog.this.mLuaPrevieweCallback != null){
                                GameLuaLivePreviewDialog.this.mLuaPrevieweCallback.onCountDownEnd();
                            }
                        }else{
                            if (GameLuaLivePreviewDialog.this.mLuaPrevieweCallback != null){
                                GameLuaLivePreviewDialog.this.mLuaPrevieweCallback.onCancelPreview();
                            }
                        }

                    }
                });
                countdownView.startCountdown();
                break;
        }
    }

    public void setPrevieweCallback(GameLuaPreviewCallback mLuaPrevieweCallback) {
        this.mLuaPrevieweCallback = mLuaPrevieweCallback;
    }

    @Override
    public void onCancel(DialogInterface dialog) {
        if (GameLuaLivePreviewDialog.this.mLuaPrevieweCallback != null){
            GameLuaLivePreviewDialog.this.mLuaPrevieweCallback.onCancelPreview();
        }
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        try {
            if (surfaceLayout != null){
                surfaceLayout.removeAllViews();
            }
            if (countdownView != null){
                countdownView.setOnCountdownListener(null);
                countdownView.stopCountDown();
            }
            if (mGameLuaLiveManager != null){
                mGameLuaLiveManager.unRegisterFilterDownloadCompleteSubscriber();
            }
        } catch (Exception e) {
        }
    }

    public interface GameLuaPreviewCallback{
        void onCountDownEnd();
        void onCancelPreview();
    }

}