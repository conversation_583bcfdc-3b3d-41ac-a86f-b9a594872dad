package com.immomo.momo.luaview.ud;


import com.immomo.mls.MLSEngine;
import com.immomo.mls.fun.ud.UDMap;
import com.immomo.momo.feed.activity.SelectFeedSiteActivity;
import com.immomo.momo.feed.bean.BasePublishConstant;
import com.immomo.android.module.nearbypeople.presentation.helper.NearbyPeopleClockInBean;
import com.immomo.momo.globalevent.GlobalEventManager;

import org.luaj.vm2.JavaUserdata;
import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by lei.jialin on 2019/9/4.
 * 选择位置页面
 */
@LuaApiUsed
public class UDSitePicker extends JavaUserdata {

    public static final String LUA_CLASS_NAME = "LuaPlatformCommonHelper";

    public static final String[] methods = {
            "startPickSite",
            "startPickSiteFromFeed",
            "setDealWithDataCallback",
            "setDealWithFeedDataCallback",
            "publishFeedWithDic",
            "publishFeedStayLuaWithDic"
    };

    private static final String SITE_SELECTED_EVENT = "site_selected_event";
    public static final String SITE_PUNCH_FEED_PUBLISH = "site_punch_feed_publish";
    public static final String NIGHT_PUNCH_FEED_PUBLISH = "night_punch_feed_publish";
    private static final String TAG = UDSitePicker.class.getSimpleName();
    private static final String KEY_RAW_CLOCK = "key_raw_clock";
    private static final String KEY_ORIGIN_LIST = "key_origin_list";

    private LuaFunction pictureLocalUrlCallBack;
    private GlobalEventManager.Subscriber subscriber;

    @LuaApiUsed
    protected UDSitePicker(long L, LuaValue[] v) {
        super(L, v);
        initBridge();
    }

    public static void postEvent2Lua(Map<String, Object> clockin) {
        GlobalEventManager.getInstance().sendEvent(new GlobalEventManager.Event(UDSitePicker.SITE_SELECTED_EVENT).dst("native").src("native").msg(clockin));
    }

    @LuaApiUsed
    public LuaValue[] setDealWithDataCallback(LuaValue[] values) {
        if (values.length >= 1)
            pictureLocalUrlCallBack = values[0].toLuaFunction();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setDealWithFeedDataCallback(LuaValue[] values) {
        if (values.length >= 1)
            pictureLocalUrlCallBack = values[0].toLuaFunction();
        return null;
    }

    public static List<File> extractStringToFiles(Object obj) {
        List<Map<String, String>> pathList = null;
        if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            if (!list.isEmpty() && list.get(0) instanceof Map) {
                pathList = (List<Map<String, String>>) list;
            }
        }
        if (pathList == null || pathList.isEmpty()) {
            return null;
        }
        List<File> files = new ArrayList<>();
        for (int i = 0; i < pathList.size(); i++) {
            String originPath = pathList.get(i).get(UDCameraHelper.KEY_ORIGIN_IMAGE);
            File file = new File(originPath);
            if (file.exists()) {
                files.add(file);
            }
        }
        return files;
    }

    @LuaApiUsed
    public LuaValue[] publishFeedWithDic(LuaValue[] values) {
        publishFeed(values, false);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] publishFeedStayLuaWithDic(LuaValue[] values) {
        publishFeed(values, true);
        return null;
    }

    /**
     * @param values
     * @param isNight 是否是失眠夜话
     */
    private void publishFeed(LuaValue[] values, boolean isNight) {
        LuaValue firstMap = values.length > 0 ? values[0] : null;
        if (firstMap instanceof UDMap) {
            Map<String, String> m = ((UDMap) firstMap).getMap();
            HashMap<String, Object> hashMap = new HashMap<>(m);
            GlobalEventManager.getInstance().sendEvent(new GlobalEventManager.Event(isNight ? UDSitePicker.NIGHT_PUNCH_FEED_PUBLISH : UDSitePicker.SITE_PUNCH_FEED_PUBLISH).dst("native").src("native").msg(hashMap));
        }
    }

    @Override
    protected void __onLuaGc() {
        super.__onLuaGc();
        releaseReceiver();
        if (pictureLocalUrlCallBack != null) {
            pictureLocalUrlCallBack.destroy();
        }
    }

    private void releaseReceiver() {
        GlobalEventManager.getInstance().unregister(subscriber, "native");
    }

    //<editor-fold desc="API">
    @LuaApiUsed
    public LuaValue[] startPickSite(LuaValue[] values) {
        SelectFeedSiteActivity.openActivity(MLSEngine.getContext(), BasePublishConstant.ACT_RES_SELECT_SITE, SelectFeedSiteActivity.FROM_TYPE_LUA);
        return null;
    }

    //<editor-fold desc="API">
    @LuaApiUsed
    public LuaValue[] startPickSiteFromFeed(LuaValue[] values) {
        SelectFeedSiteActivity.openActivity(MLSEngine.getContext(), BasePublishConstant.ACT_RES_SELECT_SITE, SelectFeedSiteActivity.FROM_TYPE_FEED);
        return null;
    }

    private ArrayList<NearbyPeopleClockInBean.Item> convertToListIfNeed(List<NearbyPeopleClockInBean.Item> origin) {
        ArrayList<NearbyPeopleClockInBean.Item> list = new ArrayList<>();
        for (int i = 0; i < origin.size(); i++) {
            NearbyPeopleClockInBean.Item clockIn = origin.get(i);
            if (clockIn == null) {
                break;
            }
            NearbyPeopleClockInBean.Item bean = new NearbyPeopleClockInBean.Item();
            bean.siteId = clockIn.siteId;
            bean.siteName = clockIn.siteName;
            bean.lat = clockIn.lat;
            bean.lng = clockIn.lng;
            list.add(bean);
        }
        return list;
    }

    //</editor-fold>

    private void initBridge() {
        if (subscriber == null) {
            subscriber = new GlobalEventManager.Subscriber() {
                @Override
                public void onGlobalEventReceived(GlobalEventManager.Event event) {
                    if (event == null) {
                        return;
                    }
                    if (SITE_SELECTED_EVENT.equals(event.getName())) {
                        if (pictureLocalUrlCallBack != null) {
                            pictureLocalUrlCallBack.invoke(varargsOf(new UDMap(globals, event.getMsg())));
                        }
                    }
                }
            };
        }
        releaseReceiver();
        GlobalEventManager.getInstance().register(subscriber, "native");
    }

}