package com.immomo.momo.luaview.ud.sliceupload;

import android.content.Context;
import android.text.TextUtils;
import android.util.Pair;

import com.immomo.mls.LuaViewManager;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.luaview.ud.sliceupload.api.impl.SimpleUploadApiImpl;
import com.immomo.momo.luaview.ud.sliceupload.callback.SliceUploadListener;
import com.immomo.momo.luaview.ud.sliceupload.handler.SimpleSliceUploadHandler;
import com.immomo.momo.luaview.ud.sliceupload.model.SliceUploadModel;
import com.immomo.momo.luaview.ud.sliceupload.model.SliceUploadResult;
import com.immomo.momo.luaview.ud.sliceupload.task.SliceUploadTask;

import org.luaj.vm2.Globals;
import org.luaj.vm2.LuaValue;

import java.io.File;
import java.util.Map;

/**
 * Created on 2019-11-30.
 *
 * <AUTHOR>
 */

@LuaClass
public class UDSliceSimpleUploadProcess {
    class State {
        static final String START = "start";
        static final String CANCELLED = "cancelled";
        static final String FAILED = "failed";
        static final String SUCCESS = "success";
    }

    public static final String LUA_CLASS_NAME = "SliceSimpleUploadProcess";

    private final Context mContext;


    public UDSliceSimpleUploadProcess(Globals g, LuaValue[] init) {
        this.mContext = ((LuaViewManager) g.getJavaUserdata()).context;
    }

    @LuaBridge
    public void requestSliceUpload(String url, String tag, boolean isNeedSlice, String videoFile, Map expandParams, final LVCallback stateCallback, final LVCallback processCallback) {

        if (TextUtils.isEmpty(videoFile)) {
            return;
        }

        File file = new File(videoFile);
        if (!file.exists()) {
            return;
        }
        SliceUploadModel sliceUploadModel = new SliceUploadModel(file);
        sliceUploadModel.setTargetFileUuid(tag);

        sliceUploadModel.setCustomParams(expandParams);

        SliceUploadTask uploadTask = new SliceUploadTask(new SimpleSliceUploadHandler(new SimpleUploadApiImpl(url), isNeedSlice), sliceUploadModel, new SliceUploadListener() {

            @Override
            public void onStart() {
                if (stateCallback != null) {
                    stateCallback.call(State.START);
                }
            }

            @Override
            public void onProcess(Pair<Long, Long> progress, String uuid) {
                if (processCallback != null) {
                    MomoMainThreadExecutor.post(() -> {
                        if (processCallback != null) {
                            processCallback.call(progress.first * 1.0f / progress.second);
                        }
                    });
                }
            }

            @Override
            public void onCancelled() {
                if (stateCallback != null) {
                    stateCallback.call(State.CANCELLED);
                }
            }

            @Override
            public void onFailed() {

                if (stateCallback != null) {
                    stateCallback.call(State.FAILED);
                }
            }

            @Override
            public void onSuccess(SliceUploadResult result) {
                if (stateCallback != null && result != null) {
                    stateCallback.call(State.SUCCESS, result.getFileName());
                }
            }
        });
        MomoTaskExecutor.executeUserTask(tag, uploadTask);
    }

    @LuaBridge
    public void cancel(String tag) {
        MomoTaskExecutor.cancleAllTasksByTag(tag);
    }
}
