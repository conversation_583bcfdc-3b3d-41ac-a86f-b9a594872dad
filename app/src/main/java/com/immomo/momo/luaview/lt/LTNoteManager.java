package com.immomo.momo.luaview.lt;

import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.protocol.http.requestbean.ChatMediaResponse;
import com.immomo.momo.protocol.http.requestbean.UploadMedia;
import com.immomo.momo.protocol.imjson.RangeUploadHandler;
import com.immomo.momo.protocol.imjson.util.JusticeHelper;
import com.immomo.momo.util.DataUtil;

import java.io.File;
import java.util.HashMap;

import io.reactivex.Observable;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.annotations.NonNull;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

@LuaClass(isStatic = true)
public class LTNoteManager {
    public static final String LUA_CLASS_NAME = "NoteUploader";
    private static final int MEDIA_TYPE_IMG = 1;
    private static final int MEDIA_TYPE_VIDEO = 2;


    @LuaBridge
    public static void sendFile(String fileName, int type, String uuid, HashMap<String, String> params, LVCallback callback) {
        if (type == MEDIA_TYPE_IMG) {
            checkImgSpam(fileName, uuid, callback, params);
        } else if (type == MEDIA_TYPE_VIDEO) {
            checkVideoSpam(fileName, uuid, callback, params);
        }
    }


    private static void checkImgSpam(String fileName, String uuid, LVCallback callback, HashMap<String, String> params) {
        Observable.create((ObservableOnSubscribe<String>) e -> {
            if (JusticeHelper.isPhotoSpamReady()) {
                JusticeHelper.getInstance().getPhotoSpam(fileName, spamJsonResult -> {
                    e.onNext(spamJsonResult);
                    e.onComplete();
                });
            } else {
                e.onNext("");
                e.onComplete();

            }

        }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<String>() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {

                    }

                    @Override
                    public void onNext(@NonNull String s) {
                        MomoTaskExecutor.executeUserTask(LUA_CLASS_NAME, new UploadFileTask(params.get("remoteid"), s, callback, fileName, 1, uuid));

                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        MomoTaskExecutor.executeUserTask(LUA_CLASS_NAME, new UploadFileTask(params.get("remoteid"), "", callback, fileName, 1,uuid));

                    }

                    @Override
                    public void onComplete() {

                    }
                });


    }

    private static void checkVideoSpam(String fileName, String uuid, LVCallback callback, HashMap<String, String> params) {
        Observable.create((ObservableOnSubscribe<String>) e -> {
            if (JusticeHelper.isPublishAntiVideoReady()) {
                JusticeHelper.getInstance().getVideoPublishSpam(fileName, spamJsonResult -> {
                    e.onNext(spamJsonResult);
                    e.onComplete();
                });
            } else {
                e.onNext("");
                e.onComplete();
            }

        }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<String>() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {

                    }

                    @Override
                    public void onNext(@NonNull String s) {
                        MomoTaskExecutor.executeUserTask(LUA_CLASS_NAME, new UploadFileTask(params.get("remoteid"), s, callback, fileName, 2, uuid));
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        MomoTaskExecutor.executeUserTask(LUA_CLASS_NAME, new UploadFileTask(params.get("remoteid"), "", callback, fileName, 2,uuid));

                    }

                    @Override
                    public void onComplete() {

                    }
                });
    }

    static class UploadFileTask extends MomoTaskExecutor.Task<Void, Void, HashMap<String, String>> {
        String remoteId;
        String spamInfo;
        String fileName;
        String uuid;
        int type;
        LVCallback callback;

        public UploadFileTask(String remoteId, String spamInfo, LVCallback callback, String fileName, int type, String uuid) {
            this.remoteId = remoteId;
            this.spamInfo = spamInfo;
            this.fileName = fileName;
            this.type = type;
            this.callback = callback;
            this.uuid = uuid;
        }

        @Override
        protected HashMap<String, String> executeTask(Void... voids) throws Exception {
            HashMap<String, String> map = new HashMap<>();
            try {
                UploadMedia request = new UploadMedia(fileName);
                request.setPredictInfo(spamInfo);
                ChatMediaResponse response = upload(uuid, 0L, remoteId, fileName, request, type);
                String picName = response.getRemoteFileName();
                String spamResult = request.getPredictInfo();
                if (DataUtil.hasValue(picName)) {
                    map.put("isSuc", "1");
                    map.put("picName", picName);
                    map.put("msg", "上传成功");
                } else {
                    map.put("isSuc", "0");
                    map.put("picName", "");
                    map.put("msg", "上传失败");
                }
            } catch (Exception e) {
                map.put("isSuc", "0");
                map.put("picName", "");
                map.put("msg", e.getMessage());
            }
            return map;
        }

        @Override
        protected void onTaskSuccess(HashMap<String, String> map) {
            super.onTaskSuccess(map);
            if (map.get("isSuc").equals("1")) {
                callback.call(true, "上传成功", map.get("picName"));
            } else {
                callback.call(false, map.get("msg"), "");
            }
        }

        @Override
        protected void onTaskError(Exception e) {
            super.onTaskError(e);
            callback.call(false, e.getMessage(), "");
        }
    }


    private static ChatMediaResponse upload(String uuid, long fileLength, String remoteId, String fileName, UploadMedia request, int type) {
        try {
            File f = new File(fileName);
            if (f == null || !f.exists()) {
                throw new IllegalStateException("文件为空，不应上传");
            }
            if (fileLength == f.length()) {
                fileLength = 0;
            }


            String spamResult = request.getPredictInfo();
            UploadMedia uploadMedia = RangeUploadHandler.createUploadMedia(spamResult);
            uploadMedia.isOriginImage(true);
            String picName = RangeUploadHandler.uploadNoteImage(f, fileLength, uuid, uploadMedia, l -> {

            }, type, remoteId);

            return uploadMedia.getResponse() == null ? new ChatMediaResponse(picName) : uploadMedia.getResponse();
        } catch (Exception e) {
            return new ChatMediaResponse("");
        }
    }


}
