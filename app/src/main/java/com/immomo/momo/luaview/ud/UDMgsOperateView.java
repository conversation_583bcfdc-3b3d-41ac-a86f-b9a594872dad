package com.immomo.momo.luaview.ud;

import com.cosmos.mdlog.MDLog;
import com.immomo.android.module.feedlist.domain.model.style.common.CommonFeedWithMgsOperateModel;
import com.immomo.android.module.feedlist.domain.model.style.common.MgsGameModel;
import com.immomo.android.module.nearbypeople.lua.util.NearbyPeopleHelper;
import com.immomo.framework.utils.UIUtils;
import com.immomo.mls.fun.ud.view.UDView;
import com.immomo.momo.businessmodel.statistics.ILogRecordHelper;
import com.immomo.momo.feed.ui.view.MgsOperateView;

import org.jetbrains.annotations.NotNull;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import static com.immomo.momo.group.mgs_game.ConfigKey.THEME_RED;
import static com.immomo.momo.group.mgs_game.ConfigKey.THEME_WHITE;

/**
 * Created by qu.jiaqi
 * on 2021/2/2
 */
@LuaApiUsed
public class UDMgsOperateView extends UDView<MgsOperateView> {
    public static final String LUA_CLASS_NAME = "MgsOperateView";

    private MgsOperateView mgsOperateView;

    public static final String[] methods = {
            "bindData",
            "appear",
            "disappear",
    };

    @LuaApiUsed
    public UDMgsOperateView(long L, LuaValue[] initParams) {
        super(L, initParams);
    }

    @NotNull
    @Override
    protected MgsOperateView newView(LuaValue[] init) {
        mgsOperateView = new MgsOperateView(getContext());
        mgsOperateView.needShowPlayIcon();
        mgsOperateView.setOnMgsOperateClickListener();
        return mgsOperateView;
    }

    @LuaApiUsed
    public LuaValue[] bindData(LuaValue[] fun) {

        if (fun != null && fun.length > 1 && fun[0] != null && fun[1] != null) {
            String json = fun[0].toJavaString();
            String id = fun[1].toJavaString();
            try {
                CommonFeedWithMgsOperateModel model = NearbyPeopleHelper.Companion.parseCommonFeedWithMgsOperateModel(json);
                MgsGameModel gameModel = model.getMgsGame().orNull();
                mgsOperateView.setMgsModel(gameModel, model.getLoggerPos(), ILogRecordHelper.PeopleSource.NEARBY, false, id, "", "");
                refreshItemTheme();
            } catch (Exception e) {
                MDLog.printErrStackTrace(LUA_CLASS_NAME, e);
            }
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] appear(LuaValue[] fun) {
        mgsOperateView.attachedToWindow();
        return null;
    }


    @LuaApiUsed
    public LuaValue[] disappear(LuaValue[] fun) {
        mgsOperateView.detachedFromWindow();
        return null;
    }

    /**
     * 设置主题
     */
    private void refreshItemTheme() {
        String themeType = mgsOperateView.getThemeType();
        if (themeType == null) {
            return;
        }
        switch (themeType) {
            case THEME_RED:
                mgsOperateView.refreshItemTheme(
                        UIUtils.getColor(com.immomo.android.momo.feed.R.color.feed_mgs_gold),
                        UIUtils.getColor(com.immomo.android.momo.feed.R.color.feed_mgs_red),
                        UIUtils.getColor(com.immomo.android.momo.feed.R.color.feed_mgs_red),
                        UIUtils.getColor(com.immomo.android.momo.feed.R.color.feed_mgs_gold),
                        UIUtils.getColor(com.immomo.android.momo.feed.R.color.feed_mgs_gold),
                        UIUtils.getColor(com.immomo.android.momo.feed.R.color.feed_mgs_alpha_gray));
                break;
            case THEME_WHITE:
                mgsOperateView.refreshItemTheme(
                        UIUtils.getColor(com.immomo.android.momo.feed.R.color.feed_mgs_blue),
                        UIUtils.getColor(com.immomo.android.momo.feed.R.color.feed_mgs_white),
                        UIUtils.getColor(com.immomo.android.momo.feed.R.color.transparent),
                        UIUtils.getColor(com.immomo.android.momo.feed.R.color.feed_mgs_gray),
                        UIUtils.getColor(com.immomo.android.momo.feed.R.color.feed_mgs_white),
                        UIUtils.getColor(com.immomo.android.momo.feed.R.color.feed_mgs_alpha_gray));
                break;
            default:
                break;
        }
    }

}
