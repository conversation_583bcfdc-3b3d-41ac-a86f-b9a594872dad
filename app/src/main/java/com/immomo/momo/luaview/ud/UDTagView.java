package com.immomo.momo.luaview.ud;

import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.immomo.framework.utils.UIUtils;
import com.immomo.mls.fun.ud.UDColor;
import com.immomo.mls.fun.ud.view.UDViewGroup;
import com.immomo.mls.utils.convert.ConvertUtils;
import com.immomo.momo.R;
import com.immomo.momo.android.view.BadgeView;
import com.immomo.momo.luaview.weight.AutoHideTextView;
import com.immomo.momo.router.SimpleProfileGrowthInfo;
import com.immomo.momo.router.SimpleProfileSpecialInfo;
import com.immomo.momo.router.UserSvipPoint;
import com.immomo.momo.service.bean.User;

import org.luaj.vm2.LuaString;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import java.util.List;
import java.util.Map;

import static com.immomo.momo.luaview.constants.TagViewType.TagViewTypeAgeAndSexF;
import static com.immomo.momo.luaview.constants.TagViewType.TagViewTypeAgeAndSexM;
import static com.immomo.momo.luaview.constants.TagViewType.TagViewTypeConstellation;
import static com.immomo.momo.luaview.constants.TagViewType.TagViewTypeFriend;
import static com.immomo.momo.luaview.constants.TagViewType.TagViewTypeGrowupLevel;
import static com.immomo.momo.luaview.constants.TagViewType.TagViewTypeIndustry;
import static com.immomo.momo.luaview.constants.TagViewType.TagViewTypeNoVIP;
import static com.immomo.momo.luaview.constants.TagViewType.TagViewTypeOffcial;
import static com.immomo.momo.luaview.constants.TagViewType.TagViewTypePrettyId;
import static com.immomo.momo.luaview.constants.TagViewType.TagViewTypeSVIP;
import static com.immomo.momo.luaview.constants.TagViewType.TagViewTypeVIP;
import static com.immomo.momo.luaview.constants.TagViewType.TagViewTypeYearSVIP;
import static com.immomo.momo.luaview.constants.TagViewType.TagViewTypeYearVIP;



/**
 * 和lua交互的标签视图
 * Created by fanqiang on 2018/9/25.
 */
@LuaApiUsed
public class UDTagView<V extends BadgeView> extends UDViewGroup<V> {

    public static final String LUA_CLASS_NAME = "TagView";

    public static final String[] methods = {
            "addTagViewItem",
            "addTextTagViewItems",
            "clear"
    };

    private static final String KEY_CONTENT = "content";
    private static final String KEY_TEXTCOLOR = "textColor";
    private static final String KEY_BBGCOLOR = "bgColor";
    private UDColor tempColor;
    private int textTagViewInfoNum = 0;

    @LuaApiUsed
    public UDTagView(long L, LuaValue[] v) {
        super(L, v);
    }

    @Override
    protected V newView(LuaValue[] init) {
        return (V) new BadgeView(getContext());
    }

    @LuaApiUsed
    public LuaValue[] addTagViewItem(LuaValue[] values) {
        if (values.length != 2) {
            return null;
        }
        int type = values[0].toInt();
        String content = values[1].toJavaString();

        switch (type) {
            case TagViewTypeAgeAndSexF:
                getView().showGenderOrOfficialLayout(getGenderUser("F", (int) Double.valueOf(content).doubleValue()));
                break;
            case TagViewTypeAgeAndSexM:
                getView().showGenderOrOfficialLayout(getGenderUser("M", (int) Double.valueOf(content).doubleValue()));
                break;
            case TagViewTypeNoVIP:
            case TagViewTypeVIP:
            case TagViewTypeYearVIP:
            case TagViewTypeSVIP:
            case TagViewTypeYearSVIP:
            case TagViewTypePrettyId:   //vip靓号
                User user = getVipUser(type, (int) Double.valueOf(content).doubleValue());
                getView().showVIP(user);
                break;
            case TagViewTypeGrowupLevel:
                getView().showGrade(getGradeUser((int) Double.valueOf(content).doubleValue()));
                break;
            case TagViewTypeFriend:
                getView().showRelation();
                break;
            case TagViewTypeOffcial:
                getView().showGenderOrOfficialLayout(getOfficialUser());
                break;
            case TagViewTypeConstellation:
                getView().showConstellationLua(getConstellationUser(content));
                break;
            case TagViewTypeIndustry:
                getView().showIndustry(getIndustryUser(content));
                break;
            default:
                break;
        }

        return null;
    }

    @LuaApiUsed
    public LuaValue[] addTextTagViewItems(LuaValue[] arrays) {
        if (arrays.length == 0) {
            return null;
        }
        List array = (List) ConvertUtils.toNativeValue(arrays[0]);
        if (array == null || array.size() == 0) {
            return null;
        }
        int size = array.size();
        Object object;
        String content;
        String textColor;
        String bgColor;
        Object value;
        boolean addMargin;
        for (int i = 0; i < size; i++) {
            object = array.get(i);
            content = null;
            textColor = null;
            bgColor = null;
            if (object instanceof Map) {
                if ((value = ((Map) object).get(KEY_CONTENT)) instanceof String) {
                    content = (String) value;
                }
                if ((value = ((Map) object).get(KEY_TEXTCOLOR)) instanceof String) {
                    textColor = (String) value;
                }
                if ((value = ((Map) object).get(KEY_BBGCOLOR)) instanceof String) {
                    bgColor = (String) value;
                }
                addMargin = false;
                if (i > 0) {
                    addMargin = true;
                }
                setTextTagViewInfo(content, textColor, bgColor, addMargin);
            }
        }

        return null;
    }

    @LuaApiUsed
    public LuaValue[] clear(LuaValue[] values) {
        getView().showNone();
        if (textTagViewInfoNum > 0) {
            int childCount = getView().getChildCount();
            getView().removeViews(childCount - textTagViewInfoNum, textTagViewInfoNum);
        }
        textTagViewInfoNum = 0;
        return null;
    }

    private User getIndustryUser(String content) {
        User user = new User();
        user.profileSpecialInfo = new SimpleProfileSpecialInfo(content);
        return user;
    }

    private User getConstellationUser(String constellation) {
        User user = new User();
        user.constellation = constellation;
        return user;
    }

    private User getGradeUser(int level) {
        User user = new User();
        user.profileGrowthInfo = new SimpleProfileGrowthInfo(level);
        return user;
    }

    private User getVipUser(int type, int level) {
        User user = new User();
        switch (type) {
            case TagViewTypeNoVIP:
                user.setIsVip(false);
                break;
            case TagViewTypeVIP:
                user.setIsVip(true);
                user.setVipActivityLevel(level);
                break;
            case TagViewTypeYearVIP:
                user.setIsVip(true);
                user.setVipActivityLevel(level);
                user.setVipYear(level);
                break;
            case TagViewTypeSVIP:
                user.setIsVip(true);
                UserSvipPoint point = new UserSvipPoint();
                point.setSvip(true);
                point.setActive_level(level);
                user.setSvipPoint(point);
                break;
            case TagViewTypeYearSVIP:
                user.setIsVip(true);
                point = new UserSvipPoint();
                point.setSvip(true);
                point.setActive_level(level);
                point.setYearSvip(true);
                user.setSvipPoint(point);
                break;
            case TagViewTypePrettyId:    //靓号vip
                user.setIsVip(true);
                point = new UserSvipPoint();
                point.setSvip(true);
                point.setYearSvip(true);
                point.setActive_level(level);
                point.setPretty_id_type(1);
                user.setSvipPoint(point);
                break;
            default:
                break;
        }
        return user;
    }

    private User getGenderUser(String gender, int age) {
        User user = new User();
        user.sex = gender;
        user.age = age;
        return user;
    }

    private User getOfficialUser() {
        User user = new User();
        user.official = true;
        return user;
    }

    private void setTextTagViewInfo(String content, String textColor, String bgColor, boolean addMargin) {
        TextView textView = getTextView();
        textView.setTextColor(getColor(textColor));
        textView.setText(content);

        Drawable gd = textView.getBackground();
        gd.setColorFilter(getColor(bgColor), PorterDuff.Mode.SRC_IN);

        getView().addView(textView);
        textTagViewInfoNum++;
        LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) textView.getLayoutParams();
        if (addMargin) {
            params.leftMargin = UIUtils.getDimensionPixelSize(R.dimen.margin_badge);
        }
        params.height = LinearLayout.LayoutParams.MATCH_PARENT;
    }

    private TextView getTextView() {
        return (AutoHideTextView) LayoutInflater.from(getContext()).inflate(R.layout.include_lua_tagview_label, null);
    }

    private int getColor(String color) {
        if (tempColor == null) {
            tempColor = new UDColor(getGlobals(), 0);
        }
        tempColor.setColor(varargsOf(LuaString.valueOf(color)));
        return tempColor.getColor();
    }
}
