package com.immomo.momo.luaview.media;


import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.webkit.URLUtil;
import android.widget.SeekBar;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.immomo.framework.kotlin.ImageLoader;
import com.immomo.framework.kotlin.ImageLoaderOptions;
import com.immomo.framework.kotlin.ImageLoadingListener;
import com.immomo.mls.fun.ud.UDColor;
import com.immomo.mls.fun.ud.view.UDView;
import com.immomo.mls.util.DimenUtil;
import com.immomo.momo.R;
import com.immomo.momo.feed.ui.view.DisallowInterceptSeekBar;

import org.luaj.vm2.LuaBoolean;
import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaNumber;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import java.lang.reflect.Field;

/**
 * author: hongming.wei
 * data: 2023/2/21
 */

@LuaApiUsed
public class UDVideoSeekBar<V extends DisallowInterceptSeekBar> extends UDView<V> {

    public static final String LUA_CLASS_NAME = "VideoSeekBar";

    public static final String[] methods = {
            "getCurrentProgress",
            "setProgress",
            "setMax",
            "setProgressColor",
            "setProgressBackgroundColor",
            "setThumb",
            "onStartTrackingTouch",
            "onStopTrackingTouch",
            "onProgressChanged",
            "setProgressHeight"
    };
    private LuaFunction mOnStartTrackingTouchFunction;
    private LuaFunction mOnStopTrackingTouchFunction;
    private LuaFunction mOnProgressChangedFunction;

    @LuaApiUsed
    protected UDVideoSeekBar(long L, LuaValue[] v) {
        super(L, v);
        getView().setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (mOnProgressChangedFunction != null) {
                    LuaValue[] lv = new LuaValue[2];
                    lv[0] = LuaNumber.valueOf(progress);
                    lv[1] = LuaBoolean.valueOf(fromUser);
                    mOnProgressChangedFunction.invoke(lv);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                if (mOnStartTrackingTouchFunction != null) {
                    mOnStartTrackingTouchFunction.invoke(null);
                }
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                if (mOnStopTrackingTouchFunction != null) {
                    mOnStopTrackingTouchFunction.invoke(null);
                }
            }
        });
    }

    @NonNull
    @Override
    protected V newView(@NonNull LuaValue[] init) {
        return (V) LayoutInflater.from(getContext()).inflate(R.layout.layout_video_seek_bar, null);
    }

    @LuaApiUsed
    public LuaValue[] getCurrentProgress(LuaValue[] values) {
        int progress = getView().getProgress();
        LuaValue[] lv = new LuaValue[1];
        lv[0] = LuaNumber.valueOf(progress);
        return lv;
    }

    @LuaApiUsed
    public LuaValue[] setProgress(LuaValue[] values) {
        if (values == null || values.length == 0 || !values[0].isNumber()) {
            return null;
        }
        getView().setProgress(values[0].toInt());
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setMax(LuaValue[] values) {
        if (values == null || values.length == 0 || !values[0].isNumber()) {
            return null;
        }
        getView().setMax(values[0].toInt());
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setProgressColor(LuaValue[] values) {
        if (values == null || values.length == 0 || !(values[0] instanceof UDColor)) {
            return null;
        }
        UDColor color = (UDColor) values[0];
        getView().setProgressTintList(ColorStateList.valueOf(color.getColor()));
        getView().setProgressTintMode(PorterDuff.Mode.SRC);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setProgressBackgroundColor(LuaValue[] values) {
        if (values == null || values.length == 0 || !(values[0] instanceof UDColor)) {
            return null;
        }
        UDColor color = (UDColor) values[0];
        getView().setProgressBackgroundTintList(ColorStateList.valueOf(color.getColor()));
        getView().setProgressBackgroundTintMode(PorterDuff.Mode.SRC);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setThumb(LuaValue[] values) {
        if (values == null || values.length == 0 || !values[0].isString()) {
            return null;
        }
        final String url = values[0].toJavaString();
        final boolean isNetworkUrl = URLUtil.isNetworkUrl(url);
        if (isNetworkUrl) {
            ImageLoader.load(url).listener(new ImageLoadingListener<Drawable>() {
                @Override
                public void onLoadStarted(@NonNull ImageLoaderOptions.Model model, @Nullable Drawable drawable) {

                }

                @Override
                public void onLoadCompleted(@NonNull ImageLoaderOptions.Model model, Drawable drawable) {
                    getView().setThumb(drawable);
                }

                @Override
                public void onLoadFailed(@NonNull ImageLoaderOptions.Model model, @Nullable Drawable drawable) {

                }

                @Override
                public void onLoadCancelled(@NonNull ImageLoaderOptions.Model model, @Nullable Drawable drawable) {

                }
            }).into();
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] onStartTrackingTouch(LuaValue[] v) {
        if (mOnStartTrackingTouchFunction != null) {
            mOnStartTrackingTouchFunction.destroy();
        }
        mOnStartTrackingTouchFunction = v[0].toLuaFunction();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] onStopTrackingTouch(LuaValue[] v) {
        if (mOnStopTrackingTouchFunction != null) {
            mOnStopTrackingTouchFunction.destroy();
        }
        mOnStopTrackingTouchFunction = v[0].toLuaFunction();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] onProgressChanged(LuaValue[] v) {
        if (mOnProgressChangedFunction != null) {
            mOnProgressChangedFunction.destroy();
        }
        mOnProgressChangedFunction = v[0].toLuaFunction();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setProgressHeight(LuaValue[] values) {
        if (values == null || values.length == 0) {
            return null;
        }
        double src = values[0].toDouble();
        checkSize(src);
        int w = DimenUtil.dpiToPx(src);
        int cs = DimenUtil.check(w);
        setMinHeight(cs);
        setMaxHeight(cs);
        getView().requestLayout();
        return null;
    }

    private void setMinHeight(int height) {
        try {
            Class<?> superclass = getView().getClass().getSuperclass().getSuperclass().getSuperclass();
            Field mMinHeight = superclass.getDeclaredField("mMinHeight");
            mMinHeight.setAccessible(true);
            mMinHeight.set(getView(), height);
        } catch (Exception e) {
        }
    }

    private void setMaxHeight(int height) {
        try {
            Class<?> superclass = getView().getClass().getSuperclass().getSuperclass().getSuperclass();
            Field mMaxHeight = superclass.getDeclaredField("mMaxHeight");
            mMaxHeight.setAccessible(true);
            mMaxHeight.set(getView(), height);
        } catch (Exception e) {
        }
    }

}
