package com.immomo.momo.luaview.ud;

import android.content.Context;
import android.net.Uri;

import com.immomo.mls.LuaViewManager;
import com.immomo.momo.feed.player.IMediaPlayer;
import com.immomo.momo.luaview.LuaRadioPlayer;
import com.immomo.momo.luaview.constants.PlayStatus;

import org.luaj.vm2.JavaUserdata;
import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaNumber;
import org.luaj.vm2.LuaString;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

/**
 * Created by fanqiang on 2018/9/5.
 */

@LuaApiUsed
public class UDRadioPlayer extends JavaUserdata implements LuaRadioPlayer.Callback {
    public static final String LUA_CLASS_NAME = "RadioPlayer";
    public static final String[] methods = {
            "src",
            "mute",
            "totalDuration",
            "directAccess",
            "play",
            "pause",
            "stop",
            "setStartStallingCallback",
            "setEndStallingCallback",
            "setFinishCallback",
            "setFailCallback",
            "setWillRepeatCallback",
            "getPlayStatus",

    };
    private LuaRadioPlayer mIJKPlayer;

    private Uri uri;

    private boolean isMute;
    private long totalDuration;

    private long stopPoint = 0;
    private LuaFunction startStallingFun;
    private LuaFunction endStallingFun;
    private LuaFunction finishFun;
    private LuaFunction failFun;
    private LuaFunction willRepeatFun;
    private int playStatus = PlayStatus.IDEL;
    private boolean hasStalling = false;
    private boolean isStop = true;

    @LuaApiUsed
    protected UDRadioPlayer(long L, LuaValue[] v) {
        super(L, v);
        this.mIJKPlayer = new LuaRadioPlayer(getContext());
        if (v != null && v.length > 0) {
            uri = Uri.parse(v[0].toJavaString());
        }
        initView();
    }

    public Context getContext() {
        LuaViewManager m = (LuaViewManager) globals.getJavaUserdata();
        return m != null ? m.context : null;
    }

    @LuaApiUsed
    public LuaValue[] src(LuaValue[] src) {
        if (src.length == 0) {
            return LuaValue.rString(uri == null ? null : uri.toString());
        }
        uri = Uri.parse(src[0].toJavaString());
        if (getIJKPlayer() != null) {
            getIJKPlayer().setUri(uri);
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] mute(LuaValue[] isMute) {
        if (isMute.length == 0) {
            return LuaValue.rBoolean(this.isMute);
        }
        this.isMute = isMute[0].toBoolean();
        if (getIJKPlayer() != null) {
            getIJKPlayer().setSilentMode(this.isMute);
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] totalDuration(LuaValue[] duration) {
        if (duration.length == 0) {
            return LuaValue.rNumber(getIJKPlayer().getDuration() * 1.0 / 1000);
        }
        totalDuration = duration[0].toInt();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] directAccess(LuaValue[] directAccess) {
        if (directAccess.length == 0) {
            return LuaValue.rBoolean(!mIJKPlayer.getUseProxyOrCache());
        }
        getIJKPlayer().setUseProxyOrCache(!directAccess[0].toBoolean());
        return null;
    }

    @LuaApiUsed
    public LuaValue[] play(LuaValue[] values) {
        startPlayVideo();
        isStop = false;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] pause(LuaValue[] values) {
        pausePlayVideo();
        return null;
    }

    @LuaApiUsed
    public LuaValue[] stop(LuaValue[] values) {
        stopPlayVideo();
        isStop = true;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setStartStallingCallback(LuaValue[] callback) {
        startStallingFun = callback.length > 0 ? callback[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setEndStallingCallback(LuaValue[] callback) {
        endStallingFun = callback.length > 0 ? callback[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setFinishCallback(LuaValue[] callback) {
        finishFun = callback.length > 0 ? callback[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setFailCallback(LuaValue[] callback) {
        failFun = callback.length > 0 ? callback[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setWillRepeatCallback(LuaValue[] callback) {
        willRepeatFun = callback.length > 0 ? callback[0].toLuaFunction() : null;
        return null;
    }

    @LuaApiUsed
    public LuaValue[] getPlayStatus(LuaValue[] values) {
        if (isStop) {
            return LuaValue.rNumber(PlayStatus.IDEL);
        }
        switch (playStatus) {
            case PlayStatus.READY:
            case PlayStatus.PREPARING:
                return LuaValue.rNumber(mIJKPlayer.isPlaying() ? PlayStatus.PLAYING : PlayStatus.PAUSE);
        }
        return LuaValue.rNumber(playStatus);
    }

    protected void initView() {
        getIJKPlayer().setCallback(this);
        getIJKPlayer().setSilentMode(isMute);
        if (uri != null)
            getIJKPlayer().setUri(uri);
    }

    private void pausePlayVideo() {
        if (getIJKPlayer() != null) {
            stopPoint = getIJKPlayer().getCurrentPosition();
            getIJKPlayer().pause();
        }
    }

    private void stopPlayVideo() {
        if (getIJKPlayer() != null) {
            stopPoint = getIJKPlayer().getCurrentPosition();
            getIJKPlayer().stopPlayback();
        }
    }

    private void startPlayVideo() {
        if (getIJKPlayer() != null) {
            if (totalDuration > 0 && stopPoint >= totalDuration)
                stopPoint = 0;
            if (stopPoint > 0) {
                getIJKPlayer().seekTo(stopPoint);
                stopPoint = 0;
            }
            getIJKPlayer().start();
        }
    }

    @Override
    public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {
        switch (playbackState) {
            case IMediaPlayer.STATE_ENDED:
                playStatus = PlayStatus.IDEL;
                if (finishFun != null) {
                    finishFun.invoke(LuaValue.rString(uri.toString()));
                }
                break;
            case IMediaPlayer.STATE_BUFFERING:
                playStatus = PlayStatus.PREPARING;
                if (startStallingFun != null) {
                    startStallingFun.invoke(LuaValue.rString(uri.toString()));
                }
                hasStalling = true;
                break;
            case IMediaPlayer.STATE_READY:
                if (hasStalling && endStallingFun != null) {
                    endStallingFun.invoke(LuaValue.rString(uri.toString()));
                    hasStalling = false;
                }
                playStatus = PlayStatus.READY;
                totalDuration = getIJKPlayer() != null ? getIJKPlayer().getDuration() : -1;
                break;
        }
    }

    @Override
    public void onError(int what, int extra) {
        playStatus = PlayStatus.ERROR;
        if (failFun != null) {
            failFun.invoke(LuaValue.rString(uri.toString()));
        }
    }

    @Override
    public void onLoopStart() {
        if (willRepeatFun != null) {
            willRepeatFun.invoke(LuaValue.varargsOf(LuaString.valueOf(uri.toString()), LuaNumber.valueOf(getIJKPlayer().getDuration() * 1.0 / 1000)));
        }
    }

    private LuaRadioPlayer getIJKPlayer() {
        return mIJKPlayer;
    }
}
