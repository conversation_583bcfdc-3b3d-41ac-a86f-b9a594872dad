package com.immomo.momo.luaview.media;

import com.immomo.framework.utils.UIUtils;
import com.immomo.mls.fun.ud.view.UDViewGroup;
import com.immomo.mls.util.DimenUtil;
import com.immomo.momo.feed.ui.view.CustomSwipeRefreshLayout;
import com.immomo.momo.feed.ui.view.RefreshAnimView;

import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

/**
 * 短视频下拉刷新
 * author: hongming.wei
 * data: 2023/2/2
 */

@LuaApiUsed
public class UDCustomSwipeRefreshLayout<V extends CustomSwipeRefreshLayout> extends UDViewGroup<V> {

    public static final String LUA_CLASS_NAME = "CustomSwipeRefreshLayout";

    private CustomSwipeRefreshLayout mRefreshLayout;
    private RefreshAnimView mRefreshAnimView;

    public static final String[] methods = {
            "setEnabled",
            "setOnRefreshListener",
            "setRefreshing",
            "setHandleUnIntercept",
            "setData"
    };


    @LuaApiUsed
    public UDCustomSwipeRefreshLayout(long L, LuaValue[] v) {
        super(L, v);
    }

    @Override
    protected V newView(LuaValue[] init) {
        mRefreshLayout = new CustomSwipeRefreshLayout(getContext());
        mRefreshAnimView = new RefreshAnimView(getContext());
        mRefreshLayout.setRefreshView(mRefreshAnimView);
        mRefreshLayout.setProgressViewOffset(false, 30, 50);
        mRefreshLayout.setProgressViewEndTarget(false, UIUtils.getPixels(64f));
        mRefreshAnimView.setData("");
        return (V) mRefreshLayout;
    }

    @LuaApiUsed
    public LuaValue[] setEnabled(LuaValue[] var) {
        if (var.length > 0 && mRefreshLayout != null) {
            mRefreshLayout.setEnabled(var[0].toBoolean());
        }
        return null;
    }
    @LuaApiUsed
    public LuaValue[] setHandleUnIntercept(LuaValue[] var) {
        if (var.length > 0 && mRefreshLayout != null) {
            mRefreshLayout.setHandleUnIntercept(var[0].toBoolean());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setOnRefreshListener(LuaValue[] var) {
        LuaFunction function = var[0].toLuaFunction();
        if (mRefreshLayout != null && function != null) {
            mRefreshLayout.setOnRefreshListener(() -> {
                mRefreshLayout.post(() -> {
                    mRefreshLayout.setRefreshing(true);
                    function.fastInvoke();
                });
            });
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setRefreshing(LuaValue[] var) {
        if (var.length > 0 && mRefreshLayout != null) {
            mRefreshLayout.setRefreshing(var[0].toBoolean());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setData(LuaValue[] str) {
        if (mRefreshAnimView != null) {
            mRefreshAnimView.setData(str.length > 0 && !str[0].isNil() ? str[0].toJavaString() : "");
        }
        return null;
    }
}
