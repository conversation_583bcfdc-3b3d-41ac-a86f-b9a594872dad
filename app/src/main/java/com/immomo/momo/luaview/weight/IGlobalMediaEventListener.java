package com.immomo.momo.luaview.weight;

/**
 * author: hongming.wei
 * data: 2023/2/13
 */
public interface IGlobalMediaEventListener {

    void onSingleTapConfirmed();

    void onDoubleTap();

    void onPlayerStateChanged(boolean playWhenReady, int playbackState);

    void onPlayPosition(long position, long duration);

    void setToIdleState();

    void setToStateEnded(int playCount);

    void setToBufferState();

    void setToPlayState();

    void onSurfaceUpdated();
}
