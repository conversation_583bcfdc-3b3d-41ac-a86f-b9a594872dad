package com.immomo.momo.luaview.ud;

import android.app.Activity;

import com.cosmos.mdlog.MDLog;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.momo.MomoKit;
import com.immomo.momo.knock.KnockBusinessManager;
import com.immomo.momo.knock.bean.KnockTopCardBean;
import com.immomo.momo.knock.config.KnockSettingConfigV2;
import com.immomo.momo.util.GsonUtils;
import com.immomo.momo.util.StringUtils;
import com.immomo.momo.util.TopCardUtils;

/**
 * author: hongming.wei
 * data: 2023/11/29
 */
@LuaClass(isStatic = true)
public class UDQiaoQiaoLuaManager {

    private static final String TAG = "UDQiaoQiaoLuaManager";

    public static final String LUA_CLASS_NAME = "QiaoQiaoLuaManager";

    @LuaBridge
    public static void showRecommendView(String packet) {
        if (StringUtils.isNotEmpty(packet)) {
            try {
                Activity topActivity = MomoKit.getTopActivity();
                if (topActivity == null) {
                    return;
                }
                KnockTopCardBean knockTopCardBean = GsonUtils.g().fromJson(packet, KnockTopCardBean.class);
                knockTopCardBean.getRecommend().setBadgeTagString(packet);
                if (KnockSettingConfigV2.Companion.isInNewKnockReflect()) {
                    KnockBusinessManager.INSTANCE.onFirstShowKnockCard(knockTopCardBean, false);
                } else {
                    TopCardUtils.firstShowTopCard(knockTopCardBean, false);
                }
            } catch (Exception e) {
                MDLog.printErrStackTrace(TAG, e);
            }
        }
    }

}
