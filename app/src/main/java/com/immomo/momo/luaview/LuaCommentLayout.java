package com.immomo.momo.luaview;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.MotionEvent;

import androidx.annotation.NonNull;

import com.immomo.framework.base.BaseActivity;
import com.immomo.mls.fun.weight.BorderRadiusFrameLayout;
import com.immomo.momo.R;
import com.immomo.momo.feed.BaseCommentHandler;
import com.immomo.momo.mvp.feed.view.CommentView;
import com.immomo.momo.sessionnotice.bean.NoticeMsg;

public class LuaCommentLayout extends BorderRadiusFrameLayout {

    private CommentView commentView;

    public LuaCommentLayout(@NonNull Context activity) {
        super(activity);
        init(activity);
        bindAc(activity);
    }

    public CommentView getCommentView() {
        return commentView;
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.lua_comment_layout, this);
        commentView = (CommentView) findViewById(R.id.comment_view);
    }

    private void bindAc(Context activity) {
        if (activity instanceof BaseActivity) {
            commentView.bindAc((BaseActivity) activity);
        }
    }

    public void setSendCommentCallBack(BaseCommentHandler.OnCommentListener<Object, Object> listener) {
        commentView.setOutsideCommentListener(listener);
    }

    public void setCommentKeyboardListener(CommentView.CommentKeyboardListener listener) {
        commentView.setCommentKeyboardListener(listener);
    }

    public void setSoftInputMode(){
        commentView.setSoftInputMode();
    }

    public void hideVideoCommentLayout(){
        commentView.hideVideoCommentLayout();
    }


    public void showPanel(NoticeMsg notice) {
        commentView.onCommentClicked(notice,true);
    }

    public void setCommentConfig(NoticeMsg notice) {
        commentView.setCommentConfig(notice);
    }

    public void showCommentLayoutOnly() {
        commentView.showCommentLayout();
    }

    public void setVideoMode() {
        commentView.setVideoMode();
    }

    public void setFeedType() {
        commentView.setFeedType();
    }

    public void clearInput() {
        commentView.clearInput();
    }

    public void hidePanel() {
        commentView.hideCommentLayout();
    }

    public void resume() {
        commentView.resume();
    }

    public void back() {
        commentView.back();
    }

    public boolean isPanelShowing() {
        return commentView.isPanelShowing();
    }

    public void setEmotionFooterAndSize(int size) {
         commentView.setEmotionFooterAndSize(size);
    }

    public void pause() {
        commentView.pause();
    }


    public void destory() {
        commentView.destory();
        commentView = null;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        return true;
    }
}
