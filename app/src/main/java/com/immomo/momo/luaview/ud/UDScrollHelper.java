package com.immomo.momo.luaview.ud;

import com.alibaba.fastjson.JSONObject;
import com.immomo.android.module.feedlist.presentation.feedUtils.ScrollPositionHelper;
import com.immomo.framework.base.BaseActivity;
import com.immomo.mls.LuaViewManager;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.LVCallback;
import com.immomo.momo.personalprofile.module.data.api.response.PersonalProfileModel;
import com.immomo.momo.personalprofile.module.data.api.response.source.ProfileThemeInfoModel;
import com.immomo.momo.personalprofile.module.data.api.response.source.ProfileThemesModel;
import com.immomo.momo.personalprofile.module.domain.model.ProfileUserModel;
import com.immomo.momo.personalprofile.utils.PersonalThemeHelper;
import com.immomo.momo.profiledependcy.apt.ProfileAppConfigV2Getter;
import com.immomo.momo.service.user.ProfileModelHelper;
import com.immomo.momo.util.MomoKit;
import com.immomo.momo.util.StringUtils;
import com.immomo.momo.util.ToastUtils;

import org.luaj.vm2.Globals;

import java.util.Map;


@LuaClass(isStatic = true)
public class UDScrollHelper {
    public static final String LUA_CLASS_NAME = "ScrollHelper";

    @LuaBridge
    public static void scrollViewDidScroll(double x,double y) {

    }

    @LuaBridge
    public static void tableViewCellWillAppear(int position) {
        ScrollPositionHelper.INSTANCE.changePosition(position);
    }

    @LuaBridge
    public static int getTopicType() {
        return -1;
    }

    @LuaBridge
    public static void scrollViewWillBeginDraging() {
    }

    @LuaBridge
    public static void scrollViewEndScroll() {
    }

    @LuaBridge
    public static void scrollViewDidEndDragging(double x,double y,boolean decelerate) {
    }

    @LuaBridge
    public static void setUserData(String momoid, Globals globals, LVCallback callback) {
        ProfileUserModel profileUser = ProfileModelHelper.getUserModel(momoid);
        BaseActivity context = (BaseActivity) ((LuaViewManager) globals.getJavaUserdata()).context;
        if (profileUser != null) {
            String profileStr;
            if (ProfileAppConfigV2Getter.get().profileLuaNewData() == 1) {
                profileStr = profileUser.getProfileJsonStr();
                if (StringUtils.isEmpty(profileStr)) {
                    PersonalProfileModel personalModel = profileUser.toUserModel();
                    profileStr = JSONObject.toJSONString(personalModel);
                }
            } else {
                PersonalProfileModel personalModel = profileUser.toUserModel();
                profileStr = JSONObject.toJSONString(personalModel);
            }
            String themePath = null;
            String themeJsonStr = null;
            ProfileThemeInfoModel themeInfoModel = profileUser.toThemeInfoModel();

            if (!PersonalThemeHelper.Companion.getProfileThemeEnable() && themeInfoModel != null) {
                String useTheme = themeInfoModel.getUseTheme();
                PersonalThemeHelper helper = PersonalThemeHelper.Companion.getInstance();
                if (StringUtils.isNotEmpty(useTheme) && helper != null){
                    for (ProfileThemesModel theme: themeInfoModel.getThemes()) {
                        if (theme != null && StringUtils.equalsNonNull(theme.getCategory(), useTheme)) {
                            themePath = helper.getThemeFilePath(useTheme, theme.getUniqueId());
                        }
                    }
                    if (StringUtils.isNotEmpty(themePath)) {
                        themeJsonStr = helper.getThemeFileJson(themePath);
                    } else if (MomoKit.INSTANCE.isDarkMode(context)) {
                        ProfileThemesModel theme = PersonalThemeHelper.Companion.getDarkModel();
                        themePath = helper.getThemeFilePath(theme.getCategory(), theme.getUniqueId());
                        if (StringUtils.isNotEmpty(themePath)) {
                            themeJsonStr = helper.getThemeFileJson(themePath);
                        }
                    }
                }
                if (StringUtils.isEmpty(useTheme) && helper != null && MomoKit.INSTANCE.isDarkMode(context)) {
                    ProfileThemesModel theme = themeInfoModel.getDarkTheme();
                    if (theme != null) {
                        themePath = helper.getThemeFilePath(theme.getCategory(), theme.getUniqueId());
                    }
                    if (StringUtils.isNotEmpty(themePath)) {
                        themeJsonStr = helper.getThemeFileJson(themePath);
                    } else {
                        theme = PersonalThemeHelper.Companion.getDarkModel();
                        themePath = helper.getThemeFilePath(theme.getCategory(), theme.getUniqueId());
                        if (StringUtils.isNotEmpty(themePath)) {
                            themeJsonStr = helper.getThemeFileJson(themePath);
                        }
                    }
                }
            }
            callback.call(profileStr, themeJsonStr, "file://" + themePath);
        }
    }

    @LuaBridge
    public static void getUserDataCount() {
    }
}
