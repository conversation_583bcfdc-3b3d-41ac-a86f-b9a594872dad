package com.immomo.momo.luaview;

import com.immomo.momo.luaview.apt.LuaViewConfigV2Getter;
import com.immomo.momo.luaview.statistics.ILuaWhiteScreenRouter;

import info.xudshen.android.appasm.RouterProvider;

@RouterProvider
public class Lua<PERSON>hiteScreenRouter implements ILuaWhiteScreenRouter {
    @Override
    public boolean isEnable() {
        return LuaViewConfigV2Getter.get().checkWhiteScreenEnable() == 1;
    }

    @Override
    public int getCheckInterval() {
        return LuaViewConfigV2Getter.get().checkWhiteScreenInterval();
    }

    @Override
    public int getDetectTimes() {
        return LuaViewConfigV2Getter.get().checkWhiteScreenLimit();
    }
}
