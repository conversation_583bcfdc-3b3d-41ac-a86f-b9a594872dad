package com.immomo.momo.luaview.ud;


import android.graphics.Color;
import android.graphics.Typeface;

import com.immomo.mls.MLSAdapterContainer;
import com.immomo.mls.adapter.TypeFaceAdapter;
import com.immomo.mls.fun.ud.UDArray;
import com.immomo.mls.fun.ud.UDColor;
import com.immomo.mls.fun.ud.view.UDViewGroup;
import com.immomo.momo.luaview.LuaShimmeringLabel;

import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import java.util.ArrayList;
import java.util.List;

/**
 * 扫光控件桥接
 * Created by li.mengnan on 2021/2/16.
 */
@LuaApiUsed
public class UDShimmeringLabel<V extends LuaShimmeringLabel> extends UDViewGroup<V> {

    public static final String LUA_CLASS_NAME = "ShimmeringLabel";
    public static final String[] methods = {
            "text",
            "textColor",
            "fontSize",
            "setTextBold",
            "fontNameSize",
            "setLightColor",
            "setLightColors",
            "setRepeatCount",
            "setInterval",
            "setAngle",
            "setDuration",
            "setSpeed",
            "setPercent",
            "setSpace",
            "showAnimation",
            //空实现，适配ios
            "setViewWidth",
            "setViewHeight",
            "updateSize",
            "refreshView"
    };
    private LuaShimmeringLabel shimmeringLabel;

    @LuaApiUsed
    public UDShimmeringLabel(long l, LuaValue[] v) {
        super(l, v);
    }

    @Override
    protected V newView(LuaValue[] init) {
        shimmeringLabel = new LuaShimmeringLabel(getContext());
        return (V) shimmeringLabel;
    }

    @LuaApiUsed
    public LuaValue[] text(LuaValue[] var) {
        String text = null;
        if (var.length == 1) {
            text = var[0].toJavaString();
            if (var[0].isNil())
                text = "";
        }
        if (text != null) {
            shimmeringLabel.setText(text);
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setTextBold(LuaValue[] var) {
        shimmeringLabel.setFontStyle(true);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] textColor(LuaValue[] var) {
        if (var.length == 1 && var[0] instanceof UDColor) {
            UDColor color = (UDColor) var[0];
            shimmeringLabel.setTextColor(color.getColor());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] fontSize(LuaValue[] var) {
        if (var.length == 1) {
            shimmeringLabel.setTextSize((float) var[0].toDouble());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] fontNameSize(LuaValue[] var) {
        String name = var[0].toJavaString();
        float size = (float) var[1].toDouble();
        TypeFaceAdapter a = MLSAdapterContainer.getTypeFaceAdapter();
        if (a != null) {
            Typeface typeface = a.create(name);
            if (typeface != null) {
                shimmeringLabel.setFont(typeface);
            }
        }
        shimmeringLabel.setTextSize(size);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setLightColor(LuaValue[] var) {
        if (var.length == 1 && var[0] instanceof UDColor) {
            UDColor color = (UDColor) var[0];
            shimmeringLabel.setShimmerColors(color.getColor());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setLightColors(LuaValue[] var) {
        if (var.length == 1 && var[0] instanceof UDArray) {
            UDArray colors = (UDArray) var[0];
            List array = colors.getArray();
            ArrayList<Integer> intArray = new ArrayList();
            for (int i = 0; i < array.size(); i++) {
                Object color = array.get(i);
                if (color instanceof String) {
                    try {
                        if (((String) color).contains("#")) {
                            intArray.add(Color.parseColor((String) color));
                        } else {
                            intArray.add(Color.parseColor("#" + color));
                        }
                    } catch (Exception e) {
                        //暂不处理
                    }
                }
            }
            int[] result = new int[intArray.size()];
            for (int i = 0; i < intArray.size(); i++) {
                result[i] = intArray.get(i);
            }
            shimmeringLabel.setShimmerColors(result);
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setRepeatCount(LuaValue[] var) {
        if (var.length == 1) {
            shimmeringLabel.setShimmeringRepeat(var[0].toInt());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setInterval(LuaValue[] var) {
        if (var.length == 1) {
            shimmeringLabel.setShimmeringInterval(var[0].toFloat());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setAngle(LuaValue[] var) {
        if (var.length == 1) {
            shimmeringLabel.setShimmeringAngle(var[0].toFloat());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setDuration(LuaValue[] var) {
        if (var.length == 1) {
            shimmeringLabel.setShimmeringDuration(var[0].toFloat());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setSpeed(LuaValue[] var) {
        if (var.length == 1) {
            shimmeringLabel.setShimmeringSpeed(var[0].toFloat());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setPercent(LuaValue[] var) {
        if (var.length == 1) {
            shimmeringLabel.setShimmeringPercent(var[0].toFloat());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setSpace(LuaValue[] var) {
        if (var.length == 1) {
            shimmeringLabel.setShimmeringSpace(var[0].toFloat());
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] showAnimation(LuaValue[] var) {
        if (var.length == 1) {
            if (var[0].toBoolean()) {
                shimmeringLabel.startShimmering();
            } else {
                shimmeringLabel.stopShimmering();
            }
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setViewWidth(LuaValue[] values) {
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setViewHeight(LuaValue[] values) {
        return null;
    }

    @LuaApiUsed
    public LuaValue[] updateSize(LuaValue[] values) {
        return null;
    }

    @LuaApiUsed
    public LuaValue[] refreshView(LuaValue[] values) {
        if (shimmeringLabel != null) {
            shimmeringLabel.refreshView();
        }
        return null;
    }

}
