package com.immomo.momo.luaview;

import android.content.Context;
import android.graphics.SurfaceTexture;
import android.net.Uri;
import android.os.Handler;
import android.os.Message;
import android.view.MotionEvent;

import com.immomo.framework.base.BaseActivity;
import com.immomo.momo.agora.mr.conflictHelper.VideoConflictNewHelper;
import com.immomo.momo.businessmodel.statistics.PageSourceMatcher;
import com.immomo.momo.feed.player.IMediaPlayer;
import com.immomo.momo.luaview.weight.BorderRadiusMediaView;
import com.immomo.momo.util.StringUtils;

import java.lang.ref.WeakReference;

/**
 * Created by <PERSON>.ke on 2018/11/28.
 */

public class LuaMediaViewNew extends BorderRadiusMediaView {

    private Uri mUri;
    private Callback callback;
    private ProgressUpdateCallback progressCallback;
    private boolean started = false;
    private boolean mute = false;
    private boolean firstUpdate = true;
    private boolean isViewExist;
    private boolean useProxyOrCache = true;
    private LuaIJKPlayer mPlayer;
    private int repeatCount = 1;//循环播放次数 ,默认1次
    private int repeatPos = 1;//当前播放次数
    private LuaIJKPlayer.CompletionListener completionListener;
    private String feedid;

    public LuaMediaViewNew(Context context) {
        super(context);
        mPlayer = new LuaIJKPlayer();
        mPlayer.setLoopPlay(false);
        mPlayer.setLoopListener(new LuaIJKPlayer.LoopListener() {
            @Override
            public void onLoopStart() {
                if (callback != null) {
                    callback.onLoopStart();
                }
            }
        });
        mPlayer.setCompletionListener(new LuaIJKPlayer.CompletionListener() {
            @Override
            public void onCompletion() {
                firstUpdate = true;
                if (repeatCount > 1 && mPlayer.isLoopPlay()) {//有限loop计数
                    if (++repeatPos > repeatCount) {
                        resetRepeatPos();//重置次数
                        setLoopPlay(false);
                    } else {
                        setLoopPlay(true);
                    }
                }
                if (!mPlayer.isLoopPlay() && completionListener != null) {//统一，轮播就不调，轮播结束才调
                    completionListener.onCompletion();
                }
            }
        });
    }

    private Handler handler = new ProressHander(this);

    private static class ProressHander extends Handler {

        private WeakReference<LuaMediaViewNew> reference;

        ProressHander(LuaMediaViewNew luaMediaView) {
            this.reference = new WeakReference<>(luaMediaView);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == 1) {
                LuaMediaViewNew luaMediaView = reference.get();
                if (luaMediaView != null && luaMediaView.needPostProgress()) {
                    if (luaMediaView.getDuration() > 0 && luaMediaView.isPlaying() && luaMediaView.mPlayer.getPlaybackState() == IMediaPlayer.STATE_READY) {
                        luaMediaView.progressCallback.onProgressUpdate(luaMediaView.mPlayer.getCurrentPosition());
                    }

                    luaMediaView.checkAndPostProgress();
                }
            }
        }
    }

    private void checkAndPostProgress() {
        if (!isViewExist) {
            return;
        }
        if (needPostProgress()) {
            handler.sendEmptyMessageDelayed(1, 100);
        }
    }


    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    public void setFeedid(String feedid){
        this.feedid = feedid;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        boolean result = super.onTouchEvent(event);
        return result;
    }

    public void directAccess(boolean directAccess) {
        this.useProxyOrCache = !directAccess;//取反，useProxyOrCache的false为直播流
    }

    public void start() {
        start(useProxyOrCache);
    }

    public void start(boolean useProxyOrCache) {
        if (mUri == null)
            return;
        VideoConflictNewHelper.stopMusic();
        VideoConflictNewHelper.stopOutMusic();
        if (getContext() instanceof BaseActivity) {
            BaseActivity activity = ((BaseActivity) getContext());
            if (!activity.isForeground()) return;
        }
        mPlayer.setUseProxyOrCache(useProxyOrCache);
        mPlayer.setErrorListener(this);
        if (!mUri.equals(mPlayer.getCurrentUri())) {
            mPlayer.updatePlayPosition();
            mPlayer.prepareAndSeek(mUri, false, feedid, true, PageSourceMatcher.getMicroVideoLogSource(), "");
            acquireVideoTexture(getContext(), mPlayer);
        }
        mPlayer.setSilentMode(mute);
        started = true;
        resetRepeatPos();//重置重复播放次数
        mPlayer.resume();
    }

    public void setUri(Uri uri) {
        mUri = uri;
        mPlayer.prepare(mUri);//两端统一src后，会重新prepare。调play重新播放
//        IJKMediaPreLoader.getInstance().preloadVideo(uri);
    }

    public void setUri(Uri uri, boolean useMediaCodec) {
        mUri = uri;
        mPlayer.prepareAndSeek(mUri, useMediaCodec, feedid, true, PageSourceMatcher.getMicroVideoLogSource(), "");//两端统一src后，会重新prepare。调play重新播放
        acquireVideoTexture(getContext(), mPlayer);
//        IJKMediaPreLoader.getInstance().preloadVideo(uri);
    }

    public void checkUrl(String url) {
        mPlayer.setUseProxyOrCache(StringUtils.isStartWithHttpOrHttps(url));
    }

    public void pause() {
        mPlayer.pause();
    }

    public void stopPlayback() {
        mPlayer.release();
        firstUpdate = true;
    }

    public void resume() {
        mPlayer.resume();
    }

    public void setRepeatCount(int repeatCount) {
        this.repeatCount = repeatCount;
    }

    private void resetRepeatPos() {
        repeatPos = 1;
    }

    public void setSilentMode(boolean silent) {
        mute = silent;
        mPlayer.setSilentMode(silent);
    }

    public void setLoopPlay(boolean loop) {
        mPlayer.setLoopPlay(loop);
    }

    @Override
    public void onSurfaceTextureUpdated(SurfaceTexture surface) {
        if (!firstUpdate)
            return;
        firstUpdate = false;
        if (callback != null)
            callback.onStartRendering();
    }

    @Override
    public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {
        if (callback != null) {
            callback.onPlayerStateChanged(playWhenReady, playbackState);
        }
    }

    @Override
    public void onVideoSizeChanged(int width, int height, int unappliedRotationDegrees, float pixelWidthHeightRatio) {
        super.onVideoSizeChanged(width, height, unappliedRotationDegrees, pixelWidthHeightRatio);
        if (callback != null) {
            callback.onVideoSizeChanged(width, height, unappliedRotationDegrees, pixelWidthHeightRatio);
        }
    }

    @Override
    public void onPlayerError(int what, int extra) {
        if (callback != null) {
            callback.onError(what, extra);
        }
    }

    public boolean canSeek() {
        if (mUri == null || !started)
            return false;
        int state = mPlayer.getPlaybackState();
        return state == IMediaPlayer.STATE_READY
                || state == IMediaPlayer.STATE_ENDED;
    }

    public long getDuration() {
        if (mUri == null || !started)
            return 0;
        return mPlayer.getDuration();
    }

    public long getCurrentPosition() {
        if (mUri == null || !started)
            return 0;
        return mPlayer.getCurrentPosition();
    }

    public void seekTo(long t) {
        if (mUri == null || !started)
            return;
        mPlayer.seekTo(t);
    }

    public boolean isPlaying() {
        if (mUri == null || !started)
            return false;
        return mPlayer.getPlayWhenReady();
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        isViewExist = true;
        handler.removeMessages(1);

        if (needPostProgress()) {
            handler.sendEmptyMessageDelayed(1, 100);
        }
    }

//    @Override
//    protected void onWindowVisibilityChanged(int visibility) {
//        super.onWindowVisibilityChanged(visibility);
//        if (getDuration() > 0 && mPlayer.getPlaybackState() == IMediaPlayer.STATE_READY) {
//            if (visibility == VISIBLE) {
//                resume();
//            } else if (visibility == INVISIBLE || visibility == GONE) {
//                pause();
//            }
//        }
//    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        handler.removeMessages(1);
        isViewExist = false;
    }

    @Override
    public void setToInitialState() {
        if (callback != null) {
            callback.onSetToInitialState();
        }
    }

    public void setProgressCallback(ProgressUpdateCallback progressCallback) {
        this.progressCallback = progressCallback;
        handler.removeMessages(1);
        handler.sendEmptyMessageDelayed(1, 100);
    }

    private boolean needPostProgress() {
        return progressCallback != null;
    }


    public void setCompletionListener(LuaIJKPlayer.CompletionListener completionListener) {
        this.completionListener = completionListener;
    }

    public interface ProgressUpdateCallback {
        void onProgressUpdate(long progress);
    }

    public interface Callback {
        void onPlayerStateChanged(boolean playWhenReady, int playbackState);

        void onError(int what, int extra);

        void onStartRendering();

        void onVideoSizeChanged(int width, int height, int unappliedRotationDegrees, float pixelWidthHeightRatio);

        void onLoopStart();

        void onSetToInitialState();
    }
}
