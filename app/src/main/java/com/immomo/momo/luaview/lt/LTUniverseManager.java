package com.immomo.momo.luaview.lt;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.cosmos.mdlog.MDLog;
import com.immomo.android.router.share.model.ShareData;
import com.immomo.framework.storage.kv.KV;
import com.immomo.kotlin.extern.StringKt;
import com.immomo.mls.MLSEngine;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.util.JsonUtil;
import com.immomo.mls.utils.LVCallback;
import com.immomo.momo.LogTag;
import com.immomo.momo.common.activity.CommonShareActivity;
import com.immomo.momo.globalevent.GlobalEventManager;
import com.immomo.momo.homepage.HomeUniverseGuideUtils;
import com.immomo.momo.share2.ShareConstant;
import com.immomo.momo.universe.UniverseModule;
import com.immomo.momo.universe.apt.UniAppConfigV2Getter;
import com.immomo.momo.universe.im.UniUnreadManager;
import com.immomo.momo.universe.im.service.UniSessionService;
import com.immomo.momo.universe.phonograph.PhonographManager;
import com.immomo.momo.universe.phonograph.PhonographUtils;
import com.immomo.momo.universe.phonograph.audio.AudioPlayListener;
import com.immomo.momo.universe.phonograph.domain.model.AudioModel;
import com.immomo.momo.universe.profile.ProfileBgUploadActivity;
import com.immomo.momo.universe.user.model.UniIntimacyModel;
import com.immomo.momo.universe.user.model.UniUserModel;
import com.immomo.momo.universe.util.AudioUtil;
import com.immomo.momo.universe.util.SessionUtils;
import com.immomo.momo.universe.util.UniverseCons;
import com.immomo.momo.universe.util.UniverseUtils;
import com.immomo.momo.universe.util.VibratorUtil;
import com.immomo.momo.util.DateUtil;

import org.jetbrains.annotations.Nullable;
import org.json.JSONException;
import org.json.JSONObject;
import org.luaj.vm2.Globals;
import org.luaj.vm2.LuaUserdata;
import org.luaj.vm2.LuaValue;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import kotlin.Unit;


/**
 * 小宇宙Lua桥接帮助类，桥接类不可以使用kotiln编写，不然程序运行会有问题
 * Created by li.mengnan on 2021/2/4.
 */
@LuaClass(isStatic = true)
public class LTUniverseManager {
    //lua 桥接名称
    public static final String LUA_CLASS_NAME = "UniverseNoticeManager";

    private static Context getContext() {
        return MLSEngine.getContext();
    }

    @LuaBridge
    public static boolean isInUniFmTest() {
        return false;
    }

    @LuaBridge
    public static void isInUniFmConflict(LVCallback callback) {
        callback.call(false);
    }

    /**
     * 进入小宇宙主feed业务时回调
     */
    @LuaBridge
    public static void onFeedCreate() {
        UniverseModule.INSTANCE.onCreate();
    }

    /**
     * lua主动获取未读数
     */
    @LuaBridge
    public static void getNotificationCount(LVCallback callback) {
        if (callback != null) {
            int totalUnreadCount = UniUnreadManager.INSTANCE.getTotalUnreadCount();
            callback.call(totalUnreadCount);
        }
    }

    /**
     * 退出小宇宙主feed业务时回调
     */
    @LuaBridge
    public static void onFeedDestroy() {
        UniverseModule.INSTANCE.onDestroy();
    }

    /**
     * 更新当前用户数据
     */
    @LuaBridge
    public static void updateUserInfo(Map userInfo) {
        if (userInfo != null) {
            try {
                UniUserModel user = new UniUserModel();
                if (userInfo.containsKey("isHost")) {
                    user.setHost((Boolean) userInfo.get("isHost"));
                }
                if (userInfo.containsKey("userId")) {
                    user.setUid((String) userInfo.get("userId"));
                }
                if (userInfo.containsKey("nickName")) {
                    user.setNickName((String) userInfo.get("nickName"));
                }
                if (userInfo.containsKey("userAvatar")) {
                    user.setAvatar((String) userInfo.get("userAvatar"));
                }
                if (userInfo.containsKey("sex")) {
                    user.setSex((String) userInfo.get("sex"));
                }
                if (userInfo.containsKey("isRegistered")) {
                    user.setRegistered((Boolean) userInfo.get("isRegistered"));
                }
                if (userInfo.containsKey("remark")) {
                    user.setMarkName((String) userInfo.get("remark"));
                }
                if (userInfo.containsKey("intimacyInfo")) {
                    Map intimacy = (Map) userInfo.get("intimacyInfo");
                    user.setIntimacyModel(
                            new UniIntimacyModel(
                                    StringKt.toInt(String.valueOf(intimacy.get("level")), 0, 10),
                                    String.valueOf(intimacy.get("icon")),
                                    String.valueOf(intimacy.get("isAnim")).equals("1"),
                                    String.valueOf(intimacy.get("status"))
                            )
                    );
                }
                UniverseModule.INSTANCE.onUserSync(user, null);
            } catch (Exception e) {
                MDLog.printErrStackTrace(LogTag.COMMON, e);
            }
        }
    }

    /**
     * 获取当前用户数据
     */
    @LuaBridge
    public static Map getUniverseUserInfo() {
        final Map<String, Object> map = new HashMap<>();
        UniUserModel user = UniverseModule.INSTANCE.getCurrentUser();
        if (user != null) {
            map.put("isHost", user.isHost());
            String uid = user.getUid();
            if (!TextUtils.isEmpty(uid)) {
                map.put("userId", uid);
            }
            String nickName = user.getNickName();
            if (!TextUtils.isEmpty(nickName)) {
                map.put("nickName", nickName);
            }
            String userAvatar = user.getAvatar();
            if (!TextUtils.isEmpty(userAvatar)) {
                map.put("userAvatar", userAvatar);
            }
            map.put("sex", user.getSex());
            boolean isRegister = UniverseUtils.INSTANCE.isUniverseUser();
            if (user.isRegistered() == isRegister) {
                map.put("isRegistered", user.isRegistered());
            } else {
                map.put("isRegistered", isRegister);
            }
            map.put("remark", user.getMarkName());
            UniIntimacyModel intimacyModel = user.getIntimacyModel();
            if (intimacyModel != null) {
                map.put("intimacyInfo", new HashMap<String, String>() {{
                    put("level", String.valueOf(intimacyModel.getLevel()));
                    put("icon", intimacyModel.getIcon());
                    put("status", intimacyModel.getStatus());
                    put("isAnim", String.valueOf(intimacyModel.isAnim() ? 1 : 0));
                }});
            }
        }
        return map;
    }

    /**
     * 获取当前正在播放的音频数据
     */
    @LuaBridge
    public static Map getCurrentAudioInfo() {
        AudioModel audio = PhonographManager.Companion.getManger().getCurrentAudio();
        return PhonographUtils.audioDataToMap(audio);
    }

    /**
     * 展示留声机窗口
     */
    @LuaBridge
    public static void playAudio(Map audioData, LVCallback callback) {

        PhonographManager.Companion.getManger().playAudio(audioData, new AudioPlayListener() {
            @Override
            public void onAudioPlayStart(@Nullable AudioModel audio) {
                if (callback != null && audio != null) {
                    callback.call(audio.getGuid(), true);
                }
            }

            @Override
            public void onAudioPlayFinish(@Nullable AudioModel audio, boolean isAudioQueueFinished) {
                if (callback != null && audio != null) {
                    callback.call(audio.getGuid(), false);
                }
            }

            @Override
            public void onAudioDownloadStart(@Nullable AudioModel audio) {
                if (callback != null && audio != null) {
                    callback.call(audio.getGuid(), true);
                }
            }

            @Override
            public void onAudioDownloadSucc(@Nullable AudioModel audio) {
                //无需实现
            }

            @Override
            public void onAudioDownloadFailed(@Nullable AudioModel audio) {
                if (callback != null && audio != null) {
                    callback.call(audio.getGuid(), false);
                }
            }

            @Override
            public void onAudioPlayPause(@Nullable AudioModel audio) {
                if (callback != null && audio != null) {
                    callback.call(audio.getGuid(), false);
                }
            }

            @Override
            public void onAudioPlayResume(@Nullable AudioModel audio) {
                if (callback != null && audio != null) {
                    callback.call(audio.getGuid(), true);
                }
            }

            @Override
            public void onPlaying(int progress, long positon) {
                //无需实现
            }
        });
    }

    /**
     * 切换播放状态
     */
    @LuaBridge
    public static void switchPlayStatus(boolean isToPlay) {
        PhonographManager.Companion.getManger().switchPlayStatus(isToPlay);
    }

    /**
     * 移除留声机窗口
     */
    @LuaBridge
    public static void quitPlay() {
        PhonographManager.Companion.getManger(false).quitAudio();
    }

    /**
     * 震动
     *
     * @param type 震动强度 0，1，2震感渐强
     */
    @LuaBridge
    public static void doShake(int type) {
        VibratorUtil.INSTANCE.doShake(type);
    }

    /**
     * 更新用户剩余流星数量
     *
     * @param remainStar
     */
    @LuaBridge
    public static void updateRemainStar(int remainStar) {
        UniverseModule.INSTANCE.updateRemainStar(remainStar);
    }

    /**
     * 获取用户剩余流星数量
     *
     * @return
     */
    @LuaBridge
    public static int getRemainStar() {
        return UniverseModule.INSTANCE.getRemainStar();
    }

    /**
     * 清理会话消息数据
     *
     * @return
     */
    @LuaBridge
    public static void clearSessionMsg(String remoteId) {
        if (!TextUtils.isEmpty(remoteId)) {
            SessionUtils.INSTANCE.clearUserMsg(remoteId);
        }
    }

    /**
     * 展示图片选择页
     *
     * @return
     */
    @LuaBridge
    public static void showImagePicker() {
        Intent intent = new Intent(getContext(), ProfileBgUploadActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        getContext().startActivity(intent);
    }

    /**
     * 连麦心跳控制
     * 已废弃
     * @param status    true：开始；false：结束
     * @param channelId 频道Id
     * @return
     */
    @LuaBridge
    public static void voiceChatHeartBeat(boolean status, String channelId) {
        return;
    }

    /**
     * 展示连麦浮窗
     * 已废弃
     * @param values
     */
    @LuaBridge
    public static void showVoiceChatFloatView(LuaValue[] values) {
        return;
    }

    /**
     * 已废弃
     * 隐藏连麦浮窗
     */
    @LuaBridge
    public static void hideVoiceChatFloatView() {
        return;
    }

    /**
     * 已废弃
     * 连麦浮窗是否正在展示
     */
    @LuaBridge
    public static boolean isVoiceChatFloatViewShowing() {
        //小宇宙连麦已下线，故返回false
        return false;
    }

    /**
     * 已废弃
     * 请求结束连麦
     */
    @LuaBridge
    public static void stopVoiceChat(String remoteUserId, String remoteMomoId) {
        return;
    }

    /**
     * 已废弃
     * 注册耳机使用状态监听
     */
    @LuaBridge
    public static void registerHeadsetReceiver(Globals globals) {
        return;
    }

    /**
     * 已废弃
     * 解注册注册耳机使用状态监听
     */
    @LuaBridge
    public static void unregisterHeadsetReceiver() {
        return;
    }

    /**
     * 是否正在使用耳机
     *
     * @return
     */
    @LuaBridge
    public static boolean isHeadsetInUse() {
        return AudioUtil.INSTANCE.hasWiredHeadset() || AudioUtil.INSTANCE.hasBluetoothHeadset();
    }

    /**
     * 切换免提状态
     *
     * @param isOn
     */
    @LuaBridge
    public static void switchSpeakerphoneState(boolean isOn) {
        isOn = !AudioUtil.INSTANCE.hasWiredHeadset() && !AudioUtil.INSTANCE.hasBluetoothHeadset() && isOn;
        AudioUtil.INSTANCE.switchSpeakerphoneState(isOn);
    }

    /**
     * 已废弃
     * 开启连麦校验
     *
     * @param source
     * @param callback
     */
    @LuaBridge
    public static void startMatchWithSource(int source, LVCallback callback) {
        return;
    }

    //倒计时提示音
//    已废弃
    @LuaBridge
    public static void playCountDownSound() {
        return;
    }

    //挂断提示音
    //    已废弃
    @LuaBridge
    public static void playHangUpSound() {
        return;
    }

    //判断业务是否冲突
    @LuaBridge
    public static boolean isBusinessConflict() {
        return AudioUtil.INSTANCE.isConflict(false);
    }

    //跳转至连麦页
    //    已废弃
    @LuaBridge
    public static void gotoVoiceChat(String gotoStr) {
        return;
    }
    //    已废弃
    @LuaBridge
    public static void isInLoadingPage(boolean isInLoadingPage) {
        return;
    }
    //    已废弃
    //用于原生统一管理lua中的rtc实例
    @LuaBridge
    public static void updateRtcInstance(LuaValue[] values) {
        return;
    }

    //小宇宙注册成功，更新用户状态，解决线上因小宇宙入口增加，在主页侧滑、下拉入口不存在时，用户注册，状态更新不及时问题
    @LuaBridge
    public static void registerDone() {
        KV.saveUserValue(UniverseCons.KEY_UNIVERSE_STAR_USER, 1);
    }

    @LuaBridge//获取置顶
    public static void getSessionUpStatus(String uid, LVCallback callback) {
        UniSessionService.INSTANCE.isTopping(uid, aBoolean -> {
            callback.call(aBoolean);
            return Unit.INSTANCE;
        });
    }

    @LuaBridge//置顶
    public static void setSessionUpStatus(boolean topping, String uid) {
        UniSessionService.INSTANCE.markTopping(uid, topping);
    }

    @LuaBridge//每日首次展示小宇宙帧lua 需要展示header
    public static boolean showFeedHeader() {
        long curTime = System.currentTimeMillis();
        long cacheTime = KV.getUserLong(UniverseCons.KEY_HOME_UNIVERSE_TAB_SHOW, 0l);
        String curTimeStr = DateUtil.formateDateTime11(new Date(curTime));
        String cacheTimeStr = DateUtil.formateDateTime11(new Date(cacheTime));
        if (curTimeStr.equals(cacheTimeStr)) {
            return false;
        } else {
            return true;
        }
    }
    //    已废弃
    @LuaBridge
    public static boolean requestPermissionAudio() {
        //小宇宙连麦已下线，故返回false
        return false;
    }

    @LuaBridge
    public static boolean isInHomePageExperiment() {
        return HomeUniverseGuideUtils.INSTANCE.universeTabCheck();
    }

    /**
     * 电波分享
     *
     * @param params
     */
    @LuaBridge
    public static void shareToMomoContact(String url, Map<String, String> params) {
        ShareData shareData = new ShareData();
        shareData.extra = JsonUtil.toJson(params).toString();
        Intent intent = new Intent(getContext(), CommonShareActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.putExtra(CommonShareActivity.KEY_FROM_TYPE, CommonShareActivity.FROM_TYPE_UNIVERSE_ATTRACTION);
        intent.putExtra(ShareConstant.KEY_SHARE_DATA, shareData);
        intent.putExtra(CommonShareActivity.KEY_TITLE_STR, "分享到");
        intent.putExtra(CommonShareActivity.KEY_DIALOG_MSG, "确认发送消息给:%s?");
        getContext().startActivity(intent);
    }

    @LuaBridge
    public static boolean isUniverseWhiteSimpleTest() {
        return true;
    }

    @LuaBridge
    public static boolean isUniverseRemarkLuaTest() {
        return UniAppConfigV2Getter.get().universeRemarkSwitch() == 1;
    }
}
