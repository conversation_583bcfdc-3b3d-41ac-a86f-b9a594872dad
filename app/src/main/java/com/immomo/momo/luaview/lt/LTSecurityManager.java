package com.immomo.momo.luaview.lt;

import android.content.ContentValues;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import com.cosmos.mdlog.MDLog;
import com.immomo.android.login.base.bean.UserLike;
import com.immomo.android.login.router.LoginRegisterRouter;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.framework.task.BaseDialogTask;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.IMConfigs;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.account.api.AccountApi;
import com.immomo.momo.account.view.WebViewDialog;
import com.immomo.momo.common.AppKit;
import com.immomo.momo.gotologic.GotoDispatcher;
import com.immomo.momo.homepage.view.DataProtectConfirmDialog;
import com.immomo.momo.protocol.http.UserApi;
import com.immomo.momo.protocol.imjson.util.Debugger;
import com.immomo.momo.service.bean.Preference;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.service.user.UserService;
import com.immomo.momo.setting.BasicUserInfoUtil;
import com.immomo.momo.setting.bean.SecurityInfo;
import com.immomo.momo.statistics.dmlogger.APILoggerKeys;
import com.immomo.momo.test.dbcheck.JDBTimeoutHandler;
import com.immomo.momo.user.UserInfoRequest;

import java.util.HashMap;
import java.util.Map;

import info.xudshen.android.appasm.AppAsm;

/**
 * -----------------------------------------------------------------
 * Copyright (C)  sun
 * Create: 2021/12/28 6:28 PM
 * -----------------------------------------------------------------
 */
@LuaClass(isStatic = true)
public class LTSecurityManager {
    public static final String LUA_CLASS_NAME = "SecurityManager";

    @LuaBridge
    public static boolean getAliBindState() {
        return KV.getUserBool(BasicUserInfoUtil.KEY_BIND_ALIPAY, false);
    }

    @LuaBridge
    public static void setAliBindState(Map map) {
        if (map == null) {
            return;
        }
        boolean bind = (int) map.get("is_blinding") == 1;
        KV.saveUserValue(BasicUserInfoUtil.KEY_BIND_ALIPAY, bind);
        KV.saveUserValue(BasicUserInfoUtil.KEY_ALIPAY_ACCOUNT, (String) map.get("name"));
    }

    @LuaBridge
    public static void saveSecurityInfo(Map infoMap) {
        if (infoMap == null) {
            return;
        }
        Map settingMap = (Map) infoMap.get("setting");

        long id = JDBTimeoutHandler.checkMe();
        String fileName = Preference.getSecurityPreferenceName();

        if (!TextUtils.isEmpty(fileName)) {
            ContentValues values = new ContentValues();
            values.put("user_safe_level", (int) infoMap.get("safe_level"));
            values.put("user_accredit_device", (int) settingMap.get("accredit_device"));
            values.put("user_password_if_strong", (int) settingMap.get("password"));
            values.put("user_bind_phone", (String) settingMap.get("bind_phone"));
            values.put("user_bind_certificate", (int) settingMap.get("bind_papers"));
            values.put("user_bind_certificateAction", (String) settingMap.get("papers_goto"));
            KV.saveSpecificValues(fileName, values);
        }

        KV.saveUserValue(SPKeys.User.Setting.KEY_ALI_AUTH_URL, (String) infoMap.get("alipay_goto"));
        KV.saveUserValue(SPKeys.User.Setting.KEY_BIND_PHONE_GOTO, (String) infoMap.get("bind_phone_goto"));
        JDBTimeoutHandler.unCheckMe(id);
    }

    @LuaBridge
    public static Map getSecurityInfo() {
        SecurityInfo securityInfo = UserService.getInstance().getSecurityInfo();
        Map<String, Object> infoMap = new HashMap<>();
        infoMap.put("safe_level", securityInfo.getSageLevel());
        infoMap.put("alipay_goto", securityInfo.getAliAction());
        infoMap.put("bind_phone_goto", securityInfo.getBindPhoneGoto());
        Map<String, Object> settingMap = new HashMap<>();
        settingMap.put("accredit_device", securityInfo.getAccreditDeviceValue());
        settingMap.put("password", securityInfo.getPasswordStrongValue());
        settingMap.put("bind_phone", securityInfo.getBindPhoneValue());
        settingMap.put("bind_papers", securityInfo.getBindCertificateValue());
        settingMap.put("papers_goto", securityInfo.getBindCertificateAction());
        infoMap.put("setting", settingMap);
        return infoMap;
    }

    @LuaBridge
    public static void setDebuggerState(boolean isInDebuger) {
        KV.saveUserValue(Debugger.KEY_SP_IS_IN_DEBUGER_LIST, isInDebuger);
        Debugger.setIsSpecial(isInDebuger);
        IMConfigs.setLogOpen(isInDebuger);
    }

    @LuaBridge
    public static void getProfileAndGotoChangePassword() {
        MomoTaskExecutor.executeUserTask(LUA_CLASS_NAME, new GetProfileAndGotoChangePassword());
    }

    private static class GetProfileAndGotoChangePassword extends BaseDialogTask<Void, Void, UserLike> {
        private User currentUser;

        GetProfileAndGotoChangePassword() {
            super();
            currentUser = AppKit.getAccountManager().getCurrentAccountUser().getAdaptiveUser();
        }

        @Override
        protected UserLike executeTask(Void... params) throws Exception {
            if (currentUser == null)
                return null;
            UserInfoRequest.INSTANCE.fetchMyProfile(currentUser, APILoggerKeys.MY_INDEX_SOURCE_CHANGE_PASSWORD);
            String type = getThirdType();
            if (TextUtils.isEmpty(type))
                return null;
            return AccountApi.getInstance().getThirdUserId(type, AppKit.getAccountManager().getCurrentAccountUser().getSession());
        }

        @Override
        protected void onTaskSuccess(UserLike result) {
            if (result != null) {
                AppAsm.getRouter(LoginRegisterRouter.class).gotoChangeThirdPwdActivity(getSafeActivity(), result);
            } else {
                AppAsm.getRouter(LoginRegisterRouter.class).gotoChangePwdIntent(getSafeActivity());
            }
        }

        private String getThirdType() {
            String type = currentUser.thirdPartyUser;
            if (TextUtils.isEmpty(type) && currentUser.is_wx_user == 1) {
                type = UserLike.TYPE_WECHAT;
            }
            return type;
        }
    }

    @LuaBridge
    public static void showPolicyDialog(Map policyMap) {
        SecurityInfo.Policy policy = new SecurityInfo.Policy();
        policy.setPop((Integer) policyMap.get("pop"));
        policy.setShowCloseButton((Integer) policyMap.get("showCloseButton"));
        policy.setPopUrl((String) policyMap.get("popUrl"));
        policy.setTitle((String) policyMap.get("title"));
        policy.setConsultGoto((String) policyMap.get("policy_goto"));
        if (!policy.isCanShowPop()) {
            return;
        }
        Context context = MomoKit.getTopActivity();
        WebViewDialog mWebViewDialog = new WebViewDialog(context);
        mWebViewDialog.setDialogClickListener(new WebViewDialog.OnDialogClickListener() {
            @Override
            public void closeClick() {
                mWebViewDialog.dismiss();
                showConfirmDialog(context, mWebViewDialog);
            }

            @Override
            public void agreeClick() {
                requestAgreePolicy("1", null, mWebViewDialog);
            }

            @Override
            public void lookAllProtectClick() {
                GotoDispatcher.action(policy.getConsultGoto(), context).execute();
            }
        });

        mWebViewDialog.setIvCloseVisibility(policy);
        mWebViewDialog.setTitle(policy);
        mWebViewDialog.loadUrl(policy);
        try {
            if (!mWebViewDialog.isShowing()) {
                mWebViewDialog.show();
            }
        } catch (Exception e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
    }

    private static void showConfirmDialog(Context context, WebViewDialog webViewDialog) {
        DataProtectConfirmDialog mDataProtectConfirmDialog = new DataProtectConfirmDialog(context, R.style.customDialog);
        mDataProtectConfirmDialog.getCustomer().setVisibility(View.GONE);
        mDataProtectConfirmDialog.getViewLineTwo().setVisibility(View.GONE);
        mDataProtectConfirmDialog.getTitle().setText("若无法得到你的授权，将无法使用完整功能");
        mDataProtectConfirmDialog.getExit().setText("稍后确认");
        mDataProtectConfirmDialog.setOnDataProtectConfirmListener(new DataProtectConfirmDialog.OnClickListener() {
            @Override
            public void onBack(DataProtectConfirmDialog dialog) {
                mDataProtectConfirmDialog.dismiss();
                if (webViewDialog != null) {
                    webViewDialog.show();
                }
            }

            @Override
            public void onExit(DataProtectConfirmDialog dialog) {
                mDataProtectConfirmDialog.dismiss();
                requestAgreePolicy("0", mDataProtectConfirmDialog, webViewDialog);
            }

            @Override
            public void onCustomer(DataProtectConfirmDialog dialog) {
                // default implementation ignored
            }
        });
        mDataProtectConfirmDialog.show();
    }

    private static void requestAgreePolicy(String status, DataProtectConfirmDialog confirmDialog, WebViewDialog webViewDialog) {
        MomoTaskExecutor.executeUserTask(LUA_CLASS_NAME, new BaseDialogTask() {
            @Override
            protected boolean mayCancleOnBackPress() {
                return false;
            }

            @Override
            protected boolean mayCancleOnTouchOutSide() {
                return false;
            }

            @Override
            protected Object executeTask(Object[] objects) throws Exception {
                UserApi.getInstance().setPolicyStatus(status);
                return null;
            }

            @Override
            protected void onTaskSuccess(Object o) {
                super.onTaskSuccess(o);
                if (confirmDialog != null) {
                    confirmDialog.dismiss();
                }
                if (webViewDialog != null) {
                    webViewDialog.dismiss();
                }
            }

            @Override
            protected void onTaskError(Exception e) {
                super.onTaskError(e);
            }
        });
    }
}
