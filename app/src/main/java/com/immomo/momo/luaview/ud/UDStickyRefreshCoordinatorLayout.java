package com.immomo.momo.luaview.ud;

import android.view.View;

import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.appbar.AppBarLayout;
import com.immomo.mls.fun.ud.view.UDView;
import com.immomo.mls.fun.ud.view.UDViewGroup;
import com.immomo.momo.luaview.weight.LUAStickyRefreshCoordinatorLayout;

import org.luaj.vm2.Globals;
import org.luaj.vm2.LuaFunction;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

@LuaApiUsed
public class UDStickyRefreshCoordinatorLayout<V extends LUAStickyRefreshCoordinatorLayout> extends UDViewGroup<V> {

    public static final String LUA_CLASS_NAME = "StickyRefreshCoordinatorLayout";

    public static final String[] methods = new String[]{
            "addContentView",
            "addAppBarView",
            "addTabView",
            "scrollToTop",
            "setRefreshEnable",
            "setRefreshListener",
            "startRefreshing",
            "isExpandAppBar",
            "isRefreshing"
    };

    private LuaFunction switchChangedCallback = null;

    @LuaApiUsed
    public UDStickyRefreshCoordinatorLayout(long L, LuaValue[] v) {
        super(L, v);
    }

    @LuaApiUsed
    public UDStickyRefreshCoordinatorLayout(Globals g, V jud) {
        super(g, jud);
    }

    public UDStickyRefreshCoordinatorLayout(Globals g) {
        super(g);
    }

    @Override
    protected V newView(LuaValue[] init) {
        return (V) new LUAStickyRefreshCoordinatorLayout(getContext());
    }

    @LuaApiUsed
    public LuaValue[] addContentView(LuaValue[] values) {
        if (values.length == 0) {
            return null;
        }
        getView().addContentView(((UDView) values[0].toUserdata()).getView());
        return null;
    }

    @LuaApiUsed
    public LuaValue[] addAppBarView(LuaValue[] values) {
        View appbarView = values.length != 0 ? ((UDView) values[0].toUserdata()).getView() : null;
        getView().addAppBarView(appbarView);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] addTabView(LuaValue[] values) {
        if (values.length != 0)
            getView().addStickyTabView(((UDView) values[0].toUserdata()).getView());
        return null;
    }

    @LuaApiUsed
    public LuaValue[] scrollToTop(LuaValue[] luaValues) {
        AppBarLayout appBarLayout = getView().getAppBarLayout();
        if (appBarLayout != null) {
            appBarLayout.setExpanded(true);
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setRefreshListener(LuaValue[] luaValues) {
        switchChangedCallback = luaValues[0].toLuaFunction();
        SwipeRefreshLayout refreshLayout = getView().getRefreshLayout();
        if (refreshLayout != null) {
            refreshLayout.setOnRefreshListener(() -> {
                checkStartRefresh();
            });
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setRefreshEnable(LuaValue[] values) {
        if (values != null) {
            boolean isEnable = values.length > 0 ? values[0].toBoolean() : true;
            getView().setRefreshEnable(isEnable);
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] startRefreshing(LuaValue[] values) {
        if (values != null) {
            boolean isEnable = values.length > 0 ? values[0].toBoolean() : false;
            getView().setRefreshing(isEnable);
            if (isEnable) {
                checkStartRefresh();
            }
        }
        return null;
    }

    @LuaApiUsed
    public LuaValue[] isExpandAppBar(LuaValue[] values) {
        return LuaValue.varargsOf(((getView().isExpandAppBar()) ? LuaValue.True() : LuaValue.False()));
    }

    @LuaApiUsed
    public LuaValue[] isRefreshing(LuaValue[] values) {
        if (values != null) {
            SwipeRefreshLayout refreshLayout = getView().getRefreshLayout();
            if (refreshLayout != null) {
                return LuaValue.varargsOf(((refreshLayout.isRefreshing()) ? LuaValue.True() : LuaValue.False()));
            }
        }
        return LuaValue.varargsOf(LuaValue.False());
    }

    private void checkStartRefresh() {
        if (switchChangedCallback != null) {
            switchChangedCallback.invoke(null);
        }
    }

}
