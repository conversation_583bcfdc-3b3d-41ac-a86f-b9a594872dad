package com.immomo.momo.luaview;

import android.content.Context;
import android.graphics.SurfaceTexture;
import android.net.Uri;
import android.os.Handler;
import android.os.Message;
import android.view.MotionEvent;

import com.immomo.framework.base.BaseActivity;
import com.immomo.momo.agora.mr.conflictHelper.VideoConflictNewHelper;
import com.immomo.momo.feed.player.IMediaPlayer;
import com.immomo.momo.luaview.weight.BorderRadiusMediaView;
import com.immomo.momo.util.StringUtils;
import com.immomo.momo.luaview.video.LuaIJKPlayer;

import java.lang.ref.WeakReference;
import java.util.Timer;
import java.util.TimerTask;

/**
 * Created by Zhang.ke on 2018/11/28.
 */

public class LuaMediaView extends BorderRadiusMediaView {

    private Uri mUri;
    private Callback callback;
    private ProgressUpdateCallback progressCallback;
    private boolean started = false;
    private boolean mute = false;
    private boolean firstUpdate = true;
    private boolean isViewExist;
    private boolean useProxyOrCache = true;
    private LuaIJKPlayer mPlayer;

    public LuaMediaView(Context context) {
        super(context);
        mPlayer = new LuaIJKPlayer();
        mPlayer.setLoopListener(new LuaIJKPlayer.LoopListener() {
            @Override
            public void onLoopStart() {
                if (callback != null) {
                    callback.onLoopStart();
                }
            }
        });
    }

    private ProccessTask timerTask = null;

    private final class ProccessTask extends TimerTask {
        @Override
        public void run() {
            if (!isViewExist) {
                if (timer != null) {
                    timer.cancel();
                }
                return;
            }
            if (progressCallback != null) {
                if (getDuration() <= 0 || !isPlaying() || mPlayer.getPlaybackState() != IMediaPlayer.STATE_READY) {
                    return;
                }
                handler.sendEmptyMessage(1);
            }
        }
    };
    private Timer timer;
    private Handler handler = new ProressHander(this);

    private static class ProressHander extends Handler {

        private WeakReference<LuaMediaView> reference;

        ProressHander(LuaMediaView luaMediaView) {
            this.reference = new WeakReference<>(luaMediaView);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == 1) {
                LuaMediaView luaMediaView = reference.get();
                if (luaMediaView != null && luaMediaView.progressCallback != null)
                    luaMediaView.progressCallback.onProgressUpdate(luaMediaView.mPlayer.getCurrentPosition());
            }
        }
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        boolean result = super.onTouchEvent(event);
        return result;
    }

    public void directAccess(boolean directAccess) {
        this.useProxyOrCache = !directAccess;//取反，useProxyOrCache的false为直播流
    }

    public void start() {
        start(useProxyOrCache);
    }

    public void start(boolean useProxyOrCache) {
        if (mUri == null)
            return;
        VideoConflictNewHelper.stopMusic();
        VideoConflictNewHelper.stopOutMusic();
        if (getContext() instanceof BaseActivity) {
            BaseActivity activity = ((BaseActivity) getContext());
            if (!activity.isForeground()) return;
        }
        mPlayer.setUseProxyOrCache(useProxyOrCache);
        mPlayer.setErrorListener(this);
        if (!mUri.equals(mPlayer.getCurrentUri())) {
            mPlayer.updatePlayPosition();
            mPlayer.prepareAndSeek(mUri);
        }
        mPlayer.setSilentMode(mute);
        started = true;
        acquireVideoTexture(getContext(), mPlayer);
        mPlayer.resume();
    }

    public void setUri(Uri uri) {
        mUri = uri;
//        IJKMediaPreLoader.getInstance().preloadVideo(uri);
    }

    public void checkUrl(String url) {
        mPlayer.setUseProxyOrCache(StringUtils.isStartWithHttpOrHttps(url));
    }

    public void pause() {
        mPlayer.pause();
    }

    public void stopPlayback() {
        mPlayer.release();
        firstUpdate = true;
    }

    public void resume() {
        mPlayer.resume();
    }

    public void setSilentMode(boolean silent) {
        mute = silent;
        if (started)
            mPlayer.setSilentMode(silent);
    }

    public void setLoopPlay(boolean loop) {
        mPlayer.setLoopPlay(loop);
    }

    @Override
    public void onSurfaceTextureUpdated(SurfaceTexture surface) {
        if (!firstUpdate)
            return;
        firstUpdate = false;
        if (callback != null)
            callback.onStartRendering();
    }

    @Override
    public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {
        if (callback != null) {
            callback.onPlayerStateChanged(playWhenReady, playbackState);
        }
    }

    @Override
    public void onVideoSizeChanged(int width, int height, int unappliedRotationDegrees, float pixelWidthHeightRatio) {
        super.onVideoSizeChanged(width, height, unappliedRotationDegrees, pixelWidthHeightRatio);
        if (callback != null) {
            callback.onVideoSizeChanged(width, height, unappliedRotationDegrees, pixelWidthHeightRatio);
        }
    }

    @Override
    public void onPlayerError(int what, int extra) {
        if (callback != null) {
            callback.onError(what, extra);
        }
    }

    public boolean canSeek() {
        if (mUri == null || !started)
            return false;
        int state = mPlayer.getPlaybackState();
        return state == IMediaPlayer.STATE_READY
                || state == IMediaPlayer.STATE_ENDED;
    }

    public long getDuration() {
        if (mUri == null || !started)
            return 0;
        return mPlayer.getDuration();
    }

    public long getCurrentPosition() {
        if (mUri == null || !started)
            return 0;
        return mPlayer.getCurrentPosition();
    }

    public void seekTo(long t) {
        if (mUri == null || !started)
            return;
        mPlayer.seekTo(t);
    }

    public boolean isPlaying() {
        if (mUri == null || !started)
            return false;
        return mPlayer.getPlayWhenReady();
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        isViewExist = true;
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
        timer = new Timer();

        if (timerTask != null) {
            timerTask.cancel();
            timerTask = null;
        }

        timerTask = new ProccessTask();
        timer.schedule(timerTask, 200, 500);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
        if (timerTask != null) {
            timerTask.cancel();
            timerTask = null;
        }
        isViewExist = false;
    }

    public void setProgressCallback(ProgressUpdateCallback progressCallback) {
        this.progressCallback = progressCallback;
    }

    public interface ProgressUpdateCallback {
        void onProgressUpdate(long progress);
    }

    public interface Callback {
        void onPlayerStateChanged(boolean playWhenReady, int playbackState);

        void onError(int what, int extra);

        void onStartRendering();

        void onVideoSizeChanged(int width, int height, int unappliedRotationDegrees, float pixelWidthHeightRatio);

        void onLoopStart();
    }
}
