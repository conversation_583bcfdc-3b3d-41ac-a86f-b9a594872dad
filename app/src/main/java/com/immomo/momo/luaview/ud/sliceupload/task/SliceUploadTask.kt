package com.immomo.momo.luaview.ud.sliceupload.task

import com.immomo.mmutil.task.MomoTaskExecutor
import com.immomo.momo.luaview.ud.sliceupload.callback.SliceUploadListener
import com.immomo.momo.luaview.ud.sliceupload.handler.SimpleSliceUploadHandler
import com.immomo.momo.luaview.ud.sliceupload.model.SliceUploadModel
import com.immomo.momo.luaview.ud.sliceupload.model.SliceUploadResult

/**
 * Created on 2019-12-06.
 * <AUTHOR>
 */

class SliceUploadTask(

    val sliceUploadHandler: SimpleSliceUploadHandler,
    val sliceUploadModel: SliceUploadModel,
    val sliceUploadListener: SliceUploadListener?
) :
    MomoTaskExecutor.Task<Any, Any, SliceUploadResult>("") {


    override fun executeTask(vararg params: Any): SliceUploadResult? {
        if (isCancelled) {
            return null
        }
        return sliceUploadHandler.upload(sliceUploadModel, sliceUploadListener)
    }

    override fun onCancelled() {
        super.onCancelled()
        sliceUploadListener?.onCancelled()
    }

    override fun interrupt() {
        super.interrupt()
        sliceUploadHandler?.interrupt()
    }

    override fun onPreTask() {
        super.onPreTask()
        if (isCancelled) {
            return
        }
        sliceUploadListener?.onStart()

    }

    override fun onTaskError(e: Exception?) {
        super.onTaskError(e)
        sliceUploadListener?.onFailed()
    }

    override fun onTaskSuccess(result: SliceUploadResult?) {
        super.onTaskSuccess(result)
        sliceUploadListener?.onSuccess(result)
    }

}