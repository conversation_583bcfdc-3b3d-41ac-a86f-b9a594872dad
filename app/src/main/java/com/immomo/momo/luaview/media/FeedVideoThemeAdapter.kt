package com.immomo.momo.luaview.media

import com.immomo.android.module.feedlist.data.api.mapper.theme.parseFeedTheme2Model
import com.immomo.android.module.feedlist.data.api.response.theme.FeedListTheme
import com.immomo.android.module.feedlist.domain.model.style.common.MicroVideoFeedModel

/**
 *
 * author: hongming.wei
 * data: 2023/4/7
 */
object FeedVideoThemeAdapter {

    fun parseFeedStringToModel(feedModel: String) : MicroVideoFeedModel?{
        val theme: FeedListTheme<*>? =
            FeedListTheme.moshi.adapter(FeedListTheme::class.java).fromJson(feedModel)
        val feedModel = theme.parseFeedTheme2Model()
        if (feedModel is MicroVideoFeedModel) {
            return feedModel
        }
        return null
    }

}