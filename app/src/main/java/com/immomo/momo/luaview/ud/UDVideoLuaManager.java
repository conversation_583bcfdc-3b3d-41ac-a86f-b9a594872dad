package com.immomo.momo.luaview.ud;

import static com.immomo.momo.protocol.api.FeedApi.Src;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Rect;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.cosmos.mdlog.MDLog;
import com.google.gson.reflect.TypeToken;
import com.immomo.android.module.feed.broadcast.FeedReceiver;
import com.immomo.android.module.feed.share.CommonFeedShareInfoKt;
import com.immomo.android.module.feed.statistics.EVPage;
import com.immomo.android.module.feedlist.data.api.mapper.theme.FeedListThemeMapperKt;
import com.immomo.android.module.feedlist.data.api.response.theme.FeedListTheme;
import com.immomo.android.module.feedlist.data.api.response.theme.common.CommonFeedSource;
import com.immomo.android.module.feedlist.domain.model.style.AbstractFeedModel;
import com.immomo.android.module.feedlist.domain.model.style.common.AbstractBasicFeedModel;
import com.immomo.android.module.feedlist.domain.model.style.common.AbstractMicroVideoFeedModel;
import com.immomo.android.module.feedlist.domain.model.style.common.AtlasFeedModel;
import com.immomo.android.module.feedlist.domain.model.style.common.MicroVideoFeedModel;
import com.immomo.android.module.feedlist.domain.model.style.common.TextPicFeedModel;
import com.immomo.android.module.feedlist.domain.model.style.inner.MicroVideoModel;
import com.immomo.android.router.momo.UserRouter;
import com.immomo.android.router.momo.bean.IUser;
import com.immomo.android.router.momo.business.feed.FeedConfigs;
import com.immomo.android.router.momo.business.message.ChatRouter;
import com.immomo.android.router.momo.util.LoggerUtilRouter;
import com.immomo.android.router.share.ShareDialogConfig;
import com.immomo.android.router.share.ShareRouter;
import com.immomo.android.share.page.ShareDialog;
import com.immomo.downloader.DownloadManager;
import com.immomo.downloader.bean.DownloadConstant;
import com.immomo.downloader.bean.DownloadTask;
import com.immomo.framework.SPKeys;
import com.immomo.framework.base.BaseActivity;
import com.immomo.framework.imageloader.ImageType;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.utils.UIUtils;
import com.immomo.mls.LuaViewManager;
import com.immomo.mls.annotation.LuaBridge;
import com.immomo.mls.annotation.LuaClass;
import com.immomo.mls.utils.LVCallback;
import com.immomo.mmutil.StringUtils;
import com.immomo.mmutil.log.Log4Android;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.Configs;
import com.immomo.momo.MomoKit;
import com.immomo.momo.android.broadcast.FriendListReceiver;
import com.immomo.momo.android.view.LoadingProgressPopuWindow;
import com.immomo.momo.businessmodel.statistics.PageStepHelper;
import com.immomo.momo.dynamicresources.ResourceCallbackAdapter;
import com.immomo.momo.dynamicresources.ResourceChecker;
import com.immomo.momo.feed.FeedStepHelper;
import com.immomo.momo.feed.bean.ForwardTailResource;
import com.immomo.momo.feed.bean.InputActionBar;
import com.immomo.momo.feed.bean.PublishFeedOptionsForward;
import com.immomo.momo.feed.bean.VideoSourceInfo;
import com.immomo.momo.feed.service.FeedTransmitService;
import com.immomo.momo.feed.util.FeedModelChatNavigator;
import com.immomo.momo.feed.util.FeedShareUtils;
import com.immomo.momo.feedlist.helper.FeedLuaEventHelper;
import com.immomo.momo.imagefactory.imageborwser.ImageBrowserConfig;
import com.immomo.momo.imagefactory.imageborwser.impls.FeedImageBrowserActivity;
import com.immomo.momo.innergoto.matcher.MicroVideoMatcher;
import com.immomo.momo.moment.publish.MomentVideoProcessor;
import com.immomo.momo.moment.utils.MicroVideoWaterMarkHelper;
import com.immomo.momo.moment.utils.VideoProcessorHelper;
import com.immomo.momo.mvp.nearby.presenter.DoForwardFeedTask;
import com.immomo.momo.permission.PermissionHelper;
import com.immomo.momo.protocol.api.FeedApi;
import com.immomo.momo.router.ProfileGotoOptions;
import com.immomo.momo.router.ProfileRouter;
import com.immomo.momo.router.RefreshTag;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.service.sessions.SessionUserCache;
import com.immomo.momo.share2.IShareDialog;
import com.immomo.momo.share2.listeners.BaseShareClickListener;
import com.immomo.momo.share2.listeners.FeedShareClickListener;
import com.immomo.momo.share3.data.CustomApp;
import com.immomo.momo.statistics.LoggerKeys;
import com.immomo.momo.util.AlbumNotifyHelper;
import com.immomo.momo.util.GsonUtils;
import com.immomo.momo.util.VideoUtils;
import com.immomo.momo.util.permissionlog.PermissionLogUtils;
import com.immomo.momo.video.model.Video;
import com.immomo.uiframework.old.LogTag;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.luaj.vm2.Globals;
import org.luaj.vm2.LuaString;
import org.luaj.vm2.LuaValue;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import info.xudshen.android.appasm.AppAsm;

@LuaClass(isStatic = true)
public class UDVideoLuaManager {
    public static final String LUA_CLASS_NAME = "VideoLuaManager";
    // 分享Listener
    private FeedReceiver receiver;
    private FriendListReceiver friendListReceiver;
    private LoadingProgressPopuWindow popuWindow;
    private static UDVideoLuaManager videoLuaManager;
    private static IShareDialog mShareDialog;

    @LuaBridge
    public static String getSourceInfo() {
        return MicroVideoMatcher.getMicroVideoLogSourceForLike(FeedStepHelper.INSTANCE.getPreFeedStepExcludeProfile());
    }

    @LuaBridge
    public static String getRandomString() {
        return UUID.randomUUID().toString();
    }


    public static boolean canShowGreet(String remoteID) {
        if (TextUtils.isEmpty(remoteID)) {
            return false;
        }
        User user = SessionUserCache.getUser(remoteID);
        if (user == null) {
            return false;
        }
        return user.showGreet == 1;
    }

    @LuaBridge
    public static void doFollow(String momoId, String videoId, String eventId, String source, LVCallback callback) {
        MomoTaskExecutor.executeUserTask(getTaskTag(), new RefreshLocalUserRelationTask(momoId));
        MomoTaskExecutor.executeUserTask(getTaskTag(), new FollowUserTask(momoId, videoId, eventId, source, callback));
    }

    @LuaBridge
    public static void gotoChat(String jsonFeed){
        try {
            AbstractBasicFeedModel feed = (AbstractBasicFeedModel) getFeedModelFromString(jsonFeed);
            JSONObject jsonObject1 = new JSONObject(jsonFeed);
            Map<String, String> logMap = null;
            try {
                if (jsonObject1.has("logmap")) {
                    String logmap = (String) jsonObject1.get("logmap");
                    logMap = GsonUtils.g().fromJson(logmap, new TypeToken<HashMap<String, String>>() {
                    }.getType());
                    if (logMap != null) {
                        feed.setLogMap(logMap);
                    }
                }
            } catch (Exception e) {
                MDLog.printErrStackTrace(LogTag.COMMON, e);
            }
            FeedModelChatNavigator.INSTANCE.navigateToChat(
                    MomoKit.getTopActivity(),
                    feed, FeedStepHelper.INSTANCE.getFeedUpStepConfig().getLogSource()
            );
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @LuaBridge
    public static void gotoChatFromVideoWall(String remoteid,boolean canShowGreet) {
        if (StringUtils.isEmpty(remoteid)) {
            return;
        }
        if (null != MomoKit.getTopActivity()) {
            int model = ChatRouter.SHOW_MODE_FULL;
            boolean isGreetShow = false;
            IUser user = AppAsm.getRouter(UserRouter.class).getUser(remoteid);
            if (user != null && user.getShowGreet() == 1) {//查询数据库判断是否可以显示招呼弹窗还是全屏对话
                isGreetShow = true;
            }
            if (!isGreetShow) {
                if (canShowGreet) {
                    model = ChatRouter.SHOW_MODE_GREET_HALF;
                    isGreetShow = true;
                }
            }
            AppAsm.getRouter(ChatRouter.class).startChatActivity( MomoKit.getTopActivity(), remoteid, FeedStepHelper.INSTANCE.getFeedUpStepConfig().getLogSource(), model, isGreetShow);
        }
    }

    /**
     * 分享弹窗
     * @param jsonData 基础数据
     * @param callback 弹窗消失回调
     */
    @LuaBridge
    public static void showShareDialog(String jsonData, LVCallback callback, Globals globals) {
        BaseActivity context = (BaseActivity) ((LuaViewManager) globals.getJavaUserdata()).context;
        AbstractBasicFeedModel feed = (AbstractBasicFeedModel) getFeedModelFromString(jsonData);
        if (feed == null || context == null) {
            return;
        }

        List<CustomApp> customAppList = new ArrayList<>();

        showShareDialog(context, feed, customAppList, callback, null);
    }


    @LuaBridge
    public static void showNewShareDialog(String jsonData, LVCallback callback, Map params, LVCallback shareCallback,Globals globals) {
        BaseActivity context = (BaseActivity) ((LuaViewManager) globals.getJavaUserdata()).context;
        AbstractBasicFeedModel feed = (AbstractBasicFeedModel) getFeedModelFromString(jsonData);
        if (feed == null || context == null) {
            return;
        }

        List<CustomApp> customAppList = new ArrayList<>();
        if (params != null && params.containsKey("funcs")) {
            List<Map<String, String>> funcList = (List) params.get("funcs");
            if (funcList != null && funcList.size() > 0) {
                CustomApp item;
                for (Map<String, String> mapItem : funcList) {
                    item = new CustomApp();
                    item.app = mapItem.get("platformString");
                    item.appName = mapItem.get("title");
                    item.icon = mapItem.get("icon");
                    item.darkIcon = mapItem.get("darkIcon");
                    customAppList.add(item);
                }
            }
        }

        showShareDialog(context, feed, customAppList, callback, shareCallback);
    }

    private static void showShareDialog(BaseActivity context, AbstractBasicFeedModel feed, List<CustomApp> customAppList, LVCallback callback, @Nullable LVCallback shareCallback) {
        if (videoLuaManager != null) {
            videoLuaManager = null;
        }
        videoLuaManager = new UDVideoLuaManager();

        BaseShareClickListener<?> localShareClickListener = videoLuaManager.getShareClickListener((AbstractMicroVideoFeedModel) feed, shareCallback);
        ShareDialogConfig config = new ShareDialogConfig.Builder(context)
                .mShareListener(localShareClickListener)
                .mPageConfig(FeedShareUtils.getVideoShareDialogUIConfig(feed.isMe(), feed.isPrivate(), false, false, true, customAppList))
                .build();
        mShareDialog = AppAsm.getRouter(ShareRouter.class).showShareDialog(config);
        mShareDialog.setOnDismissListener(dialog1 -> {
            callback.call();
            mShareDialog = null;
        });
    }

    @LuaBridge
    public static void dismissShareDialog() {
        if (mShareDialog != null) {
            mShareDialog.dismiss();
            mShareDialog = null;
        }
    }

    private BaseShareClickListener<?> getShareClickListener(AbstractMicroVideoFeedModel feed, @Nullable LVCallback shareCallback) {
        FeedShareClickListener  forwardShareClickListener = new FeedShareClickListener(MomoKit.getTopActivity()) {
                @Override
                public void onClickSavePhoto() {
                    super.onClickSavePhoto();
                    clickSavePhoto(feed);
                }

                @Override
                public void onClick(String keyName) {
                    super.onClick(keyName);
                    try {
                        JSONObject obj = new JSONObject();
                        obj.put("platformString", keyName);
                        if (shareCallback != null) {
                            shareCallback.call(obj.toString());
                        }
                    } catch (Exception e) {
                        MDLog.printErrStackTrace("UDVideoLuaManager", e);
                    }
                }

                @Override
                public void shareToPublishFeedActivity() {

                    AppAsm.getRouter(LoggerUtilRouter.class).saveGotoLog(LoggerKeys.LOCAL_LOG_KEY_ABTEST_VIDEO_PLAY_FORWARD_BUTTON_SHARE_INTERNAL);

                    MicroVideoModel microVideo = feed.getMicroVideo();
                    if (microVideo != null) {
                        MicroVideoModel.Video video = microVideo.getVideo();

                        VideoSourceInfo videoSource = new VideoSourceInfo();
                        videoSource.setVideoId(microVideo.getMicroVideoId());

                        videoSource.setVideoPath(video.getVideoUrl());
                        videoSource.setVideoRation(video.getScreenRatio());
                        videoSource.setVideoCover(video.getCover());

                        ForwardTailResource forwardTailResource = new ForwardTailResource();
                        forwardTailResource.setFeedId(feed.getFeedId());
                        forwardTailResource.setOriginFeedId(microVideo.getOriginFeedId());

                        HashMap<String, String> apiExtra = new HashMap<>();
                        apiExtra.put(Src, FeedConfigs.PUBLISH_FEED_FROM.VIDEO_PLAYER_DETAIL);
                        PublishFeedOptionsForward publishFeedOptions = (PublishFeedOptionsForward) new PublishFeedOptionsForward(forwardTailResource, videoSource)
                                .inputActionBtn(InputActionBar.INPUT_TXT)
                                .barTxt("转发动态")
                                .apiExtraMap(apiExtra);
                        publishForwardVideoFeed(publishFeedOptions,feed);
                    }
                }
            };
            forwardShareClickListener.setSourcePage(EVPage.NearbyFeed.VideoDetail);
            forwardShareClickListener.setLogInfo("", new HashMap<String, String>() {{
                put("doc_id", feed.getFeedId());
                put("avatar_id", feed.getUserId());
            }}, 0);
            CommonFeedShareInfoKt.setCommonFeedShareInfoCompat(forwardShareClickListener, feed);
            forwardShareClickListener.setFromForwardBtn(true);
            return forwardShareClickListener;
    }


    public void publishForwardVideoFeed(PublishFeedOptionsForward publishFeedOptions,AbstractMicroVideoFeedModel feedModel) {

        if (!TextUtils.isEmpty(feedModel.getFeedId())) {
            MomoTaskExecutor.executeUserTask(getTaskTag(),
                    new DoForwardFeedTask(feedModel, publishFeedOptions, MicroVideoMatcher.buildApiMicroVideoSource(
                            PageStepHelper.INSTANCE.getFeedUpStepConfig().getMicroVideoSourceType(), PageStepHelper.INSTANCE.getFeedUpStepConfig().getLogSource(), null)));
        }
    }


    // 保存到本地的下载task
    private static DownloadTask task;
    private static File targetFile;

    public void clickSavePhoto(AbstractMicroVideoFeedModel feed) {
        String  downloadUrl = feed.getMicroVideo().getVideo().getDownloadUrl();
        if (TextUtils.isEmpty(downloadUrl)) {
            return;
        }
        if (!PermissionHelper.INSTANCE.checkStoragePermission(MomoKit.getTopActivity())) {
            PermissionLogUtils.getInstance().uploadPermissionLog(PermissionLogUtils.LogFromKey.TYPE_MEDIA_SAVE, Manifest.permission.WRITE_EXTERNAL_STORAGE);
            return;
        }
        targetFile = new File(Configs.getCameraHome(), StringUtils.md5(downloadUrl) + ".mp4");
        if (targetFile.exists() && targetFile.length() > 0) {
            AlbumNotifyHelper.getAblumNotifyHelper().copyVideoFile(true, targetFile);
            Toaster.showInvalidate("视频已存在相册中");
            return;
        }
        task = DownloadManager.getInstance().findRunningTaskById(downloadUrl);
        if (null == task) {
            task = new DownloadTask();
            task.isShowNotify = false;
            task.downloadType = DownloadConstant.DOWNLOAD_TYPE_COMMON;
            task.sourceUrl = downloadUrl;
            task.taskID = task.sourceUrl;
            task.savePath = targetFile.getAbsolutePath();
            task.mProirity = DownloadConstant.PRIORITY_ACTIVE_START;
            int result = DownloadManager.getInstance().add(task);
            if (result == DownloadManager.DownloadResult.INVALID ||
                    result == DownloadManager.DownloadResult.MEDIA_UNMOUNTED ||
                    result == DownloadManager.DownloadResult.NO_AVALIABLE_NETWORK) {
                Toaster.show("下载失败，请重新尝试");
            } else {
                DownloadManager.getInstance().addDownloadListener(UDVideoLuaManager.class.getName(),
                        new DownLoadListener(downloadUrl,feed));
            }
        }
    }


    class DownLoadListener implements DownloadManager.DownloadListener {

        private String downloadUrl;
        private AbstractMicroVideoFeedModel model;

        DownLoadListener(String  downLoadUrl,AbstractMicroVideoFeedModel model){
            this.downloadUrl = downLoadUrl;
            this.model = model;

            closePupopWindow();

            if (MomoKit.getTopActivity() != null && MomoKit.getTopActivity().getWindow() != null && MomoKit.getTopActivity().getWindow().getDecorView() != null) {
                popuWindow = new LoadingProgressPopuWindow(MomoKit.getTopActivity());
                popuWindow.show(MomoKit.getTopActivity().getWindow().getDecorView());
                popuWindow.setOnDismissListener(() -> {
                    DownloadManager.getInstance().cancel(task, false);
                    Toaster.show("已取消下载");
                });
            }
        }

        @Override
        public void onStart(DownloadManager manager, DownloadTask task) {

        }

        @Override
        public void onProcess(DownloadManager manager, DownloadTask task) {
            if (downloadUrl.equals(task.taskID) && popuWindow != null) {
                popuWindow.setProgress((task.completeNum * 100F / task.totalNum));
            }
        }

        @Override
        public void onPause(DownloadManager manager, DownloadTask task) {
        }

        @Override
        public void onCancel(DownloadManager manager, DownloadTask task) {
        }

        @Override
        public void onFailed(DownloadManager manager, DownloadTask task, int cause) {
            if (!TextUtils.isEmpty(downloadUrl) &&
                    downloadUrl.equals(task.taskID)) {
                Toaster.show("下载失败，请重新尝试");
                closePupopWindow();
            }
        }

        @Override
        public void onCompleted(DownloadManager manager, DownloadTask task) {
            if (!TextUtils.isEmpty(downloadUrl) &&
                    downloadUrl.equals(task.taskID)) {
                checkResource(targetFile.getAbsolutePath(), model);
            }
            }
        }

    public void checkResource(String path, AbstractMicroVideoFeedModel model) {
        if (ResourceChecker.needBlockService(ResourceChecker.BUSINESS_TYPE_PHOTO,
                                             ResourceChecker.TYPE_RECORDER,
                                             new ResourceCallbackAdapter() {
                                                 @Override
                                                 public void onSuccess() {
                                                     addWaterMark(path, model);
                                                 }
                                             })) {
            return;
        }

        addWaterMark(path, model);
    }

    private VideoProcessorHelper processorHelper;

    private void addWaterMark(final String downloadFilePath,AbstractMicroVideoFeedModel playingFeed) {
        boolean needWatermark = KV.getUserBool(SPKeys.User.MicroVideo.KEY_VIDEO_NEED_WATERMARK, false);
        processorHelper = prepareDraft(downloadFilePath, needWatermark);
        Video video = new Video(downloadFilePath);
        if (playingFeed != null && VideoUtils.getVideoFixMetaInfo(video)) {
            processorHelper.getDraft().setVideoWidth(video.width);
            processorHelper.getDraft().setVideoHeight(video.height);
        }
        processorHelper.startProcess(MomoKit.getTopActivity(), new MomentVideoProcessor.OnProcessListener() {
            @Override
            public void onProcessProgress(float progress) {
            }

            @Override
            public void onProcessFinish(String videoPath) {
                //删除下载的视频，将此视频复制到下载路径
                File downloadFile = new File(downloadFilePath);
                File renameFile = new File(downloadFilePath + "_");
                downloadFile.renameTo(renameFile);
                boolean result = VideoUtils.saveVideoFileToGallery(new File(videoPath), downloadFile);
                if (result) {
                    renameFile.delete();
                    MomoMainThreadExecutor.post(getTaskTag(), new Runnable() {
                        @Override
                        public void run() {
                            closePupopWindow();
                            Toaster.show("已保存到本地相册中");
                        }
                    });
                } else {
                    //保存失败
                    renameFile.renameTo(downloadFile);
                    onAddWaterMarkSuccess(downloadFile);
                }
            }

            @Override
            public void onProcessStart() {
            }

            @Override
            public void onProcessFailed(Exception ex) {
                Log4Android.getInstance().d("tang----水印合成失败");
                //如果合成失败，则将下载的视频直接保存即可
                onAddWaterMarkFailed(new File(downloadFilePath));
            }

            @Override
            public void onDraftWrong() {
                onAddWaterMarkFailed(new File(downloadFilePath));
            }
        }, true);
    }

    private void onAddWaterMarkFailed(final File downloadFile) {
        MomoMainThreadExecutor.post(getTaskTag(), new Runnable() {
            @Override
            public void run() {
                try {
                    downloadFile.delete();
                } catch (Exception e) {
                    Log4Android.getInstance().e(e);
                }
                closePupopWindow();
                Toaster.show("下载失败，请重新尝试");
            }
        });
    }

    private VideoProcessorHelper prepareDraft(String mSourceVideoPath, boolean addWaterMark) {
        //创建一个 blendBitap 将水印绘制上去
        Bitmap blendBitmap = null;
        //增加水印
        if (addWaterMark) {
            final Video v = new Video(mSourceVideoPath);
            try {
                VideoUtils.getVideoFixMetaInfo(v);
                int videoWidth = v.width;
                int videoHeight = v.height;
                blendBitmap = MicroVideoWaterMarkHelper.generateBlendBitmap(videoWidth, videoHeight);
            } catch (Exception e) {
                Log4Android.getInstance().e(e);
            }
        }
        return VideoProcessorHelper.newHelper(mSourceVideoPath, blendBitmap);
    }

    private void onAddWaterMarkSuccess(final File file) {
        MomoMainThreadExecutor.post(getTaskTag(), new Runnable() {
            @Override
            public void run() {
                //加入到相册
                VideoUtils.copyVideoFileToGallery(file);
                Toaster.show("已保存到本地相册中");
                closePupopWindow();
            }
        });
    }

    protected static String getTaskTag() {
        return "UD_FEED_MANAGER";
    }


    private static AbstractFeedModel getFeedModelFromString(String jsonFeed){
        JSONObject jsonObject1 = null;
        try {
            jsonObject1 = new JSONObject(jsonFeed);
            if (jsonObject1.has("source")) {
                JSONObject source = new JSONObject(jsonObject1.get("source").toString());
                MDLog.e("LogLogLog", "getFeedModel:= " + source.has("contentData"));
                if (jsonObject1.optInt("theme", 0) == 64) {
                    CommonFeedSource commonFeedSource = FeedApi.parseCommonFeedSource(source);
                    commonFeedSource.setTheme(22);
                    commonFeedSource.setLogId(jsonObject1.has("logid") ? jsonObject1.get("logid").toString() : "");
                    AtlasFeedModel atlasFeedModel = FeedApi.parseAtlasFeed(commonFeedSource);
                    return atlasFeedModel;
                }
                if (source.has("contentData")) {
                    FeedListTheme<?> theme = FeedListTheme.Companion.getMoshi().adapter(FeedListTheme.class).fromJson(jsonFeed);
                    return FeedListThemeMapperKt.parseFeedTheme2Model(theme);
                } else {
                    CommonFeedSource commonFeedSource = FeedApi.parseCommonFeedSource(source);
                    commonFeedSource.setTheme(23);
                    commonFeedSource.setLogId(jsonObject1.has("logid") ? jsonObject1.get("logid").toString() : "");
                    MicroVideoFeedModel abstractCommonModel = FeedApi.parseMicroVideoFeed(commonFeedSource);
                    return abstractCommonModel;
                }
            } else {
                CommonFeedSource commonFeedSource = FeedApi.parseCommonFeedSource(jsonObject1);
                commonFeedSource.setTheme(23);
                MicroVideoFeedModel abstractCommonModel = FeedApi.parseMicroVideoFeed(commonFeedSource);
                return abstractCommonModel;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return  null;

    }


    @LuaBridge
    public static void gotoPic(String dataSource,String feedId,String remoteid,int position,String guid,String originImage,String image,String feedIdString){
        try {
            JSONObject jsonObject = new JSONObject(dataSource);
            FeedListTheme<?> theme = FeedListTheme.Companion.getMoshi().adapter(FeedListTheme.class).fromJson(String.valueOf(jsonObject));
            TextPicFeedModel feed = (TextPicFeedModel) FeedListThemeMapperKt.parseFeedTheme2Model(theme);

            JSONArray guidList = new JSONArray(guid);
            JSONArray originImageList = new JSONArray(originImage);
            JSONArray imageList = new JSONArray(image);
            JSONArray feedIdArray = new JSONArray(feedIdString);

            List<String> feedimgList = new ArrayList<>();
            List<String> originalFeedimgList = new ArrayList<>();
            List<String> feedGuids = new ArrayList<>();
            ArrayList<String> feedIds = new ArrayList<>();

            for (int i = 0; i < guidList.length(); i++) {
                feedGuids.add(guidList.getString(i));
            }

            for (int i = 0; i < originImageList.length(); i++) {
                originalFeedimgList.add(originImageList.getString(i));
            }

            for (int i = 0; i < imageList.length(); i++) {
                feedimgList.add(imageList.getString(i));
            }

            for (int i = 0; i < feedIdArray.length(); i++) {
                feedIds.add(feedIdArray.getString(i));
            }

            if (FeedTransmitService.INSTANCE.get(feedId) == null) {
                FeedTransmitService.INSTANCE.put(feedId, feed);
            }
            Context context = MomoKit.getTopActivity();
            Rect[] imageBounds = new Rect[1];
            imageBounds[0] = new Rect(UIUtils.getScreenWidth() / 2, UIUtils.getScreenHeight() / 2, UIUtils.getScreenWidth() / 2, UIUtils.getScreenHeight() / 2);
            Intent intent = new Intent(context, FeedImageBrowserActivity.class);
            JSONObject extra = new JSONObject();
            try {
                extra.put(FeedImageBrowserActivity.KEY_HEADER_AUTOHIDE, true);
                extra.put(FeedImageBrowserActivity.KEY_FEED_ID, feedId);
                extra.put(FeedImageBrowserActivity.KEY_REMOTE_ID, remoteid);
                extra.put(FeedImageBrowserActivity.KEY_IS_FROM_RECOMMEND, true);
                extra.put(FeedImageBrowserActivity.KEY_LAST_IMAGE_GUID, feedGuids.get(feedGuids.size() - 1));
                extra.put(FeedImageBrowserActivity.KEY_HASREMAIN, false);
                intent.putStringArrayListExtra(FeedImageBrowserActivity.KEY_FEED_ID_LIST, feedIds);
            } catch (JSONException e) {
                MDLog.printErrStackTrace(com.immomo.android.module.feed.LogTag.COMMON, e);
            }

            ImageBrowserConfig config = new ImageBrowserConfig.Builder()
                    .imageType(ImageBrowserConfig.Type.TYPE_FEED)
                    .startIndex(position)
                    .imageBounds(imageBounds)
                    .thumbImageType(ImageType.IMAGE_TYPE_FEEDIMG_400X400)
                    .imageThumbUrls(feedimgList.toArray(new String[feedimgList.size()]))
                    .imageLargeUrls(originalFeedimgList.toArray(new String[originalFeedimgList.size()]))
                    .extraData(extra.toString())
                    .imageMode(ImageBrowserConfig.Mode.IMAGE_URL)
                    .build();
            intent.putExtra(FeedImageBrowserActivity.KEY_CONFIG, config);


            context.startActivity(intent);
            if (context instanceof Activity) {
                Activity activity = (Activity) context;
                if (activity.getParent() != null) {
                    activity.getParent().overridePendingTransition(0, 0);
                } else {
                    activity.overridePendingTransition(com.immomo.android.momo.feed.R.anim.feed_image_enter, 0);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @LuaBridge
    public static void gotoProfile(String momoId, Globals globals){
        BaseActivity context = (BaseActivity) ((LuaViewManager) globals.getJavaUserdata()).context;

        ProfileGotoOptions options = new ProfileGotoOptions(momoId);
        options.setRequestTypeTag(RefreshTag.LOCAL);
        AppAsm.getRouter(ProfileRouter.class).gotoProfile(context, options);
    }


    @LuaBridge
    public static void videoReceiver(LVCallback callback){
        if (videoLuaManager == null) {
            videoLuaManager = new UDVideoLuaManager();
        }
        videoLuaManager.createReceiver(MomoKit.getApp(), callback);
    }

    @LuaBridge
    public static void unregisterFeedReceiver() {
        if (videoLuaManager == null) {
            videoLuaManager = new UDVideoLuaManager();
        }
        videoLuaManager.unregisterFeedVideoReceiver(MomoKit.getApp());
    }


    private static class FollowUserTask extends MomoTaskExecutor.Task<Object, Object, String> {
        private String momoId;
        private String videoId;
        private String eventId;
        private String source;
        private LVCallback callback;

        public FollowUserTask(String momoId, String videoId, String eventId, String source, LVCallback callback) {
            this.momoId = momoId;
            this.videoId = videoId;
            this.eventId = eventId;
            this.source = source;
            this.callback = callback;
        }

        @Override
        protected String executeTask(Object... params) throws Exception {
            return FeedApi.getInstance().microVideoFollow(momoId, videoId, source, eventId);
        }

        @Override
        protected void onTaskSuccess(String o) {
            if (StringUtils.isNotBlank(o)) {
                Toaster.show(o);
            }
            if (callback != null) {
                callback.call(true, o);
            }
        }
    }

    private static class RefreshLocalUserRelationTask extends MomoTaskExecutor.Task<Object, Object, Object> {
        private String momoId;

        public RefreshLocalUserRelationTask(String momoId) {
            this.momoId = momoId;
        }

        @Override
        protected Object executeTask(Object... params) throws Exception {
            AppAsm.getRouter(UserRouter.class).doAfterFollowSuccess(momoId);
            return null;
        }

        @Override
        protected void onTaskSuccess(Object o) {
            Intent intent = new Intent(FriendListReceiver.ACTION_ADD_FRIEND);
            intent.putExtra(FriendListReceiver.KEY_MOMOID, momoId);
            FriendListReceiver.send(intent);
        }
    }

    private void closePupopWindow(){
        if (popuWindow != null) {
            popuWindow.cancel();
            popuWindow = null;
        }
    }

    private void unregisterFeedVideoReceiver(Context context) {
        if (receiver != null) {
            receiver.unregister();
            receiver = null;
        }

        if (friendListReceiver != null) {
            context.unregisterReceiver(friendListReceiver);
            friendListReceiver = null;
        }
    }

    private void createReceiver(Context context, LVCallback callback) {
        receiver = new FeedReceiver(context);
        receiver.setReceiveListener(intent -> {
            String action = intent.getAction();
            Map<String, Object> map = new HashMap<>();
            if (FeedReceiver.ACTION_FEED_NOT_INTERESTED.equals(action)) {
                Toaster.show("操作成功，将减少此类视频");
                String feedId = intent.getStringExtra(FeedReceiver.KEY_FEEDID);
                map.put("feedId", feedId);
                map.put("action", "ACTION_FEED_NOT_INTERESTED");
                callback.call(map);
            } else if (FeedReceiver.ACTION_FEED_DELETE.equals(action)) {
                String feedId = intent.getStringExtra(FeedReceiver.KEY_FEEDID);
                map.put("feedId", feedId);
                map.put("action", "ACTION_FEED_DELETE");
                callback.call(map);
            } else if (FeedReceiver.ACTION_FEED_CHANGED.equals(action)) {
                String feedId = intent.getStringExtra(FeedReceiver.KEY_FEEDID);
                FeedLuaEventHelper.INSTANCE.sendFeedChangeGlobalEvent(feedId);
            } else if (FeedReceiver.ACTION_FEED_FORWARD_SUCCESS.equals(action)) {
                String originalFeedId = intent.getStringExtra(FeedReceiver.KEY_ORIGINAL_FEED_ID);
                int newForwardTimes = intent.getIntExtra(FeedReceiver.KEY_CURRENT_FORWARD_TIMES, 0);
                map.put("action", "ACTION_FEED_FORWARD_SUCCESS");
                map.put("feedId", originalFeedId);
                map.put("forwardCount", newForwardTimes);
                callback.call(map);
            } else if (FeedReceiver.ACTION_FEED_STATUS_CHANGE.equals(action)) {
                String feedId = intent.getStringExtra(FeedReceiver.KEY_FEEDID);
                int status = intent.getIntExtra(FeedReceiver.KEY_STATUS, 0);
                String hideText = intent.getStringExtra(FeedReceiver.KEY_HIDE_TEXT);
                map.put("feedId", feedId);
                map.put("status", status);
                map.put("hideText", hideText);
                map.put("action", "ACTION_FEED_PRIVATE_STATUS");
                callback.call(map);
            }
        });

        friendListReceiver = new FriendListReceiver(context);
        friendListReceiver.setReceiveListener(intent -> {
            if (intent == null || TextUtils.isEmpty(intent.getAction())) {
                return;
            }
            Map<String, Object> map = new HashMap<>();
            String action = intent.getAction();
            String momoId = intent.getStringExtra(FriendListReceiver.KEY_MOMOID);
            String relation = intent.getStringExtra(FriendListReceiver.ACTION_REFRESH);

            if (FriendListReceiver.ACTION_ADD_FRIEND.equals(action)) {
                if (StringUtils.isEmpty(relation)){
                    relation = IUser.RELATION_FOLLOW;
                }
                map.put("momoId", momoId);
                map.put("action", "ACTION_ADD_FRIEND");
                map.put("relation", relation);
                callback.call(map);
            } else if (FriendListReceiver.ACTION_DELETE_FRIEND.equals(action)) {
                if (StringUtils.isEmpty(relation)){
                    relation = IUser.RELATION_NONE;
                }
                map.put("momoId", momoId);
                map.put("action", "ACTION_DELETE_FRIEND");
                map.put("relation", relation);
                callback.call(map);
            }
        });
    }



}
