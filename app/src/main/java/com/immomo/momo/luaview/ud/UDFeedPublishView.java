package com.immomo.momo.luaview.ud;

import android.app.Activity;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.immomo.android.module.feed.statistics.EVAction;
import com.immomo.android.module.feed.statistics.EVPage;
import com.immomo.android.router.momo.util.LoggerUtilRouter;
import com.immomo.mls.fun.ud.UDMap;
import com.immomo.mls.fun.ud.view.UDView;
import com.immomo.mmstatistics.event.ClickEvent;
import com.immomo.mmstatistics.event.Event;
import com.immomo.momo.MomoKit;
import com.immomo.momo.feed.ui.CachePublishType;
import com.immomo.momo.feed.ui.PublishFeedButton;
import com.immomo.momo.feed.ui.PublishFeedButtonKt;
import com.immomo.momo.feed.util.BasePublishUtil;
import com.immomo.momo.feed.util.PublishLuaClickManager;
import com.immomo.momo.statistics.LoggerKeys;

import org.jetbrains.annotations.NotNull;
import org.luaj.vm2.LuaValue;
import org.luaj.vm2.utils.LuaApiUsed;

import java.util.Map;

import info.xudshen.android.appasm.AppAsm;


@LuaApiUsed
public class UDFeedPublishView extends UDView<PublishFeedButton> {
    public static final String LUA_CLASS_NAME = "PublishProgressView";
    private String page;
    private String action;
    private String requireId;
    private String logId;
    /**
     * false情况下正常上报个人资料页打点
     */
    private boolean upLoad = false;

    private PublishFeedButton publishFeedButton;

    public static final String[] methods = {
            "setCustomStyle",
            "isProgressOrError",
            "setLogParam",
            "release"
    };


    @LuaApiUsed
    public UDFeedPublishView(long L, LuaValue[] initParams) {
        super(L, initParams);
    }

    @NotNull
    @Override
    protected PublishFeedButton newView(LuaValue[] init) {
        publishFeedButton = new PublishFeedButton(getContext());
        publishFeedButton.setOnClickListener(v -> {
            AppAsm.getRouter(LoggerUtilRouter.class).saveGotoLog(LoggerKeys.Record.FEED_RELEASE_BUTTON + LoggerKeys.Record.MINE);

            Activity activity = MomoKit.getTopActivity();
            BasePublishUtil.checkDraftBeforePublish(activity, new Handler.Callback() {
                @Override
                public boolean handleMessage(@NonNull Message msg) {
                    if (upLoad) {
                        if (!TextUtils.isEmpty(page) && !TextUtils.isEmpty(action) && !TextUtils.isEmpty(requireId)) {
                            ClickEvent.create()
                                    .page(new Event.Page(page, null, requireId))
                                    .requireId(requireId)
                                    .action(new Event.Action(action, null))
                                    .logId(logId)
                                    .submit();
                        }
                    }else {
                        ClickEvent.create()
                                .page(EVPage.Profile.PersonalFeed)
                                .action(EVAction.Head.SendFeed)
                                .submit();
                    }

                    if (activity != null) {
                        PublishLuaClickManager.gotoPublishFeed(
                                activity,
                                activity.getClass().getName(),
                                LoggerKeys.Record.MINE,
                                null);
                    }

                    return false;
                }
            });
        });
        return publishFeedButton;
    }

    @LuaApiUsed
    public LuaValue[] setCustomStyle(LuaValue[] vars) {
        PublishFeedButtonKt.customStyle(publishFeedButton, CachePublishType.PageFrom.Lua);
        return null;
    }

    @LuaApiUsed
    public LuaValue[] setLogParam(LuaValue[] vars) {
        UDMap map = vars.length != 0 ? (UDMap) vars[0].toUserdata() : null;

        if (map == null) {
            return null;
        }
        Map dataMap = map.getMap();
        page = (String) dataMap.get("page");
        action = (String) dataMap.get("action");
        requireId = (String) dataMap.get("requireId");
        logId = (String) dataMap.get("logId");
        upLoad = (boolean) dataMap.get("upLoad");
        return null;
    }

    @LuaApiUsed
    public LuaValue[] isProgressOrError(LuaValue[] vars) {
        if (publishFeedButton != null) {
           return LuaValue.rBoolean(publishFeedButton.isProgressingOrFail());
        }
        return LuaValue.rBoolean(false);
    }

    @LuaApiUsed
    public LuaValue[] release(LuaValue[] vars) {
        publishFeedButton.release();
        return null;
    }

}
