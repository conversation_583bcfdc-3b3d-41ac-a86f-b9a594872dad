package com.immomo.momo.cleaner;

import com.immomo.mmutil.FileUtil;

import java.io.File;
import java.io.FilenameFilter;

/**
 * Created by tanji<PERSON> on 2017/7/4.
 */

public class ChatBackgroundCleaner {
    private static String ChatBGPath = "immomo/avatar/large/b";

    public static void clean() {
        File fileDir = new File(FileUtil.getSafeExternalStorage(), ChatBGPath);
        if (fileDir.exists() && fileDir.isDirectory()) {
            File[] files = fileDir.listFiles(new FilenameFilter() {
                @Override
                public boolean accept(File dir, String name) {
                    return name.startsWith("bg_chat");
                }
            });
            for (File file: files) {
                file.delete();
            }
        }
    }
}
