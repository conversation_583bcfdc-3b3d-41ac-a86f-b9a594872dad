package com.immomo.momo.discuss.service;

import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.mmutil.log.Log4Android;
import com.immomo.momo.MomoKit;
import com.immomo.momo.common.AppKit;
import com.immomo.momo.discuss.bean.Discuss;
import com.immomo.momo.discuss.bean.DiscussUser;
import com.immomo.momo.discuss.dao.DiscussDao;
import com.immomo.momo.discuss.dao.DiscussUserDao;
import com.immomo.momo.discuss.dao.MyDiscussDao;
import com.immomo.momo.fullsearch.base.FullSearchHelper;
import com.immomo.momo.service.BaseService;
import com.immomo.momo.service.sessions.SessionUserCache;
import com.immomo.momo.service.user.UserService;
import com.immomo.momo.util.MemoryCache;
import com.immomo.momo.util.StringUtils;
import com.tencent.wcdb.database.SQLiteDatabase;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;


public class DiscussService extends BaseService {
    private static DiscussService instance;
    private MyDiscussDao myDiscussDao = null;
    private DiscussDao discussDao = null;
    private UserService userService = null;
    private DiscussUserDao duserDao = null;

    private DiscussService() {
        this(MomoKit.getApp().getSqliteInstance());
    }

    private DiscussService(SQLiteDatabase db) {
        this.db = db;
        discussDao = new DiscussDao(db);
        myDiscussDao = new MyDiscussDao(db);
        duserDao = new DiscussUserDao(db);
        userService = UserService.getInstance();
    }

    public static synchronized DiscussService getInstance() {
        if (instance != null && instance.getDb() != null && instance.getDb().isOpen()) {
            return instance;
        }
        instance = new DiscussService();
        return instance;
    }

    public static synchronized void reset() {
        instance = null;
    }

    public static void init() {
        if (MomoKit.getCurrentUser() != null) {
            DiscussService service = DiscussService.getInstance();
            service.findMyDiscuss();
        }
    }

    public Discuss findDiscussById(String disId, boolean fetchUsers) {
        Discuss d = discussDao.get(disId);
        if (d != null && fetchUsers) {
            d.members = findDiscussMembers(disId, true);
        }
        return d;
    }

    public void get(Discuss discuss, String did) {
        discussDao.get(discuss, did);
    }

    public Discuss get(String did) {
        return discussDao.get(did);
    }

    public List<Discuss> get(List<String> discussIds){
        return discussDao.listIn(DiscussDao.F_DiscussId, discussIds.toArray(), null, false);
    }

    public void saveDiscusses(List<Discuss> discusses) {
        try {
            db.beginTransaction();
            for (Discuss discuss : discusses) {
                saveDiscuss(discuss, false);
            }
            db.setTransactionSuccessful();
        } catch (Exception e) {
            log.e(e);
        } finally {
            db.endTransaction();
        }
    }

    public void saveDiscuss(Discuss discuss, boolean saveMember) {
        if (discussDao.checkExsit(discuss.id)) {
            discussDao.update(discuss);
        } else {
            discussDao.insert(discuss);
        }
        SessionUserCache.insertOrUpdateDiscussCache(discuss);
        if (saveMember) {
            try {
                db.beginTransaction();
                clearDiscussUsers(discuss.id);
                if (discuss.members != null) {
                    for (DiscussUser duser : discuss.members) {
                        duserDao.insert(duser);
                    }
                }
                db.setTransactionSuccessful();
            } catch (Exception e) {
                log.e(e);
            } finally {
                db.endTransaction();
            }
        }
    }

    public void updateChatBackground(String background, String discussid) {
        KV.saveUserValue(SPKeys.Public.PUBLIC_PREFERENCE_KEY_CHATBG_RESOURSEID + "d" + discussid, background);
//        discussDao.updateField(Discuss.Table.F_ChatBackground, background, discussid);
    }

    public void updateChatBackground(String largePic, String smallPic, String discussid) {
        KV.saveUserValue(SPKeys.Public.PUBLIC_PREFERENCE_KEY_CHATBG_RESOURSEID + "d" + discussid, largePic);
        KV.saveUserValue(SPKeys.Public.PUBLIC_PREFERENCE_KEY_CHATBG_SMALL_RESOURSEID + "d" + discussid, smallPic);
    }

    public List<Discuss> findMyDiscuss() {
        if (MemoryCache.contains(MemoryCache.DiscussMineList)) {
            return (List<Discuss>) MemoryCache.get(MemoryCache.DiscussMineList);
        }
        List<String> list = myDiscussDao.getAll();
        List<Discuss> resultList = discussDao.listIn(Discuss.Table.F_DiscussId, list.toArray(), null, false);
        MemoryCache.save(MemoryCache.DiscussMineList, resultList);
        return resultList;
    }

    public void addMyDiscuss(Discuss discuss) {
        if (!myDiscussDao.checkExsit(discuss.id)) {
            myDiscussDao.insert(discuss.id);
        }
        FullSearchHelper.getInstace().insertDiscuss(discuss);
        if (MemoryCache.contains(MemoryCache.DiscussMineList)) {
            List<Discuss> list = (List<Discuss>) MemoryCache.get(MemoryCache.DiscussMineList);
            if (!list.contains(discuss)) {
                list.add(0, discuss);
                MemoryCache.save(MemoryCache.DiscussMineList, list);
            }
        }
    }

    public void saveMyDiscuss(List<Discuss> list, boolean needDelete) {
        discussDao.beginTransaction();
        try {
            if (needDelete){
                myDiscussDao.deleteAll();
            }
            for (Discuss discuss : list) {
                saveDiscuss(discuss, false);
                myDiscussDao.insert(discuss.id);
            }
            MemoryCache.save(MemoryCache.DiscussMineList, list);
            discussDao.setTransactionSuccessful();
        } catch (Exception e) {
            log.e(e);
        } finally {
            discussDao.endTransaction();
        }
    }

    public void updateStatus(String discussId, int status) {
        discussDao.updateField(Discuss.Table.F_Status, status, discussId);
    }

    public void updateDiscussName(String discussId, String newName) {
        discussDao.updateField(Discuss.Table.F_DiscussName, newName, discussId);
    }

    public void updateMemberCount(String discussId, int count) {
        discussDao.updateField(Discuss.Table.F_MEMBER_COUNT, count, discussId);
    }

    public String getDiscussName(String discussId) {
        return discussDao.getFiled(Discuss.Table.F_DiscussName, new String[]{Discuss.Table.F_DiscussId}, new String[]{discussId});
    }

    public void updateDiscussMembers(List<DiscussUser> discussUsers, String did) {
        db.beginTransaction();

        try {
            clearDiscussUsers(did);
            for (DiscussUser duser : discussUsers) {
                duser.did = did;
                duserDao.insert(duser);
                if (duser.user != null) {
                    userService.saveUserSimple(duser.user);
                }
            }
            db.setTransactionSuccessful();
        } catch (Exception e) {
            log.e(e);
        } finally {
            db.endTransaction();
        }
    }

    /**
     * 清空成员
     */
    public void clearDiscussUsers(String id) {
        duserDao.delete(DiscussUser.DBFIELD_DISCUSSID, id);
    }

    public List<DiscussUser> findDiscussMembers(String did, final int orderType, boolean asc, boolean fetchUserProfile) {
        List<DiscussUser> discussUsers = duserDao.list(
                new String[]{DiscussUser.DBFIELD_DISCUSSID},
                new String[]{did});
        if (fetchUserProfile && discussUsers != null) {
            for (DiscussUser dUser : discussUsers) {
                dUser.user = userService.getSimpleUser(dUser.momoid);
            }
        }

        if (discussUsers == null) {
            return new ArrayList<>();
        }

        Collections.sort(discussUsers, new Comparator<DiscussUser>() {
            @Override
            public int compare(DiscussUser lhs, DiscussUser rhs) {
                if (orderType == 1) {
                    // 加入时间越新越靠前
                    long lhsJoinTime = lhs.joinTime != null ? lhs.joinTime.getTime() : 0;
                    long rhsJoinTime = rhs.joinTime != null ? rhs.joinTime.getTime() : 0;
                    if (lhsJoinTime > rhsJoinTime) {
                        return -1;
                    } else if (lhsJoinTime < rhsJoinTime) {
                        return 1;
                    }

                } else if (orderType == 2) {
                    // 距离越小越靠前
                    float lhsDistance = lhs.user != null ? lhs.user.getDistance() : -1;
                    float rhsDistance = rhs.user != null ? rhs.user.getDistance() : -1;

                    if (rhsDistance < 0) {
                        rhsDistance = Integer.MAX_VALUE;
                    }

                    if (lhsDistance < 0) {
                        lhsDistance = Integer.MAX_VALUE;
                    }

                    if (lhsDistance < rhsDistance) {
                        return -1;
                    } else if (lhsDistance > rhsDistance) {
                        return 1;
                    }

                } else if (orderType == 3) {
                    // 位置时间越新越靠前
                    long lhsJoinTime = lhs.user != null && lhs.user.getLocationTimestamp() != null ? lhs.user.getLocationTimestamp().getTime() : 0;
                    long rhsJoinTime = rhs.user != null && rhs.user.getLocationTimestamp() != null ? rhs.user.getLocationTimestamp().getTime() : 0;

                    if (lhsJoinTime > rhsJoinTime) {
                        return -1;
                    } else if (lhsJoinTime < rhsJoinTime) {
                        return 1;
                    }
                } else if (orderType == 4) {
                    // 活跃时间越新，越靠前

                    log.i("orderType: " + orderType);
                    log.i("lhs.msgTime: " + lhs.msgTime);
                    log.i("rhs.msgTime" + rhs.msgTime);
                    long lhsActiveTime = lhs.msgTime != null ? lhs.msgTime.getTime() : 0;
                    long rhsActiveTime = rhs.msgTime != null ? rhs.msgTime.getTime() : 0;

                    if (lhsActiveTime > rhsActiveTime) {
                        return -1;
                    } else if (lhsActiveTime < rhsActiveTime) {
                        return 1;
                    }
                }
                return 0;
            }
        });
        return discussUsers;
    }

    public List<DiscussUser> findDiscussMembers(String did,
                                                boolean fetchUserProfile) {
        List<DiscussUser> discussUsers = duserDao.list(
                new String[]{DiscussUser.DBFIELD_DISCUSSID},
                new String[]{did});
        if (fetchUserProfile && discussUsers != null) {
            for (DiscussUser duser : discussUsers) {
                duser.user = userService.getSimpleUser(duser.momoid);
            }
        }
        return discussUsers;
    }

    public int getMemberCount(String did) {
        List<DiscussUser> discussUsers = duserDao.list(
                new String[]{DiscussUser.DBFIELD_DISCUSSID},
                new String[]{did});
        return discussUsers == null ? 0 : discussUsers.size();
    }

    public void removeDiscussUser(String momoid, String did) {
        duserDao.delete(new String[]{DiscussUser.DBFIELD_DISCUSSID,
                DiscussUser.DBFIELD_MOMOID}, new Object[]{did, momoid});
        try {
            if (momoid.equals(AppKit.getAccountManager().getCurrentAccountUserId())) {
                deleteMyDiscuss(did);
            }
        } catch (Exception e) {
            log.e(e);
        }
    }

    public void deleteMyDiscuss(String discussId) {
        myDiscussDao.delete(discussId);
        //退出多人会话后同步删除全局搜索库
        FullSearchHelper.getInstace().deleteDiscuss(discussId);
        if (MemoryCache.contains(MemoryCache.DiscussMineList)) {
            List<Discuss> list = (List<Discuss>) MemoryCache.get(MemoryCache.DiscussMineList);
            list.remove(new Discuss(discussId));
            MemoryCache.save(MemoryCache.DiscussMineList, list);
        }
    }

    public void removeDiscussUser(List<DiscussUser> discussUsers) {
        db.beginTransaction();
        try {
            for (DiscussUser dUser : discussUsers) {
                if (!MomoKit.getCurrentUser().momoid.equals(dUser.momoid)) {
                    removeDiscussUser(dUser.momoid, dUser.did);
                }
            }
            db.setTransactionSuccessful();
        } catch (Exception e) {
            log.e(e);
        } finally {
            db.endTransaction();
        }
    }

    public void addDiscussUser(String momoid, String did, int role) {
        if (momoid.equals(MomoKit.getCurrentUser().momoid)) {
            if (!myDiscussDao.checkExsit(did)) {
                myDiscussDao.insert(did);
            }
        }
        if (!isDiscussMember(momoid, did)) {
            DiscussUser dUser = new DiscussUser();
            dUser.did = did;
            dUser.role = role;
            dUser.joinTime = new Date();
            dUser.momoid = momoid;
            duserDao.insert(dUser);
        } else {
            DiscussUser dUser = new DiscussUser();
            dUser.did = did;
            dUser.role = role;
            dUser.joinTime = new Date();
            dUser.momoid = momoid;
            duserDao.update(dUser);
        }
    }

    public boolean isDiscussMember(String momoid, String did) {
        return duserDao.count(new String[]{DiscussUser.DBFIELD_DISCUSSID,
                DiscussUser.DBFIELD_MOMOID}, new String[]{did, momoid}) > 0;
    }

    public void updateDisplayMemenbers(String did, String[] momoids) {
        discussDao.updateField(Discuss.Table.F_Members, StringUtils.join(momoids, ","), did);
    }

    public void saveDiscussAndMembers(List<Discuss> discussList, ArrayList<ArrayList<DiscussUser>> memberList) {
        db.beginTransaction();
        try {
            int count = discussList.size();
            Discuss tempDisc;
            ArrayList<DiscussUser> members;
            for (int i = 0; i < count; i++) {
                tempDisc = discussList.get(i);
                members = memberList.get(i);

                if (tempDisc == null || members == null) {
                    continue;
                }
                for (DiscussUser user : members) {
                    //搞那么复杂干啥，进入讨论组资料页时再保存user
//                    userService.saveAvatarAndName(user.momoid, user.avatar, user.name);
                    addDiscussUser(user.momoid, tempDisc.id, DiscussUser.ROLE_MEMBER);
                }
                updateDisplayMemenbers(tempDisc.id, tempDisc.displayMomoids);
            }
            db.setTransactionSuccessful();
        } catch (Exception ex) {
            log.e(ex);
        } finally {
            db.endTransaction();
        }
    }

    public void saveFullSearchGroup(List<Discuss> discusses) {
        try {
            db.beginTransaction();
            for (Discuss discuss : discusses) {
                discussDao.saveFullSearchGroup(discuss);
            }
            db.setTransactionSuccessful();
        } catch (Exception e) {
            Log4Android.getInstance().e(e);
        } finally {
            db.endTransaction();
        }
    }
}
