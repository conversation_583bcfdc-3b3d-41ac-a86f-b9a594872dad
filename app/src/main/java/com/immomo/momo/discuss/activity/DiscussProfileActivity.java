package com.immomo.momo.discuss.activity;

import static com.immomo.momo.service.bean.Message.CHATTYPE_DISCUSS;

import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.TextView;

import androidx.slidingpanelayout.widget.SlidingPaneLayout;

import com.immomo.framework.base.BaseActivity;
import com.immomo.framework.task.BaseDialogTask;
import com.immomo.framework.utils.StatusBarUtil;
import com.immomo.framework.utils.UIUtils;
import com.immomo.framework.view.esayui.NumberTextView;
import com.immomo.mmutil.log.Log4Android;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.moarch.account.AccountKit;
import com.immomo.momo.Configs;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.android.broadcast.ReflushMyDiscussListReceiver;
import com.immomo.momo.android.view.EmoteEditeText;
import com.immomo.momo.android.view.SingleLineView;
import com.immomo.momo.android.view.dialog.MAlertDialog;
import com.immomo.momo.android.view.dialog.MProcessDialog;
import com.immomo.momo.businessmodel.usermodel.IUserModel;
import com.immomo.momo.common.activity.InviteToDiscussTabsActivity;
import com.immomo.momo.discuss.bean.Discuss;
import com.immomo.momo.discuss.bean.DiscussPreference;
import com.immomo.momo.discuss.service.DiscussService;
import com.immomo.momo.discuss.view.DiscussMemberView;
import com.immomo.momo.exception.HttpException404;
import com.immomo.momo.exception.HttpException406;
import com.immomo.momo.fullsearch.activity.FullSearchMessageDetailActivity;
import com.immomo.momo.group.bean.GroupPreference;
import com.immomo.momo.imagefactory.imagewall.ImageWallActivity;
import com.immomo.momo.imagefactory.imagewall.ImageWallFragment;
import com.immomo.momo.maintab.model.AbsSession;
import com.immomo.momo.maintab.sessionlist.SessionStickyHelper;
import com.immomo.momo.message.activity.ChatBGSettingActivity;
import com.immomo.momo.message.activity.MultiChatActivity;
import com.immomo.momo.message.activity.NewChatBGSettingActivity;
import com.immomo.momo.message.receiver.ChatBackgroundReceiver;
import com.immomo.momo.mvp.common.model.ModelManager;
import com.immomo.momo.protocol.http.DiscussApi;
import com.immomo.momo.protocol.http.SessionStickyApi;
import com.immomo.momo.router.ProfileGotoOptions;
import com.immomo.momo.router.ProfileRouter;
import com.immomo.momo.router.RefreshTag;
import com.immomo.momo.service.bean.Preference;
import com.immomo.momo.service.bean.ProfileAvaterItem;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.service.sessions.SessionService;
import com.immomo.momo.service.sessions.SessionUserCache;
import com.immomo.momo.service.user.UserService;
import com.immomo.momo.util.DeviceUtils;
import com.immomo.momo.util.StringUtils;
import com.immomo.momo.util.TooLongValidator;

import java.util.ArrayList;
import java.util.List;

import info.xudshen.android.appasm.AppAsm;

/**
 * 讨论组的资料页面
 */
public class DiscussProfileActivity extends BaseActivity implements View.OnClickListener, CompoundButton.OnCheckedChangeListener {

    /**
     * 来源的tag
     ****/
    public final static String INTENT_KEY_TAG = "tag";
    public final static String INTENT_KEY_TAG_LOCAL = "local";
    /**
     * 不需要在网络上重新下载资料
     */
    public final static String INTENT_KEY_TAG_NOREFLUSH = "notreflsh";
    public final static String INTENT_KEY_DID = "did";

    private final static int REQUEST_CODE_INVITE = 100;
    private final static int REQUEST_CODE_MEMBERLIST = 101;
    private static final int REQUEST_CODE_SETBG = 102;

    public final static int MAXCOUNT_DISPLAY_MEMBER = 6;

    private boolean isNoCache = false;
    private boolean isMember = false;

    private boolean isOwner = false;
    private boolean isChangeIgnored = false;
    private String tag;
    private String did;// 讨论组ID

    private Button chatBtn; // 发起对话
    private View memberViewContainer;
    private TextView quitLayout;
    private TextView msgSettingTv;
    private TextView tv_ownerName = null;
    private TextView tv_discuss_name = null;
    private CompoundButton stickySwitch;
    /* 成员头像部分 */
    private NumberTextView tv_memberCount = null;
    private DiscussMemberView avatarContentView;

    private DiscussService dservice = null;
    private GetDiscussTask downloadDiscussTask;
    private DiscussPreference dpreference = null;
    private Discuss discuss = null;
    private ArrayList<ProfileAvaterItem> memberavatarItemlist = new ArrayList<ProfileAvaterItem>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_discussprofile);
        initInternal();
        initViews();
        initEvents();
        initData(savedInstanceState);
    }

    @Override
    protected boolean isLightTheme() {
        return !com.immomo.momo.util.MomoKit.INSTANCE.isDarkMode(this);
    }

    private void downloadProfile() {
        if (!INTENT_KEY_TAG_NOREFLUSH.equals(tag)) {
            doDownload();
        }
    }

    private void initInternal() {
        dservice = DiscussService.getInstance();
    }

    protected void initEvents() {
        findViewById(R.id.layout_title).setOnClickListener(this);
        findViewById(R.id.layout_owner).setOnClickListener(this);
        findViewById(R.id.layout_inviteothers).setOnClickListener(this);
        findViewById(R.id.layout_image_wall).setOnClickListener(this);
        findViewById(R.id.layout_setting_background).setOnClickListener(this);
        findViewById(R.id.layout_msg_setting).setOnClickListener(this);
        findViewById(R.id.layout_lookfor_msg_record).setOnClickListener(this);
        quitLayout.setOnClickListener(this);
        chatBtn.setOnClickListener(this);
        memberViewContainer.setOnClickListener(this);
        stickySwitch.setOnCheckedChangeListener(this);
        avatarContentView.setOnItemClickListener(new SingleLineView.OnMomoItemClickListener() {
            @Override
            public void onItemClick(int position, View view) {
                if (position >= 0 && position < discuss.displayMomoids.length) {
                    String momoid = discuss.displayMomoids[position];
                    AppAsm.getRouter(ProfileRouter.class).gotoProfile(thisActivity(), momoid);
                }
            }
        });
    }

    protected void initViews() {
        tv_memberCount = (NumberTextView) findViewById(R.id.tv_member_count);
        tv_discuss_name = (TextView) findViewById(R.id.tv_discuss_name);
        tv_ownerName = (TextView) findViewById(R.id.tv_ownername);
        chatBtn = (Button) findViewById(R.id.btn_goto_discuss);
        msgSettingTv = (TextView) findViewById(R.id.tv_msg_setting);
        memberViewContainer = findViewById(R.id.layout_parent_showmemberlist);
        avatarContentView = (DiscussMemberView) findViewById(R.id.nearby_group_member_content);
        avatarContentView.setMinPading(UIUtils.getPixels(2));
        stickySwitch = (CompoundButton) findViewById(R.id.setting_switch_sticky);
        quitLayout = (TextView) findViewById(R.id.layout_quit);

        View root_layout = findViewById(R.id.root_layout);
        if (DeviceUtils.hasSmartBar()) {
            SlidingPaneLayout.LayoutParams layoutParams = (SlidingPaneLayout.LayoutParams) root_layout.getLayoutParams();
            layoutParams.setMargins(0, 0, 0, StatusBarUtil.getNavigationheight(thisActivity()));
            root_layout.setLayoutParams(layoutParams);
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        String dispargm = intent.getExtras() != null ? (String) intent.getExtras().get(INTENT_KEY_DID) : null;
        if (!StringUtils.isEmpty(dispargm) && !did.equals(dispargm)) {
            this.did = dispargm;
            initDiscussProfile();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        updateNoticeSetting();
    }

    private void doDismissDiscuss() {
        MAlertDialog dialog = MAlertDialog.makeConfirm(this, getString(R.string.dprofile_setting_dismiss_tip), new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                MomoTaskExecutor.executeUserTask(getTaskTag(), new DismissTask(DiscussProfileActivity.this));
            }
        });
        dialog.setSupportDark(true);
        showDialog(dialog);
    }

    private void showEditnameDialog() {
        View view = MomoKit.getLayoutInflater().inflate(R.layout.dialog_contactpeople_apply, null);
        final EmoteEditeText editor = (EmoteEditeText) view.findViewById(R.id.edittext_reason);
        if (discuss != null && !StringUtils.isEmpty(discuss.name))
            editor.setText(discuss.name);
        editor.addTextChangedListener(new TooLongValidator(Configs.NAME_LENGTH, editor));
        editor.setHint(R.string.dprofile_editname_hint);
        editor.setSelection(editor.getText().toString().length());
        MAlertDialog mDialog = new MAlertDialog(DiscussProfileActivity.this);
        mDialog.setTitle("修改名称");
        mDialog.setContentView(view);
        mDialog.setButton(MAlertDialog.INDEX_RIGHT, getString(R.string.dialog_btn_confim), new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (StringUtils.isEmpty(editor.getText().toString().trim())) {
                    Toaster.show("名称不能为空");
                } else {
                    MomoTaskExecutor.executeUserTask(getTaskTag(), new EditDiscussTask(DiscussProfileActivity.this, editor.getText().toString().trim()));
                    dialog.dismiss();
                }
            }
        });
        mDialog.setButton(MAlertDialog.INDEX_LEFT, getString(R.string.dialog_btn_cancel), mDialog.defaultButtonOnclick);
        mDialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
        mDialog.show();
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        if (isChangeIgnored) {
            isChangeIgnored = false;
            return;
        }
        if (isChecked) {
            Log4Android.getInstance().i("jarek switch打开了");
            stickySwitch.postDelayed(new Runnable() {
                @Override
                public void run() {
                    MomoTaskExecutor.executeTask(MomoTaskExecutor.EXECUTOR_TYPE_USER, getStickyTag(), new StickySetTask(did, true));
                }
            }, 200);
        } else {
            Log4Android.getInstance().i("jarek switch关闭了");
            stickySwitch.postDelayed(new Runnable() {
                @Override
                public void run() {
                    MomoTaskExecutor.executeTask(MomoTaskExecutor.EXECUTOR_TYPE_USER, getStickyTag(), new StickySetTask(did, false));
                }
            }, 200);
        }
    }

    private void doQuitDiscuss() {
        MAlertDialog dialog = MAlertDialog.makeConfirm(this, getString(R.string.dprofile_setting_quit_tip), new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                MomoTaskExecutor.executeUserTask(getTaskTag(), new QuitTask(DiscussProfileActivity.this));
            }
        });
        dialog.setSupportDark(true);
        showDialog(dialog);
    }

    protected void initData(Bundle savedInstanceState) {
        if (savedInstanceState != null && savedInstanceState.getBoolean("from_saveinstance", false)) {
            did = (String) savedInstanceState.get(INTENT_KEY_DID);
            tag = (String) savedInstanceState.get(INTENT_KEY_TAG);
            tag = tag == null ? INTENT_KEY_TAG_LOCAL : tag;
        } else {
            Intent intent = getIntent();
            tag = intent.getStringExtra(INTENT_KEY_TAG);
            did = intent.getStringExtra(INTENT_KEY_DID);
        }
        initDiscussProfile();
        downloadProfile();
        initOwnerInfo();
        initSwitchData();
    }

    private void initOwnerInfo() {
        RefreshOwnerTask refreshOwnerTask = new RefreshOwnerTask(new String[]{discuss.owerMomoid});
        MomoTaskExecutor.executeUserTask(getTaskTag(), refreshOwnerTask);
    }

    private void initDiscussProfile() {
        if (StringUtils.isEmpty(did)) {
            return;
        }
        Preference p = MomoKit.getCurrentPreference();
        dpreference = p.getDiscussPreference(did);
        discuss = SessionUserCache.getDiscuss(did);
        if (discuss != null) {
            isNoCache = false;
            initMemberAvatarViews();
            refreshBasicTextInfo();
        } else {
            isNoCache = true;
            discuss = new Discuss(did);
            setDiscussName(discuss.id);
        }
    }

    private void setStickySwitch(boolean isSticky) {
        if (isSticky == stickySwitch.isChecked()) {
            isChangeIgnored = false;
        } else {
            isChangeIgnored = true;
            stickySwitch.setChecked(isSticky);
        }
    }

    private void initSwitchData() {
        setStickySwitch(SessionStickyHelper.getInstance().isSessionSticky(did, SessionStickyHelper.ChatType.TYPE_DISCUSS));
    }

    private void initMemberAvatarViews() {
        initMemberAvatarItemList();
        addActiveMember(memberavatarItemlist);
    }

    private void initMemberAvatarItemList() {
        if (discuss != null && discuss.displayMomoids != null && discuss.displayMomoids.length > 0) {
            UserService service = UserService.getInstance();
            int length = discuss.displayMomoids.length > MAXCOUNT_DISPLAY_MEMBER ? MAXCOUNT_DISPLAY_MEMBER : discuss.displayMomoids.length;
            memberavatarItemlist.clear();
            for (int i = 0; i < length; i++) {
                ProfileAvaterItem item = new ProfileAvaterItem();
                String momoid = discuss.displayMomoids[i];
                String avatar = service.getAvator(momoid);
                item.guid = avatar;
                memberavatarItemlist.add(item);
            }
        }
    }

    private void refreshBasicTextInfo() {
        if (discuss == null) {
            return;
        }
        if (!StringUtils.isEmpty(discuss.name)) {
            setDiscussName(discuss.name);
        } else {
            setDiscussName(discuss.id);
        }

        tv_memberCount.setTextNumber("成员列表", UIUtils.getColor(R.color.black),
                                     discuss.member_count + "/" + discuss.member_max);

        refreshButtons();

        updateNoticeSetting();

        if (isOwner) {
            quitLayout.setText("解散多人对话");
        } else {
            quitLayout.setText("退出多人对话");
        }
    }

    public void refreshOwnName(String name) {
        tv_ownerName.setText(name);
    }

    private void updateNoticeSetting() {
        if (dpreference == null) {
            msgSettingTv.setText("");
        } else if (dpreference.getNotificationModel() == GroupPreference.NOTIFICATION_OPEN) {
            msgSettingTv.setText("开启");
        } else if (dpreference.getNotificationModel() == GroupPreference.NOTIFICATION_CLOSE) {
            msgSettingTv.setText("屏蔽消息");
        } else if (dpreference.getNotificationModel() == GroupPreference.NOTIFICATION_MUTE) {
            msgSettingTv.setText("接收消息但不提醒");
        }
    }

    private void refreshButtons() {
        if (discuss == null)
            return;
        String curId = AccountKit.getAccountManager().getCurrentAccountUserId();
        if (curId.equals(discuss.owerMomoid) || isMember) {
            chatBtn.setVisibility(View.VISIBLE);
        } else {
            chatBtn.setVisibility(View.GONE);
        }
    }

    private void addActiveMember(final List<ProfileAvaterItem> simpleMembers) {
        if (simpleMembers == null || simpleMembers.isEmpty()) {
            avatarContentView.setVisibility(View.GONE);
            return;
        }

        avatarContentView.setItemViewWidth(45);
        avatarContentView.post(new Runnable() {
            @Override
            public void run() {
                avatarContentView.setData(simpleMembers, true);
            }
        });
        avatarContentView.setVisibility(View.VISIBLE);
    }

    private void refreshDiscussProfile() {
        if (discuss == null)
            return;
        refreshBasicTextInfo();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.layout_owner:
                ProfileGotoOptions options = new ProfileGotoOptions(discuss.owerMomoid);
                options.setRequestTypeTag(RefreshTag.INTERNET);
                AppAsm.getRouter(ProfileRouter.class).gotoProfile(thisActivity(), options);
                break;
            case R.id.layout_inviteothers:
                if (!StringUtils.isEmpty(did)) {
                    Intent it = new Intent(this, InviteToDiscussTabsActivity.class);
                    it.putExtra(InviteToDiscussTabsActivity.KEY_DID, did);
                    startActivityForResult(it, REQUEST_CODE_INVITE);
                }
                break;
            case R.id.btn_goto_discuss:
                if (!StringUtils.isEmpty(did)) {
                    Intent it = new Intent(DiscussProfileActivity.this, MultiChatActivity.class);
                    it.putExtra(MultiChatActivity.REMOTE_DISCUSS_ID, did);
                    startActivity(it);
                }
                break;
            case R.id.layout_parent_showmemberlist:
                showMemberList();
                break;
            case R.id.layout_msg_setting:
                Intent it = new Intent(thisActivity(), DiscussNotificationSettingActivity.class);
                it.putExtra(DiscussNotificationSettingActivity.KEY_DISCUSSID, did);
                startActivity(it);
                break;
            case R.id.layout_setting_background:
                NewChatBGSettingActivity.startActivity(thisActivity(), discuss.id, CHATTYPE_DISCUSS);
                break;
            case R.id.layout_quit:
                if (isOwner) {
                    doDismissDiscuss();
                } else {
                    doQuitDiscuss();
                }
                break;
            case R.id.layout_lookfor_msg_record:
                Intent it1 = new Intent(thisActivity(), FullSearchMessageDetailActivity.class);
                it1.putExtra(FullSearchMessageDetailActivity.KEY_SEARCH_XID, did);
                it1.putExtra(FullSearchMessageDetailActivity.KEY_SEARCH_CHAT_TYPE, CHATTYPE_DISCUSS);
                startActivity(it1);
                overridePendingTransition(R.anim.anim_slide_in_from_right, R.anim.anim_fade_out);
                break;
            case R.id.layout_title:
                if (isOwner) {
                    showEditnameDialog();
                }
                break;
            case R.id.layout_image_wall:
                ImageWallActivity.startImageWall(this, did, ImageWallFragment.TYPE_DCHAT);
                break;
            default:
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent arg2) {
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case REQUEST_CODE_INVITE:
                    doDownload();
                    break;
                case REQUEST_CODE_MEMBERLIST:
                    initDiscussProfile();
                    break;
                case REQUEST_CODE_SETBG:
                    String resId = arg2.getStringExtra(ChatBGSettingActivity.KEY_RESOURSEID);
                    String smallResId = arg2.getStringExtra(ChatBGSettingActivity.KEY_SMALL_RESOURSEID);
                    discuss.chatBackgroup = resId;
                    dservice.updateChatBackground(resId, smallResId, did);
                    Intent chatBgIntent = new Intent(ChatBackgroundReceiver.ACTION);
                    chatBgIntent.putExtra(ChatBackgroundReceiver.KEY_RESOURSEID, resId);
                    sendBroadcast(chatBgIntent);
                    return;
                case ImageWallActivity.REQUEST_CODE:
                    if (arg2 != null) {
                        if (arg2.getBooleanExtra(ImageWallActivity.EXTRA_NEED_FINISH, false))
                            finish();
                    }
                    break;
                default:
                    break;
            }
        }
        super.onActivityResult(requestCode, resultCode, arg2);
    }

    @Override
    protected void onDestroy() {
        if (downloadDiscussTask != null && !downloadDiscussTask.isCancelled()) {
            downloadDiscussTask.cancel(true);
        }
        super.onDestroy();

        MomoTaskExecutor.cancleAllTasksByTag(getTaskTag());
    }

    private void showMemberList() {
        Intent intent = new Intent(DiscussProfileActivity.this, DiscussMemberListActivity.class);
        intent.putExtra(DiscussMemberListActivity.INTENT_KEY_DID, discuss == null ? did : discuss.id);
        intent.putExtra(DiscussMemberListActivity.INTENT_KEY_COUNT, discuss == null ? 0 : discuss.member_count);
        startActivityForResult(intent, REQUEST_CODE_MEMBERLIST);
    }

    private void doDownload() {
        if (downloadDiscussTask != null && !downloadDiscussTask.isCancelled()) {
            downloadDiscussTask.cancel(true);
        }
        downloadDiscussTask = new GetDiscussTask();
        MomoTaskExecutor.executeUserTask(getTaskTag(), downloadDiscussTask);
    }

    private void setDiscussName(String name) {
        setTitle("多人会话");
        tv_discuss_name.setText(name);
    }

    private int getStickyTag() {
        return this.hashCode() + 11;
    }

    private class GetDiscussTask extends MomoTaskExecutor.Task<String, Object, Integer> {

        public GetDiscussTask() {
        }

        @Override
        protected void onPreTask() {
            if (isNoCache) {
                showDialog(new MProcessDialog(thisActivity()));
            }
        }

        @Override
        protected void onCancelled() {
            super.onCancelled();
            finish();
        }

        @Override
        protected Integer executeTask(String... params) throws Exception {
            int result = DiscussApi.getInstance().downloadDiscussProfile(did, discuss);
            String currentId = AccountKit.getAccountManager().getCurrentAccountUserId();
            isMember = dservice.isDiscussMember(currentId, did);
            isOwner = currentId.equals(discuss.owerMomoid);
            return result;
        }

        @Override
        protected void onTaskFinish() {
            if (isNoCache) {
                closeDialog();
            }
        }

        @Override
        protected void onTaskError(Exception e) {
            if (e instanceof HttpException404) {
                dservice.updateStatus(did, Discuss.STATUS_DISMISS);
                Toaster.show(e.getMessage());
                finish();
                return;
            }
            if (e instanceof HttpException406) {
                Toaster.show(e.getMessage());
                dservice.updateStatus(did, Discuss.STATUS_BANDED);
                finish();
                return;
            }
            super.onTaskError(e);
        }

        @Override
        protected void onTaskSuccess(Integer result) {
            initMemberAvatarViews();
            refreshDiscussProfile();
            Intent it = new Intent(ReflushMyDiscussListReceiver.ACTION_REFLUSH_PROFILE);
            it.putExtra(ReflushMyDiscussListReceiver.KEY_DID, did);
            sendBroadcast(it);
        }
    }

    private class DismissTask extends BaseDialogTask<Object, Object, String> {
        public DismissTask(Activity context) {
            super(context);
        }

        @Override
        protected String executeTask(Object... params) throws Exception {
            String result = DiscussApi.getInstance().dismissDiscuss(did);
            dservice.updateStatus(did, Discuss.STATUS_DISMISS);
            dservice.clearDiscussUsers(did);
            dservice.removeDiscussUser(AccountKit.getAccountManager().getCurrentAccountUserId(), did);
            SessionStickyHelper.getInstance().removeSessionSticky(did, SessionStickyHelper.ChatType.TYPE_DISCUSS);
            return result;
        }

        @Override
        protected void onTaskSuccess(String result) {
            Toaster.show(result);
            Intent intent = new Intent();
            intent.putExtra(ReflushMyDiscussListReceiver.KEY_DID, did);
            intent.setAction(ReflushMyDiscussListReceiver.ACTION_DELETE);
            sendBroadcast(intent);
            finish();
        }
    }

    private class QuitTask extends BaseDialogTask<Object, Object, String> {

        public QuitTask(Activity context) {
            super(context);
        }

        @Override
        protected String executeTask(Object... params) throws Exception {
            String result = DiscussApi.getInstance().quitDiscuss(did);
            dservice.removeDiscussUser(AccountKit.getAccountManager().getCurrentAccountUserId(), did);

            // 主动退群删除消息列表.
            SessionService.getInstance().delete(did, AbsSession.TYPE_DISCUSS, true);
            SessionStickyHelper.getInstance().removeSessionSticky(did, SessionStickyHelper.ChatType.TYPE_DISCUSS);
            return result;
        }

        @Override
        protected void onTaskSuccess(String result) {
            Toaster.show(result);
            Intent intent = new Intent();
            intent.putExtra(ReflushMyDiscussListReceiver.KEY_DID, did);
            intent.setAction(ReflushMyDiscussListReceiver.ACTION_DELETE);
            sendBroadcast(intent);
            finish();
        }
    }

    private class EditDiscussTask extends BaseDialogTask<Object, Object, String> {
        private String name = null;

        public EditDiscussTask(Activity context, String name) {
            super(context);
            this.name = name;
        }

        @Override
        protected String executeTask(Object... params) throws Exception {
            String result = DiscussApi.getInstance().editDiscuss(did, name);
            discuss.name = name;
            dservice.saveDiscuss(discuss, false);
            return result;
        }

        @Override
        protected void onTaskSuccess(String result) {
            Toaster.show(result);
            setDiscussName(discuss.name);
            Intent intent = new Intent();
            intent.setAction(ReflushMyDiscussListReceiver.ACTION_REFLUSH_PROFILE);
            intent.putExtra(ReflushMyDiscussListReceiver.KEY_DID, did);
            sendBroadcast(intent);
        }
    }

    private class StickySetTask extends MomoTaskExecutor.Task<Object, Object, Long> {
        private String momoId;
        private boolean isSticky;
        private boolean showDialog;
        private MProcessDialog mProcessDialog;

        @Override
        protected void onPreTask() {
            super.onPreTask();
            if (showDialog) {
                mProcessDialog = new MProcessDialog(DiscussProfileActivity.this);
                mProcessDialog.setCancelable(false);
                showDialog(mProcessDialog);
            }
        }

        public StickySetTask(String momoId, boolean isSticky) {
            this.momoId = momoId;
            this.isSticky = isSticky;
            this.showDialog = true;
        }

        public StickySetTask(String momoId, boolean isSticky, boolean showDialog) {
            this.momoId = momoId;
            this.isSticky = isSticky;
            this.showDialog = showDialog;
        }

        @Override
        protected void onTaskFinish() {
            super.onTaskFinish();
            if (mProcessDialog != null) {
                mProcessDialog.dismiss();
            }
        }

        @Override
        protected Long executeTask(Object... params) throws Exception {
            return SessionStickyApi.getInstance().setDiscussChatSticky(momoId, isSticky);
        }

        @Override
        protected void onTaskError(Exception e) {
            super.onTaskError(e);
            Toaster.show(e.getMessage());
            setStickySwitch(!isSticky);
        }

        @Override
        protected void onTaskSuccess(Long setTime) {
            Log4Android.getInstance().i("jarek setTime:" + setTime + " Diff:" + (System.currentTimeMillis() - setTime));
            if (isSticky) {
                SessionStickyHelper.getInstance().makeSessionSticky(momoId, SessionStickyHelper.ChatType.TYPE_DISCUSS, setTime);
            } else {
                SessionStickyHelper.getInstance().removeSessionSticky(momoId, SessionStickyHelper.ChatType.TYPE_DISCUSS);
            }
        }
    }

    private class RefreshOwnerTask extends MomoTaskExecutor.Task<String, Object, String> {

        String ownerMomoid;

        public RefreshOwnerTask(String... strings) {
            super(strings);
            if (strings.length > 0) {
                ownerMomoid = strings[0];
            }
        }

        @Override
        protected String executeTask(String... params) throws Exception {
            String name = "";
            if (StringUtils.notEmpty(ownerMomoid)) {
                User user = ModelManager.getModel(IUserModel.class).getUserFromDB(ownerMomoid);
                if (user != null) {
                    name = user.getDisplayName();
                } else {
                    name = ownerMomoid;
                }
            }

            return name;
        }

        @Override
        protected void onTaskSuccess(String s) {
            super.onTaskSuccess(s);

            refreshOwnName(s);
        }

        @Override
        protected void onTaskError(Exception e) {
            if (StringUtils.notEmpty(ownerMomoid)) {
                refreshOwnName(ownerMomoid);
            }
        }
    }
}