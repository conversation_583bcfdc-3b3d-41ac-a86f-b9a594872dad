package com.immomo.momo.discuss.itemmodel;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.immomo.android.router.momo.bean.IUser;
import com.immomo.framework.cement.CementAdapter;
import com.immomo.framework.cement.CementModel;
import com.immomo.framework.cement.CementViewHolder;
import com.immomo.framework.imageloader.ImageLoaderUtil;
import com.immomo.framework.imageloader.ImageLoaderX;
import com.immomo.framework.imageloader.ImageType;
import com.immomo.framework.utils.UIUtils;
import com.immomo.mmutil.StringUtils;
import com.immomo.momo.R;
import com.immomo.momo.android.view.BadgeView;
import com.immomo.momo.android.view.EmoteTextView;
import com.immomo.momo.discuss.bean.DiscussUser;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.util.AgeLabelABTest;

/**
 * Created by x<PERSON><PERSON> on 21/06/2017.
 */
public class DiscussUserItemModel extends
        CementModel<DiscussUserItemModel.ViewHolder> {
    @NonNull
    private final DiscussUser discussUser;
    private boolean isOwner;

    public DiscussUserItemModel(@NonNull DiscussUser discussUser, boolean isOwner) {
        this.discussUser = discussUser;
        this.isOwner = isOwner;

        if (discussUser.user != null) {
            id(discussUser.user.momoid);
        }
    }

    @NonNull
    public DiscussUser getDiscussUser() {
        return discussUser;
    }

    @Override
    public boolean isContentTheSame(@NonNull CementModel<?> item) {
        return false;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.listitem_groupuser;
    }

    @Override
    public void bindData(@NonNull ViewHolder holder) {
        User user = discussUser.user;
        if (user == null) return;

        ImageLoaderUtil
                .loadRoundImageWithReset(user.getLoadImageId(),
                        ImageType.IMAGE_TYPE_ALBUM_250x250, holder.faceView, UIUtils.getPixels(6), true, 0);
        holder.nameView.setText(user.getDisplayName());
        if (user.isMomoVip()) {
            holder.nameView.setTextColor(UIUtils.getColor(R.color.font_vip_name));
        } else {
            holder.nameView.setTextColor(UIUtils.getColor(R.color.color_text_3b3b3b));
        }

        holder.distanceView.setText(user.distanceString);
        holder.distanceView.setVisibility(user.showDistance() || !user.showDistance() && !user.showTime() ? View.VISIBLE:View.GONE);
        holder.timeDriver.setVisibility(user.showTime() && user.showDistance() ? View.VISIBLE:View.GONE);
        holder.timeView.setText(user.agoTime);
        holder.timeView.setVisibility(user.showTime()?View.VISIBLE:View.GONE);

        if (UIUtils.getString(R.string.profile_distance_hide).equals(user.distanceString)
                && UIUtils.getString(R.string.profile_distance_unknown).equals(user.agoTime)) {
            holder.agoTimeContainer.setVisibility(View.GONE);
        } else {
            holder.agoTimeContainer.setVisibility(View.VISIBLE);
        }
        holder.ageView.setText(String.valueOf(user.age));
        updateGenderTextState(user, holder);

        if ("F".equals(user.sex)) {
            holder.genderBackgroudView.setBackgroundResource(R.drawable.bg_gender_female_dark);
            holder.genderView.setImageResource(R.drawable.ic_user_famale);
        } else {
            holder.genderBackgroudView.setBackgroundResource(R.drawable.bg_gender_male);
            holder.genderView.setImageResource(R.drawable.ic_user_male);
        }

        holder.signatureView.setText(user.getSignexEmoteContent());
        if (!StringUtils.isEmpty(user.signexIcon)) {
            ImageLoaderX.loadWithReset(user.signexIcon).type(ImageType.IMAGE_TYPE_URL).showDefault().into(holder.signextIconView);
        } else {
            holder.signextIconView.setVisibility(View.GONE);
        }

        holder.badgeView.setUser(user);
        holder.badgeView.updateGenderTextState(user);
        holder.triangleZone.setVisibility(isOwner ? View.VISIBLE : View.GONE);
    }

    private void updateGenderTextState(IUser user, ViewHolder holder) {
        if (user == null) {
            return;
        }
        if (AgeLabelABTest.INSTANCE.isTestA() && "F".equalsIgnoreCase(user.getSex())) {
            holder.ageView.setVisibility(View.GONE);
        } else {
            holder.ageView.setVisibility(View.VISIBLE);
        }
    }

    @NonNull
    @Override
    public CementAdapter.IViewHolderCreator<ViewHolder> getViewHolderCreator() {
        return ViewHolder::new;
    }

    public static class ViewHolder extends CementViewHolder {
        private ImageView faceView;
        private TextView nameView;

        private TextView distanceView;
        private View timeDriver;
        private TextView timeView;

        private View agoTimeContainer;
        private TextView ageView;

        private ImageView genderView;
        private View genderBackgroudView;

        private EmoteTextView signatureView;
        private ImageView signextIconView;

        private BadgeView badgeView;
        public View triangleZone; //右下角三角

        public ViewHolder(View itemView) {
            super(itemView);
            itemView.setClickable(true);

            faceView = (ImageView) itemView.findViewById(R.id.userlist_item_iv_face);
            nameView = (TextView) itemView.findViewById(R.id.userlist_item_tv_name);

            distanceView = (TextView) itemView.findViewById(R.id.userlist_item_tv_distance);
            timeDriver = itemView.findViewById(R.id.userlist_tv_timedriver);
            timeView = (TextView) itemView.findViewById(R.id.profile_tv_time);

            agoTimeContainer = itemView.findViewById(R.id.layout_time_container);
            ageView = (TextView) itemView.findViewById(R.id.userlist_item_tv_age);

            signatureView = (EmoteTextView) itemView.findViewById(R.id.userlist_item_tv_sign);
            signextIconView = (ImageView) itemView.findViewById(R.id.userlist_item_pic_sign);

            genderView = (ImageView) itemView.findViewById(R.id.userlist_item_iv_gender);
            genderBackgroudView = itemView.findViewById(R.id.userlist_item_layout_genderbackgroud);

            badgeView = (BadgeView) itemView.findViewById(R.id.userlist_bage);
            triangleZone = itemView.findViewById(R.id.triangle_zone);
        }
    }
}
