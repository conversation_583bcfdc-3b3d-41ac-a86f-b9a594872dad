package com.immomo.momo.discuss.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.immomo.framework.base.BaseActivity;
import com.immomo.framework.base.BaseReceiver;
import com.immomo.framework.cement.CementAdapter;
import com.immomo.framework.cement.CementModel;
import com.immomo.framework.cement.CementViewHolder;
import com.immomo.framework.cement.SimpleCementAdapter;
import com.immomo.framework.cement.eventhook.OnClickEventHook;
import com.immomo.framework.utils.UIUtils;
import com.immomo.framework.view.recyclerview.LoadMoreRecyclerView;
import com.immomo.momo.R;
import com.immomo.momo.android.broadcast.ReflushDiscussMemberListReceiver;
import com.immomo.momo.android.view.dialog.MAlertDialog;
import com.immomo.momo.android.view.dialog.MAlertListDialog;
import com.immomo.momo.android.view.dialog.OnItemSelectedListener;
import com.immomo.momo.android.view.dialog.SimpleMenuSmartBox;
import com.immomo.momo.discuss.bean.DiscussUser;
import com.immomo.momo.discuss.itemmodel.DiscussUserItemModel;
import com.immomo.momo.discuss.presenter.DiscussMemberListPresenter;
import com.immomo.momo.discuss.presenter.IDiscussMemberListPresenter;
import com.immomo.momo.router.ProfileGotoOptions;
import com.immomo.momo.router.ProfileRouter;
import com.immomo.momo.router.RefreshTag;
import com.immomo.momo.util.MomoKit;
import com.immomo.momo.util.StringUtils;

import info.xudshen.android.appasm.AppAsm;

;

/**
 * Created by xudshen on 21/06/2017.
 */

public class DiscussMemberListActivity extends BaseActivity implements IDiscussMemberListView {
    public final static String INTENT_KEY_DID = "did";
    /**
     * 当前群成员数量
     **/
    public final static String INTENT_KEY_COUNT = "count";

    @NonNull
    private String did;
    private SwipeRefreshLayout swipeRefreshLayout;
    private LoadMoreRecyclerView rv;

    @Nullable
    private IDiscussMemberListPresenter presenter;
    @Nullable
    private ReflushDiscussMemberListReceiver receiver;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_discussmemberlist);
        did = getIntent().getStringExtra(INTENT_KEY_DID);
        if (StringUtils.isBlank(did)) finish();

        initViews();
        initPresenter();
        initEvents();
        initReceiver();
    }


    @Override
    protected boolean isLightTheme() {
        return !MomoKit.INSTANCE.isDarkMode(this);
    }

    private void showSortDialog() {
        if (presenter == null) return;

        MAlertListDialog mdialog = new MAlertListDialog(this,
                getResources().getStringArray(R.array.order_groupmember_list));
        mdialog.setTitle(R.string.header_order);
        mdialog.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(final int index) {
                if (presenter.getCurrentSortType() != index + 1) {
                    presenter.requestRefresh(index + 1);
                }
            }
        });
        mdialog.setSupportDark(true);
        mdialog.show();
    }

    private void initViews() {
        swipeRefreshLayout = (SwipeRefreshLayout) findViewById(R.id.ptr_swipe_refresh_layout);
        swipeRefreshLayout.setColorSchemeResources(R.color.colorAccent);
        swipeRefreshLayout.setProgressViewEndTarget(true, UIUtils.getPixels(64));

        rv = (LoadMoreRecyclerView) findViewById(R.id.rv);
        rv.setLayoutManager(new LinearLayoutManager(this));
        DefaultItemAnimator defaultItemAnimator = new DefaultItemAnimator();
        defaultItemAnimator.setSupportsChangeAnimations(false);
        rv.setItemAnimator(defaultItemAnimator);
        refreshTitle(getIntent().getIntExtra(INTENT_KEY_COUNT, 0));

        addRightMenu(getResources().getString(R.string.header_order), R.drawable.icon_sort_black, item -> {
            showSortDialog();
            return false;
        });
    }

    private void initPresenter() {
        presenter = new DiscussMemberListPresenter(did);
        presenter.bindView(this);
        presenter.init();
    }

    private void initEvents() {
        swipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                if (presenter != null) {
                    presenter.requestRefresh();
                }
            }
        });
    }

    private void initReceiver() {
        receiver = new ReflushDiscussMemberListReceiver(this);
        receiver.setReceiveListener(new BaseReceiver.IBroadcastReceiveListener() {
            @Override
            public void onReceive(Intent intent) {
                if (ReflushDiscussMemberListReceiver.ACTION_DELETE.equals(intent.getAction())) {
                    if (presenter != null) {
                        presenter.requestRefresh(presenter.getCurrentSortType());
                    }
                }
            }
        });
    }

    private void unregisterReceiver() {
        if (receiver != null) {
            unregisterReceiver(receiver);
            receiver = null;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        presenter.resume();
    }

    @Override
    protected void onPause() {
        presenter.pause();
        super.onPause();
    }


    @Override
    protected void onDestroy() {
        unregisterReceiver();
        if (presenter != null) {
            presenter.destroy();
            presenter = null;
        }
        super.onDestroy();
    }


    @Override
    public void refreshTitle(int memberCount) {
        String str = UIUtils.getString(R.string.groupmember_list_header_title);
        setTitle(String.format(str, memberCount));
    }

    @Override
    public void setAdapter(SimpleCementAdapter adapter) {
        adapter.setOnItemClickListener(new CementAdapter.OnItemClickListener() {
            @Override
            public void onClick(@NonNull View itemView, @NonNull CementViewHolder holder,
                                int position, @NonNull CementModel<?> model) {
                if (!DiscussUserItemModel.class.isInstance(model)) return;

                DiscussUser user = ((DiscussUserItemModel) model).getDiscussUser();
                ProfileGotoOptions options = new ProfileGotoOptions(user.momoid);
                options.setRequestTypeTag(RefreshTag.LOCAL);
                AppAsm.getRouter(ProfileRouter.class).gotoProfile(thisActivity(), options);
            }
        });
        adapter.addEventHook(new OnClickEventHook<DiscussUserItemModel.ViewHolder>(
                DiscussUserItemModel.ViewHolder.class) {
            @Override
            public void onClick(@NonNull View view, @NonNull DiscussUserItemModel.ViewHolder holder,
                                int position, @NonNull CementModel model) {
                if (!DiscussUserItemModel.class.isInstance(model)) return;
                final String momoid = ((DiscussUserItemModel) model).getDiscussUser().user.momoid;

                String[] textArrs = getResources().getStringArray(R.array.discussmemberlist_remove_items);
                SimpleMenuSmartBox menuSmartBox = new SimpleMenuSmartBox(thisContext(), holder.triangleZone, textArrs);
                menuSmartBox.setOnItemClickListener(new AdapterView.OnItemClickListener() {
                    @Override
                    public void onItemClick(AdapterView<?> parent, View view, int index, long id) {
                        switch (index) {
                            case 0: {
                                MAlertDialog dialog = MAlertDialog.makeConfirm(thisContext(),
                                        getResources().getString(R.string.dmemberlist_remove_tip),
                                        (dialog1, which) -> {
                                            //remove
                                            if (presenter != null) {
                                                presenter.deleteUser(momoid);
                                            }
                                        });
                                dialog.setSupportDark(true);
                                dialog.show();
                            }
                            break;
                        }
                    }
                });
                menuSmartBox.show();
            }

            @Nullable
            @Override
            public View onBind(@NonNull DiscussUserItemModel.ViewHolder holder) {
                return holder.triangleZone;
            }
        });
        rv.setAdapter(adapter);
    }

    @Override
    public void showRefreshStart() {
        swipeRefreshLayout.setRefreshing(true);
    }

    @Override
    public void showRefreshComplete() {
        swipeRefreshLayout.setRefreshing(false);
        rv.scrollToPosition(0);
    }

    @Override
    public void showRefreshFailed() {
        swipeRefreshLayout.setRefreshing(false);
        rv.scrollToPosition(0);
    }

    @Override
    public void showEmptyView() {

    }

    @Override
    public void tryEndInflateInChain(@Nullable String traceId) {
        if (rv != null) {
            rv.tryEndInflateInChain(traceId);
        }
    }

    @Override
    public Context thisContext() {
        return this;
    }
}
