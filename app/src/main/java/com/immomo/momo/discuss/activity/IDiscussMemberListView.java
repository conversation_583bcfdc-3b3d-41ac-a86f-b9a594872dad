package com.immomo.momo.discuss.activity;

import androidx.annotation.Nullable;

import com.immomo.framework.cement.SimpleCementAdapter;
import com.immomo.momo.mvp.common.RecyclerViewContract;

/**
 * Created by xudshen on 21/06/2017.
 */

public interface IDiscussMemberListView extends RecyclerViewContract.IView<SimpleCementAdapter> {
    void refreshTitle(int memberCount);

    void tryEndInflateInChain(@Nullable String traceId);
}
