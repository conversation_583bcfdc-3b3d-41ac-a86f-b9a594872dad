package com.immomo.momo.discuss.view;

import android.content.Context;
import android.util.AttributeSet;
import android.util.SparseBooleanArray;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.ScaleAnimation;
import android.widget.ImageView;

import com.immomo.framework.imageloader.ImageLoaderUtil;
import com.immomo.framework.imageloader.ImageType;
import com.immomo.momo.R;
import com.immomo.momo.android.view.SingleLineView;
import com.immomo.momo.service.bean.ProfileAvaterItem;

/**
 * Created by joel on 16/5/9.
 * <p/>
 * Momo Tech 2011-2016 © All Rights Reserved.
 */
public class DiscussMemberView extends SingleLineView<ProfileAvaterItem> {
    public DiscussMemberView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public View getView(int position) {
        View convertView = inflate(getContext(), R.layout.profile_discuss_member, null);
        if (position < getItemCount()) {
            ProfileAvaterItem member = getItem(position);
            refreshView(member, convertView, position);
        } else {
            convertView.setVisibility(View.GONE);
        }
        return convertView;
    }

    @Override
    public void updateView(int position) {
        if (position < getItemCount()) {
            View convertView = getChildAt(position);
            ProfileAvaterItem member = getItem(position);
            if (member != null) {
                refreshView(member, convertView, position);
            }
        } else {
            View convertView = getChildAt(position);
            convertView.setVisibility(View.GONE);
        }
    }

    private SparseBooleanArray animPosArray = new SparseBooleanArray();


    private void refreshView(ProfileAvaterItem member, View convertView, int position) {
        ImageView avatarIv = (ImageView) convertView.findViewById(R.id.avatar_imageview);
        ImageLoaderUtil.loadImage(member.guid, ImageType.IMAGE_TYPE_ALBUM_250x250, avatarIv, null, false);

        //如果没用播放过动画
        if (!animPosArray.get(position)) {
            animPosArray.put(position, true);

            ScaleAnimation scale = new ScaleAnimation(0f, 1f, 0f, 1f, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
            scale.setInterpolator(new DecelerateInterpolator());
            scale.setDuration(300);
            //不同位置动画的开始时间都不一样,造成依次播放的效果
            scale.setStartOffset((position + 1) * 100L);
            convertView.startAnimation(scale);
        }
        convertView.setVisibility(View.VISIBLE);
    }
}