package com.immomo.momo.discuss.presenter;

import androidx.annotation.NonNull;

import com.immomo.momo.discuss.activity.IDiscussMemberListView;
import com.immomo.momo.mvp.common.RecyclerViewContract;

/**
 * Created by x<PERSON><PERSON> on 21/06/2017.
 */

public interface IDiscussMemberListPresenter extends RecyclerViewContract.IPresenter {
    void pause();

    void resume();

    void destroy();

    void init();

    void bindView(@NonNull IDiscussMemberListView iView);

    int getCurrentSortType();

    void requestRefresh(int sortType);

    void deleteUser(String momoid);
}
