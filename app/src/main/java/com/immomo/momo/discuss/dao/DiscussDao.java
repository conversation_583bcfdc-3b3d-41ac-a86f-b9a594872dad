package com.immomo.momo.discuss.dao;

import android.database.Cursor;
import com.tencent.wcdb.database.SQLiteDatabase;

import com.immomo.momo.discuss.bean.Discuss;
import com.immomo.momo.service.daobase.BaseDao;
import com.immomo.momo.util.StringUtils;

import java.util.HashMap;

public class DiscussDao extends BaseDao<Discuss, String> implements Discuss.Table {

	public DiscussDao(SQLiteDatabase db) {
		super(db, TableName, F_DiscussId);
	}

	@Override
	protected Discuss assemble(Cursor cursor) {
		Discuss discuss = new Discuss();
		assemble(discuss, cursor);
		return discuss;
	}

	@Override
	protected void assemble(Discuss obj, Cursor cursor) {
		obj.id = cursor.getString(cursor.getColumnIndex(F_DiscussId));
		obj.name = cursor.getString(cursor.getColumnIndex(F_DiscussName));
		obj.membernames = cursor.getString(cursor.getColumnIndex(F_MEMBER_MNAMES));
		obj.createTime = toDate(cursor.getLong(cursor.getColumnIndex(F_CREATETIME)));
		obj.owerMomoid = cursor.getString(cursor.getColumnIndex(F_Owener));
		obj.photos = StringUtils.str2Arr(cursor.getString(cursor.getColumnIndex(F_Photos)), ",");
		obj.status = cursor.getInt(cursor.getColumnIndex(F_Status));
		obj.member_count = cursor.getInt(cursor.getColumnIndex(F_MEMBER_COUNT));
		obj.member_max = cursor.getInt(cursor.getColumnIndex(F_MEMBER_MAX));
		obj.level = cursor.getInt(cursor.getColumnIndex(F_Level));
		obj.displayMomoids = StringUtils.str2Arr(cursor.getString(cursor.getColumnIndex(F_Members)), ",");
		obj.chatBackgroup = getString(cursor, F_ChatBackground);
	}

	@Override
	public void insert(Discuss t) {
		insert(new String[]{
			F_DiscussId,
			F_DiscussName,
			F_MEMBER_MNAMES,
			F_CREATETIME,
			F_Members,
			F_Owener,
			F_Photos,
			F_Status,
			F_MEMBER_COUNT,
			F_MEMBER_MAX,
			F_Level
		}, new Object[]{
			t.id,
			t.name,
			t.membernames,
			toDbTime(t.createTime),
			StringUtils.join(t.displayMomoids, ","),
			t.owerMomoid,
			StringUtils.join(t.photos, ","),
			t.status,
			t.member_count,
			t.member_max,
			t.level
		});
	}

	@Override
	public void update(Discuss t) {
		updateField(new String[]{
			F_DiscussName,
			F_MEMBER_MNAMES,
			F_CREATETIME,
			F_Members,
			F_Owener,
			F_Photos,
			F_Status,
			F_MEMBER_COUNT,
			F_MEMBER_MAX,
			F_Level
		}, new Object[]{
			t.name,
			t.membernames,
			toDbTime(t.createTime),
			StringUtils.join(t.displayMomoids, ","),
			t.owerMomoid,
			StringUtils.join(t.photos, ","),
			t.status,
			t.member_count,
			t.member_max,
			t.level
		}, new String[]{
			F_DiscussId,
		}, new String[]{
			t.id,
		});
	}

	@Override
	public void deleteInstence(Discuss obj) {
		delete(obj.id);
	}

	public void saveFullSearchGroup(Discuss discuss) {
        boolean isInsert = false;
        if (!checkExsit(discuss.id)) {
            isInsert = true;
        }
        HashMap<String, Object> map = new HashMap<>(15);
        map.put(Discuss.Table.F_DiscussName, discuss.name);
        map.put(Discuss.Table.F_Owener, discuss.owerMomoid);
        map.put(Discuss.Table.F_Photos, StringUtils.join(discuss.photos, ","));
        if (isInsert) {
            map.put(Discuss.Table.F_DiscussId, discuss.id);
            insertFields(map);
        } else {
            updateFields(map, new String[]{Discuss.Table.F_DiscussId}, new Object[]{discuss.id});
        }
    }

}
