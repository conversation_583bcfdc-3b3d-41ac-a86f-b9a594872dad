package com.immomo.momo.discuss.dao;

import android.database.Cursor;
import com.tencent.wcdb.database.SQLiteDatabase;

import com.immomo.momo.service.daobase.BaseDao;
import com.immomo.momo.service.daobase.DBOpenHandler;


/**
 * 我的群组Dao
 *
 */
public class MyDiscussDao extends BaseDao<String, String> {
	public final static String DBFIELD_DID = "md_id";
	public final static String TABLE_NAME = DBOpenHandler.TABLE_NAME_MYDISCUSS;
	
	/**
	 * 构建一个 FriendDao数据库操作对象
	 * @param db 数据库实例
	 */
	public MyDiscussDao(SQLiteDatabase db) {
		super(db, TABLE_NAME, DBFIELD_DID);
	}

	@Override
	protected String assemble(Cursor cursor) {
		return cursor.getString(cursor.getColumnIndex(DBFIELD_DID));
	}

	@Override
	public void insert(String t) {
		StringBuilder sql = new StringBuilder();
		sql.append("insert or ignore into "+ tableName +" (")
	        .append(DBFIELD_DID)
		    .append(") values(")
		    .append("?")
		    .append(")");
		
		executeSQL(sql.toString(), new String[]{t});
	}

	@Override
	public void update(String t) {
	}

	@Override
	public void deleteInstence(String t) {
		delete(t);
	}

	@Override
	protected void assemble(String obj, Cursor cursor) {
		
	}
}
