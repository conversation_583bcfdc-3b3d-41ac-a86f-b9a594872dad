package com.immomo.momo.discuss.presenter;

import android.app.Activity;
import android.content.Intent;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;

import com.immomo.framework.base.BaseActivity;
import com.immomo.framework.cement.CementModel;
import com.immomo.framework.cement.SimpleCementAdapter;
import com.immomo.framework.task.BaseDialogTask;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.momo.android.broadcast.ReflushDiscussMemberListReceiver;
import com.immomo.momo.businessmodel.usermodel.IUserModel;
import com.immomo.momo.discuss.activity.IDiscussMemberListView;
import com.immomo.momo.discuss.bean.Discuss;
import com.immomo.momo.discuss.bean.DiscussUser;
import com.immomo.momo.discuss.itemmodel.DiscussUserItemModel;
import com.immomo.momo.discuss.service.DiscussService;
import com.immomo.momo.mvp.common.model.ModelManager;
import com.immomo.momo.protocol.http.DiscussApi;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.service.sessions.SessionUserCache;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by xudshen on 21/06/2017.
 */

public class DiscussMemberListPresenter implements IDiscussMemberListPresenter {
    @NonNull
    private String did;
    private boolean isOwner;
    private int sortType = 1;
    @NonNull
    private DiscussService discussService;

    private SimpleCementAdapter adapter;
    private IDiscussMemberListView iView;

    public DiscussMemberListPresenter(@NonNull String did) {
        this.did = did;
        discussService = DiscussService.getInstance();
        Discuss discuss = SessionUserCache.getDiscuss(did);
        User user = ModelManager.getInstance().getModel(IUserModel.class).getCurrentUser();
        isOwner = discuss != null && user != null && TextUtils.equals(discuss.owerMomoid, user.momoid);
    }

    @Override
    public void bindView(@NonNull IDiscussMemberListView iView) {
        this.iView = iView;
    }

    @Override
    public void init() {
        adapter = new SimpleCementAdapter();
        iView.setAdapter(adapter);
    }

    @Override
    public void pause() {

    }

    @Override
    public void resume() {
        if (adapter.getDataList().isEmpty()) {
            iView.showRefreshStart();
            MomoTaskExecutor.cancleAllTasksByTag(hashCode());
            MomoTaskExecutor.executeUserTask(hashCode(), new GetLocalDataTask(new Runnable() {
                @Override
                public void run() {
                    requestRefresh();
                }
            }));
        }
    }

    @Override
    public void destroy() {
        MomoTaskExecutor.cancleAllTasksByTag(hashCode());
        iView = null;
    }

    @Override
    public int getCurrentSortType() {
        return sortType;
    }

    @Override
    public void requestRefresh() {
        MomoTaskExecutor.cancleAllTasksByTag(hashCode());

        iView.showRefreshStart();
        MomoTaskExecutor.executeUserTask(hashCode(), new GetDataTask());
    }

    @Override
    public void requestRefresh(int sortType) {
        this.sortType = sortType;

        int count = discussService.findDiscussMembers(did, false).size();
        Discuss discuss = SessionUserCache.getDiscuss(did);
        if (discuss != null) {
            discuss.member_count = count;
            discussService.updateMemberCount(did, discuss.member_count);
        }

        MomoTaskExecutor.cancleAllTasksByTag(hashCode());
        MomoTaskExecutor.executeUserTask(hashCode(), new GetLocalDataTask(null));
    }

    @Override
    public void deleteUser(String momoid) {
        MomoTaskExecutor.cancleAllTasksByTag(hashCode());
        MomoTaskExecutor.executeUserTask(hashCode(), new RemoveUserTask(
                (BaseActivity) iView.thisContext(), momoid));
    }

    private class GetLocalDataTask extends MomoTaskExecutor.Task<Object, Object, List<DiscussUser>> {
        @Nullable
        private Runnable runnable;
        private String traceId;

        public GetLocalDataTask(@Nullable Runnable runnable) {
            this.runnable = runnable;
        }

        @Override
        protected void onPreTask() {
        }

        @Override
        protected List<DiscussUser> executeTask(Object... params) throws Exception {
            List<DiscussUser> result = discussService.findDiscussMembers(did, sortType, false, true);
            return result;
        }

        @Override
        protected void onTaskError(Exception e) {
            super.onTaskError(e);
            if (iView != null) {
                iView.showRefreshFailed();
            }

        }

        @Override
        protected void onTaskSuccess(List<DiscussUser> list) {
            adapter.updateDataList(trans(list));
            if (iView != null) {
                iView.refreshTitle(list.size());
            }
            if (runnable != null) {
                runnable.run();
            } else if (iView != null) {
                iView.showRefreshComplete();
            }
            if (iView != null) {
                iView.tryEndInflateInChain(traceId);
            }
        }
    }

    private void updateAdapterList(List<DiscussUser> list) {
        //超过限额不使用DiffUtil避免影响性能
        if (list.size() > 100) {
            adapter.clearData();
        }
        adapter.updateDataList(trans(list));
    }

    private class GetDataTask extends MomoTaskExecutor.Task<Object, Object, List<DiscussUser>> {
        private String traceId;

        public GetDataTask() {
        }

        @Override
        protected void onPreTask() {
        }

        @Override
        protected List<DiscussUser> executeTask(Object... params) throws Exception {
            List<DiscussUser> list = DiscussApi.getInstance().downloadDiscussMembers(did);

            if (list != null && list.size() > 0) {
                discussService.updateDiscussMembers(list, did);
            }
            return discussService.findDiscussMembers(did, sortType, false, true);
        }

        @Override
        protected void onTaskError(Exception e) {
            super.onTaskError(e);
            if (iView != null) {
                iView.showRefreshFailed();
            }
        }

        @Override
        protected void onTaskSuccess(List<DiscussUser> list) {

            updateAdapterList(list);
            if (iView != null) {
                iView.showRefreshComplete();
                iView.refreshTitle(list.size());
                iView.tryEndInflateInChain(traceId);
            }
        }
    }

    private class RemoveUserTask extends BaseDialogTask {
        private String momoid;

        public RemoveUserTask(Activity activity, String momoid) {
            super(activity);
            this.momoid = momoid;
        }

        @Override
        protected Object executeTask(Object[] params) throws Exception {
            List<String> momoids = new ArrayList<>();
            momoids.add(momoid);
            DiscussApi.getInstance().removeMemeber(did, momoids);
            for (String momoid : momoids) {
                discussService.removeDiscussUser(momoid, did);
            }
            Intent it = new Intent(ReflushDiscussMemberListReceiver.ACTION_DELETE);
            activity.sendBroadcast(it);
            return null;
        }

        @Override
        protected void onTaskError(Exception e) {
            super.onTaskError(e);
        }
    }

    @NonNull
    private List<CementModel<?>> trans(List<DiscussUser> users) {
        Set<String> momoids = new HashSet<>();
        List<CementModel<?>> result = new ArrayList<>();
        for (DiscussUser discussUser : users) {
            if (discussUser != null && discussUser.user != null
                    && !momoids.contains(discussUser.user.momoid)) {
                momoids.add(discussUser.user.momoid);
                result.add(new DiscussUserItemModel(discussUser, isOwner));
            }
        }
        return result;
    }
}
