package com.immomo.momo.discuss.activity;

import android.os.Bundle;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.RadioButton;

import com.immomo.framework.imjson.client.exception.IMJsonException;
import com.immomo.framework.task.CommonTaskErrorProcessor;
import com.immomo.http.exception.HttpBaseException;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.ExceptionCatcher;
import com.immomo.momo.MomoKit;
import com.immomo.momo.R;
import com.immomo.momo.android.activity.BaseAccountActivity;
import com.immomo.momo.android.view.dialog.MProcessDialog;
import com.immomo.momo.discuss.bean.DiscussPreference;
import com.immomo.momo.exception.HttpException40445;
import com.immomo.momo.group.bean.GroupPreference;
import com.immomo.momo.maintab.sessionlist.SessionListFragment;
import com.immomo.momo.messages.service.DiscussMsgService;
import com.immomo.momo.protocol.http.UserApi;
import com.immomo.momo.service.bean.Message;
import com.immomo.momo.service.bean.Session;
import com.immomo.momo.util.StringUtils;

import org.json.JSONException;

public class DiscussNotificationSettingActivity extends BaseAccountActivity implements OnClickListener {
    public static final String KEY_DISCUSSID = "discuss_id";
    private String discussId;
    private RadioButton openButton = null;
    private RadioButton muteButton = null;
    private RadioButton closeButton = null;
    private DiscussPreference discussPreference;

    @Override
    protected void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        setContentView(R.layout.activity_gnotice_setting);
        initViews();
        initEvents();
        initData(savedInstanceState);
    }

    private void initData(Bundle savedInstanceState) {
        discussId = getIntent().getStringExtra(KEY_DISCUSSID);
        discussPreference = userPreference.getDiscussPreference(discussId);
        refreshRadioButtons();
    }

    private void refreshRadioButtons() {
        int mode = discussPreference.getNotificationModel();
        openButton.setChecked(mode == GroupPreference.NOTIFICATION_OPEN);
        muteButton.setChecked(mode == GroupPreference.NOTIFICATION_MUTE);
        closeButton.setChecked(mode == GroupPreference.NOTIFICATION_CLOSE);
    }

    @Override
    protected void initEvents() {
        findViewById(R.id.gnotification_layout_open).setOnClickListener(this);
        findViewById(R.id.gnotification_layout_mute).setOnClickListener(this);
        findViewById(R.id.gnotification_layout_close).setOnClickListener(this);
    }

    @Override
    protected void initViews() {
        setTitle("消息提醒设置");
        openButton = (RadioButton) findViewById(R.id.gnotification_rb_open);
        muteButton = (RadioButton) findViewById(R.id.gnotification_rb_mute);
        closeButton = (RadioButton) findViewById(R.id.gnotification_rb_close);
    }

    @Override
    public void onClick(View v) {
        int notifySetting = 0;
        switch (v.getId()) {
            case R.id.gnotification_layout_open:
                notifySetting = UserApi.GroupNotifyOpen;
                break;
            case R.id.gnotification_layout_mute:
                notifySetting = UserApi.GroupNotifySilent;
                break;
            case R.id.gnotification_layout_close:
                notifySetting = UserApi.GroupNotifyOff;
                break;
        }
        changeSetting(notifySetting);
    }

    public void changeSetting(final int finalNotifySetting) {
        MProcessDialog dialog = new MProcessDialog(this);
        showDialog(dialog);
        MomoTaskExecutor.executeTask(MomoTaskExecutor.EXECUTOR_TYPE_USER, hashCode(), new MomoTaskExecutor.Task() {
            @Override
            protected Object executeTask(Object[] params) throws Exception {
                UserApi.getInstance().uploadDiscussNotifySet(discussId, finalNotifySetting);
                return null;
            }

            @Override
            protected void onTaskFinish() {
                super.onTaskFinish();
                closeDialog();
            }

            @Override
            protected void onTaskSuccess(Object integer) {
                switch (finalNotifySetting) {
                    case TYPE_PUSH_CLOSE:
                        onCloseClick();
                        break;
                    case TYPE_PUSH_OPEN:
                        onOpenClick();
                        break;
                    case TYPE_PUSH_MUTE:
                        onMuteClick();
                        break;
                }
                refreshRadioButtons();
            }

            @Override
            protected void onTaskError(Exception e) {
                if (e == null) return;
                log.e(e);
                if (e instanceof HttpBaseException) {
                    if (e instanceof HttpException40445) {
                        CommonTaskErrorProcessor.processException40045();
                    }
                    if (!StringUtils.isEmpty(e.getMessage())) {
                        toast(e.getMessage());
                    } else {
                        toast(R.string.errormsg_server);
                    }
                } else if (e instanceof IMJsonException) {
                    toast(e.getMessage());
                } else if (e instanceof JSONException) {
                    toast(R.string.errormsg_dataerror);
                } else {
                    Toaster.show(R.string.errormsg_client);
                    Exception exception = new Exception("[WARNING] asynctask error", e);
                    //ExceptionCatcher.saveException(exception);
                    ExceptionCatcher.saveWarnException(exception);
                }
            }
        });
    }

    private void onCloseClick() {
        if (discussPreference.getNotificationModel() != GroupPreference.NOTIFICATION_CLOSE) {
            discussPreference.setNotificationModel(GroupPreference.NOTIFICATION_CLOSE);
            // 由other变为close，要改变未读消息的状态类型。close时，消息气泡只需要显示一个“点”。
            if (DiscussMsgService.getInstance().hasUnreaded(discussId)) {
                DiscussMsgService.getInstance().updateMessagesStatusByMid(discussId, Message.STATUS_RECE_SILENT,
                        Message.STATUS_RECE_UNREADED);
                MomoKit.getApp().removeDiscussMessageNotify();
            }
            Bundle bundle = new Bundle();
            bundle.putString(SessionListFragment.Key_SessionId, "d_" + discussId);
            bundle.putInt(SessionListFragment.Key_SessionType, Session.TYPE_DISCUSS);
            MomoKit.getApp().dispatchMessage(bundle, SessionListFragment.Action_SessionChanged);
        }
    }

    private void onOpenClick() {
        if (discussPreference.getNotificationModel() != GroupPreference.NOTIFICATION_OPEN) {
            // 由close变为open，要改变未读消息的状态类型。close时，消息气泡只显式一个“点”。
            if (discussPreference.getNotificationModel() == GroupPreference.NOTIFICATION_CLOSE) {
                if (DiscussMsgService.getInstance().hasUnreaded(discussId)) {
                    DiscussMsgService.getInstance().updateMessagesStatusByMid(discussId, Message.STATUS_RECE_UNREADED,
                            Message.STATUS_RECE_SILENT);
                }
            }
            Bundle bundle = new Bundle();
            //sessionid == d_ + discussId
            bundle.putString(SessionListFragment.Key_SessionId, "d_" + discussId);
            bundle.putInt(SessionListFragment.Key_SessionType, Session.TYPE_DISCUSS);
            MomoKit.getApp().dispatchMessage(bundle, SessionListFragment.Action_SessionChanged);
            discussPreference.setNotificationModel(GroupPreference.NOTIFICATION_OPEN);
        }
    }

    private void onMuteClick() {
        // 由close变为mute，要改变未读消息的状态类型。close时，消息气泡只显式一个“点”。
        if (discussPreference.getNotificationModel() != GroupPreference.NOTIFICATION_MUTE) {
            if (discussPreference.getNotificationModel() == GroupPreference.NOTIFICATION_CLOSE) {
                if (DiscussMsgService.getInstance().hasUnreaded(discussId)) {
                    DiscussMsgService.getInstance().updateMessagesStatusByMid(discussId, Message.STATUS_RECE_UNREADED,
                            Message.STATUS_RECE_SILENT);
                }
            }
            Bundle bundle = new Bundle();
            bundle.putString(SessionListFragment.Key_SessionId, "d_" + discussId);
            bundle.putInt(SessionListFragment.Key_SessionType, Session.TYPE_DISCUSS);
            MomoKit.getApp().dispatchMessage(bundle, SessionListFragment.Action_SessionChanged);
            discussPreference.setNotificationModel(GroupPreference.NOTIFICATION_MUTE);
        }
    }

    private static final int TYPE_PUSH_CLOSE = 0;
    private static final int TYPE_PUSH_OPEN = 1;
    private static final int TYPE_PUSH_MUTE = 2;

    @Override
    protected void onDestroy() {
        MomoTaskExecutor.cancleAllTasksByTag(hashCode());
        super.onDestroy();
    }
}
