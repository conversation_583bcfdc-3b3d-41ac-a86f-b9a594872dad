package com.immomo.momo.discuss.bean;

import com.immomo.momo.service.daobase.ITable;
import com.immomo.momo.util.DataUtil;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class Discuss implements Serializable {
    public String[] photos;
    public String name;
    public String owerMomoid;
    public List<DiscussUser> members = null;
    public String[] displayMomoids = null;
    public String id;
    public int status = STATUS_NORMAL;

    public int level;
    public Date createTime;
    public int member_count = 0;
    public int member_max = 0;
    public String chatBackgroup;
    public String membernames = null;

    /** 封禁**/
    public static final int STATUS_BANDED = 3;
    /** 解散**/
    public static final int STATUS_DISMISS = 2;
    public static final int STATUS_NORMAL = 1;

    public Discuss() {
    }

    public Discuss(String id) {
        this.id = id;
    }

    public String getLoadImageId() {
        return photos != null && photos.length > 0 ? photos[0] : "";
    }

    public String getDisplayName() {
        if (DataUtil.hasValue(name)) {
            return name;
        } else {
            return id;
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        Discuss other = (Discuss) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        } else if (!id.equals(other.id))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((id == null) ? 0 : id.hashCode());
        return result;
    }

    public interface Table extends ITable {
        String F_DiscussId = "did";
        String F_DiscussName = DBFIELD_TMP + "1";
        String F_Owener = DBFIELD_TMP + "2";
        String F_Photos = DBFIELD_TMP + "3";
        String F_Members = DBFIELD_TMP + "4";
        String F_Status = DBFIELD_TMP + "5";
        String F_Level = DBFIELD_TMP + "6";
        String F_CREATETIME = DBFIELD_TMP + "7";
        String F_MEMBER_COUNT = DBFIELD_TMP + "8";
        String F_MEMBER_MAX = DBFIELD_TMP + "9";
        String F_MEMBER_MNAMES = DBFIELD_TMP + "10";
        String F_ChatBackground = DBFIELD_TMP + "11";
        String TableName = "discuss";
    }

}
