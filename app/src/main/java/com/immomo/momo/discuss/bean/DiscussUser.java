package com.immomo.momo.discuss.bean;

import com.immomo.momo.service.bean.User;
import com.immomo.momo.service.daobase.ITable;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

public class DiscussUser implements Serializable {
    private static final long serialVersionUID = 8282676914850436181L;
    /**
     * 群成员级别：群的所有者，一般为群的创建者
     */
    public static final int ROLE_OWNER = 1;
    /**
     * 群成员级别：管理者
     */
    public static final int ROLE_MANAGER = 2;
    /**
     * 群成员级别：普通成员
     */
    public static final int ROLE_MEMBER = 3;
    /**
     * 和群完全没有关系
     */
    public static final int ROLE_NONE = 0;

//	0|1|2 对应 不是成员|创建者|普通成员

    /**
     * 用户的陌陌号码
     */
    public String momoid;
    /**
     * 所属的多人对话号
     */
    public String did;
    /**
     * 加入时间
     */
    public Date joinTime;
    public Date activeTime;
    /**
     * 最后发言时间
     **/
    public Date msgTime;

    /**
     * 级别
     */
    public int level;
    /**
     * 在多人对话中的角色(管理员/普通)
     **/
    public int role;
    /**
     * 存放用户详细资料的对象
     */
    public User user;
    /**
     * 存放用户的头像
     **/
    public String avatar;
    /**
     * 用户名称
     **/
    public String name;

    @Override
    public String toString() {
        return "DiscussUser [momoid=" + momoid + ", did=" + did
                + ", joinTime=" + joinTime + ", level=" + level + ", user="
                + user + "]";
    }

    public final static String DBFIELD_MOMOID = ITable.DBFIELD_TMP + "1";
    public final static String DBFIELD_JOINTIME = ITable.DBFIELD_TMP + "2";
    public final static String DBFIELD_LEVEL = ITable.DBFIELD_TMP + "3";
    public final static String DBFIELD_DISCUSSID = ITable.DBFIELD_TMP + "4";
    public final static String DBFIELD_ACTIVETIME = ITable.DBFIELD_TMP + "5";
    public final static String DBFIELD_ROLE = ITable.DBFIELD_TMP + "6";
    public final static String DBFIELD_AVATOR = ITable.DBFIELD_TMP + "7";
    public final static String DBFIELD_MSGTIME = ITable.DBFIELD_TMP + "8";

    @Override
    public boolean equals(Object object) {
        if (object instanceof DiscussUser) {
            return this.momoid.equals(((DiscussUser) object).momoid);
        }
        return false;
    }

    @Override
    public int hashCode() {
        return Objects.hash(momoid);
    }
}
