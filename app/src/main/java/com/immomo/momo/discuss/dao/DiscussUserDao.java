package com.immomo.momo.discuss.dao;

import android.database.Cursor;
import com.tencent.wcdb.database.SQLiteDatabase;

import com.immomo.momo.discuss.bean.DiscussUser;
import com.immomo.momo.service.daobase.BaseDao;
import com.immomo.momo.service.daobase.DBOpenHandler;

public class DiscussUserDao extends BaseDao<DiscussUser, Integer> {
    public final static String TABLE_NAME = DBOpenHandler.TABLE_NAME_DISCUSS_MEMBER;

    public DiscussUserDao(SQLiteDatabase db) {
        super(db, TABLE_NAME);
    }

    @Override
    protected DiscussUser assemble(Cursor cursor) {
        DiscussUser duser = new DiscussUser();
        assemble(duser, cursor);
        return duser;
    }

    @Override
    public void insert(DiscussUser discussUser) {
        StringBuilder sql = new StringBuilder();
        sql.append("insert into "+ tableName +" (")
                .append(DiscussUser.DBFIELD_DISCUSSID +", ")
                .append(DiscussUser.DBFIELD_JOINTIME +", ")
                .append(DiscussUser.DBFIELD_ACTIVETIME +", ")
                .append(DiscussUser.DBFIELD_MSGTIME +", ")
                .append(DiscussUser.DBFIELD_LEVEL +", ")
                .append(DiscussUser.DBFIELD_ROLE +", ")
                .append(DiscussUser.DBFIELD_MOMOID)
                .append(") values(")
                .append("?,?,?,?,?,?,?")
                .append(")");

        executeSQL(sql.toString(), new Object[]{discussUser.did, toDbTime(discussUser.joinTime), toDbTime(discussUser.activeTime), toDbTime(discussUser.msgTime), discussUser.level, discussUser.role,  discussUser.momoid});
    }

    @Override
    public void update(DiscussUser discussUser) {
        if(discussUser.joinTime == null) {
            StringBuilder sql = new StringBuilder();
            sql.append("update "+ tableName +" set ")
                    .append(DiscussUser.DBFIELD_ROLE +"=?, ")
                    .append("where "+ DiscussUser.DBFIELD_DISCUSSID +"=? ")
                    .append("and " + DiscussUser.DBFIELD_MOMOID +"=?");

            executeSQL(sql.toString(), new Object[]{discussUser.role,  discussUser.did, discussUser.momoid});
        } else {
            StringBuilder sql = new StringBuilder();
            sql.append("update "+ tableName +" set ")
                    .append(DiscussUser.DBFIELD_JOINTIME +"=?, ")
                    .append(DiscussUser.DBFIELD_ACTIVETIME +"=?, ")
                    .append(DiscussUser.DBFIELD_MSGTIME +"=?, ")
                    .append(DiscussUser.DBFIELD_LEVEL +"=?, ")
                    .append(DiscussUser.DBFIELD_ROLE +"=? ")
                    .append("where "+ DiscussUser.DBFIELD_DISCUSSID +"=? ")
                    .append("and " + DiscussUser.DBFIELD_MOMOID +"=?");

            executeSQL(sql.toString(), new Object[]{toDbTime(discussUser.joinTime), toDbTime(discussUser.activeTime), toDbTime(discussUser.msgTime),discussUser.level, discussUser.role,  discussUser.did, discussUser.momoid});
        }
    }

    @Override
    public void deleteInstence(DiscussUser discussUser) {
        delete(new String[]{DiscussUser.DBFIELD_DISCUSSID, DiscussUser.DBFIELD_MOMOID}, new Object[]{discussUser.did, discussUser.momoid});
    }

    @Override
    protected void assemble(DiscussUser discussUser, Cursor cursor) {
        discussUser.did = cursor.getString(cursor.getColumnIndex(DiscussUser.DBFIELD_DISCUSSID));
        discussUser.momoid = cursor.getString(cursor.getColumnIndex(DiscussUser.DBFIELD_MOMOID));
        discussUser.level = cursor.getInt(cursor.getColumnIndex(DiscussUser.DBFIELD_LEVEL));
        discussUser.role = cursor.getInt(cursor.getColumnIndex(DiscussUser.DBFIELD_ROLE));
        discussUser.joinTime = toDate(cursor.getLong(cursor.getColumnIndex(DiscussUser.DBFIELD_JOINTIME)));
        discussUser.msgTime = toDate(cursor.getLong(cursor.getColumnIndex(DiscussUser.DBFIELD_MSGTIME)));
    }
}
