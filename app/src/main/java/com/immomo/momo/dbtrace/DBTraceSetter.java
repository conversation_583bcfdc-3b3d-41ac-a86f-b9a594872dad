package com.immomo.momo.dbtrace;

import android.content.Context;

import com.cosmos.mdlog.MDLog;
import com.immomo.dbtrace.MODBTrace;
import com.immomo.dbtrace.MeasureConfig;
import com.immomo.dbtrace.SwitchConfig;
import com.immomo.momo.BuildConfig;
import com.immomo.momo.dbtrace.apt.DbTraceAppConfigV2Getter;
import com.immomo.momo.mulog.MULogConstants;
import com.immomo.momo.mulog.MURealtimeLog;
import com.immomo.momo.mulog.pair.MUPairItem;

/**
 * 见文档：https://moji.wemomo.com/doc#/detail/143619
 */
public class DBTraceSetter {

    /**
     * 初始化可以在更早的位置，只是受限于 KV 的操作
     * 更早的好处是不会漏掉一个 db 的监控
     */
    public static void initDbTrace(Context appContext) {
        MODBTrace.init(appContext, new MODBTrace.Builder()
                .switchConfig(new SwitchConfig() {
                    @Override
                    public boolean enable() {
                        return DbTraceAppConfigV2Getter.get().enable() == 1;
                    }

                    @Override
                    public boolean enableLog() {
                        return BuildConfig.DEBUG;
                    }

                    @Override
                    public boolean enableAlert() {
                        return BuildConfig.DEBUG;
                    }

                    @Override
                    public boolean enableHookSo() {
                        // 线上 so 有崩溃，集中在小米和华为 9 版本手机上
                        return BuildConfig.DEBUG;
//                        return DbTraceAppConfigV2Getter.get().needHookSo() == 1;
                    }

                    @Override
                    public boolean clearLocalData() {
                        return DbTraceAppConfigV2Getter.get().clearLocalData() == 1;
                    }

                    @Override
                    public boolean enableRecordExeInMain() {
                        return DbTraceAppConfigV2Getter.get().recordMainThread() == 1;
                    }
                })
                .measureConfig(new MeasureConfig() {
                    @Override
                    public long getThresholdTimeCostForRecord() {
                        return DbTraceAppConfigV2Getter.get().thresholdCostForRecord();
                    }

                    @Override
                    public long getThresholdTimeCostForDumpStack() {
                        return DbTraceAppConfigV2Getter.get().thresholdCostForDump();
                    }
                })
                .setIssuePublisher(info -> {
                    MDLog.w("MODBTrace", info.toString());
                    if (DbTraceAppConfigV2Getter.get().enableUpload() == 1) {
                        try {
                            //MODBTrace初始化过早，防止将来加入DB操作，导致崩溃
                            MURealtimeLog.business(MULogConstants.BUSINESS_MOMO_BASIC)
                                    .secondLBusiness("dbtrace")
                                    .thirdLBusiness(info.dbPath)
                                    .addBodyItem(new MUPairItem("id", info.id))
                                    .addBodyItem(new MUPairItem("cost", info.sqlTimeCost))
                                    .addBodyItem(new MUPairItem("sql", info.sql))
                                    .addBodyItem(new MUPairItem("isInMain", info.isInMainThread))
                                    .addBodyItem(new MUPairItem("thread", info.detail))
                                    .addBodyItem(new MUPairItem("desc", info.desc))
                                    .addBodyItem(new MUPairItem("advice", info.advice))
                                    .addBodyItem(new MUPairItem("stack", info.extInfo))
                                    .commit();
                        } catch (Exception e) {
                            MDLog.printErrStackTrace("MODBTrace", e);
                        }

                    }
                }).build());
    }
}
