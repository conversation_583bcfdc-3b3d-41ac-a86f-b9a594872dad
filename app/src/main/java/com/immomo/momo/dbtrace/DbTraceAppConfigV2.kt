package com.immomo.momo.dbtrace

import com.immomo.annotations.appconfig.AppConfigField
import com.immomo.annotations.appconfig.appconfigv2.AppConfigV2

@AppConfigV2
object DbTraceAppConfigV2 {

    @AppConfigField(mark = "309", key = "enable", defValue = "0", isSysValue = true)
    var enable = 0

    @AppConfigField(mark = "309", key = "enableUpload", defValue = "0", isSysValue = true)
    var enableUpload = 0

    @AppConfigField(mark = "309", key = "needHookSo", defValue = "0", isSysValue = true)
    var needHookSo = 0

    @AppConfigField(mark = "309", key = "clearLocalData", defValue = "0", isSysValue = true)
    var clearLocalData = 0

    @AppConfigField(mark = "309", key = "recordMainThread", defValue = "1", isSysValue = true)
    var recordMainThread = 1

    @AppConfigField(mark = "309", key = "thresholdCostForRecord", defValue = "15", isSysValue = true)
    var thresholdCostForRecord = 15

    @AppConfigField(mark = "309", key = "thresholdCostForDump", defValue = "15", isSysValue = true)
    var thresholdCostForDump = 15


}

