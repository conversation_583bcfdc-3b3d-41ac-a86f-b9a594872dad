package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

public class ReflushVChatSuperRoomProfileReceiver extends BaseReceiver {

    public static final String ACTION = MomoKit.getPackageName() + ".action.vchat.super.room.reflush";
    public static final String KEY_VID = "vid";


    public ReflushVChatSuperRoomProfileReceiver(Context context) {
        super(context);
        register(ACTION);
    }

}
