package com.immomo.momo.android.presenter;

import android.text.TextUtils;

import com.cosmos.mdlog.MDLog;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.moarch.account.AccountKit;
import com.immomo.momo.Configs;
import com.immomo.momo.LogTag;
import com.immomo.momo.abtest.config.ABConfigManager;
import com.immomo.momo.abtest.config.ABUploadParamUtils;
import com.immomo.momo.account.mobile.QuickLoginConfig;
import com.immomo.momo.android.activity.IWelcomeView;
import com.immomo.momo.android.synctask.CheckNewVersionTask;
import com.immomo.momo.android.synctask.WelcomeLogHelper;
import com.immomo.momo.appstart.AppStartHelper;
import com.immomo.momo.emotionstore.service.EmotionService;
import com.immomo.momo.guest.GuestConfig;
import com.immomo.momo.newaccount.login.interactor.GetGuestUserDataUseCase;
import com.immomo.momo.newaccount.login.repository.GuestRepositoryImpl;
import com.immomo.momo.personalprofile.utils.PersonalThemeHelper;
import com.immomo.momo.protocol.http.core.HttpClient;
import com.immomo.momo.service.bean.GuestAccountInfo;
import com.immomo.momo.util.OaidSupplier;

import java.util.concurrent.atomic.AtomicBoolean;

import io.reactivex.subscribers.DisposableSubscriber;

/**
 * Created by wang.renguang on 2018/6/1.
 */
public class WelcomeViewPresenter implements IWelcomeViewPresenter {

  private IWelcomeView view;

  private AtomicBoolean isGettingQuestData = new AtomicBoolean(false);

  private GetGuestUserDataUseCase mGuestDataUseCase =
      new GetGuestUserDataUseCase(new GuestRepositoryImpl());

  public WelcomeViewPresenter(IWelcomeView view) {
    QuickLoginConfig.INSTANCE.requestConfig();
    this.view = view;
    if (!AccountKit.getAccountManager().isOnline()) {
      GuestConfig.getInstance().initGuestLocation();
    }
    AppStartHelper.INSTANCE.getAppStartConfigAsync(true);
    OaidSupplier.INSTANCE.init();
    WelcomeLogHelper.getInstance().callLog(); // 统计应用激活的数量
    updateABConfig();
    PersonalThemeHelper.Companion.getInstance().preloadTheme();
  }

  // 更新图片格式
  @Override
  public void checkUpdateVersion() {
    String suffix = KV.getSysStr(SPKeys.System.AppMultiConfig.KEY_IMAGE_SUFFIX, "");
    if ("webp".equalsIgnoreCase(suffix)) {
      Configs.downImageType = Configs.CONFIGS_DOWNLOAD_IMAGE_TYPE_WEBP;
    } else {
      Configs.downImageType = Configs.CONFIGS_DOWNLOAD_IMAGE_TYPE_JPG;
    }

    try {
      long lastttime = KV.getSysLong(SPKeys.Public.PUBLIC_PREFERENCE_KEY_CHECKVERSION_TIME, 0L);
      long now = System.currentTimeMillis() / 1000;
      long offset = Math.abs(lastttime - now);
      if (offset > Configs.CHECK_VERSION_TIME) {
        new CheckNewVersionTask(null, false).execute();
        KV.saveSysValue(SPKeys.Public.PUBLIC_PREFERENCE_KEY_CHECKVERSION_TIME, now);
      }
      EmotionService.checkEmotionConfigs();
    } catch (Exception e) {
      MDLog.printErrStackTrace(LogTag.COMMON, e);
      KV.saveSysValue(SPKeys.Public.PUBLIC_PREFERENCE_KEY_CHECKVERSION_TIME, 0L);
    }
  }

  @Override
  public void updateABConfig() {
    if (!AccountKit.getAccountManager().isOnline()
        && AccountKit.getAccountManager().getAccountList() != null
        && !AccountKit.getAccountManager().getAccountList().isEmpty()) {
      // 有帐号但是非登录状态  请求更新ab配置
      // 没有帐号的情况会通过访客接口下发配置
      AppStartHelper.INSTANCE.getAppStartConfigAsync(true);
    }
  }

  @Override
  public void getGuestData() {
    if (isGettingQuestData.get()) {
      return;
    }

    isGettingQuestData.set(true);
    mGuestDataUseCase.execute(
        new DisposableSubscriber<GuestAccountInfo>() {
          @Override
          public void onNext(GuestAccountInfo guestUser) {
            if (guestUser != null && !TextUtils.isEmpty(guestUser.guestId)) {
              ABConfigManager.getInstance().setOppoKeyChainLoginState(guestUser.oppoAutoLogin);
              ABConfigManager.getInstance().setOppoKeyChainLoginTimeout(guestUser.oppoAutoLoginTimeout);
              ABConfigManager.getInstance().setGuestConfig(guestUser.guestABConfig);
              if (guestUser.abData != null && guestUser.guestABConfig != null) {
                HttpClient.setAbConfigParam(
                    ABUploadParamUtils.INSTANCE.getReplacedUploadPara(guestUser.guestABConfig));
              }
            }

            if (view != null) {
              view.onGetGuestDataFinish();
            }
          }

          @Override
          public void onError(Throwable exception) {
            if (view != null) {
              view.onGetGuestDataFinish();
            }
          }

          @Override
          public void onComplete() {
            isGettingQuestData.set(false);
          }
        });
  }

  @Override
  public void destroy() {
    mGuestDataUseCase.dispose();
  }
}
