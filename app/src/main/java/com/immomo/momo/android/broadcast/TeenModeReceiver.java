package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;

/**
 * Created by li.qiang on 2019-09-05
 */
public class TeenModeReceiver extends BaseReceiver {
    public static final String EVENT_NAME_TEEN = "TeenModelChangeNotifiction";
    public static final String EVENT_NAME_TEEN_NOTIFY = "MDTeenModelAuthResultNotifiction";
    public static final String PARAM_TEENMODEL = "teenModel";
    public static final String PARAM_TIMESEC = "timesec";

    public static final String PARAM_AUTHRESULT = "authResult";
    public static final String PARAM_TYPE = "type";//0:沉迷解锁；1：宵禁



    public TeenModeReceiver(Context context) {
        super(context);
    }

}
