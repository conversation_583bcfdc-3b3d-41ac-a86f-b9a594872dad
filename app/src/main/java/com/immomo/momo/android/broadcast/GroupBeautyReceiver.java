package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;

/**
 * <AUTHOR>
 */
public class GroupBeautyReceiver extends BaseReceiver {
    public static final String ACTION_BEAUTY = "mm.action.group.beauty";
    public static final String KEY_GID = "gid";
    public static final String BEAUTY_JSON = "beauty";

    public GroupBeautyReceiver(Context context) {
        super(context);
        register(ACTION_BEAUTY);
    }
}
