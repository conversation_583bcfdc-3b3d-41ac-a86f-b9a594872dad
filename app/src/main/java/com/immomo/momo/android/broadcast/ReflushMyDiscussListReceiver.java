package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;

public class ReflushMyDiscussListReceiver extends BaseReceiver {

	public static final String ACTION_ADD = "mm.action.discusslist.add";
	public static final String ACTION_DELETE = "mm.action.discusslist.delete";
	public static final String ACTION_BANDED = "mm.action.discusslist.banded";
	public static final String ACTION_REFLUSH_PROFILE = "mm.action.discusslist.reflush.profile";
	public static final String KEY_DID = "disid";

	public ReflushMyDiscussListReceiver(Context context) {
		super(context);
		register(ACTION_DELETE, ACTION_ADD, ACTION_REFLUSH_PROFILE,ACTION_BANDED);
	}

}
