package com.immomo.momo.android.broadcast;

import android.content.Context;
import android.content.IntentFilter;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

/**
 * 程序注销时的广播
 * <AUTHOR>
 *
 */
public class GroupPartyChangedReceiver extends BaseReceiver {
	public final static String ACTION_MODIFY_FINISH = MomoKit.getPackageName()+".action.groupparty.modify";
	public final static String KEY_GROUP_ID = "group_id";
	public GroupPartyChangedReceiver(Context context, int level) {
		super(context);
		IntentFilter intentFilter = new IntentFilter(ACTION_MODIFY_FINISH);
		intentFilter.setPriority(level);
		register(intentFilter);
	}
}
