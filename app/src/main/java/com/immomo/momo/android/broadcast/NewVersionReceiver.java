package com.immomo.momo.android.broadcast;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.immomo.momo.MomoKit;
import com.immomo.momo.setting.activity.AboutActivity;
import com.immomo.momo.util.StringUtils;
import com.immomo.momo.util.jni.Codec;

public class NewVersionReceiver extends BroadcastReceiver {
	public static final String ACTION = MomoKit.getPackageName()+".action.hasnewversion";
	
	
	@Override
	public void onReceive(Context context, Intent intent) {
		if(ACTION.equals(intent.getAction())) {
			String clazz = intent.getExtras() != null ? intent.getStringExtra("activity") : null;
			Intent i = null;
			while(true) {
				String key = null;
				if(!StringUtils.isEmpty(clazz) && !StringUtils.isEmpty(key = intent.getStringExtra("key"))) {
					try {
						clazz = Codec.decode(clazz);
						Class activity = Class.forName(clazz);
						if(activity != null && activity.getName().contains("tivity")) {
							i = new Intent(context, activity);
							break;
						}
					} catch (Exception e) {
					}
				}
				i = new Intent(context, AboutActivity.class);
				break;
			}
			
			i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
			context.startActivity(i);
		}
	}
	
}
