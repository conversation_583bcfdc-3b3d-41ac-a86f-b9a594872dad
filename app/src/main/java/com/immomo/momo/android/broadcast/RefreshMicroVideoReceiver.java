package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

public class RefreshMicroVideoReceiver extends BaseReceiver {
    public static final String KEY_MICRO_VIDEO_SHOW_REDDOT_STRING = "KEY_MICRO_VIDEO_SHOW_REDDOT_STRING";

    public static final String ACTION_MICRO_VIDEO_SHOW_REDDOT = MomoKit.getPackageName() + ".action.micro.video.show.reddot";

    public RefreshMicroVideoReceiver(Context context) {
        super(context);
        register(ACTION_MICRO_VIDEO_SHOW_REDDOT
        );
    }
}
