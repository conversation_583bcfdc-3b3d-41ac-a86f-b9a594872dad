package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

public class ReflushSelectFriendReceiver extends BaseReceiver {

	public static final String ACTION = MomoKit.getPackageName() + ".action.selectfriend.reflush";

	public ReflushSelectFriendReceiver(Context context) {
		super(context);
		register(ACTION);
	}

}
