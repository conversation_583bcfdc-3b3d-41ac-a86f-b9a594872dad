package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

/**
 * <AUTHOR>
 * @version 1.0
 * @company:
 * @date 17/2/22 17:18
 */

public class FriendNoticeReceiver extends BaseReceiver{
    public final static String ACTION_FRIEND_NOTICE_REFRESH =  MomoKit.getPackageName() + ".action.friend.notice.refresh";
    public FriendNoticeReceiver(Context context) {
        super(context);
        register(ACTION_FRIEND_NOTICE_REFRESH);
    }
}
