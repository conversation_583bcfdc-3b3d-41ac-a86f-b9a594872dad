package com.immomo.momo.android.broadcast;

import android.content.Context;
import android.content.IntentFilter;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

public class RefreshTiebaProfileReceiver extends BaseReceiver {
    public static final String ACTION_NEED_REFRESH_TIEBAPROFILE = MomoKit.getPackageName() + ".action.refresh.tieba";
    public static final String ACTION_NEED_UPDATE_TIEBAPROFILE = MomoKit.getPackageName() + ".action.update.tieba";
    
    public static final String KEY_TIEID = "key_pid"; 
    public static final String KEY_TIEBAID = "key_tiebaid";

    public RefreshTiebaProfileReceiver(Context context) {
        super(context);
        
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ACTION_NEED_REFRESH_TIEBAPROFILE);
        intentFilter.addAction(ACTION_NEED_UPDATE_TIEBAPROFILE);
        this.register(intentFilter);
    }

}
