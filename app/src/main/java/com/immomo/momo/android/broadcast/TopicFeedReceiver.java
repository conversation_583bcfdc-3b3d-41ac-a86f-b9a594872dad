package com.immomo.momo.android.broadcast;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

public class TopicFeedReceiver extends BaseReceiver {
    public static final String ACTION_TOPICFEED_TOP = MomoKit.getPackageName() + ".action.topicfeed.top";//置顶
    public static final String ACTION_TOPICFEED_UNTOP = MomoKit.getPackageName() + ".action.topicfeed.untop";//取消置顶
    public static final String ACTION_TOPICFEED_HOT = MomoKit.getPackageName() + ".action.topicfeed.hot";//设置热门
    public static final String ACTION_TOPICFEED_UNHOT = MomoKit.getPackageName() + ".action.topicfeed.unhot";//取消热门
    public static final String ACTION_TOPICFEED_HIDE = MomoKit.getPackageName() + ".action.topicfeed.hide";//话题隐藏
    /*Feed数据发生变化*/
    public static final String ACTION_FEED_CHANGED = MomoKit.getPackageName() + ".action.feed.changed";

    public static final String KEY_FEEDID = "feedid";

    public TopicFeedReceiver(Context context) {
        super(context);
        register(ACTION_TOPICFEED_TOP, ACTION_TOPICFEED_UNTOP, ACTION_TOPICFEED_HOT, ACTION_TOPICFEED_UNHOT, ACTION_TOPICFEED_HIDE);
    }


    public static boolean sendTopicFeedTop(Context mContext, String feedId) {
        if (TextUtils.isEmpty(feedId)) {
            return false;
        }
        Intent intent = new Intent(ACTION_TOPICFEED_TOP);
        intent.putExtra(KEY_FEEDID, feedId);
        mContext.sendBroadcast(intent);
        return true;
    }

    public static boolean sendTopicFeedUnTop(Context mContext, String feedId) {
        if (TextUtils.isEmpty(feedId)) {
            return false;
        }
        Intent intent = new Intent(ACTION_TOPICFEED_UNTOP);
        intent.putExtra(KEY_FEEDID, feedId);
        mContext.sendBroadcast(intent);
        return true;
    }

    public static boolean sendTopicFeedHot(Context mContext, String feedId) {
        if (TextUtils.isEmpty(feedId)) {
            return false;
        }
        Intent intent = new Intent(ACTION_TOPICFEED_HOT);
        intent.putExtra(KEY_FEEDID, feedId);
        mContext.sendBroadcast(intent);
        return true;
    }

    public static boolean sendTopicFeedUnHot(Context mContext, String feedId) {
        if (TextUtils.isEmpty(feedId)) {
            return false;
        }
        Intent intent = new Intent(ACTION_TOPICFEED_UNHOT);
        intent.putExtra(KEY_FEEDID, feedId);
        mContext.sendBroadcast(intent);
        return true;
    }

    public static boolean sendTopicFeedHide(Context mContext, String feedId) {
        if (TextUtils.isEmpty(feedId)) {
            return false;
        }
        Intent intent = new Intent(ACTION_TOPICFEED_UNTOP);
        intent.putExtra(KEY_FEEDID, feedId);
        mContext.sendBroadcast(intent);
        return true;
    }
}
