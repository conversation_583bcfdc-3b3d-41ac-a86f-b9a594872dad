package com.immomo.momo.android.broadcast;

import android.content.Context;
import android.content.IntentFilter;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

public class SynCloudMsgReceiver extends BaseReceiver {
	public final static String ACTION_SYN = MomoKit.getPackageName()+".action.cloudmsg.syn";
	
	public SynCloudMsgReceiver(Context context) {
		super(context);
		IntentFilter intentFilter = new IntentFilter();
		intentFilter.addAction(ACTION_SYN);
		register(intentFilter);
	}
	
	public SynCloudMsgReceiver(Context context, String... actions) {
		super(context);
		IntentFilter intentFilter = new IntentFilter();
		for (String a : actions) {
			intentFilter.addAction(a);
		}
		register(intentFilter);
	}

}
