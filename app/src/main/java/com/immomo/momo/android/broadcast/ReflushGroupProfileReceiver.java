package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

public class ReflushGroupProfileReceiver extends BaseReceiver {

    public static final String ACTION = MomoKit.getPackageName() + ".action.group.reflush";
    public static final String LUA_ACTION = "action.group.reflush";
    public static final String KEY_GID = "gid";


    public ReflushGroupProfileReceiver(Context context) {
        super(context);
        register(ACTION);
    }

}
