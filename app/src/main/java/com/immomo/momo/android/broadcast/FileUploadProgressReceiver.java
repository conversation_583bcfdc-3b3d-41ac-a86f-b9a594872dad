package com.immomo.momo.android.broadcast;

import android.content.Context;
import android.content.IntentFilter;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

/**  
 * <AUTHOR>
 */
public class FileUploadProgressReceiver extends BaseReceiver {
	public final static String ACTION_FILE_UPLOAD_PROGRESS = MomoKit.getPackageName()+".action.fileuploadprogress";
	
	public final static String KEY_MESSAGE_ID = "key_message_id";
	public final static String KEY_UPLOADED_LENGTH = "key_upload_progress";
	
	public FileUploadProgressReceiver(Context context) {
		super(context);
		IntentFilter filter = new IntentFilter();
		filter.addAction(ACTION_FILE_UPLOAD_PROGRESS);
		register(filter );
	}
}
