package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

public class NewGroupFeedPublishReceiver extends BaseReceiver {

	public static final String ACTION_NEW_FEED = MomoKit.getPackageName() + ".action.group.publishfeed";
	public static final String KEY_FEEDID = "feedid";
	public static final String KEY_USERID = "userid";
	public static final String KEY_GROUPID = "groupid";

	public NewGroupFeedPublishReceiver(Context context) {
		super(context);
		register(ACTION_NEW_FEED);
	}

}
