package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

public class AppInitedFinishedReceiver extends BaseReceiver {
	public final static String ACTION_FINISH = MomoKit.getPackageName() + ".action.appinited.finish";
	public AppInitedFinishedReceiver(Context context) {
		super(context, ACTION_FINISH);
	}

}
