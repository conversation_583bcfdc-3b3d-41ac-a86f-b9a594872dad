package com.immomo.momo.android.broadcast

import android.content.Context
import com.immomo.framework.base.BaseReceiver
import com.immomo.momo.MomoKit

class ReflushBusiUnreadReceiver(context: Context?) : BaseReceiver(context) {
    companion object {
        val KEY_HOME = "homeKey"
        val ClearAll = MomoKit.getPackageName() + "ReflushBusiUnreadReceiver.action.clear"
    }

    init {
        register(ClearAll)
    }
}