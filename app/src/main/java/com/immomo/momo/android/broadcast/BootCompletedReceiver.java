package com.immomo.momo.android.broadcast;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.immomo.momo.MomoApplication;
import com.immomo.momo.MomoKit;

public class BootCompletedReceiver extends BroadcastReceiver {

	@Override
	public void onReceive(Context context, Intent intent) {
		if (!MomoApplication.isInitOnCreate) {
			return;
		}

		if(MomoKit.getCurrentUser() != null) {
			MomoKit.getApp().asyncWatchIMService();
		}
	}

}
