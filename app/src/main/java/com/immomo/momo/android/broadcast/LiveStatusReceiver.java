package com.immomo.momo.android.broadcast;

import android.content.Context;
import android.content.IntentFilter;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.immomo.framework.base.BaseReceiver;

/**
 * 直播状态监听器
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 15/7/16.
 */
public class LiveStatusReceiver extends BaseReceiver {
    public final static String ACTION_LIVE_START = "android.intent.action.MOLIVE_PLAYER_START";
    public final static String ACTION_LIVE_STOP = "android.intent.action.MOLIVE_PLAYER_STOP";
    public final static String ACTION_LIVE_PREVIEW_SHOW = "android.intent.action.MOLIVE_PLAYER_PREVIEW_SHOW";
    public final static String ACTION_LIVE_PREVIEW_HIDE = "android.intent.action.MOLIVE_PLAYER_PREVIEW_HIDE";
    private LocalBroadcastManager broadcastManager;

    public LiveStatusReceiver(Context context) {
        super(context);
        broadcastManager = LocalBroadcastManager.getInstance(context);
        register(ACTION_LIVE_START, ACTION_LIVE_STOP, ACTION_LIVE_PREVIEW_SHOW, ACTION_LIVE_PREVIEW_HIDE);
    }

    @Override
    public void register(IntentFilter filter) {
        broadcastManager.registerReceiver(this, filter);
    }

    public void unRegister() {
        broadcastManager.unregisterReceiver(this);
    }
}