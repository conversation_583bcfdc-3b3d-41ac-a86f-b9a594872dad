package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;

public class ReflushMyGroupListReceiver extends BaseReceiver {

    public static final String ACTION_ADD = "mm.action.grouplist.deletegroup";
    public static final String ACTION_DELETE = "mm.action.grouplist.addgroup";
    public static final String ACTION_BANDED = "mm.action.grouplist.banded";
    public static final String ACTION_PASS = "mm.action.grouplist.pass";
    public static final String ACTION_REFLUSH_PROFILE = "mm.action.grouplist.reflush.profile";
    public static final String ACTION_REFLUSH_ITEM = "mm.action.grouplist.reflush.item";
    public static final String ACTION_REFLUSH = "mm.action.grouplist.reflush.reflush";
    public static final String KEY_GID = "gid";

    public ReflushMyGroupListReceiver(Context context) {
        super(context);
		register(ACTION_DELETE, ACTION_ADD, ACTION_REFLUSH_PROFILE, ACTION_REFLUSH_ITEM,ACTION_BANDED,ACTION_PASS,ACTION_REFLUSH);
    }

}
