package com.immomo.momo.android.broadcast;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.immomo.momo.MomoApplication;
import com.immomo.momo.MomoKit;

/**
 * 监听耳机状态的广播
 * <AUTHOR>
 *
 */
public class HeadsetStatusReceiver extends BroadcastReceiver {

	@Override
	public void onReceive(Context context, Intent intent) {
		if (!MomoApplication.isInitOnCreate) {
			return;
		}

		if (Intent.ACTION_HEADSET_PLUG.equals(intent.getAction())) {
			if (intent.getIntExtra("state", 0) != 0) {
				MomoKit.setHeadset(true);
			} else {
				MomoKit.setHeadset(false);
			}
		}
	}
}


//extends BaseReceiver {
//	
//	
//	public HeadsetStatusReceiver(Context context) {
//		super(context);
//		IntentFilter filter = new IntentFilter();
//		filter.addAction(Intent.ACTION_HEADSET_PLUG);
//		register(filter );
//	}
//}



