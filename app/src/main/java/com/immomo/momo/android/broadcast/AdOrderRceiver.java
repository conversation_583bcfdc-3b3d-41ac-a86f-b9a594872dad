package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

public class AdOrderRceiver extends BaseReceiver {
	public final static String ACTION = MomoKit.getPackageName()+".action.adorder.reflush";
	public final static String KEY_EVENT = "event";
	public final static String EVENT_REFLUSH = "reflush";
	
	public AdOrderRceiver(Context context) {
		super(context);
		register(ACTION);
	}
}
