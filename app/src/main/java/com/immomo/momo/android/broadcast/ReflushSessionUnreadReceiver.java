package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

/**
 * Created by joel on 16/4/26.
 * <p/>
 * Momo Tech 2011-2016 © All Rights Reserved.
 */
public class ReflushSessionUnreadReceiver extends BaseReceiver {
    public static final String ReflushAll = MomoKit.getPackageName() + ".action.sessionlist.reflush";
    public static final String ReflushNotice = MomoKit.getPackageName() + ".action.sessionlist.reflushnotice";
    public static final String KEY_NEED_ANIM = "key_need_animation";

    public ReflushSessionUnreadReceiver(Context context) {
        super(context);
        register(ReflushAll, ReflushNotice);
    }
}
