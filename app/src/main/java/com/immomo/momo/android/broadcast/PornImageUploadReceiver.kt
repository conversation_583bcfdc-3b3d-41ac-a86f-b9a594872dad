package com.immomo.momo.android.broadcast

import android.content.Context
import android.content.IntentFilter

import com.immomo.framework.base.BaseReceiver
import com.immomo.kotlin.extern.isNotNullOrEmpty
import com.immomo.mmstatistics.event.ExposureEvent
import com.immomo.momo.MomoKit
import com.immomo.momo.statistics.EVAction
import com.immomo.momo.statistics.EVPage


class PornImageUploadReceiver(context: Context) : BaseReceiver(context) {

    fun register() {
        val filter = IntentFilter()
        filter.addAction(PORN_IMAGE_UPLOAD)
        register(filter)
        setReceiveListener {
            val remoteId = it.getStringExtra(REMOTE_ID)
            remoteId.isNotNullOrEmpty {
                ExposureEvent.create(ExposureEvent.Type.Normal).page(EVPage.Msg.ChatPage)
                        .action(EVAction.Content.PornPicture)
                        .putExtra("from_momo_id", MomoKit.getCurrentOrGuestMomoId())
                        .putExtra("to_momo_id", remoteId)
                        .submit()

            }
        }
    }

    fun unregister(context: Context) {
        context.unregisterReceiver(this)
    }

    companion object {
        val PORN_IMAGE_UPLOAD = MomoKit.getPackageName() + ".action.pornImageUpload"
        val REMOTE_ID = "remoteId"
    }
}
