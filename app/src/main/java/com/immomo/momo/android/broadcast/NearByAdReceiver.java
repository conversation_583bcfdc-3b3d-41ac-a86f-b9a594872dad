package com.immomo.momo.android.broadcast;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

/**
 * Created by joel on 15/6/17.
 * <p/>
 * Momo Tech 2011-2015 © All Rights Reserved.
 */
public class NearByAdReceiver extends BaseReceiver {

    public static final String ACTION = MomoKit.getPackageName() + ".action.nearby.close";
    public static final String KEY_AD_ID = "ad_id";
    public static final String KEY_INDEX = "ad_index";//广告位置，luabridge提供参数

    public NearByAdReceiver(Context context) {
        super(context);
        register(ACTION);
    }

    /**
     * 关闭广告后发送广播
     *
     * @param adid
     * @param index lua传递的广告item position
     * @return
     */
    public static boolean sendAdCloseBroadcast(String adid, int index) {
        if (TextUtils.isEmpty(adid)) {
            return false;
        }
        Intent intent = new Intent(ACTION);
        if (index >= 0) {
            intent.putExtra(NearByAdReceiver.KEY_INDEX, index);
        }
        intent.putExtra(NearByAdReceiver.KEY_AD_ID, adid);
        MomoKit.getContext().sendBroadcast(intent);
        return true;
    }
}
