package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

public class MaskChatMatchReceiver extends BaseReceiver {
    public final static String ACTION = MomoKit.getPackageName() + ".action.adorder.maskchat.matchsuccess";
    public final static String ACTION_EXIT_ROOM = MomoKit.getPackageName() + ".action.adorder.maskchat.match.exitroom";
    public final static String ACTION_UNLOCK = MomoKit.getPackageName() + ".action.adorder.maskchat.match.unlock";

    public MaskChatMatchReceiver(Context context) {
        super(context, ACTION, ACTION_EXIT_ROOM, ACTION_UNLOCK);
    }
}
