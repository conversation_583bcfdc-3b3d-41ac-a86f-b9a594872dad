package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

public class IMJLoginSuccessReceiver extends BaseReceiver {
    public static final String ACTION_IMJLOGIN_SUCCESS = MomoKit.getPackageName() + ".action.imj.loginsuccess";

    public IMJLoginSuccessReceiver(Context context) {
        super(context);
        register(ACTION_IMJLOGIN_SUCCESS);
    }
}
