package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

/**
 * FeedFilterReciver
 * author: <EMAIL>
 * since: 2015-02-10
 * MomoTech
 * <p/>
 * _-----_
 * |       |    .------------------------------.
 * |--(o)--|    |  If this comment is removed  |
 * `---------´   |      this program will       |
 * ( _´U`_ )    |          blow up             |
 * /___A___\    '------------------------------'
 * |  ~  |
 * __'.___.'__
 * ´   `  |° ´ Y `
 */

public class FeedFilterReceiver extends BaseReceiver {
    public static final String ACTION = MomoKit.getPackageName() + ".action.refresh_circle_setting";

    public static final String KEY_PROFILE_CHANGE_FIELD = "KEY_PROFILE_CHANGE_FIELD";

    public static final String KEY_PROFILE_CHANGED = "KEY_PROFILE_CHANGED";

    public static final String KEY_COMPLETE_PROFILE_UPGRADE = "KEY_COMPLETE_PROFILE_UPGRADE";

    public FeedFilterReceiver(Context context) {
        super(context);
        register(ACTION);
    }
}
