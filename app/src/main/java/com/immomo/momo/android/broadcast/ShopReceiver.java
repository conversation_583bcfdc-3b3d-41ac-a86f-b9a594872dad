package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

public class ShopReceiver extends BaseReceiver {

	public static final String ACTION_BUY = MomoKit.getPackageName() + ".action.shop.buy";
	public static final String ACTION_BUYFREE = MomoKit.getPackageName() + ".action.shop.buyfree";
	public static final String ACTION_GIFT = MomoKit.getPackageName() + ".action.shop.gift";
	public static final String EID = "eid";

	public ShopReceiver(Context context) {
		super(context);
		register(ACTION_BUY);
	}

}
