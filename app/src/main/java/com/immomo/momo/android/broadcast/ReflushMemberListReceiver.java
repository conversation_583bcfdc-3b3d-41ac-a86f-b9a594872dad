package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

public class ReflushMemberListReceiver extends BaseReceiver {

	public static final String ACTION_DELETE = MomoKit.getPackageName() + ".action.memberlist.delete";

	public ReflushMemberListReceiver(Context context) {
		super(context, ACTION_DELETE);
	}

}
