package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;

/**
 * Created by r<PERSON><PERSON><PERSON> on 1/7/16.
 */
public class DraftReceiver extends BaseReceiver {

    public final static String ACTION_DRAFT_CHANGE = "ACTION_DRAFT_CHANGE";

    public static final String SESSION_ID = "SESSION_ID";
    public static final String DRAFT = "DRAFT";

    public DraftReceiver(Context context) {
        super(context);
        register( ACTION_DRAFT_CHANGE);
    }

}
