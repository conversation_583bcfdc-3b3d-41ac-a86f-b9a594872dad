package com.immomo.momo.android.synctask;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;

import com.immomo.mmutil.StringUtils;
import com.immomo.mmutil.log.Log4Android;
import com.immomo.mmutil.task.MomoTaskExecutor;
import com.immomo.mmutil.toast.Toaster;
import com.immomo.momo.MomoKit;
import com.immomo.momo.android.view.dialog.MProcessDialog;
import com.immomo.momo.appconfig.model.AppMultiConfig;
import com.immomo.momo.common.activity.NewVersionActivity;
import com.immomo.momo.common.activity.ToAppStoreUpdateActivity;
import com.immomo.momo.common.activity.UpgradeActivity;
import com.immomo.momo.mvp.maintab.mainimpl.appconfig.apt.UpgradeAppConfigGetter;
import com.immomo.momo.protocol.http.AppApi;
import com.immomo.momo.service.model.MomoVersion;
import com.immomo.momo.util.DeviceUtils;

public class CheckNewVersionTask extends MomoTaskExecutor.Task<String, Object, MomoVersion> {

    private MProcessDialog dialog;
    private Context context;
    private boolean isClicked = false;

    private boolean isGooglePlayChanel = MomoKit.isIntlBuild();

    public CheckNewVersionTask(Context context, boolean isClicked) {
        this.context = context == null ? MomoKit.getTopActivity() : context;
        this.isClicked = isClicked;
        if (!isGooglePlayChanel) {
            if (isClicked) {
                dialog = new MProcessDialog(this.context);
                dialog.setCancelable(true);
            }
        }
    }

    public void execute() {
        MomoTaskExecutor.executeUserTask(CheckNewVersionTask.class.getName(), this);
    }

    @Override
    protected void onPreTask() {
        if (dialog != null && isClicked) {
            dialog.setText("请求提交中");
            dialog.show();
        }
        super.onPreTask();
    }

    @Override
    protected MomoVersion executeTask(String... params) throws Exception {
        if (!isGooglePlayChanel) {
            try {
                return AppApi.getInstance().checkVersionInfo(isClicked ? AppMultiConfig.ANDROID_VERSION_CHECK_MANUAL : AppMultiConfig.ANDROID_VERSION_CHECK_AUTO);
            } catch (Exception e) {
                Log4Android.getInstance().e("CheckNewVersionTask", e);
                if (isClicked) {
                    Toaster.showInvalidate(e.getMessage());
                }
            }
        }
        return null;
    }

    @Override
    protected void onTaskSuccess(final MomoVersion version) {
        if (dialog != null && (isClicked || !(context instanceof Activity && ((Activity) context).isFinishing()))) {
            dialog.dismiss();
        }
        processMomoVersion(version);
    }

    private void processMomoVersion(MomoVersion version) {
        if (isGooglePlayChanel) {
            if (isClicked) {
                checkUpdate(context);
            }
        } else {
            if (version != null) {
                if (MomoKit.getVersionCode() < version.version_count) {    // 应用需要更新
                    if (version.app_store_update) {    // 应用自更新 大于当前版本五个版本号
                        Intent it = new Intent(context, ToAppStoreUpdateActivity.class);
                        it.putExtra(ToAppStoreUpdateActivity.KEY_VERSION_CODE, String.valueOf(version.version_count));
                        it.putExtra(ToAppStoreUpdateActivity.KEY_PLATFORM, version.platform);
                        context.startActivity(it);
                    } else if (StringUtils.isNotBlank(version.url) && StringUtils.isNotBlank(version.desc_url)) {
                        Intent intent = new Intent(context, NewVersionActivity.class);
                        intent.putExtra(NewVersionActivity.KEY_URL_DOWNLOAD, version.url);
                        intent.putExtra(NewVersionActivity.KEY_VERSIONNAME, version.version_name);
                        intent.putExtra(NewVersionActivity.KEY_VERSION_DESC, version.desc_url);
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        context.getApplicationContext().startActivity(intent);
                    }
                } else {
                    if (isClicked) {
                        Toaster.showInvalidate("当前已是最新版");
                    } else if (DeviceUtils.isVivo()) {
                        String deeplink = UpgradeAppConfigGetter.get().deeplink();
                        int configVersion = UpgradeAppConfigGetter.get().version();
                        if (MomoKit.getVersionCode() < configVersion && StringUtils.isNotBlank(deeplink)) {
                            Intent intent = new Intent(context, UpgradeActivity.class);
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            context.getApplicationContext().startActivity(intent);
                        }
                    }
                }
            }
        }
    }

    private void checkUpdate(Context context) {
        String str = "market://details?id=" + context.getPackageName();
        try {
            Intent mark = new Intent(Intent.ACTION_VIEW);
            mark.setData(Uri.parse(str));
            context.startActivity(mark);
        } catch (Exception e) {
        }
    }
}
