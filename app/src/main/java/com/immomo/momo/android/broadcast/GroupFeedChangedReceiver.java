package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

public class GroupFeedChangedReceiver extends BaseReceiver {

	public static final String ACTION = MomoKit.getPackageName() + ".action.event.groupfeed.changed";
	public static final String KEY_FEEDID = "feedid";
	public static final String KEY_COMMENT_COUNT = "comment_count";
	public static final String KEY_COMMENT_TIME = "comment_time";

	public GroupFeedChangedReceiver(Context context) {
		super(context, ACTION);
	}

}
