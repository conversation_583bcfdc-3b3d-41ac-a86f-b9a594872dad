package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

/**
 * Created by joel on 15/11/4.
 * <p/>
 * Momo Tech 2011-2015 © All Rights Reserved.
 */
public class WeixinResultReceiver extends BaseReceiver {
    public final static String ACTION = MomoKit.getPackageName() + ".action.weixinresult.complete";
    public final static String ERRCODE = "errcode";

    public WeixinResultReceiver(Context context) {
        super(context);
        register(ACTION);
    }
}
