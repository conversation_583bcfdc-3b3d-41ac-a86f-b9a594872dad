package com.immomo.momo.android.broadcast;

import android.content.Context;

import com.immomo.framework.base.BaseReceiver;
import com.immomo.momo.MomoKit;

public class ReflushDiscussMemberListReceiver extends BaseReceiver {

	public static final String ACTION_DELETE = MomoKit.getPackageName() + ".action.discussmemlist.delete";

	public ReflushDiscussMemberListReceiver(Context context) {
		super(context, ACTION_DELETE);
	}

}
