package com.immomo.momo.android.broadcast;

import android.app.DownloadManager;
import android.app.DownloadManager.Query;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;

import com.cosmos.mdlog.MDLog;
import com.immomo.LogTag;
import com.immomo.momo.MomoApplication;
import com.immomo.momo.MomoKit;
import com.immomo.momo.util.StringUtils;

import java.io.File;

public class SystemDownloadComplete extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        if (!MomoApplication.isInitOnCreate) {
            return;
        }

        if (DownloadManager.ACTION_DOWNLOAD_COMPLETE.equals(intent.getAction())) {
            DownloadManager downloadManager = (DownloadManager) context.getSystemService(Context.DOWNLOAD_SERVICE);
            long reference = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1);

            Cursor cursor = null;
            try {
                Query query = new Query();
                query.setFilterById(reference);
                cursor = downloadManager.query(query);
                if (cursor != null && cursor.moveToFirst()) {
                    String fileUri = cursor.getString(cursor.getColumnIndex(DownloadManager.COLUMN_LOCAL_URI));
                    fileUri = fileUri != null ? fileUri.replace("file://", "") : null;
                    String mediaType = cursor.getString(cursor.getColumnIndex(DownloadManager.COLUMN_MEDIA_TYPE));
                    if (MomoKit.APK_MIMETYPE.equals(mediaType) && !StringUtils.isEmpty(fileUri)) {
                        if (!MomoKit.isIntlBuild()) {
                            MomoKit.openFileByIntent(context, new File(fileUri), MomoKit.APK_MIMETYPE);
                        }
                    }
                }
            } catch (Exception e) {
                MDLog.printErrStackTrace(LogTag.COMMON, e);
            } finally {
                if (cursor != null) {
                    cursor.close();
                }
            }

        } else if (DownloadManager.ACTION_NOTIFICATION_CLICKED.equals(intent.getAction())) {
            Intent i = Intent.createChooser(new Intent(DownloadManager.ACTION_VIEW_DOWNLOADS, intent.getData()), "下载");
            i.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(i);
        }
    }

}
