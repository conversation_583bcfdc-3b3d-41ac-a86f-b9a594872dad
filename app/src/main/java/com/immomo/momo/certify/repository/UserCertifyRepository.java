package com.immomo.momo.certify.repository;

import com.immomo.momo.certify.result.AliFaceUserCertifyAuthResult;
import com.immomo.momo.certify.result.AliFaceUserCertifyRegisterResult;
import com.immomo.momo.certify.result.UserCertifyResult;
import com.immomo.momo.protocol.http.UserCertifyApi;

import java.util.HashMap;

import io.reactivex.Flowable;

/**
 * <AUTHOR>
 * @data 2019/3/8.
 */
public class UserCertifyRepository implements IUserCertifyRepository {
    @Override
    public Flowable<UserCertifyResult> sendGetUserCertifyResultRequest(boolean isRegister) {
        return UserCertifyApi.getInstance().sendGetUserCertifyResultRequest(isRegister);
    }

    @Override
    public Flowable<AliFaceUserCertifyAuthResult> aliFaceCheckLaunchAuthRequest(String s) {
        return UserCertifyApi.getInstance().aliFaceCheckLaunchAuthRequest(s);
    }

    @Override
    public Flowable<AliFaceUserCertifyRegisterResult> aliFaceCheckRegisterRequest(HashMap<String, String> params) {
        return UserCertifyApi.getInstance().aliFaceCheckRegisterRequest(params);
    }

}
