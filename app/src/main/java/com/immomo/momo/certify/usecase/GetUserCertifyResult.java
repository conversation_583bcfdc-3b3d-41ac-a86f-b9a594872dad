package com.immomo.momo.certify.usecase;

import com.immomo.framework.rxjava.interactor.UseCase;
import com.immomo.mmutil.task.MMThreadExecutors;
import com.immomo.momo.certify.repository.IUserCertifyRepository;
import com.immomo.momo.certify.result.UserCertifyResult;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import io.reactivex.Flowable;

/**
 * <AUTHOR>
 * @data 2019/3/8.
 */
public class GetUserCertifyResult extends UseCase<UserCertifyResult, Boolean> {
    private IUserCertifyRepository mRepository;


    public GetUserCertifyResult(IUserCertifyRepository repository) {
        super(MMThreadExecutors.INSTANCE.getUser(), MMThreadExecutors.INSTANCE.getMain());
        mRepository = repository;
    }

    @NonNull
    @Override
    protected Flowable<UserCertifyResult> buildUseCaseFlowable(@Nullable Boolean isRegister) {
        return mRepository.sendGetUserCertifyResultRequest(isRegister);
    }
}
