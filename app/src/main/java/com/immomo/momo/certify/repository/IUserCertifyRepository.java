package com.immomo.momo.certify.repository;

import com.immomo.momo.certify.result.AliFaceUserCertifyAuthResult;
import com.immomo.momo.certify.result.AliFaceUserCertifyRegisterResult;
import com.immomo.momo.certify.result.UserCertifyResult;

import java.util.HashMap;

import io.reactivex.Flowable;

/**
 * <AUTHOR>
 * @data 2019/3/8.
 */
public interface IUserCertifyRepository {
    Flowable<UserCertifyResult> sendGetUserCertifyResultRequest(boolean isRegister);

    Flowable<AliFaceUserCertifyAuthResult> aliFaceCheckLaunchAuthRequest(String s);

    Flowable<AliFaceUserCertifyRegisterResult> aliFaceCheckRegisterRequest(HashMap<String, String> params);
}
