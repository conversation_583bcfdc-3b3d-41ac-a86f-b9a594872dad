package com.immomo.momo.certify.usecase

import com.immomo.framework.rxjava.interactor.UseCase
import com.immomo.mmutil.task.MMThreadExecutors
import com.immomo.momo.certify.repository.IUserCertifyRepository
import com.immomo.momo.certify.result.AliFaceUserCertifyAuthResult
import io.reactivex.Flowable

/**
 * 阿里真人认证接口：https://api.immomo.com/v4/user/momoface/launch
 */
class AliFaceCheckLaunchAuthUseCase(private val mRepository: IUserCertifyRepository) :
    UseCase<AliFaceUserCertifyAuthResult, String?>(
        MMThreadExecutors.User,
        MMThreadExecutors.Main
    ) {

    override fun buildUseCaseFlowable(s: String?): Flowable<AliFaceUserCertifyAuthResult> {
        return mRepository.aliFaceCheckLaunchAuthRequest(s)
    }
}
