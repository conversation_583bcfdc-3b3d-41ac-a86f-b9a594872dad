package com.immomo.momo.certify.appconfig

import com.immomo.annotations.appconfig.AppConfigField
import com.immomo.annotations.appconfig.appconfigv2.AppConfigV2
import com.immomo.momo.certify.appconfig.apt.ImageFaceDetectConfigV2Getter

/**
 * 图片检测配置
 */
@AppConfigV2
object ImageFaceDetectConfigV2 {

    @AppConfigField(key = "new_face_pic_detect", mark = "575", isSysValue = true)
    val enable = 1

    fun isOpenNewDetect() = ImageFaceDetectConfigV2Getter.get().enable() == 1
}