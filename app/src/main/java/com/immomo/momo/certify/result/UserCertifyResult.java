package com.immomo.momo.certify.result;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @data 2019/3/9.
 * 查询
 */
public class UserCertifyResult {

    public static final int FACE_ERROR = -1; //faceId有问题（敏感人物、五张脸不是同一个人）
    public static final int NONE = 0;
    public static final int SUCCESS = 1;
    public static final int FAIL = 2;
    public static final int CHECKING = 3;
    public static final int FAIL_SPAM = 4;//1:N失败

    @Expose
    @SerializedName("error")
    public ErrorBean error;

    @Expose
    @SerializedName("status")
    public int status;

    @Expose
    @SerializedName("goto")
    public String gotoString;

    @Expose
    @SerializedName("timeoutToast")
    public String timeoutToast;


    public int getStatus() {
        return status;
    }

    public String getGotoString() {
        return gotoString;
    }

    public static class ErrorBean {
        @Expose
        @SerializedName("code")
        public int code;

        @Expose
        @SerializedName("msg")
        public String msg;
    }
}
