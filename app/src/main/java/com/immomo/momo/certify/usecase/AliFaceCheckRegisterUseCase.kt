package com.immomo.momo.certify.usecase

import com.immomo.framework.rxjava.interactor.UseCase
import com.immomo.mmutil.task.MMThreadExecutors
import com.immomo.momo.certify.repository.IUserCertifyRepository
import com.immomo.momo.certify.result.AliFaceUserCertifyAuthResult
import com.immomo.momo.certify.result.AliFaceUserCertifyRegisterResult
import io.reactivex.Flowable

/**
 * 阿里真人认证接口：https://api.immomo.com/v4/user/momoface/register
 */
class AliFaceCheckRegisterUseCase(private val mRepository: IUserCertifyRepository) :
    UseCase<AliFaceUserCertifyRegisterResult, HashMap<String, String>>(
        MMThreadExecutors.User,
        MMThreadExecutors.Main
    ) {

    override fun buildUseCaseFlowable(params: HashMap<String, String>?): Flowable<AliFaceUserCertifyRegisterResult> {
        return mRepository.aliFaceCheckRegisterRequest(params)
    }
}
