package com.immomo.momo.certify;

import android.media.AudioManager;
import android.media.MediaPlayer;

import com.cosmos.mdlog.MDLog;
import com.immomo.momo.LogTag;


/**
 * <AUTHOR>
 * @data 2019/5/5.
 */
public class UserCertifySoundHelper {
    private MediaPlayer player;

    public UserCertifySoundHelper() {
        if (player == null) {
            player = new MediaPlayer();
        }
        player.reset();
    }

    public void play(String path) {
        try {
            if (player.isPlaying()) {
                player.stop();
            }
            player.reset();
            player.setDataSource(path);
            player.setAudioStreamType(AudioManager.STREAM_MUSIC);
            player.prepare();
            player.start();
        } catch (Exception e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
    }

    public void release() {
        try {
            player.stop();
            player.release();
            player = null;
        } catch (Exception e) {
            MDLog.printErrStackTrace(LogTag.COMMON, e);
        }
    }

}
