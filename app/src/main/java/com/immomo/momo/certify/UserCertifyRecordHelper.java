package com.immomo.momo.certify;

import android.content.Context;

import com.cosmos.mdlog.MDLog;
import com.immomo.mmutil.FileUtil;
import com.immomo.momo.LogTag;
import com.immomo.momo.MomoKit;

import java.io.File;

/**
 * <AUTHOR>
 * @data 2019/6/27.
 */
public class UserCertifyRecordHelper {
    public static volatile boolean isClearing = false;

    /**
     * 删除已经录制过的文件
     */
    public static void clearRecordFile() {
        MDLog.d(LogTag.Digimon.UserCertify, "clearRecordFile");
        if (isClearing) {
            return;
        }
        try {
            isClearing = true;
            File tempMomentDir = MomoKit.getApp().getDir("scan_data", Context.MODE_PRIVATE);
            if (!FileUtil.isValidFile(tempMomentDir)) {
                MDLog.d(LogTag.Digimon.UserCertify, "clearRecordFile tempMomentDir isValidFile");
                return;
            }
            FileUtil.deleteDir(tempMomentDir);
            MDLog.d(LogTag.Digimon.UserCertify, "clearRecordFile ok");
        } catch (Exception e) {
            MDLog.d(LogTag.Digimon.UserCertify, "clearRecordFile onTaskError");
            MDLog.printErrStackTrace(LogTag.Digimon.UserCertify, e);
        } finally {
            isClearing = false;
        }
    }
}
