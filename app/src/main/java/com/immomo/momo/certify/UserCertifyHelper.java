package com.immomo.momo.certify;

import android.annotation.SuppressLint;
import android.os.Build;

import com.cosmos.mdlog.MDLog;
import com.immomo.cvcenter.CVCenter;
import com.immomo.framework.imageloader.ImageType;
import com.immomo.framework.storage.kv.KV;
import com.immomo.framework.storage.preference.SPKeys;
import com.immomo.mmutil.FileUtil;
import com.immomo.mmutil.task.MomoMainThreadExecutor;
import com.immomo.momo.LogTag;
import com.immomo.momo.businessmodel.usermodel.IUserModel;
import com.immomo.momo.certify.appconfig.ImageFaceDetectConfigV2;
import com.immomo.momo.certify.params.CertifyPhotoInfo;
import com.immomo.momo.dynamicresources.DynamicResourceManager;
import com.immomo.momo.dynamicresources.ResourceChecker;
import com.immomo.momo.dynamicresources.ResourceLoadCallback;
import com.immomo.momo.mvp.common.model.ModelManager;
import com.immomo.momo.personalprofile.data.api.response.ProfileUser;
import com.immomo.momo.publish.util.ImageFaceDetect;
import com.immomo.momo.router.EditProfileResult;
import com.immomo.momo.router.ProfileRealAuth;
import com.immomo.momo.service.bean.User;
import com.immomo.momo.service.user.ProfileUserService;
import com.immomo.momo.util.MediaFileUtil;
import com.immomo.momo.util.StringUtils;
import com.momo.xscan.alivedetec.AliveDetector;
import com.momo.xscan.bean.CodeMNImage;
import com.momo.xscan.bean.MNImage;
import com.momo.xscan.fun.FaceDetector;
import com.momo.xscan.utils.Utils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import androidx.annotation.Nullable;

/**
 * <AUTHOR>
 * @data 2019/4/28.
 */
public class UserCertifyHelper {
    public static final String ACTION_SHOW_NEARBY_CERTIFY_TIP = "action_show_nearby_certify_tip";
    /**
     * 没有人脸
     */
    public static final int NO_FACE_CODE = 50;
    /**
     * 多张人脸
     */
    public static final int MORE_FACE_CODE = 51;

    private static EditProfileResult.RealAuth mRealAuth;

    public static String getScanSvgaUrlByStepCode(int code) {
        String url = "";
        switch (code) {
            case AliveDetector.STEP_LEFT_FACE:
                url = getScanMediaFilePathFromLocal("left_shake_head.svga");
                break;
            case AliveDetector.STEP_RIGHT_FACE:
                url = getScanMediaFilePathFromLocal("right_shake_head.svga");
                break;
            case AliveDetector.STEP_UP_FACE:
                url = getScanMediaFilePathFromLocal("raise_head.svga");
                break;
            case AliveDetector.STEP_DOWN_FACE:
                url = getScanMediaFilePathFromLocal("bow_head.svga");
                break;
            case AliveDetector.STEP_CLOSE_EYES:
                url = getScanMediaFilePathFromLocal("blink.svga");
                break;
            case AliveDetector.STEP_OPEN_MOUTH:
                url = getScanMediaFilePathFromLocal("open_mouth.svga");
                break;
            default:
                break;
        }
        return url;
    }

    private static String getScanMediaFilePathFromLocal(String fileName) {
        String dirPath = ResourceChecker.getScanMediaFile();
        File file = FileUtil.newFile(new File(dirPath), fileName);
        if (FileUtil.isValidFile(file)) {
            return file.getAbsolutePath();
        }
        return "";
    }

    public static boolean isPlayMedia() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.M;
    }


    /**
     * 是否显示过删选红点
     *
     * @return
     */
    public static boolean isShowedFilterRedPoint() {
        return KV.getUserBool(SPKeys.User.NearbyFilter.KEY_NEARBY_USER_CERTIFY_RED_POINT, false);
    }

    public static void saveShowFilterRedPointKV() {
        if (!isShowedFilterRedPoint()) {
            KV.saveUserValue(SPKeys.User.NearbyFilter.KEY_NEARBY_USER_CERTIFY_RED_POINT, true);
        }
    }

    /**
     * 是否显示过附近的人筛选tip
     *
     * @return
     */
    public static boolean isShowedFilterTip() {
        return KV.getUserBool(SPKeys.User.NearbyFilter.KEY_NEARBY_USER_CERTIFY_TIP, false);
    }

    public static void saveShowFilterTip() {
        KV.saveUserValue(SPKeys.User.NearbyFilter.KEY_NEARBY_USER_CERTIFY_TIP, true);
    }

    /**
     * 编辑照片认证结果
     * 用于其他页面显示tip
     *
     * @param realAuth
     */
    public static void setRealAuth(EditProfileResult.RealAuth realAuth) {
        mRealAuth = realAuth;
    }

    @Nullable
    public static EditProfileResult.RealAuth getRealAuth() {
        return mRealAuth;
    }

    /**
     * 企业版真人认证
     */
    public static void initCVCenterModelLoader() {
        CVCenter.getInstance().setModelLoader(new com.immomo.resdownloader.manager.ModelResourceManager.ModelLoader() {
            @Override
            public File getResource(String s) {
                return DynamicResourceManager.getInstance().getResource(s);
            }

            @SuppressLint("WrongConstant")
            @Override
            public void loadSource(com.immomo.resdownloader.ModelLoadCallback modelLoadCallback, String... strings) {
                DynamicResourceManager.getInstance().loadSource(new ResourceLoadCallback() {
                    @Override
                    public void onSuccess() {
                        if (modelLoadCallback != null) {
                            modelLoadCallback.onSuccess();
                        }
                    }

                    @Override
                    public void onFailed(String errorMsg) {
                        if (modelLoadCallback != null) {
                            modelLoadCallback.onFailed(-1, errorMsg);
                        }
                    }

                    @Override
                    public void onProcess(int percent, double speed) {
                        if (modelLoadCallback != null) {
                            modelLoadCallback.onProcess(percent, speed);
                        }
                    }

                    @Override
                    public void onProcessDialogClose() {
                        if (modelLoadCallback != null) {
                            modelLoadCallback.onProcessDialogClose();
                        }
                    }
                }, strings);
            }
        });
    }

    public static void dealUploadPhotosInfo(List<String> photos, PhotoDetectorListener photoDetectorListener) {

        IUserModel userModel = ModelManager.getInstance().getModel(IUserModel.class);

        Map<String, File> uploadFile = new HashMap<>();

        // 原来的头像
        Set<String> oldPhoto = new HashSet<>();
        if (userModel.getCurrentUser().photos != null) {
            oldPhoto.addAll(Arrays.asList(userModel.getCurrentUser().photos));
        }
        List<CertifyPhotoInfo> certifyPhotoInfos = new ArrayList<>();
        for (int i = 0; i < photos.size(); i++) {
            String photo = photos.get(i);
            if (!oldPhoto.contains(photo)) {   //去重，确保新头像不在已上传的头像中
                if (photo.startsWith("file://")) {
                    String[] arr = photo.split("/");
                    if (arr.length > 0) {
                        String[] arr2 = arr[arr.length - 1].split("\\.");
                        if (arr2.length > 0) {
                            photo = arr2[0];
                        }
                    }
                }
                File imageFile = MediaFileUtil.getImageFile(photo, ImageType.IMAGE_TYPE_ALBUM_LARGE);
                if (imageFile != null && imageFile.exists()) {
                    uploadFile.put("photo_" + i, imageFile);
                    CertifyPhotoInfo certifyPhotoInfo = new CertifyPhotoInfo();
                    certifyPhotoInfo.index = i;
                    certifyPhotoInfo.path = imageFile.getPath();
                    certifyPhotoInfos.add(certifyPhotoInfo);
                }
            }
        }
        MDLog.i(LogTag.Digimon.UserCertify, "dealUploadPhotosInfo certifyPhotoInfos size:" + certifyPhotoInfos.size() + "uploadFile size:" + uploadFile.size());
        processPhoto(certifyPhotoInfos, photoDetectorListener, uploadFile);
    }

    private static void processPhoto(List<CertifyPhotoInfo> certifyPhotoInfoList, PhotoDetectorListener detectorListener, Map<String, File> uploadFile) {
        if (certifyPhotoInfoList == null || certifyPhotoInfoList.size() == 0) {
            if (detectorListener != null) {
                MDLog.i(LogTag.Digimon.UserCertify, "processPhoto complete,certifyPhotoInfoList is null");
                detectorListener.onPhotoDetectorListener("", "", uploadFile, 0);
            }
            return;
        }

        try {
            // 状态大于0表示真人认证过，此时才需要提取图片特征值
            String fdPath = ResourceChecker.getPinchFqModelFile();
            String faModelFile = ResourceChecker.getFaModelFile();
            ProfileUser currentProfileUser = null;
            try {
                currentProfileUser = ProfileUserService.getInstance().getCurrentProfileUser();
            } catch (Throwable throwable) {
                MDLog.printErrStackTrace(LogTag.COMMON, throwable);
            }
            ProfileRealAuth realAuth = null;
            if (currentProfileUser != null) {
                realAuth = currentProfileUser.getRealAuth();
            } else {
                IUserModel userModel = ModelManager.getInstance().getModel(IUserModel.class);
                User currentUser = userModel.getCurrentUser();
                if (currentUser != null) {
                    realAuth = currentUser.realAuth;
                }
            }
            if (realAuth != null && realAuth.status > ProfileRealAuth.NONE
                    && StringUtils.isNotEmpty(fdPath)
                    && StringUtils.isNotEmpty(faModelFile)) {
                PhotoCount photoCount = new PhotoCount();
                JSONObject faceDataJson = new JSONObject();
                JSONObject faceCodeJson = new JSONObject();
                for (CertifyPhotoInfo certifyPhotoInfo : certifyPhotoInfoList) {
                    if (ImageFaceDetectConfigV2.INSTANCE.isOpenNewDetect()) {
                        useNewFaceDetect(certifyPhotoInfoList, detectorListener, uploadFile, certifyPhotoInfo, faceCodeJson, faceDataJson, photoCount);
                    } else {
                        useOldFaceDetect(certifyPhotoInfoList, detectorListener, uploadFile, certifyPhotoInfo, faceCodeJson, faceDataJson, photoCount);
                    }
                }
            } else {
                if (detectorListener != null) {
                    MDLog.i(LogTag.Digimon.UserCertify, "processPhoto complete,face data is null");
                    detectorListener.onPhotoDetectorListener("", "", uploadFile, 0);
                }
            }

        } catch (Throwable e) {
            MDLog.printErrStackTrace("realAuthUploadFeature", e);
            if (detectorListener != null) {
                detectorListener.onPhotoDetectorListener("", "", uploadFile, 0);
            }
        }
    }

    private static void useOldFaceDetect(List<CertifyPhotoInfo> certifyPhotoInfoList, PhotoDetectorListener detectorListener, Map<String, File> uploadFile, CertifyPhotoInfo certifyPhotoInfo, JSONObject faceCodeJson, JSONObject faceDataJson, PhotoCount photoCount) {
        FaceDetector faceDetector = new FaceDetector();
        faceDetector.processBitmap(certifyPhotoInfo.path, new FaceDetector.OnBitmapDetectFinishListener() {
            @Override
            public void onFinish(CodeMNImage codeMNImage) {
                MDLog.i(LogTag.Digimon.UserCertify, "codeMNImage face size:" + (codeMNImage.faces != null ? codeMNImage.faces.size() : 0));
                try {
                    //face 数量不为1，传入错误码，如果为1，传原数据
                    if (codeMNImage.faces == null || codeMNImage.faces.size() == 0) {
                        faceCodeJson.put("photo_" + certifyPhotoInfo.index, NO_FACE_CODE);
                    } else if (codeMNImage.faces.size() > 1) {
                        faceCodeJson.put("photo_" + certifyPhotoInfo.index, MORE_FACE_CODE);
                    } else {
                        List<MNImage> images = new ArrayList<>();
                        images.add(codeMNImage);
                        byte[] bytes = MNImage.generatePostPB(images);
                        String pbStr = Utils.byte2Base64(bytes);
                        faceDataJson.put("photo_" + certifyPhotoInfo.index, pbStr);
                    }

                } catch (JSONException e) {
                    MDLog.printErrStackTrace(LogTag.Digimon.UserCertify, e);
                }
                MomoMainThreadExecutor.post(getTaskTag(), () -> {
                    photoCount.count++;
                    if (photoCount.count == certifyPhotoInfoList.size()) {
                        MDLog.i(LogTag.Digimon.UserCertify, "processPhoto complete:");
                        int faceNum = codeMNImage.faces == null ? 0 : codeMNImage.faces.size();
                        detectorListener.onPhotoDetectorListener(faceDataJson.toString(), faceCodeJson.toString(), uploadFile, faceNum);
                    }
                });
                faceDetector.release();
            }

            @Override
            public void onError(String error) {
                photoCount.count++;
                MDLog.i(LogTag.Digimon.UserCertify, "processPhoto onError index:" + certifyPhotoInfo.index);
                if (photoCount.count == certifyPhotoInfoList.size()) {
                    MDLog.i(LogTag.Digimon.UserCertify, "processPhoto onError complete");
                    detectorListener.onPhotoDetectorListener(faceDataJson.toString(), faceCodeJson.toString(), uploadFile, 0);
                }
                faceDetector.release();
            }
        });
    }

    private static void useNewFaceDetect(List<CertifyPhotoInfo> certifyPhotoInfoList, PhotoDetectorListener detectorListener, Map<String, File> uploadFile, CertifyPhotoInfo certifyPhotoInfo, JSONObject faceCodeJson, JSONObject faceDataJson, PhotoCount photoCount) {
        ImageFaceDetect imageFaceDetect = new ImageFaceDetect();
        imageFaceDetect.checkImageFace(certifyPhotoInfo.path, facePhoto -> {
            MDLog.i(LogTag.Digimon.UserCertify, "useNewFaceDetect facePhoto:" + facePhoto);
            if (facePhoto != null && facePhoto.getHasFace()) { // 有人脸
                try {
                    faceDataJson.put("photo_" + certifyPhotoInfo.index, 1);
                } catch (Throwable e) {
                    MDLog.printErrStackTrace(LogTag.Digimon.UserCertify, e);
                }
                MomoMainThreadExecutor.post(getTaskTag(), () -> {
                    photoCount.count++;
                    if (photoCount.count == certifyPhotoInfoList.size()) {
                        MDLog.i(LogTag.Digimon.UserCertify, "useNewFaceDetect processPhoto success complete");
                        detectorListener.onPhotoDetectorListener(faceDataJson.toString(), faceCodeJson.toString(), uploadFile, 1);
                    }
                });
            } else {
                photoCount.count++;
                MDLog.i(LogTag.Digimon.UserCertify, "useNewFaceDetect onError index:" + certifyPhotoInfo.index);
                if (photoCount.count == certifyPhotoInfoList.size()) {
                    MDLog.i(LogTag.Digimon.UserCertify, "useNewFaceDetect processPhoto onError complete");
                    detectorListener.onPhotoDetectorListener(faceDataJson.toString(), faceCodeJson.toString(), uploadFile, 0);
                }
            }
        });
    }

    private static String getTaskTag() {
        return UserCertifyHelper.class.getSimpleName();
    }

    static class PhotoCount {
        int count;
    }

}
